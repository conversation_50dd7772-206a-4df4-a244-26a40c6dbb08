# Django Settings
DEBUG=1
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1
DJANGO_SETTINGS_MODULE=cv_analyzer.settings

# Database Settings
DATABASE_URL=postgres://user:password@localhost:5432/cv_analyzer

# Redis and Celery Settings
REDIS_URL=redis://localhost:6379/1
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# AI API Settings
OPENAI_API_KEY=your-openai-api-key-here
GROQ_API_KEY=your-groq-api-key-here

# Email Settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=1
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-specific-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Security Settings
SECURE_SSL_REDIRECT=0
SESSION_COOKIE_SECURE=0
CSRF_COOKIE_SECURE=0

# Storage Settings
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
AWS_S3_REGION_NAME=your-region

# Monitoring Settings
SENTRY_DSN=your-sentry-dsn

# Feature Flags
ENABLE_CLOUD_STORAGE=1
ENABLE_WHATSAPP_INTEGRATION=1
ENABLE_EMAIL_INTEGRATION=1

# Rate Limiting
API_RATE_LIMIT=100/hour
UPLOAD_RATE_LIMIT=10/hour

# Cache Settings
CACHE_TTL=900  # 15 minutes in seconds

# Logging
LOG_LEVEL=INFO

# Development Settings
DEVELOPMENT_MODE=1
ENABLE_DEBUG_TOOLBAR=1

# Documentation
ENABLE_SWAGGER_UI=1
ENABLE_REDOC=1 

# Encryption Settings
FIELD_ENCRYPTION_KEY=your-32-byte-encryption-key-here