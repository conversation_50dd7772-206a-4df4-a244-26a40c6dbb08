# CV Analyzer API Documentation

## 📋 Overview
The CV Analyzer API provides secure, production-ready endpoints for automated CV analysis and job matching. This REST API supports multiple authentication methods and implements comprehensive security measures.

**Base URL:** `https://your-domain.com/api/`
**Current Version:** v1 (v2 in development)
**Authentication:** JWT Tokens, API Keys

---

## 🔐 Authentication

### Supported Authentication Methods

#### 1. JWT Bearer Tokens
```http
Authorization: Bearer <access_token>
```

#### 2. API Keys
```http
X-API-Key: <api_key>
# OR
Authorization: ApiKey <api_key>
```

### Authentication Endpoints

#### Login
```http
POST /api/v1/auth/login/
Content-Type: application/json

{
    "username": "your_username",
    "password": "your_password"
}
```

**Response:**
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600,
    "token_type": "Bearer",
    "user": {
        "id": 1,
        "username": "your_username",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe"
    }
}
```

#### Refresh Token
```http
POST /api/v1/auth/refresh/
Content-Type: application/json

{
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

#### Generate API Key
```http
POST /api/v1/auth/api-key/
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "name": "My API Key",
    "expires_days": 365
}
```

**Response:**
```json
{
    "api_key": "cvanalyzer_abc123_def456",
    "key_id": "abc123",
    "expires_at": "2025-12-01T00:00:00Z",
    "name": "My API Key"
}
```

---

## 📄 CV Management

### Upload CV
```http
POST /api/v1/cv/upload/
Authorization: Bearer <access_token>
Content-Type: multipart/form-data

Form Data:
- cv_file: (file) CV file (PDF, DOC, DOCX)
- vacancy_id: (integer, optional) Target vacancy ID
```

**Response (v1):**
```json
{
    "cv_id": 123,
    "analysis_id": 456,
    "message": "Upload successful",
    "file_info": {
        "md5": "d41d8cd98f00b204e9800998ecf8427e",
        "sha256": "e3b0c44298fc1c149afbf4c8996fb924...",
        "size": 245760,
        "mime_type": "application/pdf"
    }
}
```

**Response (v2):**
```json
{
    "success": true,
    "message": "CV uploaded successfully",
    "data": {
        "cv_id": 123,
        "analysis_id": 456,
        "upload_id": 789,
        "file_info": {
            "md5": "d41d8cd98f00b204e9800998ecf8427e",
            "sha256": "e3b0c44298fc1c149afbf4c8996fb924...",
            "size": 245760,
            "mime_type": "application/pdf"
        },
        "status": "processing"
    }
}
```

### Get CV Analysis
```http
GET /api/v1/cv/analysis/{analysis_id}/
Authorization: Bearer <access_token>
```

**Response:**
```json
{
    "id": 456,
    "cv": {
        "id": 123,
        "file_name": "john_doe_cv.pdf",
        "file_size": 245760,
        "status": "complete",
        "source": "api",
        "created_at": "2024-12-01T10:00:00Z"
    },
    "overall_score": 85,
    "content_score": 88,
    "format_score": 82,
    "sections_score": 90,
    "skills_score": 80,
    "style_score": 85,
    "feedback": "Strong technical background with relevant experience...",
    "recommendations": ["Add more quantified achievements", "Include relevant certifications"],
    "analysis_details": {
        "scores": {
            "overall": 85,
            "content": 88,
            "format": 82,
            "sections": 90,
            "skills": 80,
            "style": 85
        },
        "grade": "B",
        "completion_status": "complete"
    },
    "created_at": "2024-12-01T10:00:00Z",
    "updated_at": "2024-12-01T10:05:00Z"
}
```

### List User's CV Analyses
```http
GET /api/v1/cv/analysis/
Authorization: Bearer <access_token>
```

**Response:**
```json
[
    {
        "id": 456,
        "cv": {...},
        "overall_score": 85,
        ...
    },
    {
        "id": 457,
        "cv": {...},
        "overall_score": 92,
        ...
    }
]
```

---

## 🏢 Vacancy Management

### List Active Vacancies
```http
GET /api/v1/vacancies/
Authorization: Bearer <access_token>
```

**Response:**
```json
[
    {
        "id": 1,
        "title": "Senior Software Developer",
        "description": "We are looking for an experienced software developer...",
        "requirements": "5+ years of experience with Python, Django...",
        "company": {
            "id": 1,
            "name": "Tech Corp",
            "location": "San Francisco, CA",
            "industry": "Technology"
        },
        "location": "San Francisco, CA",
        "salary_range": "$120,000 - $150,000",
        "employment_type": "full_time",
        "experience_level": "senior",
        "status": "active",
        "application_count": 0,
        "created_at": "2024-12-01T09:00:00Z",
        "deadline": "2024-12-31T23:59:59Z"
    }
]
```

---

## 🏥 Health Check

### System Health Status
```http
GET /api/v1/health/
```

**Response:**
```json
{
    "status": "healthy",
    "timestamp": "2024-12-01T10:00:00Z",
    "version": "1.0.0",
    "checks": {
        "database": true,
        "cache": true,
        "storage": true
    }
}
```

---

## 🔒 Security Features

### File Upload Security
- **Virus Scanning:** All uploads scanned with ClamAV
- **Magic Number Validation:** File type verification using magic numbers
- **Content Pattern Detection:** Scans for malicious patterns
- **File Quarantine:** Suspicious files automatically quarantined
- **Size Limits:** Configurable file size restrictions
- **Hash Verification:** MD5 and SHA256 checksums generated

### Request Security
- **Rate Limiting:** Different limits per endpoint type
- **Input Validation:** Comprehensive sanitization
- **XSS Protection:** Pattern detection and blocking
- **SQL Injection Prevention:** Query parameter validation
- **IP Filtering:** Allowlist/blocklist support

### Authentication Security
- **Token Expiration:** Configurable JWT expiration
- **Refresh Token Rotation:** Automatic token refresh
- **API Key Management:** Secure key generation and storage
- **Session Security:** User agent validation
- **Audit Logging:** Complete authentication tracking

---

## 📊 Error Handling

### Error Response Format
```json
{
    "error": "error_code",
    "message": "Human-readable error description",
    "details": {
        "field_errors": {
            "field_name": ["Error message"]
        }
    },
    "timestamp": "2024-12-01T10:00:00Z"
}
```

### Common Error Codes

| Status Code | Error Code | Description |
|-------------|------------|-------------|
| 400 | `validation_error` | Invalid request data |
| 401 | `authentication_failed` | Invalid credentials |
| 401 | `token_expired` | JWT token expired |
| 403 | `permission_denied` | Insufficient permissions |
| 404 | `not_found` | Resource not found |
| 413 | `file_too_large` | Uploaded file exceeds size limit |
| 415 | `unsupported_media_type` | Invalid file type |
| 429 | `rate_limit_exceeded` | Too many requests |
| 500 | `internal_error` | Server error |

---

## 🔄 API Versioning

### Version Selection Methods

1. **URL Path (Recommended):**
   ```
   /api/v1/cv/upload/
   /api/v2/cv/upload/
   ```

2. **Accept Header:**
   ```http
   Accept: application/vnd.cvanalyzer.v1+json
   ```

3. **Custom Header:**
   ```http
   API-Version: v1
   ```

### Version Differences

#### v1 (Current)
- Basic response formats
- Standard error handling
- JWT and API key authentication

#### v2 (Coming Soon)
- Enhanced response formats with metadata
- Improved error details
- Additional authentication methods
- Webhook support

---

## 🚀 Rate Limits

| Endpoint Type | Limit | Window |
|---------------|-------|--------|
| Upload | 10 requests | 1 hour |
| API Calls | 100 requests | 1 hour |
| Login | 5 attempts | 5 minutes |
| General | 1000 requests | 1 hour |

Rate limit headers included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset timestamp

---

## 📝 Examples

### Complete Upload Workflow

1. **Login to get token:**
```bash
curl -X POST https://api.example.com/api/v1/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "user", "password": "secure_password"}'
```

2. **Upload CV:**
```bash
curl -X POST https://api.example.com/api/v1/cv/upload/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "cv_file=@resume.pdf"
```

3. **Check analysis status:**
```bash
curl -X GET https://api.example.com/api/v1/cv/analysis/456/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Using API Keys

1. **Generate API key (after login):**
```bash
curl -X POST https://api.example.com/api/v1/auth/api-key/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name": "Production API", "expires_days": 365}'
```

2. **Use API key for requests:**
```bash
curl -X GET https://api.example.com/api/v1/vacancies/ \
  -H "X-API-Key: cvanalyzer_abc123_def456"
```

---

## 🔧 SDK and Libraries

### Python SDK (Recommended)
```python
from cv_analyzer_sdk import CVAnalyzerClient

client = CVAnalyzerClient(
    base_url="https://api.example.com",
    api_key="cvanalyzer_abc123_def456"
)

# Upload CV
with open("resume.pdf", "rb") as f:
    result = client.upload_cv(f)
    
# Get analysis
analysis = client.get_analysis(result.analysis_id)
print(f"Score: {analysis.overall_score}")
```

### JavaScript SDK
```javascript
import CVAnalyzerAPI from 'cv-analyzer-sdk';

const client = new CVAnalyzerAPI({
    baseURL: 'https://api.example.com',
    apiKey: 'cvanalyzer_abc123_def456'
});

// Upload CV
const fileInput = document.getElementById('cv-file');
const result = await client.uploadCV(fileInput.files[0]);

// Get analysis
const analysis = await client.getAnalysis(result.analysis_id);
console.log(`Score: ${analysis.overall_score}`);
```

---

## 🛡️ Security Best Practices

### For API Consumers

1. **Store credentials securely:**
   - Never hardcode API keys in source code
   - Use environment variables or secure key management
   - Rotate API keys regularly

2. **Implement proper error handling:**
   - Handle rate limits gracefully
   - Implement exponential backoff for retries
   - Log errors appropriately

3. **Validate responses:**
   - Check response status codes
   - Validate response data structure
   - Handle unexpected responses

4. **Use HTTPS only:**
   - Never send credentials over HTTP
   - Verify SSL certificates
   - Use certificate pinning if possible

---

## 📞 Support and Contact

- **Documentation:** https://docs.cvanalyzer.com
- **API Status:** https://status.cvanalyzer.com
- **Support Email:** <EMAIL>
- **Developer Portal:** https://developers.cvanalyzer.com

---

## 📄 Changelog

### v1.0.0 (Current)
- Initial API release
- JWT and API key authentication
- CV upload and analysis endpoints
- Comprehensive security features
- Rate limiting and monitoring

### v2.0.0 (Coming Soon)
- Enhanced response formats
- Webhook support
- Batch processing endpoints
- Advanced analytics
- Multi-language support

---

*Last Updated: December 2024*
*API Version: v1.0.0* 