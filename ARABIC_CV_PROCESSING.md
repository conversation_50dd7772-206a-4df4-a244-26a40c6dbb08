# Arabic CV Processing Enhancement

This document describes the comprehensive Arabic language processing capabilities added to the HRSYS CV analyzer system.

## Overview

The Arabic CV processing enhancement provides robust support for analyzing Arabic CVs with high accuracy, including:

- **Arabic Language Detection**: Automatic detection of Arabic content with confidence scoring
- **RTL Text Handling**: Proper handling of Right-to-Left Arabic text
- **Arabic OCR**: Advanced OCR capabilities for image-based Arabic PDFs
- **Translation Services**: Bidirectional Arabic-English translation
- **Arabic NLP**: Specialized NLP processing for Arabic text
- **Validation & Quality Assurance**: Comprehensive validation of Arabic processing results

## Key Features

### 1. Arabic Language Detection (`arabic_processing.py`)
- Detects Arabic, English, and mixed-language content
- Provides confidence scores and language ratios
- Handles Arabic text normalization and reshaping
- Extracts Arabic entities (names, emails, phones)

### 2. Enhanced Text Extraction (`text_extraction/extractor.py`)
- Integrated Arabic language detection in PDF/DOCX extraction
- OCR fallback for image-based Arabic PDFs
- Enhanced metadata with Arabic-specific fields
- Multilingual section detection

### 3. Arabic OCR Processing (`arabic_ocr.py`)
- Specialized OCR for Arabic text using Tesseract
- Image preprocessing optimized for Arabic characters
- Multi-language OCR support (Arabic + English)
- Quality validation for OCR results

### 4. Arabic NLP Processing (`arabic_nlp.py`)
- CAMEL Tools integration for Arabic morphological analysis
- Arabic named entity recognition
- Arabic skill and job title extraction
- Education and experience extraction from Arabic text

### 5. AI Prompt Optimization (`ai/arabic_prompts.py`)
- Specialized prompts for Arabic CV analysis
- Cultural context awareness
- Bilingual capability assessment
- Arabic vacancy matching prompts

### 6. Comprehensive Validation (`arabic_validation.py`)
- Quality scoring for Arabic processing results
- Issue detection and categorization
- Processing recommendations
- Batch validation capabilities

### 7. Utility Functions (`arabic_cv_utils.py`)
- End-to-end Arabic CV processing pipeline
- Job compatibility analysis
- Batch processing capabilities
- Quality assessment and ranking

## Installation

### Required Dependencies

Add these packages to your `requirements.txt`:

```txt
# Arabic Language Processing
spacy>=3.7.0
langdetect>=1.0.9
arabic-reshaper>=3.0.0
python-bidi>=0.4.2
pyarabic>=0.6.15
camel-tools>=1.5.2
googletrans>=4.0.0
deep-translator>=1.11.4

# Enhanced OCR
pytesseract>=0.3.10
PyMuPDF>=1.23.0
opencv-python>=4.8.0
```

### System Requirements

1. **Tesseract OCR** with Arabic language support:
   ```bash
   # Ubuntu/Debian
   sudo apt-get install tesseract-ocr tesseract-ocr-ara
   
   # Windows
   # Download from: https://github.com/UB-Mannheim/tesseract/wiki
   ```

2. **spaCy Arabic Model** (optional but recommended):
   ```bash
   python -m spacy download ar_core_news_sm
   ```

## Usage Examples

### Basic Arabic CV Processing

```python
from cv_analyzer.arabic_cv_utils import ArabicCVProcessor

processor = ArabicCVProcessor()

# Process a single Arabic CV
result = processor.process_arabic_cv('path/to/arabic_cv.pdf')

if result['success']:
    print(f"Language: {result['language_analysis']['language']}")
    print(f"Arabic Ratio: {result['language_analysis']['arabic_ratio']:.2%}")
    print(f"Quality Score: {result['quality_score']:.2f}")
    
    if result['translation']:
        print(f"Translation: {result['translation']['translated_text']}")
```

### Job Compatibility Analysis

```python
# Analyze Arabic CV for specific job
job_details = {
    'title': 'Senior Software Engineer',
    'company': 'Tech Company',
    'location': 'Riyadh, Saudi Arabia',
    'description': 'We are looking for a senior software engineer...',
    'requirements': 'Bachelor degree, 5+ years experience, Java, Python',
    'language_requirements': 'Arabic native, English fluent'
}

result = processor.analyze_arabic_cv_for_job('cv.pdf', job_details)

if result['success']:
    analysis = result['job_analysis']
    print(f"Overall Score: {analysis['compatibility_scores']['overall']}")
    print(f"Language Match: {analysis['compatibility_scores']['language_match']}")
```

### Batch Processing

```python
# Process multiple Arabic CVs
cv_files = ['cv1.pdf', 'cv2.pdf', 'cv3.pdf']
batch_result = processor.batch_process_arabic_cvs(cv_files, job_details)

print(f"Processed: {batch_result['processing_summary']['successful_processing']}")
print(f"Arabic CVs: {batch_result['processing_summary']['arabic_cvs']}")

# Get top candidates
for candidate in batch_result['rankings'][:3]:
    print(f"CV: {candidate['cv_path']}, Score: {candidate['score']:.1f}")
```

### Direct Arabic Text Analysis

```python
from cv_analyzer.arabic_processing import ArabicTextProcessor
from cv_analyzer.arabic_nlp import ArabicNLPProcessor

# Language detection
arabic_processor = ArabicTextProcessor()
lang_info = arabic_processor.detect_language("أحمد محمد مهندس برمجيات")
print(f"Is Arabic: {lang_info['is_arabic']}")

# NLP processing
nlp_processor = ArabicNLPProcessor()
nlp_result = nlp_processor.process_arabic_cv_text(arabic_text)
print(f"Skills found: {len(nlp_result['skills'])}")
print(f"Job titles: {len(nlp_result['job_titles'])}")
```

## API Integration

### Enhanced AI Service

The AI service automatically detects Arabic content and uses appropriate prompts:

```python
from cv_analyzer.ai.service import CVAnalysisService

ai_service = CVAnalysisService()

# Automatically uses Arabic-optimized prompts for Arabic CVs
analysis = await ai_service.analyze_cv(
    cv_text=arabic_cv_text,
    position="Software Engineer",
    company="Tech Company",
    requirements="5+ years experience, Java, Python"
)
```

### Vacancy Matching

```python
# Specialized Arabic vacancy matching
compatibility = await ai_service.analyze_cv_for_vacancy(
    cv_text=arabic_cv_text,
    vacancy_title="Senior Developer",
    company_name="Saudi Tech",
    vacancy_location="Riyadh",
    vacancy_description="...",
    vacancy_requirements="...",
    language_requirements="Arabic native, English fluent"
)
```

## Configuration

### Environment Variables

```bash
# Translation service settings
GOOGLE_TRANSLATE_API_KEY=your_api_key
TRANSLATION_CACHE_TTL=3600

# OCR settings
TESSERACT_CMD=/usr/bin/tesseract
OCR_CONFIDENCE_THRESHOLD=60

# Arabic processing settings
ARABIC_CONFIDENCE_THRESHOLD=0.7
MIN_ARABIC_RATIO=0.1
```

### Django Settings

```python
# settings.py
ARABIC_PROCESSING = {
    'ENABLE_OCR_FALLBACK': True,
    'TRANSLATION_SERVICE': 'google',  # or 'deep_translator'
    'CACHE_TRANSLATIONS': True,
    'VALIDATION_ENABLED': True,
    'QUALITY_THRESHOLD': 0.6,
}
```

## Quality Assurance

### Validation Levels

The system provides three levels of validation:

1. **CRITICAL**: Issues that prevent processing
2. **WARNING**: Issues that may affect accuracy
3. **INFO**: Informational notices

### Quality Metrics

- **Text Quality**: Length, encoding, character presence
- **Language Detection**: Confidence, ratio accuracy
- **Entity Extraction**: Completeness, accuracy
- **Translation Quality**: Length ratios, completeness
- **Processing Completeness**: Required fields presence

### Example Validation

```python
from cv_analyzer.arabic_validation import ArabicCVValidator

validator = ArabicCVValidator()
validation = validator.validate_cv_processing(original_text, processed_data)

print(f"Valid: {validation.is_valid}")
print(f"Quality Score: {validation.quality_score:.2f}")
print(f"Issues: {len(validation.issues)}")

for issue in validation.issues:
    print(f"- {issue.level.value}: {issue.message}")
```

## Testing

Run the comprehensive test suite:

```bash
# Run all Arabic processing tests
python -m pytest tests/test_arabic_processing.py -v

# Run specific test categories
python -m pytest tests/test_arabic_processing.py::TestArabicTextProcessor -v
python -m pytest tests/test_arabic_processing.py::TestArabicNLPProcessor -v
```

## Performance Considerations

### Optimization Tips

1. **Caching**: Enable translation and language detection caching
2. **Batch Processing**: Use batch methods for multiple CVs
3. **OCR Fallback**: Only enable OCR when necessary
4. **Quality Thresholds**: Set appropriate quality thresholds

### Expected Performance

- **Language Detection**: ~10ms per CV
- **Text Extraction**: ~100-500ms per PDF
- **OCR Processing**: ~2-5 seconds per page
- **NLP Analysis**: ~200-800ms per CV
- **Translation**: ~500-2000ms per CV (depending on service)

## Troubleshooting

### Common Issues

1. **Tesseract Not Found**
   ```bash
   # Install Tesseract and Arabic language pack
   sudo apt-get install tesseract-ocr tesseract-ocr-ara
   ```

2. **Low OCR Accuracy**
   - Check image quality and resolution
   - Verify Arabic language pack installation
   - Adjust OCR confidence thresholds

3. **Translation Failures**
   - Verify internet connection
   - Check API keys and quotas
   - Enable fallback translation services

4. **Memory Issues with Large Files**
   - Process files in batches
   - Enable garbage collection
   - Increase system memory limits

### Debug Mode

Enable debug logging for detailed processing information:

```python
import logging
logging.getLogger('cv_analyzer.arabic_processing').setLevel(logging.DEBUG)
```

## Future Enhancements

- Support for additional Arabic dialects
- Integration with more translation services
- Advanced Arabic NER models
- Real-time processing capabilities
- Arabic CV template recognition
- Enhanced cultural context analysis

## Support

For issues and questions related to Arabic CV processing:

1. Check the validation results for processing issues
2. Review the debug logs for detailed error information
3. Verify all dependencies are properly installed
4. Test with sample Arabic CVs to isolate issues
