# 🚀 CV Analyzer - Production Deployment Guide

## 📋 Overview
This guide provides comprehensive instructions for deploying the enterprise-ready CV Analyzer application to production environments.

**Application Status:** ✅ 100% Production Ready  
**Deployment Type:** Enterprise-Grade Production System  
**Target Environments:** AWS, Azure, Google Cloud, On-Premises

---

## 🎯 Pre-Deployment Checklist

### ✅ System Requirements Met
- [x] All 8 development phases completed
- [x] Security hardening implemented
- [x] Performance optimization completed
- [x] Testing and quality assurance passed
- [x] Monitoring and alerting configured
- [x] Documentation completed

### ✅ Infrastructure Requirements
- **Minimum Production Setup:**
  - 2x Application Servers (4 CPU, 8GB RAM each)
  - 1x Database Server (8 CPU, 16GB RAM, SSD storage)
  - 1x Redis Cache Server (2 CPU, 4GB RAM)
  - 1x Load Balancer
  - CDN for static files
  - SSL certificates

- **Recommended Enterprise Setup:**
  - 4x Application Servers (8 CPU, 16GB RAM each)
  - 2x Database Servers (16 CPU, 32GB RAM, NVMe SSD)
  - 2x Redis Cache Servers (4 CPU, 8GB RAM each)
  - Application Load Balancer with auto-scaling
  - CloudFront CDN
  - Multi-AZ deployment

---

## 🔧 Environment Setup

### 1. Server Preparation

#### Ubuntu/Debian Servers
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y python3.11 python3.11-venv python3-pip
sudo apt install -y postgresql-client redis-tools nginx
sudo apt install -y git curl wget unzip
sudo apt install -y supervisor  # For process management

# Install Docker (for containerized deployment)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

#### CentOS/RHEL Servers
```bash
# Update system
sudo yum update -y

# Install required packages
sudo yum install -y python3.11 python3-pip
sudo yum install -y postgresql-client redis nginx
sudo yum install -y git curl wget unzip
sudo yum install -y supervisor

# Install Docker
sudo yum install -y docker
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

### 2. Database Setup (PostgreSQL)

#### Primary Database Server
```bash
# Install PostgreSQL 15
sudo apt install -y postgresql-15 postgresql-contrib-15

# Configure PostgreSQL
sudo -u postgres psql
```

```sql
-- Create database and user
CREATE DATABASE cv_analyzer_prod;
CREATE USER cv_analyzer WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE cv_analyzer_prod TO cv_analyzer;
ALTER USER cv_analyzer CREATEDB;

-- Configure for production
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET work_mem = '4MB';
```

#### Database Configuration (`/etc/postgresql/15/main/postgresql.conf`)
```ini
# Memory settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Connection settings
max_connections = 200
listen_addresses = '*'

# Performance settings
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Logging
log_statement = 'all'
log_duration = on
log_min_duration_statement = 1000
```

### 3. Redis Setup

#### Redis Configuration (`/etc/redis/redis.conf`)
```ini
# Network
bind 0.0.0.0
port 6379
protected-mode yes
requireauth your_redis_password

# Memory
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Security
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
```

---

## 📦 Application Deployment

### 1. Code Deployment

#### Option A: Direct Deployment
```bash
# Create application directory
sudo mkdir -p /opt/cv_analyzer
sudo chown $USER:$USER /opt/cv_analyzer
cd /opt/cv_analyzer

# Clone repository
git clone https://github.com/your-org/cv_analyzer_project.git .

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies
pip install --upgrade pip
pip install -r requirements.txt
```

#### Option B: Docker Deployment
```bash
# Build Docker image
docker build -t cv-analyzer:latest .

# Run with Docker Compose
docker-compose -f docker-compose.prod.yml up -d
```

### 2. Environment Configuration

#### Create Production Environment File (`.env.prod`)
```bash
# Django Settings
DEBUG=False
SECRET_KEY=your_super_secret_key_here
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com,your-server-ip

# Database
DATABASE_URL=************************************************/cv_analyzer_prod

# Redis
REDIS_URL=redis://:password@redis-server:6379/0

# AI Providers
OPENAI_API_KEY=your_openai_api_key
GROQ_API_KEY=your_groq_api_key

# AWS (if using)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket
AWS_S3_REGION_NAME=us-east-1

# Email
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password
EMAIL_USE_TLS=True

# Security
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True

# Monitoring
SENTRY_DSN=your_sentry_dsn

# File Upload
MAX_UPLOAD_SIZE=10485760  # 10MB
VIRUS_SCAN_ENABLED=True
```

### 3. Database Migration
```bash
# Activate virtual environment
source venv/bin/activate

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput

# Load initial data (if any)
python manage.py loaddata initial_data.json
```

### 4. Static Files and Media

#### Nginx Configuration (`/etc/nginx/sites-available/cv_analyzer`)
```nginx
upstream cv_analyzer {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;  # If using multiple app servers
}

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # File Upload Limits
    client_max_body_size 10M;

    # Static Files
    location /static/ {
        alias /opt/cv_analyzer/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Media Files
    location /media/ {
        alias /opt/cv_analyzer/media/;
        expires 1y;
        add_header Cache-Control "public";
    }

    # Application
    location / {
        proxy_pass http://cv_analyzer;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # WebSocket support (if needed)
    location /ws/ {
        proxy_pass http://cv_analyzer;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

---

## 🔄 Process Management

### 1. Gunicorn Configuration

#### Gunicorn Configuration File (`gunicorn.conf.py`)
```python
# Server socket
bind = "127.0.0.1:8000"
backlog = 2048

# Worker processes
workers = 4  # (2 x CPU cores) + 1
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers
max_requests = 1000
max_requests_jitter = 50
preload_app = True

# Logging
accesslog = "/var/log/cv_analyzer/access.log"
errorlog = "/var/log/cv_analyzer/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# Process naming
proc_name = "cv_analyzer"

# Server mechanics
daemon = False
pidfile = "/var/run/cv_analyzer/gunicorn.pid"
user = "www-data"
group = "www-data"
tmp_upload_dir = None

# SSL (if terminating SSL at application level)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"
```

### 2. Supervisor Configuration

#### Supervisor Configuration (`/etc/supervisor/conf.d/cv_analyzer.conf`)
```ini
[program:cv_analyzer]
command=/opt/cv_analyzer/venv/bin/gunicorn cv_analyzer_project.wsgi:application -c /opt/cv_analyzer/gunicorn.conf.py
directory=/opt/cv_analyzer
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/cv_analyzer/supervisor.log
environment=PATH="/opt/cv_analyzer/venv/bin"

[program:cv_analyzer_celery]
command=/opt/cv_analyzer/venv/bin/celery -A cv_analyzer_project worker -l info
directory=/opt/cv_analyzer
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/cv_analyzer/celery.log
environment=PATH="/opt/cv_analyzer/venv/bin"

[program:cv_analyzer_celery_beat]
command=/opt/cv_analyzer/venv/bin/celery -A cv_analyzer_project beat -l info
directory=/opt/cv_analyzer
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/cv_analyzer/celery_beat.log
environment=PATH="/opt/cv_analyzer/venv/bin"
```

---

## 📊 Monitoring Setup

### 1. Health Check Endpoint
```bash
# Test application health
curl -f http://localhost:8000/health/ || exit 1

# Test database connectivity
python manage.py health_check --component=database

# Test cache connectivity
python manage.py health_check --component=cache

# Full system check
python manage.py health_check --component=all --detailed
```

### 2. Log Monitoring

#### Logrotate Configuration (`/etc/logrotate.d/cv_analyzer`)
```
/var/log/cv_analyzer/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        supervisorctl restart cv_analyzer
    endscript
}
```

### 3. System Monitoring Script
```bash
#!/bin/bash
# /opt/cv_analyzer/scripts/monitor.sh

# Check application status
if ! curl -f http://localhost:8000/health/ > /dev/null 2>&1; then
    echo "Application health check failed"
    # Send alert
    # Restart application if needed
fi

# Check disk space
DISK_USAGE=$(df /opt/cv_analyzer | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is ${DISK_USAGE}%"
    # Send alert
fi

# Check memory usage
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
if [ $MEMORY_USAGE -gt 85 ]; then
    echo "Memory usage is ${MEMORY_USAGE}%"
    # Send alert
fi
```

---

## 🔐 Security Configuration

### 1. Firewall Setup (UFW)
```bash
# Enable firewall
sudo ufw enable

# Allow SSH
sudo ufw allow ssh

# Allow HTTP/HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow database (only from app servers)
sudo ufw allow from app-server-ip to any port 5432

# Allow Redis (only from app servers)
sudo ufw allow from app-server-ip to any port 6379
```

### 2. SSL Certificate Setup (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. Security Hardening
```bash
# Disable root login
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config

# Change SSH port (optional)
sudo sed -i 's/#Port 22/Port 2222/' /etc/ssh/sshd_config

# Install fail2ban
sudo apt install fail2ban

# Configure fail2ban
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
# Edit /etc/fail2ban/jail.local as needed
```

---

## 🚀 Deployment Commands

### 1. Initial Deployment
```bash
# Deploy application
./scripts/deploy.sh production

# Run health checks
python manage.py health_check --component=all --save-results

# Start monitoring
sudo systemctl start supervisor
sudo systemctl enable supervisor
```

### 2. Update Deployment
```bash
# Pull latest code
git pull origin main

# Update dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Collect static files
python manage.py collectstatic --noinput

# Restart application
sudo supervisorctl restart cv_analyzer
```

### 3. Rollback Procedure
```bash
# Rollback to previous version
git checkout previous-stable-tag

# Restore database (if needed)
pg_restore -d cv_analyzer_prod backup_file.sql

# Restart services
sudo supervisorctl restart all
```

---

## 📈 Performance Optimization

### 1. Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX CONCURRENTLY idx_cv_uploaded_at ON cv_analyzer_cv(uploaded_at);
CREATE INDEX CONCURRENTLY idx_analysis_overall_score ON cv_analyzer_cvanalysis(overall_score);
CREATE INDEX CONCURRENTLY idx_vacancy_status ON cv_analyzer_vacancy(status);

-- Analyze tables
ANALYZE;
```

### 2. Cache Warming
```bash
# Warm up cache after deployment
python manage.py warm_cache

# Pre-generate reports
python manage.py generate_reports --cache
```

### 3. CDN Configuration
```bash
# Configure CloudFront (AWS) or equivalent
# Point to your static files and media
# Set appropriate cache headers
```

---

## 🔍 Testing Production Deployment

### 1. Smoke Tests
```bash
# Test basic functionality
curl -I https://yourdomain.com/
curl -I https://yourdomain.com/api/health/

# Test file upload
curl -X POST -F "file=@test.pdf" https://yourdomain.com/api/upload/

# Test authentication
curl -X POST -d "username=test&password=test" https://yourdomain.com/api/auth/login/
```

### 2. Load Testing
```bash
# Install Apache Bench
sudo apt install apache2-utils

# Basic load test
ab -n 1000 -c 10 https://yourdomain.com/

# More comprehensive testing with wrk
wrk -t12 -c400 -d30s https://yourdomain.com/
```

### 3. Security Testing
```bash
# SSL test
nmap --script ssl-enum-ciphers -p 443 yourdomain.com

# Security headers test
curl -I https://yourdomain.com/

# Vulnerability scan
nikto -h https://yourdomain.com/
```

---

## 📋 Post-Deployment Checklist

### ✅ Immediate Checks (First 24 Hours)
- [ ] Application responds correctly
- [ ] Database connections working
- [ ] File uploads functioning
- [ ] AI analysis working
- [ ] Email notifications sending
- [ ] SSL certificate valid
- [ ] Monitoring alerts configured
- [ ] Backup systems running
- [ ] Log rotation working

### ✅ Weekly Checks
- [ ] Performance metrics review
- [ ] Security log analysis
- [ ] Database performance check
- [ ] Backup verification
- [ ] Capacity planning review
- [ ] User feedback analysis

### ✅ Monthly Checks
- [ ] Security updates applied
- [ ] Performance optimization review
- [ ] Disaster recovery test
- [ ] Compliance audit
- [ ] Cost optimization review

---

## 🆘 Troubleshooting

### Common Issues and Solutions

#### Application Won't Start
```bash
# Check logs
tail -f /var/log/cv_analyzer/error.log
sudo supervisorctl status

# Check configuration
python manage.py check --deploy

# Test database connection
python manage.py dbshell
```

#### High Memory Usage
```bash
# Check memory usage
free -h
ps aux --sort=-%mem | head

# Restart workers
sudo supervisorctl restart cv_analyzer_celery
```

#### Database Performance Issues
```sql
-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Check database connections
SELECT count(*) FROM pg_stat_activity;
```

#### File Upload Issues
```bash
# Check disk space
df -h

# Check permissions
ls -la /opt/cv_analyzer/media/

# Check virus scanner
systemctl status clamav-daemon
```

---

## 📞 Support and Maintenance

### Emergency Contacts
- **System Administrator:** <EMAIL>
- **Database Administrator:** <EMAIL>
- **Security Team:** <EMAIL>
- **On-Call Engineer:** +1-XXX-XXX-XXXX

### Maintenance Windows
- **Regular Maintenance:** Sundays 2:00-4:00 AM UTC
- **Emergency Maintenance:** As needed with 1-hour notice
- **Security Updates:** Within 24 hours of release

### Backup and Recovery
- **Database Backups:** Daily at 2:00 AM UTC
- **File Backups:** Daily at 3:00 AM UTC
- **Configuration Backups:** Weekly
- **Recovery Time Objective (RTO):** < 30 minutes
- **Recovery Point Objective (RPO):** < 5 minutes

---

## 🎉 Deployment Complete!

Congratulations! Your CV Analyzer application is now deployed and ready for production use.

**Next Steps:**
1. Monitor application performance for the first 48 hours
2. Conduct user acceptance testing
3. Train end users on the new system
4. Set up regular maintenance schedules
5. Plan for future enhancements

**Support Resources:**
- [User Documentation](./USER_GUIDE.md)
- [API Documentation](./API_DOCS.md)
- [Admin Guide](./ADMIN_GUIDE.md)
- [Troubleshooting Guide](./TROUBLESHOOTING.md)

---

**Document Version:** 1.0  
**Last Updated:** December 2024  
**Next Review:** Monthly  
**Maintained By:** DevOps Team 