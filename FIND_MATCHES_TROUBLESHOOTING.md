# Find Matches AI Feature - Troubleshooting Guide

## 🎯 Issue: AI Not Triggering for Vacancy Matching

If the AI is not being triggered when you click "Find Matches", follow this troubleshooting guide:

## ✅ Step 1: Verify System Requirements

### **Check Django Server**
```bash
python manage.py runserver 0.0.0.0:8000
```
- Server should start without errors
- Look for: `Starting development server at http://0.0.0.0:8000/`

### **Check Database**
```bash
python manage.py check
```
- Should show: `System check identified no issues (0 silenced).`

## ✅ Step 2: Verify Data Prerequisites

### **Check if CVs are Analyzed**
1. Go to Django Admin: `http://127.0.0.1:8000/admin/`
2. Check `CV Analyzer > CV analyses`
3. **Requirement**: Must have CVs with `status = 'analyzed'` and existing `CVAnalysis` records

### **Check if Vacancies Exist**
1. Go to Django Admin: `http://127.0.0.1:8000/admin/`
2. Check `CV Analyzer > Vacancies`
3. **Requirement**: Must have at least one vacancy with title and requirements

### **Check AI Configuration**
1. Go to Django Admin: `http://127.0.0.1:8000/admin/`
2. Check `CV Analyzer > AI API configs`
3. **Requirement**: Must have at least one active AI provider (OpenAI or Groq)

## ✅ Step 3: Test the Feature Step-by-Step

### **Access Operations Hub**
1. Navigate to: `http://127.0.0.1:8000/operations-hub/`
2. Should load without errors
3. Look for "Analyze CVs" button

### **Open CV Matching**
1. Click **"Analyze CVs"** button
2. Select **"CV Matching"** analysis type
3. Should see vacancy dropdown populated

### **Trigger AI Analysis**
1. Select a vacancy from dropdown
2. Click **"Find Matches"** button
3. Button should change to: "🧠 AI Analyzing CVs..."

## ✅ Step 4: Monitor Debug Output

### **Server Console Debugging**
When you click "Find Matches", check the Django server console for:

```
DEBUG: match_cvs_api called by user [username]
DEBUG: Processing vacancy_id: [number]
DEBUG: Found [N] analyzed CVs for matching
```

### **Common Debug Messages**

**✅ Success Pattern:**
```
DEBUG: match_cvs_api called by user admin
DEBUG: Processing vacancy_id: 1
DEBUG: Found 5 analyzed CVs for matching
```

**❌ No CVs Found:**
```
DEBUG: match_cvs_api called by user admin
DEBUG: Processing vacancy_id: 1
DEBUG: Found 0 analyzed CVs for matching
DEBUG: No analyzed CVs found - returning empty result
```

**❌ AI Error:**
```
DEBUG: AI analysis error for CV 15: [error message]
```

## 🔧 Common Issues & Solutions

### **Issue 1: "No analyzed CVs available for matching"**

**Cause**: No CVs have been analyzed yet
**Solution**:
1. Go to Dashboard: `http://127.0.0.1:8000/dashboard/`
2. Upload some CVs
3. Analyze them using Single CV Analysis
4. Wait for analysis to complete
5. Try Find Matches again

### **Issue 2: AI Analysis Fails**

**Cause**: AI service configuration issues
**Solutions**:

**Check AI API Configuration:**
```bash
# In Django shell
python manage.py shell
>>> from cv_analyzer.models import AIAPIConfig
>>> configs = AIAPIConfig.objects.filter(is_active=True)
>>> for config in configs:
...     print(f"Provider: {config.provider}, Model: {config.model_name}, Active: {config.is_active}")
```

**Test AI Service Directly:**
```python
# In Django shell
from cv_analyzer.ai.service import CVAnalysisService
import asyncio

service = CVAnalysisService()
loop = asyncio.get_event_loop()

# Test basic functionality
result = loop.run_until_complete(
    service.analyze_cv(
        cv_text="Test CV content",
        position="Software Developer",
        company="Test Company",
        requirements="Python programming skills"
    )
)
print(result)
```

### **Issue 3: Button Doesn't Respond**

**Cause**: JavaScript errors or missing functions
**Solutions**:

**Check Browser Console:**
1. Open browser Developer Tools (F12)
2. Go to Console tab
3. Click "Find Matches"
4. Look for JavaScript errors

**Common JavaScript Errors:**
- `startMatching is not defined` → Function not loaded
- `CSRF token not found` → CSRF middleware issue
- `Network error` → Server not responding

### **Issue 4: Authentication Issues**

**Cause**: User not properly authenticated
**Solutions**:
1. Login to Django admin first: `http://127.0.0.1:8000/admin/`
2. Then access Operations Hub
3. Ensure `@login_required` decorator is working

## 🚀 Quick Fix Commands

### **Reset and Test Everything**
```bash
# 1. Stop server (Ctrl+C)
# 2. Check system
python manage.py check

# 3. Migrate database
python manage.py migrate

# 4. Create superuser if needed
python manage.py createsuperuser

# 5. Start server with debug
python manage.py runserver 0.0.0.0:8000

# 6. Test in browser
# Navigate to: http://127.0.0.1:8000/operations-hub/
```

### **Generate Test Data**
```bash
# Create demo data for testing
python manage.py shell
>>> from cv_analyzer.management.commands.generate_demo_data import Command
>>> cmd = Command()
>>> cmd.handle()
```

## 📊 Verification Checklist

Before reporting issues, verify:

- [ ] Django server running without errors
- [ ] At least 1 active AI API configuration
- [ ] At least 1 analyzed CV in database
- [ ] At least 1 vacancy in database
- [ ] User is authenticated
- [ ] Browser console shows no JavaScript errors
- [ ] Server console shows debug messages when clicking "Find Matches"

## 🔍 Advanced Debugging

### **Enable Detailed Logging**
Add to `settings.py`:
```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'cv_analyzer.views': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
        'cv_analyzer.ai.service': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

### **Test AI Service Independently**
```python
# Create test script: test_ai_service.py
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cv_analyzer_project.settings')
django.setup()

from cv_analyzer.ai.service import CVAnalysisService
import asyncio

async def test_ai():
    service = CVAnalysisService()
    result = await service.analyze_cv(
        cv_text="John Doe, Software Engineer, 5 years experience in Python, Django, React",
        position="Senior Software Developer",
        company="Tech Corp",
        requirements="5+ years Python, Django, React experience"
    )
    print("AI Result:", result)

asyncio.run(test_ai())
```

## 📞 Support Information

If issues persist after following this guide:

1. **Check Server Console**: Look for specific error messages
2. **Check Browser Console**: Look for JavaScript errors
3. **Verify Data**: Ensure CVs are analyzed and vacancies exist
4. **Test AI Service**: Use the independent test script above

The Find Matches feature should trigger AI analysis when all prerequisites are met and properly configured. 