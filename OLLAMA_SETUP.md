# 🚀 Ollama Setup Guide for CV Analyzer

This guide will help you configure Ollama as your AI provider for CV analysis.

## 📋 Quick Setup

### 1. Automatic Configuration (Recommended)

Use the built-in management command to automatically configure Ollama:

```bash
# Basic setup with default server and model
python manage.py setup_ollama

# Custom server and model
python manage.py setup_ollama --server *************:11434 --model qwen-qwq-32b

# Force update existing configuration
python manage.py setup_ollama --force
```

### 2. Manual Configuration

If you prefer to configure manually through Django Admin:

1. **Access Django Admin**: Go to `/admin/cv_analyzer/aiapiconfig/`
2. **Add Configuration**:
   - Provider: `ollama`
   - API Key: `*************:11434` (or just `*************`)
   - Model Name: Click "🔄 Fetch Available Models" to see options
   - Priority: `1` (highest)
   - Is Active: ✅ checked

## 🔧 Configuration Options

### Server URL Formats

The system accepts multiple URL formats:

- **Full URL**: `http://*************:11434`
- **IP with Port**: `*************:11434`
- **IP Only**: `*************` (assumes port 11434)
- **Empty**: Uses default server `http://*************:11434`

### Available Models

Current models on your server:
- `huihui_ai/deepseek-r1-abliterated:14b` ✅ (Currently configured)
- `qwen3:14b`
- `gemma3:12b`

To see all available models, run:
```bash
python test_ollama.py
```

## 🧪 Testing

### Test Ollama Connection
```bash
python test_ollama.py
```

### Test CV Analysis
1. Upload a CV through the web interface
2. Click "Analyze CV"
3. Check the results

### Debug Configuration
Visit: `http://127.0.0.1:8000/debug/ai-config/`

## 🏃‍♂️ Quick Commands

```bash
# Setup Ollama with best available model
python manage.py setup_ollama

# Test connection
python test_ollama.py

# Check current configuration
python manage.py shell -c "from cv_analyzer.models import AIAPIConfig; print(AIAPIConfig.objects.filter(provider='ollama').first())"

# Force reconfigure with specific model
python manage.py setup_ollama --model huihui_ai/deepseek-r1-abliterated:14b --force
```

## 🔄 Model Updates

When new models are available on your Ollama server:

1. **Automatic**: Run `python manage.py setup_ollama --force`
2. **Manual**: Use "Fetch Available Models" in Django Admin
3. **Command Line**: Update with specific model:
   ```bash
   python manage.py setup_ollama --model new-model-name --force
   ```

## 🎯 Performance Optimization

The system automatically optimizes settings for different models:

- **Temperature**: 0.3 (balanced creativity/consistency)
- **Max Tokens**: 2000 (sufficient for CV analysis)
- **Top K**: 40 (focused responses)
- **Top P**: 0.9 (quality control)
- **Repeat Penalty**: 1.1 (avoid repetition)

## 🐛 Troubleshooting

### Connection Issues
```bash
# Test server connectivity
python test_ollama.py

# Check if server is running
curl http://*************:11434/api/tags
```

### Model Issues
```bash
# List available models
python test_ollama.py

# Update configuration with working model
python manage.py setup_ollama --model available-model-name --force
```

### Analysis Failures
1. Check `/debug/ai-config/` for configuration status
2. Verify model is available on server
3. Check Django logs for detailed errors

## 🚀 Next Steps

After configuration:
1. Upload test CV
2. Run analysis
3. Check results quality
4. Adjust model if needed

Your CV Analyzer is now powered by Ollama! 🎉 