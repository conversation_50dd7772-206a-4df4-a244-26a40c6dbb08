# CV Analyzer

CV Analyzer is a Django-based web application that helps streamline the recruitment process by analyzing and evaluating CVs quickly and efficiently using AI-powered tools.

## Features

- Upload and manage multiple CVs
- Create and manage job vacancies
- AI-powered CV analysis
- Match CVs against job requirements
- Comprehensive analysis reports
- User authentication and authorization
- Company and vacancy management
- CV statistics and visualization

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/cv-analyzer.git
   cd cv-analyzer
   ```

2. Create a virtual environment and activate it:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows, use `venv\Scripts\activate`
   ```

3. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

4. Set up the database:
   ```
   python manage.py migrate
   ```

5. Create a superuser:
   ```
   python manage.py createsuperuser
   ```

6. Run the development server:
   ```
   python manage.py runserver
   ```

7. Access the application at `http://localhost:8000`

## Configuration

1. Set up your OpenAI API key in `cv_analyzer_project/settings.py`:
   ```python
   OPENAI_API_KEY = 'your-api-key-here'
   ```

2. Customize other settings as needed in the `settings.py` file.

## Usage

1. Log in to the application using your superuser credentials.
2. Upload CVs through the "New Analysis" page.
3. Create job vacancies in the "Vacancy Management" section.
4. View and manage CVs in the "CV Management" section.
5. Access analysis results and reports in the "Dashboard" and "Analysis Process" pages.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License. See the LICENSE file for details.
