"""
Custom scoring engine for CV analysis with configurable weighted scoring.

This module provides a sophisticated scoring system that allows customizable 
weighting of different components (education, experience, skills, responsibilities)
for more precise CV-vacancy matching.
"""

import json
import re
from typing import Dict, List, Optional, Tuple, Any
from difflib import SequenceMatcher
from django.db import transaction
from django.core.exceptions import ValidationError
from django.utils import timezone
import logging

from cv_analyzer.models import (
    CVAnalysis, 
    Vacancy, 
    VacancyScoringRules, 
    CustomScoringResult,
    ScoringTemplate
)

logger = logging.getLogger(__name__)


class CustomScoringEngine:
    """
    Custom scoring engine for CV analysis with configurable weighted scoring.
    
    This engine provides detailed scoring across multiple dimensions:
    - Education scoring with degree level matching
    - Experience scoring with years-based calculations
    - Skills scoring with semantic similarity
    - Responsibilities scoring with keyword matching
    """
    
    def __init__(self):
        self.similarity_threshold = 0.7  # Default similarity threshold
        self.debug_mode = False
        
    def calculate_custom_score(self, cv_analysis: CVAnalysis, vacancy: Vacancy) -> CustomScoringResult:
        """
        Calculate custom weighted score for a CV against a vacancy.
        
        Args:
            cv_analysis: The CV analysis object
            vacancy: The vacancy object
            
        Returns:
            CustomScoringResult: The calculated scoring result
        """
        try:
            # Get or create scoring rules for the vacancy
            scoring_rules, created = VacancyScoringRules.objects.get_or_create(
                vacancy=vacancy,
                defaults={
                    'education_weight': 25.0,
                    'experience_weight': 35.0,
                    'skills_weight': 30.0,
                    'responsibilities_weight': 10.0,
                    'education_requirements': self._get_default_education_requirements(),
                    'experience_requirements': self._get_default_experience_requirements(),
                    'skills_requirements': self._get_default_skills_requirements(),
                    'responsibilities_requirements': self._get_default_responsibilities_requirements(),
                }
            )
            
            if created:
                logger.info(f"Created default scoring rules for vacancy {vacancy.id}")
            
            # Calculate individual component scores
            education_score = self._calculate_education_score(cv_analysis, scoring_rules)
            experience_score = self._calculate_experience_score(cv_analysis, scoring_rules)
            skills_score = self._calculate_skills_score(cv_analysis, scoring_rules)
            responsibilities_score = self._calculate_responsibilities_score(cv_analysis, scoring_rules)
            
            # Calculate weighted scores
            weighted_education = education_score * (scoring_rules.education_weight / 100.0)
            weighted_experience = experience_score * (scoring_rules.experience_weight / 100.0)
            weighted_skills = skills_score * (scoring_rules.skills_weight / 100.0)
            weighted_responsibilities = responsibilities_score * (scoring_rules.responsibilities_weight / 100.0)
            
            # Calculate final score
            final_score = weighted_education + weighted_experience + weighted_skills + weighted_responsibilities
            
            # Check minimum requirements
            meets_requirements = self._check_minimum_requirements(
                education_score, experience_score, skills_score, final_score, scoring_rules
            )
            
            # Generate recommendation
            recommendation = self._generate_recommendation(final_score, meets_requirements)
            
            # Calculate AI vs custom difference
            ai_vs_custom_difference = 0.0
            if hasattr(cv_analysis, 'overall_score') and cv_analysis.overall_score:
                ai_vs_custom_difference = final_score - cv_analysis.overall_score
            
            # Create detailed score breakdown
            score_breakdown = self._create_score_breakdown(
                education_score, experience_score, skills_score, responsibilities_score,
                weighted_education, weighted_experience, weighted_skills, weighted_responsibilities,
                scoring_rules, cv_analysis, vacancy
            )
            
            # Create or update the scoring result
            result, created = CustomScoringResult.objects.update_or_create(
                cv_analysis=cv_analysis,
                vacancy=vacancy,
                defaults={
                    'education_score': education_score,
                    'experience_score': experience_score,
                    'skills_score': skills_score,
                    'responsibilities_score': responsibilities_score,
                    'weighted_education_score': weighted_education,
                    'weighted_experience_score': weighted_experience,
                    'weighted_skills_score': weighted_skills,
                    'weighted_responsibilities_score': weighted_responsibilities,
                    'final_custom_score': final_score,
                    'score_breakdown': score_breakdown,
                    'meets_minimum_requirements': meets_requirements,
                    'custom_recommendation': recommendation,
                    'ai_vs_custom_difference': ai_vs_custom_difference,
                }
            )
            
            logger.info(f"Custom score calculated for CV {cv_analysis.id} vs Vacancy {vacancy.id}: {final_score:.1f}%")
            return result
            
        except Exception as e:
            logger.error(f"Error calculating custom score for CV {cv_analysis.id} vs Vacancy {vacancy.id}: {str(e)}")
            raise
    
    def _calculate_education_score(self, cv_analysis: CVAnalysis, scoring_rules: VacancyScoringRules) -> float:
        """Calculate education component score."""
        try:
            education_reqs = scoring_rules.education_requirements or self._get_default_education_requirements()
            
            # Get candidate's education level
            candidate_education = (cv_analysis.education_level or '').lower()
            
            # Check education data if available
            if hasattr(cv_analysis, 'education_data') and cv_analysis.education_data:
                education_data = cv_analysis.education_data
                if isinstance(education_data, dict):
                    degrees = education_data.get('degrees', [])
                    if degrees:
                        # Get highest degree
                        highest_degree = max(degrees, key=lambda x: self._get_education_level_score(x.get('degree', '')))
                        candidate_education = highest_degree.get('degree', '').lower()
            
            # Score based on education level
            score = education_reqs.get('default_score', 30.0)
            
            for level, config in education_reqs.items():
                if level == 'default_score':
                    continue
                    
                if isinstance(config, dict) and 'keywords' in config:
                    keywords = config['keywords']
                    if any(keyword in candidate_education for keyword in keywords):
                        score = max(score, config.get('score', 0.0))
            
            return min(100.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"Error calculating education score: {str(e)}")
            return 30.0  # Default score
    
    def _calculate_experience_score(self, cv_analysis: CVAnalysis, scoring_rules: VacancyScoringRules) -> float:
        """Calculate experience component score."""
        try:
            experience_reqs = scoring_rules.experience_requirements or self._get_default_experience_requirements()
            
            # Get candidate's years of experience
            years_experience = cv_analysis.years_of_experience or 0
            
            # Check experience data if available
            if hasattr(cv_analysis, 'experience_data') and cv_analysis.experience_data:
                experience_data = cv_analysis.experience_data
                if isinstance(experience_data, dict):
                    positions = experience_data.get('positions', [])
                    if positions:
                        # Calculate total experience from positions
                        total_months = sum(pos.get('duration_months', 0) for pos in positions)
                        calculated_years = total_months / 12.0
                        years_experience = max(years_experience, calculated_years)
            
            # Score based on experience ranges
            score = experience_reqs.get('default_score', 30.0)
            
            ranges = experience_reqs.get('ranges', [])
            for range_config in ranges:
                min_years = range_config.get('min', 0)
                max_years = range_config.get('max', 999)
                
                if min_years <= years_experience <= max_years:
                    score = max(score, range_config.get('score', 0.0))
            
            return min(100.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"Error calculating experience score: {str(e)}")
            return 30.0  # Default score
    
    def _calculate_skills_score(self, cv_analysis: CVAnalysis, scoring_rules: VacancyScoringRules) -> float:
        """Calculate skills component score with semantic similarity."""
        try:
            skills_reqs = scoring_rules.skills_requirements or self._get_default_skills_requirements()
            
            # Get candidate's skills
            candidate_skills = self._extract_skills(cv_analysis)
            
            if not candidate_skills:
                return skills_reqs.get('default_score', 50.0)
            
            required_skills = skills_reqs.get('required_skills', [])
            preferred_skills = skills_reqs.get('preferred_skills', [])
            matching_threshold = skills_reqs.get('skill_matching_threshold', 0.7)
            
            # Calculate skill matches
            required_matches = 0
            preferred_matches = 0
            
            for required_skill in required_skills:
                if self._skill_matches(required_skill, candidate_skills, matching_threshold):
                    required_matches += 1
            
            for preferred_skill in preferred_skills:
                if self._skill_matches(preferred_skill, candidate_skills, matching_threshold):
                    preferred_matches += 1
            
            # Calculate score based on matches
            score = skills_reqs.get('default_score', 50.0)
            
            if required_skills:
                required_percentage = (required_matches / len(required_skills)) * 100
                score = max(score, required_percentage * 0.8)  # Required skills worth 80%
            
            if preferred_skills:
                preferred_percentage = (preferred_matches / len(preferred_skills)) * 100
                score = max(score, score + (preferred_percentage * 0.2))  # Preferred skills add 20%
            
            return min(100.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"Error calculating skills score: {str(e)}")
            return 50.0  # Default score
    
    def _calculate_responsibilities_score(self, cv_analysis: CVAnalysis, scoring_rules: VacancyScoringRules) -> float:
        """Calculate responsibilities component score."""
        try:
            resp_reqs = scoring_rules.responsibilities_requirements or self._get_default_responsibilities_requirements()
            
            # Get candidate's responsibilities/experience
            candidate_responsibilities = self._extract_responsibilities(cv_analysis)
            
            if not candidate_responsibilities:
                return resp_reqs.get('default_score', 50.0)
            
            key_responsibilities = resp_reqs.get('key_responsibilities', [])
            matching_threshold = resp_reqs.get('responsibility_matching_threshold', 0.6)
            
            if not key_responsibilities:
                return resp_reqs.get('default_score', 50.0)
            
            # Calculate responsibility matches
            matches = 0
            for key_resp in key_responsibilities:
                if self._responsibility_matches(key_resp, candidate_responsibilities, matching_threshold):
                    matches += 1
            
            # Calculate score based on matches
            match_percentage = (matches / len(key_responsibilities)) * 100
            score = max(resp_reqs.get('default_score', 50.0), match_percentage)
            
            return min(100.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"Error calculating responsibilities score: {str(e)}")
            return 50.0  # Default score
    
    def _extract_skills(self, cv_analysis: CVAnalysis) -> List[str]:
        """Extract skills from CV analysis."""
        skills = []
        
        # Get skills from main skills field
        if cv_analysis.skills:
            skills.extend(self._parse_skills_text(cv_analysis.skills))
        
        # Get skills from structured data
        if hasattr(cv_analysis, 'skills_details') and cv_analysis.skills_details:
            if isinstance(cv_analysis.skills_details, dict):
                extracted_skills = cv_analysis.skills_details.get('skills', [])
                if isinstance(extracted_skills, list):
                    skills.extend(extracted_skills)
        
        return [skill.lower().strip() for skill in skills if skill.strip()]
    
    def _extract_responsibilities(self, cv_analysis: CVAnalysis) -> List[str]:
        """Extract responsibilities from CV analysis."""
        responsibilities = []
        
        # Get responsibilities from experience data
        if hasattr(cv_analysis, 'experience_data') and cv_analysis.experience_data:
            if isinstance(cv_analysis.experience_data, dict):
                positions = cv_analysis.experience_data.get('positions', [])
                for position in positions:
                    resp_list = position.get('responsibilities', [])
                    if isinstance(resp_list, list):
                        responsibilities.extend(resp_list)
                    elif isinstance(resp_list, str):
                        responsibilities.append(resp_list)
        
        return [resp.lower().strip() for resp in responsibilities if resp.strip()]
    
    def _parse_skills_text(self, skills_text: str) -> List[str]:
        """Parse skills from text format."""
        if not skills_text:
            return []
        
        # Split by common separators
        separators = [',', ';', '|', '\n', '•', '-']
        skills = [skills_text]
        
        for separator in separators:
            new_skills = []
            for skill in skills:
                new_skills.extend(skill.split(separator))
            skills = new_skills
        
        return [skill.strip() for skill in skills if skill.strip()]
    
    def _skill_matches(self, required_skill: str, candidate_skills: List[str], threshold: float) -> bool:
        """Check if a required skill matches candidate skills."""
        required_skill_lower = required_skill.lower()
        
        for candidate_skill in candidate_skills:
            # Exact match
            if required_skill_lower == candidate_skill:
                return True
            
            # Substring match
            if required_skill_lower in candidate_skill or candidate_skill in required_skill_lower:
                return True
            
            # Similarity match
            similarity = SequenceMatcher(None, required_skill_lower, candidate_skill).ratio()
            if similarity >= threshold:
                return True
        
        return False
    
    def _responsibility_matches(self, key_resp: str, candidate_responsibilities: List[str], threshold: float) -> bool:
        """Check if a key responsibility matches candidate responsibilities."""
        key_resp_lower = key_resp.lower()
        
        for candidate_resp in candidate_responsibilities:
            # Substring match
            if key_resp_lower in candidate_resp or candidate_resp in key_resp_lower:
                return True
            
            # Similarity match
            similarity = SequenceMatcher(None, key_resp_lower, candidate_resp).ratio()
            if similarity >= threshold:
                return True
        
        return False
    
    def _get_education_level_score(self, degree: str) -> int:
        """Get numeric score for education level for comparison."""
        degree_lower = degree.lower()
        
        if any(keyword in degree_lower for keyword in ['phd', 'doctorate', 'doctoral']):
            return 100
        elif any(keyword in degree_lower for keyword in ['master', 'ma', 'ms', 'msc', 'mba']):
            return 90
        elif any(keyword in degree_lower for keyword in ['bachelor', 'ba', 'bs', 'bsc']):
            return 70
        elif any(keyword in degree_lower for keyword in ['associate', 'diploma']):
            return 40
        elif any(keyword in degree_lower for keyword in ['high school', 'secondary']):
            return 20
        else:
            return 30
    
    def _check_minimum_requirements(self, education_score: float, experience_score: float, 
                                   skills_score: float, final_score: float, 
                                   scoring_rules: VacancyScoringRules) -> bool:
        """Check if candidate meets minimum requirements."""
        if education_score < scoring_rules.minimum_education_score:
            return False
        if experience_score < scoring_rules.minimum_experience_score:
            return False
        if skills_score < scoring_rules.minimum_skills_score:
            return False
        if final_score < scoring_rules.minimum_overall_score:
            return False
        
        return True
    
    def _generate_recommendation(self, final_score: float, meets_requirements: bool) -> str:
        """Generate recommendation based on score and requirements."""
        if not meets_requirements:
            return 'not_recommended'
        
        if final_score >= 85:
            return 'highly_recommended'
        elif final_score >= 70:
            return 'recommended'
        else:
            return 'consider'
    
    def _create_score_breakdown(self, education_score: float, experience_score: float,
                               skills_score: float, responsibilities_score: float,
                               weighted_education: float, weighted_experience: float,
                               weighted_skills: float, weighted_responsibilities: float,
                               scoring_rules: VacancyScoringRules, cv_analysis: CVAnalysis,
                               vacancy: Vacancy) -> Dict[str, Any]:
        """Create detailed score breakdown."""
        return {
            'component_scores': {
                'education': {
                    'raw_score': education_score,
                    'weight': scoring_rules.education_weight,
                    'weighted_score': weighted_education
                },
                'experience': {
                    'raw_score': experience_score,
                    'weight': scoring_rules.experience_weight,
                    'weighted_score': weighted_experience
                },
                'skills': {
                    'raw_score': skills_score,
                    'weight': scoring_rules.skills_weight,
                    'weighted_score': weighted_skills
                },
                'responsibilities': {
                    'raw_score': responsibilities_score,
                    'weight': scoring_rules.responsibilities_weight,
                    'weighted_score': weighted_responsibilities
                }
            },
            'minimum_requirements': {
                'education': scoring_rules.minimum_education_score,
                'experience': scoring_rules.minimum_experience_score,
                'skills': scoring_rules.minimum_skills_score,
                'overall': scoring_rules.minimum_overall_score
            },
            'candidate_data': {
                'education_level': cv_analysis.education_level,
                'years_of_experience': cv_analysis.years_of_experience,
                'skills_count': len(self._extract_skills(cv_analysis)),
                'responsibilities_count': len(self._extract_responsibilities(cv_analysis))
            },
            'vacancy_info': {
                'title': vacancy.title,
                'category': vacancy.category
            }
        }
    
    def _get_default_education_requirements(self) -> Dict[str, Any]:
        """Get default education requirements."""
        return {
            'high_school': {'score': 20, 'keywords': ['high school', 'secondary']},
            'associate': {'score': 40, 'keywords': ['associate', 'diploma']},
            'bachelor': {'score': 70, 'keywords': ['bachelor', 'ba', 'bs', 'bsc']},
            'master': {'score': 90, 'keywords': ['master', 'ma', 'ms', 'msc', 'mba']},
            'phd': {'score': 100, 'keywords': ['phd', 'doctorate', 'doctoral']},
            'default_score': 30
        }
    
    def _get_default_experience_requirements(self) -> Dict[str, Any]:
        """Get default experience requirements."""
        return {
            'ranges': [
                {'min': 0, 'max': 1, 'score': 20},
                {'min': 1, 'max': 3, 'score': 50},
                {'min': 3, 'max': 5, 'score': 75},
                {'min': 5, 'max': 10, 'score': 90},
                {'min': 10, 'max': 999, 'score': 100}
            ],
            'default_score': 30
        }
    
    def _get_default_skills_requirements(self) -> Dict[str, Any]:
        """Get default skills requirements."""
        return {
            'required_skills': [],
            'preferred_skills': [],
            'skill_matching_threshold': 0.7,
            'default_score': 50
        }
    
    def _get_default_responsibilities_requirements(self) -> Dict[str, Any]:
        """Get default responsibilities requirements."""
        return {
            'key_responsibilities': [],
            'responsibility_matching_threshold': 0.6,
            'default_score': 50
        }


def calculate_custom_scores_for_vacancy(vacancy: Vacancy) -> List[CustomScoringResult]:
    """
    Calculate custom scores for all CV analyses against a specific vacancy.
    
    Args:
        vacancy: The vacancy to calculate scores for
        
    Returns:
        List of CustomScoringResult objects
    """
    engine = CustomScoringEngine()
    results = []
    
    # Get all CV analyses
    cv_analyses = CVAnalysis.objects.select_related('cv').all()
    
    with transaction.atomic():
        for cv_analysis in cv_analyses:
            try:
                result = engine.calculate_custom_score(cv_analysis, vacancy)
                results.append(result)
            except Exception as e:
                logger.error(f"Error calculating custom score for CV {cv_analysis.id}: {str(e)}")
                continue
    
    return results


def recalculate_all_custom_scores() -> List[CustomScoringResult]:
    """
    Recalculate all custom scores for all CV-vacancy combinations.
    
    Returns:
        List of CustomScoringResult objects
    """
    engine = CustomScoringEngine()
    results = []
    
    # Get all vacancies with scoring rules
    vacancies = Vacancy.objects.filter(scoring_rules__isnull=False)
    cv_analyses = CVAnalysis.objects.select_related('cv').all()
    
    with transaction.atomic():
        for vacancy in vacancies:
            for cv_analysis in cv_analyses:
                try:
                    result = engine.calculate_custom_score(cv_analysis, vacancy)
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error recalculating custom score for CV {cv_analysis.id} vs Vacancy {vacancy.id}: {str(e)}")
                    continue
    
    return results


def bulk_calculate_custom_scores(cv_analyses: List[CVAnalysis], vacancies: List[Vacancy]) -> List[CustomScoringResult]:
    """
    Calculate custom scores for multiple CV analyses against multiple vacancies.
    
    Args:
        cv_analyses: List of CV analyses
        vacancies: List of vacancies
        
    Returns:
        List of CustomScoringResult objects
    """
    engine = CustomScoringEngine()
    results = []
    
    with transaction.atomic():
        for cv_analysis in cv_analyses:
            for vacancy in vacancies:
                try:
                    result = engine.calculate_custom_score(cv_analysis, vacancy)
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error calculating custom score for CV {cv_analysis.id} vs Vacancy {vacancy.id}: {str(e)}")
                    continue
    
    return results 