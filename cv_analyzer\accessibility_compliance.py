"""
Accessibility Compliance System (WCAG 2.1)
Automated testing, fixes, and compliance monitoring
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from django.http import JsonResponse
from django.template.loader import render_to_string
from django.conf import settings

logger = logging.getLogger(__name__)

class AccessibilityAuditor:
    """WCAG 2.1 compliance auditor and automated fixes"""
    
    def __init__(self):
        self.wcag_levels = ['A', 'AA', 'AAA']
        self.current_target = 'AA'  # Target WCAG 2.1 AA compliance
        
        self.accessibility_rules = {
            'images': {
                'alt_text_required': True,
                'decorative_alt_empty': True,
                'complex_images_longdesc': True
            },
            'headings': {
                'hierarchical_structure': True,
                'unique_page_title': True,
                'descriptive_headings': True
            },
            'forms': {
                'labels_required': True,
                'error_identification': True,
                'instructions_clear': True
            },
            'navigation': {
                'skip_links': True,
                'keyboard_accessible': True,
                'focus_indicators': True
            },
            'color_contrast': {
                'text_background_ratio': 4.5,  # WCAG AA standard
                'large_text_ratio': 3.0
            }
        }
    
    def audit_page_accessibility(self, html_content: str) -> Dict[str, Any]:
        """Perform comprehensive accessibility audit"""
        audit_results = {
            'compliance_level': None,
            'issues': [],
            'warnings': [],
            'passed_checks': [],
            'score': 0,
            'recommendations': []
        }
        
        # Parse HTML and run checks
        issues = []
        warnings = []
        passed = []
        
        # Image accessibility checks
        img_results = self._check_image_accessibility(html_content)
        issues.extend(img_results['issues'])
        warnings.extend(img_results['warnings'])
        passed.extend(img_results['passed'])
        
        # Heading structure checks
        heading_results = self._check_heading_structure(html_content)
        issues.extend(heading_results['issues'])
        warnings.extend(heading_results['warnings'])
        passed.extend(heading_results['passed'])
        
        # Form accessibility checks
        form_results = self._check_form_accessibility(html_content)
        issues.extend(form_results['issues'])
        warnings.extend(form_results['warnings'])
        passed.extend(form_results['passed'])
        
        # Navigation accessibility checks
        nav_results = self._check_navigation_accessibility(html_content)
        issues.extend(nav_results['issues'])
        warnings.extend(nav_results['warnings'])
        passed.extend(nav_results['passed'])
        
        # Color contrast checks (simulated)
        contrast_results = self._check_color_contrast(html_content)
        issues.extend(contrast_results['issues'])
        warnings.extend(contrast_results['warnings'])
        passed.extend(contrast_results['passed'])
        
        # Calculate compliance
        total_checks = len(issues) + len(warnings) + len(passed)
        if total_checks > 0:
            score = (len(passed) / total_checks) * 100
        else:
            score = 100
        
        # Determine compliance level
        compliance_level = self._determine_compliance_level(score, issues)
        
        audit_results.update({
            'compliance_level': compliance_level,
            'issues': issues,
            'warnings': warnings,
            'passed_checks': passed,
            'score': round(score, 1),
            'recommendations': self._generate_recommendations(issues, warnings)
        })
        
        return audit_results
    
    def _check_image_accessibility(self, html_content: str) -> Dict[str, List]:
        """Check image accessibility compliance"""
        results = {'issues': [], 'warnings': [], 'passed': []}
        
        # Simulate image checks (in real implementation, parse HTML)
        # Check for missing alt text
        if '<img' in html_content and 'alt=' not in html_content:
            results['issues'].append({
                'rule': 'WCAG 1.1.1',
                'level': 'A',
                'message': 'Images must have alternative text',
                'element': 'img',
                'fix': 'Add alt attribute to all img elements'
            })
        else:
            results['passed'].append({
                'rule': 'WCAG 1.1.1',
                'message': 'Images have alternative text'
            })
        
        # Check for decorative images with empty alt
        if 'class="decorative"' in html_content and 'alt=""' not in html_content:
            results['warnings'].append({
                'rule': 'WCAG 1.1.1',
                'level': 'A',
                'message': 'Decorative images should have empty alt attribute',
                'element': 'img.decorative',
                'fix': 'Use alt="" for decorative images'
            })
        
        return results
    
    def _check_heading_structure(self, html_content: str) -> Dict[str, List]:
        """Check heading structure compliance"""
        results = {'issues': [], 'warnings': [], 'passed': []}
        
        # Check for page title
        if '<title>' not in html_content:
            results['issues'].append({
                'rule': 'WCAG 2.4.2',
                'level': 'A',
                'message': 'Page must have a unique title',
                'element': 'title',
                'fix': 'Add descriptive title element to page head'
            })
        else:
            results['passed'].append({
                'rule': 'WCAG 2.4.2',
                'message': 'Page has title element'
            })
        
        # Check heading hierarchy (simplified)
        has_h1 = '<h1' in html_content
        if not has_h1:
            results['issues'].append({
                'rule': 'WCAG 1.3.1',
                'level': 'A',
                'message': 'Page should have exactly one h1 element',
                'element': 'h1',
                'fix': 'Add main h1 heading to page'
            })
        else:
            results['passed'].append({
                'rule': 'WCAG 1.3.1',
                'message': 'Page has h1 heading'
            })
        
        return results
    
    def _check_form_accessibility(self, html_content: str) -> Dict[str, List]:
        """Check form accessibility compliance"""
        results = {'issues': [], 'warnings': [], 'passed': []}
        
        # Check for form labels
        if '<input' in html_content:
            if '<label' not in html_content and 'aria-label' not in html_content:
                results['issues'].append({
                    'rule': 'WCAG 3.3.2',
                    'level': 'A',
                    'message': 'Form inputs must have labels',
                    'element': 'input',
                    'fix': 'Add label elements or aria-label attributes'
                })
            else:
                results['passed'].append({
                    'rule': 'WCAG 3.3.2',
                    'message': 'Form inputs have labels'
                })
        
        # Check for error identification
        if 'class="error"' in html_content:
            if 'aria-describedby' not in html_content:
                results['warnings'].append({
                    'rule': 'WCAG 3.3.1',
                    'level': 'A',
                    'message': 'Error messages should be programmatically associated',
                    'element': '.error',
                    'fix': 'Use aria-describedby to link errors to inputs'
                })
        
        return results
    
    def _check_navigation_accessibility(self, html_content: str) -> Dict[str, List]:
        """Check navigation accessibility compliance"""
        results = {'issues': [], 'warnings': [], 'passed': []}
        
        # Check for skip links
        if 'skip-link' not in html_content and 'skip-to-content' not in html_content:
            results['issues'].append({
                'rule': 'WCAG 2.4.1',
                'level': 'A',
                'message': 'Page should have skip links for keyboard navigation',
                'element': 'nav',
                'fix': 'Add skip link to main content'
            })
        else:
            results['passed'].append({
                'rule': 'WCAG 2.4.1',
                'message': 'Page has skip links'
            })
        
        # Check for keyboard accessibility indicators
        if 'tabindex' in html_content or 'role=' in html_content:
            results['passed'].append({
                'rule': 'WCAG 2.1.1',
                'message': 'Elements support keyboard interaction'
            })
        
        return results
    
    def _check_color_contrast(self, html_content: str) -> Dict[str, List]:
        """Check color contrast compliance (simplified)"""
        results = {'issues': [], 'warnings': [], 'passed': []}
        
        # In real implementation, this would analyze CSS colors
        # For now, assume good contrast if proper CSS classes are used
        if 'text-muted' in html_content:
            results['warnings'].append({
                'rule': 'WCAG 1.4.3',
                'level': 'AA',
                'message': 'Muted text may not meet contrast requirements',
                'element': '.text-muted',
                'fix': 'Ensure 4.5:1 contrast ratio for normal text'
            })
        
        results['passed'].append({
            'rule': 'WCAG 1.4.3',
            'message': 'Main text appears to have sufficient contrast'
        })
        
        return results
    
    def _determine_compliance_level(self, score: float, issues: List) -> str:
        """Determine WCAG compliance level based on audit results"""
        level_a_issues = [issue for issue in issues if issue.get('level') == 'A']
        level_aa_issues = [issue for issue in issues if issue.get('level') == 'AA']
        level_aaa_issues = [issue for issue in issues if issue.get('level') == 'AAA']
        
        if level_a_issues:
            return 'Non-compliant'
        elif level_aa_issues:
            return 'WCAG 2.1 A'
        elif level_aaa_issues:
            return 'WCAG 2.1 AA'
        else:
            return 'WCAG 2.1 AAA'
    
    def _generate_recommendations(self, issues: List, warnings: List) -> List[str]:
        """Generate accessibility improvement recommendations"""
        recommendations = []
        
        # Priority fixes for critical issues
        critical_rules = ['WCAG 1.1.1', 'WCAG 2.4.2', 'WCAG 3.3.2']
        for issue in issues:
            if issue.get('rule') in critical_rules:
                recommendations.append(f"HIGH PRIORITY: {issue.get('fix')}")
        
        # Standard recommendations
        for issue in issues:
            if issue.get('rule') not in critical_rules:
                recommendations.append(f"Fix: {issue.get('fix')}")
        
        # Warning-based recommendations
        for warning in warnings:
            recommendations.append(f"Improve: {warning.get('fix')}")
        
        return recommendations

class AccessibilityEnhancer:
    """Automatic accessibility enhancements"""
    
    def generate_accessibility_css(self):
        """Generate CSS for accessibility enhancements"""
        return """
/* Accessibility Enhancement CSS */

/* Skip Links */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    font-weight: bold;
}

.skip-link:focus {
    top: 6px;
}

/* Focus Management */
*:focus {
    outline: 2px solid #005fcc;
    outline-offset: 2px;
}

/* Keyboard Navigation Enhancement */
.keyboard-navigation *:focus {
    outline: 3px solid #005fcc;
    outline-offset: 2px;
    box-shadow: 0 0 0 5px rgba(0, 95, 204, 0.3);
}

/* Remove focus outline for mouse users */
.mouse-navigation *:focus {
    outline: none;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid currentColor;
    }
    
    .card {
        border: 2px solid currentColor;
    }
    
    .form-control {
        border: 2px solid currentColor;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --border-color: #404040;
    }
    
    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .card {
        background-color: #2d2d2d;
        border-color: var(--border-color);
    }
}

/* Screen Reader Only Content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    overflow: visible;
    clip: auto;
    white-space: normal;
}

/* Font Size and Zoom Support */
html {
    font-size: 16px; /* Base font size */
}

@media (min-width: 768px) {
    html {
        font-size: 18px;
    }
}

/* Ensure text scales properly */
body {
    font-size: 1rem;
    line-height: 1.5;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

/* Ensure interactive elements are large enough */
button,
input,
select,
textarea,
.btn,
.link,
[role="button"] {
    min-height: 44px;
    min-width: 44px;
}

/* Error and Success States */
.error {
    border-color: #dc3545;
    background-color: #f8d7da;
}

.error::after {
    content: " (Error)";
    speak: literal-punctuation;
}

.success {
    border-color: #28a745;
    background-color: #d4edda;
}

/* Loading States for Screen Readers */
.loading::before {
    content: "Loading, please wait...";
    speak: always;
    position: absolute;
    left: -10000px;
}

/* Form Improvements */
label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
}

.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* Table Accessibility */
table {
    width: 100%;
    border-collapse: collapse;
}

th {
    text-align: left;
    font-weight: 600;
    background-color: #f8f9fa;
}

th,
td {
    padding: 0.75rem;
    border: 1px solid #dee2e6;
}

/* Navigation Improvements */
nav ul {
    list-style: none;
    padding: 0;
}

nav a {
    display: block;
    padding: 0.5rem 1rem;
    text-decoration: none;
}

nav a:hover,
nav a:focus {
    background-color: #e9ecef;
    text-decoration: underline;
}

/* Modal Accessibility */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1050;
}

.modal-content {
    background-color: white;
    padding: 2rem;
    border-radius: 0.5rem;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    a::after {
        content: " (" attr(href) ")";
    }
    
    .page-break {
        page-break-before: always;
    }
}
"""
    
    def generate_accessibility_javascript(self):
        """Generate JavaScript for accessibility enhancements"""
        return """
// Accessibility Enhancement JavaScript
class AccessibilityEnhancer {
    constructor() {
        this.isKeyboardUser = false;
        this.init();
    }
    
    init() {
        this.setupFocusManagement();
        this.setupKeyboardNavigation();
        this.setupSkipLinks();
        this.setupAriaEnhancements();
        this.setupFormEnhancements();
        this.setupAnnouncementRegion();
    }
    
    setupFocusManagement() {
        // Track keyboard vs mouse usage
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                this.isKeyboardUser = true;
                document.body.classList.add('keyboard-navigation');
                document.body.classList.remove('mouse-navigation');
            }
        });
        
        document.addEventListener('mousedown', () => {
            this.isKeyboardUser = false;
            document.body.classList.add('mouse-navigation');
            document.body.classList.remove('keyboard-navigation');
        });
        
        // Focus trap for modals
        document.querySelectorAll('.modal').forEach(modal => {
            this.setupFocusTrap(modal);
        });
    }
    
    setupFocusTrap(modal) {
        const focusableElements = modal.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        if (focusableElements.length === 0) return;
        
        const firstFocusable = focusableElements[0];
        const lastFocusable = focusableElements[focusableElements.length - 1];
        
        modal.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                if (e.shiftKey) {
                    if (document.activeElement === firstFocusable) {
                        e.preventDefault();
                        lastFocusable.focus();
                    }
                } else {
                    if (document.activeElement === lastFocusable) {
                        e.preventDefault();
                        firstFocusable.focus();
                    }
                }
            }
            
            if (e.key === 'Escape') {
                this.closeModal(modal);
            }
        });
    }
    
    setupKeyboardNavigation() {
        // Arrow key navigation for grids and lists
        document.querySelectorAll('[role="grid"], [role="listbox"]').forEach(container => {
            this.setupArrowKeyNavigation(container);
        });
        
        // Enter/Space activation for custom buttons
        document.querySelectorAll('[role="button"]').forEach(button => {
            button.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    button.click();
                }
            });
        });
    }
    
    setupArrowKeyNavigation(container) {
        const items = container.querySelectorAll('[role="gridcell"], [role="option"]');
        let currentIndex = 0;
        
        container.addEventListener('keydown', (e) => {
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    currentIndex = Math.min(currentIndex + 1, items.length - 1);
                    items[currentIndex].focus();
                    break;
                    
                case 'ArrowUp':
                    e.preventDefault();
                    currentIndex = Math.max(currentIndex - 1, 0);
                    items[currentIndex].focus();
                    break;
                    
                case 'Home':
                    e.preventDefault();
                    currentIndex = 0;
                    items[currentIndex].focus();
                    break;
                    
                case 'End':
                    e.preventDefault();
                    currentIndex = items.length - 1;
                    items[currentIndex].focus();
                    break;
            }
        });
    }
    
    setupSkipLinks() {
        // Add skip link if not present
        if (!document.querySelector('.skip-link')) {
            const skipLink = document.createElement('a');
            skipLink.href = '#main-content';
            skipLink.className = 'skip-link';
            skipLink.textContent = 'Skip to main content';
            document.body.insertBefore(skipLink, document.body.firstChild);
        }
        
        // Ensure main content has proper ID
        const mainContent = document.querySelector('main') || document.querySelector('#main-content');
        if (mainContent && !mainContent.id) {
            mainContent.id = 'main-content';
        }
    }
    
    setupAriaEnhancements() {
        // Add ARIA labels to unlabeled form controls
        document.querySelectorAll('input, select, textarea').forEach(control => {
            if (!control.getAttribute('aria-label') && !control.getAttribute('aria-labelledby')) {
                const label = control.previousElementSibling;
                if (label && label.tagName === 'LABEL') {
                    const labelId = 'label-' + Date.now() + Math.random();
                    label.id = labelId;
                    control.setAttribute('aria-labelledby', labelId);
                }
            }
        });
        
        // Add ARIA live regions for dynamic content
        if (!document.querySelector('[aria-live]')) {
            const liveRegion = document.createElement('div');
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.className = 'sr-only';
            liveRegion.id = 'aria-live-region';
            document.body.appendChild(liveRegion);
        }
        
        // Add ARIA expanded to collapsible elements
        document.querySelectorAll('[data-toggle="collapse"]').forEach(trigger => {
            if (!trigger.getAttribute('aria-expanded')) {
                trigger.setAttribute('aria-expanded', 'false');
            }
        });
    }
    
    setupFormEnhancements() {
        // Add required indicators
        document.querySelectorAll('input[required], select[required], textarea[required]').forEach(field => {
            const label = document.querySelector(`label[for="${field.id}"]`) || 
                         field.previousElementSibling;
            
            if (label && !label.classList.contains('required')) {
                label.classList.add('required');
            }
        });
        
        // Enhance error messaging
        document.querySelectorAll('.error-message').forEach(error => {
            if (!error.id) {
                error.id = 'error-' + Date.now() + Math.random();
            }
            
            const associatedField = error.previousElementSibling || 
                                  document.querySelector(`[data-error="${error.id}"]`);
            
            if (associatedField) {
                associatedField.setAttribute('aria-describedby', error.id);
                associatedField.setAttribute('aria-invalid', 'true');
            }
        });
    }
    
    setupAnnouncementRegion() {
        this.announceRegion = document.getElementById('aria-live-region');
    }
    
    announce(message, priority = 'polite') {
        if (this.announceRegion) {
            this.announceRegion.setAttribute('aria-live', priority);
            this.announceRegion.textContent = message;
            
            // Clear after announcement
            setTimeout(() => {
                this.announceRegion.textContent = '';
            }, 1000);
        }
    }
    
    closeModal(modal) {
        modal.style.display = 'none';
        modal.setAttribute('aria-hidden', 'true');
        
        // Return focus to trigger element
        const trigger = document.querySelector(`[data-target="#${modal.id}"]`);
        if (trigger) {
            trigger.focus();
        }
    }
    
    // Color contrast checker (basic implementation)
    checkContrast(foreground, background) {
        const getLuminance = (color) => {
            const rgb = color.match(/\\d+/g);
            const [r, g, b] = rgb.map(c => {
                c = c / 255;
                return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
            });
            return 0.2126 * r + 0.7152 * g + 0.0722 * b;
        };
        
        const l1 = getLuminance(foreground);
        const l2 = getLuminance(background);
        const lighter = Math.max(l1, l2);
        const darker = Math.min(l1, l2);
        
        return (lighter + 0.05) / (darker + 0.05);
    }
    
    // Auto-generate alt text for images (placeholder)
    generateAltText(img) {
        if (!img.alt && img.src) {
            // In real implementation, this could use AI/ML to generate alt text
            const filename = img.src.split('/').pop().split('.')[0];
            img.alt = `Image: ${filename.replace(/[_-]/g, ' ')}`;
        }
    }
}

// Initialize accessibility enhancer
document.addEventListener('DOMContentLoaded', () => {
    window.accessibilityEnhancer = new AccessibilityEnhancer();
});

// Utility functions for developers
window.a11y = {
    announce: (message, priority = 'polite') => {
        window.accessibilityEnhancer?.announce(message, priority);
    },
    
    checkContrast: (fg, bg) => {
        return window.accessibilityEnhancer?.checkContrast(fg, bg);
    },
    
    validateForm: (form) => {
        // Run accessibility validation on form
        const issues = [];
        
        form.querySelectorAll('input, select, textarea').forEach(field => {
            if (!field.labels.length && !field.getAttribute('aria-label')) {
                issues.push(`Field ${field.name || field.id} missing label`);
            }
        });
        
        return issues;
    }
};
"""

# Global instances
accessibility_auditor = AccessibilityAuditor()
accessibility_enhancer = AccessibilityEnhancer()

def audit_page_accessibility(html_content: str):
    """Audit page for accessibility compliance"""
    return accessibility_auditor.audit_page_accessibility(html_content)

def get_accessibility_css():
    """Get accessibility enhancement CSS"""
    return accessibility_enhancer.generate_accessibility_css()

def get_accessibility_javascript():
    """Get accessibility enhancement JavaScript"""
    return accessibility_enhancer.generate_accessibility_javascript() 