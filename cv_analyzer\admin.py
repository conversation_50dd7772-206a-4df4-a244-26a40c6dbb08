from django import forms
from django.contrib import admin
from django.urls import path
from django.http import JsonResponse
from django.utils.html import format_html
from .models import (
    CV, CVAnalysis, Company, CompanyAnalysis, 
    Vacancy, VacancyAnalysis, ApplicantProfile, 
    WelcomeContent, AnalysisProcess, AIAPIConfig, AIPromptConfig, 
    UnifiedAnalysis, AIConfig, ComparisonAnalysis, AIUsageLog,
    VacancyScoringRules, ScoringTemplate, CustomScoringResult
)

@admin.register(AIAPIConfig)
class AIAPIConfigAdmin(admin.ModelAdmin):
    """Admin interface for AI API configurations."""
    
    list_display = (
        'provider',
        'model_name',
        'is_active',
        'priority',
        'connection_status',
        'created_at',
        'updated_at',
    )
    list_filter = ('provider', 'is_active')
    search_fields = ('provider', 'model_name')
    ordering = ('priority',)
    readonly_fields = ('created_at', 'updated_at')
    
    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('fetch-models/<int:config_id>/', self.admin_site.admin_view(self.fetch_models_view), name='fetch_models'),
            path('test-connection/<int:config_id>/', self.admin_site.admin_view(self.test_connection_view), name='test_connection'),
        ]
        return custom_urls + urls
    
    def fetch_models_view(self, request, config_id):
        """Fetch available models for a given AI configuration."""
        try:
            config = AIAPIConfig.objects.get(id=config_id)
            models = config.fetch_available_models()
            return JsonResponse({'success': True, 'models': models})
        except AIAPIConfig.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Configuration not found'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    
    def test_connection_view(self, request, config_id):
        """Test connection to AI provider."""
        try:
            config = AIAPIConfig.objects.get(id=config_id)
            models = config.fetch_available_models()
            
            if any('Error' in model for model in models):
                return JsonResponse({'success': False, 'error': models[0] if models else 'Unknown error'})
            else:
                return JsonResponse({'success': True, 'message': f'Connected successfully. Found {len(models)} models.'})
        except AIAPIConfig.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Configuration not found'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    
    def connection_status(self, obj):
        """Display connection status with test button."""
        if obj.pk:
            return format_html(
                '<button type="button" onclick="testConnection({})" class="button">Test Connection</button>',
                obj.pk
            )
        return "Save first"
    connection_status.short_description = "Connection"
    
    fieldsets = (
        ('Provider Configuration', {
            'fields': (
                'provider',
                'api_key',
                'model_name',
                'is_active',
                'priority',
            ),
            'description': """
            <strong>Configuration Guide:</strong><br>
            • <strong>OpenAI:</strong> Enter your OpenAI API key<br>
            • <strong>Groq:</strong> Enter your Groq API key<br>
            • <strong>Ollama:</strong> Enter server URL (e.g., http://*************:11434) or IP only (*************), or leave blank for default server
            """
        }),
        ('Model Settings', {
            'fields': (
                'max_tokens',
                'temperature',
            )
        }),
        ('Timestamps', {
            'fields': (
                'created_at',
                'updated_at',
            )
        }),
    )
    
    class Media:
        js = ('admin/js/ai_config_admin.js',)
        css = {
            'all': ('admin/css/ai_config_admin.css',)
        }

@admin.register(AIConfig)
class AIConfigAdmin(admin.ModelAdmin):
    """Admin interface for general AI configurations."""
    
    list_display = (
        'cache_ttl_display',
        'batch_size',
        'max_retries',
        'retry_delay',
        'updated_at',
    )
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Cache Settings', {
            'fields': ('cache_ttl',)
        }),
        ('Batch Processing', {
            'fields': ('batch_size',)
        }),
        ('Retry Settings', {
            'fields': (
                'max_retries',
                'retry_delay',
            )
        }),
        ('Timestamps', {
            'fields': (
                'created_at',
                'updated_at',
            )
        }),
    )

    def cache_ttl_display(self, obj):
        """Display cache TTL in a human-readable format."""
        hours = obj.cache_ttl // 3600
        minutes = (obj.cache_ttl % 3600) // 60
        return f"{hours}h {minutes}m"
    cache_ttl_display.short_description = "Cache TTL"

@admin.register(AIUsageLog)
class AIUsageLogAdmin(admin.ModelAdmin):
    """Admin interface for AI usage logs."""
    
    list_display = (
        'provider',
        'operation',
        'tokens_used',
        'response_time_display',
        'status',
        'user',
        'created_at',
    )
    list_filter = (
        'provider',
        'status',
        'created_at',
        'user',
    )
    search_fields = (
        'provider',
        'operation',
        'error_message',
        'user__username',
    )
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)
    
    def response_time_display(self, obj):
        """Display response time in a human-readable format."""
        if obj.response_time < 1:
            return f"{int(obj.response_time * 1000)}ms"
        return f"{obj.response_time:.2f}s"
    response_time_display.short_description = "Response Time"

    def has_add_permission(self, request):
        """Disable manual creation of usage logs."""
        return False

    def has_change_permission(self, request, obj=None):
        """Disable editing of usage logs."""
        return False

@admin.register(AIPromptConfig)
class AIPromptConfigAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name', 'prompt', 'instructions')

@admin.register(UnifiedAnalysis)
class UnifiedAnalysisAdmin(admin.ModelAdmin):
    list_display = ('applicant', 'vacancy', 'company', 'compatibility', 'created_at')
    list_filter = ('vacancy', 'company', 'created_at')
    search_fields = ('applicant__username', 'vacancy__title', 'company__name')

@admin.register(ComparisonAnalysis)
class ComparisonAnalysisAdmin(admin.ModelAdmin):
    list_display = ('vacancy', 'cv', 'compatibility_score', 'ai_config', 'created_at')
    list_filter = ('ai_config', 'created_at')
    search_fields = ('vacancy__title', 'cv__applicant_profile__user__username', 'analysis_text')

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ('name', 'industry', 'size', 'created_at')
    list_filter = ('industry', 'size')
    search_fields = ('name', 'description', 'industry')

@admin.register(Vacancy)
class VacancyAdmin(admin.ModelAdmin):
    list_display = ('title', 'company', 'status', 'published', 'category', 'created_at')
    list_filter = ('status', 'published', 'category', 'created_at')
    search_fields = ('title', 'description', 'requirements')
    fieldsets = (
        ('Basic Information', {
            'fields': ('company', 'title', 'category')
        }),
        ('Content', {
            'fields': ('description', 'requirements')
        }),
        ('Status & Visibility', {
            'fields': ('status', 'published'),
            'description': 'Status controls internal workflow, Published controls public visibility'
        }),
        ('Dates', {
            'fields': ('expires_at',),
            'classes': ('collapse',)
        })
    )

@admin.register(CV)
class CVAdmin(admin.ModelAdmin):
    list_display = ('id', 'applicant_profile', 'status', 'source', 'uploaded_at')
    list_filter = ('status', 'source', 'uploaded_at')
    search_fields = ('applicant_profile__user__username', 'skills', 'education_level')

@admin.register(CVAnalysis)
class CVAnalysisAdmin(admin.ModelAdmin):
    list_display = ('cv', 'overall_score', 'content_score', 'skills_score')
    list_filter = ('overall_score', 'content_score', 'skills_score')
    search_fields = ('cv__applicant_profile__user__username', 'skills')

@admin.register(ApplicantProfile)
class ApplicantProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'complete', 'created_at')
    list_filter = ('complete', 'created_at')
    search_fields = ('user__username', 'phone_number')

@admin.register(AnalysisProcess)
class AnalysisProcessAdmin(admin.ModelAdmin):
    list_display = ('vacancy', 'cv', 'status', 'started_at', 'completed_at')
    list_filter = ('status', 'started_at', 'completed_at')
    search_fields = ('vacancy__title', 'cv__applicant_profile__user__username')

@admin.register(WelcomeContent)
class WelcomeContentAdmin(admin.ModelAdmin):
    list_display = ('welcome_message',)
    fieldsets = (
        ('Welcome Section', {
            'fields': ('welcome_message',)
        }),
        ('Step 1', {
            'fields': ('step_1_title', 'step_1_description', 'step_1_image')
        }),
        ('Step 2', {
            'fields': ('step_2_title', 'step_2_description', 'step_2_image')
        }),
        ('Step 3', {
            'fields': ('step_3_title', 'step_3_description', 'step_3_image')
        }),
        ('Step 4', {
            'fields': ('step_4_title', 'step_4_description', 'step_4_image')
        }),
    )

# Custom Scoring System Admin
@admin.register(VacancyScoringRules)
class VacancyScoringRulesAdmin(admin.ModelAdmin):
    list_display = ['vacancy', 'education_weight', 'experience_weight', 'skills_weight', 'responsibilities_weight', 'enable_custom_scoring', 'created_at']
    list_filter = ['enable_custom_scoring', 'created_at']
    search_fields = ['vacancy__title', 'vacancy__company__name']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('Vacancy Information', {
            'fields': ['vacancy']
        }),
        ('Scoring Weights (%)', {
            'fields': ['education_weight', 'experience_weight', 'skills_weight', 'responsibilities_weight'],
            'description': 'Weights must sum to 100%. Education + Experience + Skills + Responsibilities = 100%'
        }),
        ('Requirements Configuration', {
            'fields': ['education_requirements', 'experience_requirements', 'skills_requirements', 'responsibilities_requirements'],
            'classes': ['collapse']
        }),
        ('Minimum Thresholds', {
            'fields': ['minimum_education_score', 'minimum_experience_score', 'minimum_skills_score', 'minimum_overall_score'],
            'classes': ['collapse']
        }),
        ('Settings', {
            'fields': ['enable_custom_scoring']
        }),
        ('Timestamps', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        })
    )
    
    def save_model(self, request, obj, form, change):
        # Validate weights sum to 100%
        total_weight = obj.education_weight + obj.experience_weight + obj.skills_weight + obj.responsibilities_weight
        if abs(total_weight - 100.0) > 0.01:
            from django.contrib import messages
            messages.error(request, f"Weights must sum to 100%. Current total: {total_weight}%")
            return
        super().save_model(request, obj, form, change)

@admin.register(ScoringTemplate)
class ScoringTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'education_weight', 'experience_weight', 'skills_weight', 'responsibilities_weight', 'is_default', 'is_active', 'created_by']
    list_filter = ['category', 'is_default', 'is_active', 'created_at']
    search_fields = ['name', 'category', 'description']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('Template Information', {
            'fields': ['name', 'category', 'description']
        }),
        ('Scoring Weights (%)', {
            'fields': ['education_weight', 'experience_weight', 'skills_weight', 'responsibilities_weight'],
            'description': 'Weights must sum to 100%. Education + Experience + Skills + Responsibilities = 100%'
        }),
        ('Requirements Configuration', {
            'fields': ['education_requirements', 'experience_requirements', 'skills_requirements', 'responsibilities_requirements'],
            'classes': ['collapse']
        }),
        ('Template Settings', {
            'fields': ['is_default', 'is_active']
        }),
        ('Metadata', {
            'fields': ['created_by', 'created_at', 'updated_at'],
            'classes': ['collapse']
        })
    )
    
    def save_model(self, request, obj, form, change):
        if not obj.created_by:
            obj.created_by = request.user
        
        # Validate weights sum to 100%
        total_weight = obj.education_weight + obj.experience_weight + obj.skills_weight + obj.responsibilities_weight
        if abs(total_weight - 100.0) > 0.01:
            from django.contrib import messages
            messages.error(request, f"Weights must sum to 100%. Current total: {total_weight}%")
            return
        
        super().save_model(request, obj, form, change)

@admin.register(CustomScoringResult)
class CustomScoringResultAdmin(admin.ModelAdmin):
    list_display = ['cv_analysis', 'vacancy', 'final_custom_score', 'custom_recommendation', 'meets_minimum_requirements', 'ai_vs_custom_difference', 'created_at']
    list_filter = ['custom_recommendation', 'meets_minimum_requirements', 'created_at', 'vacancy__company']
    search_fields = ['cv_analysis__cv__applicant_profile__user__username', 'vacancy__title', 'vacancy__company__name']
    readonly_fields = ['created_at', 'updated_at', 'ai_vs_custom_difference']
    fieldsets = (
        ('Analysis Information', {
            'fields': ['cv_analysis', 'vacancy']
        }),
        ('Component Scores (0-100%)', {
            'fields': ['education_score', 'experience_score', 'skills_score', 'responsibilities_score']
        }),
        ('Weighted Scores', {
            'fields': ['weighted_education_score', 'weighted_experience_score', 'weighted_skills_score', 'weighted_responsibilities_score'],
            'classes': ['collapse']
        }),
        ('Final Results', {
            'fields': ['final_custom_score', 'custom_recommendation', 'meets_minimum_requirements']
        }),
        ('Comparison with AI', {
            'fields': ['ai_vs_custom_difference'],
            'description': 'Difference between AI score and custom score (positive = custom higher)'
        }),
        ('Score Breakdown', {
            'fields': ['score_breakdown'],
            'classes': ['collapse']
        }),
        ('Timestamps', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        })
    )
    
    def has_add_permission(self, request):
        # Custom scoring results should be generated automatically
        return False