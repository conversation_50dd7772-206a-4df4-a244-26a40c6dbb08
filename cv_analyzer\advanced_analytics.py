"""
Advanced Analytics System with Machine Learning
Provides predictive analytics, data mining, and intelligent insights.
"""

import numpy as np
import pandas as pd
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, Counter
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, mean_squared_error
import pickle
import os

from django.conf import settings
from django.db.models import Count, Avg, Sum, Q, F
from django.utils import timezone
from django.core.cache import cache
from django.contrib.auth.models import User

from .models import (
    CV, CVAnalysis, Company, Vacancy, VacancyAnalysis, ComparisonAnalysis,
    UnifiedAnalysis, AIUsageLog, ApplicantProfile
)

logger = logging.getLogger(__name__)

@dataclass
class MLPrediction:
    """Machine learning prediction result"""
    prediction: Any
    confidence: float
    model_used: str
    features_used: List[str]
    explanation: str

@dataclass
class AnalyticsInsight:
    """Analytics insight data structure"""
    type: str  # 'trend', 'anomaly', 'pattern', 'recommendation'
    title: str
    description: str
    confidence: float
    data_points: Dict[str, Any]
    actionable: bool
    priority: str  # 'high', 'medium', 'low'

class AdvancedAnalyticsEngine:
    """Main advanced analytics engine with ML capabilities"""
    
    def __init__(self):
        self.models_cache = {}
        self.feature_extractors = {}
        self.model_path = os.path.join(settings.BASE_DIR, 'ml_models')
        self._ensure_model_directory()
    
    def _ensure_model_directory(self):
        """Ensure ML models directory exists"""
        if not os.path.exists(self.model_path):
            os.makedirs(self.model_path)
    
    def analyze_cv_patterns(self, date_range: int = 90) -> Dict[str, Any]:
        """
        Analyze CV patterns using machine learning
        
        Args:
            date_range: Number of days to analyze
            
        Returns:
            Dictionary containing pattern analysis results
        """
        try:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=date_range)
            
            # Get CV analysis data
            cv_analyses = CVAnalysis.objects.filter(
                cv__uploaded_at__range=[start_date, end_date]
            ).select_related('cv')
            
            if not cv_analyses.exists():
                return {'error': 'No CV data available for analysis'}
            
            # Prepare data for ML analysis
            analysis_data = self._prepare_cv_data_for_ml(cv_analyses)
            
            results = {
                'skill_clusters': self._analyze_skill_clusters(analysis_data),
                'score_predictions': self._predict_cv_scores(analysis_data),
                'success_patterns': self._identify_success_patterns(analysis_data),
                'improvement_recommendations': self._generate_improvement_recommendations(analysis_data),
                'market_trends': self._analyze_market_trends(analysis_data),
                'outlier_detection': self._detect_cv_outliers(analysis_data)
            }
            
            return results
            
        except Exception as e:
            logger.error(f"Error in CV pattern analysis: {str(e)}")
            return {'error': str(e)}
    
    def _prepare_cv_data_for_ml(self, cv_analyses) -> pd.DataFrame:
        """Prepare CV data for machine learning analysis"""
        data = []
        
        for analysis in cv_analyses:
            try:
                # Extract features from CV analysis
                features = {
                    'overall_score': analysis.overall_score or 0,
                    'content_score': analysis.content_score or 0,
                    'format_score': analysis.format_score or 0,
                    'skills_score': analysis.skills_score or 0,
                    'style_score': analysis.style_score or 0,
                    'years_experience': analysis.years_of_experience or 0,
                    'education_level': self._encode_education_level(analysis.education_level),
                    'skills_count': len(analysis.skills.split(',')) if analysis.skills else 0,
                    'languages_count': len(analysis.languages.split(',')) if analysis.languages else 0,
                    'cv_source': self._encode_cv_source(analysis.cv.source),
                    'upload_day_of_week': analysis.cv.uploaded_at.weekday(),
                    'upload_hour': analysis.cv.uploaded_at.hour,
                    'has_salary_expectation': 1 if analysis.salary_expectation else 0,
                    'preferred_remote': 1 if analysis.preferred_work_location == 'remote' else 0
                }
                
                # Add text features
                text_features = self._extract_text_features(analysis)
                features.update(text_features)
                
                data.append(features)
                
            except Exception as e:
                logger.warning(f"Error processing CV analysis {analysis.id}: {str(e)}")
                continue
        
        return pd.DataFrame(data)
    
    def _encode_education_level(self, education: str) -> int:
        """Encode education level to numeric value"""
        education_mapping = {
            '': 0,
            'high school': 1,
            'associate': 2,
            'bachelor': 3,
            'master': 4,
            'phd': 5,
            'doctorate': 5
        }
        
        education_lower = education.lower() if education else ''
        
        # Find best match
        for key, value in education_mapping.items():
            if key in education_lower:
                return value
        
        return 0
    
    def _encode_cv_source(self, source: str) -> int:
        """Encode CV source to numeric value"""
        source_mapping = {
            'local': 1,
            'email': 2,
            'shared': 3,
            'onedrive': 4,
            'googledrive': 5,
            'whatsapp': 6,
            'telegram': 7
        }
        
        return source_mapping.get(source, 0)
    
    def _extract_text_features(self, analysis: CVAnalysis) -> Dict[str, Any]:
        """Extract features from text fields"""
        features = {}
        
        # Skills text analysis
        if analysis.skills:
            skills_text = analysis.skills.lower()
            
            # Technical skills indicators
            tech_keywords = ['python', 'java', 'javascript', 'sql', 'html', 'css', 'react', 'angular', 'node']
            features['tech_skills_count'] = sum(1 for keyword in tech_keywords if keyword in skills_text)
            
            # Soft skills indicators
            soft_keywords = ['communication', 'leadership', 'teamwork', 'problem solving', 'creative']
            features['soft_skills_count'] = sum(1 for keyword in soft_keywords if keyword in skills_text)
            
            # Total unique words in skills
            features['skills_diversity'] = len(set(skills_text.split()))
        else:
            features.update({
                'tech_skills_count': 0,
                'soft_skills_count': 0,
                'skills_diversity': 0
            })
        
        return features
    
    def _analyze_skill_clusters(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze skill clusters using K-means clustering"""
        try:
            if data.empty:
                return {'error': 'No data available for clustering'}
            
            # Prepare features for clustering
            clustering_features = [
                'overall_score', 'content_score', 'format_score', 'skills_score',
                'years_experience', 'tech_skills_count', 'soft_skills_count'
            ]
            
            # Filter available features
            available_features = [f for f in clustering_features if f in data.columns]
            
            if len(available_features) < 3:
                return {'error': 'Insufficient features for clustering'}
            
            # Prepare data
            cluster_data = data[available_features].fillna(0)
            
            # Standardize features
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(cluster_data)
            
            # Determine optimal number of clusters (2-8)
            optimal_k = min(8, max(2, len(data) // 10))
            
            # Perform K-means clustering
            kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
            clusters = kmeans.fit_predict(scaled_data)
            
            # Analyze clusters
            cluster_analysis = {}
            for i in range(optimal_k):
                cluster_mask = clusters == i
                cluster_data_subset = data[cluster_mask]
                
                cluster_analysis[f'cluster_{i}'] = {
                    'size': int(cluster_mask.sum()),
                    'avg_overall_score': float(cluster_data_subset['overall_score'].mean()),
                    'avg_experience': float(cluster_data_subset['years_experience'].mean()),
                    'dominant_characteristics': self._get_cluster_characteristics(cluster_data_subset)
                }
            
            return {
                'clusters': cluster_analysis,
                'total_samples': len(data),
                'features_used': available_features,
                'insights': self._generate_clustering_insights(cluster_analysis)
            }
            
        except Exception as e:
            logger.error(f"Error in skill clustering: {str(e)}")
            return {'error': str(e)}
    
    def _get_cluster_characteristics(self, cluster_data: pd.DataFrame) -> List[str]:
        """Get dominant characteristics of a cluster"""
        characteristics = []
        
        # Check high scores
        if cluster_data['overall_score'].mean() > 80:
            characteristics.append('high_performers')
        elif cluster_data['overall_score'].mean() < 50:
            characteristics.append('needs_improvement')
        
        # Check experience level
        if cluster_data['years_experience'].mean() > 5:
            characteristics.append('experienced')
        elif cluster_data['years_experience'].mean() < 2:
            characteristics.append('entry_level')
        
        # Check technical skills
        if 'tech_skills_count' in cluster_data.columns:
            if cluster_data['tech_skills_count'].mean() > 3:
                characteristics.append('technical')
        
        return characteristics
    
    def _generate_clustering_insights(self, cluster_analysis: Dict[str, Any]) -> List[str]:
        """Generate insights from clustering analysis"""
        insights = []
        
        # Find largest cluster
        largest_cluster = max(cluster_analysis.values(), key=lambda x: x['size'])
        insights.append(f"Most common profile type represents {largest_cluster['size']} candidates")
        
        # Find highest performing cluster
        best_cluster = max(cluster_analysis.values(), key=lambda x: x['avg_overall_score'])
        insights.append(f"Top performers average {best_cluster['avg_overall_score']:.1f} score")
        
        # Experience insights
        exp_clusters = [c for c in cluster_analysis.values() if 'experienced' in c.get('dominant_characteristics', [])]
        if exp_clusters:
            insights.append(f"{len(exp_clusters)} clusters represent experienced professionals")
        
        return insights
    
    def _predict_cv_scores(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Predict CV scores using machine learning"""
        try:
            if len(data) < 10:
                return {'error': 'Insufficient data for score prediction'}
            
            # Prepare features and target
            feature_columns = [
                'content_score', 'format_score', 'skills_score', 'style_score',
                'years_experience', 'education_level', 'skills_count',
                'tech_skills_count', 'soft_skills_count'
            ]
            
            # Filter available features
            available_features = [f for f in feature_columns if f in data.columns]
            
            if len(available_features) < 3:
                return {'error': 'Insufficient features for prediction'}
            
            # Prepare data
            X = data[available_features].fillna(0)
            y = data['overall_score'].fillna(0)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # Train model
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X_train, y_train)
            
            # Make predictions
            predictions = model.predict(X_test)
            mse = mean_squared_error(y_test, predictions)
            
            # Feature importance
            feature_importance = dict(zip(available_features, model.feature_importances_))
            
            # Save model
            model_file = os.path.join(self.model_path, 'cv_score_predictor.pkl')
            with open(model_file, 'wb') as f:
                pickle.dump({'model': model, 'features': available_features}, f)
            
            return {
                'model_performance': {
                    'mse': float(mse),
                    'rmse': float(np.sqrt(mse)),
                    'samples_trained': len(X_train),
                    'samples_tested': len(X_test)
                },
                'feature_importance': {k: float(v) for k, v in feature_importance.items()},
                'top_predictive_features': sorted(
                    feature_importance.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )[:5],
                'model_insights': self._generate_prediction_insights(feature_importance, mse)
            }
            
        except Exception as e:
            logger.error(f"Error in CV score prediction: {str(e)}")
            return {'error': str(e)}
    
    def _generate_prediction_insights(self, feature_importance: Dict[str, float], mse: float) -> List[str]:
        """Generate insights from prediction model"""
        insights = []
        
        # Model accuracy insight
        rmse = np.sqrt(mse)
        if rmse < 10:
            insights.append("Model shows high accuracy in predicting CV scores")
        elif rmse < 20:
            insights.append("Model shows moderate accuracy in predicting CV scores")
        else:
            insights.append("Model accuracy could be improved with more data")
        
        # Most important features
        top_feature = max(feature_importance.items(), key=lambda x: x[1])
        insights.append(f"'{top_feature[0]}' is the most predictive factor for CV scores")
        
        # Feature distribution insights
        high_importance_features = [k for k, v in feature_importance.items() if v > 0.1]
        if len(high_importance_features) > 3:
            insights.append("Multiple factors significantly influence CV scores")
        else:
            insights.append("CV scores are primarily driven by few key factors")
        
        return insights
    
    def _identify_success_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Identify patterns in successful CVs"""
        try:
            if data.empty:
                return {'error': 'No data available for pattern analysis'}
            
            # Define success criteria (top 25% scores)
            success_threshold = data['overall_score'].quantile(0.75)
            successful_cvs = data[data['overall_score'] >= success_threshold]
            
            if len(successful_cvs) < 5:
                return {'error': 'Insufficient successful CVs for pattern analysis'}
            
            # Analyze patterns
            patterns = {
                'common_characteristics': self._find_common_characteristics(successful_cvs, data),
                'skill_patterns': self._analyze_successful_skills(successful_cvs),
                'experience_patterns': self._analyze_experience_patterns(successful_cvs),
                'format_patterns': self._analyze_format_patterns(successful_cvs),
                'timing_patterns': self._analyze_timing_patterns(successful_cvs)
            }
            
            return {
                'success_threshold': float(success_threshold),
                'successful_cvs_count': len(successful_cvs),
                'total_cvs_count': len(data),
                'patterns': patterns,
                'recommendations': self._generate_success_recommendations(patterns)
            }
            
        except Exception as e:
            logger.error(f"Error identifying success patterns: {str(e)}")
            return {'error': str(e)}
    
    def _find_common_characteristics(self, successful_cvs: pd.DataFrame, all_data: pd.DataFrame) -> Dict[str, Any]:
        """Find characteristics common to successful CVs"""
        characteristics = {}
        
        # Numeric characteristics
        numeric_columns = ['years_experience', 'education_level', 'skills_count', 'tech_skills_count']
        
        for column in numeric_columns:
            if column in successful_cvs.columns:
                successful_avg = successful_cvs[column].mean()
                overall_avg = all_data[column].mean()
                
                if successful_avg > overall_avg * 1.2:  # 20% higher
                    characteristics[f'higher_{column}'] = {
                        'successful_avg': float(successful_avg),
                        'overall_avg': float(overall_avg),
                        'improvement_factor': float(successful_avg / overall_avg) if overall_avg > 0 else 0
                    }
        
        return characteristics
    
    def _analyze_successful_skills(self, successful_cvs: pd.DataFrame) -> Dict[str, Any]:
        """Analyze skill patterns in successful CVs"""
        skills_analysis = {}
        
        if 'tech_skills_count' in successful_cvs.columns:
            skills_analysis['avg_tech_skills'] = float(successful_cvs['tech_skills_count'].mean())
        
        if 'soft_skills_count' in successful_cvs.columns:
            skills_analysis['avg_soft_skills'] = float(successful_cvs['soft_skills_count'].mean())
        
        if 'skills_diversity' in successful_cvs.columns:
            skills_analysis['avg_skills_diversity'] = float(successful_cvs['skills_diversity'].mean())
        
        return skills_analysis
    
    def _analyze_experience_patterns(self, successful_cvs: pd.DataFrame) -> Dict[str, Any]:
        """Analyze experience patterns in successful CVs"""
        if 'years_experience' not in successful_cvs.columns:
            return {}
        
        experience_data = successful_cvs['years_experience']
        
        return {
            'avg_experience': float(experience_data.mean()),
            'median_experience': float(experience_data.median()),
            'experience_range': {
                'min': float(experience_data.min()),
                'max': float(experience_data.max())
            },
            'experience_distribution': {
                'entry_level': int((experience_data < 2).sum()),
                'mid_level': int(((experience_data >= 2) & (experience_data < 5)).sum()),
                'senior_level': int((experience_data >= 5).sum())
            }
        }
    
    def _analyze_format_patterns(self, successful_cvs: pd.DataFrame) -> Dict[str, Any]:
        """Analyze format patterns in successful CVs"""
        format_columns = ['format_score', 'style_score']
        patterns = {}
        
        for column in format_columns:
            if column in successful_cvs.columns:
                patterns[f'avg_{column}'] = float(successful_cvs[column].mean())
        
        return patterns
    
    def _analyze_timing_patterns(self, successful_cvs: pd.DataFrame) -> Dict[str, Any]:
        """Analyze timing patterns in successful CVs"""
        timing_patterns = {}
        
        if 'upload_day_of_week' in successful_cvs.columns:
            day_distribution = successful_cvs['upload_day_of_week'].value_counts()
            timing_patterns['popular_upload_days'] = day_distribution.to_dict()
        
        if 'upload_hour' in successful_cvs.columns:
            hour_distribution = successful_cvs['upload_hour'].value_counts()
            timing_patterns['popular_upload_hours'] = hour_distribution.to_dict()
        
        return timing_patterns
    
    def _generate_success_recommendations(self, patterns: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on success patterns"""
        recommendations = []
        
        # Experience recommendations
        if 'experience_patterns' in patterns:
            exp_patterns = patterns['experience_patterns']
            if 'avg_experience' in exp_patterns:
                avg_exp = exp_patterns['avg_experience']
                if avg_exp > 3:
                    recommendations.append(f"Successful CVs average {avg_exp:.1f} years experience")
        
        # Skills recommendations
        if 'skill_patterns' in patterns:
            skill_patterns = patterns['skill_patterns']
            if 'avg_tech_skills' in skill_patterns:
                recommendations.append(f"Include diverse technical skills (successful CVs average {skill_patterns['avg_tech_skills']:.1f})")
        
        # Format recommendations
        if 'format_patterns' in patterns:
            format_patterns = patterns['format_patterns']
            if 'avg_format_score' in format_patterns:
                score = format_patterns['avg_format_score']
                if score > 75:
                    recommendations.append("Focus on professional formatting and presentation")
        
        return recommendations
    
    def _generate_improvement_recommendations(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Generate improvement recommendations based on data analysis"""
        try:
            recommendations = {
                'low_performers': [],
                'general': [],
                'skill_gaps': [],
                'format_improvements': []
            }
            
            # Analyze low performers (bottom 25%)
            low_threshold = data['overall_score'].quantile(0.25)
            low_performers = data[data['overall_score'] <= low_threshold]
            
            if not low_performers.empty:
                # Common issues in low performers
                avg_content = low_performers['content_score'].mean()
                avg_format = low_performers['format_score'].mean()
                avg_skills = low_performers['skills_score'].mean()
                
                if avg_content < 50:
                    recommendations['low_performers'].append("Focus on improving content quality and relevance")
                
                if avg_format < 50:
                    recommendations['low_performers'].append("Improve CV formatting and structure")
                
                if avg_skills < 50:
                    recommendations['low_performers'].append("Highlight skills more effectively")
            
            # General recommendations
            overall_avg = data['overall_score'].mean()
            if overall_avg < 70:
                recommendations['general'].append("Overall CV quality needs improvement across the board")
            
            # Skill gap analysis
            if 'tech_skills_count' in data.columns:
                low_tech_skills = data[data['tech_skills_count'] < 2]
                if len(low_tech_skills) > len(data) * 0.3:
                    recommendations['skill_gaps'].append("30%+ of CVs lack sufficient technical skills")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating improvement recommendations: {str(e)}")
            return {'error': str(e)}
    
    def _analyze_market_trends(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze market trends from CV data"""
        try:
            trends = {}
            
            # Skills demand trends
            if 'tech_skills_count' in data.columns:
                high_tech_cvs = data[data['tech_skills_count'] > 3]
                trends['technical_demand'] = {
                    'percentage': float(len(high_tech_cvs) / len(data) * 100),
                    'trend': 'increasing' if len(high_tech_cvs) > len(data) * 0.4 else 'stable'
                }
            
            # Experience level trends
            if 'years_experience' in data.columns:
                exp_distribution = {
                    'entry_level': float((data['years_experience'] < 2).sum() / len(data) * 100),
                    'mid_level': float(((data['years_experience'] >= 2) & (data['years_experience'] < 5)).sum() / len(data) * 100),
                    'senior_level': float((data['years_experience'] >= 5).sum() / len(data) * 100)
                }
                trends['experience_distribution'] = exp_distribution
            
            # Education trends
            if 'education_level' in data.columns:
                high_education = data[data['education_level'] >= 3]  # Bachelor or higher
                trends['education_level'] = {
                    'high_education_percentage': float(len(high_education) / len(data) * 100),
                    'trend': 'increasing'  # Placeholder - would need historical data
                }
            
            return trends
            
        except Exception as e:
            logger.error(f"Error analyzing market trends: {str(e)}")
            return {'error': str(e)}
    
    def _detect_cv_outliers(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Detect outliers in CV data using statistical methods"""
        try:
            outliers = {}
            
            # Score outliers
            score_columns = ['overall_score', 'content_score', 'format_score', 'skills_score']
            
            for column in score_columns:
                if column in data.columns:
                    Q1 = data[column].quantile(0.25)
                    Q3 = data[column].quantile(0.75)
                    IQR = Q3 - Q1
                    
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    
                    column_outliers = data[
                        (data[column] < lower_bound) | (data[column] > upper_bound)
                    ]
                    
                    outliers[column] = {
                        'count': len(column_outliers),
                        'percentage': float(len(column_outliers) / len(data) * 100),
                        'high_outliers': int((data[column] > upper_bound).sum()),
                        'low_outliers': int((data[column] < lower_bound).sum())
                    }
            
            return outliers
            
        except Exception as e:
            logger.error(f"Error detecting outliers: {str(e)}")
            return {'error': str(e)}
    
    def predict_candidate_success(self, cv_features: Dict[str, Any]) -> MLPrediction:
        """Predict candidate success probability"""
        try:
            # Load trained model
            model_file = os.path.join(self.model_path, 'cv_score_predictor.pkl')
            
            if not os.path.exists(model_file):
                return MLPrediction(
                    prediction=0.5,
                    confidence=0.0,
                    model_used='fallback',
                    features_used=[],
                    explanation='No trained model available'
                )
            
            with open(model_file, 'rb') as f:
                model_data = pickle.load(f)
                model = model_data['model']
                features = model_data['features']
            
            # Prepare features
            feature_vector = []
            used_features = []
            
            for feature in features:
                if feature in cv_features:
                    feature_vector.append(cv_features[feature])
                    used_features.append(feature)
                else:
                    feature_vector.append(0)  # Default value
            
            # Make prediction
            prediction = model.predict([feature_vector])[0]
            
            # Calculate confidence (simplified)
            confidence = min(0.95, max(0.1, abs(prediction - 50) / 50))
            
            # Generate explanation
            explanation = f"Predicted score: {prediction:.1f}/100 based on {len(used_features)} features"
            
            return MLPrediction(
                prediction=float(prediction),
                confidence=float(confidence),
                model_used='random_forest_regressor',
                features_used=used_features,
                explanation=explanation
            )
            
        except Exception as e:
            logger.error(f"Error predicting candidate success: {str(e)}")
            return MLPrediction(
                prediction=0.0,
                confidence=0.0,
                model_used='error',
                features_used=[],
                explanation=f'Prediction failed: {str(e)}'
            )
    
    def generate_insights(self, analysis_results: Dict[str, Any]) -> List[AnalyticsInsight]:
        """Generate actionable insights from analysis results"""
        insights = []
        
        try:
            # Skill cluster insights
            if 'skill_clusters' in analysis_results:
                cluster_data = analysis_results['skill_clusters']
                if 'insights' in cluster_data:
                    for insight_text in cluster_data['insights']:
                        insights.append(AnalyticsInsight(
                            type='pattern',
                            title='Skill Clustering Pattern',
                            description=insight_text,
                            confidence=0.8,
                            data_points={'source': 'skill_clustering'},
                            actionable=True,
                            priority='medium'
                        ))
            
            # Success pattern insights
            if 'success_patterns' in analysis_results:
                success_data = analysis_results['success_patterns']
                if 'recommendations' in success_data:
                    for recommendation in success_data['recommendations']:
                        insights.append(AnalyticsInsight(
                            type='recommendation',
                            title='Success Pattern Recommendation',
                            description=recommendation,
                            confidence=0.9,
                            data_points={'source': 'success_patterns'},
                            actionable=True,
                            priority='high'
                        ))
            
            # Market trend insights
            if 'market_trends' in analysis_results:
                trend_data = analysis_results['market_trends']
                
                if 'technical_demand' in trend_data:
                    tech_demand = trend_data['technical_demand']
                    insights.append(AnalyticsInsight(
                        type='trend',
                        title='Technical Skills Demand',
                        description=f"Technical skills present in {tech_demand['percentage']:.1f}% of CVs",
                        confidence=0.85,
                        data_points=tech_demand,
                        actionable=True,
                        priority='high'
                    ))
            
            # Outlier insights
            if 'outlier_detection' in analysis_results:
                outlier_data = analysis_results['outlier_detection']
                
                for score_type, outlier_info in outlier_data.items():
                    if outlier_info['percentage'] > 10:  # More than 10% outliers
                        insights.append(AnalyticsInsight(
                            type='anomaly',
                            title=f'{score_type.title()} Outliers Detected',
                            description=f"{outlier_info['percentage']:.1f}% of CVs show unusual {score_type} patterns",
                            confidence=0.7,
                            data_points=outlier_info,
                            actionable=True,
                            priority='medium'
                        ))
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating insights: {str(e)}")
            return []

# Usage Examples:
"""
# Initialize advanced analytics engine
analytics_engine = AdvancedAnalyticsEngine()

# Analyze CV patterns
patterns = analytics_engine.analyze_cv_patterns(date_range=90)

# Predict candidate success
cv_features = {
    'content_score': 75,
    'format_score': 80,
    'skills_score': 70,
    'years_experience': 3,
    'education_level': 3,
    'tech_skills_count': 5
}
prediction = analytics_engine.predict_candidate_success(cv_features)

# Generate insights
insights = analytics_engine.generate_insights(patterns)
""" 