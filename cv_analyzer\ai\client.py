from typing import Dict, Any, Optional, List
import openai
from pocketgroq import <PERSON>roqProvider
from tenacity import retry, stop_after_attempt, wait_exponential
import json
import logging
from .config import AIProviderConfig, AIModelConfig, AIPromptTemplate
from django.core.cache import cache

logger = logging.getLogger(__name__)

class AIClient:
    """Client for interacting with AI providers"""
    
    def __init__(self, provider: str = 'openai'):
        self.provider = provider
        self.config = AIProviderConfig(provider)
        self._setup_client()
    
    def _setup_client(self):
        """Set up the API client based on provider"""
        if self.provider == 'openai':
            openai.api_key = self.config.api_key
        elif self.provider == 'groq':
            self.client = GroqProvider(api_key=self.config.api_key)
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        reraise=True
    )
    async def analyze_cv(self, cv_text: str, position: str, company: str, requirements: str) -> Dict[str, Any]:
        """Analyze a CV using the AI provider"""
        try:
            if not self.config.check_rate_limit('analyze_cv'):
                raise Exception("Rate limit exceeded")
            
            prompt = AIPromptTemplate.format_prompt(
                'CV_ANALYSIS_TEMPLATE',
                position=position,
                company=company,
                requirements=requirements,
                cv_text=cv_text
            )
            
            model = AIModelConfig.get_model(self.provider, 'analysis')
            model_config = AIModelConfig.get_model_config(self.provider, 'analysis')
            
            if self.provider == 'openai':
                response = await openai.ChatCompletion.acreate(
                    model=model,
                    messages=[
                        {"role": "system", "content": "You are a professional CV analyzer."},
                        {"role": "user", "content": prompt}
                    ],
                    **model_config
                )
                result = json.loads(response.choices[0].message.content)
                self.config.track_usage(response.usage.total_tokens, 'analyze_cv')
            
            else:  # Groq
                response = await self.client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": "You are a professional CV analyzer."},
                        {"role": "user", "content": prompt}
                    ],
                    **model_config
                )
                result = json.loads(response.choices[0].message.content)
                # Groq doesn't provide token usage, estimate based on input/output length
                estimated_tokens = len(prompt) // 4 + len(response.choices[0].message.content) // 4
                self.config.track_usage(estimated_tokens, 'analyze_cv')
            
            return result
        
        except Exception as e:
            logger.error(f"Error in CV analysis: {str(e)}")
            raise
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        reraise=True
    )
    async def compare_cvs(
        self,
        cv1_text: str,
        cv2_text: str,
        position: str,
        requirements: str
    ) -> Dict[str, Any]:
        """Compare two CVs using the AI provider"""
        try:
            if not self.config.check_rate_limit('compare_cvs'):
                raise Exception("Rate limit exceeded")
            
            prompt = AIPromptTemplate.format_prompt(
                'COMPARISON_TEMPLATE',
                position=position,
                cv1_text=cv1_text,
                cv2_text=cv2_text,
                requirements=requirements
            )
            
            model = AIModelConfig.get_model(self.provider, 'analysis')
            model_config = AIModelConfig.get_model_config(self.provider, 'analysis')
            
            if self.provider == 'openai':
                response = await openai.ChatCompletion.acreate(
                    model=model,
                    messages=[
                        {"role": "system", "content": "You are a professional CV analyzer."},
                        {"role": "user", "content": prompt}
                    ],
                    **model_config
                )
                result = json.loads(response.choices[0].message.content)
                self.config.track_usage(response.usage.total_tokens, 'compare_cvs')
            
            else:  # Groq
                response = await self.client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": "You are a professional CV analyzer."},
                        {"role": "user", "content": prompt}
                    ],
                    **model_config
                )
                result = json.loads(response.choices[0].message.content)
                estimated_tokens = len(prompt) // 4 + len(response.choices[0].message.content) // 4
                self.config.track_usage(estimated_tokens, 'compare_cvs')
            
            return result
        
        except Exception as e:
            logger.error(f"Error in CV comparison: {str(e)}")
            raise
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics for the provider"""
        try:
            date_key = f"ai_usage_{self.provider}_*"
            keys = cache.keys(date_key)
            
            stats = {
                'total_tokens': 0,
                'total_requests': 0,
                'total_errors': 0,
                'daily_stats': {}
            }
            
            for key in keys:
                usage = cache.get(key)
                if usage:
                    date = key.split('_')[-1]
                    stats['daily_stats'][date] = usage
                    stats['total_tokens'] += usage['total_tokens']
                    stats['total_requests'] += usage['request_count']
                    stats['total_errors'] += usage.get('errors', 0)
            
            return stats
        
        except Exception as e:
            logger.error(f"Error getting usage stats: {str(e)}")
            return {} 