from django.conf import settings
from django.core.cache import cache
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class AIProviderConfig:
    """Configuration class for AI providers (OpenAI and Groq)"""
    
    def __init__(self, provider: str):
        self.provider = provider
        self.api_key = self._get_api_key()
        self.rate_limit = self._get_rate_limit()
        self.timeout = settings.AI_ANALYSIS_TIMEOUT
        self.max_retries = 3
        self.retry_delay = 1
        self.cache_ttl = 3600  # 1 hour
        
    def _get_api_key(self) -> str:
        """Get API key for the provider"""
        if self.provider == 'openai':
            return settings.OPENAI_API_KEY
        elif self.provider == 'groq':
            return settings.GROQ_API_KEY
        raise ValueError(f"Unsupported provider: {self.provider}")
    
    def _get_rate_limit(self) -> Dict[str, int]:
        """Get rate limit configuration for the provider"""
        if self.provider == 'openai':
            return {
                'requests_per_minute': 60,
                'tokens_per_minute': 90000
            }
        elif self.provider == 'groq':
            return {
                'requests_per_minute': 100,
                'tokens_per_minute': 150000
            }
        raise ValueError(f"Unsupported provider: {self.provider}")
    
    def check_rate_limit(self, key_suffix: str = '') -> bool:
        """Check if we're within rate limits"""
        cache_key = f"ai_rate_limit_{self.provider}_{key_suffix}"
        current_count = cache.get(cache_key, 0)
        
        if current_count >= self.rate_limit['requests_per_minute']:
            logger.warning(f"Rate limit exceeded for {self.provider}")
            return False
        
        # Increment counter with 60s expiry
        cache.set(cache_key, current_count + 1, 60)
        return True
    
    def track_usage(self, tokens_used: int, request_type: str):
        """Track API usage"""
        date_key = f"ai_usage_{self.provider}_{request_type}_{datetime.now().strftime('%Y%m%d')}"
        
        current_usage = cache.get(date_key, {
            'total_tokens': 0,
            'request_count': 0,
            'errors': 0
        })
        
        current_usage['total_tokens'] += tokens_used
        current_usage['request_count'] += 1
        
        cache.set(date_key, current_usage, 86400)  # Store for 24 hours

class AIPromptTemplate:
    """Template manager for AI prompts"""
    
    CV_ANALYSIS_TEMPLATE = """Analyze the following CV for the position of {position} at {company}.
    
Requirements:
{requirements}

CV Text:
{cv_text}

Provide a detailed analysis covering:
1. Overall match score (0-100)
2. Skills assessment
3. Experience evaluation
4. Education relevance
5. Key strengths
6. Areas for improvement
7. Specific recommendations

Format the response as a structured JSON object."""

    COMPARISON_TEMPLATE = """Compare the following CVs for the position of {position}:

CV 1:
{cv1_text}

CV 2:
{cv2_text}

Requirements:
{requirements}

Provide a detailed comparison covering:
1. Relative strengths and weaknesses
2. Skills comparison
3. Experience comparison
4. Best fit analysis
5. Recommendations

Format the response as a structured JSON object."""

    @staticmethod
    def get_template(template_name: str) -> str:
        """Get a prompt template by name"""
        if hasattr(AIPromptTemplate, template_name):
            return getattr(AIPromptTemplate, template_name)
        raise ValueError(f"Template not found: {template_name}")
    
    @staticmethod
    def format_prompt(template_name: str, **kwargs) -> str:
        """Format a prompt template with provided parameters"""
        template = AIPromptTemplate.get_template(template_name)
        try:
            return template.format(**kwargs)
        except KeyError as e:
            logger.error(f"Missing required parameter in prompt template: {e}")
            raise ValueError(f"Missing required parameter: {e}")

class AIModelConfig:
    """Configuration for AI models"""
    
    OPENAI_MODELS = {
        'default': 'gpt-4',
        'fast': 'gpt-3.5-turbo',
        'analysis': 'gpt-4-turbo-preview'
    }
    
    GROQ_MODELS = {
        'default': 'mixtral-8x7b-32768',
        'fast': 'llama2-70b-4096',
        'analysis': 'mixtral-8x7b-32768'
    }
    
    @staticmethod
    def get_model(provider: str, model_type: str = 'default') -> str:
        """Get the appropriate model for a provider and type"""
        if provider == 'openai':
            return AIModelConfig.OPENAI_MODELS.get(model_type, AIModelConfig.OPENAI_MODELS['default'])
        elif provider == 'groq':
            return AIModelConfig.GROQ_MODELS.get(model_type, AIModelConfig.GROQ_MODELS['default'])
        raise ValueError(f"Unsupported provider: {provider}")
    
    @staticmethod
    def get_model_config(provider: str, model_type: str = 'default') -> Dict[str, Any]:
        """Get model configuration parameters"""
        base_config = {
            'temperature': 0.7,
            'max_tokens': 2000,
            'top_p': 1,
            'frequency_penalty': 0,
            'presence_penalty': 0
        }
        
        if model_type == 'analysis':
            base_config.update({
                'temperature': 0.5,
                'max_tokens': 4000
            })
        elif model_type == 'fast':
            base_config.update({
                'temperature': 0.9,
                'max_tokens': 1000
            })
        
        return base_config 