"""
Optimized prompt templates for CV analysis using AI providers.
These prompts are designed to extract structured information and provide consistent analysis.
"""

CV_ANALYSIS_PROMPT = """
Analyze the following CV in detail and provide a structured assessment.

CV Text:
{cv_text}

Job Requirements:
{job_requirements}

Instructions:
1. Extract and validate key information:
   - Personal details (name, contact, location)
   - Professional links (LinkedIn, websites, portfolios, GitHub)
   - Professional summary
   - Work experience (chronological order)
   - Education and certifications
   - Skills and competencies
   - Languages and proficiency levels

2. Evaluate the following aspects (score 0-100):
   - Content relevance to the position
   - Format and presentation
   - Experience alignment
   - Skills match
   - Education fit
   - Overall compatibility

3. Provide specific insights:
   - Key strengths
   - Areas for improvement
   - Missing critical requirements
   - Unique qualifications
   - Professional growth trajectory

4. Technical assessment:
   - Required skills present
   - Years of relevant experience
   - Technical proficiency levels
   - Industry-specific knowledge
   - Tool and technology expertise

Please format the response as a structured JSON object with the following schema:
{
    "personal_info": {
        "name": str,
        "email": str,
        "phone": str,
        "location": str
    },
    "professional_links": {
        "linkedin_profile": str,
        "website_portfolio": str,
        "github_profile": str,
        "other_links": list[str]
    },
    "scores": {
        "overall": int,
        "content": int,
        "format": int,
        "experience": int,
        "skills": int,
        "education": int
    },
    "analysis": {
        "strengths": list[str],
        "improvements": list[str],
        "missing_requirements": list[str],
        "unique_qualifications": list[str]
    },
    "technical": {
        "skills_present": list[str],
        "skills_missing": list[str],
        "years_relevant_experience": int,
        "proficiency_levels": dict[str, str]
    },
    "recommendations": list[str]
}
"""

COMPARISON_PROMPT = """
Compare the following CVs for the specified position and provide a detailed analysis.

Position: {position}
Requirements: {requirements}

CV 1:
{cv1_text}

CV 2:
{cv2_text}

Instructions:
1. Compare key aspects:
   - Relevant experience
   - Skills alignment
   - Education fit
   - Technical proficiency
   - Industry knowledge

2. Evaluate relative strengths:
   - Unique qualifications
   - Experience depth
   - Skill breadth
   - Cultural fit indicators
   - Growth potential

3. Provide specific insights:
   - Key differentiators
   - Comparative advantages
   - Risk factors
   - Development needs
   - Best fit analysis

Please format the response as a structured JSON object with the following schema:
{
    "comparison": {
        "experience": {
            "cv1_score": int,
            "cv2_score": int,
            "analysis": str
        },
        "skills": {
            "cv1_score": int,
            "cv2_score": int,
            "analysis": str
        },
        "education": {
            "cv1_score": int,
            "cv2_score": int,
            "analysis": str
        },
        "overall": {
            "cv1_score": int,
            "cv2_score": int,
            "recommendation": str
        }
    },
    "differentiators": {
        "cv1": list[str],
        "cv2": list[str]
    },
    "best_fit": {
        "recommendation": str,
        "rationale": str
    }
}
"""

CV_VACANCY_COMPARISON_PROMPT = """
Perform a detailed compatibility analysis between a candidate's CV and a specific job vacancy.

JOB VACANCY:
Title: {vacancy_title}
Company: {company_name}
Description: {vacancy_description}
Requirements: {vacancy_requirements}
Category: {vacancy_category}

CANDIDATE CV:
{cv_text}

ANALYSIS INSTRUCTIONS:
1. Evaluate compatibility across key dimensions (score 0-100 for each):
   - Skills alignment with job requirements
   - Experience relevance and depth
   - Education and qualifications match
   - Industry knowledge and background
   - Role-specific competencies

2. Identify specific strengths:
   - Skills that directly match requirements
   - Unique qualifications or certifications
   - Relevant experience highlights
   - Competitive advantages

3. Note areas of concern:
   - Missing critical skills or experience
   - Potential skill gaps
   - Experience mismatches
   - Development needs

4. Provide actionable recommendations:
   - Overall recommendation level
   - Specific next steps
   - Interview focus areas
   - Development suggestions

Format as JSON:
{{
    "compatibility_analysis": {{
        "overall_score": [0-100 integer],
        "skills_alignment": [0-100 integer],
        "experience_relevance": [0-100 integer],
        "education_match": [0-100 integer],
        "industry_knowledge": [0-100 integer],
        "role_suitability": [0-100 integer]
    }},
    "strengths": [
        "Specific strength 1",
        "Specific strength 2", 
        "Specific strength 3"
    ],
    "concerns": [
        "Specific concern 1",
        "Specific concern 2"
    ],
    "skills_analysis": {{
        "matching_skills": ["skill1", "skill2", "skill3"],
        "missing_critical_skills": ["skill1", "skill2"],
        "technical_proficiency": [0-100 integer],
        "soft_skills_rating": [0-100 integer]
    }},
    "recommendation": {{
        "level": "Highly Recommended|Recommended|Consider|Not Suitable",
        "reasoning": "Detailed explanation for recommendation",
        "next_steps": ["action1", "action2", "action3"],
        "interview_focus": ["area1", "area2"]
    }},
    "summary": "2-3 sentence overall assessment and fit summary"
}}

IMPORTANT: Provide ONLY the JSON response. Be specific and actionable in your analysis.
"""

BATCH_ANALYSIS_PROMPT = """
Analyze the following batch of CVs for the specified position and provide a comparative assessment.

Position: {position}
Requirements: {requirements}

CVs:
{cv_texts}

Instructions:
1. For each CV:
   - Extract key information
   - Calculate compatibility scores
   - Identify unique strengths
   - Note missing requirements

2. Provide batch insights:
   - Top candidates
   - Skill distribution
   - Experience ranges
   - Common gaps
   - Diversity indicators

3. Generate recommendations:
   - Interview priorities
   - Skill development needs
   - Alternative roles
   - Group characteristics

Please format the response as a structured JSON object with the following schema:
{
    "individual_analyses": list[{
        "cv_id": str,
        "scores": {
            "overall": int,
            "skills": int,
            "experience": int,
            "education": int
        },
        "strengths": list[str],
        "gaps": list[str]
    }],
    "batch_insights": {
        "skill_distribution": dict[str, int],
        "experience_ranges": dict[str, int],
        "common_gaps": list[str],
        "diversity_metrics": dict[str, float]
    },
    "recommendations": {
        "top_candidates": list[str],
        "interview_priority": list[str],
        "skill_development": dict[str, list[str]],
        "alternative_roles": dict[str, list[str]]
    }
}
"""

QUALITY_CHECK_PROMPT = """
Validate the following CV analysis results for quality and consistency.

Analysis Results:
{analysis_results}

Instructions:
1. Verify data quality:
   - Score consistency
   - Analysis completeness
   - Recommendation relevance
   - Data format validity

2. Check for:
   - Scoring anomalies
   - Missing critical data
   - Inconsistent assessments
   - Bias indicators
   - Format compliance

Please format the response as a structured JSON object with the following schema:
{
    "validation": {
        "is_valid": bool,
        "score_consistency": bool,
        "completeness": bool,
        "format_compliance": bool
    },
    "issues": {
        "critical": list[str],
        "warnings": list[str],
        "suggestions": list[str]
    },
    "metrics": {
        "quality_score": int,
        "confidence_level": int,
        "bias_indicators": list[str]
    },
    "recommendations": {
        "improvements": list[str],
        "corrections": list[str]
    }
}
""" 