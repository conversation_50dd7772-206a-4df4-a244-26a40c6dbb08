"""
AI System Enhancement Module
Handles AI provider management, cost monitoring, and performance optimization
"""

import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
from django.db import models
from concurrent.futures import ThreadPoolExecutor, as_completed
import openai
import requests

logger = logging.getLogger(__name__)

class AIProvider(Enum):
    OPENAI = "openai"
    GROQ = "groq"
    ANTHROPIC = "anthropic"
    COHERE = "cohere"

@dataclass
class AIProviderConfig:
    """Configuration for AI providers"""
    name: str
    api_key: str
    base_url: Optional[str] = None
    model: str = "gpt-3.5-turbo"
    max_tokens: int = 4000
    temperature: float = 0.7
    timeout: int = 30
    cost_per_1k_tokens: float = 0.002
    rate_limit_rpm: int = 3500  # requests per minute
    rate_limit_tpm: int = 90000  # tokens per minute

class AIProviderManager:
    """Manages multiple AI providers with health checks and failover"""
    
    def __init__(self):
        self.providers = {}
        self.provider_health = {}
        self.provider_costs = {}
        self.provider_usage = {}
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize AI providers from configuration"""
        
        # OpenAI Configuration
        if settings.OPENAI_API_KEY:
            self.providers[AIProvider.OPENAI] = AIProviderConfig(
                name="OpenAI",
                api_key=settings.OPENAI_API_KEY,
                model="gpt-3.5-turbo",
                max_tokens=4000,
                cost_per_1k_tokens=0.002,
                rate_limit_rpm=3500,
                rate_limit_tpm=90000
            )
        
        # Groq Configuration
        if settings.GROQ_API_KEY:
            self.providers[AIProvider.GROQ] = AIProviderConfig(
                name="Groq",
                api_key=settings.GROQ_API_KEY,
                base_url="https://api.groq.com/openai/v1",
                model="mixtral-8x7b-32768",
                max_tokens=4000,
                cost_per_1k_tokens=0.0002,  # Much cheaper
                rate_limit_rpm=30,
                rate_limit_tpm=6000
            )
        
        # Initialize health status
        for provider in self.providers:
            self.provider_health[provider] = {
                'status': 'unknown',
                'last_check': None,
                'response_time': None,
                'error_count': 0
            }
            self.provider_costs[provider] = {
                'total_cost': 0.0,
                'total_tokens': 0,
                'requests_count': 0
            }
            self.provider_usage[provider] = {
                'requests_last_hour': 0,
                'tokens_last_hour': 0,
                'last_request': None
            }
    
    def check_provider_health(self, provider: AIProvider) -> Dict[str, Any]:
        """Check health of a specific AI provider"""
        if provider not in self.providers:
            return {'status': 'unavailable', 'error': 'Provider not configured'}
        
        config = self.providers[provider]
        start_time = time.time()
        
        try:
            # Perform health check with minimal request
            test_prompt = "Say 'OK' if you're working."
            
            if provider == AIProvider.OPENAI:
                response = self._test_openai_health(config, test_prompt)
            elif provider == AIProvider.GROQ:
                response = self._test_groq_health(config, test_prompt)
            else:
                response = {'status': 'unsupported', 'error': 'Provider not implemented'}
            
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            if response.get('status') == 'healthy':
                self.provider_health[provider] = {
                    'status': 'healthy',
                    'last_check': timezone.now(),
                    'response_time': response_time,
                    'error_count': 0
                }
            else:
                self.provider_health[provider]['error_count'] += 1
                self.provider_health[provider]['status'] = 'unhealthy'
            
            return {
                'provider': provider.value,
                'status': self.provider_health[provider]['status'],
                'response_time_ms': response_time,
                'error': response.get('error')
            }
            
        except Exception as e:
            self.provider_health[provider]['error_count'] += 1
            self.provider_health[provider]['status'] = 'error'
            self.provider_health[provider]['last_check'] = timezone.now()
            
            return {
                'provider': provider.value,
                'status': 'error',
                'error': str(e),
                'response_time_ms': (time.time() - start_time) * 1000
            }
    
    def _test_openai_health(self, config: AIProviderConfig, test_prompt: str) -> Dict[str, Any]:
        """Test OpenAI provider health"""
        try:
            client = openai.OpenAI(api_key=config.api_key)
            
            response = client.chat.completions.create(
                model=config.model,
                messages=[{"role": "user", "content": test_prompt}],
                max_tokens=10,
                timeout=config.timeout
            )
            
            if response.choices and response.choices[0].message.content:
                return {'status': 'healthy'}
            else:
                return {'status': 'unhealthy', 'error': 'Empty response'}
                
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def _test_groq_health(self, config: AIProviderConfig, test_prompt: str) -> Dict[str, Any]:
        """Test Groq provider health"""
        try:
            headers = {
                'Authorization': f'Bearer {config.api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': config.model,
                'messages': [{"role": "user", "content": test_prompt}],
                'max_tokens': 10
            }
            
            response = requests.post(
                f"{config.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=config.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('choices') and result['choices'][0].get('message'):
                    return {'status': 'healthy'}
                else:
                    return {'status': 'unhealthy', 'error': 'Empty response'}
            else:
                return {'status': 'error', 'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def check_all_providers_health(self) -> Dict[str, Any]:
        """Check health of all configured providers"""
        health_results = {}
        
        with ThreadPoolExecutor(max_workers=len(self.providers)) as executor:
            future_to_provider = {
                executor.submit(self.check_provider_health, provider): provider
                for provider in self.providers
            }
            
            for future in as_completed(future_to_provider):
                provider = future_to_provider[future]
                try:
                    result = future.result()
                    health_results[provider.value] = result
                except Exception as e:
                    health_results[provider.value] = {
                        'status': 'error',
                        'error': str(e)
                    }
        
        return health_results
    
    def get_best_provider(self, criteria: str = 'health') -> Optional[AIProvider]:
        """Get best provider based on criteria"""
        available_providers = [
            provider for provider in self.providers
            if self.provider_health[provider]['status'] == 'healthy'
        ]
        
        if not available_providers:
            return None
        
        if criteria == 'cost':
            # Return cheapest provider
            return min(available_providers, 
                      key=lambda p: self.providers[p].cost_per_1k_tokens)
        elif criteria == 'speed':
            # Return fastest provider
            return min(available_providers,
                      key=lambda p: self.provider_health[p].get('response_time', float('inf')))
        else:
            # Return first healthy provider
            return available_providers[0]
    
    def track_usage(self, provider: AIProvider, tokens_used: int, cost: float):
        """Track provider usage and costs"""
        if provider in self.provider_costs:
            self.provider_costs[provider]['total_cost'] += cost
            self.provider_costs[provider]['total_tokens'] += tokens_used
            self.provider_costs[provider]['requests_count'] += 1
        
        if provider in self.provider_usage:
            self.provider_usage[provider]['requests_last_hour'] += 1
            self.provider_usage[provider]['tokens_last_hour'] += tokens_used
            self.provider_usage[provider]['last_request'] = timezone.now()
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """Get usage statistics for all providers"""
        stats = {}
        
        for provider in self.providers:
            provider_name = provider.value
            stats[provider_name] = {
                'health': self.provider_health[provider],
                'costs': self.provider_costs[provider],
                'usage': self.provider_usage[provider],
                'config': {
                    'model': self.providers[provider].model,
                    'cost_per_1k_tokens': self.providers[provider].cost_per_1k_tokens,
                    'rate_limits': {
                        'rpm': self.providers[provider].rate_limit_rpm,
                        'tpm': self.providers[provider].rate_limit_tpm
                    }
                }
            }
        
        return stats

class AIAnalysisOptimizer:
    """Optimizes AI analysis performance and caching"""
    
    def __init__(self):
        self.provider_manager = AIProviderManager()
        self.cache_prefix = 'ai_analysis_'
        self.cache_timeout = 3600  # 1 hour
    
    def optimize_prompt(self, cv_content: str, vacancy_description: str) -> str:
        """Optimize prompt for better AI analysis"""
        
        # Truncate content if too long
        max_cv_length = 3000
        max_vacancy_length = 1000
        
        if len(cv_content) > max_cv_length:
            cv_content = cv_content[:max_cv_length] + "..."
        
        if len(vacancy_description) > max_vacancy_length:
            vacancy_description = vacancy_description[:max_vacancy_length] + "..."
        
        optimized_prompt = f"""
Analyze the CV-vacancy match with the following criteria:

VACANCY REQUIREMENTS:
{vacancy_description}

CV CONTENT:
{cv_content}

ANALYSIS INSTRUCTIONS:
1. Calculate match percentage (0-100%)
2. Identify key matching skills and experience
3. Highlight missing requirements
4. Provide specific recommendations
5. Rate overall fit: Excellent/Good/Fair/Poor

Respond in JSON format with:
{{
    "match_percentage": <number>,
    "matching_skills": [<list of strings>],
    "missing_requirements": [<list of strings>],
    "recommendations": "<string>",
    "overall_fit": "<rating>",
    "confidence": <number 0-1>
}}
"""
        return optimized_prompt
    
    def get_cached_analysis(self, cv_id: int, vacancy_id: int) -> Optional[Dict[str, Any]]:
        """Get cached analysis result"""
        cache_key = f"{self.cache_prefix}cv_{cv_id}_vacancy_{vacancy_id}"
        return cache.get(cache_key)
    
    def cache_analysis_result(self, cv_id: int, vacancy_id: int, result: Dict[str, Any]):
        """Cache analysis result"""
        cache_key = f"{self.cache_prefix}cv_{cv_id}_vacancy_{vacancy_id}"
        cache.set(cache_key, result, timeout=self.cache_timeout)
    
    def analyze_with_fallback(self, cv_content: str, vacancy_description: str, 
                            cv_id: int, vacancy_id: int) -> Dict[str, Any]:
        """Perform analysis with provider fallback"""
        
        # Check cache first
        cached_result = self.get_cached_analysis(cv_id, vacancy_id)
        if cached_result:
            logger.info(f"Using cached analysis for CV {cv_id} and Vacancy {vacancy_id}")
            return cached_result
        
        # Get optimized prompt
        prompt = self.optimize_prompt(cv_content, vacancy_description)
        
        # Try providers in order of preference
        providers_to_try = [
            self.provider_manager.get_best_provider('cost'),
            self.provider_manager.get_best_provider('speed'),
            self.provider_manager.get_best_provider('health')
        ]
        
        # Remove duplicates while preserving order
        providers_to_try = list(dict.fromkeys(filter(None, providers_to_try)))
        
        for provider in providers_to_try:
            try:
                result = self._perform_analysis(provider, prompt)
                
                # Track usage
                tokens_used = len(prompt.split()) * 1.3  # Rough estimate
                cost = (tokens_used / 1000) * self.provider_manager.providers[provider].cost_per_1k_tokens
                self.provider_manager.track_usage(provider, int(tokens_used), cost)
                
                # Cache result
                result['provider_used'] = provider.value
                result['analysis_timestamp'] = timezone.now().isoformat()
                self.cache_analysis_result(cv_id, vacancy_id, result)
                
                return result
                
            except Exception as e:
                logger.warning(f"Analysis failed with provider {provider.value}: {e}")
                continue
        
        # If all providers fail, return error result
        return {
            'error': 'All AI providers failed',
            'match_percentage': 0,
            'matching_skills': [],
            'missing_requirements': ['Analysis unavailable'],
            'recommendations': 'Unable to analyze due to AI service issues',
            'overall_fit': 'Unknown',
            'confidence': 0,
            'provider_used': 'none'
        }
    
    def _perform_analysis(self, provider: AIProvider, prompt: str) -> Dict[str, Any]:
        """Perform analysis with specific provider"""
        config = self.provider_manager.providers[provider]
        
        if provider == AIProvider.OPENAI:
            return self._analyze_with_openai(config, prompt)
        elif provider == AIProvider.GROQ:
            return self._analyze_with_groq(config, prompt)
        else:
            raise NotImplementedError(f"Provider {provider.value} not implemented")
    
    def _analyze_with_openai(self, config: AIProviderConfig, prompt: str) -> Dict[str, Any]:
        """Analyze using OpenAI"""
        client = openai.OpenAI(api_key=config.api_key)
        
        response = client.chat.completions.create(
            model=config.model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=config.max_tokens,
            temperature=config.temperature,
            timeout=config.timeout
        )
        
        content = response.choices[0].message.content
        
        try:
            # Parse JSON response
            result = json.loads(content)
            return result
        except json.JSONDecodeError:
            # Fallback parsing
            return self._parse_fallback_response(content)
    
    def _analyze_with_groq(self, config: AIProviderConfig, prompt: str) -> Dict[str, Any]:
        """Analyze using Groq"""
        headers = {
            'Authorization': f'Bearer {config.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': config.model,
            'messages': [{"role": "user", "content": prompt}],
            'max_tokens': config.max_tokens,
            'temperature': config.temperature
        }
        
        response = requests.post(
            f"{config.base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=config.timeout
        )
        
        if response.status_code != 200:
            raise Exception(f"Groq API error: {response.status_code}")
        
        result = response.json()
        content = result['choices'][0]['message']['content']
        
        try:
            # Parse JSON response
            analysis_result = json.loads(content)
            return analysis_result
        except json.JSONDecodeError:
            # Fallback parsing
            return self._parse_fallback_response(content)
    
    def _parse_fallback_response(self, content: str) -> Dict[str, Any]:
        """Parse non-JSON response as fallback"""
        # Basic fallback parsing
        match_percentage = 50  # Default
        confidence = 0.5
        
        # Try to extract percentage
        import re
        percentage_match = re.search(r'(\d+)%', content)
        if percentage_match:
            match_percentage = int(percentage_match.group(1))
        
        return {
            'match_percentage': match_percentage,
            'matching_skills': ['Unable to parse specific skills'],
            'missing_requirements': ['Analysis parsing incomplete'],
            'recommendations': content[:500] + "..." if len(content) > 500 else content,
            'overall_fit': 'Fair',
            'confidence': confidence
        }

class AICostMonitor:
    """Monitors and alerts on AI usage costs"""
    
    def __init__(self):
        self.daily_budget = 50.0  # $50 daily budget
        self.monthly_budget = 1000.0  # $1000 monthly budget
        self.alert_thresholds = [0.5, 0.75, 0.9, 1.0]  # 50%, 75%, 90%, 100%
    
    def get_current_usage(self) -> Dict[str, Any]:
        """Get current usage statistics"""
        provider_manager = AIProviderManager()
        usage_stats = provider_manager.get_usage_statistics()
        
        total_daily_cost = 0
        total_monthly_cost = 0
        
        for provider_name, stats in usage_stats.items():
            provider_cost = stats['costs']['total_cost']
            total_monthly_cost += provider_cost
            
            # Estimate daily cost (rough approximation)
            # In production, this should use proper time-based tracking
            total_daily_cost += provider_cost / 30  # Rough daily average
        
        return {
            'daily_cost': total_daily_cost,
            'monthly_cost': total_monthly_cost,
            'daily_budget': self.daily_budget,
            'monthly_budget': self.monthly_budget,
            'daily_usage_percent': (total_daily_cost / self.daily_budget) * 100,
            'monthly_usage_percent': (total_monthly_cost / self.monthly_budget) * 100,
            'provider_breakdown': usage_stats
        }
    
    def check_budget_alerts(self) -> List[Dict[str, Any]]:
        """Check if budget alerts should be triggered"""
        usage = self.get_current_usage()
        alerts = []
        
        for threshold in self.alert_thresholds:
            # Check daily budget
            if usage['daily_usage_percent'] >= (threshold * 100):
                alerts.append({
                    'type': 'daily_budget',
                    'threshold': threshold,
                    'current_usage': usage['daily_usage_percent'],
                    'message': f"Daily AI budget {threshold*100}% threshold exceeded"
                })
            
            # Check monthly budget
            if usage['monthly_usage_percent'] >= (threshold * 100):
                alerts.append({
                    'type': 'monthly_budget',
                    'threshold': threshold,
                    'current_usage': usage['monthly_usage_percent'],
                    'message': f"Monthly AI budget {threshold*100}% threshold exceeded"
                })
        
        return alerts
    
    def get_cost_optimization_recommendations(self) -> List[str]:
        """Get cost optimization recommendations"""
        usage = self.get_current_usage()
        recommendations = []
        
        if usage['daily_usage_percent'] > 80:
            recommendations.append("Consider using cheaper AI providers for non-critical analyses")
            recommendations.append("Implement more aggressive caching to reduce API calls")
            recommendations.append("Optimize prompts to reduce token usage")
        
        if usage['monthly_usage_percent'] > 70:
            recommendations.append("Review and optimize AI analysis frequency")
            recommendations.append("Consider batch processing for multiple CVs")
            recommendations.append("Implement usage limits per user")
        
        # Provider-specific recommendations
        provider_stats = usage['provider_breakdown']
        for provider_name, stats in provider_stats.items():
            cost_per_request = stats['costs']['total_cost'] / max(stats['costs']['requests_count'], 1)
            if cost_per_request > 0.10:  # $0.10 per request
                recommendations.append(f"High cost per request for {provider_name}: ${cost_per_request:.3f}")
        
        return recommendations

# Global instances
ai_provider_manager = AIProviderManager()
ai_analysis_optimizer = AIAnalysisOptimizer()
ai_cost_monitor = AICostMonitor()

# Utility functions
def get_ai_provider_health() -> Dict[str, Any]:
    """Get health status of all AI providers"""
    return ai_provider_manager.check_all_providers_health()

def get_ai_usage_statistics() -> Dict[str, Any]:
    """Get AI usage statistics"""
    return ai_provider_manager.get_usage_statistics()

def get_ai_cost_monitoring() -> Dict[str, Any]:
    """Get AI cost monitoring data"""
    return ai_cost_monitor.get_current_usage()

def analyze_cv_optimized(cv_content: str, vacancy_description: str, 
                        cv_id: int, vacancy_id: int) -> Dict[str, Any]:
    """Perform optimized CV analysis"""
    return ai_analysis_optimizer.analyze_with_fallback(
        cv_content, vacancy_description, cv_id, vacancy_id
    ) 