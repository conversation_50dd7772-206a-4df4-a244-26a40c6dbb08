import requests
import openai
from django.conf import settings
from .models import AIAPIConfig, AIPromptConfig

def get_ai_response(prompt, instructions):
    api_configs = AIAPIConfig.objects.filter(is_active=True).order_by('priority')
    
    for config in api_configs:
        try:
            if config.provider == 'ollama':
                return ollama_api_call(config, prompt, instructions)
            elif config.provider == 'groq':
                return groq_api_call(config, prompt, instructions)
            elif config.provider == 'openai':
                return openai_api_call(config, prompt, instructions)
        except Exception as e:
            print(f"Error with {config.provider}: {str(e)}")
            continue
    
    raise Exception("All AI API calls failed")

def ollama_api_call(config, prompt, instructions):
    url = config.api_url or "http://localhost:11434/api/generate"
    data = {
        "model": config.model_name,
        "prompt": f"{instructions}\n\n{prompt}",
    }
    response = requests.post(url, json=data)
    response.raise_for_status()
    return response.json()['response']

def groq_api_call(config, prompt, instructions):
    import groq
    client = groq.Groq(api_key=config.api_key)
    response = client.chat.completions.create(
        messages=[
            {"role": "system", "content": instructions},
            {"role": "user", "content": prompt}
        ],
        model=config.model_name,
    )
    return response.choices[0].message.content

def openai_api_call(config, prompt, instructions):
    openai.api_key = config.api_key
    response = openai.ChatCompletion.create(
        model=config.model_name,
        messages=[
            {"role": "system", "content": instructions},
            {"role": "user", "content": prompt}
        ],
    )
    return response.choices[0].message['content']
