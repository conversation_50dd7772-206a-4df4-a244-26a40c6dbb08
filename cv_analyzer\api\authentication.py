"""
API Authentication system for CV Analyzer.
Implements JWT tokens, API keys, and OAuth2 authentication.
"""

import jwt
import secrets
import hashlib
import hmac
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, Union
from django.conf import settings
from django.contrib.auth.models import User
from django.core.cache import cache
from django.utils import timezone
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from rest_framework import status
from ..models import SecurityAuditLog
import logging

logger = logging.getLogger(__name__)

class APIKey:
    """API Key management and validation."""
    
    def __init__(self):
        """Initialize API key manager."""
        self.secret_key = getattr(settings, 'SECRET_KEY', 'default-secret')
        self.api_key_prefix = 'cvanalyzer_'
        
    def generate_api_key(self, user_id: int, name: str = '', expires_days: int = 365) -> Dict[str, str]:
        """
        Generate a new API key for a user.
        
        Args:
            user_id: User ID
            name: Optional name for the API key
            expires_days: Number of days until expiration
            
        Returns:
            Dict containing the API key and metadata
        """
        # Generate random key
        random_bytes = secrets.token_bytes(32)
        key_id = secrets.token_urlsafe(16)
        
        # Create key data
        key_data = {
            'user_id': user_id,
            'key_id': key_id,
            'name': name,
            'created_at': datetime.utcnow().isoformat(),
            'expires_at': (datetime.utcnow() + timedelta(days=expires_days)).isoformat(),
            'is_active': True
        }
        
        # Create signature
        signature = self._create_signature(key_data)
        
        # Combine into API key
        api_key = f"{self.api_key_prefix}{key_id}_{signature}"
        
        # Store key data in cache/database
        cache_key = f"api_key:{key_id}"
        cache.set(cache_key, key_data, timeout=expires_days * 24 * 3600)
        
        return {
            'api_key': api_key,
            'key_id': key_id,
            'expires_at': key_data['expires_at'],
            'name': name
        }
    
    def validate_api_key(self, api_key: str) -> Optional[Dict]:
        """
        Validate an API key and return user data.
        
        Args:
            api_key: The API key to validate
            
        Returns:
            User data if valid, None if invalid
        """
        try:
            # Check format
            if not api_key.startswith(self.api_key_prefix):
                return None
            
            # Extract key ID and signature
            key_part = api_key[len(self.api_key_prefix):]
            if '_' not in key_part:
                return None
            
            key_id, signature = key_part.split('_', 1)
            
            # Get key data from cache
            cache_key = f"api_key:{key_id}"
            key_data = cache.get(cache_key)
            
            if not key_data:
                return None
            
            # Verify signature
            expected_signature = self._create_signature(key_data)
            if not hmac.compare_digest(signature, expected_signature):
                return None
            
            # Check if active and not expired
            if not key_data.get('is_active', False):
                return None
            
            expires_at = datetime.fromisoformat(key_data['expires_at'])
            if datetime.utcnow() > expires_at:
                return None
            
            return key_data
            
        except Exception as e:
            logger.error(f"API key validation error: {str(e)}")
            return None
    
    def _create_signature(self, key_data: Dict) -> str:
        """Create HMAC signature for key data."""
        # Create deterministic string from key data
        data_string = f"{key_data['user_id']}:{key_data['key_id']}:{key_data['created_at']}"
        
        # Create HMAC signature
        signature = hmac.new(
            self.secret_key.encode(),
            data_string.encode(),
            hashlib.sha256
        ).hexdigest()[:32]
        
        return signature
    
    def revoke_api_key(self, key_id: str) -> bool:
        """Revoke an API key."""
        try:
            cache_key = f"api_key:{key_id}"
            key_data = cache.get(cache_key)
            
            if key_data:
                key_data['is_active'] = False
                cache.set(cache_key, key_data, timeout=86400)  # Keep for 24h for audit
                return True
            
            return False
        except Exception as e:
            logger.error(f"Error revoking API key: {str(e)}")
            return False

class JWTManager:
    """JWT token management for API authentication."""
    
    def __init__(self):
        """Initialize JWT manager."""
        self.secret_key = getattr(settings, 'SECRET_KEY', 'default-secret')
        self.algorithm = 'HS256'
        self.access_token_lifetime = timedelta(hours=1)
        self.refresh_token_lifetime = timedelta(days=7)
    
    def generate_tokens(self, user: User) -> Dict[str, str]:
        """
        Generate access and refresh tokens for a user.
        
        Args:
            user: Django User instance
            
        Returns:
            Dict containing access and refresh tokens
        """
        now = datetime.utcnow()
        
        # Access token payload
        access_payload = {
            'user_id': user.id,
            'username': user.username,
            'email': user.email,
            'iat': now,
            'exp': now + self.access_token_lifetime,
            'type': 'access'
        }
        
        # Refresh token payload
        refresh_payload = {
            'user_id': user.id,
            'iat': now,
            'exp': now + self.refresh_token_lifetime,
            'type': 'refresh'
        }
        
        # Generate tokens
        access_token = jwt.encode(access_payload, self.secret_key, algorithm=self.algorithm)
        refresh_token = jwt.encode(refresh_payload, self.secret_key, algorithm=self.algorithm)
        
        # Store refresh token in cache for blacklisting capability
        cache.set(f"refresh_token:{user.id}:{refresh_token}", True, 
                 timeout=int(self.refresh_token_lifetime.total_seconds()))
        
        return {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'expires_in': int(self.access_token_lifetime.total_seconds()),
            'token_type': 'Bearer'
        }
    
    def validate_token(self, token: str, token_type: str = 'access') -> Optional[Dict]:
        """
        Validate a JWT token.
        
        Args:
            token: JWT token string
            token_type: Type of token ('access' or 'refresh')
            
        Returns:
            Token payload if valid, None if invalid
        """
        try:
            # Decode token
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Check token type
            if payload.get('type') != token_type:
                return None
            
            # Check if token is blacklisted (for refresh tokens)
            if token_type == 'refresh':
                cache_key = f"refresh_token:{payload['user_id']}:{token}"
                if not cache.get(cache_key):
                    return None
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Token validation error: {str(e)}")
            return None
    
    def refresh_access_token(self, refresh_token: str) -> Optional[Dict[str, str]]:
        """
        Generate new access token using refresh token.
        
        Args:
            refresh_token: Valid refresh token
            
        Returns:
            New tokens if successful, None if failed
        """
        # Validate refresh token
        payload = self.validate_token(refresh_token, 'refresh')
        if not payload:
            return None
        
        try:
            # Get user
            user = User.objects.get(id=payload['user_id'])
            
            # Generate new tokens
            return self.generate_tokens(user)
            
        except User.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"Token refresh error: {str(e)}")
            return None
    
    def blacklist_token(self, user_id: int, token: str) -> bool:
        """Blacklist a refresh token."""
        try:
            cache_key = f"refresh_token:{user_id}:{token}"
            cache.delete(cache_key)
            return True
        except Exception as e:
            logger.error(f"Error blacklisting token: {str(e)}")
            return False

class APIKeyAuthentication(BaseAuthentication):
    """DRF Authentication class for API keys."""
    
    def __init__(self):
        """Initialize API key authentication."""
        self.api_key_manager = APIKey()
    
    def authenticate(self, request):
        """Authenticate request using API key."""
        # Get API key from header
        api_key = request.META.get('HTTP_X_API_KEY')
        if not api_key:
            # Try Authorization header
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            if auth_header.startswith('ApiKey '):
                api_key = auth_header[7:]
        
        if not api_key:
            return None
        
        # Validate API key
        key_data = self.api_key_manager.validate_api_key(api_key)
        if not key_data:
            raise AuthenticationFailed('Invalid API key')
        
        # Get user
        try:
            user = User.objects.get(id=key_data['user_id'])
        except User.DoesNotExist:
            raise AuthenticationFailed('User not found')
        
        # Log authentication
        self._log_authentication(request, user, 'api_key', True)
        
        return (user, api_key)
    
    def _log_authentication(self, request, user, method, success):
        """Log authentication attempt."""
        try:
            client_ip = request.META.get('HTTP_X_FORWARDED_FOR', 
                                       request.META.get('REMOTE_ADDR', 'unknown'))
            if client_ip and ',' in client_ip:
                client_ip = client_ip.split(',')[0].strip()
            
            SecurityAuditLog.objects.create(
                event_type='api_authentication',
                user=user,
                username=user.username if user else 'unknown',
                ip_address=client_ip,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                request_path=request.path,
                request_method=request.method,
                success=success,
                details={
                    'authentication_method': method,
                    'api_endpoint': request.path
                }
            )
        except Exception as e:
            logger.error(f"Error logging authentication: {str(e)}")

class JWTAuthentication(BaseAuthentication):
    """DRF Authentication class for JWT tokens."""
    
    def __init__(self):
        """Initialize JWT authentication."""
        self.jwt_manager = JWTManager()
    
    def authenticate(self, request):
        """Authenticate request using JWT token."""
        # Get token from Authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if not auth_header.startswith('Bearer '):
            return None
        
        token = auth_header[7:]
        
        # Validate token
        payload = self.jwt_manager.validate_token(token, 'access')
        if not payload:
            raise AuthenticationFailed('Invalid or expired token')
        
        # Get user
        try:
            user = User.objects.get(id=payload['user_id'])
        except User.DoesNotExist:
            raise AuthenticationFailed('User not found')
        
        # Log authentication
        self._log_authentication(request, user, 'jwt', True)
        
        return (user, token)
    
    def _log_authentication(self, request, user, method, success):
        """Log authentication attempt."""
        try:
            client_ip = request.META.get('HTTP_X_FORWARDED_FOR', 
                                       request.META.get('REMOTE_ADDR', 'unknown'))
            if client_ip and ',' in client_ip:
                client_ip = client_ip.split(',')[0].strip()
            
            SecurityAuditLog.objects.create(
                event_type='api_authentication',
                user=user,
                username=user.username if user else 'unknown',
                ip_address=client_ip,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                request_path=request.path,
                request_method=request.method,
                success=success,
                details={
                    'authentication_method': method,
                    'api_endpoint': request.path
                }
            )
        except Exception as e:
            logger.error(f"Error logging authentication: {str(e)}")

class APIVersioning:
    """API versioning support."""
    
    SUPPORTED_VERSIONS = ['v1', 'v2']
    DEFAULT_VERSION = 'v1'
    
    @classmethod
    def get_version_from_request(cls, request) -> str:
        """Extract API version from request."""
        # Check URL path
        path_parts = request.path.strip('/').split('/')
        if path_parts and path_parts[0] in ['api']:
            if len(path_parts) > 1 and path_parts[1] in cls.SUPPORTED_VERSIONS:
                return path_parts[1]
        
        # Check Accept header
        accept_header = request.META.get('HTTP_ACCEPT', '')
        for version in cls.SUPPORTED_VERSIONS:
            if f'application/vnd.cvanalyzer.{version}+json' in accept_header:
                return version
        
        # Check custom header
        version_header = request.META.get('HTTP_API_VERSION')
        if version_header in cls.SUPPORTED_VERSIONS:
            return version_header
        
        return cls.DEFAULT_VERSION
    
    @classmethod
    def is_version_supported(cls, version: str) -> bool:
        """Check if API version is supported."""
        return version in cls.SUPPORTED_VERSIONS

# OAuth2 placeholder for future implementation
class OAuth2Handler:
    """OAuth2 authentication handler (placeholder for future implementation)."""
    
    def __init__(self):
        """Initialize OAuth2 handler."""
        self.client_id = getattr(settings, 'OAUTH2_CLIENT_ID', '')
        self.client_secret = getattr(settings, 'OAUTH2_CLIENT_SECRET', '')
        self.redirect_uri = getattr(settings, 'OAUTH2_REDIRECT_URI', '')
    
    def get_authorization_url(self) -> str:
        """Get OAuth2 authorization URL."""
        # Placeholder - implement OAuth2 flow
        return ""
    
    def exchange_code_for_token(self, code: str) -> Optional[Dict]:
        """Exchange authorization code for access token."""
        # Placeholder - implement token exchange
        return None 