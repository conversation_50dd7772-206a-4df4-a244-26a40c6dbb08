"""
Serializers for CV Analyzer API.
Implements data serialization and validation for REST API endpoints.
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from django.utils import timezone
from ..models import (
    CV, CVAnalysis, Company, Vacancy, ApplicantProfile,
    SecurityAuditLog, FileUploadLog
)
from ..validators import (
    ComplexPasswordValidator, UsernameValidator, EmailDomainValidator,
    CompanyNameValidator, JobTitleValidator
)

class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model."""
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'date_joined', 'is_active']
        read_only_fields = ['id', 'date_joined']
    
    def validate_username(self, value):
        """Validate username."""
        validator = UsernameValidator()
        validator(value)
        return value
    
    def validate_email(self, value):
        """Validate email."""
        validator = EmailDomainValidator()
        validator(value)
        return value

class CVSerializer(serializers.ModelSerializer):
    """Serializer for CV model."""
    
    file_size = serializers.SerializerMethodField()
    file_name = serializers.SerializerMethodField()
    
    class Meta:
        model = CV
        fields = [
            'id', 'file', 'file_size', 'file_name', 'status', 'source',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_file_size(self, obj):
        """Get file size if available."""
        try:
            return obj.file.size if obj.file else None
        except:
            return None
    
    def get_file_name(self, obj):
        """Get original filename."""
        try:
            return obj.file.name.split('/')[-1] if obj.file else None
        except:
            return None

class CVAnalysisSerializer(serializers.ModelSerializer):
    """Serializer for CV Analysis model."""
    
    cv = CVSerializer(read_only=True)
    analysis_details = serializers.SerializerMethodField()
    
    class Meta:
        model = CVAnalysis
        fields = [
            'id', 'cv', 'overall_score', 'content_score', 'format_score',
            'sections_score', 'skills_score', 'style_score', 'feedback',
            'recommendations', 'analysis_details', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_analysis_details(self, obj):
        """Get detailed analysis information."""
        return {
            'scores': {
                'overall': obj.overall_score,
                'content': obj.content_score,
                'format': obj.format_score,
                'sections': obj.sections_score,
                'skills': obj.skills_score,
                'style': obj.style_score
            },
            'grade': self._calculate_grade(obj.overall_score),
            'completion_status': obj.cv.status if obj.cv else 'unknown'
        }
    
    def _calculate_grade(self, score):
        """Calculate letter grade from numerical score."""
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B'
        elif score >= 70:
            return 'C'
        elif score >= 60:
            return 'D'
        else:
            return 'F'

class CompanySerializer(serializers.ModelSerializer):
    """Serializer for Company model."""
    
    class Meta:
        model = Company
        fields = [
            'id', 'name', 'description', 'website', 'location',
            'industry', 'size', 'logo', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
    
    def validate_name(self, value):
        """Validate company name."""
        validator = CompanyNameValidator()
        validator(value)
        return value

class VacancySerializer(serializers.ModelSerializer):
    """Serializer for Vacancy model."""
    
    company = CompanySerializer(read_only=True)
    company_id = serializers.IntegerField(write_only=True)
    application_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Vacancy
        fields = [
            'id', 'title', 'description', 'requirements', 'company',
            'company_id', 'location', 'salary_range', 'employment_type',
            'experience_level', 'status', 'application_count',
            'created_at', 'updated_at', 'deadline'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_title(self, value):
        """Validate job title."""
        validator = JobTitleValidator()
        validator(value)
        return value
    
    def get_application_count(self, obj):
        """Get number of applications for this vacancy."""
        # This would be implemented based on your application model
        return 0  # Placeholder

class ApplicantProfileSerializer(serializers.ModelSerializer):
    """Serializer for Applicant Profile model."""
    
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = ApplicantProfile
        fields = [
            'id', 'user', 'phone', 'location', 'summary',
            'skills', 'experience_years', 'education_level',
            'preferred_salary', 'availability', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

class SecurityAuditLogSerializer(serializers.ModelSerializer):
    """Serializer for Security Audit Log model."""
    
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = SecurityAuditLog
        fields = [
            'id', 'event_type', 'user', 'username', 'ip_address',
            'user_agent', 'request_path', 'request_method',
            'timestamp', 'success', 'details'
        ]
        read_only_fields = '__all__'

class FileUploadLogSerializer(serializers.ModelSerializer):
    """Serializer for File Upload Log model."""
    
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = FileUploadLog
        fields = [
            'id', 'filename', 'original_filename', 'file_size',
            'file_type', 'mime_type', 'md5_hash', 'sha256_hash',
            'upload_path', 'status', 'user', 'uploaded_at',
            'validation_results', 'virus_scan_results'
        ]
        read_only_fields = '__all__'

class TokenSerializer(serializers.Serializer):
    """Serializer for authentication tokens."""
    
    access_token = serializers.CharField()
    refresh_token = serializers.CharField()
    expires_in = serializers.IntegerField()
    token_type = serializers.CharField()

class LoginSerializer(serializers.Serializer):
    """Serializer for login requests."""
    
    username = serializers.CharField(max_length=150)
    password = serializers.CharField(write_only=True)
    
    def validate_username(self, value):
        """Validate username format."""
        if not value.strip():
            raise serializers.ValidationError("Username cannot be empty")
        return value.strip()
    
    def validate_password(self, value):
        """Validate password."""
        if len(value) < 8:
            raise serializers.ValidationError("Password must be at least 8 characters long")
        return value

class RefreshTokenSerializer(serializers.Serializer):
    """Serializer for token refresh requests."""
    
    refresh_token = serializers.CharField()

class APIKeySerializer(serializers.Serializer):
    """Serializer for API key generation requests."""
    
    name = serializers.CharField(max_length=100, required=False, allow_blank=True)
    expires_days = serializers.IntegerField(min_value=1, max_value=3650, default=365)

class CVUploadSerializer(serializers.Serializer):
    """Serializer for CV upload requests."""
    
    cv_file = serializers.FileField()
    vacancy_id = serializers.IntegerField(required=False)
    
    def validate_cv_file(self, value):
        """Validate uploaded CV file."""
        # Basic validation - detailed validation happens in the view
        if not value:
            raise serializers.ValidationError("No file provided")
        
        # Check file size (basic check)
        if value.size > 10 * 1024 * 1024:  # 10MB
            raise serializers.ValidationError("File too large. Maximum size is 10MB")
        
        # Check file extension
        allowed_extensions = ['.pdf', '.doc', '.docx']
        file_extension = value.name.lower().split('.')[-1] if '.' in value.name else ''
        if f'.{file_extension}' not in allowed_extensions:
            raise serializers.ValidationError(
                f"File type not allowed. Allowed types: {', '.join(allowed_extensions)}"
            )
        
        return value

class PasswordChangeSerializer(serializers.Serializer):
    """Serializer for password change requests."""
    
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True)
    confirm_password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        """Validate password change data."""
        new_password = attrs.get('new_password')
        confirm_password = attrs.get('confirm_password')
        
        if new_password != confirm_password:
            raise serializers.ValidationError("New passwords do not match")
        
        # Validate new password complexity
        validator = ComplexPasswordValidator()
        try:
            validator.validate(new_password)
        except Exception as e:
            raise serializers.ValidationError(f"Password validation failed: {str(e)}")
        
        return attrs

class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for user registration."""
    
    password = serializers.CharField(write_only=True)
    confirm_password = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = ['username', 'email', 'first_name', 'last_name', 'password', 'confirm_password']
    
    def validate_username(self, value):
        """Validate username."""
        validator = UsernameValidator()
        validator(value)
        
        # Check if username already exists
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError("Username already exists")
        
        return value
    
    def validate_email(self, value):
        """Validate email."""
        validator = EmailDomainValidator()
        validator(value)
        
        # Check if email already exists
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("Email already exists")
        
        return value
    
    def validate(self, attrs):
        """Validate registration data."""
        password = attrs.get('password')
        confirm_password = attrs.get('confirm_password')
        
        if password != confirm_password:
            raise serializers.ValidationError("Passwords do not match")
        
        # Validate password complexity
        validator = ComplexPasswordValidator()
        try:
            validator.validate(password)
        except Exception as e:
            raise serializers.ValidationError(f"Password validation failed: {str(e)}")
        
        return attrs
    
    def create(self, validated_data):
        """Create new user."""
        validated_data.pop('confirm_password')
        password = validated_data.pop('password')
        
        user = User.objects.create(**validated_data)
        user.set_password(password)
        user.save()
        
        return user

class HealthCheckSerializer(serializers.Serializer):
    """Serializer for health check responses."""
    
    status = serializers.CharField()
    timestamp = serializers.DateTimeField()
    version = serializers.CharField()
    checks = serializers.DictField()

class ErrorResponseSerializer(serializers.Serializer):
    """Serializer for error responses."""
    
    error = serializers.CharField()
    message = serializers.CharField(required=False)
    details = serializers.DictField(required=False)
    timestamp = serializers.DateTimeField(default=timezone.now)

class SuccessResponseSerializer(serializers.Serializer):
    """Serializer for success responses."""
    
    success = serializers.BooleanField(default=True)
    message = serializers.CharField()
    data = serializers.DictField(required=False)
    timestamp = serializers.DateTimeField(default=timezone.now) 