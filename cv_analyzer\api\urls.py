"""
URL configuration for CV Analyzer API.
Implements versioned API endpoints with proper routing.
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

# API v1 URLs
v1_urlpatterns = [
    # Authentication endpoints
    path('auth/login/', views.AuthenticationViewSet.as_view(), {'action': 'login'}, name='api-login'),
    path('auth/refresh/', views.AuthenticationViewSet.as_view(), {'action': 'refresh'}, name='api-refresh'),
    path('auth/logout/', views.AuthenticationViewSet.as_view(), {'action': 'logout'}, name='api-logout'),
    path('auth/api-key/', views.AuthenticationViewSet.as_view(), {'action': 'api-key'}, name='api-key-generate'),
    
    # CV management endpoints
    path('cv/upload/', views.CVUploadAPIView.as_view(), name='api-cv-upload'),
    path('cv/analysis/', views.CVAnalysisAPIView.as_view(), name='api-cv-analysis-list'),
    path('cv/analysis/<int:analysis_id>/', views.CVAnalysisAPIView.as_view(), name='api-cv-analysis-detail'),
    
    # Vacancy endpoints
    path('vacancies/', views.VacancyAPIView.as_view(), name='api-vacancies'),
    
    # Health check
    path('health/', views.HealthCheckAPIView.as_view(), name='api-health'),
    
    # Legacy endpoints for backwards compatibility
    path('upload/', views.upload_cv_legacy, name='api-upload-legacy'),
    path('analysis/<int:analysis_id>/', views.get_analysis_legacy, name='api-analysis-legacy'),
]

# API v2 URLs (future expansion)
v2_urlpatterns = [
    # V2 will have enhanced endpoints with improved response formats
    # For now, include v1 endpoints
    path('auth/login/', views.AuthenticationViewSet.as_view(), {'action': 'login'}, name='api-v2-login'),
    path('auth/refresh/', views.AuthenticationViewSet.as_view(), {'action': 'refresh'}, name='api-v2-refresh'),
    path('auth/logout/', views.AuthenticationViewSet.as_view(), {'action': 'logout'}, name='api-v2-logout'),
    path('auth/api-key/', views.AuthenticationViewSet.as_view(), {'action': 'api-key'}, name='api-v2-key-generate'),
    
    path('cv/upload/', views.CVUploadAPIView.as_view(), name='api-v2-cv-upload'),
    path('cv/analysis/', views.CVAnalysisAPIView.as_view(), name='api-v2-cv-analysis-list'),
    path('cv/analysis/<int:analysis_id>/', views.CVAnalysisAPIView.as_view(), name='api-v2-cv-analysis-detail'),
    
    path('vacancies/', views.VacancyAPIView.as_view(), name='api-v2-vacancies'),
    path('health/', views.HealthCheckAPIView.as_view(), name='api-v2-health'),
]

# Main API URL patterns
urlpatterns = [
    # Versioned API endpoints
    path('v1/', include(v1_urlpatterns)),
    path('v2/', include(v2_urlpatterns)),
    
    # Default to v1 for backwards compatibility
    path('', include(v1_urlpatterns)),

    # Custom Scoring API endpoints
    path('calculate-custom-score/', views.calculate_custom_score, name='calculate_custom_score'),
    path('custom-scores/', views.get_custom_scores, name='get_custom_scores'),
    path('scoring-templates/', views.get_scoring_templates, name='get_scoring_templates'),
    path('apply-scoring-template/', views.apply_scoring_template, name='apply_scoring_template'),
] 