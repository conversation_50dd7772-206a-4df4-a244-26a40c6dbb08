"""
API Views for CV Analyzer application.
Implements secure REST API endpoints with authentication and validation.
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON>Part<PERSON>ars<PERSON>, FormParser, JSONParser
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.conf import settings
import logging
import time

from .authentication import (
    APIKeyAuthentication, 
    JWTAuthentication, 
    APIKey, 
    JWTManager,
    APIVersioning
)
from ..security import FileUploadValidator, FileQuarantine, InputValidator
from ..models import (
    CV, CVAnalysis, SecurityAuditLog, FileUploadLog, 
    Company, Vacancy, ApplicantProfile, CustomScoringResult, ScoringTemplate
)
from .serializers import (
    CVSerializer, CVAnalysisSerializer, UserSerializer,
    CompanySerializer, VacancySerializer, TokenSerializer
)

logger = logging.getLogger(__name__)

class AuthenticationViewSet(APIView):
    """Authentication endpoints for API access."""
    
    permission_classes = [permissions.AllowAny]
    
    def post(self, request, *args, **kwargs):
        """Handle authentication requests."""
        action = kwargs.get('action', 'login')
        
        if action == 'login':
            return self._handle_login(request)
        elif action == 'refresh':
            return self._handle_token_refresh(request)
        elif action == 'logout':
            return self._handle_logout(request)
        elif action == 'api-key':
            return self._handle_api_key_generation(request)
        else:
            return Response(
                {'error': 'Invalid action'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def _handle_login(self, request):
        """Handle user login and token generation."""
        try:
            username = request.data.get('username')
            password = request.data.get('password')
            
            if not username or not password:
                return Response(
                    {'error': 'Username and password are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Validate input
            try:
                username = InputValidator.sanitize_string(username, 150)
            except Exception:
                return Response(
                    {'error': 'Invalid username format'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Authenticate user
            user = authenticate(username=username, password=password)
            if not user:
                self._log_failed_login(request, username)
                return Response(
                    {'error': 'Invalid credentials'},
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            if not user.is_active:
                return Response(
                    {'error': 'Account is disabled'},
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            # Generate tokens
            jwt_manager = JWTManager()
            tokens = jwt_manager.generate_tokens(user)
            
            # Log successful login
            self._log_successful_login(request, user)
            
            # Return tokens and user info
            return Response({
                'access_token': tokens['access_token'],
                'refresh_token': tokens['refresh_token'],
                'expires_in': tokens['expires_in'],
                'token_type': tokens['token_type'],
                'user': UserSerializer(user).data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return Response(
                {'error': 'Authentication failed'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _handle_token_refresh(self, request):
        """Handle token refresh."""
        try:
            refresh_token = request.data.get('refresh_token')
            if not refresh_token:
                return Response(
                    {'error': 'Refresh token is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            jwt_manager = JWTManager()
            tokens = jwt_manager.refresh_access_token(refresh_token)
            
            if not tokens:
                return Response(
                    {'error': 'Invalid refresh token'},
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            return Response(tokens, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Token refresh error: {str(e)}")
            return Response(
                {'error': 'Token refresh failed'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _handle_logout(self, request):
        """Handle user logout."""
        try:
            refresh_token = request.data.get('refresh_token')
            if refresh_token and hasattr(request, 'user') and request.user.is_authenticated:
                jwt_manager = JWTManager()
                jwt_manager.blacklist_token(request.user.id, refresh_token)
            
            return Response(
                {'message': 'Logged out successfully'},
                status=status.HTTP_200_OK
            )
            
        except Exception as e:
            logger.error(f"Logout error: {str(e)}")
            return Response(
                {'error': 'Logout failed'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _handle_api_key_generation(self, request):
        """Handle API key generation."""
        try:
            # This endpoint requires authentication
            if not hasattr(request, 'user') or not request.user.is_authenticated:
                return Response(
                    {'error': 'Authentication required'},
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            name = request.data.get('name', '')
            expires_days = int(request.data.get('expires_days', 365))
            
            # Validate expires_days
            if expires_days < 1 or expires_days > 3650:  # Max 10 years
                return Response(
                    {'error': 'expires_days must be between 1 and 3650'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            api_key_manager = APIKey()
            api_key_data = api_key_manager.generate_api_key(
                user_id=request.user.id,
                name=name,
                expires_days=expires_days
            )
            
            # Log API key generation
            self._log_api_key_generation(request, api_key_data)
            
            return Response(api_key_data, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"API key generation error: {str(e)}")
            return Response(
                {'error': 'API key generation failed'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _log_failed_login(self, request, username):
        """Log failed login attempt."""
        client_ip = self._get_client_ip(request)
        SecurityAuditLog.objects.create(
            event_type='login_failure',
            username=username,
            ip_address=client_ip,
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            request_path=request.path,
            request_method=request.method,
            success=False
        )
    
    def _log_successful_login(self, request, user):
        """Log successful login."""
        client_ip = self._get_client_ip(request)
        SecurityAuditLog.objects.create(
            event_type='login_success',
            user=user,
            username=user.username,
            ip_address=client_ip,
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            request_path=request.path,
            request_method=request.method,
            success=True
        )
    
    def _log_api_key_generation(self, request, api_key_data):
        """Log API key generation."""
        client_ip = self._get_client_ip(request)
        SecurityAuditLog.objects.create(
            event_type='api_key_generated',
            user=request.user,
            username=request.user.username,
            ip_address=client_ip,
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            request_path=request.path,
            request_method=request.method,
            success=True,
            details={
                'key_id': api_key_data.get('key_id'),
                'expires_at': api_key_data.get('expires_at')
            }
        )
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        client_ip = request.META.get('HTTP_X_FORWARDED_FOR', 
                                   request.META.get('REMOTE_ADDR', 'unknown'))
        if client_ip and ',' in client_ip:
            client_ip = client_ip.split(',')[0].strip()
        return client_ip

class CVUploadAPIView(APIView):
    """Secure CV upload API endpoint."""
    
    authentication_classes = [JWTAuthentication, APIKeyAuthentication]
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    
    def post(self, request, *args, **kwargs):
        """Handle CV file upload with comprehensive security validation."""
        try:
            # Get API version
            api_version = APIVersioning.get_version_from_request(request)
            
            # Get uploaded file
            cv_file = request.FILES.get('cv_file')
            if not cv_file:
                return Response(
                    {'error': 'No file uploaded'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get client info for logging
            client_ip = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            
            # Initialize security components
            validator = FileUploadValidator()
            quarantine = FileQuarantine()
            
            # Comprehensive file validation
            validation_results = validator.validate_file(cv_file)
            
            # Create upload log entry
            upload_log = FileUploadLog.objects.create(
                filename=cv_file.name,
                original_filename=cv_file.name,
                file_size=cv_file.size,
                file_type=cv_file.content_type,
                mime_type=validation_results.get('file_info', {}).get('mime_type', ''),
                md5_hash=validation_results.get('file_info', {}).get('md5', ''),
                sha256_hash=validation_results.get('file_info', {}).get('sha256', ''),
                upload_path='',
                status='uploaded',
                user=request.user,
                ip_address=client_ip,
                user_agent=user_agent,
                validation_results=validation_results,
            )
            
            # Check validation results
            if not validation_results['is_valid']:
                # Quarantine the file
                quarantine_path = quarantine.quarantine_file(
                    cv_file, 
                    f"Validation failed: {', '.join(validation_results['errors'])}"
                )
                
                # Update log
                upload_log.status = 'quarantined'
                upload_log.quarantine_path = quarantine_path
                upload_log.quarantine_reason = '; '.join(validation_results['errors'])
                upload_log.save()
                
                return Response({
                    'error': 'File validation failed',
                    'details': validation_results['errors'],
                    'upload_id': upload_log.id
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Save the file securely
            safe_filename = f"{request.user.id}_{int(time.time())}_{cv_file.name}"
            file_path = default_storage.save(
                f'cvs/{request.user.id}/{safe_filename}',
                ContentFile(cv_file.read())
            )
            
            # Update upload log
            upload_log.upload_path = file_path
            upload_log.status = 'validated'
            upload_log.validated_at = timezone.now()
            upload_log.save()
            
            # Create CV record
            cv = CV.objects.create(
                file=file_path,
                status='incomplete',
                source='api',
                applicant_profile=getattr(request.user, 'applicantprofile', None)
            )
            
            # Create CV analysis entry
            analysis = CVAnalysis.objects.create(
                cv=cv,
                overall_score=0,
                content_score=0,
                format_score=0,
                sections_score=0,
                skills_score=0,
                style_score=0
            )
            
            # Log successful upload
            SecurityAuditLog.objects.create(
                event_type='file_upload',
                user=request.user,
                username=request.user.username,
                ip_address=client_ip,
                user_agent=user_agent,
                request_path=request.path,
                request_method=request.method,
                success=True,
                details={
                    'filename': cv_file.name,
                    'file_path': file_path,
                    'cv_id': cv.id,
                    'analysis_id': analysis.id,
                    'api_version': api_version
                }
            )
            
            # Return response based on API version
            if api_version == 'v2':
                return Response({
                    'success': True,
                    'message': 'CV uploaded successfully',
                    'data': {
                        'cv_id': cv.id,
                        'analysis_id': analysis.id,
                        'upload_id': upload_log.id,
                        'file_info': validation_results['file_info'],
                        'status': 'processing'
                    }
                }, status=status.HTTP_201_CREATED)
            else:
                # v1 response format
                return Response({
                    'cv_id': cv.id,
                    'analysis_id': analysis.id,
                    'message': 'Upload successful',
                    'file_info': validation_results['file_info']
                }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"CV upload error for user {request.user.username}: {str(e)}")
            
            # Log error
            SecurityAuditLog.objects.create(
                event_type='file_upload',
                user=request.user,
                username=request.user.username,
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                request_path=request.path,
                request_method=request.method,
                success=False,
                details={'error': str(e)}
            )
            
            return Response(
                {'error': 'Upload failed due to server error'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        client_ip = request.META.get('HTTP_X_FORWARDED_FOR', 
                                   request.META.get('REMOTE_ADDR', 'unknown'))
        if client_ip and ',' in client_ip:
            client_ip = client_ip.split(',')[0].strip()
        return client_ip

class CVAnalysisAPIView(APIView):
    """CV analysis retrieval API endpoint."""
    
    authentication_classes = [JWTAuthentication, APIKeyAuthentication]
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, analysis_id=None, *args, **kwargs):
        """Get CV analysis results."""
        try:
            if analysis_id:
                # Get specific analysis
                try:
                    analysis = CVAnalysis.objects.get(
                        id=analysis_id,
                        cv__applicant_profile__user=request.user
                    )
                    serializer = CVAnalysisSerializer(analysis)
                    return Response(serializer.data, status=status.HTTP_200_OK)
                except CVAnalysis.DoesNotExist:
                    return Response(
                        {'error': 'Analysis not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )
            else:
                # Get all analyses for user
                analyses = CVAnalysis.objects.filter(
                    cv__applicant_profile__user=request.user
                ).order_by('-created_at')
                
                serializer = CVAnalysisSerializer(analyses, many=True)
                return Response(serializer.data, status=status.HTTP_200_OK)
                
        except Exception as e:
            logger.error(f"CV analysis retrieval error: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve analysis'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class VacancyAPIView(APIView):
    """Vacancy management API endpoint."""
    
    authentication_classes = [JWTAuthentication, APIKeyAuthentication]
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """Get active vacancies."""
        try:
            vacancies = Vacancy.objects.filter(
                status='active'
            ).select_related('company').order_by('-created_at')
            
            serializer = VacancySerializer(vacancies, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Vacancy retrieval error: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve vacancies'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class HealthCheckAPIView(APIView):
    """Health check endpoint for monitoring."""
    
    permission_classes = [permissions.AllowAny]
    
    def get(self, request, *args, **kwargs):
        """Return system health status."""
        try:
            # Basic health checks
            health_status = {
                'status': 'healthy',
                'timestamp': timezone.now().isoformat(),
                'version': getattr(settings, 'APP_VERSION', '1.0.0'),
                'checks': {
                    'database': self._check_database(),
                    'cache': self._check_cache(),
                    'storage': self._check_storage(),
                }
            }
            
            # Determine overall status
            if not all(health_status['checks'].values()):
                health_status['status'] = 'unhealthy'
                response_status = status.HTTP_503_SERVICE_UNAVAILABLE
            else:
                response_status = status.HTTP_200_OK
            
            return Response(health_status, status=response_status)
            
        except Exception as e:
            logger.error(f"Health check error: {str(e)}")
            return Response({
                'status': 'error',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _check_database(self):
        """Check database connectivity."""
        try:
            User.objects.first()
            return True
        except Exception:
            return False
    
    def _check_cache(self):
        """Check cache connectivity."""
        try:
            from django.core.cache import cache
            cache.set('health_check', 'ok', 60)
            return cache.get('health_check') == 'ok'
        except Exception:
            return False
    
    def _check_storage(self):
        """Check storage accessibility."""
        try:
            test_file = ContentFile(b'health_check')
            file_path = default_storage.save('health_check.txt', test_file)
            default_storage.delete(file_path)
            return True
        except Exception:
            return False

# API endpoints for backwards compatibility
@api_view(['POST'])
@authentication_classes([JWTAuthentication, APIKeyAuthentication])
@permission_classes([permissions.IsAuthenticated])
def upload_cv_legacy(request):
    """Legacy CV upload endpoint for v1 compatibility."""
    view = CVUploadAPIView()
    view.request = request
    view.format_kwarg = None
    return view.post(request)

@api_view(['GET'])
@authentication_classes([JWTAuthentication, APIKeyAuthentication])
@permission_classes([permissions.IsAuthenticated])
def get_analysis_legacy(request, analysis_id):
    """Legacy analysis retrieval endpoint for v1 compatibility."""
    view = CVAnalysisAPIView()
    view.request = request
    view.format_kwarg = None
    return view.get(request, analysis_id)

# Custom Scoring API Views
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def calculate_custom_score(request):
    """
    Calculate custom weighted score for a CV against a vacancy
    
    POST /api/calculate-custom-score/
    {
        "cv_id": 1,
        "vacancy_id": 2
    }
    """
    try:
        cv_id = request.data.get('cv_id')
        vacancy_id = request.data.get('vacancy_id')
        
        if not cv_id or not vacancy_id:
            return Response(
                {'error': 'cv_id and vacancy_id are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get objects
        try:
            cv_analysis = CVAnalysis.objects.select_related('cv').get(cv__id=cv_id)
            vacancy = Vacancy.objects.get(id=vacancy_id)
        except CVAnalysis.DoesNotExist:
            return Response(
                {'error': 'CV analysis not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Vacancy.DoesNotExist:
            return Response(
                {'error': 'Vacancy not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Calculate custom score
        from cv_analyzer.custom_scoring import CustomScoringEngine
        engine = CustomScoringEngine()
        result = engine.calculate_custom_score(cv_analysis, vacancy)
        
        return Response({
            'success': True,
            'custom_score': {
                'final_score': result.final_custom_score,
                'education_score': result.education_score,
                'experience_score': result.experience_score,
                'skills_score': result.skills_score,
                'responsibilities_score': result.responsibilities_score,
                'recommendation': result.custom_recommendation,
                'meets_requirements': result.meets_minimum_requirements,
                'ai_vs_custom_difference': result.ai_vs_custom_difference,
                'score_breakdown': result.score_breakdown
            }
        })
        
    except Exception as e:
        logger.error(f"Error calculating custom score: {str(e)}")
        return Response(
            {'error': 'Failed to calculate custom score'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_custom_scores(request):
    """
    Get custom scores for a CV or vacancy
    
    GET /api/custom-scores/?cv_id=1
    GET /api/custom-scores/?vacancy_id=2
    """
    try:
        cv_id = request.query_params.get('cv_id')
        vacancy_id = request.query_params.get('vacancy_id')
        
        if cv_id:
            # Get scores for a specific CV
            custom_scores = CustomScoringResult.objects.filter(
                cv_analysis__cv__id=cv_id
            ).select_related('cv_analysis', 'vacancy')
        elif vacancy_id:
            # Get scores for a specific vacancy
            custom_scores = CustomScoringResult.objects.filter(
                vacancy__id=vacancy_id
            ).select_related('cv_analysis', 'vacancy')
        else:
            return Response(
                {'error': 'Either cv_id or vacancy_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        results = []
        for score in custom_scores:
            results.append({
                'id': score.id,
                'cv_id': score.cv_analysis.cv.id,
                'vacancy_id': score.vacancy.id,
                'vacancy_title': score.vacancy.title,
                'final_score': score.final_custom_score,
                'education_score': score.education_score,
                'experience_score': score.experience_score,
                'skills_score': score.skills_score,
                'responsibilities_score': score.responsibilities_score,
                'recommendation': score.custom_recommendation,
                'meets_requirements': score.meets_minimum_requirements,
                'ai_vs_custom_difference': score.ai_vs_custom_difference,
                'created_at': score.created_at.isoformat()
            })
        
        return Response({
            'success': True,
            'custom_scores': results,
            'total': len(results)
        })
        
    except Exception as e:
        logger.error(f"Error fetching custom scores: {str(e)}")
        return Response(
            {'error': 'Failed to fetch custom scores'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_scoring_templates(request):
    """
    Get available scoring templates
    
    GET /api/scoring-templates/
    """
    try:
        templates = ScoringTemplate.objects.filter(is_active=True)
        results = []
        
        for template in templates:
            results.append({
                'id': template.id,
                'name': template.name,
                'category': template.category,
                'description': template.description,
                'education_weight': template.education_weight,
                'experience_weight': template.experience_weight,
                'skills_weight': template.skills_weight,
                'responsibilities_weight': template.responsibilities_weight,
                'is_default': template.is_default
            })
        
        return Response({
            'success': True,
            'templates': results
        })
        
    except Exception as e:
        logger.error(f"Error fetching scoring templates: {str(e)}")
        return Response(
            {'error': 'Failed to fetch scoring templates'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def apply_scoring_template(request):
    """
    Apply a scoring template to a vacancy
    
    POST /api/apply-scoring-template/
    {
        "vacancy_id": 1,
        "template_id": 2
    }
    """
    try:
        vacancy_id = request.data.get('vacancy_id')
        template_id = request.data.get('template_id')
        
        if not vacancy_id or not template_id:
            return Response(
                {'error': 'vacancy_id and template_id are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get objects
        try:
            vacancy = Vacancy.objects.get(id=vacancy_id)
            template = ScoringTemplate.objects.get(id=template_id)
        except (Vacancy.DoesNotExist, ScoringTemplate.DoesNotExist):
            return Response(
                {'error': 'Vacancy or template not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Apply template to vacancy
        from cv_analyzer.models import VacancyScoringRules
        scoring_rules, created = VacancyScoringRules.objects.get_or_create(
            vacancy=vacancy,
            defaults={
                'education_weight': template.education_weight,
                'experience_weight': template.experience_weight,
                'skills_weight': template.skills_weight,
                'responsibilities_weight': template.responsibilities_weight,
                'education_requirements': template.education_requirements,
                'experience_requirements': template.experience_requirements,
                'skills_requirements': template.skills_requirements,
                'responsibilities_requirements': template.responsibilities_requirements,
            }
        )
        
        if not created:
            # Update existing rules
            scoring_rules.education_weight = template.education_weight
            scoring_rules.experience_weight = template.experience_weight
            scoring_rules.skills_weight = template.skills_weight
            scoring_rules.responsibilities_weight = template.responsibilities_weight
            scoring_rules.education_requirements = template.education_requirements
            scoring_rules.experience_requirements = template.experience_requirements
            scoring_rules.skills_requirements = template.skills_requirements
            scoring_rules.responsibilities_requirements = template.responsibilities_requirements
            scoring_rules.save()
        
        return Response({
            'success': True,
            'message': f'Template "{template.name}" applied to vacancy "{vacancy.title}"',
            'scoring_rules_id': scoring_rules.id
        })
        
    except Exception as e:
        logger.error(f"Error applying scoring template: {str(e)}")
        return Response(
            {'error': 'Failed to apply scoring template'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )