"""
API Enhancement System
Implements Django REST Framework with advanced features
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.db.models import QuerySet, Q
from django.conf import settings
from django.utils import timezone
from django.core.paginator import Paginator
from django.http import JsonResponse
from rest_framework import serializers, viewsets, filters, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.pagination import PageNumberPagination
from rest_framework.throttling import UserRateThrottle, AnonRateThrottle
from rest_framework.versioning import AcceptHeaderVersioning
from rest_framework.authentication import TokenAuthentication, SessionAuthentication
from django_filters.rest_framework import DjangoFilterBackend, FilterSet, CharFilter, DateTimeFilter
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiResponse
from drf_spectacular.openapi import AutoSchema
import logging

logger = logging.getLogger(__name__)

class CVAnalyzerPagination(PageNumberPagination):
    """Custom pagination for CV Analyzer API"""
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100
    
    def get_paginated_response(self, data):
        return Response({
            'links': {
                'next': self.get_next_link(),
                'previous': self.get_previous_link()
            },
            'count': self.page.paginator.count,
            'page_size': self.page_size,
            'current_page': self.page.number,
            'total_pages': self.page.paginator.num_pages,
            'results': data
        })

class APIRateLimiting:
    """Enhanced API rate limiting"""
    
    class BurstRateThrottle(UserRateThrottle):
        scope = 'burst'
        rate = '60/min'
    
    class SustainedRateThrottle(UserRateThrottle):
        scope = 'sustained'
        rate = '1000/day'
    
    class UploadRateThrottle(UserRateThrottle):
        scope = 'upload'
        rate = '10/hour'
    
    class AnonBurstRateThrottle(AnonRateThrottle):
        scope = 'anon_burst'
        rate = '20/min'

class APIVersioning(AcceptHeaderVersioning):
    """API versioning with Accept header"""
    allowed_versions = ['1.0', '1.1', '2.0']
    version_param = 'version'
    default_version = '1.0'

# Serializers
class BaseModelSerializer(serializers.ModelSerializer):
    """Base serializer with common fields and methods"""
    
    created_at = serializers.DateTimeField(read_only=True)
    updated_at = serializers.DateTimeField(read_only=True)
    
    class Meta:
        abstract = True
    
    def to_representation(self, instance):
        """Add API version info to response"""
        data = super().to_representation(instance)
        request = self.context.get('request')
        if request:
            data['_meta'] = {
                'api_version': getattr(request, 'version', '1.0'),
                'timestamp': timezone.now().isoformat()
            }
        return data

class CVSerializer(BaseModelSerializer):
    """Serializer for CV model"""
    
    file_url = serializers.SerializerMethodField()
    analysis_status = serializers.SerializerMethodField()
    file_size_mb = serializers.SerializerMethodField()
    
    class Meta:
        from .models import CV
        model = CV
        fields = [
            'id', 'file_name', 'file_url', 'file_size_mb', 'uploaded_at', 
            'status', 'analysis_status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'uploaded_at', 'created_at', 'updated_at']
    
    def get_file_url(self, obj):
        """Get secure file URL"""
        request = self.context.get('request')
        if obj.file and hasattr(obj.file, 'url'):
            if request:
                return request.build_absolute_uri(obj.file.url)
            return obj.file.url
        return None
    
    def get_analysis_status(self, obj):
        """Get analysis status with details"""
        from .models import AnalysisResult
        try:
            latest_analysis = AnalysisResult.objects.filter(cv=obj).latest('created_at')
            return {
                'status': latest_analysis.status,
                'completed_at': latest_analysis.completed_at,
                'confidence_score': latest_analysis.confidence_score
            }
        except AnalysisResult.DoesNotExist:
            return {'status': 'pending', 'completed_at': None, 'confidence_score': None}
    
    def get_file_size_mb(self, obj):
        """Get file size in MB"""
        if obj.file:
            return round(obj.file.size / (1024 * 1024), 2)
        return None

class VacancySerializer(BaseModelSerializer):
    """Serializer for Vacancy model"""
    
    match_count = serializers.SerializerMethodField()
    
    class Meta:
        from .models import Vacancy
        model = Vacancy
        fields = [
            'id', 'title', 'company', 'description', 'requirements',
            'location', 'salary_range', 'status', 'match_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_match_count(self, obj):
        """Get number of matching CVs"""
        from .models import AnalysisResult
        return AnalysisResult.objects.filter(
            vacancy=obj, 
            confidence_score__gte=70
        ).count()

class AnalysisResultSerializer(BaseModelSerializer):
    """Serializer for Analysis Result model"""
    
    cv_details = CVSerializer(source='cv', read_only=True)
    vacancy_details = VacancySerializer(source='vacancy', read_only=True)
    
    class Meta:
        from .models import AnalysisResult
        model = AnalysisResult
        fields = [
            'id', 'cv', 'vacancy', 'cv_details', 'vacancy_details',
            'confidence_score', 'match_details', 'status',
            'analysis_duration', 'completed_at', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

# Filters
class CVFilter(FilterSet):
    """Filter for CV queries"""
    
    uploaded_after = DateTimeFilter(field_name='uploaded_at', lookup_expr='gte')
    uploaded_before = DateTimeFilter(field_name='uploaded_at', lookup_expr='lte')
    status = CharFilter(field_name='status', lookup_expr='iexact')
    file_name = CharFilter(field_name='file_name', lookup_expr='icontains')
    
    class Meta:
        from .models import CV
        model = CV
        fields = ['status', 'uploaded_after', 'uploaded_before', 'file_name']

class VacancyFilter(FilterSet):
    """Filter for Vacancy queries"""
    
    title = CharFilter(field_name='title', lookup_expr='icontains')
    company = CharFilter(field_name='company', lookup_expr='icontains')
    location = CharFilter(field_name='location', lookup_expr='icontains')
    status = CharFilter(field_name='status', lookup_expr='iexact')
    created_after = DateTimeFilter(field_name='created_at', lookup_expr='gte')
    created_before = DateTimeFilter(field_name='created_at', lookup_expr='lte')
    
    class Meta:
        from .models import Vacancy
        model = Vacancy
        fields = ['title', 'company', 'location', 'status', 'created_after', 'created_before']

class AnalysisResultFilter(FilterSet):
    """Filter for Analysis Result queries"""
    
    min_confidence = CharFilter(field_name='confidence_score', lookup_expr='gte')
    max_confidence = CharFilter(field_name='confidence_score', lookup_expr='lte')
    status = CharFilter(field_name='status', lookup_expr='iexact')
    completed_after = DateTimeFilter(field_name='completed_at', lookup_expr='gte')
    completed_before = DateTimeFilter(field_name='completed_at', lookup_expr='lte')
    
    class Meta:
        from .models import AnalysisResult
        model = AnalysisResult
        fields = ['min_confidence', 'max_confidence', 'status', 'completed_after', 'completed_before']

# ViewSets
class CVViewSet(viewsets.ModelViewSet):
    """API ViewSet for CV management"""
    
    serializer_class = CVSerializer
    pagination_class = CVAnalyzerPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = CVFilter
    search_fields = ['file_name', 'status']
    ordering_fields = ['uploaded_at', 'file_name', 'status']
    ordering = ['-uploaded_at']
    
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]
    throttle_classes = [APIRateLimiting.BurstRateThrottle, APIRateLimiting.SustainedRateThrottle]
    
    def get_queryset(self):
        """Get CVs for current user"""
        from .models import CV
        if self.request.user.is_staff:
            return CV.objects.all()
        return CV.objects.filter(applicant_profile__user=self.request.user)
    
    @extend_schema(
        summary="Upload new CV",
        description="Upload a new CV file for analysis",
        request=CVSerializer,
        responses={201: CVSerializer, 400: OpenApiResponse(description="Invalid file")}
    )
    def create(self, request, *args, **kwargs):
        """Handle CV upload with validation"""
        try:
            from .file_upload import validate_cv_file
            from .error_handling import FileUploadError, log_info
            
            file = request.FILES.get('file')
            if not file:
                raise FileUploadError("No file provided")
            
            # Validate file
            validation_result = validate_cv_file(file)
            if not validation_result['valid']:
                raise FileUploadError(
                    f"File validation failed: {validation_result['error']}",
                    {'file_name': file.name, 'file_size': file.size}
                )
            
            log_info("CV upload started", {
                'user_id': request.user.id,
                'file_name': file.name,
                'file_size': file.size
            })
            
            return super().create(request, *args, **kwargs)
            
        except FileUploadError as e:
            return Response(e.to_dict(), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            from .error_handling import track_error
            error_id = track_error(e, request, request.user.id)
            return Response({
                'error': 'Upload failed',
                'error_id': error_id
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @extend_schema(
        summary="Get CV analysis results",
        description="Get analysis results for a specific CV",
        responses={200: AnalysisResultSerializer(many=True)}
    )
    @action(detail=True, methods=['get'])
    def analysis_results(self, request, pk=None):
        """Get analysis results for CV"""
        cv = self.get_object()
        from .models import AnalysisResult
        
        results = AnalysisResult.objects.filter(cv=cv).order_by('-created_at')
        serializer = AnalysisResultSerializer(results, many=True, context={'request': request})
        return Response(serializer.data)
    
    @extend_schema(
        summary="Trigger CV analysis",
        description="Trigger analysis for a specific CV against a vacancy",
        request={'vacancy_id': 'integer'},
        responses={202: OpenApiResponse(description="Analysis started")}
    )
    @action(detail=True, methods=['post'])
    def analyze(self, request, pk=None):
        """Trigger CV analysis"""
        cv = self.get_object()
        vacancy_id = request.data.get('vacancy_id')
        
        if not vacancy_id:
            return Response({
                'error': 'vacancy_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            from .models import Vacancy
            from .tasks import analyze_cv_task
            
            vacancy = Vacancy.objects.get(id=vacancy_id)
            
            # Trigger analysis task
            task = analyze_cv_task.delay(cv.id, vacancy.id)
            
            return Response({
                'message': 'Analysis started',
                'task_id': task.id,
                'cv_id': cv.id,
                'vacancy_id': vacancy.id
            }, status=status.HTTP_202_ACCEPTED)
            
        except Vacancy.DoesNotExist:
            return Response({
                'error': 'Vacancy not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            from .error_handling import track_error
            error_id = track_error(e, request, request.user.id)
            return Response({
                'error': 'Analysis failed to start',
                'error_id': error_id
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class VacancyViewSet(viewsets.ModelViewSet):
    """API ViewSet for Vacancy management"""
    
    serializer_class = VacancySerializer
    pagination_class = CVAnalyzerPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = VacancyFilter
    search_fields = ['title', 'company', 'description', 'location']
    ordering_fields = ['created_at', 'title', 'company']
    ordering = ['-created_at']
    
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]
    throttle_classes = [APIRateLimiting.BurstRateThrottle, APIRateLimiting.SustainedRateThrottle]
    
    def get_queryset(self):
        """Get vacancies based on user permissions"""
        from .models import Vacancy
        if self.request.user.is_staff:
            return Vacancy.objects.all()
        return Vacancy.objects.filter(status='active')
    
    @extend_schema(
        summary="Get matching CVs for vacancy",
        description="Get CVs that match this vacancy based on analysis results",
        parameters=[
            OpenApiParameter('min_confidence', int, description='Minimum confidence score'),
            OpenApiParameter('limit', int, description='Maximum number of results')
        ],
        responses={200: CVSerializer(many=True)}
    )
    @action(detail=True, methods=['get'])
    def matching_cvs(self, request, pk=None):
        """Get matching CVs for vacancy"""
        vacancy = self.get_object()
        min_confidence = int(request.query_params.get('min_confidence', 70))
        limit = int(request.query_params.get('limit', 20))
        
        from .models import AnalysisResult
        
        # Get matching analysis results
        matching_results = AnalysisResult.objects.filter(
            vacancy=vacancy,
            confidence_score__gte=min_confidence,
            status='completed'
        ).order_by('-confidence_score')[:limit]
        
        # Extract CVs
        cvs = [result.cv for result in matching_results]
        serializer = CVSerializer(cvs, many=True, context={'request': request})
        
        return Response({
            'vacancy_id': vacancy.id,
            'min_confidence': min_confidence,
            'total_matches': len(cvs),
            'results': serializer.data
        })

class AnalysisResultViewSet(viewsets.ReadOnlyModelViewSet):
    """API ViewSet for Analysis Results (read-only)"""
    
    serializer_class = AnalysisResultSerializer
    pagination_class = CVAnalyzerPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_class = AnalysisResultFilter
    ordering_fields = ['created_at', 'confidence_score', 'completed_at']
    ordering = ['-created_at']
    
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]
    throttle_classes = [APIRateLimiting.BurstRateThrottle, APIRateLimiting.SustainedRateThrottle]
    
    def get_queryset(self):
        """Get analysis results based on user permissions"""
        from .models import AnalysisResult
        if self.request.user.is_staff:
            return AnalysisResult.objects.all()
        return AnalysisResult.objects.filter(cv__applicant_profile__user=self.request.user)

# API Status and Health endpoints
@extend_schema(
    summary="API Status",
    description="Get API status and version information",
    responses={200: {
        'type': 'object',
        'properties': {
            'status': {'type': 'string'},
            'version': {'type': 'string'},
            'timestamp': {'type': 'string'}
        }
    }}
)
@api_view(['GET'])
@permission_classes([])
def api_status(request):
    """Get API status"""
    return Response({
        'status': 'operational',
        'version': getattr(request, 'version', '1.0'),
        'timestamp': timezone.now().isoformat(),
        'features': {
            'cv_upload': True,
            'ai_analysis': True,
            'vacancy_matching': True,
            'real_time_monitoring': True
        }
    })

@extend_schema(
    summary="API Health Check",
    description="Comprehensive API health check including dependencies",
    responses={200: {
        'type': 'object',
        'properties': {
            'status': {'type': 'string'},
            'checks': {'type': 'object'},
            'timestamp': {'type': 'string'}
        }
    }}
)
@api_view(['GET'])
@permission_classes([])
def api_health(request):
    """Comprehensive API health check"""
    from .performance_monitoring import get_health_check
    
    health_data = get_health_check()
    
    return Response({
        'status': health_data.get('status', 'unknown'),
        'checks': health_data.get('checks', {}),
        'timestamp': timezone.now().isoformat(),
        'api_version': getattr(request, 'version', '1.0')
    })

@extend_schema(
    summary="API Metrics",
    description="Get API usage metrics and statistics",
    responses={200: {
        'type': 'object',
        'properties': {
            'requests_today': {'type': 'integer'},
            'active_users': {'type': 'integer'},
            'avg_response_time': {'type': 'number'}
        }
    }}
)
@api_view(['GET'])
@permission_classes([IsAdminUser])
def api_metrics(request):
    """Get API metrics (admin only)"""
    from .performance_monitoring import get_performance_dashboard
    
    dashboard_data = get_performance_dashboard()
    app_metrics = dashboard_data.get('application_metrics', {})
    
    return Response({
        'requests_today': app_metrics.get('request_count', {}).get('count', 0),
        'avg_response_time_ms': app_metrics.get('response_times', {}).get('avg', 0),
        'error_rate': app_metrics.get('error_rate', {}).get('avg', 0),
        'cache_hit_ratio': dashboard_data.get('cache_metrics', {}).get('hit_ratio', {}).get('avg', 0),
        'timestamp': timezone.now().isoformat()
    })

# API Documentation customization
class CVAnalyzerAutoSchema(AutoSchema):
    """Custom schema generator for CV Analyzer API"""
    
    def get_operation_id(self, path, method):
        """Generate operation ID for OpenAPI"""
        if path.startswith('/api/v'):
            path = path[7:]  # Remove /api/v1 prefix
        return super().get_operation_id(path, method)
    
    def get_tags(self, path, method):
        """Generate tags for OpenAPI"""
        if '/cvs/' in path:
            return ['CVs']
        elif '/vacancies/' in path:
            return ['Vacancies']
        elif '/analysis-results/' in path:
            return ['Analysis Results']
        elif '/auth/' in path:
            return ['Authentication']
        else:
            return ['API'] 