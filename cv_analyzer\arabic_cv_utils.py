"""
Arabic CV Processing Utilities
Comprehensive utilities for processing Arabic CVs with translation and validation.
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
from django.core.cache import cache
from .arabic_processing import ArabicTextProcessor
# from .text_extraction.extractor import CVTextExtractor  # Disabled due to spaCy dependency
# from .ai.service import CVAnalysisService  # Disabled due to spaCy dependency

logger = logging.getLogger(__name__)

class ArabicCVProcessor:
    """
    Comprehensive Arabic CV processing with translation, validation, and analysis.
    """
    
    def __init__(self):
        """Initialize Arabic CV processor with required services."""
        self.arabic_processor = ArabicTextProcessor()
        # self.text_extractor = CVTextExtractor()  # Disabled due to spaCy dependency
        # self.ai_service = CVAnalysisService()  # Disabled due to spaCy dependency
        
    def process_arabic_cv(self, cv_file_path: str) -> Dict[str, Any]:
        """
        Complete processing pipeline for Arabic CVs.
        
        Args:
            cv_file_path: Path to the CV file
            
        Returns:
            Comprehensive processing results
        """
        try:
            # Step 1: Extract text and metadata
            extracted_text, metadata = self.text_extractor.extract_from_file(cv_file_path)
            
            # Step 2: Language analysis
            lang_info = self.arabic_processor.detect_language(extracted_text)
            
            # Step 3: Text preprocessing
            processed_text = self._preprocess_text(extracted_text, lang_info)
            
            # Step 4: Translation if needed
            translation_result = None
            if lang_info['is_arabic']:
                translation_result = self.arabic_processor.translate_text(
                    processed_text, 'ar', 'en'
                )
            
            # Step 5: Entity extraction
            entities = self.arabic_processor.extract_arabic_entities(extracted_text)
            
            # Step 6: Quality validation
            quality_score = self._validate_cv_quality(extracted_text, metadata, lang_info)
            
            return {
                'success': True,
                'original_text': extracted_text,
                'processed_text': processed_text,
                'metadata': metadata,
                'language_analysis': lang_info,
                'translation': translation_result,
                'entities': entities,
                'quality_score': quality_score,
                'processing_notes': self._generate_processing_notes(lang_info, quality_score)
            }
            
        except Exception as e:
            logger.error(f"Arabic CV processing failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'original_text': '',
                'processed_text': '',
                'metadata': None,
                'language_analysis': None,
                'translation': None,
                'entities': {},
                'quality_score': 0.0
            }
    
    def analyze_arabic_cv_for_job(
        self, 
        cv_file_path: str, 
        job_details: Dict[str, str]
    ) -> Dict[str, Any]:
        """
        Analyze Arabic CV for specific job compatibility.
        
        Args:
            cv_file_path: Path to the CV file
            job_details: Dictionary containing job information
            
        Returns:
            Detailed job compatibility analysis
        """
        try:
            # Process the CV
            cv_processing_result = self.process_arabic_cv(cv_file_path)
            
            if not cv_processing_result['success']:
                return cv_processing_result
            
            # Prepare text for analysis (use translation if available)
            analysis_text = cv_processing_result['processed_text']
            if cv_processing_result['translation'] and cv_processing_result['translation']['success']:
                analysis_text = cv_processing_result['translation']['translated_text']
            
            # Perform AI analysis
            import asyncio
            loop = asyncio.get_event_loop()
            
            ai_analysis = loop.run_until_complete(
                self.ai_service.analyze_cv_for_vacancy(
                    cv_text=analysis_text,
                    vacancy_title=job_details.get('title', ''),
                    company_name=job_details.get('company', ''),
                    vacancy_location=job_details.get('location', ''),
                    vacancy_description=job_details.get('description', ''),
                    vacancy_requirements=job_details.get('requirements', ''),
                    language_requirements=job_details.get('language_requirements', '')
                )
            )
            
            # Combine results
            return {
                'success': True,
                'cv_processing': cv_processing_result,
                'job_analysis': ai_analysis,
                'recommendations': self._generate_hiring_recommendations(
                    cv_processing_result, ai_analysis, job_details
                )
            }
            
        except Exception as e:
            logger.error(f"Arabic CV job analysis failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'cv_processing': None,
                'job_analysis': None,
                'recommendations': None
            }
    
    def batch_process_arabic_cvs(
        self, 
        cv_file_paths: List[str], 
        job_details: Dict[str, str]
    ) -> Dict[str, Any]:
        """
        Process multiple Arabic CVs for job matching.
        
        Args:
            cv_file_paths: List of CV file paths
            job_details: Job information
            
        Returns:
            Batch processing results with rankings
        """
        results = []
        processing_summary = {
            'total_cvs': len(cv_file_paths),
            'successful_processing': 0,
            'arabic_cvs': 0,
            'mixed_language_cvs': 0,
            'translation_successful': 0,
            'high_quality_cvs': 0
        }
        
        for cv_path in cv_file_paths:
            try:
                result = self.analyze_arabic_cv_for_job(cv_path, job_details)
                results.append({
                    'cv_path': cv_path,
                    'result': result
                })
                
                if result['success']:
                    processing_summary['successful_processing'] += 1
                    
                    cv_proc = result['cv_processing']
                    if cv_proc['language_analysis']['is_arabic']:
                        processing_summary['arabic_cvs'] += 1
                    if cv_proc['language_analysis']['is_mixed']:
                        processing_summary['mixed_language_cvs'] += 1
                    if cv_proc['translation'] and cv_proc['translation']['success']:
                        processing_summary['translation_successful'] += 1
                    if cv_proc['quality_score'] > 0.7:
                        processing_summary['high_quality_cvs'] += 1
                        
            except Exception as e:
                logger.error(f"Failed to process CV {cv_path}: {str(e)}")
                results.append({
                    'cv_path': cv_path,
                    'result': {'success': False, 'error': str(e)}
                })
        
        # Rank candidates
        rankings = self._rank_candidates(results)
        
        return {
            'success': True,
            'processing_summary': processing_summary,
            'individual_results': results,
            'rankings': rankings,
            'recommendations': self._generate_batch_recommendations(results, job_details)
        }
    
    def _preprocess_text(self, text: str, lang_info: Dict[str, Any]) -> str:
        """Preprocess text based on language analysis."""
        if not text:
            return text
        
        processed_text = text
        
        # Arabic-specific preprocessing
        if lang_info['is_arabic']:
            processed_text = self.arabic_processor.normalize_arabic_text(processed_text)
        
        # General preprocessing
        import re
        # Remove excessive whitespace
        processed_text = re.sub(r'\s+', ' ', processed_text)
        # Remove special characters that might interfere with analysis
        processed_text = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF@.-]', ' ', processed_text)
        
        return processed_text.strip()
    
    def _validate_cv_quality(self, text: str, metadata: Any, lang_info: Dict[str, Any]) -> float:
        """Validate CV quality and completeness."""
        quality_score = 0.0
        max_score = 10.0
        
        # Text length check
        if len(text) > 100:
            quality_score += 1.0
        if len(text) > 500:
            quality_score += 1.0
        
        # Language detection confidence
        quality_score += lang_info['confidence'] * 2.0
        
        # Metadata completeness
        if metadata and hasattr(metadata, 'sections') and metadata.sections:
            quality_score += len(metadata.sections) * 0.5
        
        # Entity extraction success
        entities = self.arabic_processor.extract_arabic_entities(text)
        if entities['emails']:
            quality_score += 1.0
        if entities['phones']:
            quality_score += 1.0
        if entities['names']:
            quality_score += 1.0
        
        # Arabic-specific quality checks
        if lang_info['is_arabic']:
            # Check for proper Arabic text structure
            if lang_info['arabic_ratio'] > 0.5:
                quality_score += 1.0
        
        return min(quality_score / max_score, 1.0)
    
    def _generate_processing_notes(self, lang_info: Dict[str, Any], quality_score: float) -> List[str]:
        """Generate processing notes and recommendations."""
        notes = []
        
        if lang_info['is_arabic']:
            notes.append(f"Arabic content detected ({lang_info['arabic_ratio']:.1%})")
        
        if lang_info['is_mixed']:
            notes.append("Mixed language content - bilingual candidate")
        
        if quality_score < 0.5:
            notes.append("Low quality CV - may need manual review")
        elif quality_score > 0.8:
            notes.append("High quality CV - well structured")
        
        if lang_info['confidence'] < 0.7:
            notes.append("Language detection uncertainty - manual verification recommended")
        
        return notes
    
    def _generate_hiring_recommendations(
        self, 
        cv_processing: Dict[str, Any], 
        ai_analysis: Dict[str, Any], 
        job_details: Dict[str, str]
    ) -> Dict[str, Any]:
        """Generate hiring recommendations based on analysis."""
        recommendations = {
            'overall_recommendation': 'Review Required',
            'strengths': [],
            'concerns': [],
            'next_steps': [],
            'interview_focus': []
        }
        
        if not cv_processing['success'] or not ai_analysis:
            recommendations['overall_recommendation'] = 'Not Suitable - Processing Failed'
            recommendations['concerns'].append('CV processing or analysis failed')
            return recommendations
        
        # Analyze language capabilities
        lang_info = cv_processing['language_analysis']
        if lang_info['is_arabic']:
            recommendations['strengths'].append('Native Arabic speaker')
        if lang_info['is_mixed']:
            recommendations['strengths'].append('Bilingual capabilities')
        
        # Quality assessment
        if cv_processing['quality_score'] > 0.7:
            recommendations['strengths'].append('Well-structured CV')
        elif cv_processing['quality_score'] < 0.5:
            recommendations['concerns'].append('CV quality issues')
        
        # Translation success
        if cv_processing['translation'] and cv_processing['translation']['success']:
            recommendations['strengths'].append('Translation available for detailed review')
        
        # AI analysis integration
        if ai_analysis and isinstance(ai_analysis, dict):
            if 'compatibility_scores' in ai_analysis:
                scores = ai_analysis['compatibility_scores']
                overall_score = scores.get('overall', 0)
                
                if overall_score >= 80:
                    recommendations['overall_recommendation'] = 'Highly Recommended'
                elif overall_score >= 60:
                    recommendations['overall_recommendation'] = 'Recommended'
                elif overall_score >= 40:
                    recommendations['overall_recommendation'] = 'Consider'
                else:
                    recommendations['overall_recommendation'] = 'Not Suitable'
        
        return recommendations
    
    def _rank_candidates(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Rank candidates based on analysis results."""
        rankings = []
        
        for result in results:
            if not result['result']['success']:
                continue
            
            cv_proc = result['result']['cv_processing']
            ai_analysis = result['result']['job_analysis']
            
            # Calculate ranking score
            score = 0.0
            
            # Quality score weight
            score += cv_proc['quality_score'] * 20
            
            # Language capabilities
            lang_info = cv_proc['language_analysis']
            if lang_info['is_arabic']:
                score += 10  # Arabic language bonus
            if lang_info['is_mixed']:
                score += 15  # Bilingual bonus
            
            # AI analysis score
            if ai_analysis and isinstance(ai_analysis, dict):
                if 'compatibility_scores' in ai_analysis:
                    score += ai_analysis['compatibility_scores'].get('overall', 0)
            
            rankings.append({
                'cv_path': result['cv_path'],
                'score': score,
                'quality': cv_proc['quality_score'],
                'language_capabilities': {
                    'arabic': lang_info['is_arabic'],
                    'bilingual': lang_info['is_mixed'],
                    'confidence': lang_info['confidence']
                }
            })
        
        # Sort by score descending
        rankings.sort(key=lambda x: x['score'], reverse=True)
        
        return rankings
    
    def _generate_batch_recommendations(
        self, 
        results: List[Dict[str, Any]], 
        job_details: Dict[str, str]
    ) -> Dict[str, Any]:
        """Generate recommendations for batch processing."""
        return {
            'top_candidates': 3,
            'interview_immediately': [],
            'consider_for_interview': [],
            'language_advantages': [],
            'skill_gaps_common': [],
            'hiring_strategy': 'Review top Arabic-speaking candidates first'
        }
