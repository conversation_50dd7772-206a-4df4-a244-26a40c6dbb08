"""
Simplified Arabic CV Processing Utilities
Basic utilities for processing Arabic CVs without spaCy dependencies.
"""

import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from .arabic_processing import ArabicTextProcessor

logger = logging.getLogger(__name__)

class ArabicCVProcessor:
    """
    Simplified Arabic CV processing with translation and basic analysis.
    """
    
    def __init__(self):
        """Initialize Arabic CV processor with required services."""
        self.arabic_processor = ArabicTextProcessor()
        
    def process_arabic_cv(self, cv_text: str) -> Dict[str, Any]:
        """
        Complete processing pipeline for Arabic CVs.
        
        Args:
            cv_text: Text content of the CV
            
        Returns:
            Dict containing processing results and extracted data
        """
        try:
            if not cv_text.strip():
                return {
                    'success': False,
                    'error': 'No text provided for processing',
                    'extracted_data': {}
                }
            
            # Step 1: Language detection
            lang_info = self.arabic_processor.detect_language(cv_text)
            
            # Step 2: Text normalization
            normalized_text = self.arabic_processor.normalize_arabic_text(cv_text)
            
            # Step 3: Entity extraction
            entities = self.arabic_processor.extract_arabic_entities(cv_text)
            
            # Step 4: Basic section extraction
            sections = self._extract_basic_sections(cv_text)
            
            # Step 5: Skills extraction
            skills = self._extract_skills(cv_text)
            
            # Step 6: Experience extraction
            experience = self._extract_experience(cv_text)
            
            # Step 7: Education extraction
            education = self._extract_education(cv_text)
            
            return {
                'success': True,
                'language_detected': lang_info.get('language', 'unknown'),
                'is_arabic': lang_info.get('is_arabic', False),
                'confidence': lang_info.get('confidence', 0.0),
                'extracted_data': {
                    'name': entities.get('names', [None])[0] if entities.get('names') else None,
                    'email': entities.get('emails', [None])[0] if entities.get('emails') else None,
                    'phone': entities.get('phones', [None])[0] if entities.get('phones') else None,
                    'sections': sections,
                    'skills': skills,
                    'experience': experience,
                    'education': education,
                    'normalized_text': normalized_text[:500] + '...' if len(normalized_text) > 500 else normalized_text
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing Arabic CV: {e}")
            return {
                'success': False,
                'error': str(e),
                'extracted_data': {}
            }
    
    def _extract_basic_sections(self, text: str) -> Dict[str, str]:
        """Extract basic CV sections."""
        sections = {}
        
        # Arabic section patterns
        section_patterns = {
            'personal_info': [
                r'المعلومات الشخصية',
                r'البيانات الشخصية',
                r'معلومات شخصية'
            ],
            'experience': [
                r'الخبرة المهنية',
                r'الخبرات',
                r'التجربة المهنية',
                r'خبرة العمل'
            ],
            'education': [
                r'التعليم',
                r'المؤهلات العلمية',
                r'الشهادات',
                r'التحصيل العلمي'
            ],
            'skills': [
                r'المهارات',
                r'القدرات',
                r'الكفاءات'
            ]
        }
        
        for section_name, patterns in section_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    sections[section_name] = f"Found: {pattern}"
                    break
        
        return sections
    
    def _extract_skills(self, text: str) -> List[str]:
        """Extract skills from CV text."""
        skills = []
        
        # Common Arabic skill patterns
        skill_patterns = [
            r'Python', r'Java', r'JavaScript', r'C\+\+', r'C#',
            r'HTML', r'CSS', r'React', r'Angular', r'Vue',
            r'Django', r'Flask', r'Spring', r'Node\.js',
            r'MySQL', r'PostgreSQL', r'MongoDB', r'Oracle',
            r'Git', r'Docker', r'Kubernetes', r'AWS', r'Azure',
            r'البرمجة', r'التطوير', r'التصميم', r'إدارة المشاريع'
        ]
        
        for pattern in skill_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            skills.extend(matches)
        
        return list(set(skills))  # Remove duplicates
    
    def _extract_experience(self, text: str) -> List[Dict[str, str]]:
        """Extract work experience from CV text."""
        experience = []
        
        # Look for date patterns and job titles
        date_patterns = [
            r'\d{4}\s*-\s*\d{4}',
            r'\d{4}\s*إلى\s*\d{4}',
            r'\d{4}\s*حتى\s*\d{4}'
        ]
        
        for pattern in date_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                # Extract surrounding context as potential job description
                start = max(0, match.start() - 100)
                end = min(len(text), match.end() + 100)
                context = text[start:end].strip()
                
                experience.append({
                    'period': match.group(),
                    'context': context
                })
        
        return experience[:5]  # Limit to 5 entries
    
    def _extract_education(self, text: str) -> List[Dict[str, str]]:
        """Extract education information from CV text."""
        education = []
        
        # Common education keywords
        education_keywords = [
            r'بكالوريوس', r'ماجستير', r'دكتوراه', r'دبلوم',
            r'Bachelor', r'Master', r'PhD', r'Diploma',
            r'جامعة', r'كلية', r'معهد'
        ]
        
        for keyword in education_keywords:
            matches = re.finditer(keyword, text, re.IGNORECASE)
            for match in matches:
                # Extract surrounding context
                start = max(0, match.start() - 50)
                end = min(len(text), match.end() + 100)
                context = text[start:end].strip()
                
                education.append({
                    'degree_type': match.group(),
                    'context': context
                })
        
        return education[:3]  # Limit to 3 entries
    
    def analyze_arabic_cv_for_job(self, cv_text: str, job_description: str) -> Dict[str, Any]:
        """
        Analyze Arabic CV compatibility with job requirements.
        
        Args:
            cv_text: CV text content
            job_description: Job description text
            
        Returns:
            Analysis results with compatibility score
        """
        try:
            # Process CV
            cv_result = self.process_arabic_cv(cv_text)
            if not cv_result['success']:
                return cv_result
            
            # Extract job requirements (simplified)
            job_skills = self._extract_skills(job_description)
            cv_skills = cv_result['extracted_data'].get('skills', [])
            
            # Calculate skill match
            matching_skills = set(cv_skills) & set(job_skills)
            skill_match_ratio = len(matching_skills) / max(len(job_skills), 1)
            
            # Simple compatibility score
            compatibility_score = min(100, int(skill_match_ratio * 100))
            
            return {
                'success': True,
                'cv_analysis': cv_result,
                'job_requirements': {
                    'skills': job_skills
                },
                'compatibility': {
                    'score': compatibility_score,
                    'matching_skills': list(matching_skills),
                    'missing_skills': list(set(job_skills) - set(cv_skills))
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing Arabic CV for job: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def batch_process_arabic_cvs(self, cv_texts: List[str]) -> List[Dict[str, Any]]:
        """
        Process multiple Arabic CVs in batch.
        
        Args:
            cv_texts: List of CV text contents
            
        Returns:
            List of processing results
        """
        results = []
        
        for i, cv_text in enumerate(cv_texts):
            try:
                result = self.process_arabic_cv(cv_text)
                result['cv_index'] = i
                results.append(result)
            except Exception as e:
                logger.error(f"Error processing CV {i}: {e}")
                results.append({
                    'success': False,
                    'error': str(e),
                    'cv_index': i
                })
        
        return results
