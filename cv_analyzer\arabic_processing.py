"""
Arabic Language Processing Module for CV Analysis
Handles Arabic text detection, preprocessing, normalization, and RTL text processing.
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from langdetect import detect, DetectorFactory
from langdetect.lang_detect_exception import LangDetectException
import arabic_reshaper
from bidi.algorithm import get_display
import pyarabic.araby as araby
from deep_translator import GoogleTranslator, MicrosoftTranslator
from googletrans import Translator as GoogleTranslatorV2

logger = logging.getLogger(__name__)

# Set seed for consistent language detection
DetectorFactory.seed = 0

class ArabicTextProcessor:
    """Comprehensive Arabic text processing and analysis."""
    
    def __init__(self):
        """Initialize Arabic text processor with translation services."""
        self.google_translator = GoogleTranslator(source='ar', target='en')

        # Initialize Microsoft translator only if API key is available
        self.microsoft_translator = None
        logger.info("Microsoft Translator not initialized - API key required")

        self.google_v2_translator = GoogleTranslatorV2()
        
        # Arabic text patterns
        self.arabic_pattern = re.compile(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+')
        self.english_pattern = re.compile(r'[a-zA-Z]+')
        
    def detect_language(self, text: str) -> Dict[str, Any]:
        """
        Detect language of text with confidence score and detailed analysis.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Dict containing language detection results
        """
        if not text or not text.strip():
            return {
                'language': 'unknown',
                'confidence': 0.0,
                'is_arabic': False,
                'is_mixed': False,
                'arabic_ratio': 0.0,
                'english_ratio': 0.0
            }
        
        # Clean text for analysis
        clean_text = self._clean_text_for_detection(text)
        
        # Count Arabic and English characters
        arabic_chars = len(self.arabic_pattern.findall(clean_text))
        english_chars = len(self.english_pattern.findall(clean_text))
        total_chars = len(re.sub(r'\s+', '', clean_text))
        
        arabic_ratio = arabic_chars / max(total_chars, 1)
        english_ratio = english_chars / max(total_chars, 1)
        
        # Use langdetect for primary detection
        detected_lang = 'unknown'
        confidence = 0.0
        
        try:
            detected_lang = detect(clean_text)
            # Estimate confidence based on character ratios
            if detected_lang == 'ar':
                confidence = min(0.9, arabic_ratio + 0.1)
            elif detected_lang == 'en':
                confidence = min(0.9, english_ratio + 0.1)
            else:
                confidence = 0.5
        except LangDetectException:
            # Fallback to character-based detection
            if arabic_ratio > 0.3:
                detected_lang = 'ar'
                confidence = arabic_ratio
            elif english_ratio > 0.5:
                detected_lang = 'en'
                confidence = english_ratio
        
        is_arabic = detected_lang == 'ar' or arabic_ratio > 0.3
        is_mixed = arabic_ratio > 0.1 and english_ratio > 0.1
        
        return {
            'language': detected_lang,
            'confidence': confidence,
            'is_arabic': is_arabic,
            'is_mixed': is_mixed,
            'arabic_ratio': arabic_ratio,
            'english_ratio': english_ratio,
            'total_chars': total_chars,
            'arabic_chars': arabic_chars,
            'english_chars': english_chars
        }
    
    def normalize_arabic_text(self, text: str) -> str:
        """
        Normalize Arabic text for better processing.
        
        Args:
            text: Arabic text to normalize
            
        Returns:
            Normalized Arabic text
        """
        if not text:
            return text
        
        # Remove diacritics (tashkeel)
        text = araby.strip_diacritics(text)
        
        # Normalize Arabic characters
        text = araby.normalize_hamza(text)
        text = araby.normalize_alef(text)
        text = araby.normalize_teh(text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def reshape_arabic_text(self, text: str) -> str:
        """
        Reshape Arabic text for proper RTL display.
        
        Args:
            text: Arabic text to reshape
            
        Returns:
            Reshaped text for RTL display
        """
        if not text:
            return text
        
        try:
            # Reshape Arabic text
            reshaped_text = arabic_reshaper.reshape(text)
            # Apply bidirectional algorithm
            display_text = get_display(reshaped_text)
            return display_text
        except Exception as e:
            logger.warning(f"Failed to reshape Arabic text: {e}")
            return text
    
    def translate_text(self, text: str, source_lang: str = 'ar', target_lang: str = 'en') -> Dict[str, Any]:
        """
        Translate text using multiple translation services with fallback.
        
        Args:
            text: Text to translate
            source_lang: Source language code
            target_lang: Target language code
            
        Returns:
            Translation result with metadata
        """
        if not text or not text.strip():
            return {
                'translated_text': text,
                'original_text': text,
                'source_lang': source_lang,
                'target_lang': target_lang,
                'service_used': 'none',
                'confidence': 0.0,
                'success': False
            }
        
        # Try multiple translation services
        translation_services = [
            ('google_v2', self._translate_with_google_v2),
            ('google', self._translate_with_google),
            ('microsoft', self._translate_with_microsoft)
        ]
        
        for service_name, translate_func in translation_services:
            try:
                result = translate_func(text, source_lang, target_lang)
                if result['success']:
                    result['service_used'] = service_name
                    return result
            except Exception as e:
                logger.warning(f"Translation failed with {service_name}: {e}")
                continue
        
        # If all services fail, return original text
        return {
            'translated_text': text,
            'original_text': text,
            'source_lang': source_lang,
            'target_lang': target_lang,
            'service_used': 'none',
            'confidence': 0.0,
            'success': False,
            'error': 'All translation services failed'
        }
    
    def extract_arabic_entities(self, text: str) -> Dict[str, List[str]]:
        """
        Extract Arabic named entities and key information.
        
        Args:
            text: Arabic text to analyze
            
        Returns:
            Dictionary of extracted entities
        """
        entities = {
            'names': [],
            'locations': [],
            'organizations': [],
            'dates': [],
            'emails': [],
            'phones': [],
            'skills': []
        }
        
        # Extract emails (universal pattern)
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        entities['emails'] = re.findall(email_pattern, text)
        
        # Extract phone numbers (Arabic and international formats)
        phone_patterns = [
            r'[\+]?[0-9]{1,4}[\s\-]?[0-9]{3,4}[\s\-]?[0-9]{3,4}[\s\-]?[0-9]{3,4}',
            r'[0-9]{10,15}',
            r'[\u0660-\u0669]{10,15}'  # Arabic numerals
        ]
        
        for pattern in phone_patterns:
            entities['phones'].extend(re.findall(pattern, text))
        
        # Extract dates (Arabic and English formats)
        date_patterns = [
            r'\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}',
            r'\d{2,4}[\/\-]\d{1,2}[\/\-]\d{1,2}',
            r'[\u0660-\u0669]{1,2}[\/\-][\u0660-\u0669]{1,2}[\/\-][\u0660-\u0669]{2,4}'
        ]
        
        for pattern in date_patterns:
            entities['dates'].extend(re.findall(pattern, text))
        
        # Extract potential names (Arabic names pattern)
        # This is a simplified approach - in production, use a proper Arabic NER model
        arabic_name_pattern = r'[\u0621-\u064A\s]{2,50}(?=\s|$)'
        potential_names = re.findall(arabic_name_pattern, text)
        
        # Filter names (basic heuristics)
        for name in potential_names:
            name = name.strip()
            if len(name.split()) >= 2 and len(name.split()) <= 4:
                entities['names'].append(name)
        
        # Remove duplicates
        for key in entities:
            entities[key] = list(set(entities[key]))
        
        return entities
    
    def _clean_text_for_detection(self, text: str) -> str:
        """Clean text for language detection."""
        # Remove URLs, emails, and numbers
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', text)
        text = re.sub(r'\d+', '', text)
        text = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]', ' ', text)
        return text.strip()
    
    def _translate_with_google_v2(self, text: str, source_lang: str, target_lang: str) -> Dict[str, Any]:
        """Translate using Google Translate v2."""
        try:
            result = self.google_v2_translator.translate(text, src=source_lang, dest=target_lang)
            return {
                'translated_text': result.text,
                'original_text': text,
                'source_lang': source_lang,
                'target_lang': target_lang,
                'confidence': getattr(result, 'confidence', 0.8),
                'success': True
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _translate_with_google(self, text: str, source_lang: str, target_lang: str) -> Dict[str, Any]:
        """Translate using Google Translate (deep-translator)."""
        try:
            translator = GoogleTranslator(source=source_lang, target=target_lang)
            translated = translator.translate(text)
            return {
                'translated_text': translated,
                'original_text': text,
                'source_lang': source_lang,
                'target_lang': target_lang,
                'confidence': 0.8,
                'success': True
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _translate_with_microsoft(self, text: str, source_lang: str, target_lang: str) -> Dict[str, Any]:
        """Translate using Microsoft Translator."""
        try:
            if self.microsoft_translator is None:
                return {'success': False, 'error': 'Microsoft Translator not available - API key required'}

            translated = self.microsoft_translator.translate(text)
            return {
                'translated_text': translated,
                'original_text': text,
                'source_language': source_lang,
                'target_language': target_lang,
                'confidence': 0.8,
                'success': True
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
