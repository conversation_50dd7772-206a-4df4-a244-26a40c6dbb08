"""
Business Intelligence and Analytics System
Provides comprehensive analytics, reporting, and data visualization capabilities.
"""

import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

from django.db.models import Count, Avg, Sum, Q, F
from django.db.models.functions import <PERSON>runcDate, TruncMonth, TruncWeek
from django.utils import timezone
from django.core.cache import cache
from django.contrib.auth.models import User

from .models import (
    CV, CVAnalysis, Company, Vacancy, VacancyAnalysis, ComparisonAnalysis,
    UnifiedAnalysis, AIUsageLog, SecurityAuditLog, FileUploadLog,
    MonitoringMetric, DeploymentLog, HealthCheckResult
)

logger = logging.getLogger(__name__)

@dataclass
class AnalyticsMetric:
    """Data class for analytics metrics"""
    name: str
    value: float
    change_percentage: Optional[float] = None
    trend: Optional[str] = None  # 'up', 'down', 'stable'
    metadata: Dict[str, Any] = None

@dataclass
class ReportData:
    """Data class for report data"""
    title: str
    data: Dict[str, Any]
    charts: List[Dict[str, Any]]
    summary: Dict[str, Any]
    generated_at: datetime

class BusinessIntelligenceManager:
    """Main Business Intelligence manager"""
    
    def __init__(self):
        self.cache_timeout = 3600  # 1 hour
        
    def get_dashboard_analytics(self, date_range: int = 30) -> Dict[str, Any]:
        """
        Get comprehensive dashboard analytics
        
        Args:
            date_range: Number of days to analyze
            
        Returns:
            Dictionary containing all dashboard analytics
        """
        cache_key = f"dashboard_analytics_{date_range}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
            
        try:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=date_range)
            
            analytics_data = {
                'overview': self._get_overview_metrics(start_date, end_date),
                'cv_analytics': self._get_cv_analytics(start_date, end_date),
                'vacancy_analytics': self._get_vacancy_analytics(start_date, end_date),
                'ai_usage': self._get_ai_usage_analytics(start_date, end_date),
                'user_activity': self._get_user_activity_analytics(start_date, end_date),
                'performance_metrics': self._get_performance_metrics(start_date, end_date),
                'security_metrics': self._get_security_metrics(start_date, end_date),
                'trends': self._get_trend_analysis(start_date, end_date),
                'predictions': self._get_predictive_analytics(start_date, end_date)
            }
            
            # Cache the results
            cache.set(cache_key, analytics_data, self.cache_timeout)
            
            return analytics_data
            
        except Exception as e:
            logger.error(f"Error generating dashboard analytics: {str(e)}")
            return {'error': str(e)}
    
    def _get_overview_metrics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get overview metrics"""
        try:
            # Total counts
            total_cvs = CV.objects.count()
            total_companies = Company.objects.count()
            total_vacancies = Vacancy.objects.count()
            total_users = User.objects.count()
            
            # Period counts
            period_cvs = CV.objects.filter(uploaded_at__range=[start_date, end_date]).count()
            period_analyses = CVAnalysis.objects.filter(
                cv__uploaded_at__range=[start_date, end_date]
            ).count()
            period_users = User.objects.filter(date_joined__range=[start_date, end_date]).count()
            
            # Calculate growth rates
            previous_start = start_date - (end_date - start_date)
            prev_cvs = CV.objects.filter(uploaded_at__range=[previous_start, start_date]).count()
            cv_growth = ((period_cvs - prev_cvs) / max(prev_cvs, 1)) * 100 if prev_cvs > 0 else 0
            
            return {
                'total_metrics': {
                    'cvs': total_cvs,
                    'companies': total_companies,
                    'vacancies': total_vacancies,
                    'users': total_users
                },
                'period_metrics': {
                    'new_cvs': period_cvs,
                    'new_analyses': period_analyses,
                    'new_users': period_users,
                    'cv_growth_rate': round(cv_growth, 2)
                },
                'activity_rate': {
                    'daily_avg_cvs': round(period_cvs / max((end_date - start_date).days, 1), 2),
                    'analysis_completion_rate': round((period_analyses / max(period_cvs, 1)) * 100, 2)
                }
            }
            
        except Exception as e:
            logger.error(f"Error in overview metrics: {str(e)}")
            return {'error': str(e)}
    
    def _get_cv_analytics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get CV analytics"""
        try:
            # CV status distribution
            cv_status = CV.objects.values('status').annotate(count=Count('id')).order_by('status')
            
            # CV sources distribution
            cv_sources = CV.objects.values('source').annotate(count=Count('id')).order_by('-count')
            
            # Average scores
            avg_scores = CVAnalysis.objects.aggregate(
                avg_overall=Avg('overall_score'),
                avg_content=Avg('content_score'),
                avg_format=Avg('format_score'),
                avg_skills=Avg('skills_score')
            )
            
            # Experience distribution
            experience_dist = CVAnalysis.objects.values('years_of_experience').annotate(
                count=Count('id')
            ).order_by('years_of_experience')
            
            # Education level distribution
            education_dist = CVAnalysis.objects.exclude(
                education_level=''
            ).values('education_level').annotate(count=Count('id')).order_by('-count')
            
            # Top skills analysis
            top_skills = self._analyze_top_skills()
            
            # CV upload trends
            upload_trends = CV.objects.filter(
                uploaded_at__range=[start_date, end_date]
            ).extra(
                {'date': 'date(uploaded_at)'}
            ).values('date').annotate(count=Count('id')).order_by('date')
            
            return {
                'status_distribution': list(cv_status),
                'source_distribution': list(cv_sources),
                'average_scores': avg_scores,
                'experience_distribution': list(experience_dist),
                'education_distribution': list(education_dist),
                'top_skills': top_skills,
                'upload_trends': list(upload_trends)
            }
            
        except Exception as e:
            logger.error(f"Error in CV analytics: {str(e)}")
            return {'error': str(e)}
    
    def _get_vacancy_analytics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get vacancy analytics"""
        try:
            # Vacancy status distribution
            vacancy_status = Vacancy.objects.values('status').annotate(count=Count('id'))
            
            # Most active companies
            active_companies = Company.objects.annotate(
                vacancy_count=Count('vacancy')
            ).order_by('-vacancy_count')[:10]
            
            # Industry distribution
            industry_dist = Company.objects.values('industry').annotate(
                vacancy_count=Count('vacancy')
            ).order_by('-vacancy_count')
            
            # Vacancy creation trends
            vacancy_trends = Vacancy.objects.filter(
                created_at__range=[start_date, end_date]
            ).extra(
                {'date': 'date(created_at)'}
            ).values('date').annotate(count=Count('id')).order_by('date')
            
            # Application success rates
            application_stats = ComparisonAnalysis.objects.filter(
                created_at__range=[start_date, end_date]
            ).aggregate(
                total_applications=Count('id'),
                avg_compatibility=Avg('compatibility_score')
            )
            
            return {
                'status_distribution': list(vacancy_status),
                'active_companies': [
                    {'name': c.name, 'vacancy_count': c.vacancy_count, 'industry': c.industry}
                    for c in active_companies
                ],
                'industry_distribution': list(industry_dist),
                'creation_trends': list(vacancy_trends),
                'application_stats': application_stats
            }
            
        except Exception as e:
            logger.error(f"Error in vacancy analytics: {str(e)}")
            return {'error': str(e)}
    
    def _get_ai_usage_analytics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get AI usage analytics"""
        try:
            # AI provider usage
            provider_usage = AIUsageLog.objects.filter(
                created_at__range=[start_date, end_date]
            ).values('provider').annotate(
                request_count=Count('id'),
                total_tokens=Sum('tokens_used'),
                avg_response_time=Avg('response_time'),
                success_rate=Avg('status')
            ).order_by('-request_count')
            
            # AI operation types
            operation_stats = AIUsageLog.objects.filter(
                created_at__range=[start_date, end_date]
            ).values('operation').annotate(count=Count('id')).order_by('-count')
            
            # AI usage trends
            usage_trends = AIUsageLog.objects.filter(
                created_at__range=[start_date, end_date]
            ).extra(
                {'date': 'date(created_at)'}
            ).values('date').annotate(
                requests=Count('id'),
                tokens=Sum('tokens_used')
            ).order_by('date')
            
            # Error rate analysis
            error_stats = AIUsageLog.objects.filter(
                created_at__range=[start_date, end_date]
            ).aggregate(
                total_requests=Count('id'),
                failed_requests=Count('id', filter=Q(status='error')),
                avg_response_time=Avg('response_time')
            )
            
            if error_stats['total_requests'] > 0:
                error_stats['error_rate'] = (
                    error_stats['failed_requests'] / error_stats['total_requests']
                ) * 100
            else:
                error_stats['error_rate'] = 0
            
            return {
                'provider_usage': list(provider_usage),
                'operation_stats': list(operation_stats),
                'usage_trends': list(usage_trends),
                'error_stats': error_stats
            }
            
        except Exception as e:
            logger.error(f"Error in AI usage analytics: {str(e)}")
            return {'error': str(e)}
    
    def _get_user_activity_analytics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get user activity analytics"""
        try:
            # New user registrations
            new_users = User.objects.filter(
                date_joined__range=[start_date, end_date]
            ).extra(
                {'date': 'date(date_joined)'}
            ).values('date').annotate(count=Count('id')).order_by('date')
            
            # Active users (users who performed actions)
            active_users = SecurityAuditLog.objects.filter(
                timestamp__range=[start_date, end_date],
                user__isnull=False
            ).values('user').distinct().count()
            
            # Most active users
            top_users = SecurityAuditLog.objects.filter(
                timestamp__range=[start_date, end_date],
                user__isnull=False
            ).values('user__username').annotate(
                activity_count=Count('id')
            ).order_by('-activity_count')[:10]
            
            # User engagement metrics
            engagement_metrics = {
                'total_sessions': SecurityAuditLog.objects.filter(
                    timestamp__range=[start_date, end_date],
                    event_type='login_success'
                ).count(),
                'unique_active_users': active_users,
                'avg_session_duration': 0  # Would need session tracking
            }
            
            return {
                'new_user_trends': list(new_users),
                'active_users': active_users,
                'top_users': list(top_users),
                'engagement_metrics': engagement_metrics
            }
            
        except Exception as e:
            logger.error(f"Error in user activity analytics: {str(e)}")
            return {'error': str(e)}
    
    def _get_performance_metrics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get system performance metrics"""
        try:
            # Health check results
            health_stats = HealthCheckResult.objects.filter(
                timestamp__range=[start_date, end_date]
            ).values('status').annotate(count=Count('id'))
            
            # Average response times
            avg_response_times = HealthCheckResult.objects.filter(
                timestamp__range=[start_date, end_date]
            ).values('check_name').annotate(
                avg_response_time=Avg('response_time_ms')
            ).order_by('check_name')
            
            # System availability
            total_checks = HealthCheckResult.objects.filter(
                timestamp__range=[start_date, end_date]
            ).count()
            
            healthy_checks = HealthCheckResult.objects.filter(
                timestamp__range=[start_date, end_date],
                status='healthy'
            ).count()
            
            availability = (healthy_checks / max(total_checks, 1)) * 100 if total_checks > 0 else 0
            
            return {
                'health_distribution': list(health_stats),
                'response_times': list(avg_response_times),
                'system_availability': round(availability, 2),
                'total_health_checks': total_checks
            }
            
        except Exception as e:
            logger.error(f"Error in performance metrics: {str(e)}")
            return {'error': str(e)}
    
    def _get_security_metrics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get security metrics"""
        try:
            # Security event types
            security_events = SecurityAuditLog.objects.filter(
                timestamp__range=[start_date, end_date]
            ).values('event_type').annotate(count=Count('id')).order_by('-count')
            
            # Failed login attempts
            failed_logins = SecurityAuditLog.objects.filter(
                timestamp__range=[start_date, end_date],
                event_type='login_failure'
            ).count()
            
            # File upload security
            file_security = FileUploadLog.objects.filter(
                uploaded_at__range=[start_date, end_date]
            ).values('status').annotate(count=Count('id'))
            
            # Top IP addresses
            top_ips = SecurityAuditLog.objects.filter(
                timestamp__range=[start_date, end_date]
            ).values('ip_address').annotate(
                request_count=Count('id')
            ).order_by('-request_count')[:10]
            
            return {
                'security_events': list(security_events),
                'failed_logins': failed_logins,
                'file_security_status': list(file_security),
                'top_ip_addresses': list(top_ips)
            }
            
        except Exception as e:
            logger.error(f"Error in security metrics: {str(e)}")
            return {'error': str(e)}
    
    def _get_trend_analysis(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get trend analysis"""
        try:
            # Weekly trends
            weekly_data = CV.objects.filter(
                uploaded_at__range=[start_date, end_date]
            ).annotate(
                week=TruncWeek('uploaded_at')
            ).values('week').annotate(
                cv_count=Count('id')
            ).order_by('week')
            
            # Calculate trend direction
            if len(weekly_data) >= 2:
                recent_avg = sum(item['cv_count'] for item in weekly_data[-2:]) / 2
                older_avg = sum(item['cv_count'] for item in weekly_data[:2]) / 2
                trend_direction = 'up' if recent_avg > older_avg else 'down' if recent_avg < older_avg else 'stable'
            else:
                trend_direction = 'stable'
            
            return {
                'weekly_cv_uploads': list(weekly_data),
                'trend_direction': trend_direction,
                'growth_indicators': {
                    'cv_uploads': trend_direction,
                    'user_registrations': 'stable',  # Would need more complex calculation
                    'ai_usage': 'up'  # Would need more complex calculation
                }
            }
            
        except Exception as e:
            logger.error(f"Error in trend analysis: {str(e)}")
            return {'error': str(e)}
    
    def _get_predictive_analytics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get predictive analytics using simple statistical methods"""
        try:
            # Predict next month's CV uploads based on current trend
            monthly_uploads = CV.objects.filter(
                uploaded_at__range=[start_date, end_date]
            ).annotate(
                month=TruncMonth('uploaded_at')
            ).values('month').annotate(
                count=Count('id')
            ).order_by('month')
            
            if len(monthly_uploads) >= 2:
                # Simple linear prediction
                recent_data = [item['count'] for item in monthly_uploads]
                if len(recent_data) >= 3:
                    # Calculate trend using last 3 months
                    trend = (recent_data[-1] - recent_data[-3]) / 2
                    predicted_next_month = max(0, int(recent_data[-1] + trend))
                else:
                    predicted_next_month = recent_data[-1]
            else:
                predicted_next_month = 0
            
            # Predict resource needs
            avg_ai_tokens_per_cv = 1000  # Placeholder - would calculate from actual data
            predicted_token_usage = predicted_next_month * avg_ai_tokens_per_cv
            
            return {
                'next_month_cv_uploads': predicted_next_month,
                'predicted_ai_token_usage': predicted_token_usage,
                'capacity_recommendations': {
                    'scaling_needed': predicted_next_month > 100,
                    'additional_ai_quota': predicted_token_usage > 50000
                }
            }
            
        except Exception as e:
            logger.error(f"Error in predictive analytics: {str(e)}")
            return {'error': str(e)}
    
    def _analyze_top_skills(self) -> List[Dict[str, Any]]:
        """Analyze top skills from CV analyses"""
        try:
            # Get all skills from CV analyses
            skill_counts = defaultdict(int)
            
            cv_analyses = CVAnalysis.objects.exclude(skills='').values_list('skills', flat=True)
            
            for skills_text in cv_analyses:
                if skills_text:
                    # Simple skill extraction (could be enhanced with NLP)
                    skills = [skill.strip().lower() for skill in skills_text.split(',')]
                    for skill in skills:
                        if len(skill) > 2:  # Filter out very short strings
                            skill_counts[skill] += 1
            
            # Sort and return top skills
            top_skills = sorted(skill_counts.items(), key=lambda x: x[1], reverse=True)[:20]
            
            return [
                {'skill': skill, 'count': count}
                for skill, count in top_skills
            ]
            
        except Exception as e:
            logger.error(f"Error analyzing top skills: {str(e)}")
            return []
    
    def generate_custom_report(self, report_type: str, parameters: Dict[str, Any]) -> ReportData:
        """Generate custom reports based on type and parameters"""
        try:
            if report_type == 'cv_performance':
                return self._generate_cv_performance_report(parameters)
            elif report_type == 'company_analysis':
                return self._generate_company_analysis_report(parameters)
            elif report_type == 'ai_usage':
                return self._generate_ai_usage_report(parameters)
            elif report_type == 'security_audit':
                return self._generate_security_audit_report(parameters)
            else:
                raise ValueError(f"Unknown report type: {report_type}")
                
        except Exception as e:
            logger.error(f"Error generating custom report: {str(e)}")
            raise
    
    def _generate_cv_performance_report(self, parameters: Dict[str, Any]) -> ReportData:
        """Generate CV performance report"""
        date_range = parameters.get('date_range', 30)
        end_date = timezone.now()
        start_date = end_date - timedelta(days=date_range)
        
        # Get CV performance data
        cv_analyses = CVAnalysis.objects.filter(
            cv__uploaded_at__range=[start_date, end_date]
        )
        
        performance_data = {
            'total_cvs_analyzed': cv_analyses.count(),
            'average_scores': cv_analyses.aggregate(
                overall=Avg('overall_score'),
                content=Avg('content_score'),
                format=Avg('format_score'),
                skills=Avg('skills_score')
            ),
            'score_distribution': self._get_score_distribution(cv_analyses),
            'top_performing_cvs': cv_analyses.order_by('-overall_score')[:10],
        }
        
        charts = [
            {
                'type': 'bar',
                'title': 'Average Scores by Category',
                'data': performance_data['average_scores']
            },
            {
                'type': 'histogram',
                'title': 'Overall Score Distribution',
                'data': performance_data['score_distribution']
            }
        ]
        
        summary = {
            'total_analyzed': performance_data['total_cvs_analyzed'],
            'avg_overall_score': performance_data['average_scores']['overall'] or 0,
            'insights': self._generate_cv_insights(performance_data)
        }
        
        return ReportData(
            title="CV Performance Report",
            data=performance_data,
            charts=charts,
            summary=summary,
            generated_at=timezone.now()
        )
    
    def _generate_company_analysis_report(self, parameters: Dict[str, Any]) -> ReportData:
        """Generate company analysis report"""
        # Implementation for company analysis report
        companies = Company.objects.all()
        
        company_data = {
            'total_companies': companies.count(),
            'industry_breakdown': companies.values('industry').annotate(
                count=Count('id')
            ).order_by('-count'),
            'vacancy_stats': companies.annotate(
                vacancy_count=Count('vacancy')
            ).aggregate(
                avg_vacancies=Avg('vacancy_count'),
                max_vacancies=Count('vacancy__id')
            )
        }
        
        return ReportData(
            title="Company Analysis Report",
            data=company_data,
            charts=[],
            summary={'total_companies': company_data['total_companies']},
            generated_at=timezone.now()
        )
    
    def _generate_ai_usage_report(self, parameters: Dict[str, Any]) -> ReportData:
        """Generate AI usage report"""
        date_range = parameters.get('date_range', 30)
        end_date = timezone.now()
        start_date = end_date - timedelta(days=date_range)
        
        ai_usage = AIUsageLog.objects.filter(
            created_at__range=[start_date, end_date]
        )
        
        usage_data = {
            'total_requests': ai_usage.count(),
            'total_tokens': ai_usage.aggregate(Sum('tokens_used'))['tokens_used__sum'] or 0,
            'provider_breakdown': ai_usage.values('provider').annotate(
                count=Count('id'),
                tokens=Sum('tokens_used')
            ).order_by('-count'),
            'success_rate': (
                ai_usage.filter(status='success').count() / max(ai_usage.count(), 1)
            ) * 100
        }
        
        return ReportData(
            title="AI Usage Report",
            data=usage_data,
            charts=[],
            summary={'total_requests': usage_data['total_requests']},
            generated_at=timezone.now()
        )
    
    def _generate_security_audit_report(self, parameters: Dict[str, Any]) -> ReportData:
        """Generate security audit report"""
        date_range = parameters.get('date_range', 30)
        end_date = timezone.now()
        start_date = end_date - timedelta(days=date_range)
        
        security_events = SecurityAuditLog.objects.filter(
            timestamp__range=[start_date, end_date]
        )
        
        security_data = {
            'total_events': security_events.count(),
            'failed_logins': security_events.filter(event_type='login_failure').count(),
            'successful_logins': security_events.filter(event_type='login_success').count(),
            'file_uploads': security_events.filter(event_type='file_upload').count(),
            'event_breakdown': security_events.values('event_type').annotate(
                count=Count('id')
            ).order_by('-count')
        }
        
        return ReportData(
            title="Security Audit Report",
            data=security_data,
            charts=[],
            summary={'total_events': security_data['total_events']},
            generated_at=timezone.now()
        )
    
    def _get_score_distribution(self, cv_analyses) -> Dict[str, int]:
        """Get distribution of CV scores"""
        distribution = {
            '0-20': 0, '21-40': 0, '41-60': 0, '61-80': 0, '81-100': 0
        }
        
        for analysis in cv_analyses:
            score = analysis.overall_score or 0
            if score <= 20:
                distribution['0-20'] += 1
            elif score <= 40:
                distribution['21-40'] += 1
            elif score <= 60:
                distribution['41-60'] += 1
            elif score <= 80:
                distribution['61-80'] += 1
            else:
                distribution['81-100'] += 1
        
        return distribution
    
    def _generate_cv_insights(self, performance_data: Dict[str, Any]) -> List[str]:
        """Generate insights from CV performance data"""
        insights = []
        
        avg_overall = performance_data['average_scores']['overall'] or 0
        
        if avg_overall > 75:
            insights.append("CVs show high overall quality with strong performance across categories.")
        elif avg_overall > 50:
            insights.append("CVs show moderate quality with room for improvement.")
        else:
            insights.append("CVs need significant improvement in multiple areas.")
        
        # Add more insights based on data patterns
        avg_format = performance_data['average_scores']['format'] or 0
        avg_content = performance_data['average_scores']['content'] or 0
        
        if avg_format < avg_content:
            insights.append("Format scores are lower than content scores - focus on improving CV formatting.")
        elif avg_content < avg_format:
            insights.append("Content scores are lower than format scores - focus on improving CV content quality.")
        
        return insights
    
    def export_report_data(self, report_data: ReportData, format_type: str = 'json') -> str:
        """Export report data in specified format"""
        try:
            if format_type == 'json':
                return json.dumps({
                    'title': report_data.title,
                    'data': report_data.data,
                    'summary': report_data.summary,
                    'generated_at': report_data.generated_at.isoformat()
                }, indent=2, default=str)
            
            elif format_type == 'csv':
                # Convert to CSV format (simplified)
                import csv
                import io
                
                output = io.StringIO()
                writer = csv.writer(output)
                
                # Write header
                writer.writerow(['Metric', 'Value'])
                
                # Write data
                for key, value in report_data.summary.items():
                    writer.writerow([key, value])
                
                return output.getvalue()
            
            else:
                raise ValueError(f"Unsupported format: {format_type}")
                
        except Exception as e:
            logger.error(f"Error exporting report data: {str(e)}")
            raise

class RealtimeAnalytics:
    """Real-time analytics for live dashboard updates"""
    
    def __init__(self):
        self.cache_timeout = 60  # 1 minute for real-time data
    
    def get_realtime_metrics(self) -> Dict[str, Any]:
        """Get real-time metrics for dashboard"""
        cache_key = "realtime_metrics"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            now = timezone.now()
            hour_ago = now - timedelta(hours=1)
            
            metrics = {
                'active_users': self._get_active_users_count(hour_ago, now),
                'recent_uploads': CV.objects.filter(uploaded_at__gte=hour_ago).count(),
                'ai_requests_per_minute': self._get_ai_requests_per_minute(hour_ago, now),
                'system_health': self._get_current_system_health(),
                'last_updated': now.isoformat()
            }
            
            cache.set(cache_key, metrics, self.cache_timeout)
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting real-time metrics: {str(e)}")
            return {'error': str(e)}
    
    def _get_active_users_count(self, start_time: datetime, end_time: datetime) -> int:
        """Get count of active users in time range"""
        return SecurityAuditLog.objects.filter(
            timestamp__range=[start_time, end_time],
            user__isnull=False
        ).values('user').distinct().count()
    
    def _get_ai_requests_per_minute(self, start_time: datetime, end_time: datetime) -> float:
        """Get AI requests per minute"""
        total_requests = AIUsageLog.objects.filter(
            created_at__range=[start_time, end_time]
        ).count()
        
        minutes = (end_time - start_time).total_seconds() / 60
        return round(total_requests / max(minutes, 1), 2)
    
    def _get_current_system_health(self) -> str:
        """Get current system health status"""
        recent_health = HealthCheckResult.objects.filter(
            timestamp__gte=timezone.now() - timedelta(minutes=5)
        ).order_by('-timestamp').first()
        
        if recent_health:
            return recent_health.status
        else:
            return 'unknown'

# Usage Examples:
"""
# Initialize BI Manager
bi_manager = BusinessIntelligenceManager()

# Get dashboard analytics
analytics = bi_manager.get_dashboard_analytics(date_range=30)

# Generate custom report
report = bi_manager.generate_custom_report('cv_performance', {'date_range': 30})

# Export report
json_data = bi_manager.export_report_data(report, 'json')

# Real-time analytics
realtime = RealtimeAnalytics()
current_metrics = realtime.get_realtime_metrics()
""" 