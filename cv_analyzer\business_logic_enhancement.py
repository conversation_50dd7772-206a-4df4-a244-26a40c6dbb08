"""
Business Logic Enhancement Module
Implements business rule engine, workflow management, and audit trails
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union
from enum import Enum
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from django.db import models, transaction
from django.conf import settings
from django.utils import timezone
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError

logger = logging.getLogger(__name__)

class RuleType(Enum):
    VALIDATION = "validation"
    BUSINESS = "business"
    SECURITY = "security"
    NOTIFICATION = "notification"

class WorkflowStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class RuleContext:
    """Context for rule execution"""
    user: Optional[User] = None
    object_instance: Optional[Any] = None
    action: Optional[str] = None
    timestamp: Optional[datetime] = None
    additional_data: Optional[Dict[str, Any]] = None

@dataclass
class RuleResult:
    """Result of rule execution"""
    passed: bool
    message: str
    severity: str = "info"  # info, warning, error, critical
    metadata: Optional[Dict[str, Any]] = None

class BusinessRule(ABC):
    """Abstract base class for business rules"""
    
    def __init__(self, name: str, rule_type: RuleType, priority: int = 5):
        self.name = name
        self.rule_type = rule_type
        self.priority = priority
        self.enabled = True
    
    @abstractmethod
    def evaluate(self, context: RuleContext) -> RuleResult:
        """Evaluate the rule against the given context"""
        pass
    
    def __str__(self):
        return f"{self.name} ({self.rule_type.value})"

class BusinessRuleEngine:
    """Engine for executing business rules"""
    
    def __init__(self):
        self.rules: List[BusinessRule] = []
        self.rule_cache = {}
    
    def register_rule(self, rule: BusinessRule):
        """Register a business rule"""
        self.rules.append(rule)
        # Sort by priority (higher numbers = higher priority)
        self.rules.sort(key=lambda r: r.priority, reverse=True)
        logger.info(f"Registered business rule: {rule.name}")
    
    def unregister_rule(self, rule_name: str):
        """Unregister a business rule"""
        self.rules = [rule for rule in self.rules if rule.name != rule_name]
        logger.info(f"Unregistered business rule: {rule_name}")
    
    def execute_rules(self, rule_type: RuleType, context: RuleContext) -> List[RuleResult]:
        """Execute all rules of a specific type"""
        results = []
        applicable_rules = [rule for rule in self.rules 
                          if rule.rule_type == rule_type and rule.enabled]
        
        for rule in applicable_rules:
            try:
                result = rule.evaluate(context)
                results.append(result)
                
                # Log rule execution
                logger.info(f"Rule '{rule.name}' executed: {result.passed}")
                
                # Stop on critical failures for validation rules
                if (rule.rule_type == RuleType.VALIDATION and 
                    not result.passed and 
                    result.severity == "critical"):
                    break
                    
            except Exception as e:
                error_result = RuleResult(
                    passed=False,
                    message=f"Rule execution failed: {str(e)}",
                    severity="error",
                    metadata={"rule_name": rule.name, "error": str(e)}
                )
                results.append(error_result)
                logger.error(f"Rule '{rule.name}' execution failed: {e}")
        
        return results
    
    def validate_object(self, obj: Any, action: str = "validate", user: User = None) -> List[RuleResult]:
        """Validate an object against all validation rules"""
        context = RuleContext(
            user=user,
            object_instance=obj,
            action=action,
            timestamp=timezone.now()
        )
        return self.execute_rules(RuleType.VALIDATION, context)
    
    def check_business_rules(self, obj: Any, action: str, user: User = None) -> List[RuleResult]:
        """Check business rules for an action"""
        context = RuleContext(
            user=user,
            object_instance=obj,
            action=action,
            timestamp=timezone.now()
        )
        return self.execute_rules(RuleType.BUSINESS, context)

# Specific Business Rules Implementation

class CVUploadValidationRule(BusinessRule):
    """Validates CV upload requirements"""
    
    def __init__(self):
        super().__init__("CV Upload Validation", RuleType.VALIDATION, priority=10)
    
    def evaluate(self, context: RuleContext) -> RuleResult:
        cv = context.object_instance
        
        if not hasattr(cv, 'file') or not cv.file:
            return RuleResult(
                passed=False,
                message="CV file is required",
                severity="critical"
            )
        
        # Check file size
        max_size = getattr(settings, 'MAX_CV_SIZE', 10 * 1024 * 1024)  # 10MB
        if cv.file.size > max_size:
            return RuleResult(
                passed=False,
                message=f"CV file size exceeds limit ({max_size / (1024*1024):.1f}MB)",
                severity="error"
            )
        
        # Check file type
        allowed_types = getattr(settings, 'ALLOWED_CV_TYPES', [])
        if hasattr(cv.file, 'content_type') and cv.file.content_type not in allowed_types:
            return RuleResult(
                passed=False,
                message=f"CV file type not allowed: {cv.file.content_type}",
                severity="error"
            )
        
        return RuleResult(
            passed=True,
            message="CV upload validation passed",
            severity="info"
        )

class UserUploadLimitRule(BusinessRule):
    """Enforces user upload limits"""
    
    def __init__(self):
        super().__init__("User Upload Limit", RuleType.BUSINESS, priority=8)
    
    def evaluate(self, context: RuleContext) -> RuleResult:
        user = context.user
        
        if not user:
            return RuleResult(
                passed=False,
                message="User required for upload limit check",
                severity="error"
            )
        
        # Check daily upload limit
        from .models import CV
        today = timezone.now().date()
        daily_uploads = CV.objects.filter(
            applicant_profile__user=user,
            uploaded_at__date=today
        ).count()
        
        daily_limit = 10 if user.is_premium else 3  # Example limits
        
        if daily_uploads >= daily_limit:
            return RuleResult(
                passed=False,
                message=f"Daily upload limit reached ({daily_limit})",
                severity="warning",
                metadata={"daily_uploads": daily_uploads, "limit": daily_limit}
            )
        
        return RuleResult(
            passed=True,
            message=f"Upload limit check passed ({daily_uploads}/{daily_limit})",
            severity="info",
            metadata={"daily_uploads": daily_uploads, "limit": daily_limit}
        )

class AIAnalysisQueueRule(BusinessRule):
    """Manages AI analysis queue priorities"""
    
    def __init__(self):
        super().__init__("AI Analysis Queue", RuleType.BUSINESS, priority=7)
    
    def evaluate(self, context: RuleContext) -> RuleResult:
        user = context.user
        cv = context.object_instance
        
        # Determine priority based on user type and CV characteristics
        if user and user.is_premium:
            priority = "high"
        elif hasattr(cv, 'urgent') and cv.urgent:
            priority = "high"
        else:
            priority = "normal"
        
        # Check system load
        from .task_queue_optimization import get_queue_statistics
        queue_stats = get_queue_statistics()
        active_tasks = queue_stats.get('active_tasks_count', 0)
        
        if active_tasks > 50:  # High load
            if priority == "normal":
                return RuleResult(
                    passed=False,
                    message="System under high load. Please try again later.",
                    severity="warning",
                    metadata={"queue_priority": priority, "system_load": "high"}
                )
        
        return RuleResult(
            passed=True,
            message=f"Analysis request accepted with {priority} priority",
            severity="info",
            metadata={"queue_priority": priority, "active_tasks": active_tasks}
        )

class DataRetentionRule(BusinessRule):
    """Enforces data retention policies"""
    
    def __init__(self):
        super().__init__("Data Retention Policy", RuleType.BUSINESS, priority=6)
    
    def evaluate(self, context: RuleContext) -> RuleResult:
        obj = context.object_instance
        
        # Check if object should be retained based on age and type
        retention_days = 365  # Default 1 year
        
        if hasattr(obj, 'created_at'):
            age = timezone.now() - obj.created_at
            
            if age.days > retention_days:
                return RuleResult(
                    passed=False,
                    message=f"Object exceeds retention period ({retention_days} days)",
                    severity="warning",
                    metadata={"age_days": age.days, "retention_days": retention_days}
                )
        
        return RuleResult(
            passed=True,
            message="Data retention policy satisfied",
            severity="info"
        )

class SecurityAccessRule(BusinessRule):
    """Enforces security access controls"""
    
    def __init__(self):
        super().__init__("Security Access Control", RuleType.SECURITY, priority=9)
    
    def evaluate(self, context: RuleContext) -> RuleResult:
        user = context.user
        obj = context.object_instance
        action = context.action
        
        if not user:
            return RuleResult(
                passed=False,
                message="Authentication required",
                severity="critical"
            )
        
        # Check if user can perform action on object
        if action == "delete" and hasattr(obj, 'applicant_profile'):
            if obj.applicant_profile.user != user and not user.is_staff:
                return RuleResult(
                    passed=False,
                    message="Insufficient permissions to delete this object",
                    severity="critical"
                )
        
        # Check for suspicious activity
        if self._is_suspicious_activity(user, action):
            return RuleResult(
                passed=False,
                message="Suspicious activity detected",
                severity="critical",
                metadata={"user_id": user.id, "action": action}
            )
        
        return RuleResult(
            passed=True,
            message="Security access control passed",
            severity="info"
        )
    
    def _is_suspicious_activity(self, user: User, action: str) -> bool:
        """Detect suspicious user activity"""
        # Simple rate limiting check
        from .models import SecurityAuditLog
        
        recent_actions = SecurityAuditLog.objects.filter(
            user=user,
            timestamp__gte=timezone.now() - timedelta(minutes=5)
        ).count()
        
        return recent_actions > 20  # More than 20 actions in 5 minutes

class WorkflowEngine:
    """Engine for managing business workflows"""
    
    def __init__(self):
        self.workflows = {}
        self.active_workflows = {}
    
    def register_workflow(self, workflow_name: str, workflow_definition: Dict[str, Any]):
        """Register a workflow definition"""
        self.workflows[workflow_name] = workflow_definition
        logger.info(f"Registered workflow: {workflow_name}")
    
    def start_workflow(self, workflow_name: str, context: Dict[str, Any], 
                      initiated_by: User = None) -> str:
        """Start a workflow instance"""
        if workflow_name not in self.workflows:
            raise ValueError(f"Workflow not found: {workflow_name}")
        
        workflow_id = f"{workflow_name}_{timezone.now().timestamp()}"
        
        workflow_instance = {
            'id': workflow_id,
            'name': workflow_name,
            'status': WorkflowStatus.PENDING,
            'context': context,
            'initiated_by': initiated_by.id if initiated_by else None,
            'started_at': timezone.now(),
            'current_step': 0,
            'steps_completed': [],
            'error_message': None
        }
        
        self.active_workflows[workflow_id] = workflow_instance
        
        # Start execution
        self._execute_next_step(workflow_id)
        
        return workflow_id
    
    def _execute_next_step(self, workflow_id: str):
        """Execute the next step in a workflow"""
        workflow_instance = self.active_workflows.get(workflow_id)
        if not workflow_instance:
            return
        
        workflow_definition = self.workflows[workflow_instance['name']]
        steps = workflow_definition.get('steps', [])
        current_step = workflow_instance['current_step']
        
        if current_step >= len(steps):
            # Workflow completed
            workflow_instance['status'] = WorkflowStatus.COMPLETED
            workflow_instance['completed_at'] = timezone.now()
            return
        
        try:
            step = steps[current_step]
            step_name = step.get('name', f"Step {current_step + 1}")
            
            workflow_instance['status'] = WorkflowStatus.IN_PROGRESS
            
            # Execute step
            step_result = self._execute_step(step, workflow_instance['context'])
            
            if step_result.get('success', False):
                workflow_instance['steps_completed'].append({
                    'step_name': step_name,
                    'completed_at': timezone.now(),
                    'result': step_result
                })
                workflow_instance['current_step'] += 1
                
                # Execute next step
                self._execute_next_step(workflow_id)
            else:
                # Step failed
                workflow_instance['status'] = WorkflowStatus.FAILED
                workflow_instance['error_message'] = step_result.get('error', 'Step execution failed')
                
        except Exception as e:
            workflow_instance['status'] = WorkflowStatus.FAILED
            workflow_instance['error_message'] = str(e)
            logger.error(f"Workflow {workflow_id} failed: {e}")
    
    def _execute_step(self, step: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single workflow step"""
        step_type = step.get('type')
        
        if step_type == 'validation':
            return self._execute_validation_step(step, context)
        elif step_type == 'business_rule':
            return self._execute_business_rule_step(step, context)
        elif step_type == 'notification':
            return self._execute_notification_step(step, context)
        elif step_type == 'task':
            return self._execute_task_step(step, context)
        else:
            return {'success': False, 'error': f'Unknown step type: {step_type}'}
    
    def _execute_validation_step(self, step: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute validation step"""
        # Implementation for validation steps
        return {'success': True, 'message': 'Validation completed'}
    
    def _execute_business_rule_step(self, step: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute business rule step"""
        # Implementation for business rule steps
        return {'success': True, 'message': 'Business rule executed'}
    
    def _execute_notification_step(self, step: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute notification step"""
        # Implementation for notification steps
        return {'success': True, 'message': 'Notification sent'}
    
    def _execute_task_step(self, step: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task step"""
        # Implementation for task execution steps
        return {'success': True, 'message': 'Task executed'}
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get workflow status"""
        return self.active_workflows.get(workflow_id)

class AuditTrailManager:
    """Manages audit trails for business operations"""
    
    def __init__(self):
        self.audit_enabled = True
    
    def log_business_event(self, event_type: str, entity_type: str, entity_id: int,
                          user: User = None, details: Dict[str, Any] = None):
        """Log a business event for audit purposes"""
        if not self.audit_enabled:
            return
        
        from .models import BusinessAuditLog
        
        try:
            audit_entry = BusinessAuditLog.objects.create(
                event_type=event_type,
                entity_type=entity_type,
                entity_id=entity_id,
                user=user,
                details=details or {},
                timestamp=timezone.now()
            )
            
            logger.info(f"Business event logged: {event_type} for {entity_type}:{entity_id}")
            return audit_entry.id
            
        except Exception as e:
            logger.error(f"Failed to log business event: {e}")
            return None
    
    def get_audit_trail(self, entity_type: str, entity_id: int) -> List[Dict[str, Any]]:
        """Get audit trail for an entity"""
        from .models import BusinessAuditLog
        
        audit_logs = BusinessAuditLog.objects.filter(
            entity_type=entity_type,
            entity_id=entity_id
        ).order_by('-timestamp')
        
        return [
            {
                'id': log.id,
                'event_type': log.event_type,
                'user': log.user.username if log.user else None,
                'timestamp': log.timestamp,
                'details': log.details
            }
            for log in audit_logs
        ]

# Global instances
business_rule_engine = BusinessRuleEngine()
workflow_engine = WorkflowEngine()
audit_trail_manager = AuditTrailManager()

# Initialize default rules
def initialize_default_rules():
    """Initialize default business rules"""
    rules = [
        CVUploadValidationRule(),
        UserUploadLimitRule(),
        AIAnalysisQueueRule(),
        DataRetentionRule(),
        SecurityAccessRule()
    ]
    
    for rule in rules:
        business_rule_engine.register_rule(rule)

# Utility functions
def validate_business_object(obj: Any, action: str = "validate", user: User = None) -> List[RuleResult]:
    """Validate a business object against all rules"""
    return business_rule_engine.validate_object(obj, action, user)

def execute_business_rules(obj: Any, action: str, user: User = None) -> List[RuleResult]:
    """Execute business rules for an action"""
    return business_rule_engine.check_business_rules(obj, action, user)

def start_business_workflow(workflow_name: str, context: Dict[str, Any], user: User = None) -> str:
    """Start a business workflow"""
    return workflow_engine.start_workflow(workflow_name, context, user)

def log_business_event(event_type: str, entity_type: str, entity_id: int,
                      user: User = None, details: Dict[str, Any] = None):
    """Log a business event"""
    return audit_trail_manager.log_business_event(event_type, entity_type, entity_id, user, details)

def get_entity_audit_trail(entity_type: str, entity_id: int) -> List[Dict[str, Any]]:
    """Get audit trail for an entity"""
    return audit_trail_manager.get_audit_trail(entity_type, entity_id) 