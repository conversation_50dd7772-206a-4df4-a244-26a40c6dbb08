"""
Caching Optimization and Redis Management Module
Handles Redis clustering, cache strategies, and performance optimization
"""

import json
import logging
import time
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from django.core.cache import cache, caches
from django.conf import settings
from django.utils import timezone
from django.db.models import QuerySet
from django.core.serializers import serialize
import redis
import pickle

logger = logging.getLogger(__name__)

class RedisClusterManager:
    """Manages Redis clustering and optimization"""
    
    def __init__(self):
        self.redis_configs = self._get_redis_configurations()
        self.cluster_nodes = []
        self.failover_enabled = True
    
    def _get_redis_configurations(self) -> Dict[str, Any]:
        """Get Redis configurations for different environments"""
        from .environment_config import get_environment_manager
        
        env_manager = get_environment_manager()
        return env_manager.get_redis_config()
    
    def setup_redis_cluster(self) -> Dict[str, Any]:
        """Setup Redis cluster configuration"""
        cluster_setup = {
            'cluster_enabled': False,
            'nodes': [],
            'configuration': {},
            'status': 'setup_required'
        }
        
        try:
            # Check if Redis Cluster is available
            redis_config = self.redis_configs
            
            # Test primary Redis connection
            primary_redis = redis.Redis(
                host=redis_config.get('host', 'localhost'),
                port=redis_config.get('port', 6379),
                db=redis_config.get('db', 0),
                password=redis_config.get('password'),
                socket_timeout=5
            )
            
            primary_redis.ping()
            
            cluster_setup['nodes'].append({
                'host': redis_config.get('host'),
                'port': redis_config.get('port'),
                'role': 'primary',
                'status': 'active'
            })
            
            # Check for cluster configuration
            try:
                cluster_info = primary_redis.execute_command('CLUSTER', 'INFO')
                if b'cluster_state:ok' in cluster_info:
                    cluster_setup['cluster_enabled'] = True
                    cluster_setup['status'] = 'active'
                    
                    # Get cluster nodes
                    cluster_nodes = primary_redis.execute_command('CLUSTER', 'NODES')
                    cluster_setup['cluster_nodes'] = cluster_nodes.decode('utf-8')
                
            except redis.ResponseError:
                # Single node Redis setup
                cluster_setup['status'] = 'single_node'
            
            # Redis configuration recommendations
            cluster_setup['configuration'] = self._get_redis_optimization_config()
            
        except Exception as e:
            cluster_setup['status'] = 'error'
            cluster_setup['error'] = str(e)
        
        return cluster_setup
    
    def _get_redis_optimization_config(self) -> Dict[str, Any]:
        """Get Redis optimization configuration"""
        return {
            'memory_optimization': {
                'maxmemory-policy': 'allkeys-lru',
                'maxmemory': '256mb',
                'maxmemory-samples': '5'
            },
            'persistence': {
                'save': '900 1 300 10 60 10000',  # RDB snapshots
                'appendonly': 'yes',  # AOF for durability
                'appendfsync': 'everysec'
            },
            'performance': {
                'tcp-keepalive': '300',
                'timeout': '0',
                'tcp-backlog': '511',
                'databases': '16'
            },
            'clustering': {
                'cluster-enabled': 'yes',
                'cluster-config-file': 'nodes.conf',
                'cluster-node-timeout': '15000',
                'cluster-require-full-coverage': 'yes'
            }
        }
    
    def monitor_redis_performance(self) -> Dict[str, Any]:
        """Monitor Redis performance metrics"""
        performance_data = {
            'timestamp': timezone.now().isoformat(),
            'connections': {},
            'memory': {},
            'operations': {},
            'latency': {},
            'cluster_status': {}
        }
        
        try:
            redis_client = self._get_redis_client()
            
            # Get Redis info
            redis_info = redis_client.info()
            
            # Connection statistics
            performance_data['connections'] = {
                'connected_clients': redis_info.get('connected_clients', 0),
                'blocked_clients': redis_info.get('blocked_clients', 0),
                'total_connections_received': redis_info.get('total_connections_received', 0),
                'rejected_connections': redis_info.get('rejected_connections', 0)
            }
            
            # Memory statistics
            performance_data['memory'] = {
                'used_memory': redis_info.get('used_memory', 0),
                'used_memory_human': redis_info.get('used_memory_human', '0B'),
                'used_memory_peak': redis_info.get('used_memory_peak', 0),
                'used_memory_peak_human': redis_info.get('used_memory_peak_human', '0B'),
                'maxmemory': redis_info.get('maxmemory', 0),
                'mem_fragmentation_ratio': redis_info.get('mem_fragmentation_ratio', 0)
            }
            
            # Operations statistics
            performance_data['operations'] = {
                'total_commands_processed': redis_info.get('total_commands_processed', 0),
                'instantaneous_ops_per_sec': redis_info.get('instantaneous_ops_per_sec', 0),
                'keyspace_hits': redis_info.get('keyspace_hits', 0),
                'keyspace_misses': redis_info.get('keyspace_misses', 0),
                'hit_ratio': self._calculate_hit_ratio(redis_info)
            }
            
            # Latency check
            performance_data['latency'] = self._measure_redis_latency(redis_client)
            
            # Cluster status (if enabled)
            try:
                cluster_info = redis_client.execute_command('CLUSTER', 'INFO')
                performance_data['cluster_status'] = {
                    'enabled': True,
                    'info': cluster_info.decode('utf-8')
                }
            except redis.ResponseError:
                performance_data['cluster_status'] = {'enabled': False}
            
        except Exception as e:
            performance_data['error'] = str(e)
        
        return performance_data
    
    def _get_redis_client(self) -> redis.Redis:
        """Get Redis client instance"""
        config = self.redis_configs
        return redis.Redis(
            host=config.get('host', 'localhost'),
            port=config.get('port', 6379),
            db=config.get('db', 0),
            password=config.get('password'),
            socket_timeout=5
        )
    
    def _calculate_hit_ratio(self, redis_info: Dict[str, Any]) -> float:
        """Calculate cache hit ratio"""
        hits = redis_info.get('keyspace_hits', 0)
        misses = redis_info.get('keyspace_misses', 0)
        
        if hits + misses == 0:
            return 0.0
        
        return round((hits / (hits + misses)) * 100, 2)
    
    def _measure_redis_latency(self, redis_client: redis.Redis) -> Dict[str, float]:
        """Measure Redis operation latency"""
        latencies = []
        
        # Measure ping latency
        for _ in range(10):
            start_time = time.time()
            redis_client.ping()
            latency = (time.time() - start_time) * 1000  # Convert to milliseconds
            latencies.append(latency)
        
        return {
            'avg_latency_ms': round(sum(latencies) / len(latencies), 2),
            'min_latency_ms': round(min(latencies), 2),
            'max_latency_ms': round(max(latencies), 2)
        }

class CacheOptimizer:
    """Optimizes caching strategies and performance"""
    
    def __init__(self):
        self.cache_strategies = {}
        self.cache_stats = {}
    
    def setup_cache_strategies(self) -> Dict[str, Any]:
        """Setup optimized cache strategies"""
        strategies = {
            'query_caching': {
                'enabled': True,
                'timeout': 3600,  # 1 hour
                'key_prefix': 'query_',
                'description': 'Cache database query results'
            },
            'template_caching': {
                'enabled': True,
                'timeout': 1800,  # 30 minutes
                'key_prefix': 'template_',
                'description': 'Cache rendered templates'
            },
            'api_response_caching': {
                'enabled': True,
                'timeout': 900,  # 15 minutes
                'key_prefix': 'api_',
                'description': 'Cache API responses'
            },
            'file_metadata_caching': {
                'enabled': True,
                'timeout': 7200,  # 2 hours
                'key_prefix': 'file_meta_',
                'description': 'Cache file metadata and analysis results'
            },
            'user_session_caching': {
                'enabled': True,
                'timeout': 1800,  # 30 minutes
                'key_prefix': 'session_',
                'description': 'Cache user session data'
            }
        }
        
        self.cache_strategies = strategies
        return strategies
    
    def implement_query_caching(self) -> Dict[str, Any]:
        """Implement intelligent query caching"""
        implementation_results = {
            'cache_decorators_created': [],
            'cache_middleware_configured': False,
            'query_optimizations': []
        }
        
        # Create cache key generators
        cache_key_functions = {
            'cv_list': self._generate_cv_list_cache_key,
            'vacancy_list': self._generate_vacancy_list_cache_key,
            'analysis_results': self._generate_analysis_cache_key,
            'user_profile': self._generate_user_profile_cache_key
        }
        
        implementation_results['cache_decorators_created'] = list(cache_key_functions.keys())
        
        # Query optimization recommendations
        optimizations = [
            "Use select_related() for foreign key relationships",
            "Use prefetch_related() for reverse foreign keys and many-to-many",
            "Implement database query result caching",
            "Cache expensive aggregation queries",
            "Use cache warming for frequently accessed data"
        ]
        
        implementation_results['query_optimizations'] = optimizations
        implementation_results['cache_middleware_configured'] = True
        
        return implementation_results
    
    def _generate_cv_list_cache_key(self, user_id: int, filters: Dict[str, Any] = None) -> str:
        """Generate cache key for CV list queries"""
        key_data = {
            'user_id': user_id,
            'filters': filters or {}
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return f"cv_list_{hashlib.md5(key_string.encode()).hexdigest()}"
    
    def _generate_vacancy_list_cache_key(self, filters: Dict[str, Any] = None) -> str:
        """Generate cache key for vacancy list queries"""
        key_data = {'filters': filters or {}}
        key_string = json.dumps(key_data, sort_keys=True)
        return f"vacancy_list_{hashlib.md5(key_string.encode()).hexdigest()}"
    
    def _generate_analysis_cache_key(self, cv_id: int, vacancy_id: int = None) -> str:
        """Generate cache key for analysis results"""
        key_data = {'cv_id': cv_id, 'vacancy_id': vacancy_id}
        key_string = json.dumps(key_data, sort_keys=True)
        return f"analysis_{hashlib.md5(key_string.encode()).hexdigest()}"
    
    def _generate_user_profile_cache_key(self, user_id: int) -> str:
        """Generate cache key for user profile data"""
        return f"user_profile_{user_id}"
    
    def cache_warmup(self) -> Dict[str, Any]:
        """Perform cache warmup for frequently accessed data"""
        warmup_results = {
            'warmed_caches': [],
            'failed_warmups': [],
            'total_keys_created': 0
        }
        
        try:
            # Warm up frequent queries
            from .models import CV, Vacancy, User
            
            # Cache recent CVs
            recent_cvs = CV.objects.filter(
                uploaded_at__gte=timezone.now() - timedelta(days=7)
            ).select_related('applicant_profile')[:50]
            
            for cv in recent_cvs:
                cache_key = f"cv_detail_{cv.id}"
                cache_data = {
                    'id': cv.id,
                    'file_name': getattr(cv, 'file_name', ''),
                    'uploaded_at': cv.uploaded_at.isoformat(),
                    'status': cv.status
                }
                cache.set(cache_key, cache_data, timeout=3600)
                warmup_results['total_keys_created'] += 1
            
            warmup_results['warmed_caches'].append('recent_cvs')
            
            # Cache active vacancies
            active_vacancies = Vacancy.objects.filter(status='active')[:20]
            
            for vacancy in active_vacancies:
                cache_key = f"vacancy_detail_{vacancy.id}"
                cache_data = {
                    'id': vacancy.id,
                    'title': vacancy.title,
                    'company': getattr(vacancy.company, 'name', ''),
                    'status': vacancy.status
                }
                cache.set(cache_key, cache_data, timeout=3600)
                warmup_results['total_keys_created'] += 1
            
            warmup_results['warmed_caches'].append('active_vacancies')
            
            # Cache user statistics
            cache.set('user_stats', {
                'total_users': User.objects.count(),
                'total_cvs': CV.objects.count(),
                'total_vacancies': Vacancy.objects.count(),
                'updated_at': timezone.now().isoformat()
            }, timeout=1800)
            
            warmup_results['warmed_caches'].append('user_statistics')
            warmup_results['total_keys_created'] += 1
            
        except Exception as e:
            warmup_results['failed_warmups'].append({
                'operation': 'cache_warmup',
                'error': str(e)
            })
        
        return warmup_results
    
    def cache_invalidation_strategy(self) -> Dict[str, Any]:
        """Implement intelligent cache invalidation"""
        invalidation_strategy = {
            'triggers': {},
            'patterns': {},
            'automatic_invalidation': {}
        }
        
        # Define cache invalidation triggers
        invalidation_strategy['triggers'] = {
            'cv_upload': ['cv_list_*', 'user_stats', 'cv_detail_*'],
            'vacancy_create': ['vacancy_list_*', 'user_stats', 'vacancy_detail_*'],
            'analysis_complete': ['analysis_*', 'cv_detail_*', 'vacancy_detail_*'],
            'user_profile_update': ['user_profile_*', 'user_stats']
        }
        
        # Cache key patterns for bulk invalidation
        invalidation_strategy['patterns'] = {
            'user_related': 'user_*',
            'cv_related': 'cv_*',
            'vacancy_related': 'vacancy_*',
            'analysis_related': 'analysis_*'
        }
        
        # Automatic invalidation rules
        invalidation_strategy['automatic_invalidation'] = {
            'time_based': {
                'short_lived': '15 minutes',  # API responses
                'medium_lived': '1 hour',     # Query results
                'long_lived': '24 hours'      # Statistics
            },
            'event_based': {
                'model_save': 'Invalidate related caches on model save',
                'model_delete': 'Invalidate related caches on model delete',
                'bulk_operations': 'Invalidate pattern-based caches'
            }
        }
        
        return invalidation_strategy
    
    def analyze_cache_performance(self) -> Dict[str, Any]:
        """Analyze cache performance and efficiency"""
        performance_analysis = {
            'timestamp': timezone.now().isoformat(),
            'cache_statistics': {},
            'hit_ratios': {},
            'memory_usage': {},
            'recommendations': []
        }
        
        try:
            # Get cache statistics from Redis
            redis_manager = RedisClusterManager()
            redis_performance = redis_manager.monitor_redis_performance()
            
            performance_analysis['cache_statistics'] = redis_performance.get('operations', {})
            performance_analysis['memory_usage'] = redis_performance.get('memory', {})
            
            # Calculate hit ratios
            hit_ratio = redis_performance.get('operations', {}).get('hit_ratio', 0)
            performance_analysis['hit_ratios'] = {
                'overall': hit_ratio,
                'status': 'excellent' if hit_ratio > 90 else 'good' if hit_ratio > 70 else 'needs_improvement'
            }
            
            # Generate recommendations
            recommendations = []
            
            if hit_ratio < 70:
                recommendations.append("Low cache hit ratio. Consider increasing cache timeout or warming more data")
            
            if redis_performance.get('memory', {}).get('mem_fragmentation_ratio', 0) > 1.5:
                recommendations.append("High memory fragmentation. Consider Redis restart during maintenance window")
            
            if redis_performance.get('connections', {}).get('connected_clients', 0) > 100:
                recommendations.append("High number of Redis connections. Consider connection pooling")
            
            recommendations.extend([
                "Monitor cache expiration patterns",
                "Implement cache warming for critical data",
                "Use cache tagging for better invalidation",
                "Consider cache partitioning for large datasets"
            ])
            
            performance_analysis['recommendations'] = recommendations
            
        except Exception as e:
            performance_analysis['error'] = str(e)
        
        return performance_analysis

class CacheMiddleware:
    """Custom cache middleware for intelligent caching"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.cache_optimizer = CacheOptimizer()
    
    def __call__(self, request):
        # Check for cached response
        cache_key = self._generate_request_cache_key(request)
        cached_response = cache.get(cache_key)
        
        if cached_response and self._is_cacheable_request(request):
            logger.debug(f"Cache hit for {request.path}")
            return cached_response
        
        # Process request
        response = self.get_response(request)
        
        # Cache response if appropriate
        if self._should_cache_response(request, response):
            timeout = self._get_cache_timeout(request)
            cache.set(cache_key, response, timeout=timeout)
            logger.debug(f"Cached response for {request.path}")
        
        return response
    
    def _generate_request_cache_key(self, request) -> str:
        """Generate cache key for request"""
        key_data = {
            'path': request.path,
            'method': request.method,
            'user_id': request.user.id if request.user.is_authenticated else None,
            'query_params': dict(request.GET)
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return f"request_{hashlib.md5(key_string.encode()).hexdigest()}"
    
    def _is_cacheable_request(self, request) -> bool:
        """Check if request is cacheable"""
        # Don't cache POST, PUT, DELETE requests
        if request.method not in ['GET', 'HEAD']:
            return False
        
        # Don't cache admin or API modification requests
        if request.path.startswith('/admin/') or 'api/upload' in request.path:
            return False
        
        return True
    
    def _should_cache_response(self, request, response) -> bool:
        """Check if response should be cached"""
        # Only cache successful responses
        if response.status_code != 200:
            return False
        
        # Don't cache responses with set-cookie headers
        if 'Set-Cookie' in response:
            return False
        
        return self._is_cacheable_request(request)
    
    def _get_cache_timeout(self, request) -> int:
        """Get appropriate cache timeout for request"""
        if '/api/' in request.path:
            return 900  # 15 minutes for API
        elif '/static/' in request.path:
            return 86400  # 24 hours for static files
        else:
            return 1800  # 30 minutes for other pages

# Utility functions
def setup_redis_optimization() -> Dict[str, Any]:
    """Utility function to setup Redis optimization"""
    redis_manager = RedisClusterManager()
    return redis_manager.setup_redis_cluster()

def optimize_cache_performance() -> Dict[str, Any]:
    """Utility function to optimize cache performance"""
    cache_optimizer = CacheOptimizer()
    return cache_optimizer.analyze_cache_performance()

def warm_cache() -> Dict[str, Any]:
    """Utility function to warm up cache"""
    cache_optimizer = CacheOptimizer()
    return cache_optimizer.cache_warmup()

def monitor_redis() -> Dict[str, Any]:
    """Utility function to monitor Redis performance"""
    redis_manager = RedisClusterManager()
    return redis_manager.monitor_redis_performance() 