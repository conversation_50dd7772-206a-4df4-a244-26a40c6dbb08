import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cv_analyzer.settings')

# Create the Celery app
app = Celery('cv_analyzer')

# Configure Celery using Django settings
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load tasks from all registered Django app configs
app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)

# Configure task routes
app.conf.task_routes = {
    'cv_analyzer.tasks.analyze_cv': {'queue': 'analysis'},
}

# Configure task time limits
app.conf.task_time_limit = 600  # 10 minutes
app.conf.task_soft_time_limit = 300  # 5 minutes

# Configure task retries
app.conf.task_max_retries = 3
app.conf.task_retry_delay = 60  # 1 minute

# Configure result backend
app.conf.result_expires = 60 * 60 * 24  # 24 hours

# Configure task serialization
app.conf.task_serializer = 'json'
app.conf.result_serializer = 'json'
app.conf.accept_content = ['json']

# Configure task queues
app.conf.task_queues = {
    'analysis': {
        'exchange': 'analysis',
        'routing_key': 'analysis',
    },
    'default': {
        'exchange': 'default',
        'routing_key': 'default',
    },
}

# Configure task default queue
app.conf.task_default_queue = 'default'

# Configure task priority
app.conf.task_queue_max_priority = 10
app.conf.task_default_priority = 5

# Configure task rate limits
app.conf.task_annotations = {
    'cv_analyzer.tasks.analyze_cv': {
        'rate_limit': '10/m'  # 10 tasks per minute
    },
}

# Configure task error handling
app.conf.task_reject_on_worker_lost = True
app.conf.task_acks_late = True 