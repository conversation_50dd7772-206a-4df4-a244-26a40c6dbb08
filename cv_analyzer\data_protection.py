"""
Data Protection and GDPR Compliance Module
Implements encryption, anonymization, and compliance utilities
"""

import hashlib
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from cryptography.fernet import Fernet
from django.conf import settings
from django.core.mail import send_mail
from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User

logger = logging.getLogger(__name__)

class DataEncryption:
    """Handles field-level encryption for sensitive data"""
    
    def __init__(self):
        # Get encryption key from settings or generate one
        if hasattr(settings, 'DATA_ENCRYPTION_KEY'):
            self.key = settings.DATA_ENCRYPTION_KEY.encode()
        else:
            # Generate a key for development (should be from environment in production)
            self.key = Fernet.generate_key()
            logger.warning("Using generated encryption key. Set DATA_ENCRYPTION_KEY in production!")
        
        self.cipher = Fernet(self.key)
    
    def encrypt_field(self, value: str) -> str:
        """Encrypt a field value"""
        if not value:
            return value
        
        try:
            encrypted_value = self.cipher.encrypt(value.encode())
            return encrypted_value.decode()
        except Exception as e:
            logger.error(f"Encryption error: {str(e)}")
            raise
    
    def decrypt_field(self, encrypted_value: str) -> str:
        """Decrypt a field value"""
        if not encrypted_value:
            return encrypted_value
        
        try:
            decrypted_value = self.cipher.decrypt(encrypted_value.encode())
            return decrypted_value.decode()
        except Exception as e:
            logger.error(f"Decryption error: {str(e)}")
            raise
    
    def hash_field(self, value: str) -> str:
        """Create a hash of sensitive data for indexing"""
        if not value:
            return value
        
        return hashlib.sha256(value.encode()).hexdigest()

class DataAnonymizer:
    """Handles data anonymization for GDPR compliance"""
    
    @staticmethod
    def anonymize_email(email: str) -> str:
        """Anonymize email address"""
        if '@' in email:
            local, domain = email.split('@', 1)
            anonymized_local = 'user_' + hashlib.md5(local.encode()).hexdigest()[:8]
            return f"{anonymized_local}@{domain}"
        return 'anonymized_email'
    
    @staticmethod
    def anonymize_name(name: str) -> str:
        """Anonymize personal names"""
        if not name:
            return name
        
        return 'Anonymized_' + hashlib.md5(name.encode()).hexdigest()[:8]
    
    @staticmethod
    def anonymize_phone(phone: str) -> str:
        """Anonymize phone numbers"""
        if not phone:
            return phone
        
        return 'XXX-XXX-' + phone[-4:] if len(phone) >= 4 else 'XXX-XXX-XXXX'
    
    @staticmethod
    def anonymize_cv_content(content: str) -> str:
        """Remove personally identifiable information from CV content"""
        if not content:
            return content
        
        # Replace common PII patterns
        import re
        
        # Email patterns
        content = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', 
                        '[EMAIL_REDACTED]', content)
        
        # Phone patterns
        content = re.sub(r'\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b', 
                        '[PHONE_REDACTED]', content)
        
        # Address patterns (basic)
        content = re.sub(r'\b\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Lane|Ln)\b', 
                        '[ADDRESS_REDACTED]', content, flags=re.IGNORECASE)
        
        return content

class GDPRCompliance:
    """GDPR compliance utilities"""
    
    def __init__(self):
        self.anonymizer = DataAnonymizer()
        self.encryption = DataEncryption()
    
    def record_consent(self, user: User, consent_type: str, consent_given: bool):
        """Record user consent for data processing"""
        from .models import DataProcessingConsent
        
        consent = DataProcessingConsent.objects.create(
            user=user,
            consent_type=consent_type,
            consent_given=consent_given,
            consent_date=timezone.now(),
            consent_ip=getattr(user, 'current_ip', None),
            consent_user_agent=getattr(user, 'current_user_agent', None)
        )
        
        logger.info(f"GDPR consent recorded: User {user.id}, Type: {consent_type}, Given: {consent_given}")
        return consent
    
    def process_data_subject_request(self, user: User, request_type: str) -> Dict[str, Any]:
        """Process GDPR data subject requests"""
        from .models import DataSubjectRequest
        
        request = DataSubjectRequest.objects.create(
            user=user,
            request_type=request_type,
            request_date=timezone.now(),
            status='pending'
        )
        
        result = {
            'request_id': request.id,
            'request_type': request_type,
            'status': 'pending',
            'data': {}
        }
        
        if request_type == 'access':
            result['data'] = self._export_user_data(user)
        elif request_type == 'portability':
            result['data'] = self._export_portable_data(user)
        elif request_type == 'erasure':
            result['data'] = self._prepare_erasure_plan(user)
        elif request_type == 'rectification':
            result['data'] = self._get_rectification_options(user)
        
        request.response_data = json.dumps(result['data'])
        request.save()
        
        logger.info(f"GDPR request processed: User {user.id}, Type: {request_type}")
        return result
    
    def _export_user_data(self, user: User) -> Dict[str, Any]:
        """Export all user data for GDPR access request"""
        from .models import CV, Vacancy, AnalysisResult
        
        data = {
            'user_profile': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'date_joined': user.date_joined.isoformat(),
                'last_login': user.last_login.isoformat() if user.last_login else None,
            },
            'cvs': [],
            'vacancies': [],
            'analysis_results': []
        }
        
        # Export CVs
        for cv in CV.objects.filter(user=user):
            data['cvs'].append({
                'id': cv.id,
                'file_name': cv.file_name,
                'upload_date': cv.upload_date.isoformat(),
                'file_size': cv.file_size,
                'extracted_text': cv.extracted_text[:500] + '...' if cv.extracted_text else None
            })
        
        # Export Vacancies
        for vacancy in Vacancy.objects.filter(user=user):
            data['vacancies'].append({
                'id': vacancy.id,
                'title': vacancy.title,
                'company': vacancy.company,
                'description': vacancy.description[:500] + '...' if vacancy.description else None,
                'created_at': vacancy.created_at.isoformat()
            })
        
        # Export Analysis Results
        for result in AnalysisResult.objects.filter(cv__user=user):
            data['analysis_results'].append({
                'id': result.id,
                'cv_id': result.cv.id,
                'vacancy_id': result.vacancy.id if result.vacancy else None,
                'analysis_date': result.analysis_date.isoformat(),
                'score': result.score,
                'feedback': result.feedback[:200] + '...' if result.feedback else None
            })
        
        return data
    
    def _export_portable_data(self, user: User) -> Dict[str, Any]:
        """Export data in portable format"""
        # Similar to _export_user_data but in a more portable format
        return self._export_user_data(user)
    
    def _prepare_erasure_plan(self, user: User) -> Dict[str, Any]:
        """Prepare plan for data erasure"""
        from .models import CV, Vacancy, AnalysisResult
        
        erasure_plan = {
            'user_data': True,
            'cvs_count': CV.objects.filter(user=user).count(),
            'vacancies_count': Vacancy.objects.filter(user=user).count(),
            'analysis_results_count': AnalysisResult.objects.filter(cv__user=user).count(),
            'estimated_completion': (timezone.now() + timedelta(days=30)).isoformat(),
            'retention_requirements': [
                'Legal obligations may require retention of certain financial records',
                'Analysis results may be retained in anonymized form for service improvement'
            ]
        }
        
        return erasure_plan
    
    def _get_rectification_options(self, user: User) -> Dict[str, Any]:
        """Get options for data rectification"""
        return {
            'editable_fields': [
                'email', 'first_name', 'last_name', 'profile_settings'
            ],
            'contact_required': [
                'username', 'historical_data'
            ],
            'instructions': 'Users can update most fields through their profile. Contact support for username changes.'
        }
    
    def anonymize_user_data(self, user: User) -> Dict[str, Any]:
        """Anonymize user data for GDPR erasure"""
        from .models import CV, Vacancy
        
        # Anonymize user profile
        user.email = self.anonymizer.anonymize_email(user.email)
        user.first_name = self.anonymizer.anonymize_name(user.first_name)
        user.last_name = self.anonymizer.anonymize_name(user.last_name)
        user.username = f"anonymous_user_{user.id}"
        user.save()
        
        # Anonymize CVs
        cv_count = 0
        for cv in CV.objects.filter(user=user):
            if cv.extracted_text:
                cv.extracted_text = self.anonymizer.anonymize_cv_content(cv.extracted_text)
                cv.file_name = f"anonymized_cv_{cv.id}.pdf"
                cv.save()
                cv_count += 1
        
        # Anonymize vacancies
        vacancy_count = 0
        for vacancy in Vacancy.objects.filter(user=user):
            vacancy.title = f"Anonymized Position {vacancy.id}"
            vacancy.company = f"Anonymized Company {vacancy.id}"
            vacancy.save()
            vacancy_count += 1
        
        result = {
            'user_anonymized': True,
            'cvs_anonymized': cv_count,
            'vacancies_anonymized': vacancy_count,
            'anonymization_date': timezone.now().isoformat()
        }
        
        logger.info(f"User data anonymized: User {user.id}")
        return result

class DataRetentionManager:
    """Manages data retention policies"""
    
    def __init__(self):
        self.retention_policies = {
            'cv_files': timedelta(days=365 * 2),  # 2 years
            'analysis_results': timedelta(days=365 * 1),  # 1 year
            'audit_logs': timedelta(days=365 * 7),  # 7 years
            'user_sessions': timedelta(days=30),  # 30 days
            'temporary_files': timedelta(hours=24),  # 24 hours
        }
    
    def cleanup_expired_data(self) -> Dict[str, int]:
        """Clean up expired data based on retention policies"""
        from .models import CV, AnalysisResult, SecurityAuditLog, UserSession, FileUploadLog
        
        cleanup_results = {}
        cutoff_date = timezone.now()
        
        # Clean up old CVs
        cv_cutoff = cutoff_date - self.retention_policies['cv_files']
        expired_cvs = CV.objects.filter(upload_date__lt=cv_cutoff, is_deleted=False)
        cv_count = expired_cvs.count()
        expired_cvs.update(is_deleted=True, deleted_date=cutoff_date)
        cleanup_results['cvs_archived'] = cv_count
        
        # Clean up old analysis results
        analysis_cutoff = cutoff_date - self.retention_policies['analysis_results']
        expired_analyses = AnalysisResult.objects.filter(analysis_date__lt=analysis_cutoff)
        analysis_count = expired_analyses.count()
        expired_analyses.delete()
        cleanup_results['analyses_deleted'] = analysis_count
        
        # Clean up old audit logs (keep for compliance)
        log_cutoff = cutoff_date - self.retention_policies['audit_logs']
        expired_logs = SecurityAuditLog.objects.filter(timestamp__lt=log_cutoff)
        log_count = expired_logs.count()
        expired_logs.delete()
        cleanup_results['audit_logs_deleted'] = log_count
        
        # Clean up old sessions
        session_cutoff = cutoff_date - self.retention_policies['user_sessions']
        expired_sessions = UserSession.objects.filter(last_activity__lt=session_cutoff)
        session_count = expired_sessions.count()
        expired_sessions.delete()
        cleanup_results['sessions_deleted'] = session_count
        
        # Clean up temporary files
        temp_cutoff = cutoff_date - self.retention_policies['temporary_files']
        expired_temp_files = FileUploadLog.objects.filter(
            upload_date__lt=temp_cutoff,
            file_path__contains='temp'
        )
        temp_count = expired_temp_files.count()
        for temp_file in expired_temp_files:
            # Delete physical file if it exists
            import os
            if os.path.exists(temp_file.file_path):
                try:
                    os.remove(temp_file.file_path)
                except OSError:
                    pass
        expired_temp_files.delete()
        cleanup_results['temp_files_deleted'] = temp_count
        
        logger.info(f"Data retention cleanup completed: {cleanup_results}")
        return cleanup_results
    
    def get_retention_status(self, user: User) -> Dict[str, Any]:
        """Get retention status for user's data"""
        from .models import CV, AnalysisResult
        
        cutoff_date = timezone.now()
        status = {}
        
        # CV retention status
        cv_cutoff = cutoff_date - self.retention_policies['cv_files']
        total_cvs = CV.objects.filter(user=user).count()
        expiring_cvs = CV.objects.filter(user=user, upload_date__lt=cv_cutoff).count()
        
        status['cvs'] = {
            'total': total_cvs,
            'expiring_soon': expiring_cvs,
            'retention_period_days': self.retention_policies['cv_files'].days
        }
        
        # Analysis retention status
        analysis_cutoff = cutoff_date - self.retention_policies['analysis_results']
        total_analyses = AnalysisResult.objects.filter(cv__user=user).count()
        expiring_analyses = AnalysisResult.objects.filter(
            cv__user=user, 
            analysis_date__lt=analysis_cutoff
        ).count()
        
        status['analyses'] = {
            'total': total_analyses,
            'expiring_soon': expiring_analyses,
            'retention_period_days': self.retention_policies['analysis_results'].days
        }
        
        return status

class ComplianceReporter:
    """Generates compliance reports"""
    
    def generate_privacy_report(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Generate privacy compliance report"""
        from .models import DataSubjectRequest, DataProcessingConsent, SecurityAuditLog
        
        # GDPR requests
        gdpr_requests = DataSubjectRequest.objects.filter(
            request_date__range=[start_date, end_date]
        )
        
        # Consent records
        consent_records = DataProcessingConsent.objects.filter(
            consent_date__range=[start_date, end_date]
        )
        
        # Security incidents
        security_incidents = SecurityAuditLog.objects.filter(
            timestamp__range=[start_date, end_date],
            event_type__in=['failed_login', 'suspicious_activity', 'unauthorized_access']
        )
        
        report = {
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'gdpr_requests': {
                'total': gdpr_requests.count(),
                'by_type': {},
                'by_status': {},
                'average_response_time_days': 0
            },
            'consent_management': {
                'total_records': consent_records.count(),
                'consent_given': consent_records.filter(consent_given=True).count(),
                'consent_withdrawn': consent_records.filter(consent_given=False).count(),
            },
            'security_incidents': {
                'total': security_incidents.count(),
                'by_type': {},
                'resolved': security_incidents.filter(resolved=True).count()
            },
            'compliance_score': 0
        }
        
        # Calculate request types
        for request in gdpr_requests:
            request_type = request.request_type
            report['gdpr_requests']['by_type'][request_type] = \
                report['gdpr_requests']['by_type'].get(request_type, 0) + 1
            
            status = request.status
            report['gdpr_requests']['by_status'][status] = \
                report['gdpr_requests']['by_status'].get(status, 0) + 1
        
        # Calculate security incident types
        for incident in security_incidents:
            event_type = incident.event_type
            report['security_incidents']['by_type'][event_type] = \
                report['security_incidents']['by_type'].get(event_type, 0) + 1
        
        # Calculate compliance score (simplified)
        total_requests = report['gdpr_requests']['total']
        resolved_requests = report['gdpr_requests']['by_status'].get('completed', 0)
        
        if total_requests > 0:
            response_rate = resolved_requests / total_requests
        else:
            response_rate = 1.0
        
        security_score = 1.0 - min(0.5, report['security_incidents']['total'] / 100)
        consent_score = 0.8 if report['consent_management']['total_records'] > 0 else 0.5
        
        report['compliance_score'] = round((response_rate + security_score + consent_score) / 3 * 100, 2)
        
        return report

# Utility functions for easy access
def encrypt_sensitive_data(data: str) -> str:
    """Utility function to encrypt sensitive data"""
    encryption = DataEncryption()
    return encryption.encrypt_field(data)

def decrypt_sensitive_data(encrypted_data: str) -> str:
    """Utility function to decrypt sensitive data"""
    encryption = DataEncryption()
    return encryption.decrypt_field(encrypted_data)

def anonymize_user_for_gdpr(user: User) -> Dict[str, Any]:
    """Utility function to anonymize user data for GDPR compliance"""
    gdpr = GDPRCompliance()
    return gdpr.anonymize_user_data(user)

def cleanup_expired_data() -> Dict[str, int]:
    """Utility function to clean up expired data"""
    retention_manager = DataRetentionManager()
    return retention_manager.cleanup_expired_data() 