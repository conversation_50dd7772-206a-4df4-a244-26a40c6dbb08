"""
DevOps & Deployment System
CI/CD Pipeline, Deployment Automation, and Infrastructure Management
"""

import os
import json
import yaml
import logging
import subprocess
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from django.core.management.base import BaseCommand
import shutil

logger = logging.getLogger(__name__)

class CICDPipelineManager:
    """Continuous Integration/Continuous Deployment pipeline management"""
    
    def __init__(self):
        self.project_root = settings.BASE_DIR
        self.pipeline_configs = {
            'github_actions': self.generate_github_actions_config,
            'gitlab_ci': self.generate_gitlab_ci_config,
            'jenkins': self.generate_jenkins_config,
            'azure_devops': self.generate_azure_devops_config
        }
        
    def generate_github_actions_config(self) -> Dict[str, Any]:
        """Generate GitHub Actions workflow configuration"""
        return {
            'name': 'CV Analyzer CI/CD',
            'on': {
                'push': {
                    'branches': ['main', 'develop']
                },
                'pull_request': {
                    'branches': ['main']
                }
            },
            'jobs': {
                'test': {
                    'runs-on': 'ubuntu-latest',
                    'services': {
                        'postgres': {
                            'image': 'postgres:15',
                            'env': {
                                'POSTGRES_PASSWORD': 'postgres',
                                'POSTGRES_DB': 'cv_analyzer_test'
                            },
                            'options': '--health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5',
                            'ports': ['5432:5432']
                        },
                        'redis': {
                            'image': 'redis:7',
                            'options': '--health-cmd "redis-cli ping" --health-interval 10s --health-timeout 5s --health-retries 5',
                            'ports': ['6379:6379']
                        }
                    },
                    'steps': [
                        {
                            'uses': 'actions/checkout@v4'
                        },
                        {
                            'name': 'Set up Python',
                            'uses': 'actions/setup-python@v4',
                            'with': {
                                'python-version': '3.11'
                            }
                        },
                        {
                            'name': 'Cache pip packages',
                            'uses': 'actions/cache@v3',
                            'with': {
                                'path': '~/.cache/pip',
                                'key': "${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}",
                                'restore-keys': "${{ runner.os }}-pip-"
                            }
                        },
                        {
                            'name': 'Install dependencies',
                            'run': '''
                                python -m pip install --upgrade pip
                                pip install -r requirements.txt
                                pip install -r requirements-dev.txt
                            '''
                        },
                        {
                            'name': 'Run linting',
                            'run': '''
                                flake8 cv_analyzer --count --select=E9,F63,F7,F82 --show-source --statistics
                                flake8 cv_analyzer --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
                            '''
                        },
                        {
                            'name': 'Run type checking',
                            'run': 'mypy cv_analyzer --ignore-missing-imports'
                        },
                        {
                            'name': 'Run security check',
                            'run': 'bandit -r cv_analyzer -f json -o bandit-report.json || true'
                        },
                        {
                            'name': 'Run tests',
                            'run': '''
                                python manage.py migrate
                                python manage.py collectstatic --noinput
                                coverage run --source='.' manage.py test
                                coverage xml
                            ''',
                            'env': {
                                'DATABASE_URL': 'postgresql://postgres:postgres@localhost:5432/cv_analyzer_test',
                                'REDIS_URL': 'redis://localhost:6379/0',
                                'SECRET_KEY': 'test-secret-key',
                                'DEBUG': 'False'
                            }
                        },
                        {
                            'name': 'Upload coverage reports',
                            'uses': 'codecov/codecov-action@v3',
                            'with': {
                                'file': './coverage.xml'
                            }
                        }
                    ]
                },
                'build': {
                    'needs': 'test',
                    'runs-on': 'ubuntu-latest',
                    'if': "github.ref == 'refs/heads/main'",
                    'steps': [
                        {
                            'uses': 'actions/checkout@v4'
                        },
                        {
                            'name': 'Set up Docker Buildx',
                            'uses': 'docker/setup-buildx-action@v3'
                        },
                        {
                            'name': 'Login to Docker Hub',
                            'uses': 'docker/login-action@v3',
                            'with': {
                                'username': '${{ secrets.DOCKER_USERNAME }}',
                                'password': '${{ secrets.DOCKER_PASSWORD }}'
                            }
                        },
                        {
                            'name': 'Build and push Docker image',
                            'uses': 'docker/build-push-action@v5',
                            'with': {
                                'context': '.',
                                'push': True,
                                'tags': 'cvanalyzer/app:latest,cvanalyzer/app:${{ github.sha }}',
                                'cache-from': 'type=gha',
                                'cache-to': 'type=gha,mode=max'
                            }
                        }
                    ]
                },
                'deploy': {
                    'needs': 'build',
                    'runs-on': 'ubuntu-latest',
                    'if': "github.ref == 'refs/heads/main'",
                    'environment': 'production',
                    'steps': [
                        {
                            'uses': 'actions/checkout@v4'
                        },
                        {
                            'name': 'Deploy to production',
                            'run': '''
                                # Deployment script would go here
                                echo "Deploying to production..."
                                # kubectl apply -f k8s/
                                # helm upgrade cv-analyzer ./helm-chart
                            '''
                        },
                        {
                            'name': 'Run smoke tests',
                            'run': '''
                                # Basic health check
                                curl -f ${{ secrets.PRODUCTION_URL }}/health/ || exit 1
                            '''
                        }
                    ]
                }
            }
        }
    
    def generate_gitlab_ci_config(self) -> Dict[str, Any]:
        """Generate GitLab CI configuration"""
        return {
            'stages': ['test', 'build', 'deploy'],
            'variables': {
                'POSTGRES_DB': 'cv_analyzer_test',
                'POSTGRES_USER': 'postgres',
                'POSTGRES_PASSWORD': 'postgres',
                'POSTGRES_HOST_AUTH_METHOD': 'trust'
            },
            'services': [
                'postgres:15',
                'redis:7'
            ],
            'before_script': [
                'python -m pip install --upgrade pip',
                'pip install -r requirements.txt',
                'pip install -r requirements-dev.txt'
            ],
            'test': {
                'stage': 'test',
                'script': [
                    'flake8 cv_analyzer',
                    'mypy cv_analyzer --ignore-missing-imports',
                    'bandit -r cv_analyzer',
                    'python manage.py migrate',
                    'python manage.py collectstatic --noinput',
                    'coverage run --source="." manage.py test',
                    'coverage report',
                    'coverage xml'
                ],
                'coverage': '/TOTAL.+ ([0-9]{1,3}%)/',
                'artifacts': {
                    'reports': {
                        'coverage_report': {
                            'coverage_format': 'cobertura',
                            'path': 'coverage.xml'
                        }
                    }
                }
            },
            'build': {
                'stage': 'build',
                'services': ['docker:dind'],
                'before_script': [
                    'docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY'
                ],
                'script': [
                    'docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .',
                    'docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA'
                ],
                'only': ['main']
            },
            'deploy': {
                'stage': 'deploy',
                'script': [
                    'echo "Deploying to production..."',
                    '# kubectl apply -f k8s/',
                    '# Deployment commands here'
                ],
                'only': ['main'],
                'when': 'manual'
            }
        }
    
    def generate_jenkins_config(self) -> str:
        """Generate Jenkins pipeline configuration"""
        return '''
pipeline {
    agent any
    
    environment {
        DOCKER_IMAGE = 'cvanalyzer/app'
        REGISTRY_CREDENTIALS = credentials('docker-hub')
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Setup') {
            steps {
                sh '''
                    python -m pip install --upgrade pip
                    pip install -r requirements.txt
                    pip install -r requirements-dev.txt
                '''
            }
        }
        
        stage('Test') {
            parallel {
                stage('Lint') {
                    steps {
                        sh 'flake8 cv_analyzer'
                    }
                }
                stage('Type Check') {
                    steps {
                        sh 'mypy cv_analyzer --ignore-missing-imports'
                    }
                }
                stage('Security') {
                    steps {
                        sh 'bandit -r cv_analyzer'
                    }
                }
                stage('Unit Tests') {
                    steps {
                        sh '''
                            python manage.py migrate
                            python manage.py collectstatic --noinput
                            coverage run --source='.' manage.py test
                            coverage xml
                        '''
                    }
                    post {
                        always {
                            publishCoverage adapters: [coberturaAdapter('coverage.xml')], sourceFileResolver: sourceFiles('NEVER_STORE')
                        }
                    }
                }
            }
        }
        
        stage('Build') {
            when {
                branch 'main'
            }
            steps {
                script {
                    def image = docker.build("${DOCKER_IMAGE}:${env.BUILD_NUMBER}")
                    docker.withRegistry('https://registry.hub.docker.com', 'docker-hub') {
                        image.push()
                        image.push('latest')
                    }
                }
            }
        }
        
        stage('Deploy') {
            when {
                branch 'main'
            }
            steps {
                input message: 'Deploy to production?', ok: 'Deploy'
                sh '''
                    echo "Deploying to production..."
                    # kubectl apply -f k8s/
                    # helm upgrade cv-analyzer ./helm-chart
                '''
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
        failure {
            emailext (
                subject: "Build Failed: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "Build failed. Check console output at ${env.BUILD_URL}",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
    }
}
'''
    
    def generate_azure_devops_config(self) -> Dict[str, Any]:
        """Generate Azure DevOps pipeline configuration"""
        return {
            'trigger': {
                'branches': {
                    'include': ['main', 'develop']
                }
            },
            'pr': {
                'branches': {
                    'include': ['main']
                }
            },
            'pool': {
                'vmImage': 'ubuntu-latest'
            },
            'variables': {
                'pythonVersion': '3.11',
                'dockerImageName': 'cvanalyzer/app'
            },
            'stages': [
                {
                    'stage': 'Test',
                    'jobs': [
                        {
                            'job': 'TestJob',
                            'services': {
                                'postgres': {
                                    'image': 'postgres:15',
                                    'env': {
                                        'POSTGRES_PASSWORD': 'postgres',
                                        'POSTGRES_DB': 'cv_analyzer_test'
                                    }
                                },
                                'redis': {
                                    'image': 'redis:7'
                                }
                            },
                            'steps': [
                                {
                                    'task': 'UsePythonVersion@0',
                                    'inputs': {
                                        'versionSpec': '$(pythonVersion)'
                                    }
                                },
                                {
                                    'script': '''
                                        python -m pip install --upgrade pip
                                        pip install -r requirements.txt
                                        pip install -r requirements-dev.txt
                                    ''',
                                    'displayName': 'Install dependencies'
                                },
                                {
                                    'script': 'flake8 cv_analyzer',
                                    'displayName': 'Run linting'
                                },
                                {
                                    'script': 'mypy cv_analyzer --ignore-missing-imports',
                                    'displayName': 'Run type checking'
                                },
                                {
                                    'script': '''
                                        python manage.py migrate
                                        python manage.py collectstatic --noinput
                                        coverage run --source='.' manage.py test
                                        coverage xml
                                    ''',
                                    'displayName': 'Run tests',
                                    'env': {
                                        'DATABASE_URL': 'postgresql://postgres:postgres@localhost:5432/cv_analyzer_test'
                                    }
                                },
                                {
                                    'task': 'PublishCodeCoverageResults@1',
                                    'inputs': {
                                        'codeCoverageTool': 'Cobertura',
                                        'summaryFileLocation': 'coverage.xml'
                                    }
                                }
                            ]
                        }
                    ]
                },
                {
                    'stage': 'Build',
                    'condition': "eq(variables['Build.SourceBranch'], 'refs/heads/main')",
                    'jobs': [
                        {
                            'job': 'BuildJob',
                            'steps': [
                                {
                                    'task': 'Docker@2',
                                    'inputs': {
                                        'containerRegistry': 'dockerHub',
                                        'repository': '$(dockerImageName)',
                                        'command': 'buildAndPush',
                                        'Dockerfile': 'Dockerfile',
                                        'tags': '$(Build.BuildId)'
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    
    def create_pipeline_files(self, pipeline_type: str = 'github_actions'):
        """Create CI/CD pipeline configuration files"""
        try:
            if pipeline_type == 'github_actions':
                config = self.generate_github_actions_config()
                
                # Create .github/workflows directory
                workflows_dir = os.path.join(self.project_root, '.github', 'workflows')
                os.makedirs(workflows_dir, exist_ok=True)
                
                # Write workflow file
                workflow_file = os.path.join(workflows_dir, 'ci-cd.yml')
                with open(workflow_file, 'w') as f:
                    yaml.dump(config, f, default_flow_style=False, sort_keys=False)
                
                logger.info(f"GitHub Actions workflow created: {workflow_file}")
                
            elif pipeline_type == 'gitlab_ci':
                config = self.generate_gitlab_ci_config()
                
                # Write .gitlab-ci.yml file
                gitlab_file = os.path.join(self.project_root, '.gitlab-ci.yml')
                with open(gitlab_file, 'w') as f:
                    yaml.dump(config, f, default_flow_style=False, sort_keys=False)
                
                logger.info(f"GitLab CI configuration created: {gitlab_file}")
                
            elif pipeline_type == 'jenkins':
                config = self.generate_jenkins_config()
                
                # Write Jenkinsfile
                jenkins_file = os.path.join(self.project_root, 'Jenkinsfile')
                with open(jenkins_file, 'w') as f:
                    f.write(config)
                
                logger.info(f"Jenkins pipeline created: {jenkins_file}")
                
            elif pipeline_type == 'azure_devops':
                config = self.generate_azure_devops_config()
                
                # Write azure-pipelines.yml file
                azure_file = os.path.join(self.project_root, 'azure-pipelines.yml')
                with open(azure_file, 'w') as f:
                    yaml.dump(config, f, default_flow_style=False, sort_keys=False)
                
                logger.info(f"Azure DevOps pipeline created: {azure_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create pipeline files: {e}")
            return False 

class DockerManager:
    """Docker containerization management"""
    
    def __init__(self):
        self.project_root = settings.BASE_DIR
    
    def generate_dockerfile(self) -> str:
        """Generate optimized Dockerfile"""
        return '''# Multi-stage build for production optimization
FROM python:3.11-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    build-essential \\
    libpq-dev \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Create and use non-root user
RUN adduser --disabled-password --gecos '' appuser

# Set work directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt requirements-prod.txt ./
RUN pip install --upgrade pip && \\
    pip install --no-cache-dir -r requirements-prod.txt

# Production stage
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DJANGO_SETTINGS_MODULE=cv_analyzer.settings.production

# Install runtime dependencies
RUN apt-get update && apt-get install -y \\
    libpq5 \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser

# Copy Python packages from builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Set work directory
WORKDIR /app

# Copy application code
COPY --chown=appuser:appuser . .

# Create static and media directories
RUN mkdir -p /app/staticfiles /app/media && \\
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Collect static files
RUN python manage.py collectstatic --noinput

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:8000/health/ || exit 1

# Expose port
EXPOSE 8000

# Command to run the application
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "--worker-class", "gevent", "cv_analyzer.wsgi:application"]
'''
    
    def generate_docker_compose_dev(self) -> Dict[str, Any]:
        """Generate Docker Compose configuration for development"""
        return {
            'version': '3.8',
            'services': {
                'web': {
                    'build': '.',
                    'ports': ['8000:8000'],
                    'volumes': [
                        '.:/app',
                        'static_volume:/app/staticfiles',
                        'media_volume:/app/media'
                    ],
                    'environment': [
                        'DEBUG=True',
                        'DATABASE_URL=**************************************/cv_analyzer',
                        'REDIS_URL=redis://redis:6379/0'
                    ],
                    'depends_on': ['db', 'redis'],
                    'command': 'python manage.py runserver 0.0.0.0:8000'
                },
                'db': {
                    'image': 'postgres:15',
                    'volumes': ['postgres_data:/var/lib/postgresql/data'],
                    'environment': [
                        'POSTGRES_DB=cv_analyzer',
                        'POSTGRES_USER=postgres',
                        'POSTGRES_PASSWORD=postgres'
                    ],
                    'ports': ['5432:5432']
                },
                'redis': {
                    'image': 'redis:7',
                    'volumes': ['redis_data:/data'],
                    'ports': ['6379:6379']
                },
                'celery': {
                    'build': '.',
                    'command': 'celery -A cv_analyzer worker -l info',
                    'volumes': ['.:/app'],
                    'environment': [
                        'DATABASE_URL=**************************************/cv_analyzer',
                        'REDIS_URL=redis://redis:6379/0'
                    ],
                    'depends_on': ['db', 'redis']
                }
            },
            'volumes': {
                'postgres_data': {},
                'redis_data': {},
                'static_volume': {},
                'media_volume': {}
            }
        }
    
    def generate_docker_compose_prod(self) -> Dict[str, Any]:
        """Generate Docker Compose configuration for production"""
        return {
            'version': '3.8',
            'services': {
                'web': {
                    'image': 'cvanalyzer/app:latest',
                    'restart': 'always',
                    'volumes': [
                        'static_volume:/app/staticfiles',
                        'media_volume:/app/media'
                    ],
                    'environment': [
                        'DJANGO_SETTINGS_MODULE=cv_analyzer.settings.production',
                        'DATABASE_URL=${DATABASE_URL}',
                        'REDIS_URL=${REDIS_URL}',
                        'SECRET_KEY=${SECRET_KEY}',
                        'ALLOWED_HOSTS=${ALLOWED_HOSTS}'
                    ],
                    'depends_on': ['db', 'redis'],
                    'networks': ['app-network']
                },
                'nginx': {
                    'image': 'nginx:alpine',
                    'restart': 'always',
                    'ports': ['80:80', '443:443'],
                    'volumes': [
                        './nginx.conf:/etc/nginx/nginx.conf',
                        'static_volume:/app/staticfiles',
                        'media_volume:/app/media',
                        './ssl:/etc/nginx/ssl'
                    ],
                    'depends_on': ['web'],
                    'networks': ['app-network']
                },
                'db': {
                    'image': 'postgres:15',
                    'restart': 'always',
                    'volumes': ['postgres_data:/var/lib/postgresql/data'],
                    'environment': [
                        'POSTGRES_DB=${POSTGRES_DB}',
                        'POSTGRES_USER=${POSTGRES_USER}',
                        'POSTGRES_PASSWORD=${POSTGRES_PASSWORD}'
                    ],
                    'networks': ['app-network']
                },
                'redis': {
                    'image': 'redis:7',
                    'restart': 'always',
                    'volumes': ['redis_data:/data'],
                    'networks': ['app-network']
                },
                'celery': {
                    'image': 'cvanalyzer/app:latest',
                    'restart': 'always',
                    'command': 'celery -A cv_analyzer worker -l info',
                    'environment': [
                        'DJANGO_SETTINGS_MODULE=cv_analyzer.settings.production',
                        'DATABASE_URL=${DATABASE_URL}',
                        'REDIS_URL=${REDIS_URL}'
                    ],
                    'depends_on': ['db', 'redis'],
                    'networks': ['app-network']
                }
            },
            'volumes': {
                'postgres_data': {},
                'redis_data': {},
                'static_volume': {},
                'media_volume': {}
            },
            'networks': {
                'app-network': {
                    'driver': 'bridge'
                }
            }
        }
    
    def create_docker_files(self):
        """Create Docker configuration files"""
        try:
            # Create Dockerfile
            dockerfile_path = os.path.join(self.project_root, 'Dockerfile')
            with open(dockerfile_path, 'w') as f:
                f.write(self.generate_dockerfile())
            
            # Create docker-compose for development
            compose_dev_path = os.path.join(self.project_root, 'docker-compose.dev.yml')
            with open(compose_dev_path, 'w') as f:
                yaml.dump(self.generate_docker_compose_dev(), f, default_flow_style=False)
            
            # Create docker-compose for production
            compose_prod_path = os.path.join(self.project_root, 'docker-compose.prod.yml')
            with open(compose_prod_path, 'w') as f:
                yaml.dump(self.generate_docker_compose_prod(), f, default_flow_style=False)
            
                         # Create .dockerignore
             dockerignore_path = os.path.join(self.project_root, '.dockerignore')
             dockerignore_content = """.git
.gitignore
README.md
Dockerfile
.dockerignore
.venv
.env
.pytest_cache
__pycache__
*.pyc
*.pyo
*.pyd
.coverage
.mypy_cache
.tox
node_modules
npm-debug.log
.DS_Store
"""
             with open(dockerignore_path, 'w') as f:
                 f.write(dockerignore_content)
            
            logger.info("Docker configuration files created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create Docker files: {e}")
            return False

class InfrastructureManager:
    """Infrastructure as Code management"""
    
    def __init__(self):
        self.project_root = settings.BASE_DIR
    
    def generate_kubernetes_configs(self) -> Dict[str, Dict[str, Any]]:
        """Generate Kubernetes deployment configurations"""
        return {
            'namespace': {
                'apiVersion': 'v1',
                'kind': 'Namespace',
                'metadata': {
                    'name': 'cv-analyzer'
                }
            },
            'deployment': {
                'apiVersion': 'apps/v1',
                'kind': 'Deployment',
                'metadata': {
                    'name': 'cv-analyzer-web',
                    'namespace': 'cv-analyzer'
                },
                'spec': {
                    'replicas': 3,
                    'selector': {
                        'matchLabels': {
                            'app': 'cv-analyzer-web'
                        }
                    },
                    'template': {
                        'metadata': {
                            'labels': {
                                'app': 'cv-analyzer-web'
                            }
                        },
                        'spec': {
                            'containers': [
                                {
                                    'name': 'web',
                                    'image': 'cvanalyzer/app:latest',
                                    'ports': [
                                        {
                                            'containerPort': 8000
                                        }
                                    ],
                                    'env': [
                                        {
                                            'name': 'DATABASE_URL',
                                            'valueFrom': {
                                                'secretKeyRef': {
                                                    'name': 'cv-analyzer-secrets',
                                                    'key': 'database-url'
                                                }
                                            }
                                        }
                                    ],
                                    'resources': {
                                        'requests': {
                                            'memory': '256Mi',
                                            'cpu': '250m'
                                        },
                                        'limits': {
                                            'memory': '512Mi',
                                            'cpu': '500m'
                                        }
                                    },
                                    'livenessProbe': {
                                        'httpGet': {
                                            'path': '/health/',
                                            'port': 8000
                                        },
                                        'initialDelaySeconds': 30,
                                        'periodSeconds': 10
                                    },
                                    'readinessProbe': {
                                        'httpGet': {
                                            'path': '/health/',
                                            'port': 8000
                                        },
                                        'initialDelaySeconds': 5,
                                        'periodSeconds': 5
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            'service': {
                'apiVersion': 'v1',
                'kind': 'Service',
                'metadata': {
                    'name': 'cv-analyzer-service',
                    'namespace': 'cv-analyzer'
                },
                'spec': {
                    'selector': {
                        'app': 'cv-analyzer-web'
                    },
                    'ports': [
                        {
                            'protocol': 'TCP',
                            'port': 80,
                            'targetPort': 8000
                        }
                    ],
                    'type': 'ClusterIP'
                }
            },
            'ingress': {
                'apiVersion': 'networking.k8s.io/v1',
                'kind': 'Ingress',
                'metadata': {
                    'name': 'cv-analyzer-ingress',
                    'namespace': 'cv-analyzer',
                    'annotations': {
                        'kubernetes.io/ingress.class': 'nginx',
                        'cert-manager.io/cluster-issuer': 'letsencrypt-prod'
                    }
                },
                'spec': {
                    'tls': [
                        {
                            'hosts': ['cvanalyzer.com'],
                            'secretName': 'cv-analyzer-tls'
                        }
                    ],
                    'rules': [
                        {
                            'host': 'cvanalyzer.com',
                            'http': {
                                'paths': [
                                    {
                                        'path': '/',
                                        'pathType': 'Prefix',
                                        'backend': {
                                            'service': {
                                                'name': 'cv-analyzer-service',
                                                'port': {
                                                    'number': 80
                                                }
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        }
    
    def create_kubernetes_files(self):
        """Create Kubernetes configuration files"""
        try:
            k8s_dir = os.path.join(self.project_root, 'k8s')
            os.makedirs(k8s_dir, exist_ok=True)
            
            configs = self.generate_kubernetes_configs()
            
            for name, config in configs.items():
                file_path = os.path.join(k8s_dir, f'{name}.yaml')
                with open(file_path, 'w') as f:
                    yaml.dump(config, f, default_flow_style=False)
            
            logger.info("Kubernetes configuration files created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create Kubernetes files: {e}")
            return False

class DeploymentManager:
    """Deployment automation and management"""
    
    def __init__(self):
        self.project_root = settings.BASE_DIR
        self.cicd_manager = CICDPipelineManager()
        self.docker_manager = DockerManager()
        self.infra_manager = InfrastructureManager()
    
    def setup_devops_infrastructure(self, pipeline_type: str = 'github_actions'):
        """Setup complete DevOps infrastructure"""
        try:
            logger.info("Setting up DevOps infrastructure...")
            
            # Create CI/CD pipeline files
            self.cicd_manager.create_pipeline_files(pipeline_type)
            
            # Create Docker configuration
            self.docker_manager.create_docker_files()
            
            # Create Kubernetes configuration
            self.infra_manager.create_kubernetes_files()
            
            # Create deployment scripts
            self.create_deployment_scripts()
            
            logger.info("DevOps infrastructure setup completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup DevOps infrastructure: {e}")
            return False
    
    def create_deployment_scripts(self):
        """Create deployment automation scripts"""
        try:
            scripts_dir = os.path.join(self.project_root, 'scripts')
            os.makedirs(scripts_dir, exist_ok=True)
            
                         # Deploy script
             deploy_script = os.path.join(scripts_dir, 'deploy.sh')
             deploy_content = """#!/bin/bash
set -e

echo "Starting deployment..."

# Build and push Docker image
docker build -t cvanalyzer/app:latest .
docker push cvanalyzer/app:latest

# Deploy to Kubernetes
kubectl apply -f k8s/

# Wait for deployment to complete
kubectl rollout status deployment/cv-analyzer-web -n cv-analyzer

# Run database migrations
kubectl exec -it deployment/cv-analyzer-web -n cv-analyzer -- python manage.py migrate

echo "Deployment completed successfully!"
"""
             with open(deploy_script, 'w') as f:
                 f.write(deploy_content)
            
            # Make script executable
            os.chmod(deploy_script, 0o755)
            
            logger.info("Deployment scripts created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create deployment scripts: {e}")
            return False

# Global instances
cicd_manager = CICDPipelineManager()
docker_manager = DockerManager()
infrastructure_manager = InfrastructureManager()
deployment_manager = DeploymentManager()

# Utility functions
def setup_devops_pipeline(pipeline_type: str = 'github_actions'):
    """Setup DevOps pipeline"""
    return deployment_manager.setup_devops_infrastructure(pipeline_type)

def create_docker_config():
    """Create Docker configuration"""
    return docker_manager.create_docker_files()

def create_kubernetes_config():
    """Create Kubernetes configuration"""
    return infrastructure_manager.create_kubernetes_files() 