"""
Environment Configuration and Secrets Management
Handles different environments and secure configuration
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from cryptography.fernet import Fernet
from django.core.exceptions import ImproperlyConfigured

logger = logging.getLogger(__name__)

class EnvironmentManager:
    """Manages environment-specific configurations"""
    
    ENVIRONMENTS = ['development', 'staging', 'production', 'testing']
    
    def __init__(self):
        self.current_env = self._detect_environment()
        self.config_dir = Path(__file__).parent.parent / 'config'
        self.secrets_manager = SecretsManager()
    
    def _detect_environment(self) -> str:
        """Detect current environment from environment variables"""
        env = os.getenv('DJANGO_ENV', os.getenv('ENVIRONMENT', 'development')).lower()
        
        if env not in self.ENVIRONMENTS:
            logger.warning(f"Unknown environment '{env}', defaulting to 'development'")
            env = 'development'
        
        logger.info(f"Environment detected: {env}")
        return env
    
    def is_production(self) -> bool:
        """Check if running in production"""
        return self.current_env == 'production'
    
    def is_development(self) -> bool:
        """Check if running in development"""
        return self.current_env == 'development'
    
    def is_staging(self) -> bool:
        """Check if running in staging"""
        return self.current_env == 'staging'
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration for current environment"""
        configs = {
            'development': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': 'db.sqlite3',
                'OPTIONS': {
                    'timeout': 30,
                },
            },
            'staging': {
                'ENGINE': 'django.db.backends.postgresql',
                'NAME': self.secrets_manager.get_secret('STAGING_DB_NAME', 'cv_analyzer_staging'),
                'USER': self.secrets_manager.get_secret('STAGING_DB_USER', 'postgres'),
                'PASSWORD': self.secrets_manager.get_secret('STAGING_DB_PASSWORD'),
                'HOST': self.secrets_manager.get_secret('STAGING_DB_HOST', 'localhost'),
                'PORT': self.secrets_manager.get_secret('STAGING_DB_PORT', '5432'),
                'OPTIONS': {
                    'sslmode': 'require',
                },
            },
            'production': {
                'ENGINE': 'django.db.backends.postgresql',
                'NAME': self.secrets_manager.get_secret('PROD_DB_NAME'),
                'USER': self.secrets_manager.get_secret('PROD_DB_USER'),
                'PASSWORD': self.secrets_manager.get_secret('PROD_DB_PASSWORD'),
                'HOST': self.secrets_manager.get_secret('PROD_DB_HOST'),
                'PORT': self.secrets_manager.get_secret('PROD_DB_PORT', '5432'),
                'OPTIONS': {
                    'sslmode': 'require',
                    'conn_max_age': 60,
                },
            },
            'testing': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': ':memory:',
            }
        }
        
        return configs.get(self.current_env, configs['development'])
    
    def get_redis_config(self) -> Dict[str, Any]:
        """Get Redis configuration for current environment"""
        configs = {
            'development': {
                'host': 'localhost',
                'port': 6379,
                'db': 0,
                'password': None,
            },
            'staging': {
                'host': self.secrets_manager.get_secret('STAGING_REDIS_HOST', 'localhost'),
                'port': int(self.secrets_manager.get_secret('STAGING_REDIS_PORT', '6379')),
                'db': int(self.secrets_manager.get_secret('STAGING_REDIS_DB', '1')),
                'password': self.secrets_manager.get_secret('STAGING_REDIS_PASSWORD'),
            },
            'production': {
                'host': self.secrets_manager.get_secret('PROD_REDIS_HOST'),
                'port': int(self.secrets_manager.get_secret('PROD_REDIS_PORT', '6379')),
                'db': int(self.secrets_manager.get_secret('PROD_REDIS_DB', '0')),
                'password': self.secrets_manager.get_secret('PROD_REDIS_PASSWORD'),
                'ssl': True,
                'ssl_cert_reqs': None,
            },
            'testing': {
                'host': 'localhost',
                'port': 6379,
                'db': 15,  # Use different DB for testing
                'password': None,
            }
        }
        
        return configs.get(self.current_env, configs['development'])
    
    def get_email_config(self) -> Dict[str, Any]:
        """Get email configuration for current environment"""
        configs = {
            'development': {
                'EMAIL_BACKEND': 'django.core.mail.backends.console.EmailBackend',
                'EMAIL_HOST': None,
                'EMAIL_PORT': None,
                'EMAIL_USE_TLS': False,
                'EMAIL_HOST_USER': None,
                'EMAIL_HOST_PASSWORD': None,
            },
            'staging': {
                'EMAIL_BACKEND': 'django.core.mail.backends.smtp.EmailBackend',
                'EMAIL_HOST': self.secrets_manager.get_secret('STAGING_EMAIL_HOST', 'smtp.gmail.com'),
                'EMAIL_PORT': int(self.secrets_manager.get_secret('STAGING_EMAIL_PORT', '587')),
                'EMAIL_USE_TLS': True,
                'EMAIL_HOST_USER': self.secrets_manager.get_secret('STAGING_EMAIL_USER'),
                'EMAIL_HOST_PASSWORD': self.secrets_manager.get_secret('STAGING_EMAIL_PASSWORD'),
            },
            'production': {
                'EMAIL_BACKEND': 'django.core.mail.backends.smtp.EmailBackend',
                'EMAIL_HOST': self.secrets_manager.get_secret('PROD_EMAIL_HOST'),
                'EMAIL_PORT': int(self.secrets_manager.get_secret('PROD_EMAIL_PORT', '587')),
                'EMAIL_USE_TLS': True,
                'EMAIL_HOST_USER': self.secrets_manager.get_secret('PROD_EMAIL_USER'),
                'EMAIL_HOST_PASSWORD': self.secrets_manager.get_secret('PROD_EMAIL_PASSWORD'),
            },
            'testing': {
                'EMAIL_BACKEND': 'django.core.mail.backends.locmem.EmailBackend',
            }
        }
        
        return configs.get(self.current_env, configs['development'])
    
    def get_ai_provider_config(self) -> Dict[str, Any]:
        """Get AI provider configuration for current environment"""
        return {
            'openai': {
                'api_key': self.secrets_manager.get_secret('OPENAI_API_KEY'),
                'model': self.secrets_manager.get_secret('OPENAI_MODEL', 'gpt-3.5-turbo'),
                'max_tokens': int(self.secrets_manager.get_secret('OPENAI_MAX_TOKENS', '1000')),
                'temperature': float(self.secrets_manager.get_secret('OPENAI_TEMPERATURE', '0.7')),
            },
            'groq': {
                'api_key': self.secrets_manager.get_secret('GROQ_API_KEY'),
                'model': self.secrets_manager.get_secret('GROQ_MODEL', 'mixtral-8x7b-32768'),
                'max_tokens': int(self.secrets_manager.get_secret('GROQ_MAX_TOKENS', '1000')),
                'temperature': float(self.secrets_manager.get_secret('GROQ_TEMPERATURE', '0.7')),
            }
        }
    
    def get_security_config(self) -> Dict[str, Any]:
        """Get security configuration for current environment"""
        base_config = {
            'SECRET_KEY': self.secrets_manager.get_secret('SECRET_KEY'),
            'DEBUG': self.current_env in ['development', 'testing'],
            'ALLOWED_HOSTS': self._get_allowed_hosts(),
            'CSRF_COOKIE_SECURE': not self.is_development(),
            'SESSION_COOKIE_SECURE': not self.is_development(),
            'SECURE_SSL_REDIRECT': self.is_production(),
            'SECURE_HSTS_SECONDS': 31536000 if self.is_production() else 0,
            'SECURE_HSTS_INCLUDE_SUBDOMAINS': self.is_production(),
            'SECURE_CONTENT_TYPE_NOSNIFF': True,
            'SECURE_BROWSER_XSS_FILTER': True,
            'X_FRAME_OPTIONS': 'DENY',
        }
        
        # Environment-specific overrides
        if self.is_production():
            base_config.update({
                'SECURE_PROXY_SSL_HEADER': ('HTTP_X_FORWARDED_PROTO', 'https'),
                'USE_TLS': True,
            })
        
        return base_config
    
    def _get_allowed_hosts(self) -> list:
        """Get allowed hosts for current environment"""
        hosts_map = {
            'development': ['localhost', '127.0.0.1', '0.0.0.0'],
            'staging': [
                self.secrets_manager.get_secret('STAGING_DOMAIN', 'staging.cvanalyzer.com'),
                'staging.cvanalyzer.com'
            ],
            'production': [
                self.secrets_manager.get_secret('PROD_DOMAIN', 'cvanalyzer.com'),
                'cvanalyzer.com',
                'www.cvanalyzer.com'
            ],
            'testing': ['testserver', 'localhost']
        }
        
        return hosts_map.get(self.current_env, hosts_map['development'])
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration for current environment"""
        base_config = {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'verbose': {
                    'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
                    'style': '{',
                },
                'simple': {
                    'format': '{levelname} {message}',
                    'style': '{',
                },
                'json': {
                    'format': '{"level": "%(levelname)s", "time": "%(asctime)s", "module": "%(module)s", "message": "%(message)s"}',
                },
            },
            'handlers': {
                'console': {
                    'class': 'logging.StreamHandler',
                    'formatter': 'simple' if self.is_development() else 'json',
                },
                'file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'filename': f'logs/cv_analyzer_{self.current_env}.log',
                    'maxBytes': 15728640,  # 15MB
                    'backupCount': 10,
                    'formatter': 'verbose',
                },
            },
            'root': {
                'level': 'DEBUG' if self.is_development() else 'INFO',
                'handlers': ['console', 'file'],
            },
            'loggers': {
                'django': {
                    'level': 'INFO',
                    'handlers': ['console', 'file'],
                    'propagate': False,
                },
                'cv_analyzer': {
                    'level': 'DEBUG' if self.is_development() else 'INFO',
                    'handlers': ['console', 'file'],
                    'propagate': False,
                },
            },
        }
        
        # Add Sentry for production
        if self.is_production():
            sentry_dsn = self.secrets_manager.get_secret('SENTRY_DSN')
            if sentry_dsn:
                base_config['handlers']['sentry'] = {
                    'class': 'sentry_sdk.integrations.logging.SentryHandler',
                    'level': 'ERROR',
                }
                base_config['root']['handlers'].append('sentry')
        
        return base_config

class SecretsManager:
    """Manages secrets and sensitive configuration"""
    
    def __init__(self):
        self.secrets_file = Path(__file__).parent.parent / 'config' / 'secrets.json'
        self.encrypted_secrets_file = Path(__file__).parent.parent / 'config' / 'secrets.enc'
        self.encryption_key_file = Path(__file__).parent.parent / 'config' / '.key'
        self._secrets_cache = {}
    
    def get_secret(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """Get a secret value by key"""
        # First try environment variables
        env_value = os.getenv(key)
        if env_value:
            return env_value
        
        # Then try cached secrets
        if key in self._secrets_cache:
            return self._secrets_cache[key]
        
        # Load from encrypted file
        secrets = self._load_secrets()
        value = secrets.get(key, default)
        
        if value is None and default is None:
            logger.warning(f"Secret '{key}' not found and no default provided")
        
        return value
    
    def set_secret(self, key: str, value: str, encrypt: bool = True):
        """Set a secret value"""
        secrets = self._load_secrets()
        secrets[key] = value
        self._secrets_cache[key] = value
        
        if encrypt:
            self._save_encrypted_secrets(secrets)
        else:
            self._save_plain_secrets(secrets)
    
    def _load_secrets(self) -> Dict[str, Any]:
        """Load secrets from file"""
        if self._secrets_cache:
            return self._secrets_cache
        
        # Try to load encrypted secrets first
        if self.encrypted_secrets_file.exists():
            try:
                secrets = self._load_encrypted_secrets()
                self._secrets_cache = secrets
                return secrets
            except Exception as e:
                logger.error(f"Failed to load encrypted secrets: {e}")
        
        # Fall back to plain secrets file
        if self.secrets_file.exists():
            try:
                with open(self.secrets_file, 'r') as f:
                    secrets = json.load(f)
                    self._secrets_cache = secrets
                    return secrets
            except Exception as e:
                logger.error(f"Failed to load plain secrets: {e}")
        
        return {}
    
    def _load_encrypted_secrets(self) -> Dict[str, Any]:
        """Load and decrypt secrets"""
        if not self.encryption_key_file.exists():
            raise FileNotFoundError("Encryption key file not found")
        
        with open(self.encryption_key_file, 'rb') as f:
            key = f.read()
        
        cipher = Fernet(key)
        
        with open(self.encrypted_secrets_file, 'rb') as f:
            encrypted_data = f.read()
        
        decrypted_data = cipher.decrypt(encrypted_data)
        return json.loads(decrypted_data.decode())
    
    def _save_encrypted_secrets(self, secrets: Dict[str, Any]):
        """Encrypt and save secrets"""
        # Ensure config directory exists
        self.secrets_file.parent.mkdir(exist_ok=True)
        
        # Generate or load encryption key
        if not self.encryption_key_file.exists():
            key = Fernet.generate_key()
            with open(self.encryption_key_file, 'wb') as f:
                f.write(key)
            # Set restrictive permissions
            os.chmod(self.encryption_key_file, 0o600)
        else:
            with open(self.encryption_key_file, 'rb') as f:
                key = f.read()
        
        cipher = Fernet(key)
        
        # Encrypt secrets
        secrets_json = json.dumps(secrets, indent=2)
        encrypted_data = cipher.encrypt(secrets_json.encode())
        
        # Save encrypted secrets
        with open(self.encrypted_secrets_file, 'wb') as f:
            f.write(encrypted_data)
        
        # Set restrictive permissions
        os.chmod(self.encrypted_secrets_file, 0o600)
    
    def _save_plain_secrets(self, secrets: Dict[str, Any]):
        """Save secrets in plain text (for development only)"""
        env_manager = EnvironmentManager()
        if env_manager.is_production():
            raise ImproperlyConfigured("Plain text secrets not allowed in production")
        
        self.secrets_file.parent.mkdir(exist_ok=True)
        
        with open(self.secrets_file, 'w') as f:
            json.dump(secrets, f, indent=2)
        
        # Set restrictive permissions
        os.chmod(self.secrets_file, 0o600)
    
    def rotate_encryption_key(self):
        """Rotate the encryption key"""
        if not self.encrypted_secrets_file.exists():
            logger.warning("No encrypted secrets file found")
            return
        
        # Load current secrets
        secrets = self._load_encrypted_secrets()
        
        # Generate new key
        new_key = Fernet.generate_key()
        
        # Backup old key
        backup_key_file = self.encryption_key_file.with_suffix('.bak')
        if self.encryption_key_file.exists():
            os.rename(self.encryption_key_file, backup_key_file)
        
        # Save new key
        with open(self.encryption_key_file, 'wb') as f:
            f.write(new_key)
        os.chmod(self.encryption_key_file, 0o600)
        
        # Re-encrypt secrets with new key
        self._save_encrypted_secrets(secrets)
        
        logger.info("Encryption key rotated successfully")

class EnvironmentValidator:
    """Validates environment configuration"""
    
    def __init__(self, env_manager: EnvironmentManager):
        self.env_manager = env_manager
        self.secrets_manager = env_manager.secrets_manager
    
    def validate_environment(self) -> Dict[str, Any]:
        """Validate current environment configuration"""
        validation_results = {
            'environment': self.env_manager.current_env,
            'valid': True,
            'errors': [],
            'warnings': [],
            'checks': {}
        }
        
        # Validate secrets
        secrets_check = self._validate_secrets()
        validation_results['checks']['secrets'] = secrets_check
        if not secrets_check['valid']:
            validation_results['valid'] = False
            validation_results['errors'].extend(secrets_check['errors'])
        
        # Validate database
        db_check = self._validate_database()
        validation_results['checks']['database'] = db_check
        if not db_check['valid']:
            validation_results['valid'] = False
            validation_results['errors'].extend(db_check['errors'])
        
        # Validate Redis
        redis_check = self._validate_redis()
        validation_results['checks']['redis'] = redis_check
        if not redis_check['valid']:
            validation_results['warnings'].extend(redis_check['warnings'])
        
        # Validate security settings
        security_check = self._validate_security()
        validation_results['checks']['security'] = security_check
        if not security_check['valid']:
            validation_results['valid'] = False
            validation_results['errors'].extend(security_check['errors'])
        
        return validation_results
    
    def _validate_secrets(self) -> Dict[str, Any]:
        """Validate required secrets are present"""
        required_secrets = ['SECRET_KEY']
        
        if self.env_manager.is_production():
            required_secrets.extend([
                'PROD_DB_NAME', 'PROD_DB_USER', 'PROD_DB_PASSWORD', 'PROD_DB_HOST',
                'PROD_REDIS_HOST', 'PROD_REDIS_PASSWORD',
                'PROD_EMAIL_HOST', 'PROD_EMAIL_USER', 'PROD_EMAIL_PASSWORD',
                'OPENAI_API_KEY'
            ])
        
        missing_secrets = []
        for secret in required_secrets:
            if not self.secrets_manager.get_secret(secret):
                missing_secrets.append(secret)
        
        return {
            'valid': len(missing_secrets) == 0,
            'errors': [f"Missing required secret: {secret}" for secret in missing_secrets],
            'required_secrets': required_secrets,
            'missing_secrets': missing_secrets
        }
    
    def _validate_database(self) -> Dict[str, Any]:
        """Validate database configuration"""
        try:
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            
            return {'valid': True, 'errors': []}
        except Exception as e:
            return {
                'valid': False,
                'errors': [f"Database connection failed: {str(e)}"]
            }
    
    def _validate_redis(self) -> Dict[str, Any]:
        """Validate Redis configuration"""
        try:
            import redis
            redis_config = self.env_manager.get_redis_config()
            
            r = redis.Redis(**redis_config)
            r.ping()
            
            return {'valid': True, 'warnings': []}
        except Exception as e:
            return {
                'valid': False,
                'warnings': [f"Redis connection failed: {str(e)}"]
            }
    
    def _validate_security(self) -> Dict[str, Any]:
        """Validate security configuration"""
        errors = []
        
        # Check SECRET_KEY
        secret_key = self.secrets_manager.get_secret('SECRET_KEY')
        if not secret_key or len(secret_key) < 50:
            errors.append("SECRET_KEY is too short or missing")
        
        # Check HTTPS in production
        if self.env_manager.is_production():
            security_config = self.env_manager.get_security_config()
            if not security_config.get('SECURE_SSL_REDIRECT'):
                errors.append("HTTPS redirect not enabled in production")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }

# Utility function for easy access
def get_environment_manager() -> EnvironmentManager:
    """Get a configured environment manager instance"""
    return EnvironmentManager()

def validate_current_environment() -> Dict[str, Any]:
    """Validate the current environment configuration"""
    env_manager = get_environment_manager()
    validator = EnvironmentValidator(env_manager)
    return validator.validate_environment() 