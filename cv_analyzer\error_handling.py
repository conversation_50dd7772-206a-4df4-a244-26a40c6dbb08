"""
Comprehensive Error Handling and Logging System
Handles exceptions, structured logging, and error tracking
"""

import logging
import traceback
import json
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Type
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.core.exceptions import ValidationError, PermissionDenied
from django.db import DatabaseError, IntegrityError
from django.conf import settings
from django.utils import timezone
from django.core.mail import send_mail
from django.template.loader import render_to_string
import uuid

logger = logging.getLogger(__name__)

class CVAnalyzerException(Exception):
    """Base exception class for CV Analyzer"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or 'CV_ANALYZER_ERROR'
        self.details = details or {}
        self.timestamp = timezone.now()
        self.error_id = str(uuid.uuid4())
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'error_id': self.error_id,
            'error_code': self.error_code,
            'message': self.message,
            'details': self.details,
            'timestamp': self.timestamp.isoformat()
        }

class FileUploadError(CVAnalyzerException):
    """Exception for file upload related errors"""
    
    def __init__(self, message: str, file_info: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code='FILE_UPLOAD_ERROR',
            details={'file_info': file_info or {}}
        )

class AIAnalysisError(CVAnalyzerException):
    """Exception for AI analysis related errors"""
    
    def __init__(self, message: str, provider: str = None, cv_id: int = None):
        super().__init__(
            message=message,
            error_code='AI_ANALYSIS_ERROR',
            details={'provider': provider, 'cv_id': cv_id}
        )

class DatabaseConnectionError(CVAnalyzerException):
    """Exception for database connection issues"""
    
    def __init__(self, message: str, operation: str = None):
        super().__init__(
            message=message,
            error_code='DATABASE_CONNECTION_ERROR',
            details={'operation': operation}
        )

class AuthenticationError(CVAnalyzerException):
    """Exception for authentication related errors"""
    
    def __init__(self, message: str, user_info: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code='AUTHENTICATION_ERROR',
            details={'user_info': user_info or {}}
        )

class SecurityViolationError(CVAnalyzerException):
    """Exception for security violations"""
    
    def __init__(self, message: str, violation_type: str = None, ip_address: str = None):
        super().__init__(
            message=message,
            error_code='SECURITY_VIOLATION',
            details={'violation_type': violation_type, 'ip_address': ip_address}
        )

class BusinessLogicError(CVAnalyzerException):
    """Exception for business logic violations"""
    
    def __init__(self, message: str, rule_violated: str = None):
        super().__init__(
            message=message,
            error_code='BUSINESS_LOGIC_ERROR',
            details={'rule_violated': rule_violated}
        )

class ExternalServiceError(CVAnalyzerException):
    """Exception for external service failures"""
    
    def __init__(self, message: str, service_name: str = None, status_code: int = None):
        super().__init__(
            message=message,
            error_code='EXTERNAL_SERVICE_ERROR',
            details={'service_name': service_name, 'status_code': status_code}
        )

class StructuredLogger:
    """Structured logging with JSON format and context"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.default_context = {}
    
    def set_default_context(self, context: Dict[str, Any]):
        """Set default context for all log entries"""
        self.default_context = context
    
    def _format_log_entry(self, level: str, message: str, 
                         context: Dict[str, Any] = None, 
                         exception: Exception = None) -> Dict[str, Any]:
        """Format structured log entry"""
        log_entry = {
            'timestamp': timezone.now().isoformat(),
            'level': level.upper(),
            'message': message,
            'logger': self.logger.name,
            'context': {**self.default_context, **(context or {})}
        }
        
        if exception:
            log_entry['exception'] = {
                'type': type(exception).__name__,
                'message': str(exception),
                'traceback': traceback.format_exc() if level == 'ERROR' else None
            }
            
            if isinstance(exception, CVAnalyzerException):
                log_entry['exception'].update({
                    'error_id': exception.error_id,
                    'error_code': exception.error_code,
                    'details': exception.details
                })
        
        return log_entry
    
    def info(self, message: str, context: Dict[str, Any] = None):
        """Log info message with context"""
        log_entry = self._format_log_entry('INFO', message, context)
        self.logger.info(json.dumps(log_entry))
    
    def warning(self, message: str, context: Dict[str, Any] = None, exception: Exception = None):
        """Log warning message with context"""
        log_entry = self._format_log_entry('WARNING', message, context, exception)
        self.logger.warning(json.dumps(log_entry))
    
    def error(self, message: str, context: Dict[str, Any] = None, exception: Exception = None):
        """Log error message with context"""
        log_entry = self._format_log_entry('ERROR', message, context, exception)
        self.logger.error(json.dumps(log_entry))
    
    def critical(self, message: str, context: Dict[str, Any] = None, exception: Exception = None):
        """Log critical message with context"""
        log_entry = self._format_log_entry('CRITICAL', message, context, exception)
        self.logger.critical(json.dumps(log_entry))

class ErrorTracker:
    """Track and analyze application errors"""
    
    def __init__(self):
        self.error_storage = {}  # In production, use database or external service
        self.logger = StructuredLogger('cv_analyzer.error_tracker')
    
    def track_error(self, exception: Exception, request: HttpRequest = None, 
                   user_id: int = None, additional_context: Dict[str, Any] = None):
        """Track error occurrence with context"""
        error_data = {
            'timestamp': timezone.now(),
            'exception_type': type(exception).__name__,
            'message': str(exception),
            'traceback': traceback.format_exc(),
            'user_id': user_id,
            'additional_context': additional_context or {}
        }
        
        if request:
            error_data['request_info'] = {
                'method': request.method,
                'path': request.path,
                'query_params': dict(request.GET),
                'ip_address': self._get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'referrer': request.META.get('HTTP_REFERER', '')
            }
        
        if isinstance(exception, CVAnalyzerException):
            error_data.update({
                'error_id': exception.error_id,
                'error_code': exception.error_code,
                'details': exception.details
            })
        
        # Store error data
        error_id = error_data.get('error_id', str(uuid.uuid4()))
        self.error_storage[error_id] = error_data
        
        # Log structured error
        self.logger.error(
            f"Error tracked: {exception}",
            context={
                'error_id': error_id,
                'error_type': type(exception).__name__,
                'user_id': user_id
            },
            exception=exception
        )
        
        # Send notification for critical errors
        if self._is_critical_error(exception):
            self._notify_critical_error(error_data)
        
        return error_id
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def _is_critical_error(self, exception: Exception) -> bool:
        """Determine if error is critical and requires immediate attention"""
        critical_exceptions = (
            DatabaseConnectionError,
            SecurityViolationError,
            ExternalServiceError
        )
        
        return isinstance(exception, critical_exceptions) or \
               isinstance(exception, (DatabaseError, IntegrityError))
    
    def _notify_critical_error(self, error_data: Dict[str, Any]):
        """Send notification for critical errors"""
        try:
            # Email notification
            subject = f"[CRITICAL] CV Analyzer Error - {error_data['exception_type']}"
            
            context = {
                'error_data': error_data,
                'timestamp': error_data['timestamp'],
                'environment': getattr(settings, 'ENVIRONMENT', 'unknown')
            }
            
            message = render_to_string('emails/critical_error.txt', context)
            html_message = render_to_string('emails/critical_error.html', context)
            
            send_mail(
                subject=subject,
                message=message,
                html_message=html_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[settings.DEFAULT_FROM_EMAIL],  # Configure admin emails
                fail_silently=False
            )
            
        except Exception as e:
            self.logger.error(f"Failed to send critical error notification: {e}")
    
    def get_error_statistics(self, time_range: timedelta = timedelta(hours=24)) -> Dict[str, Any]:
        """Get error statistics for time range"""
        cutoff_time = timezone.now() - time_range
        
        recent_errors = [
            error for error in self.error_storage.values()
            if error['timestamp'] >= cutoff_time
        ]
        
        # Group by exception type
        error_counts = {}
        for error in recent_errors:
            error_type = error['exception_type']
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
        
        # Top errors
        top_errors = sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            'total_errors': len(recent_errors),
            'unique_error_types': len(error_counts),
            'top_errors': top_errors,
            'error_rate_per_hour': len(recent_errors) / (time_range.total_seconds() / 3600),
            'time_range_hours': time_range.total_seconds() / 3600
        }

class ErrorHandlingMiddleware:
    """Middleware for comprehensive error handling"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.error_tracker = ErrorTracker()
        self.logger = StructuredLogger('cv_analyzer.error_middleware')
    
    def __call__(self, request):
        try:
            response = self.get_response(request)
            return response
        except Exception as e:
            return self.handle_exception(request, e)
    
    def handle_exception(self, request: HttpRequest, exception: Exception) -> HttpResponse:
        """Handle exceptions with appropriate responses"""
        
        # Track error
        user_id = request.user.id if hasattr(request, 'user') and request.user.is_authenticated else None
        error_id = self.error_tracker.track_error(
            exception=exception,
            request=request,
            user_id=user_id,
            additional_context={'view': getattr(request, 'resolver_match', {}).get('view_name')}
        )
        
        # Determine response based on exception type
        if isinstance(exception, CVAnalyzerException):
            return self._handle_custom_exception(request, exception)
        elif isinstance(exception, ValidationError):
            return self._handle_validation_error(request, exception, error_id)
        elif isinstance(exception, PermissionDenied):
            return self._handle_permission_denied(request, exception, error_id)
        elif isinstance(exception, (DatabaseError, IntegrityError)):
            return self._handle_database_error(request, exception, error_id)
        else:
            return self._handle_generic_error(request, exception, error_id)
    
    def _handle_custom_exception(self, request: HttpRequest, exception: CVAnalyzerException) -> HttpResponse:
        """Handle custom CV Analyzer exceptions"""
        response_data = exception.to_dict()
        
        status_code_mapping = {
            'FILE_UPLOAD_ERROR': 400,
            'AI_ANALYSIS_ERROR': 502,
            'DATABASE_CONNECTION_ERROR': 503,
            'AUTHENTICATION_ERROR': 401,
            'SECURITY_VIOLATION': 403,
            'BUSINESS_LOGIC_ERROR': 422,
            'EXTERNAL_SERVICE_ERROR': 502
        }
        
        status_code = status_code_mapping.get(exception.error_code, 500)
        
        if request.content_type == 'application/json' or '/api/' in request.path:
            return JsonResponse(response_data, status=status_code)
        else:
            # Render error page for web requests
            from django.shortcuts import render
            return render(request, 'errors/error_page.html', {
                'error': response_data,
                'status_code': status_code
            }, status=status_code)
    
    def _handle_validation_error(self, request: HttpRequest, exception: ValidationError, error_id: str) -> HttpResponse:
        """Handle validation errors"""
        response_data = {
            'error_id': error_id,
            'error_code': 'VALIDATION_ERROR',
            'message': 'Validation failed',
            'details': {'validation_errors': exception.message_dict if hasattr(exception, 'message_dict') else str(exception)},
            'timestamp': timezone.now().isoformat()
        }
        
        if request.content_type == 'application/json' or '/api/' in request.path:
            return JsonResponse(response_data, status=400)
        else:
            from django.shortcuts import render
            return render(request, 'errors/validation_error.html', {
                'error': response_data
            }, status=400)
    
    def _handle_permission_denied(self, request: HttpRequest, exception: PermissionDenied, error_id: str) -> HttpResponse:
        """Handle permission denied errors"""
        response_data = {
            'error_id': error_id,
            'error_code': 'PERMISSION_DENIED',
            'message': 'Access denied',
            'details': {'reason': str(exception)},
            'timestamp': timezone.now().isoformat()
        }
        
        if request.content_type == 'application/json' or '/api/' in request.path:
            return JsonResponse(response_data, status=403)
        else:
            from django.shortcuts import render
            return render(request, 'errors/permission_denied.html', {
                'error': response_data
            }, status=403)
    
    def _handle_database_error(self, request: HttpRequest, exception: Exception, error_id: str) -> HttpResponse:
        """Handle database errors"""
        response_data = {
            'error_id': error_id,
            'error_code': 'DATABASE_ERROR',
            'message': 'Database operation failed',
            'details': {'error_type': type(exception).__name__},
            'timestamp': timezone.now().isoformat()
        }
        
        if request.content_type == 'application/json' or '/api/' in request.path:
            return JsonResponse(response_data, status=503)
        else:
            from django.shortcuts import render
            return render(request, 'errors/database_error.html', {
                'error': response_data
            }, status=503)
    
    def _handle_generic_error(self, request: HttpRequest, exception: Exception, error_id: str) -> HttpResponse:
        """Handle generic server errors"""
        response_data = {
            'error_id': error_id,
            'error_code': 'INTERNAL_SERVER_ERROR',
            'message': 'An internal server error occurred',
            'details': {'error_type': type(exception).__name__} if settings.DEBUG else {},
            'timestamp': timezone.now().isoformat()
        }
        
        if request.content_type == 'application/json' or '/api/' in request.path:
            return JsonResponse(response_data, status=500)
        else:
            from django.shortcuts import render
            return render(request, 'errors/server_error.html', {
                'error': response_data
            }, status=500)

# Global instances
error_tracker = ErrorTracker()
structured_logger = StructuredLogger('cv_analyzer')

# Utility functions
def track_error(exception: Exception, request: HttpRequest = None, 
               user_id: int = None, context: Dict[str, Any] = None) -> str:
    """Utility function to track errors"""
    return error_tracker.track_error(exception, request, user_id, context)

def log_info(message: str, context: Dict[str, Any] = None):
    """Utility function for info logging"""
    structured_logger.info(message, context)

def log_warning(message: str, context: Dict[str, Any] = None, exception: Exception = None):
    """Utility function for warning logging"""
    structured_logger.warning(message, context, exception)

def log_error(message: str, context: Dict[str, Any] = None, exception: Exception = None):
    """Utility function for error logging"""
    structured_logger.error(message, context, exception)

def log_critical(message: str, context: Dict[str, Any] = None, exception: Exception = None):
    """Utility function for critical logging"""
    structured_logger.critical(message, context, exception)

def get_error_statistics(hours: int = 24) -> Dict[str, Any]:
    """Get error statistics for the last N hours"""
    return error_tracker.get_error_statistics(timedelta(hours=hours)) 