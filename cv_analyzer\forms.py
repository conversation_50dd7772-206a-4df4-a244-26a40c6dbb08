from django import forms
from .models import Company, Vacancy, ApplicantProfile, CV

class CompanyForm(forms.ModelForm):
    class Meta:
        model = Company
        fields = ['name', 'description', 'industry']

class VacancyForm(forms.ModelForm):
    class Meta:
        model = Vacancy
        fields = ['company', 'title', 'description', 'requirements', 'category', 'status', 'published']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4}),
            'requirements': forms.Textarea(attrs={'rows': 3}),
        }

class ApplicantProfileForm(forms.ModelForm):
    class Meta:
        model = ApplicantProfile
        fields = ['phone_number', 'address', 'linkedin_profile']

from django.forms.widgets import ClearableFileInput

class MultipleFileInput(ClearableFileInput):
    allow_multiple_selected = True

class CVUploadForm(forms.ModelForm):
    file = forms.FileField(widget=MultipleFileInput(attrs={'multiple': True}))

    class Meta:
        model = CV
        fields = ['file']

