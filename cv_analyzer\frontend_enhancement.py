"""
Frontend Enhancement System for CV Analyzer
Real-time progress, validation, responsive design, and UX improvements
"""

import json
import logging
from typing import Dict, List, Optional, Any
from django.http import JsonResponse
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

logger = logging.getLogger(__name__)

class ProgressTracker:
    """Real-time progress tracking for operations"""
    
    def __init__(self):
        self.progress_cache_prefix = 'progress_'
        self.progress_timeout = 3600
    
    def create_session(self, session_id: str, operation_type: str, total_steps: int, user_id: int = None):
        """Create progress tracking session"""
        progress_data = {
            'session_id': session_id,
            'operation_type': operation_type,
            'total_steps': total_steps,
            'current_step': 0,
            'status': 'initialized',
            'progress_percentage': 0,
            'start_time': timezone.now().isoformat(),
            'user_id': user_id,
            'steps': []
        }
        
        cache_key = f"{self.progress_cache_prefix}{session_id}"
        cache.set(cache_key, progress_data, timeout=self.progress_timeout)
        return progress_data
    
    def update_progress(self, session_id: str, step_name: str, details: Dict = None):
        """Update progress step"""
        cache_key = f"{self.progress_cache_prefix}{session_id}"
        progress_data = cache.get(cache_key)
        
        if not progress_data:
            return None
        
        progress_data['current_step'] += 1
        progress_data['progress_percentage'] = (progress_data['current_step'] / progress_data['total_steps']) * 100
        progress_data['status'] = 'in_progress'
        
        step_info = {
            'step_number': progress_data['current_step'],
            'step_name': step_name,
            'timestamp': timezone.now().isoformat(),
            'details': details or {}
        }
        progress_data['steps'].append(step_info)
        
        cache.set(cache_key, progress_data, timeout=self.progress_timeout)
        return progress_data
    
    def complete_progress(self, session_id: str, success: bool = True, error_message: str = None):
        """Mark progress as completed"""
        cache_key = f"{self.progress_cache_prefix}{session_id}"
        progress_data = cache.get(cache_key)
        
        if progress_data:
            progress_data['status'] = 'completed' if success else 'failed'
            progress_data['progress_percentage'] = 100 if success else progress_data['progress_percentage']
            progress_data['end_time'] = timezone.now().isoformat()
            progress_data['error_message'] = error_message
            
            cache.set(cache_key, progress_data, timeout=self.progress_timeout)
        
        return progress_data
    
    def get_progress(self, session_id: str):
        """Get current progress"""
        cache_key = f"{self.progress_cache_prefix}{session_id}"
        return cache.get(cache_key)

class ClientSideValidator:
    """Client-side validation system"""
    
    def __init__(self):
        self.validation_rules = {
            'cv_upload': {
                'file_types': ['.pdf', '.doc', '.docx'],
                'max_size_mb': 10,
                'required': True
            },
            'user_profile': {
                'email': {'pattern': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', 'required': True},
                'phone': {'pattern': r'^\+?[\d\s\-\(\)]{10,}$', 'required': False},
                'name': {'min_length': 2, 'max_length': 100, 'required': True}
            }
        }
    
    def get_validation_rules(self, form_type: str):
        """Get validation rules for form type"""
        return self.validation_rules.get(form_type, {})
    
    def generate_javascript_validation(self, form_type: str):
        """Generate JavaScript validation code"""
        rules = self.get_validation_rules(form_type)
        
        js_code = f"""
const validationRules = {json.dumps(rules)};

function validateForm(formData) {{
    const errors = [];
    
    // File validation for CV upload
    if (formData.file && validationRules.file_types) {{
        const file = formData.file;
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!validationRules.file_types.includes(fileExtension)) {{
            errors.push('Invalid file type. Allowed: ' + validationRules.file_types.join(', '));
        }}
        
        if (file.size > validationRules.max_size_mb * 1024 * 1024) {{
            errors.push('File too large. Max size: ' + validationRules.max_size_mb + 'MB');
        }}
    }}
    
    // Field validation
    Object.keys(validationRules).forEach(fieldName => {{
        if (fieldName === 'file_types' || fieldName === 'max_size_mb') return;
        
        const fieldRules = validationRules[fieldName];
        const fieldValue = formData[fieldName];
        
        if (fieldRules.required && (!fieldValue || fieldValue.trim() === '')) {{
            errors.push(fieldName + ' is required');
        }}
        
        if (fieldValue && fieldRules.pattern) {{
            const regex = new RegExp(fieldRules.pattern);
            if (!regex.test(fieldValue)) {{
                errors.push(fieldName + ' format is invalid');
            }}
        }}
        
        if (fieldValue && fieldRules.min_length && fieldValue.length < fieldRules.min_length) {{
            errors.push(fieldName + ' too short (min: ' + fieldRules.min_length + ')');
        }}
    }});
    
    return errors;
}}
"""
        return js_code

class ResponsiveDesignManager:
    """Responsive design and mobile optimization"""
    
    def get_responsive_css(self):
        """Generate responsive CSS"""
        return """
/* Mobile First Responsive Design */
.container {
    padding: 1rem;
    max-width: 100%;
}

/* Tablet */
@media (min-width: 768px) {
    .container {
        padding: 1.5rem;
        max-width: 750px;
        margin: 0 auto;
    }
    .cv-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

/* Desktop */
@media (min-width: 1024px) {
    .container {
        max-width: 1200px;
        padding: 2rem;
    }
    .cv-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
}

/* Mobile Navigation */
@media (max-width: 767px) {
    .nav-menu {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .nav-menu.active { display: flex; }
    .hamburger-menu { display: block; }
}

@media (min-width: 768px) {
    .hamburger-menu { display: none; }
    .nav-menu {
        display: flex !important;
        flex-direction: row;
        position: static;
        box-shadow: none;
    }
}
"""
    
    def get_responsive_javascript(self):
        """Generate responsive JavaScript"""
        return """
class ResponsiveManager {
    constructor() {
        this.init();
    }
    
    init() {
        window.addEventListener('resize', this.handleResize.bind(this));
        this.initializeNavigation();
        this.initializeGridLayouts();
    }
    
    handleResize() {
        this.updateGridLayouts();
    }
    
    initializeNavigation() {
        const hamburger = document.querySelector('.hamburger-menu');
        const nav = document.querySelector('.nav-menu');
        
        if (hamburger && nav) {
            hamburger.addEventListener('click', () => {
                nav.classList.toggle('active');
            });
        }
    }
    
    initializeGridLayouts() {
        const grids = document.querySelectorAll('[data-responsive-grid]');
        grids.forEach(grid => this.adjustGridColumns(grid));
    }
    
    adjustGridColumns(grid) {
        const minItemWidth = parseInt(grid.dataset.minItemWidth) || 250;
        const gridWidth = grid.offsetWidth;
        const columns = Math.floor(gridWidth / minItemWidth);
        const maxColumns = parseInt(grid.dataset.maxColumns) || 4;
        
        grid.style.gridTemplateColumns = `repeat(${Math.min(columns, maxColumns)}, 1fr)`;
    }
    
    updateGridLayouts() {
        const grids = document.querySelectorAll('[data-responsive-grid]');
        grids.forEach(grid => this.adjustGridColumns(grid));
    }
}

document.addEventListener('DOMContentLoaded', () => {
    window.responsiveManager = new ResponsiveManager();
});
"""

class ProgressiveWebApp:
    """Progressive Web App features"""
    
    def get_service_worker(self):
        """Generate service worker for PWA"""
        return """
const CACHE_NAME = 'cv-analyzer-v1';
const urlsToCache = [
    '/',
    '/static/css/main.css',
    '/static/js/main.js',
    '/static/images/logo.png'
];

self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => cache.addAll(urlsToCache))
    );
});

self.addEventListener('fetch', event => {
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                return response || fetch(event.request);
            })
    );
});
"""
    
    def get_manifest(self):
        """Generate PWA manifest"""
        return {
            "name": "CV Analyzer",
            "short_name": "CVAnalyzer",
            "description": "Professional CV Analysis Tool",
            "start_url": "/",
            "display": "standalone",
            "background_color": "#ffffff",
            "theme_color": "#007bff",
            "orientation": "portrait",
            "icons": [
                {
                    "src": "/static/images/icon-192.png",
                    "sizes": "192x192",
                    "type": "image/png"
                },
                {
                    "src": "/static/images/icon-512.png",
                    "sizes": "512x512",
                    "type": "image/png"
                }
            ]
        }

class AccessibilityEnhancer:
    """WCAG 2.1 accessibility compliance"""
    
    def get_accessibility_javascript(self):
        """Generate accessibility enhancement JavaScript"""
        return """
class AccessibilityEnhancer {
    constructor() {
        this.init();
    }
    
    init() {
        this.addSkipLinks();
        this.enhanceKeyboardNavigation();
        this.addAriaLabels();
        this.setupFocusManagement();
    }
    
    addSkipLinks() {
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.textContent = 'Skip to main content';
        skipLink.className = 'skip-link';
        document.body.insertBefore(skipLink, document.body.firstChild);
    }
    
    enhanceKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });
        
        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });
    }
    
    addAriaLabels() {
        // Add ARIA labels to interactive elements
        document.querySelectorAll('button, input, select, textarea').forEach(element => {
            if (!element.getAttribute('aria-label') && !element.getAttribute('aria-labelledby')) {
                const label = element.previousElementSibling;
                if (label && label.tagName === 'LABEL') {
                    const labelId = 'label-' + Date.now() + Math.random();
                    label.id = labelId;
                    element.setAttribute('aria-labelledby', labelId);
                }
            }
        });
    }
    
    setupFocusManagement() {
        // Focus management for modals
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('show', () => {
                const firstFocusable = modal.querySelector('button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
                if (firstFocusable) {
                    firstFocusable.focus();
                }
            });
        });
    }
}

document.addEventListener('DOMContentLoaded', () => {
    new AccessibilityEnhancer();
});
"""
    
    def get_accessibility_css(self):
        """Generate accessibility CSS"""
        return """
/* Accessibility Enhancements */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* Focus indicators */
.keyboard-navigation *:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
"""

# Global instances
progress_tracker = ProgressTracker()
client_validator = ClientSideValidator()
responsive_manager = ResponsiveDesignManager()
pwa_manager = ProgressiveWebApp()
accessibility_enhancer = AccessibilityEnhancer()

# Utility functions
def create_progress_session(session_id: str, operation_type: str, total_steps: int, user_id: int = None):
    return progress_tracker.create_session(session_id, operation_type, total_steps, user_id)

def update_progress(session_id: str, step_name: str, details: Dict = None):
    return progress_tracker.update_progress(session_id, step_name, details)

def get_progress_status(session_id: str):
    return progress_tracker.get_progress(session_id)

def get_validation_rules(form_type: str):
    return client_validator.get_validation_rules(form_type) 