"""
Production Incident Response System
Handles incident detection, escalation, and automated response procedures for production environment.
"""

import os
import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import requests
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

logger = logging.getLogger(__name__)

class IncidentSeverity(Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class IncidentStatus(Enum):
    OPEN = "open"
    ACKNOWLEDGED = "acknowledged"
    INVESTIGATING = "investigating"
    RESOLVED = "resolved"
    CLOSED = "closed"

@dataclass
class Incident:
    """Incident data structure"""
    id: str
    title: str
    description: str
    severity: IncidentSeverity
    status: IncidentStatus
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime] = None
    assigned_to: Optional[str] = None
    escalation_level: int = 0
    affected_services: List[str] = None
    root_cause: Optional[str] = None
    resolution_notes: Optional[str] = None
    
    def __post_init__(self):
        if self.affected_services is None:
            self.affected_services = []

@dataclass
class AlertRule:
    """Alert rule configuration"""
    id: str
    name: str
    description: str
    metric: str
    threshold: float
    operator: str  # gt, lt, eq, ne
    duration: int  # seconds
    severity: IncidentSeverity
    enabled: bool = True
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []

class IncidentResponseManager:
    """Comprehensive incident response management"""
    
    def __init__(self):
        self.incidents = {}
        self.alert_rules = self._load_alert_rules()
        self.escalation_policies = self._load_escalation_policies()
        self.response_procedures = self._load_response_procedures()
        self.notification_channels = self._load_notification_channels()
        
    def _load_alert_rules(self) -> List[AlertRule]:
        """Load alert rules configuration"""
        return [
            AlertRule(
                id="high_response_time",
                name="High Response Time",
                description="API response time exceeds threshold",
                metric="http_request_duration_seconds",
                threshold=5.0,
                operator="gt",
                duration=300,  # 5 minutes
                severity=IncidentSeverity.HIGH,
                tags=["performance", "api"]
            ),
            AlertRule(
                id="high_error_rate",
                name="High Error Rate",
                description="Error rate exceeds acceptable threshold",
                metric="http_requests_total",
                threshold=0.05,  # 5%
                operator="gt",
                duration=180,  # 3 minutes
                severity=IncidentSeverity.CRITICAL,
                tags=["errors", "availability"]
            ),
            AlertRule(
                id="database_connection_failure",
                name="Database Connection Failure",
                description="Database connection failures detected",
                metric="database_connections_failed_total",
                threshold=0,
                operator="gt",
                duration=60,  # 1 minute
                severity=IncidentSeverity.CRITICAL,
                tags=["database", "connectivity"]
            ),
            AlertRule(
                id="high_cpu_usage",
                name="High CPU Usage",
                description="CPU usage exceeds threshold",
                metric="cpu_usage_percent",
                threshold=80.0,
                operator="gt",
                duration=600,  # 10 minutes
                severity=IncidentSeverity.MEDIUM,
                tags=["performance", "resources"]
            ),
            AlertRule(
                id="high_memory_usage",
                name="High Memory Usage",
                description="Memory usage exceeds threshold",
                metric="memory_usage_percent",
                threshold=85.0,
                operator="gt",
                duration=300,  # 5 minutes
                severity=IncidentSeverity.HIGH,
                tags=["performance", "resources"]
            ),
            AlertRule(
                id="disk_space_low",
                name="Low Disk Space",
                description="Disk space below threshold",
                metric="disk_free_percent",
                threshold=10.0,
                operator="lt",
                duration=60,  # 1 minute
                severity=IncidentSeverity.HIGH,
                tags=["storage", "resources"]
            ),
            AlertRule(
                id="ssl_certificate_expiry",
                name="SSL Certificate Expiring",
                description="SSL certificate expires soon",
                metric="ssl_certificate_expiry_days",
                threshold=30,
                operator="lt",
                duration=3600,  # 1 hour
                severity=IncidentSeverity.MEDIUM,
                tags=["security", "ssl"]
            ),
            AlertRule(
                id="failed_deployments",
                name="Failed Deployments",
                description="Deployment failures detected",
                metric="deployment_failures_total",
                threshold=0,
                operator="gt",
                duration=60,  # 1 minute
                severity=IncidentSeverity.HIGH,
                tags=["deployment", "ci_cd"]
            )
        ]
    
    def _load_escalation_policies(self) -> Dict[str, Any]:
        """Load escalation policies"""
        return {
            "default": {
                "levels": [
                    {
                        "level": 1,
                        "timeout": 900,  # 15 minutes
                        "contacts": ["<EMAIL>"],
                        "channels": ["email", "slack"]
                    },
                    {
                        "level": 2,
                        "timeout": 1800,  # 30 minutes
                        "contacts": ["<EMAIL>", "<EMAIL>"],
                        "channels": ["email", "slack", "phone"]
                    },
                    {
                        "level": 3,
                        "timeout": 3600,  # 1 hour
                        "contacts": ["<EMAIL>"],
                        "channels": ["email", "phone"]
                    }
                ]
            },
            "critical": {
                "levels": [
                    {
                        "level": 1,
                        "timeout": 300,  # 5 minutes
                        "contacts": ["<EMAIL>", "<EMAIL>"],
                        "channels": ["email", "slack", "phone"]
                    },
                    {
                        "level": 2,
                        "timeout": 900,  # 15 minutes
                        "contacts": ["<EMAIL>", "<EMAIL>"],
                        "channels": ["email", "phone"]
                    }
                ]
            }
        }
    
    def _load_response_procedures(self) -> Dict[str, Any]:
        """Load automated response procedures"""
        return {
            "high_response_time": {
                "auto_actions": [
                    "scale_up_replicas",
                    "clear_cache",
                    "restart_slow_workers"
                ],
                "runbook": "performance_degradation"
            },
            "high_error_rate": {
                "auto_actions": [
                    "enable_circuit_breaker",
                    "rollback_recent_deployment",
                    "switch_to_maintenance_mode"
                ],
                "runbook": "high_error_rate_response"
            },
            "database_connection_failure": {
                "auto_actions": [
                    "restart_database_connections",
                    "failover_to_read_replica",
                    "enable_database_circuit_breaker"
                ],
                "runbook": "database_incident_response"
            },
            "high_cpu_usage": {
                "auto_actions": [
                    "scale_up_replicas",
                    "adjust_resource_limits"
                ],
                "runbook": "resource_management"
            },
            "disk_space_low": {
                "auto_actions": [
                    "cleanup_temp_files",
                    "compress_old_logs",
                    "alert_storage_team"
                ],
                "runbook": "storage_management"
            }
        }
    
    def _load_notification_channels(self) -> Dict[str, Any]:
        """Load notification channel configurations"""
        return {
            "email": {
                "smtp_server": os.getenv('SMTP_SERVER', 'smtp.gmail.com'),
                "smtp_port": int(os.getenv('SMTP_PORT', '587')),
                "username": os.getenv('SMTP_USERNAME'),
                "password": os.getenv('SMTP_PASSWORD'),
                "from_email": os.getenv('ALERT_FROM_EMAIL', '<EMAIL>')
            },
            "slack": {
                "webhook_url": os.getenv('SLACK_WEBHOOK_URL'),
                "channel": "#alerts",
                "username": "CV Analyzer Alerts"
            },
            "pagerduty": {
                "api_key": os.getenv('PAGERDUTY_API_KEY'),
                "service_key": os.getenv('PAGERDUTY_SERVICE_KEY')
            },
            "webhook": {
                "url": os.getenv('ALERT_WEBHOOK_URL'),
                "timeout": 10
            }
        }
    
    async def create_incident(self, 
                            title: str, 
                            description: str, 
                            severity: IncidentSeverity,
                            affected_services: List[str] = None) -> Incident:
        """Create new incident"""
        incident_id = f"INC-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        incident = Incident(
            id=incident_id,
            title=title,
            description=description,
            severity=severity,
            status=IncidentStatus.OPEN,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            affected_services=affected_services or []
        )
        
        self.incidents[incident_id] = incident
        
        # Send notifications
        await self.send_incident_notification(incident, "created")
        
        # Start escalation timer
        asyncio.create_task(self.start_escalation_timer(incident_id))
        
        # Execute automated response
        await self.execute_automated_response(incident)
        
        logger.info(f"Incident created: {incident_id} - {title}")
        return incident
    
    async def update_incident(self, 
                            incident_id: str, 
                            status: Optional[IncidentStatus] = None,
                            assigned_to: Optional[str] = None,
                            resolution_notes: Optional[str] = None) -> Incident:
        """Update existing incident"""
        if incident_id not in self.incidents:
            raise ValueError(f"Incident {incident_id} not found")
        
        incident = self.incidents[incident_id]
        incident.updated_at = datetime.now()
        
        if status:
            incident.status = status
            if status == IncidentStatus.RESOLVED:
                incident.resolved_at = datetime.now()
        
        if assigned_to:
            incident.assigned_to = assigned_to
        
        if resolution_notes:
            incident.resolution_notes = resolution_notes
        
        # Send update notification
        await self.send_incident_notification(incident, "updated")
        
        logger.info(f"Incident updated: {incident_id}")
        return incident
    
    async def start_escalation_timer(self, incident_id: str):
        """Start escalation timer for incident"""
        incident = self.incidents.get(incident_id)
        if not incident:
            return
        
        policy_name = "critical" if incident.severity == IncidentSeverity.CRITICAL else "default"
        policy = self.escalation_policies[policy_name]
        
        for level_config in policy["levels"]:
            if incident.status in [IncidentStatus.RESOLVED, IncidentStatus.CLOSED]:
                break
            
            # Wait for timeout
            await asyncio.sleep(level_config["timeout"])
            
            # Check if incident is still open
            current_incident = self.incidents.get(incident_id)
            if not current_incident or current_incident.status in [IncidentStatus.RESOLVED, IncidentStatus.CLOSED]:
                break
            
            # Escalate
            current_incident.escalation_level = level_config["level"]
            await self.escalate_incident(current_incident, level_config)
    
    async def escalate_incident(self, incident: Incident, level_config: Dict[str, Any]):
        """Escalate incident to next level"""
        logger.warning(f"Escalating incident {incident.id} to level {level_config['level']}")
        
        # Send escalation notifications
        for contact in level_config["contacts"]:
            for channel in level_config["channels"]:
                await self.send_escalation_notification(incident, contact, channel)
        
        # Update incident
        incident.updated_at = datetime.now()
        await self.send_incident_notification(incident, "escalated")
    
    async def execute_automated_response(self, incident: Incident):
        """Execute automated response procedures"""
        # Determine response based on incident characteristics
        response_key = self._determine_response_key(incident)
        
        if response_key not in self.response_procedures:
            logger.info(f"No automated response configured for incident type: {response_key}")
            return
        
        response_config = self.response_procedures[response_key]
        
        logger.info(f"Executing automated response for incident {incident.id}: {response_key}")
        
        # Execute auto actions
        for action in response_config.get("auto_actions", []):
            try:
                await self._execute_auto_action(action, incident)
            except Exception as e:
                logger.error(f"Failed to execute auto action {action}: {e}")
        
        # Start runbook execution if available
        runbook = response_config.get("runbook")
        if runbook:
            await self._execute_response_runbook(runbook, incident)
    
    def _determine_response_key(self, incident: Incident) -> str:
        """Determine response key based on incident characteristics"""
        # Simple mapping based on title/description keywords
        title_lower = incident.title.lower()
        description_lower = incident.description.lower()
        
        if "response time" in title_lower or "slow" in description_lower:
            return "high_response_time"
        elif "error rate" in title_lower or "errors" in description_lower:
            return "high_error_rate"
        elif "database" in title_lower or "connection" in description_lower:
            return "database_connection_failure"
        elif "cpu" in title_lower:
            return "high_cpu_usage"
        elif "disk" in title_lower or "storage" in description_lower:
            return "disk_space_low"
        else:
            return "default"
    
    async def _execute_auto_action(self, action: str, incident: Incident):
        """Execute individual automated action"""
        logger.info(f"Executing auto action: {action} for incident {incident.id}")
        
        if action == "scale_up_replicas":
            await self._scale_up_replicas()
        elif action == "clear_cache":
            await self._clear_cache()
        elif action == "restart_slow_workers":
            await self._restart_slow_workers()
        elif action == "enable_circuit_breaker":
            await self._enable_circuit_breaker()
        elif action == "rollback_recent_deployment":
            await self._rollback_recent_deployment()
        elif action == "switch_to_maintenance_mode":
            await self._switch_to_maintenance_mode()
        elif action == "restart_database_connections":
            await self._restart_database_connections()
        elif action == "failover_to_read_replica":
            await self._failover_to_read_replica()
        elif action == "cleanup_temp_files":
            await self._cleanup_temp_files()
        elif action == "compress_old_logs":
            await self._compress_old_logs()
        else:
            logger.warning(f"Unknown auto action: {action}")
    
    async def _scale_up_replicas(self):
        """Scale up application replicas"""
        # Implement Kubernetes scaling logic
        logger.info("Scaling up application replicas")
    
    async def _clear_cache(self):
        """Clear application cache"""
        # Implement cache clearing logic
        logger.info("Clearing application cache")
    
    async def _restart_slow_workers(self):
        """Restart slow worker processes"""
        # Implement worker restart logic
        logger.info("Restarting slow worker processes")
    
    async def _enable_circuit_breaker(self):
        """Enable circuit breaker"""
        # Implement circuit breaker logic
        logger.info("Enabling circuit breaker")
    
    async def _rollback_recent_deployment(self):
        """Rollback recent deployment"""
        # Implement rollback logic
        logger.info("Rolling back recent deployment")
    
    async def _switch_to_maintenance_mode(self):
        """Switch to maintenance mode"""
        # Implement maintenance mode logic
        logger.info("Switching to maintenance mode")
    
    async def _restart_database_connections(self):
        """Restart database connections"""
        # Implement database connection restart logic
        logger.info("Restarting database connections")
    
    async def _failover_to_read_replica(self):
        """Failover to read replica"""
        # Implement database failover logic
        logger.info("Failing over to read replica")
    
    async def _cleanup_temp_files(self):
        """Cleanup temporary files"""
        # Implement temp file cleanup logic
        logger.info("Cleaning up temporary files")
    
    async def _compress_old_logs(self):
        """Compress old log files"""
        # Implement log compression logic
        logger.info("Compressing old log files")
    
    async def _execute_response_runbook(self, runbook: str, incident: Incident):
        """Execute response runbook"""
        logger.info(f"Executing response runbook: {runbook} for incident {incident.id}")
        
        # Import and execute runbook
        try:
            from cv_analyzer.deployment_runbooks import DeploymentRunbooks
            runbooks = DeploymentRunbooks()
            
            if runbook in runbooks.list_available_runbooks():
                result = await runbooks.execute_runbook(runbook)
                logger.info(f"Runbook execution result: {result['overall_status']}")
            else:
                logger.warning(f"Runbook not found: {runbook}")
                
        except Exception as e:
            logger.error(f"Failed to execute runbook {runbook}: {e}")
    
    async def send_incident_notification(self, incident: Incident, action: str):
        """Send incident notification"""
        message = self._format_incident_message(incident, action)
        
        # Send to all configured channels
        for channel in ["email", "slack", "webhook"]:
            try:
                await self._send_notification(channel, message, incident)
            except Exception as e:
                logger.error(f"Failed to send notification via {channel}: {e}")
    
    async def send_escalation_notification(self, incident: Incident, contact: str, channel: str):
        """Send escalation notification"""
        message = self._format_escalation_message(incident)
        
        try:
            await self._send_notification(channel, message, incident, contact)
        except Exception as e:
            logger.error(f"Failed to send escalation notification to {contact} via {channel}: {e}")
    
    def _format_incident_message(self, incident: Incident, action: str) -> str:
        """Format incident message"""
        return f"""
🚨 Incident {action.upper()}: {incident.title}

ID: {incident.id}
Severity: {incident.severity.value.upper()}
Status: {incident.status.value.upper()}
Created: {incident.created_at.strftime('%Y-%m-%d %H:%M:%S UTC')}
Affected Services: {', '.join(incident.affected_services) if incident.affected_services else 'None'}

Description:
{incident.description}

Escalation Level: {incident.escalation_level}
Assigned To: {incident.assigned_to or 'Unassigned'}

{f'Resolution: {incident.resolution_notes}' if incident.resolution_notes else ''}
"""
    
    def _format_escalation_message(self, incident: Incident) -> str:
        """Format escalation message"""
        return f"""
🔥 ESCALATION ALERT: {incident.title}

This incident has been escalated to Level {incident.escalation_level} due to lack of acknowledgment.

ID: {incident.id}
Severity: {incident.severity.value.upper()}
Status: {incident.status.value.upper()}
Duration: {datetime.now() - incident.created_at}

IMMEDIATE ATTENTION REQUIRED
"""
    
    async def _send_notification(self, channel: str, message: str, incident: Incident, contact: Optional[str] = None):
        """Send notification via specific channel"""
        config = self.notification_channels.get(channel)
        if not config:
            logger.warning(f"No configuration found for channel: {channel}")
            return
        
        if channel == "email":
            await self._send_email_notification(config, message, incident, contact)
        elif channel == "slack":
            await self._send_slack_notification(config, message, incident)
        elif channel == "webhook":
            await self._send_webhook_notification(config, message, incident)
        elif channel == "pagerduty":
            await self._send_pagerduty_notification(config, message, incident)
    
    async def _send_email_notification(self, config: Dict[str, Any], message: str, incident: Incident, contact: Optional[str] = None):
        """Send email notification"""
        if not config.get('username') or not config.get('password'):
            logger.warning("Email configuration incomplete")
            return
        
        try:
            msg = MIMEMultipart()
            msg['From'] = config['from_email']
            msg['To'] = contact or '<EMAIL>'
            msg['Subject'] = f"[{incident.severity.value.upper()}] {incident.title}"
            
            msg.attach(MIMEText(message, 'plain'))
            
            server = smtplib.SMTP(config['smtp_server'], config['smtp_port'])
            server.starttls()
            server.login(config['username'], config['password'])
            server.send_message(msg)
            server.quit()
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")
    
    async def _send_slack_notification(self, config: Dict[str, Any], message: str, incident: Incident):
        """Send Slack notification"""
        if not config.get('webhook_url'):
            logger.warning("Slack webhook URL not configured")
            return
        
        try:
            color = {
                IncidentSeverity.CRITICAL: "danger",
                IncidentSeverity.HIGH: "warning",
                IncidentSeverity.MEDIUM: "good",
                IncidentSeverity.LOW: "#36a64f"
            }.get(incident.severity, "warning")
            
            payload = {
                "channel": config.get('channel', '#alerts'),
                "username": config.get('username', 'CV Analyzer Alerts'),
                "attachments": [{
                    "color": color,
                    "title": f"Incident: {incident.title}",
                    "text": message,
                    "fields": [
                        {"title": "ID", "value": incident.id, "short": True},
                        {"title": "Severity", "value": incident.severity.value.upper(), "short": True},
                        {"title": "Status", "value": incident.status.value.upper(), "short": True}
                    ],
                    "footer": "CV Analyzer Monitoring",
                    "ts": int(incident.created_at.timestamp())
                }]
            }
            
            response = requests.post(config['webhook_url'], json=payload, timeout=10)
            response.raise_for_status()
            
        except Exception as e:
            logger.error(f"Failed to send Slack notification: {e}")
    
    async def _send_webhook_notification(self, config: Dict[str, Any], message: str, incident: Incident):
        """Send webhook notification"""
        if not config.get('url'):
            logger.warning("Webhook URL not configured")
            return
        
        try:
            payload = {
                "incident": asdict(incident),
                "message": message,
                "timestamp": datetime.now().isoformat()
            }
            
            response = requests.post(
                config['url'], 
                json=payload, 
                timeout=config.get('timeout', 10)
            )
            response.raise_for_status()
            
        except Exception as e:
            logger.error(f"Failed to send webhook notification: {e}")
    
    async def _send_pagerduty_notification(self, config: Dict[str, Any], message: str, incident: Incident):
        """Send PagerDuty notification"""
        if not config.get('service_key'):
            logger.warning("PagerDuty service key not configured")
            return
        
        try:
            payload = {
                "service_key": config['service_key'],
                "event_type": "trigger",
                "incident_key": incident.id,
                "description": incident.title,
                "details": {
                    "severity": incident.severity.value,
                    "status": incident.status.value,
                    "description": incident.description,
                    "affected_services": incident.affected_services
                }
            }
            
            response = requests.post(
                "https://events.pagerduty.com/generic/2010-04-15/create_event.json",
                json=payload,
                timeout=10
            )
            response.raise_for_status()
            
        except Exception as e:
            logger.error(f"Failed to send PagerDuty notification: {e}")
    
    def get_incident(self, incident_id: str) -> Optional[Incident]:
        """Get incident by ID"""
        return self.incidents.get(incident_id)
    
    def list_incidents(self, status: Optional[IncidentStatus] = None, severity: Optional[IncidentSeverity] = None) -> List[Incident]:
        """List incidents with optional filtering"""
        incidents = list(self.incidents.values())
        
        if status:
            incidents = [i for i in incidents if i.status == status]
        
        if severity:
            incidents = [i for i in incidents if i.severity == severity]
        
        return sorted(incidents, key=lambda x: x.created_at, reverse=True)
    
    def get_incident_metrics(self) -> Dict[str, Any]:
        """Get incident metrics"""
        incidents = list(self.incidents.values())
        
        return {
            "total_incidents": len(incidents),
            "open_incidents": len([i for i in incidents if i.status == IncidentStatus.OPEN]),
            "critical_incidents": len([i for i in incidents if i.severity == IncidentSeverity.CRITICAL]),
            "average_resolution_time": self._calculate_average_resolution_time(incidents),
            "incidents_by_severity": {
                severity.value: len([i for i in incidents if i.severity == severity])
                for severity in IncidentSeverity
            },
            "incidents_by_status": {
                status.value: len([i for i in incidents if i.status == status])
                for status in IncidentStatus
            }
        }
    
    def _calculate_average_resolution_time(self, incidents: List[Incident]) -> Optional[float]:
        """Calculate average resolution time in minutes"""
        resolved_incidents = [i for i in incidents if i.resolved_at]
        
        if not resolved_incidents:
            return None
        
        total_time = sum(
            (i.resolved_at - i.created_at).total_seconds()
            for i in resolved_incidents
        )
        
        return total_time / len(resolved_incidents) / 60  # Convert to minutes

# Example usage
if __name__ == "__main__":
    async def main():
        incident_manager = IncidentResponseManager()
        
        # Create test incident
        incident = await incident_manager.create_incident(
            title="High API Response Time",
            description="API response time has exceeded 5 seconds for the past 10 minutes",
            severity=IncidentSeverity.HIGH,
            affected_services=["api", "web"]
        )
        
        print(f"Created incident: {incident.id}")
        
        # Get metrics
        metrics = incident_manager.get_incident_metrics()
        print(f"Incident metrics: {metrics}")
    
    asyncio.run(main()) 