"""
JavaScript Enhancement System
Modern JavaScript features, real-time updates, and interactive components
"""

import json
import logging
from typing import Dict, List, Optional, Any
from django.http import JsonResponse
from django.template.loader import render_to_string
from django.conf import settings

logger = logging.getLogger(__name__)

class JavaScriptEnhancer:
    """Modern JavaScript features and enhancements"""
    
    def __init__(self):
        self.features = {
            'real_time_updates': True,
            'drag_drop': True,
            'auto_save': True,
            'keyboard_shortcuts': True,
            'charts_visualization': True
        }
    
    def get_core_javascript(self):
        """Generate core JavaScript enhancements"""
        return """
// Core JavaScript Enhancement System
class CVAnalyzer {
    constructor() {
        this.config = {
            apiBase: '/api/v1',
            websocketUrl: 'ws://localhost:8000/ws',
            autoSaveInterval: 30000
        };
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initializeFeatures();
        this.setupWebSocket();
    }
    
    setupEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeDragDrop();
            this.setupKeyboardShortcuts();
            this.initializeAutoSave();
        });
    }
    
    // Real-time WebSocket connection
    setupWebSocket() {
        this.ws = new WebSocket(this.config.websocketUrl);
        
        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.showNotification('Connected to real-time updates', 'success');
        };
        
        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleRealTimeUpdate(data);
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket disconnected');
            this.showNotification('Connection lost. Attempting to reconnect...', 'warning');
            setTimeout(() => this.setupWebSocket(), 5000);
        };
    }
    
    handleRealTimeUpdate(data) {
        switch (data.type) {
            case 'cv_analysis_progress':
                this.updateAnalysisProgress(data.session_id, data.progress);
                break;
            case 'new_cv_uploaded':
                this.addNewCVToList(data.cv_data);
                break;
            case 'analysis_completed':
                this.showAnalysisResults(data.results);
                break;
            default:
                console.log('Unknown update type:', data.type);
        }
    }
    
    // Drag and Drop functionality
    initializeDragDrop() {
        const dropZone = document.getElementById('cv-drop-zone');
        if (!dropZone) return;
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, this.preventDefaults, false);
        });
        
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => dropZone.classList.add('drag-over'), false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => dropZone.classList.remove('drag-over'), false);
        });
        
        dropZone.addEventListener('drop', this.handleDrop.bind(this), false);
    }
    
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    handleDrop(e) {
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.uploadFiles(files);
        }
    }
    
    async uploadFiles(files) {
        for (let file of files) {
            if (this.validateFile(file)) {
                await this.uploadSingleFile(file);
            }
        }
    }
    
    validateFile(file) {
        const allowedTypes = ['application/pdf', 'application/msword', 
                            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        const maxSize = 10 * 1024 * 1024; // 10MB
        
        if (!allowedTypes.includes(file.type)) {
            this.showNotification('Invalid file type. Please upload PDF or Word documents.', 'error');
            return false;
        }
        
        if (file.size > maxSize) {
            this.showNotification('File too large. Maximum size is 10MB.', 'error');
            return false;
        }
        
        return true;
    }
    
    async uploadSingleFile(file) {
        const formData = new FormData();
        formData.append('cv_file', file);
        
        const progressId = 'upload_' + Date.now();
        this.createProgressBar(progressId, file.name);
        
        try {
            const response = await fetch('/api/v1/cvs/upload/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            if (response.ok) {
                const result = await response.json();
                this.showNotification(`${file.name} uploaded successfully`, 'success');
                this.removeProgressBar(progressId);
                this.addNewCVToList(result);
            } else {
                throw new Error('Upload failed');
            }
        } catch (error) {
            this.showNotification(`Failed to upload ${file.name}`, 'error');
            this.removeProgressBar(progressId);
        }
    }
    
    // Auto-save functionality
    initializeAutoSave() {
        const forms = document.querySelectorAll('[data-auto-save]');
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('input', () => {
                    this.scheduleAutoSave(form);
                });
            });
        });
    }
    
    scheduleAutoSave(form) {
        if (form.autoSaveTimeout) {
            clearTimeout(form.autoSaveTimeout);
        }
        
        form.autoSaveTimeout = setTimeout(() => {
            this.performAutoSave(form);
        }, this.config.autoSaveInterval);
    }
    
    async performAutoSave(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        try {
            const response = await fetch(form.action || '/api/v1/auto-save/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify(data)
            });
            
            if (response.ok) {
                this.showAutoSaveIndicator();
            }
        } catch (error) {
            console.error('Auto-save failed:', error);
        }
    }
    
    // Keyboard shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + S for save
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.saveCurrentForm();
            }
            
            // Ctrl/Cmd + U for upload
            if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
                e.preventDefault();
                this.triggerFileUpload();
            }
            
            // Escape to close modals
            if (e.key === 'Escape') {
                this.closeActiveModals();
            }
            
            // Arrow keys for navigation
            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                this.handleArrowNavigation(e);
            }
        });
    }
    
    saveCurrentForm() {
        const activeForm = document.querySelector('form:focus-within');
        if (activeForm) {
            this.performAutoSave(activeForm);
            this.showNotification('Form saved', 'success');
        }
    }
    
    triggerFileUpload() {
        const fileInput = document.querySelector('input[type="file"]');
        if (fileInput) {
            fileInput.click();
        }
    }
    
    // Progress tracking
    createProgressBar(id, filename) {
        const container = document.getElementById('progress-container') || this.createProgressContainer();
        
        const progressBar = document.createElement('div');
        progressBar.id = id;
        progressBar.className = 'progress-item';
        progressBar.innerHTML = `
            <div class="progress-info">
                <span class="filename">${filename}</span>
                <span class="percentage">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        `;
        
        container.appendChild(progressBar);
    }
    
    updateProgressBar(id, percentage) {
        const progressBar = document.getElementById(id);
        if (progressBar) {
            const fill = progressBar.querySelector('.progress-fill');
            const percentageSpan = progressBar.querySelector('.percentage');
            
            fill.style.width = percentage + '%';
            percentageSpan.textContent = percentage + '%';
        }
    }
    
    removeProgressBar(id) {
        const progressBar = document.getElementById(id);
        if (progressBar) {
            progressBar.remove();
        }
    }
    
    createProgressContainer() {
        const container = document.createElement('div');
        container.id = 'progress-container';
        container.className = 'progress-container';
        document.body.appendChild(container);
        return container;
    }
    
    // Real-time updates
    updateAnalysisProgress(sessionId, progress) {
        this.updateProgressBar(sessionId, progress.percentage);
        
        if (progress.status === 'completed') {
            setTimeout(() => {
                this.removeProgressBar(sessionId);
                this.showNotification('Analysis completed successfully', 'success');
            }, 2000);
        }
    }
    
    addNewCVToList(cvData) {
        const cvList = document.getElementById('cv-list');
        if (cvList) {
            const cvItem = this.createCVListItem(cvData);
            cvList.insertBefore(cvItem, cvList.firstChild);
            
            // Animate in
            setTimeout(() => {
                cvItem.classList.add('animate-in');
            }, 100);
        }
    }
    
    createCVListItem(cvData) {
        const item = document.createElement('div');
        item.className = 'cv-item';
        item.innerHTML = `
            <div class="cv-header">
                <h3>${cvData.filename}</h3>
                <span class="cv-status">${cvData.status}</span>
            </div>
            <div class="cv-details">
                <p>Uploaded: ${new Date(cvData.created_at).toLocaleDateString()}</p>
                <p>Size: ${this.formatFileSize(cvData.file_size)}</p>
            </div>
            <div class="cv-actions">
                <button onclick="cvAnalyzer.analyzeCV(${cvData.id})" class="btn btn-primary">Analyze</button>
                <button onclick="cvAnalyzer.downloadCV(${cvData.id})" class="btn btn-secondary">Download</button>
            </div>
        `;
        return item;
    }
    
    // Utility functions
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button onclick="this.parentElement.remove()">&times;</button>
        `;
        
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        
        container.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('visible');
        }, 100);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
    
    showAutoSaveIndicator() {
        let indicator = document.querySelector('.auto-save-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'auto-save-indicator';
            indicator.innerHTML = '✓ Auto-saved';
            document.body.appendChild(indicator);
        }
        
        indicator.classList.add('visible');
        setTimeout(() => {
            indicator.classList.remove('visible');
        }, 2000);
    }
    
    async analyzeCV(cvId) {
        try {
            const response = await fetch(`/api/v1/cvs/${cvId}/analyze/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            if (response.ok) {
                const result = await response.json();
                this.showNotification('Analysis started', 'info');
                
                if (result.session_id) {
                    this.trackAnalysisProgress(result.session_id);
                }
            }
        } catch (error) {
            this.showNotification('Failed to start analysis', 'error');
        }
    }
    
    trackAnalysisProgress(sessionId) {
        const interval = setInterval(async () => {
            try {
                const response = await fetch(`/api/v1/progress/${sessionId}/`);
                const progress = await response.json();
                
                this.updateAnalysisProgress(sessionId, progress);
                
                if (progress.status === 'completed' || progress.status === 'failed') {
                    clearInterval(interval);
                }
            } catch (error) {
                clearInterval(interval);
            }
        }, 1000);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.cvAnalyzer = new CVAnalyzer();
});
"""
    
    def get_chart_visualization_script(self):
        """Generate chart visualization JavaScript"""
        return """
// Chart Visualization System
class ChartManager {
    constructor() {
        this.charts = {};
        this.defaultColors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1'];
    }
    
    createAnalysisChart(containerId, data) {
        const ctx = document.getElementById(containerId).getContext('2d');
        
        this.charts[containerId] = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: data.categories,
                datasets: [{
                    label: 'CV Analysis Score',
                    data: data.scores,
                    backgroundColor: 'rgba(0, 123, 255, 0.2)',
                    borderColor: 'rgba(0, 123, 255, 1)',
                    pointBackgroundColor: 'rgba(0, 123, 255, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(0, 123, 255, 1)'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }
    
    createSkillsChart(containerId, skillsData) {
        const ctx = document.getElementById(containerId).getContext('2d');
        
        this.charts[containerId] = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: skillsData.map(skill => skill.name),
                datasets: [{
                    data: skillsData.map(skill => skill.score),
                    backgroundColor: this.defaultColors
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    createExperienceTimeline(containerId, experienceData) {
        // Create timeline visualization
        const container = document.getElementById(containerId);
        container.innerHTML = '';
        
        experienceData.forEach((exp, index) => {
            const item = document.createElement('div');
            item.className = 'timeline-item';
            item.innerHTML = `
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <h4>${exp.position}</h4>
                    <h5>${exp.company}</h5>
                    <p class="timeline-date">${exp.start_date} - ${exp.end_date || 'Present'}</p>
                    <p>${exp.description}</p>
                </div>
            `;
            container.appendChild(item);
        });
    }
    
    updateChart(chartId, newData) {
        const chart = this.charts[chartId];
        if (chart) {
            chart.data = newData;
            chart.update();
        }
    }
    
    destroyChart(chartId) {
        if (this.charts[chartId]) {
            this.charts[chartId].destroy();
            delete this.charts[chartId];
        }
    }
}

window.chartManager = new ChartManager();
"""
    
    def get_interactive_components_script(self):
        """Generate interactive components JavaScript"""
        return """
// Interactive Components System
class InteractiveComponents {
    constructor() {
        this.init();
    }
    
    init() {
        this.initializeModals();
        this.initializeTabs();
        this.initializeAccordions();
        this.initializeTooltips();
        this.initializeDataTables();
    }
    
    initializeModals() {
        document.querySelectorAll('[data-modal-trigger]').forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                const modalId = trigger.dataset.modalTrigger;
                this.openModal(modalId);
            });
        });
        
        document.querySelectorAll('.modal-close').forEach(closeBtn => {
            closeBtn.addEventListener('click', () => {
                this.closeAllModals();
            });
        });
        
        // Close modal on backdrop click
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-backdrop')) {
                this.closeAllModals();
            }
        });
    }
    
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.classList.add('modal-open');
            
            // Focus management
            const firstFocusable = modal.querySelector('button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
            if (firstFocusable) {
                setTimeout(() => firstFocusable.focus(), 100);
            }
        }
    }
    
    closeAllModals() {
        document.querySelectorAll('.modal.active').forEach(modal => {
            modal.classList.remove('active');
        });
        document.body.classList.remove('modal-open');
    }
    
    initializeTabs() {
        document.querySelectorAll('.tab-list').forEach(tabList => {
            const tabs = tabList.querySelectorAll('.tab');
            const panels = document.querySelectorAll('.tab-panel');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    // Remove active class from all tabs and panels
                    tabs.forEach(t => t.classList.remove('active'));
                    panels.forEach(p => p.classList.remove('active'));
                    
                    // Add active class to clicked tab
                    tab.classList.add('active');
                    
                    // Show corresponding panel
                    const targetPanel = document.getElementById(tab.dataset.tab);
                    if (targetPanel) {
                        targetPanel.classList.add('active');
                    }
                });
            });
        });
    }
    
    initializeAccordions() {
        document.querySelectorAll('.accordion-header').forEach(header => {
            header.addEventListener('click', () => {
                const accordion = header.closest('.accordion-item');
                const content = accordion.querySelector('.accordion-content');
                
                // Toggle current accordion
                accordion.classList.toggle('active');
                
                if (accordion.classList.contains('active')) {
                    content.style.maxHeight = content.scrollHeight + 'px';
                } else {
                    content.style.maxHeight = null;
                }
                
                // Close other accordions in the same group
                const group = header.closest('.accordion-group');
                if (group) {
                    group.querySelectorAll('.accordion-item').forEach(item => {
                        if (item !== accordion && item.classList.contains('active')) {
                            item.classList.remove('active');
                            item.querySelector('.accordion-content').style.maxHeight = null;
                        }
                    });
                }
            });
        });
    }
    
    initializeTooltips() {
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            let tooltip;
            
            element.addEventListener('mouseenter', () => {
                tooltip = document.createElement('div');
                tooltip.className = 'tooltip';
                tooltip.textContent = element.dataset.tooltip;
                document.body.appendChild(tooltip);
                
                const rect = element.getBoundingClientRect();
                tooltip.style.left = rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + 'px';
                tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
                
                setTimeout(() => tooltip.classList.add('visible'), 10);
            });
            
            element.addEventListener('mouseleave', () => {
                if (tooltip) {
                    tooltip.remove();
                }
            });
        });
    }
    
    initializeDataTables() {
        document.querySelectorAll('[data-sortable-table]').forEach(table => {
            this.makeSortableTable(table);
        });
    }
    
    makeSortableTable(table) {
        const headers = table.querySelectorAll('th[data-sortable]');
        
        headers.forEach((header, index) => {
            header.addEventListener('click', () => {
                this.sortTable(table, index, header.dataset.sortable);
            });
            
            header.style.cursor = 'pointer';
            header.innerHTML += ' <span class="sort-indicator">⇅</span>';
        });
    }
    
    sortTable(table, columnIndex, dataType) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        const sortedRows = rows.sort((a, b) => {
            const aValue = a.cells[columnIndex].textContent.trim();
            const bValue = b.cells[columnIndex].textContent.trim();
            
            if (dataType === 'number') {
                return parseFloat(aValue) - parseFloat(bValue);
            } else if (dataType === 'date') {
                return new Date(aValue) - new Date(bValue);
            } else {
                return aValue.localeCompare(bValue);
            }
        });
        
        // Clear and re-append sorted rows
        tbody.innerHTML = '';
        sortedRows.forEach(row => tbody.appendChild(row));
    }
}

// Initialize interactive components
document.addEventListener('DOMContentLoaded', () => {
    new InteractiveComponents();
});
"""

# Global instance
js_enhancer = JavaScriptEnhancer()

def get_core_javascript():
    """Get core JavaScript enhancement code"""
    return js_enhancer.get_core_javascript()

def get_chart_scripts():
    """Get chart visualization scripts"""
    return js_enhancer.get_chart_visualization_script()

def get_interactive_scripts():
    """Get interactive components scripts"""
    return js_enhancer.get_interactive_components_script() 