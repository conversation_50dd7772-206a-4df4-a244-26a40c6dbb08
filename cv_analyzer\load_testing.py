"""
Load Testing System for CV Analyzer
Comprehensive performance testing and load validation
"""

import asyncio
import aiohttp
import time
import json
import random
import logging
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from pathlib import Path
import statistics
import psutil
import requests
from django.conf import settings
from django.test.utils import override_settings
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import transaction, connections
from django.core.cache import cache
from threading import Lock, Event
import uuid

logger = logging.getLogger(__name__)

@dataclass
class LoadTestResult:
    """Data class for load test results"""
    test_name: str
    total_requests: int
    successful_requests: int
    failed_requests: int
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    p95_response_time: float
    p99_response_time: float
    requests_per_second: float
    error_rate: float
    total_duration: float
    concurrent_users: int
    errors: List[str]

class LoadTestFramework:
    """Main load testing framework"""
    
    def __init__(self):
        self.base_url = getattr(settings, 'LOAD_TEST_BASE_URL', 'http://localhost:8000')
        self.results = []
        self.performance_targets = {
            'max_response_time_ms': 2000,
            'max_error_rate_percent': 5.0,
            'min_requests_per_second': 10,
            'max_cpu_usage_percent': 80,
            'max_memory_usage_mb': 1000,
            'target_concurrent_users': 1000
        }
        self.test_data_lock = Lock()
        self.system_monitor = SystemResourceMonitor()

    async def run_comprehensive_load_tests(self) -> Dict[str, Any]:
        """Run comprehensive load testing suite"""
        logger.info("Starting comprehensive load tests...")
        
        # Start system monitoring
        self.system_monitor.start_monitoring()
        
        try:
            # Run different test scenarios
            results = {
                'page_load_test': await self.run_page_load_test(),
                'api_endpoint_test': await self.run_api_endpoint_test(),
                'file_upload_test': await self.run_file_upload_test(),
                'database_load_test': await self.run_database_load_test(),
                'concurrent_user_test': await self.run_concurrent_user_test(),
                'stress_test': await self.run_stress_test(),
                'spike_test': await self.run_spike_test(),
                'endurance_test': await self.run_endurance_test(),
                'system_metrics': self.system_monitor.get_metrics()
            }
            
            # Generate comprehensive report
            results['summary'] = self._generate_load_test_summary(results)
            
            return results
            
        finally:
            self.system_monitor.stop_monitoring()

    async def run_page_load_test(self, concurrent_users: int = 50, duration: int = 60) -> LoadTestResult:
        """Test page load performance under load"""
        logger.info(f"Running page load test: {concurrent_users} users for {duration}s")
        
        pages_to_test = [
            '/',
            '/dashboard/',
            '/upload/',
            '/companies/',
            '/vacancies/'
        ]
        
        async def load_page(session, user_id):
            """Load a random page"""
            page = random.choice(pages_to_test)
            start_time = time.time()
            
            try:
                async with session.get(f"{self.base_url}{page}") as response:
                    await response.text()
                    end_time = time.time()
                    return {
                        'success': response.status == 200,
                        'response_time': end_time - start_time,
                        'status_code': response.status,
                        'error': None
                    }
            except Exception as e:
                end_time = time.time()
                return {
                    'success': False,
                    'response_time': end_time - start_time,
                    'status_code': 0,
                    'error': str(e)
                }

        return await self._run_load_test(
            test_name="Page Load Test",
            test_function=load_page,
            concurrent_users=concurrent_users,
            duration=duration
        )

    async def run_api_endpoint_test(self, concurrent_users: int = 100, duration: int = 120) -> LoadTestResult:
        """Test API endpoint performance under load"""
        logger.info(f"Running API endpoint test: {concurrent_users} users for {duration}s")
        
        api_endpoints = [
            '/api/health/',
            '/api/companies/',
            '/api/vacancies/',
            '/api/cv-analysis/',
            '/api/metrics/'
        ]
        
        async def call_api(session, user_id):
            """Call a random API endpoint"""
            endpoint = random.choice(api_endpoints)
            start_time = time.time()
            
            try:
                headers = {'Content-Type': 'application/json'}
                async with session.get(f"{self.base_url}{endpoint}", headers=headers) as response:
                    await response.json()
                    end_time = time.time()
                    return {
                        'success': response.status in [200, 201],
                        'response_time': end_time - start_time,
                        'status_code': response.status,
                        'error': None
                    }
            except Exception as e:
                end_time = time.time()
                return {
                    'success': False,
                    'response_time': end_time - start_time,
                    'status_code': 0,
                    'error': str(e)
                }

        return await self._run_load_test(
            test_name="API Endpoint Test",
            test_function=call_api,
            concurrent_users=concurrent_users,
            duration=duration
        )

    async def run_file_upload_test(self, concurrent_users: int = 20, duration: int = 180) -> LoadTestResult:
        """Test file upload performance under load"""
        logger.info(f"Running file upload test: {concurrent_users} users for {duration}s")
        
        # Create test file
        test_file_content = b'%PDF-1.4\n' + b'x' * (100 * 1024)  # 100KB test file
        
        async def upload_file(session, user_id):
            """Upload a test file"""
            start_time = time.time()
            
            try:
                # Create form data
                data = aiohttp.FormData()
                data.add_field('cv_file', test_file_content, 
                             filename=f'test_cv_{user_id}.pdf',
                             content_type='application/pdf')
                data.add_field('vacancy', '1')
                
                async with session.post(f"{self.base_url}/upload/", data=data) as response:
                    await response.text()
                    end_time = time.time()
                    return {
                        'success': response.status in [200, 201, 302],
                        'response_time': end_time - start_time,
                        'status_code': response.status,
                        'error': None
                    }
            except Exception as e:
                end_time = time.time()
                return {
                    'success': False,
                    'response_time': end_time - start_time,
                    'status_code': 0,
                    'error': str(e)
                }

        return await self._run_load_test(
            test_name="File Upload Test",
            test_function=upload_file,
            concurrent_users=concurrent_users,
            duration=duration
        )

    async def run_database_load_test(self, concurrent_users: int = 50, duration: int = 120) -> LoadTestResult:
        """Test database performance under load"""
        logger.info(f"Running database load test: {concurrent_users} users for {duration}s")
        
        async def database_operation(session, user_id):
            """Perform database operations"""
            start_time = time.time()
            
            try:
                # Simulate various database operations via API
                operations = [
                    ('GET', '/api/companies/'),
                    ('GET', '/api/vacancies/'),
                    ('GET', f'/api/companies/?search=test'),
                    ('GET', '/api/analytics/dashboard/')
                ]
                
                method, endpoint = random.choice(operations)
                
                if method == 'GET':
                    async with session.get(f"{self.base_url}{endpoint}") as response:
                        await response.json()
                        end_time = time.time()
                        return {
                            'success': response.status == 200,
                            'response_time': end_time - start_time,
                            'status_code': response.status,
                            'error': None
                        }
                        
            except Exception as e:
                end_time = time.time()
                return {
                    'success': False,
                    'response_time': end_time - start_time,
                    'status_code': 0,
                    'error': str(e)
                }

        return await self._run_load_test(
            test_name="Database Load Test",
            test_function=database_operation,
            concurrent_users=concurrent_users,
            duration=duration
        )

    async def run_concurrent_user_test(self, max_users: int = 1000, ramp_up_time: int = 300) -> LoadTestResult:
        """Test system behavior with increasing concurrent users"""
        logger.info(f"Running concurrent user test: ramping up to {max_users} users over {ramp_up_time}s")
        
        results = []
        step_size = 50
        step_duration = 30
        
        for users in range(step_size, max_users + 1, step_size):
            logger.info(f"Testing with {users} concurrent users")
            
            result = await self.run_page_load_test(
                concurrent_users=users,
                duration=step_duration
            )
            
            results.append({
                'concurrent_users': users,
                'avg_response_time': result.avg_response_time,
                'error_rate': result.error_rate,
                'requests_per_second': result.requests_per_second
            })
            
            # Check if system is degrading
            if result.error_rate > 10.0 or result.avg_response_time > 5.0:
                logger.warning(f"System degradation detected at {users} users")
                break
        
        # Return summary result
        total_requests = sum(r.total_requests for r in [result])
        successful_requests = sum(r.successful_requests for r in [result])
        
        return LoadTestResult(
            test_name="Concurrent User Test",
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=total_requests - successful_requests,
            avg_response_time=statistics.mean([r['avg_response_time'] for r in results]),
            min_response_time=min([r['avg_response_time'] for r in results]),
            max_response_time=max([r['avg_response_time'] for r in results]),
            p95_response_time=0.0,  # Simplified for summary
            p99_response_time=0.0,
            requests_per_second=statistics.mean([r['requests_per_second'] for r in results]),
            error_rate=statistics.mean([r['error_rate'] for r in results]),
            total_duration=ramp_up_time,
            concurrent_users=max_users,
            errors=[]
        )

    async def run_stress_test(self, concurrent_users: int = 200, duration: int = 600) -> LoadTestResult:
        """Run stress test to find system breaking point"""
        logger.info(f"Running stress test: {concurrent_users} users for {duration}s")
        
        async def stress_operation(session, user_id):
            """Perform mixed operations under stress"""
            operations = [
                ('page_load', '/'),
                ('api_call', '/api/health/'),
                ('search', '/api/companies/?search=test'),
                ('upload_simulation', '/upload/')
            ]
            
            operation_type, endpoint = random.choice(operations)
            start_time = time.time()
            
            try:
                if operation_type in ['page_load', 'api_call', 'search']:
                    async with session.get(f"{self.base_url}{endpoint}") as response:
                        await response.text()
                        end_time = time.time()
                        return {
                            'success': response.status in [200, 302],
                            'response_time': end_time - start_time,
                            'status_code': response.status,
                            'error': None
                        }
                else:
                    # Simulate upload without actually uploading
                    await asyncio.sleep(random.uniform(0.1, 0.5))
                    end_time = time.time()
                    return {
                        'success': True,
                        'response_time': end_time - start_time,
                        'status_code': 200,
                        'error': None
                    }
                    
            except Exception as e:
                end_time = time.time()
                return {
                    'success': False,
                    'response_time': end_time - start_time,
                    'status_code': 0,
                    'error': str(e)
                }

        return await self._run_load_test(
            test_name="Stress Test",
            test_function=stress_operation,
            concurrent_users=concurrent_users,
            duration=duration
        )

    async def run_spike_test(self, base_users: int = 20, spike_users: int = 200, spike_duration: int = 60) -> LoadTestResult:
        """Test system behavior during traffic spikes"""
        logger.info(f"Running spike test: {base_users} -> {spike_users} users for {spike_duration}s")
        
        # Start with base load
        base_task = asyncio.create_task(
            self.run_page_load_test(concurrent_users=base_users, duration=300)
        )
        
        # Wait for base load to establish
        await asyncio.sleep(30)
        
        # Apply spike
        spike_result = await self.run_page_load_test(
            concurrent_users=spike_users,
            duration=spike_duration
        )
        
        # Wait for base task to complete
        base_result = await base_task
        
        return spike_result

    async def run_endurance_test(self, concurrent_users: int = 50, duration: int = 3600) -> LoadTestResult:
        """Run endurance test for extended period"""
        logger.info(f"Running endurance test: {concurrent_users} users for {duration}s")
        
        return await self.run_page_load_test(
            concurrent_users=concurrent_users,
            duration=duration
        )

    async def _run_load_test(self, test_name: str, test_function, concurrent_users: int, duration: int) -> LoadTestResult:
        """Generic load test runner"""
        start_time = time.time()
        results = []
        errors = []
        
        # Create semaphore to limit concurrent connections
        semaphore = asyncio.Semaphore(concurrent_users)
        
        async def worker(session, user_id):
            """Worker function for each simulated user"""
            async with semaphore:
                end_time = start_time + duration
                user_results = []
                
                while time.time() < end_time:
                    result = await test_function(session, user_id)
                    user_results.append(result)
                    
                    if not result['success'] and result['error']:
                        with self.test_data_lock:
                            errors.append(result['error'])
                    
                    # Small delay between requests
                    await asyncio.sleep(random.uniform(0.1, 1.0))
                
                with self.test_data_lock:
                    results.extend(user_results)

        # Create aiohttp session with appropriate settings
        connector = aiohttp.TCPConnector(
            limit=concurrent_users * 2,
            limit_per_host=concurrent_users,
            ttl_dns_cache=300,
            use_dns_cache=True
        )
        
        timeout = aiohttp.ClientTimeout(total=30)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        ) as session:
            # Create tasks for all users
            tasks = [
                asyncio.create_task(worker(session, user_id))
                for user_id in range(concurrent_users)
            ]
            
            # Wait for all tasks to complete
            await asyncio.gather(*tasks, return_exceptions=True)
        
        # Calculate statistics
        total_duration = time.time() - start_time
        total_requests = len(results)
        successful_requests = sum(1 for r in results if r['success'])
        failed_requests = total_requests - successful_requests
        
        if total_requests > 0:
            response_times = [r['response_time'] for r in results]
            avg_response_time = statistics.mean(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            
            # Calculate percentiles
            sorted_times = sorted(response_times)
            p95_index = int(0.95 * len(sorted_times))
            p99_index = int(0.99 * len(sorted_times))
            p95_response_time = sorted_times[p95_index] if p95_index < len(sorted_times) else max_response_time
            p99_response_time = sorted_times[p99_index] if p99_index < len(sorted_times) else max_response_time
            
            requests_per_second = total_requests / total_duration
            error_rate = (failed_requests / total_requests) * 100
        else:
            avg_response_time = min_response_time = max_response_time = 0.0
            p95_response_time = p99_response_time = 0.0
            requests_per_second = error_rate = 0.0

        return LoadTestResult(
            test_name=test_name,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            requests_per_second=requests_per_second,
            error_rate=error_rate,
            total_duration=total_duration,
            concurrent_users=concurrent_users,
            errors=errors[:10]  # Keep only first 10 errors
        )

    def _generate_load_test_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive load test summary"""
        test_results = [v for k, v in results.items() if isinstance(v, LoadTestResult)]
        
        if not test_results:
            return {'status': 'no_tests_run'}
        
        # Calculate overall metrics
        total_requests = sum(r.total_requests for r in test_results)
        total_successful = sum(r.successful_requests for r in test_results)
        total_failed = sum(r.failed_requests for r in test_results)
        
        avg_response_times = [r.avg_response_time for r in test_results]
        max_response_times = [r.max_response_time for r in test_results]
        error_rates = [r.error_rate for r in test_results]
        rps_values = [r.requests_per_second for r in test_results]
        
        overall_avg_response_time = statistics.mean(avg_response_times)
        overall_max_response_time = max(max_response_times)
        overall_error_rate = statistics.mean(error_rates)
        overall_rps = statistics.mean(rps_values)
        
        # Check against performance targets
        targets_met = {
            'response_time': overall_avg_response_time * 1000 <= self.performance_targets['max_response_time_ms'],
            'error_rate': overall_error_rate <= self.performance_targets['max_error_rate_percent'],
            'requests_per_second': overall_rps >= self.performance_targets['min_requests_per_second']
        }
        
        # System resource checks
        system_metrics = results.get('system_metrics', {})
        if system_metrics:
            targets_met['cpu_usage'] = system_metrics.get('avg_cpu_percent', 0) <= self.performance_targets['max_cpu_usage_percent']
            targets_met['memory_usage'] = system_metrics.get('avg_memory_mb', 0) <= self.performance_targets['max_memory_usage_mb']
        
        overall_status = 'passed' if all(targets_met.values()) else 'failed'
        
        return {
            'overall_status': overall_status,
            'total_requests': total_requests,
            'successful_requests': total_successful,
            'failed_requests': total_failed,
            'overall_success_rate': (total_successful / total_requests * 100) if total_requests > 0 else 0,
            'avg_response_time_ms': overall_avg_response_time * 1000,
            'max_response_time_ms': overall_max_response_time * 1000,
            'avg_error_rate_percent': overall_error_rate,
            'avg_requests_per_second': overall_rps,
            'performance_targets_met': targets_met,
            'system_metrics': system_metrics,
            'recommendations': self._generate_recommendations(results, targets_met)
        }

    def _generate_recommendations(self, results: Dict[str, Any], targets_met: Dict[str, bool]) -> List[str]:
        """Generate performance improvement recommendations"""
        recommendations = []
        
        if not targets_met.get('response_time', True):
            recommendations.append("Response times exceed target. Consider optimizing database queries and adding caching.")
        
        if not targets_met.get('error_rate', True):
            recommendations.append("Error rate is too high. Review error logs and improve error handling.")
        
        if not targets_met.get('requests_per_second', True):
            recommendations.append("Request throughput is below target. Consider horizontal scaling or performance optimization.")
        
        if not targets_met.get('cpu_usage', True):
            recommendations.append("CPU usage is high. Consider optimizing algorithms or adding more CPU resources.")
        
        if not targets_met.get('memory_usage', True):
            recommendations.append("Memory usage is high. Review memory leaks and optimize data structures.")
        
        # Additional recommendations based on specific test results
        test_results = [v for k, v in results.items() if isinstance(v, LoadTestResult)]
        
        for result in test_results:
            if result.error_rate > 20:
                recommendations.append(f"{result.test_name} has high error rate ({result.error_rate:.1f}%). Investigate immediately.")
            
            if result.p99_response_time > 10.0:
                recommendations.append(f"{result.test_name} has high P99 response time. Optimize slow operations.")
        
        if not recommendations:
            recommendations.append("All performance targets met. System is performing well under load.")
        
        return recommendations


class SystemResourceMonitor:
    """Monitor system resources during load testing"""
    
    def __init__(self):
        self.monitoring = False
        self.metrics = []
        self.monitor_interval = 5  # seconds

    def start_monitoring(self):
        """Start monitoring system resources"""
        self.monitoring = True
        self.metrics = []
        asyncio.create_task(self._monitor_resources())

    def stop_monitoring(self):
        """Stop monitoring system resources"""
        self.monitoring = False

    async def _monitor_resources(self):
        """Monitor system resources in background"""
        while self.monitoring:
            try:
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=1)
                
                # Memory usage
                memory = psutil.virtual_memory()
                memory_mb = memory.used / 1024 / 1024
                memory_percent = memory.percent
                
                # Disk usage
                disk = psutil.disk_usage('/')
                disk_percent = disk.percent
                
                # Network I/O
                network = psutil.net_io_counters()
                
                # Database connections (if available)
                db_connections = self._get_db_connections()
                
                metric = {
                    'timestamp': time.time(),
                    'cpu_percent': cpu_percent,
                    'memory_mb': memory_mb,
                    'memory_percent': memory_percent,
                    'disk_percent': disk_percent,
                    'network_bytes_sent': network.bytes_sent,
                    'network_bytes_recv': network.bytes_recv,
                    'db_connections': db_connections
                }
                
                self.metrics.append(metric)
                
                await asyncio.sleep(self.monitor_interval)
                
            except Exception as e:
                logger.error(f"Error monitoring resources: {e}")
                await asyncio.sleep(self.monitor_interval)

    def _get_db_connections(self) -> int:
        """Get current database connections"""
        try:
            from django.db import connections
            total_connections = 0
            for conn in connections.all():
                if hasattr(conn, 'queries_log'):
                    total_connections += len(conn.queries_log)
            return total_connections
        except:
            return 0

    def get_metrics(self) -> Dict[str, Any]:
        """Get aggregated system metrics"""
        if not self.metrics:
            return {}
        
        cpu_values = [m['cpu_percent'] for m in self.metrics]
        memory_values = [m['memory_mb'] for m in self.metrics]
        
        return {
            'avg_cpu_percent': statistics.mean(cpu_values),
            'max_cpu_percent': max(cpu_values),
            'avg_memory_mb': statistics.mean(memory_values),
            'max_memory_mb': max(memory_values),
            'total_metrics_collected': len(self.metrics),
            'monitoring_duration': (self.metrics[-1]['timestamp'] - self.metrics[0]['timestamp']) if len(self.metrics) > 1 else 0
        }


class LoadTestReportGenerator:
    """Generate load test reports"""
    
    def __init__(self, results: Dict[str, Any]):
        self.results = results

    def generate_html_report(self) -> str:
        """Generate HTML load test report"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>CV Analyzer - Load Test Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .summary { background: #f0f0f0; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
                .test-result { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
                .passed { border-color: green; }
                .failed { border-color: red; }
                .metric { display: inline-block; margin: 5px 10px 5px 0; }
                .metric-value { font-weight: bold; }
                table { border-collapse: collapse; width: 100%; margin: 10px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .recommendations { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }
                .recommendations ul { margin: 10px 0; }
            </style>
        </head>
        <body>
            <h1>CV Analyzer - Load Test Report</h1>
            
            <div class="summary">
                <h2>Summary</h2>
                <div class="metric">Status: <span class="metric-value">{overall_status}</span></div>
                <div class="metric">Total Requests: <span class="metric-value">{total_requests:,}</span></div>
                <div class="metric">Success Rate: <span class="metric-value">{success_rate:.1f}%</span></div>
                <div class="metric">Avg Response Time: <span class="metric-value">{avg_response_time:.0f}ms</span></div>
                <div class="metric">Avg RPS: <span class="metric-value">{avg_rps:.1f}</span></div>
            </div>
            
            {test_results_html}
            
            {system_metrics_html}
            
            {recommendations_html}
        </body>
        </html>
        """
        
        summary = self.results.get('summary', {})
        
        # Generate test results HTML
        test_results_html = self._generate_test_results_html()
        
        # Generate system metrics HTML
        system_metrics_html = self._generate_system_metrics_html()
        
        # Generate recommendations HTML
        recommendations_html = self._generate_recommendations_html()
        
        return html_template.format(
            overall_status=summary.get('overall_status', 'unknown'),
            total_requests=summary.get('total_requests', 0),
            success_rate=summary.get('overall_success_rate', 0),
            avg_response_time=summary.get('avg_response_time_ms', 0),
            avg_rps=summary.get('avg_requests_per_second', 0),
            test_results_html=test_results_html,
            system_metrics_html=system_metrics_html,
            recommendations_html=recommendations_html
        )

    def _generate_test_results_html(self) -> str:
        """Generate HTML for individual test results"""
        html_parts = ["<h2>Test Results</h2>"]
        
        for test_name, result in self.results.items():
            if isinstance(result, LoadTestResult):
                status_class = 'passed' if result.error_rate < 5.0 else 'failed'
                
                html_parts.append(f'''
                <div class="test-result {status_class}">
                    <h3>{result.test_name}</h3>
                    <div class="metric">Requests: <span class="metric-value">{result.total_requests:,}</span></div>
                    <div class="metric">Success Rate: <span class="metric-value">{100-result.error_rate:.1f}%</span></div>
                    <div class="metric">Avg Response: <span class="metric-value">{result.avg_response_time*1000:.0f}ms</span></div>
                    <div class="metric">P95 Response: <span class="metric-value">{result.p95_response_time*1000:.0f}ms</span></div>
                    <div class="metric">RPS: <span class="metric-value">{result.requests_per_second:.1f}</span></div>
                    <div class="metric">Users: <span class="metric-value">{result.concurrent_users}</span></div>
                </div>
                ''')
        
        return '\n'.join(html_parts)

    def _generate_system_metrics_html(self) -> str:
        """Generate HTML for system metrics"""
        system_metrics = self.results.get('system_metrics', {})
        
        if not system_metrics:
            return ""
        
        return f'''
        <h2>System Metrics</h2>
        <div class="test-result">
            <div class="metric">Avg CPU: <span class="metric-value">{system_metrics.get('avg_cpu_percent', 0):.1f}%</span></div>
            <div class="metric">Max CPU: <span class="metric-value">{system_metrics.get('max_cpu_percent', 0):.1f}%</span></div>
            <div class="metric">Avg Memory: <span class="metric-value">{system_metrics.get('avg_memory_mb', 0):.0f}MB</span></div>
            <div class="metric">Max Memory: <span class="metric-value">{system_metrics.get('max_memory_mb', 0):.0f}MB</span></div>
        </div>
        '''

    def _generate_recommendations_html(self) -> str:
        """Generate HTML for recommendations"""
        recommendations = self.results.get('summary', {}).get('recommendations', [])
        
        if not recommendations:
            return ""
        
        rec_items = '\n'.join([f'<li>{rec}</li>' for rec in recommendations])
        
        return f'''
        <div class="recommendations">
            <h2>Recommendations</h2>
            <ul>
                {rec_items}
            </ul>
        </div>
        '''

    def save_report(self, filepath: str):
        """Save HTML report to file"""
        html_content = self.generate_html_report()
        with open(filepath, 'w') as f:
            f.write(html_content)
        logger.info(f"Load test report saved to {filepath}")


# Export classes
__all__ = [
    'LoadTestFramework',
    'LoadTestResult',
    'SystemResourceMonitor',
    'LoadTestReportGenerator'
] 