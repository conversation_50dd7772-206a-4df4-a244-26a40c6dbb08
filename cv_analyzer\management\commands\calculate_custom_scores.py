"""
Django management command to calculate custom scores for CVs against vacancies
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from cv_analyzer.models import CVAnalysis, Vacancy, VacancyScoringRules, CustomScoringResult
from cv_analyzer.custom_scoring import CustomScoringEngine, calculate_custom_scores_for_vacancy, recalculate_all_custom_scores
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Calculate custom weighted scores for CVs against vacancies'

    def add_arguments(self, parser):
        parser.add_argument(
            '--vacancy-id',
            type=int,
            help='Calculate scores for specific vacancy ID',
        )
        parser.add_argument(
            '--cv-id',
            type=int,
            help='Calculate scores for specific CV ID against all vacancies',
        )
        parser.add_argument(
            '--recalculate-all',
            action='store_true',
            help='Recalculate all existing custom scores',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be calculated without making changes',
        )

    def handle(self, *args, **options):
        try:
            self.stdout.write("Starting custom scoring calculation...")
            
            if options['recalculate_all']:
                self.recalculate_all_scores(options['dry_run'])
            elif options['vacancy_id']:
                self.calculate_scores_for_vacancy(options['vacancy_id'], options['dry_run'])
            elif options['cv_id']:
                self.calculate_scores_for_cv(options['cv_id'], options['dry_run'])
            else:
                self.calculate_missing_scores(options['dry_run'])
                
        except Exception as e:
            raise CommandError(f'Error during custom scoring: {str(e)}')

    def recalculate_all_scores(self, dry_run=False):
        """Recalculate all custom scores"""
        self.stdout.write("Recalculating all custom scores...")
        
        if dry_run:
            # Count what would be processed
            vacancies_with_rules = Vacancy.objects.filter(scoring_rules__isnull=False).count()
            cv_analyses = CVAnalysis.objects.count()
            
            self.stdout.write(
                f"DRY RUN: Would process {cv_analyses} CV analyses against "
                f"{vacancies_with_rules} vacancies with scoring rules"
            )
            return
        
        with transaction.atomic():
            results = recalculate_all_custom_scores()
            
        self.stdout.write(
            self.style.SUCCESS(f'Successfully recalculated {len(results)} custom scores')
        )

    def calculate_scores_for_vacancy(self, vacancy_id, dry_run=False):
        """Calculate scores for all CVs against a specific vacancy"""
        try:
            vacancy = Vacancy.objects.get(id=vacancy_id)
        except Vacancy.DoesNotExist:
            raise CommandError(f'Vacancy with ID {vacancy_id} does not exist')
        
        self.stdout.write(f"Calculating scores for vacancy: {vacancy.title}")
        
        # Ensure scoring rules exist
        scoring_rules, created = VacancyScoringRules.objects.get_or_create(
            vacancy=vacancy
        )
        if created:
            self.stdout.write(f"Created default scoring rules for vacancy {vacancy_id}")
        
        cv_analyses = CVAnalysis.objects.select_related('cv').all()
        
        if dry_run:
            self.stdout.write(
                f"DRY RUN: Would calculate scores for {cv_analyses.count()} CV analyses "
                f"against vacancy '{vacancy.title}'"
            )
            return
        
        with transaction.atomic():
            results = calculate_custom_scores_for_vacancy(vacancy)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully calculated {len(results)} custom scores for vacancy {vacancy_id}'
            )
        )

    def calculate_scores_for_cv(self, cv_id, dry_run=False):
        """Calculate scores for a specific CV against all vacancies with scoring rules"""
        try:
            cv_analysis = CVAnalysis.objects.select_related('cv').get(cv__id=cv_id)
        except CVAnalysis.DoesNotExist:
            raise CommandError(f'CV analysis for CV ID {cv_id} does not exist')
        
        self.stdout.write(f"Calculating scores for CV: {cv_analysis.cv.file.name}")
        
        vacancies_with_rules = Vacancy.objects.filter(scoring_rules__isnull=False)
        
        if dry_run:
            self.stdout.write(
                f"DRY RUN: Would calculate scores for CV {cv_id} against "
                f"{vacancies_with_rules.count()} vacancies with scoring rules"
            )
            return
        
        engine = CustomScoringEngine()
        results = []
        
        with transaction.atomic():
            for vacancy in vacancies_with_rules:
                try:
                    result = engine.calculate_custom_score(cv_analysis, vacancy)
                    results.append(result)
                    self.stdout.write(f"  ✓ Calculated score for vacancy: {vacancy.title}")
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f"  ✗ Failed for vacancy {vacancy.title}: {str(e)}")
                    )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully calculated {len(results)} custom scores for CV {cv_id}'
            )
        )

    def calculate_missing_scores(self, dry_run=False):
        """Calculate custom scores where they're missing"""
        self.stdout.write("Calculating missing custom scores...")
        
        # Find CV analyses that don't have custom scoring results for vacancies with scoring rules
        vacancies_with_rules = Vacancy.objects.filter(scoring_rules__isnull=False)
        cv_analyses = CVAnalysis.objects.all()
        
        missing_combinations = []
        for cv_analysis in cv_analyses:
            for vacancy in vacancies_with_rules:
                if not CustomScoringResult.objects.filter(
                    cv_analysis=cv_analysis, 
                    vacancy=vacancy
                ).exists():
                    missing_combinations.append((cv_analysis, vacancy))
        
        if dry_run:
            self.stdout.write(
                f"DRY RUN: Would calculate {len(missing_combinations)} missing custom scores"
            )
            return
        
        if not missing_combinations:
            self.stdout.write("No missing custom scores found.")
            return
        
        engine = CustomScoringEngine()
        results = []
        
        with transaction.atomic():
            for cv_analysis, vacancy in missing_combinations:
                try:
                    result = engine.calculate_custom_score(cv_analysis, vacancy)
                    results.append(result)
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(
                            f"Failed to calculate score for CV {cv_analysis.cv.id} "
                            f"against vacancy {vacancy.id}: {str(e)}"
                        )
                    )
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully calculated {len(results)} missing custom scores')
        ) 