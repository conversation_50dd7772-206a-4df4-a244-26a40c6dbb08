from django.core.management.base import BaseCommand
from django.db import transaction
from cv_analyzer.models import (
    Company, Vacancy, CV, CVAnalysis, CompanyAnalysis, VacancyAnalysis,
    WelcomeContent, AIConfig, AIAPIConfig, AnalysisProcess, ComparisonAnalysis,
    ApplicantProfile, UnifiedAnalysis, VacancyScoringRules, ScoringTemplate,
    CustomScoringResult
)
from django.contrib.auth.models import User
import os
from django.conf import settings


class Command(BaseCommand):
    help = 'Remove all default data from the HR system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm the deletion of all default data'
        )
        parser.add_argument(
            '--keep-users',
            action='store_true',
            help='Keep user accounts but remove all HR data'
        )
        parser.add_argument(
            '--keep-welcome',
            action='store_true',
            help='Keep welcome content configuration'
        )
        parser.add_argument(
            '--keep-ai-config',
            action='store_true',
            help='Keep AI configuration settings'
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    '⚠️  This will permanently delete ALL default data!\n'
                    'This includes:\n'
                    '• All companies and vacancies\n'
                    '• All CV files and analyses\n'
                    '• All applicant profiles\n'
                    '• All scoring rules and templates\n'
                    '• Welcome content (unless --keep-welcome)\n'
                    '• AI configurations (unless --keep-ai-config)\n'
                    '• User accounts (unless --keep-users)\n\n'
                    'Use --confirm to proceed with deletion.\n'
                    'Use --keep-users to preserve user accounts.\n'
                    'Use --keep-welcome to preserve welcome content.\n'
                    'Use --keep-ai-config to preserve AI settings.\n'
                )
            )
            return

        self.stdout.write('🧹 Starting cleanup of default data...')
        
        try:
            with transaction.atomic():
                deleted_counts = self.cleanup_data(options)
                self.cleanup_files()
                
            self.stdout.write(
                self.style.SUCCESS(
                    f'\n✅ Cleanup completed successfully!\n'
                    f'Deleted items summary:\n'
                    f'• Companies: {deleted_counts["companies"]}\n'
                    f'• Vacancies: {deleted_counts["vacancies"]}\n'
                    f'• CVs: {deleted_counts["cvs"]}\n'
                    f'• CV Analyses: {deleted_counts["cv_analyses"]}\n'
                    f'• Applicant Profiles: {deleted_counts["applicant_profiles"]}\n'
                    f'• Scoring Rules: {deleted_counts["scoring_rules"]}\n'
                    f'• Scoring Templates: {deleted_counts["scoring_templates"]}\n'
                    f'• Custom Scoring Results: {deleted_counts["custom_results"]}\n'
                    f'• Comparison Analyses: {deleted_counts["comparison_analyses"]}\n'
                    f'• Analysis Processes: {deleted_counts["analysis_processes"]}\n'
                    f'• Unified Analyses: {deleted_counts["unified_analyses"]}\n'
                    f'• Company Analyses: {deleted_counts["company_analyses"]}\n'
                    f'• Vacancy Analyses: {deleted_counts["vacancy_analyses"]}\n'
                    f'• Users: {deleted_counts["users"]}\n'
                    f'• Welcome Content: {deleted_counts["welcome_content"]}\n'
                    f'• AI Configs: {deleted_counts["ai_configs"]}\n'
                    f'• AI API Configs: {deleted_counts["ai_api_configs"]}\n'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error during cleanup: {str(e)}')
            )
            raise

    def cleanup_data(self, options):
        """Remove all data from the database"""
        deleted_counts = {}
        
        # Delete in order to respect foreign key constraints
        
        # 1. Delete scoring results and rules
        self.stdout.write('🗑️  Deleting custom scoring results...')
        deleted_counts["custom_results"] = CustomScoringResult.objects.all().count()
        CustomScoringResult.objects.all().delete()
        
        self.stdout.write('🗑️  Deleting scoring rules...')
        deleted_counts["scoring_rules"] = VacancyScoringRules.objects.all().count()
        VacancyScoringRules.objects.all().delete()
        
        self.stdout.write('🗑️  Deleting scoring templates...')
        deleted_counts["scoring_templates"] = ScoringTemplate.objects.all().count()
        ScoringTemplate.objects.all().delete()
        
        # 2. Delete analysis data
        self.stdout.write('🗑️  Deleting comparison analyses...')
        deleted_counts["comparison_analyses"] = ComparisonAnalysis.objects.all().count()
        ComparisonAnalysis.objects.all().delete()
        
        self.stdout.write('🗑️  Deleting unified analyses...')
        deleted_counts["unified_analyses"] = UnifiedAnalysis.objects.all().count()
        UnifiedAnalysis.objects.all().delete()
        
        self.stdout.write('🗑️  Deleting analysis processes...')
        deleted_counts["analysis_processes"] = AnalysisProcess.objects.all().count()
        AnalysisProcess.objects.all().delete()
        
        self.stdout.write('🗑️  Deleting CV analyses...')
        deleted_counts["cv_analyses"] = CVAnalysis.objects.all().count()
        CVAnalysis.objects.all().delete()
        
        self.stdout.write('🗑️  Deleting company analyses...')
        deleted_counts["company_analyses"] = CompanyAnalysis.objects.all().count()
        CompanyAnalysis.objects.all().delete()
        
        self.stdout.write('🗑️  Deleting vacancy analyses...')
        deleted_counts["vacancy_analyses"] = VacancyAnalysis.objects.all().count()
        VacancyAnalysis.objects.all().delete()
        
        # 3. Delete CVs
        self.stdout.write('🗑️  Deleting CVs...')
        deleted_counts["cvs"] = CV.objects.all().count()
        CV.objects.all().delete()
        
        # 4. Delete vacancies
        self.stdout.write('🗑️  Deleting vacancies...')
        deleted_counts["vacancies"] = Vacancy.objects.all().count()
        Vacancy.objects.all().delete()
        
        # 5. Delete companies
        self.stdout.write('🗑️  Deleting companies...')
        deleted_counts["companies"] = Company.objects.all().count()
        Company.objects.all().delete()
        
        # 6. Delete applicant profiles
        self.stdout.write('🗑️  Deleting applicant profiles...')
        deleted_counts["applicant_profiles"] = ApplicantProfile.objects.all().count()
        ApplicantProfile.objects.all().delete()
        
        # 7. Delete users (if not keeping them)
        if not options['keep_users']:
            self.stdout.write('🗑️  Deleting user accounts...')
            deleted_counts["users"] = User.objects.filter(is_superuser=False).count()
            User.objects.filter(is_superuser=False).delete()
        else:
            deleted_counts["users"] = 0
            self.stdout.write('✅ Keeping user accounts')
        
        # 8. Delete welcome content (if not keeping it)
        if not options['keep_welcome']:
            self.stdout.write('🗑️  Deleting welcome content...')
            deleted_counts["welcome_content"] = WelcomeContent.objects.all().count()
            WelcomeContent.objects.all().delete()
        else:
            deleted_counts["welcome_content"] = 0
            self.stdout.write('✅ Keeping welcome content')
        
        # 9. Delete AI configurations (if not keeping them)
        if not options['keep_ai_config']:
            self.stdout.write('🗑️  Deleting AI configurations...')
            deleted_counts["ai_configs"] = AIConfig.objects.all().count()
            deleted_counts["ai_api_configs"] = AIAPIConfig.objects.all().count()
            AIConfig.objects.all().delete()
            AIAPIConfig.objects.all().delete()
        else:
            deleted_counts["ai_configs"] = 0
            deleted_counts["ai_api_configs"] = 0
            self.stdout.write('✅ Keeping AI configurations')
        
        return deleted_counts

    def cleanup_files(self):
        """Remove uploaded files"""
        self.stdout.write('🗑️  Cleaning up uploaded files...')
        
        # Clean up CV files
        cv_dir = os.path.join(settings.MEDIA_ROOT, 'cvs')
        if os.path.exists(cv_dir):
            import shutil
            try:
                shutil.rmtree(cv_dir)
                os.makedirs(cv_dir, exist_ok=True)
                self.stdout.write('✅ CV files cleaned up')
            except Exception as e:
                self.stdout.write(f'⚠️  Warning: Could not clean CV files: {str(e)}')
        
        # Clean up welcome images
        welcome_dir = os.path.join(settings.MEDIA_ROOT, 'welcome_images')
        if os.path.exists(welcome_dir):
            import shutil
            try:
                shutil.rmtree(welcome_dir)
                os.makedirs(welcome_dir, exist_ok=True)
                self.stdout.write('✅ Welcome images cleaned up')
            except Exception as e:
                self.stdout.write(f'⚠️  Warning: Could not clean welcome images: {str(e)}') 