"""
Django management command for database migration
Migrates from SQLite to PostgreSQL with validation and backup
"""

import os
import json
from django.core.management.base import BaseCommand, CommandError
from django.core.management import call_command
from django.db import transaction
from cv_analyzer.database_optimization import DatabaseMigration<PERSON>anager, DatabaseMaintenanceManager


class Command(BaseCommand):
    help = 'Migrate database from SQLite to PostgreSQL with comprehensive validation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--validate-only',
            action='store_true',
            help='Only validate migration readiness without performing migration'
        )
        parser.add_argument(
            '--backup',
            action='store_true',
            help='Create backup before migration'
        )
        parser.add_argument(
            '--output',
            type=str,
            help='Output file for migration results (JSON format)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force migration even with warnings'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Starting CV Analyzer Database Migration...')
        )

        migration_manager = DatabaseMigrationManager()
        maintenance_manager = DatabaseMaintenanceManager()
        results = {}

        try:
            # Step 1: Validate migration readiness
            self.stdout.write('Validating migration readiness...')
            validation_results = migration_manager.validate_migration_readiness()
            results['validation'] = validation_results
            
            self._display_validation_results(validation_results)
            
            if not validation_results['ready'] and not options['force']:
                raise CommandError(
                    'Migration validation failed. Use --force to override or fix issues first.'
                )
            
            if options['validate_only']:
                self.stdout.write(
                    self.style.SUCCESS('Validation completed. Ready for migration.')
                )
                return

            # Step 2: Create backup if requested
            if options['backup']:
                self.stdout.write('Creating database backup...')
                backup_result = maintenance_manager.create_backup()
                results['backup'] = backup_result
                
                if backup_result['success']:
                    self.stdout.write(
                        self.style.SUCCESS(f'Backup created: {backup_result["backup_path"]}')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Backup failed: {backup_result.get("error", "Unknown error")}')
                    )

            # Step 3: Create migration script
            self.stdout.write('Creating migration script...')
            script_path = migration_manager.create_migration_script()
            results['migration_script'] = script_path
            
            self.stdout.write(
                self.style.SUCCESS(f'Migration script created: {script_path}')
            )

            # Step 4: Provide migration instructions
            self._display_migration_instructions(script_path)

            # Save results to file if requested
            if options['output']:
                with open(options['output'], 'w') as f:
                    json.dump(results, f, indent=2, default=str)
                self.stdout.write(
                    self.style.SUCCESS(f'Results saved to {options["output"]}')
                )

            self.stdout.write(
                self.style.SUCCESS('Database migration preparation completed!')
            )

        except Exception as e:
            raise CommandError(f'Database migration failed: {str(e)}')

    def _display_validation_results(self, validation_results):
        """Display migration validation results"""
        if validation_results['ready']:
            self.stdout.write(self.style.SUCCESS('✓ Migration validation passed'))
        else:
            self.stdout.write(self.style.ERROR('✗ Migration validation failed'))

        # Display errors
        for error in validation_results.get('errors', []):
            self.stdout.write(self.style.ERROR(f'  ERROR: {error}'))

        # Display warnings
        for warning in validation_results.get('warnings', []):
            self.stdout.write(self.style.WARNING(f'  WARNING: {warning}'))

        # Display check details
        checks = validation_results.get('checks', {})
        
        # PostgreSQL connection
        pg_check = checks.get('postgresql_connection', {})
        if pg_check.get('connected'):
            self.stdout.write(self.style.SUCCESS('✓ PostgreSQL connection successful'))
            self.stdout.write(f'  Version: {pg_check.get("version", "Unknown")}')
        else:
            self.stdout.write(self.style.ERROR('✗ PostgreSQL connection failed'))
            self.stdout.write(f'  Error: {pg_check.get("error", "Unknown")}')

        # Disk space
        disk_check = checks.get('disk_space', {})
        if disk_check.get('sufficient'):
            self.stdout.write(self.style.SUCCESS('✓ Sufficient disk space available'))
        else:
            self.stdout.write(self.style.ERROR('✗ Insufficient disk space'))
        
        self.stdout.write(f'  Current DB: {disk_check.get("current_db_size_mb", 0):.1f} MB')
        self.stdout.write(f'  Available: {disk_check.get("available_space_gb", 0):.1f} GB')

        # Data consistency
        consistency_check = checks.get('data_consistency', {})
        if consistency_check.get('consistent'):
            self.stdout.write(self.style.SUCCESS('✓ Data consistency check passed'))
        else:
            self.stdout.write(self.style.WARNING('⚠ Data consistency issues detected'))
            for issue in consistency_check.get('issues', []):
                self.stdout.write(f'  • {issue}')

        # Dependencies
        dep_check = checks.get('dependencies', {})
        if dep_check.get('satisfied'):
            self.stdout.write(self.style.SUCCESS('✓ All dependencies satisfied'))
        else:
            self.stdout.write(self.style.ERROR('✗ Missing dependencies'))
            for package in dep_check.get('missing_packages', []):
                self.stdout.write(f'  Missing: {package}')

    def _display_migration_instructions(self, script_path):
        """Display step-by-step migration instructions"""
        self.stdout.write(
            self.style.WARNING('\n' + '='*60)
        )
        self.stdout.write(
            self.style.WARNING('MIGRATION INSTRUCTIONS')
        )
        self.stdout.write(
            self.style.WARNING('='*60)
        )
        
        instructions = [
            '1. Stop the CV Analyzer application',
            '2. Ensure PostgreSQL is running and accessible',
            f'3. Make the migration script executable: chmod +x {script_path}',
            f'4. Run the migration script: ./{script_path}',
            '5. Update Django settings to use PostgreSQL',
            '6. Test the application with the new database',
            '7. Update environment variables for production'
        ]
        
        for instruction in instructions:
            self.stdout.write(f'   {instruction}')
        
        self.stdout.write(
            self.style.WARNING('\nIMPORTANT NOTES:')
        )
        self.stdout.write('   • Keep the original SQLite database as backup')
        self.stdout.write('   • Test thoroughly before deploying to production')
        self.stdout.write('   • Update connection pooling and performance settings')
        self.stdout.write('   • Monitor database performance after migration')
        
        self.stdout.write(
            self.style.WARNING('='*60 + '\n')
        ) 