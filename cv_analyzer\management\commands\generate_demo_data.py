import os
import requests
import random
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from django.contrib.auth.models import User
from django.utils import timezone
from cv_analyzer.models import Company, Vacancy, CV, ApplicantProfile
from django.core.files.storage import default_storage


class Command(BaseCommand):
    help = 'Generate demo data for testing the CV analyzer system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--companies',
            type=int,
            default=5,
            help='Number of companies to create (default: 5)'
        )
        parser.add_argument(
            '--vacancies',
            type=int,
            default=10,
            help='Number of vacancies to create (default: 10)'
        )
        parser.add_argument(
            '--cvs',
            type=int,
            default=10,
            help='Number of CVs to download (default: 10)'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting demo data generation...'))
        
        # Create demo companies
        companies = self.create_companies(options['companies'])
        self.stdout.write(
            self.style.SUCCESS(f'✓ Created {len(companies)} companies')
        )
        
        # Create demo vacancies
        vacancies = self.create_vacancies(companies, options['vacancies'])
        self.stdout.write(
            self.style.SUCCESS(f'✓ Created {len(vacancies)} vacancies')
        )
        
        # Download sample CVs
        downloaded_cvs = self.download_sample_cvs(options['cvs'])
        self.stdout.write(
            self.style.SUCCESS(f'✓ Downloaded {downloaded_cvs} sample CVs')
        )
        
        self.stdout.write(
            self.style.SUCCESS('\n🎉 Demo data generation completed successfully!')
        )
        self.stdout.write(
            self.style.WARNING(
                f'\nDemo data summary:'
                f'\n- Companies: {len(companies)}'
                f'\n- Vacancies: {len(vacancies)}'
                f'\n- CVs: {downloaded_cvs}'
                f'\n\nYou can now test the full system at:'
                f'\n- Dashboard: http://127.0.0.1:8000/dashboard/'
                f'\n- Operations Hub: http://127.0.0.1:8000/operations-hub/'
            )
        )

    def create_companies(self, count):
        """Create demo companies with realistic data"""
        companies_data = [
            {
                'name': 'TechFlow Solutions',
                'industry': 'Technology',
                'website': 'https://techflow.com',
                'description': 'Leading provider of innovative software solutions for enterprise clients. Specializing in cloud computing, AI, and data analytics.',
                'size': 'Large (500+ employees)'
            },
            {
                'name': 'Digital Marketing Pro',
                'industry': 'Marketing',
                'website': 'https://digitalmarketingpro.com',
                'description': 'Full-service digital marketing agency helping brands grow their online presence through SEO, social media, and content marketing.',
                'size': 'Medium (50-200 employees)'
            },
            {
                'name': 'HealthCare Innovations',
                'industry': 'Healthcare',
                'website': 'https://healthcareinnovations.com',
                'description': 'Revolutionizing healthcare through technology and innovation. Providing cutting-edge medical devices and digital health solutions.',
                'size': 'Large (200-500 employees)'
            },
            {
                'name': 'GreenEnergy Corp',
                'industry': 'Energy',
                'website': 'https://greenenergycorp.com',
                'description': 'Sustainable energy solutions company focused on solar, wind, and battery storage technologies for a cleaner future.',
                'size': 'Medium (100-300 employees)'
            },
            {
                'name': 'FinanceFirst Advisory',
                'industry': 'Finance',
                'website': 'https://financefirst.com',
                'description': 'Premier financial advisory firm providing investment management, financial planning, and wealth management services.',
                'size': 'Medium (75-150 employees)'
            },
            {
                'name': 'EduTech Innovations',
                'industry': 'Education',
                'website': 'https://edutech.com',
                'description': 'Educational technology company creating interactive learning platforms and tools for schools and universities.',
                'size': 'Small (25-75 employees)'
            },
            {
                'name': 'RetailPlus Systems',
                'industry': 'Retail',
                'website': 'https://retailplus.com',
                'description': 'Comprehensive retail management solutions including POS systems, inventory management, and customer analytics.',
                'size': 'Large (300+ employees)'
            },
            {
                'name': 'Creative Design Studio',
                'industry': 'Design',
                'website': 'https://creativedesign.com',
                'description': 'Award-winning design agency specializing in branding, web design, and digital experiences for modern businesses.',
                'size': 'Small (15-50 employees)'
            }
        ]
        
        created_companies = []
        for i in range(min(count, len(companies_data))):
            company_data = companies_data[i]
            company, created = Company.objects.get_or_create(
                name=company_data['name'],
                defaults={
                    'industry': company_data['industry'],
                    'website': company_data['website'],
                    'description': company_data['description'],
                    'size': company_data['size']
                }
            )
            created_companies.append(company)
            if created:
                self.stdout.write(f'  → Created company: {company.name}')
            else:
                self.stdout.write(f'  → Company already exists: {company.name}')
        
        return created_companies

    def create_vacancies(self, companies, count):
        """Create demo vacancies for the companies"""
        vacancy_templates = [
            {
                'title': 'Senior Software Engineer',
                'description': 'We are looking for an experienced software engineer to join our team. You will be responsible for developing scalable web applications using modern technologies.',
                'requirements': 'Bachelor\'s degree in Computer Science, 5+ years of experience with Python/Django, React, PostgreSQL. Experience with cloud platforms (AWS/Azure) preferred.',
                'category': 'Technology'
            },
            {
                'title': 'Digital Marketing Specialist',
                'description': 'Join our marketing team to create and execute digital marketing campaigns across multiple channels. Help drive brand awareness and lead generation.',
                'requirements': 'Bachelor\'s degree in Marketing or related field, 3+ years of digital marketing experience, proficiency in Google Analytics, social media platforms.',
                'category': 'Marketing'
            },
            {
                'title': 'Data Analyst',
                'description': 'Analyze complex datasets to provide insights and recommendations to business stakeholders. Create dashboards and reports to track KPIs.',
                'requirements': 'Bachelor\'s degree in Statistics, Mathematics, or related field, 2+ years of experience with SQL, Python/R, strong analytical skills.',
                'category': 'Analytics'
            },
            {
                'title': 'UX/UI Designer',
                'description': 'Design intuitive and engaging user experiences for our web and mobile applications. Collaborate with product managers and developers.',
                'requirements': 'Bachelor\'s degree in Design or related field, 3+ years of UX/UI design experience, proficiency in Figma, Adobe Creative Suite.',
                'category': 'Design'
            },
            {
                'title': 'Project Manager',
                'description': 'Lead cross-functional teams to deliver projects on time and within budget. Coordinate with stakeholders and ensure project success.',
                'requirements': 'Bachelor\'s degree, PMP certification preferred, 4+ years of project management experience, excellent communication skills.',
                'category': 'Management'
            },
            {
                'title': 'DevOps Engineer',
                'description': 'Implement and maintain CI/CD pipelines, manage cloud infrastructure, and ensure system reliability and security.',
                'requirements': 'Bachelor\'s degree in Computer Science, 3+ years of DevOps experience, experience with AWS/Azure, Docker, Kubernetes.',
                'category': 'Technology'
            },
            {
                'title': 'Sales Representative',
                'description': 'Generate new business opportunities and maintain relationships with existing clients. Meet sales targets and contribute to company growth.',
                'requirements': 'Bachelor\'s degree preferred, 2+ years of sales experience, excellent communication and negotiation skills.',
                'category': 'Sales'
            },
            {
                'title': 'Content Writer',
                'description': 'Create engaging content for websites, blogs, social media, and marketing materials. Help establish our brand voice and thought leadership.',
                'requirements': 'Bachelor\'s degree in English, Marketing, or related field, 2+ years of content writing experience, SEO knowledge.',
                'category': 'Content'
            }
        ]
        
        created_vacancies = []
        for i in range(count):
            template = vacancy_templates[i % len(vacancy_templates)]
            company = companies[i % len(companies)]
            
            # Add some variation to titles if creating multiple of the same type
            if i >= len(vacancy_templates):
                suffix = ['Senior', 'Junior', 'Lead', 'Principal'][i % 4]
                title = f"{suffix} {template['title']}"
            else:
                title = template['title']
            
            vacancy, created = Vacancy.objects.get_or_create(
                title=title,
                company=company,
                defaults={
                    'description': template['description'],
                    'requirements': template['requirements'],
                    'category': template['category'],
                    'status': 'active',
                    'expires_at': timezone.now() + timedelta(days=random.randint(10, 60))
                }
            )
            created_vacancies.append(vacancy)
            if created:
                self.stdout.write(f'  → Created vacancy: {title} at {company.name}')
            else:
                self.stdout.write(f'  → Vacancy already exists: {title} at {company.name}')
        
        return created_vacancies

    def download_sample_cvs(self, count):
        """Download sample CVs from web sources"""
        # List of publicly available CV/resume samples
        cv_sources = [
            {
                'url': 'https://novoresume.com/career-blog/wp-content/uploads/2020/04/Software-Engineer-Resume-Example.pdf',
                'filename': 'software_engineer_sample.pdf'
            },
            {
                'url': 'https://www.thebalancemoney.com/thmb/GqPK_sJiKqRFp6m8UdpLYCNd8fA=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/marketing-manager-resume-2062491-final-5c1ce71346e0fb0001d6e3c6.pdf',
                'filename': 'marketing_manager_sample.pdf'
            },
            {
                'url': 'https://resumegenius.com/wp-content/uploads/Data-Analyst-Resume-Sample.pdf',
                'filename': 'data_analyst_sample.pdf'
            }
        ]
        
        # Alternative: Create sample CV files locally if downloads fail
        sample_cv_content = """
JOHN DOE
Software Engineer
Email: <EMAIL>
Phone: (*************

EXPERIENCE
Senior Software Engineer | TechCorp (2020-2023)
- Developed and maintained web applications using Python and Django
- Led a team of 5 developers on multiple projects
- Implemented CI/CD pipelines reducing deployment time by 50%

Software Developer | StartupXYZ (2018-2020)
- Built responsive web applications using React and Node.js
- Collaborated with design team to implement user-friendly interfaces
- Optimized database queries improving application performance by 30%

EDUCATION
Bachelor of Science in Computer Science
University of Technology (2014-2018)

SKILLS
- Programming Languages: Python, JavaScript, Java, C++
- Frameworks: Django, React, Node.js, Flask
- Databases: PostgreSQL, MySQL, MongoDB
- Tools: Git, Docker, Jenkins, AWS
"""
        
        downloaded_count = 0
        
        # Create media/cvs directory if it doesn't exist
        cv_dir = 'media/cvs'
        os.makedirs(cv_dir, exist_ok=True)
        
        # Try to download real CVs first
        for i, source in enumerate(cv_sources[:count]):
            try:
                response = requests.get(source['url'], timeout=30)
                response.raise_for_status()
                
                file_content = ContentFile(response.content)
                file_path = default_storage.save(
                    f'cvs/{source["filename"]}',
                    file_content
                )
                
                downloaded_count += 1
                self.stdout.write(f'  → Downloaded: {source["filename"]}')
                
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'  → Failed to download {source["filename"]}: {str(e)}')
                )
        
        # Create local sample CVs for the remaining count
        remaining = count - downloaded_count
        for i in range(remaining):
            filename = f'sample_cv_{i+1}.txt'
            
            # Customize the content slightly for each CV
            names = ['John Doe', 'Jane Smith', 'Mike Johnson', 'Sarah Wilson', 'David Brown', 'Lisa Davis', 'Tom Clark', 'Emma Taylor']
            roles = ['Software Engineer', 'Data Analyst', 'Marketing Specialist', 'UX Designer', 'Project Manager', 'DevOps Engineer']
            
            customized_content = sample_cv_content.replace(
                'JOHN DOE', names[i % len(names)]
            ).replace(
                'Software Engineer', roles[i % len(roles)]
            ).replace(
                '<EMAIL>', f'{names[i % len(names)].lower().replace(" ", ".")}@email.com'
            )
            
            try:
                file_content = ContentFile(customized_content.encode('utf-8'))
                file_path = default_storage.save(f'cvs/{filename}', file_content)
                
                downloaded_count += 1
                self.stdout.write(f'  → Created: {filename}')
                
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'  → Failed to create {filename}: {str(e)}')
                )
        
        return downloaded_count 