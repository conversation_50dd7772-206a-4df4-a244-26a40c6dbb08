"""
Comprehensive Application Health Check Command
Validates all main application flows and components are working properly.
"""

import os
import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.contrib.auth.models import User
from django.db import connection
from django.core.cache import cache
from django.test import Client
from django.urls import reverse

from cv_analyzer.models import (
    Company, Vacancy, CV, CVAnalysis, ApplicantProfile,
    AIConfig, AIAPIConfig, SecurityAuditLog, FileUploadLog,
    HealthCheckResult
)

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Perform comprehensive application health check'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--component',
            type=str,
            choices=[
                'all', 'database', 'cache', 'ai', 'security', 
                'file_upload', 'authentication', 'api', 'frontend'
            ],
            default='all',
            help='Specific component to check'
        )
        
        parser.add_argument(
            '--save-results',
            action='store_true',
            help='Save results to database'
        )
        
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed health check results'
        )
        
        parser.add_argument(
            '--timeout',
            type=int,
            default=30,
            help='Timeout for each check in seconds'
        )
    
    def handle(self, *args, **options):
        """Main command handler"""
        self.component = options['component']
        self.save_results = options['save_results']
        self.detailed = options['detailed']
        self.timeout = options['timeout']
        
        self.stdout.write(
            self.style.SUCCESS('🏥 Starting CV Analyzer Health Check...\n')
        )
        
        start_time = datetime.now()
        
        # Run health checks
        if self.component == 'all':
            results = self.run_all_checks()
        else:
            results = self.run_specific_check(self.component)
        
        duration = datetime.now() - start_time
        
        # Display results
        self.display_results(results, duration)
        
        # Save results if requested
        if self.save_results:
            self.save_check_results(results)
        
        # Determine overall status
        overall_healthy = all(result['status'] == 'healthy' for result in results.values())
        
        if overall_healthy:
            self.stdout.write(
                self.style.SUCCESS(f'\n✅ All systems healthy! Duration: {duration.total_seconds():.2f}s')
            )
        else:
            failed_components = [name for name, result in results.items() if result['status'] != 'healthy']
            self.stdout.write(
                self.style.ERROR(f'\n❌ Health check failed. Issues found in: {", ".join(failed_components)}')
            )
            raise CommandError("Health check failed")
    
    def run_all_checks(self) -> Dict[str, Any]:
        """Run all health checks"""
        checks = {
            'database': self.check_database,
            'cache': self.check_cache,
            'ai_providers': self.check_ai_providers,
            'security': self.check_security_systems,
            'file_upload': self.check_file_upload_system,
            'authentication': self.check_authentication,
            'api_endpoints': self.check_api_endpoints,
            'frontend_pages': self.check_frontend_pages,
            'models_integrity': self.check_models_integrity,
            'configuration': self.check_configuration
        }
        
        results = {}
        
        for check_name, check_func in checks.items():
            self.stdout.write(f'🔍 Checking {check_name}...', ending='')
            
            try:
                start_time = datetime.now()
                result = check_func()
                duration = (datetime.now() - start_time).total_seconds()
                
                result['duration'] = duration
                results[check_name] = result
                
                if result['status'] == 'healthy':
                    self.stdout.write(self.style.SUCCESS(' ✓'))
                elif result['status'] == 'warning':
                    self.stdout.write(self.style.WARNING(' ⚠'))
                else:
                    self.stdout.write(self.style.ERROR(' ✗'))
                    
            except Exception as e:
                results[check_name] = {
                    'status': 'error',
                    'message': str(e),
                    'details': {},
                    'duration': 0
                }
                self.stdout.write(self.style.ERROR(' ✗'))
        
        return results
    
    def run_specific_check(self, component: str) -> Dict[str, Any]:
        """Run specific component check"""
        check_map = {
            'database': self.check_database,
            'cache': self.check_cache,
            'ai': self.check_ai_providers,
            'security': self.check_security_systems,
            'file_upload': self.check_file_upload_system,
            'authentication': self.check_authentication,
            'api': self.check_api_endpoints,
            'frontend': self.check_frontend_pages
        }
        
        if component not in check_map:
            raise CommandError(f"Unknown component: {component}")
        
        check_func = check_map[component]
        result = check_func()
        
        return {component: result}
    
    def check_database(self) -> Dict[str, Any]:
        """Check database connectivity and basic operations"""
        try:
            # Test database connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                row = cursor.fetchone()
                if row[0] != 1:
                    raise Exception("Database query returned unexpected result")
            
            # Test model operations
            user_count = User.objects.count()
            cv_count = CV.objects.count()
            company_count = Company.objects.count()
            
            # Test database write operation
            test_company = Company.objects.create(
                name="Health Check Test Company",
                description="Test company for health check",
                industry="Testing",
                size="Small"
            )
            test_company.delete()  # Clean up
            
            return {
                'status': 'healthy',
                'message': 'Database operations successful',
                'details': {
                    'users': user_count,
                    'cvs': cv_count,
                    'companies': company_count,
                    'connection_vendor': connection.vendor,
                    'db_name': settings.DATABASES['default']['NAME']
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Database check failed: {str(e)}',
                'details': {'error': str(e)}
            }
    
    def check_cache(self) -> Dict[str, Any]:
        """Check cache system functionality"""
        try:
            # Test cache write/read
            test_key = f"health_check_{datetime.now().timestamp()}"
            test_value = "health_check_value"
            
            cache.set(test_key, test_value, 60)
            retrieved_value = cache.get(test_key)
            
            if retrieved_value != test_value:
                raise Exception("Cache value mismatch")
            
            # Clean up
            cache.delete(test_key)
            
            return {
                'status': 'healthy',
                'message': 'Cache operations successful',
                'details': {
                    'backend': str(cache.__class__),
                    'test_key': test_key
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Cache check failed: {str(e)}',
                'details': {'error': str(e)}
            }
    
    def check_ai_providers(self) -> Dict[str, Any]:
        """Check AI provider configurations and connectivity"""
        try:
            # Check AI configurations exist
            ai_configs = AIAPIConfig.objects.filter(is_active=True)
            
            if not ai_configs.exists():
                return {
                    'status': 'warning',
                    'message': 'No active AI configurations found',
                    'details': {'active_configs': 0}
                }
            
            provider_status = {}
            for config in ai_configs:
                try:
                    # Test basic configuration
                    if not config.api_key:
                        provider_status[config.provider] = 'missing_api_key'
                        continue
                    
                    provider_status[config.provider] = 'configured'
                    
                except Exception as e:
                    provider_status[config.provider] = f'error: {str(e)}'
            
            # Check if at least one provider is properly configured
            healthy_providers = [p for p, s in provider_status.items() if s == 'configured']
            
            if healthy_providers:
                return {
                    'status': 'healthy',
                    'message': f'AI providers configured: {", ".join(healthy_providers)}',
                    'details': {
                        'total_configs': len(ai_configs),
                        'provider_status': provider_status
                    }
                }
            else:
                return {
                    'status': 'warning',
                    'message': 'No properly configured AI providers',
                    'details': {'provider_status': provider_status}
                }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'AI provider check failed: {str(e)}',
                'details': {'error': str(e)}
            }
    
    def check_security_systems(self) -> Dict[str, Any]:
        """Check security system functionality"""
        try:
            # Check security logging
            recent_logs = SecurityAuditLog.objects.filter(
                timestamp__gte=datetime.now() - timedelta(hours=24)
            ).count()
            
            # Check file upload logging
            recent_uploads = FileUploadLog.objects.filter(
                uploaded_at__gte=datetime.now() - timedelta(hours=24)
            ).count()
            
            # Test security log creation
            test_log = SecurityAuditLog.objects.create(
                event_type='health_check',
                username='health_check_user',
                ip_address='127.0.0.1',
                user_agent='Health Check Agent',
                request_path='/health_check',
                request_method='GET',
                success=True,
                details={'test': True}
            )
            test_log.delete()  # Clean up
            
            return {
                'status': 'healthy',
                'message': 'Security systems operational',
                'details': {
                    'recent_security_logs': recent_logs,
                    'recent_file_uploads': recent_uploads,
                    'logging_functional': True
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Security system check failed: {str(e)}',
                'details': {'error': str(e)}
            }
    
    def check_file_upload_system(self) -> Dict[str, Any]:
        """Check file upload system functionality"""
        try:
            from cv_analyzer.security import FileUploadValidator, FileQuarantine
            
            # Check upload directory exists and is writable
            upload_dir = os.path.join(settings.MEDIA_ROOT, 'cvs')
            if not os.path.exists(upload_dir):
                os.makedirs(upload_dir, exist_ok=True)
            
            if not os.access(upload_dir, os.W_OK):
                return {
                    'status': 'error',
                    'message': 'Upload directory not writable',
                    'details': {'upload_dir': upload_dir}
                }
            
            # Test file validator initialization
            validator = FileUploadValidator()
            quarantine = FileQuarantine()
            
            return {
                'status': 'healthy',
                'message': 'File upload system ready',
                'details': {
                    'upload_dir': upload_dir,
                    'validator_ready': True,
                    'quarantine_ready': True
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'File upload system check failed: {str(e)}',
                'details': {'error': str(e)}
            }
    
    def check_authentication(self) -> Dict[str, Any]:
        """Check authentication system"""
        try:
            # Check if superuser exists
            superuser_exists = User.objects.filter(is_superuser=True).exists()
            
            # Test user creation/deletion
            test_username = f"health_check_user_{datetime.now().timestamp()}"
            test_user = User.objects.create_user(
                username=test_username,
                email='<EMAIL>',
                password='test_password'
            )
            
            # Test user authentication
            from django.contrib.auth import authenticate
            auth_user = authenticate(username=test_username, password='test_password')
            
            if auth_user is None:
                raise Exception("User authentication failed")
            
            # Clean up
            test_user.delete()
            
            return {
                'status': 'healthy',
                'message': 'Authentication system functional',
                'details': {
                    'superuser_exists': superuser_exists,
                    'user_creation': True,
                    'authentication': True
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Authentication check failed: {str(e)}',
                'details': {'error': str(e)}
            }
    
    def check_api_endpoints(self) -> Dict[str, Any]:
        """Check API endpoints functionality"""
        try:
            client = Client()
            
            # Test public endpoints
            public_endpoints = [
                ('welcome', '/'),
                ('login', '/accounts/login/'),
            ]
            
            endpoint_status = {}
            
            for name, url in public_endpoints:
                try:
                    response = client.get(url)
                    endpoint_status[name] = {
                        'status_code': response.status_code,
                        'accessible': response.status_code in [200, 302]
                    }
                except Exception as e:
                    endpoint_status[name] = {
                        'status_code': None,
                        'accessible': False,
                        'error': str(e)
                    }
            
            # Check if all critical endpoints are accessible
            critical_accessible = all(
                status['accessible'] for status in endpoint_status.values()
            )
            
            return {
                'status': 'healthy' if critical_accessible else 'warning',
                'message': 'API endpoints checked',
                'details': {
                    'endpoints': endpoint_status,
                    'all_accessible': critical_accessible
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'API endpoint check failed: {str(e)}',
                'details': {'error': str(e)}
            }
    
    def check_frontend_pages(self) -> Dict[str, Any]:
        """Check frontend pages functionality"""
        try:
            client = Client()
            
            # Test frontend pages
            pages = [
                ('welcome', '/'),
                ('login', '/accounts/login/'),
            ]
            
            page_status = {}
            
            for name, url in pages:
                try:
                    response = client.get(url)
                    page_status[name] = {
                        'status_code': response.status_code,
                        'loads': response.status_code == 200,
                        'content_length': len(response.content)
                    }
                except Exception as e:
                    page_status[name] = {
                        'status_code': None,
                        'loads': False,
                        'error': str(e)
                    }
            
            # Check if all pages load properly
            all_loading = all(
                status['loads'] for status in page_status.values() 
                if 'loads' in status
            )
            
            return {
                'status': 'healthy' if all_loading else 'warning',
                'message': 'Frontend pages checked',
                'details': {
                    'pages': page_status,
                    'all_loading': all_loading
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Frontend check failed: {str(e)}',
                'details': {'error': str(e)}
            }
    
    def check_models_integrity(self) -> Dict[str, Any]:
        """Check database models integrity"""
        try:
            # Test key model relationships
            models_info = {}
            
            # Check core models
            models_to_check = [
                (User, 'users'),
                (Company, 'companies'),
                (Vacancy, 'vacancies'),
                (CV, 'cvs'),
                (CVAnalysis, 'cv_analyses'),
                (ApplicantProfile, 'applicant_profiles'),
                (AIConfig, 'ai_configs'),
                (AIAPIConfig, 'ai_api_configs'),
            ]
            
            for model, name in models_to_check:
                try:
                    count = model.objects.count()
                    # Test basic query
                    model.objects.first()
                    models_info[name] = {
                        'count': count,
                        'accessible': True
                    }
                except Exception as e:
                    models_info[name] = {
                        'count': 0,
                        'accessible': False,
                        'error': str(e)
                    }
            
            # Check if all models are accessible
            all_accessible = all(
                info['accessible'] for info in models_info.values()
            )
            
            return {
                'status': 'healthy' if all_accessible else 'error',
                'message': 'Database models checked',
                'details': {
                    'models': models_info,
                    'all_accessible': all_accessible
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Models integrity check failed: {str(e)}',
                'details': {'error': str(e)}
            }
    
    def check_configuration(self) -> Dict[str, Any]:
        """Check application configuration"""
        try:
            config_status = {}
            
            # Check critical settings
            critical_settings = [
                'SECRET_KEY',
                'DEBUG',
                'ALLOWED_HOSTS',
                'DATABASES',
                'MEDIA_ROOT',
                'STATIC_ROOT',
            ]
            
            for setting_name in critical_settings:
                try:
                    value = getattr(settings, setting_name, None)
                    config_status[setting_name] = {
                        'configured': value is not None,
                        'value_type': type(value).__name__ if value is not None else None
                    }
                except Exception as e:
                    config_status[setting_name] = {
                        'configured': False,
                        'error': str(e)
                    }
            
            # Check if all critical settings are configured
            all_configured = all(
                status['configured'] for status in config_status.values()
            )
            
            return {
                'status': 'healthy' if all_configured else 'warning',
                'message': 'Configuration checked',
                'details': {
                    'settings': config_status,
                    'all_configured': all_configured,
                    'debug_mode': getattr(settings, 'DEBUG', False)
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Configuration check failed: {str(e)}',
                'details': {'error': str(e)}
            }
    
    def display_results(self, results: Dict[str, Any], duration: timedelta):
        """Display health check results"""
        self.stdout.write(f'\n📊 Health Check Results (Duration: {duration.total_seconds():.2f}s)\n')
        
        for component, result in results.items():
            status_color = {
                'healthy': self.style.SUCCESS,
                'warning': self.style.WARNING,
                'error': self.style.ERROR
            }.get(result['status'], self.style.ERROR)
            
            status_icon = {
                'healthy': '✅',
                'warning': '⚠️',
                'error': '❌'
            }.get(result['status'], '❓')
            
            self.stdout.write(
                f'{status_icon} {component.replace("_", " ").title()}: '
                f'{status_color(result["status"].upper())} - {result["message"]}'
            )
            
            if self.detailed and 'details' in result:
                for key, value in result['details'].items():
                    self.stdout.write(f'    {key}: {value}')
                
                if 'duration' in result:
                    self.stdout.write(f'    Duration: {result["duration"]:.3f}s')
    
    def save_check_results(self, results: Dict[str, Any]):
        """Save health check results to database"""
        try:
            for component, result in results.items():
                HealthCheckResult.objects.create(
                    check_name=component,
                    status=result['status'],
                    response_time_ms=result.get('duration', 0) * 1000,
                    message=result['message'],
                    details=result.get('details', {})
                )
            
            self.stdout.write(
                self.style.SUCCESS('💾 Health check results saved to database')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Failed to save results: {str(e)}')
            )

# Usage examples:
# python manage.py health_check
# python manage.py health_check --component=database
# python manage.py health_check --detailed --save-results
# python manage.py health_check --component=all --timeout=60 