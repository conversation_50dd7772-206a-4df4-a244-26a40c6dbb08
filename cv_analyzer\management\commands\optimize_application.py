"""
Django management command for comprehensive application optimization
Manages error handling, API improvements, AI system health, and task queue optimization
"""

import json
import time
from django.core.management.base import BaseCommand, CommandError
from cv_analyzer.error_handling import get_error_statistics, structured_logger
from cv_analyzer.ai_system_enhancement import (
    get_ai_provider_health, get_ai_usage_statistics, get_ai_cost_monitoring
)
from cv_analyzer.task_queue_optimization import (
    get_queue_health, get_queue_statistics, profile_task
)


class Command(BaseCommand):
    help = 'Optimize CV Analyzer application components including error handling, AI systems, and task queues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--component',
            type=str,
            choices=['error-handling', 'ai-system', 'task-queue', 'all'],
            default='all',
            help='Application component to optimize'
        )
        parser.add_argument(
            '--check-health',
            action='store_true',
            help='Perform health checks on all components'
        )
        parser.add_argument(
            '--ai-test',
            action='store_true',
            help='Test AI provider connectivity and performance'
        )
        parser.add_argument(
            '--queue-analysis',
            action='store_true',
            help='Perform detailed task queue analysis'
        )
        parser.add_argument(
            '--profile-task',
            type=str,
            help='Profile specific task performance (task name)'
        )
        parser.add_argument(
            '--output',
            type=str,
            help='Output file for optimization results (JSON format)'
        )
        parser.add_argument(
            '--hours',
            type=int,
            default=24,
            help='Time range in hours for analysis (default: 24)'
        )

    def handle(self, *args, **options):
        component = options['component']
        
        try:
            if component == 'all' or component == 'error-handling':
                self._optimize_error_handling(options)
            
            if component == 'all' or component == 'ai-system':
                self._optimize_ai_system(options)
            
            if component == 'all' or component == 'task-queue':
                self._optimize_task_queue(options)
            
            if options['check_health']:
                self._perform_health_checks(options)
            
            if options['ai_test']:
                self._test_ai_providers(options)
            
            if options['queue_analysis']:
                self._analyze_task_queues(options)
            
            if options['profile_task']:
                self._profile_specific_task(options)
            
        except Exception as e:
            raise CommandError(f'Application optimization failed: {str(e)}')

    def _optimize_error_handling(self, options):
        """Optimize error handling and logging system"""
        self.stdout.write(
            self.style.SUCCESS('🔧 Optimizing Error Handling System...')
        )
        
        # Get error statistics
        hours = options['hours']
        error_stats = get_error_statistics(hours)
        
        self.stdout.write('\n📊 Error Statistics:')
        self.stdout.write(f'  Total Errors ({hours}h): {error_stats.get("total_errors", 0)}')
        self.stdout.write(f'  Unique Error Types: {error_stats.get("unique_error_types", 0)}')
        self.stdout.write(f'  Error Rate/Hour: {error_stats.get("error_rate_per_hour", 0):.2f}')
        
        # Display top errors
        top_errors = error_stats.get('top_errors', [])
        if top_errors:
            self.stdout.write('\n🔥 Top Error Types:')
            for i, (error_type, count) in enumerate(top_errors[:5], 1):
                self.stdout.write(f'  {i}. {error_type}: {count} occurrences')
        
        # Error handling recommendations
        recommendations = self._get_error_handling_recommendations(error_stats)
        if recommendations:
            self.stdout.write('\n💡 Error Handling Recommendations:')
            for i, rec in enumerate(recommendations, 1):
                self.stdout.write(f'  {i}. {rec}')
        
        # Log optimization status
        structured_logger.info("Error handling optimization completed", {
            'error_stats': error_stats,
            'recommendations_count': len(recommendations)
        })

    def _optimize_ai_system(self, options):
        """Optimize AI system performance and monitoring"""
        self.stdout.write(
            self.style.SUCCESS('🤖 Optimizing AI System...')
        )
        
        # AI provider health check
        provider_health = get_ai_provider_health()
        self.stdout.write('\n🏥 AI Provider Health:')
        
        for provider, health in provider_health.items():
            status = health.get('status', 'unknown')
            if status == 'healthy':
                style = self.style.SUCCESS
                icon = '✅'
            elif status == 'error':
                style = self.style.ERROR
                icon = '❌'
            else:
                style = self.style.WARNING
                icon = '⚠️'
            
            self.stdout.write(style(f'  {icon} {provider}: {status}'))
            
            if 'response_time_ms' in health:
                self.stdout.write(f'    Response Time: {health["response_time_ms"]:.1f}ms')
            
            if 'error' in health:
                self.stdout.write(f'    Error: {health["error"]}')
        
        # AI usage statistics
        usage_stats = get_ai_usage_statistics()
        self.stdout.write('\n📈 AI Usage Statistics:')
        
        for provider, stats in usage_stats.items():
            costs = stats.get('costs', {})
            usage = stats.get('usage', {})
            
            self.stdout.write(f'\n  {provider}:')
            self.stdout.write(f'    Total Cost: ${costs.get("total_cost", 0):.4f}')
            self.stdout.write(f'    Total Requests: {costs.get("requests_count", 0)}')
            self.stdout.write(f'    Requests Last Hour: {usage.get("requests_last_hour", 0)}')
        
        # Cost monitoring
        cost_data = get_ai_cost_monitoring()
        self.stdout.write('\n💰 Cost Monitoring:')
        self.stdout.write(f'  Daily Cost: ${cost_data.get("daily_cost", 0):.2f}')
        self.stdout.write(f'  Monthly Cost: ${cost_data.get("monthly_cost", 0):.2f}')
        self.stdout.write(f'  Daily Budget Usage: {cost_data.get("daily_usage_percent", 0):.1f}%')
        self.stdout.write(f'  Monthly Budget Usage: {cost_data.get("monthly_usage_percent", 0):.1f}%')
        
        # AI optimization recommendations
        ai_recommendations = self._get_ai_optimization_recommendations(
            provider_health, usage_stats, cost_data
        )
        
        if ai_recommendations:
            self.stdout.write('\n💡 AI System Recommendations:')
            for i, rec in enumerate(ai_recommendations, 1):
                self.stdout.write(f'  {i}. {rec}')

    def _optimize_task_queue(self, options):
        """Optimize task queue performance"""
        self.stdout.write(
            self.style.SUCCESS('⚡ Optimizing Task Queue System...')
        )
        
        # Queue health analysis
        queue_health = get_queue_health()
        health_status = queue_health.get('queue_health', 'unknown')
        performance_score = queue_health.get('performance_score', 0)
        
        if health_status == 'healthy':
            style = self.style.SUCCESS
            icon = '✅'
        elif health_status == 'critical':
            style = self.style.ERROR
            icon = '🚨'
        else:
            style = self.style.WARNING
            icon = '⚠️'
        
        self.stdout.write(style(f'\n{icon} Queue Health: {health_status.upper()}'))
        self.stdout.write(f'  Performance Score: {performance_score}/100')
        
        # Display issues
        issues = queue_health.get('issues', [])
        if issues:
            self.stdout.write('\n🔥 Current Issues:')
            for issue in issues:
                self.stdout.write(self.style.WARNING(f'  • {issue}'))
        
        # Queue statistics
        queue_stats = get_queue_statistics()
        self.stdout.write('\n📊 Queue Statistics:')
        
        queue_lengths = queue_stats.get('queue_lengths', {})
        for queue_name, length in queue_lengths.items():
            self.stdout.write(f'  {queue_name}: {length} tasks')
        
        self.stdout.write(f'  Active Tasks: {queue_stats.get("active_tasks_count", 0)}')
        self.stdout.write(f'  Workers: {queue_stats.get("worker_count", 0)}')
        
        # Performance metrics
        performance = queue_stats.get('performance_metrics', {})
        if performance:
            self.stdout.write('\n⚡ Performance Metrics (24h):')
            self.stdout.write(f'  Total Tasks: {performance.get("total_tasks_24h", 0)}')
            self.stdout.write(f'  Success Rate: {performance.get("success_rate", 0):.1f}%')
            self.stdout.write(f'  Avg Duration: {performance.get("average_duration", 0):.2f}s')
        
        # Task queue recommendations
        queue_recommendations = queue_health.get('recommendations', [])
        if queue_recommendations:
            self.stdout.write('\n💡 Task Queue Recommendations:')
            for i, rec in enumerate(queue_recommendations, 1):
                self.stdout.write(f'  {i}. {rec}')

    def _perform_health_checks(self, options):
        """Perform comprehensive health checks"""
        self.stdout.write(
            self.style.SUCCESS('🏥 Performing Health Checks...')
        )
        
        health_results = {
            'timestamp': time.time(),
            'checks': {}
        }
        
        # Error handling health
        error_stats = get_error_statistics(1)  # Last 1 hour
        error_rate = error_stats.get('error_rate_per_hour', 0)
        health_results['checks']['error_handling'] = {
            'status': 'healthy' if error_rate < 10 else 'warning' if error_rate < 50 else 'critical',
            'error_rate_per_hour': error_rate
        }
        
        # AI system health
        ai_health = get_ai_provider_health()
        healthy_providers = sum(1 for h in ai_health.values() if h.get('status') == 'healthy')
        total_providers = len(ai_health)
        
        health_results['checks']['ai_system'] = {
            'status': 'healthy' if healthy_providers == total_providers else 'warning' if healthy_providers > 0 else 'critical',
            'healthy_providers': healthy_providers,
            'total_providers': total_providers
        }
        
        # Task queue health
        queue_health = get_queue_health()
        health_results['checks']['task_queue'] = {
            'status': queue_health.get('queue_health', 'unknown'),
            'performance_score': queue_health.get('performance_score', 0)
        }
        
        # Display results
        self.stdout.write('\n🔍 Health Check Results:')
        for component, check in health_results['checks'].items():
            status = check['status']
            if status == 'healthy':
                style = self.style.SUCCESS
                icon = '✅'
            elif status == 'critical':
                style = self.style.ERROR
                icon = '🚨'
            else:
                style = self.style.WARNING
                icon = '⚠️'
            
            self.stdout.write(style(f'  {icon} {component.replace("_", " ").title()}: {status.upper()}'))
        
        self._save_output(health_results, options.get('output'))

    def _test_ai_providers(self, options):
        """Test AI provider connectivity and performance"""
        self.stdout.write(
            self.style.SUCCESS('🧪 Testing AI Providers...')
        )
        
        provider_health = get_ai_provider_health()
        
        self.stdout.write('\n🔬 AI Provider Test Results:')
        for provider, health in provider_health.items():
            status = health.get('status', 'unknown')
            response_time = health.get('response_time_ms', 0)
            
            if status == 'healthy':
                self.stdout.write(self.style.SUCCESS(f'  ✅ {provider}: PASS ({response_time:.1f}ms)'))
            else:
                error = health.get('error', 'Unknown error')
                self.stdout.write(self.style.ERROR(f'  ❌ {provider}: FAIL - {error}'))

    def _analyze_task_queues(self, options):
        """Perform detailed task queue analysis"""
        self.stdout.write(
            self.style.SUCCESS('📈 Analyzing Task Queues...')
        )
        
        hours = options['hours']
        
        # Get overall queue analysis
        queue_stats = get_queue_statistics()
        performance_metrics = queue_stats.get('performance_metrics', {})
        
        if performance_metrics:
            self.stdout.write(f'\n📊 Task Analysis ({hours}h):')
            self.stdout.write(f'  Total Tasks: {performance_metrics.get("total_tasks_24h", 0)}')
            self.stdout.write(f'  Successful: {performance_metrics.get("successful_tasks", 0)}')
            self.stdout.write(f'  Failed: {performance_metrics.get("failed_tasks", 0)}')
            self.stdout.write(f'  Success Rate: {performance_metrics.get("success_rate", 0):.1f}%')
            
            # Task type distribution
            task_distribution = performance_metrics.get('task_type_distribution', {})
            if task_distribution:
                self.stdout.write('\n📋 Task Type Distribution:')
                for task_type, count in sorted(task_distribution.items(), key=lambda x: x[1], reverse=True):
                    percentage = (count / performance_metrics.get("total_tasks_24h", 1)) * 100
                    self.stdout.write(f'  {task_type}: {count} ({percentage:.1f}%)')

    def _profile_specific_task(self, options):
        """Profile specific task performance"""
        task_name = options['profile_task']
        hours = options['hours']
        
        self.stdout.write(
            self.style.SUCCESS(f'🔍 Profiling Task: {task_name}')
        )
        
        profile_data = profile_task(task_name, hours)
        
        if 'error' in profile_data:
            self.stdout.write(self.style.ERROR(f'  ❌ {profile_data["error"]}'))
            return
        
        self.stdout.write(f'\n📊 Task Profile ({hours}h):')
        self.stdout.write(f'  Total Executions: {profile_data.get("total_executions", 0)}')
        self.stdout.write(f'  Success Rate: {profile_data.get("success_rate", 0):.1f}%')
        
        performance = profile_data.get('performance', {})
        self.stdout.write(f'  Average Duration: {performance.get("average_duration", 0):.2f}s')
        self.stdout.write(f'  Median Duration: {performance.get("median_duration", 0):.2f}s')
        self.stdout.write(f'  95th Percentile: {performance.get("p95_duration", 0):.2f}s')
        
        # Error analysis
        error_analysis = profile_data.get('error_analysis', {})
        if error_analysis:
            self.stdout.write('\n🔥 Error Analysis:')
            for error_type, count in error_analysis.items():
                self.stdout.write(f'  {error_type}: {count}')
        
        # Recommendations
        recommendations = profile_data.get('optimization_recommendations', [])
        if recommendations:
            self.stdout.write('\n💡 Optimization Recommendations:')
            for i, rec in enumerate(recommendations, 1):
                self.stdout.write(f'  {i}. {rec}')

    def _get_error_handling_recommendations(self, error_stats):
        """Generate error handling recommendations"""
        recommendations = []
        
        error_rate = error_stats.get('error_rate_per_hour', 0)
        total_errors = error_stats.get('total_errors', 0)
        
        if error_rate > 10:
            recommendations.append("High error rate detected. Review error patterns and improve handling.")
        
        if total_errors > 100:
            recommendations.append("Large number of errors. Consider implementing better validation.")
        
        top_errors = error_stats.get('top_errors', [])
        if top_errors:
            most_common_error = top_errors[0][0]
            recommendations.append(f"Focus on fixing '{most_common_error}' - the most common error type.")
        
        recommendations.extend([
            "Implement structured logging for better error analysis",
            "Set up automated error alerts for critical issues",
            "Review and update error handling middleware",
            "Consider implementing circuit breakers for external services"
        ])
        
        return recommendations

    def _get_ai_optimization_recommendations(self, provider_health, usage_stats, cost_data):
        """Generate AI system optimization recommendations"""
        recommendations = []
        
        # Health-based recommendations
        unhealthy_providers = [p for p, h in provider_health.items() if h.get('status') != 'healthy']
        if unhealthy_providers:
            recommendations.append(f"Fix unhealthy AI providers: {', '.join(unhealthy_providers)}")
        
        # Cost-based recommendations
        daily_usage = cost_data.get('daily_usage_percent', 0)
        monthly_usage = cost_data.get('monthly_usage_percent', 0)
        
        if daily_usage > 80:
            recommendations.append("Daily AI budget usage is high. Consider cost optimization.")
        
        if monthly_usage > 70:
            recommendations.append("Monthly AI budget usage is concerning. Review usage patterns.")
        
        # Usage-based recommendations
        for provider, stats in usage_stats.items():
            requests_per_hour = stats.get('usage', {}).get('requests_last_hour', 0)
            if requests_per_hour > 100:
                recommendations.append(f"High request rate for {provider}. Consider caching strategies.")
        
        recommendations.extend([
            "Implement AI response caching to reduce costs",
            "Set up AI provider failover for better reliability",
            "Monitor AI token usage and optimize prompts",
            "Consider load balancing across multiple providers"
        ])
        
        return recommendations

    def _save_output(self, data, output_file):
        """Save output to file if specified"""
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            self.stdout.write(
                self.style.SUCCESS(f'\nResults saved to {output_file}')
            ) 