"""
Django management command for performance monitoring
Monitors and reports on application performance metrics
"""

import json
import time
from django.core.management.base import BaseCommand, CommandError
from cv_analyzer.performance_monitoring import (
    get_performance_dashboard, get_performance_report, get_health_check,
    start_performance_monitoring, stop_performance_monitoring,
    performance_metrics, resource_monitor
)
from cv_analyzer.database_optimization import optimize_database_performance
from cv_analyzer.caching_optimization import optimize_cache_performance


class Command(BaseCommand):
    help = 'Monitor and optimize CV Analyzer performance'

    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            choices=['dashboard', 'report', 'health', 'start-monitor', 'stop-monitor', 'optimize'],
            default='dashboard',
            help='Performance monitoring action to perform'
        )
        parser.add_argument(
            '--output',
            type=str,
            help='Output file for results (JSON format)'
        )
        parser.add_argument(
            '--interval',
            type=int,
            default=60,
            help='Monitoring interval in seconds (for start-monitor action)'
        )
        parser.add_argument(
            '--continuous',
            action='store_true',
            help='Run continuous monitoring (press Ctrl+C to stop)'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        try:
            if action == 'dashboard':
                self._handle_dashboard(options)
            elif action == 'report':
                self._handle_report(options)
            elif action == 'health':
                self._handle_health_check(options)
            elif action == 'start-monitor':
                self._handle_start_monitoring(options)
            elif action == 'stop-monitor':
                self._handle_stop_monitoring(options)
            elif action == 'optimize':
                self._handle_optimization(options)
            
        except KeyboardInterrupt:
            self.stdout.write(self.style.WARNING('\nMonitoring stopped by user'))
        except Exception as e:
            raise CommandError(f'Performance monitoring failed: {str(e)}')

    def _handle_dashboard(self, options):
        """Handle performance dashboard display"""
        self.stdout.write(
            self.style.SUCCESS('CV Analyzer Performance Dashboard')
        )
        self.stdout.write('=' * 50)
        
        dashboard_data = get_performance_dashboard()
        
        # Display system metrics
        system_metrics = dashboard_data.get('system_metrics', {})
        self.stdout.write('\n📊 System Metrics:')
        self.stdout.write(f'  CPU Usage: {system_metrics.get("cpu_usage_percent", 0):.1f}%')
        self.stdout.write(f'  Memory Usage: {system_metrics.get("memory_usage_percent", 0):.1f}%')
        self.stdout.write(f'  Disk Usage: {system_metrics.get("disk_usage_percent", 0):.1f}%')
        
        # Display application metrics
        app_metrics = dashboard_data.get('application_metrics', {})
        self.stdout.write('\n🚀 Application Metrics:')
        
        response_times = app_metrics.get('response_times', {})
        if response_times.get('count', 0) > 0:
            self.stdout.write(f'  Avg Response Time: {response_times.get("avg", 0):.1f}ms')
            self.stdout.write(f'  Max Response Time: {response_times.get("max", 0):.1f}ms')
        else:
            self.stdout.write('  No response time data available')
        
        # Display database metrics
        db_metrics = dashboard_data.get('database_metrics', {})
        self.stdout.write('\n🗄️ Database Metrics:')
        
        query_times = db_metrics.get('query_time', {})
        if query_times.get('count', 0) > 0:
            self.stdout.write(f'  Avg Query Time: {query_times.get("avg", 0):.1f}ms')
            self.stdout.write(f'  Max Query Time: {query_times.get("max", 0):.1f}ms')
        else:
            self.stdout.write('  No query time data available')
        
        # Display cache metrics
        cache_metrics = dashboard_data.get('cache_metrics', {})
        self.stdout.write('\n💾 Cache Metrics:')
        
        hit_ratio = cache_metrics.get('hit_ratio', {})
        if hit_ratio.get('count', 0) > 0:
            self.stdout.write(f'  Cache Hit Ratio: {hit_ratio.get("avg", 0):.1f}%')
        else:
            self.stdout.write('  No cache data available')
        
        # Display alerts
        alerts = dashboard_data.get('alerts', [])
        if alerts:
            self.stdout.write('\n⚠️ Performance Alerts:')
            for alert in alerts:
                level_style = self.style.ERROR if alert['level'] == 'critical' else self.style.WARNING
                self.stdout.write(level_style(f'  {alert["level"].upper()}: {alert["message"]}'))
        else:
            self.stdout.write('\n✅ No performance alerts')
        
        self._save_output(dashboard_data, options['output'])

    def _handle_report(self, options):
        """Handle performance report generation"""
        self.stdout.write(
            self.style.SUCCESS('Generating Performance Report...')
        )
        
        report_data = get_performance_report()
        
        # Display function performance
        func_perf = report_data.get('function_performance', {})
        self.stdout.write('\n⚡ Function Performance:')
        if func_perf.get('count', 0) > 0:
            self.stdout.write(f'  Average Execution Time: {func_perf.get("avg", 0):.1f}ms')
            self.stdout.write(f'  Slowest Execution: {func_perf.get("max", 0):.1f}ms')
            self.stdout.write(f'  Threshold Violations: {func_perf.get("threshold_violations", 0)}')
        else:
            self.stdout.write('  No function performance data available')
        
        # Display database performance
        db_perf = report_data.get('database_performance', {})
        self.stdout.write('\n🗄️ Database Performance:')
        if db_perf.get('count', 0) > 0:
            self.stdout.write(f'  Average Query Time: {db_perf.get("avg", 0):.1f}ms')
            self.stdout.write(f'  Slowest Query: {db_perf.get("max", 0):.1f}ms')
            self.stdout.write(f'  Total Queries: {db_perf.get("count", 0)}')
        else:
            self.stdout.write('  No database performance data available')
        
        # Display recommendations
        recommendations = report_data.get('recommendations', [])
        if recommendations:
            self.stdout.write('\n💡 Recommendations:')
            for i, rec in enumerate(recommendations, 1):
                self.stdout.write(f'  {i}. {rec}')
        
        self._save_output(report_data, options['output'])

    def _handle_health_check(self, options):
        """Handle health check display"""
        self.stdout.write(
            self.style.SUCCESS('CV Analyzer Health Check')
        )
        self.stdout.write('=' * 40)
        
        health_data = get_health_check()
        
        # Overall status
        status = health_data.get('status', 'unknown')
        if status == 'healthy':
            style = self.style.SUCCESS
        elif status == 'degraded':
            style = self.style.WARNING
        else:
            style = self.style.ERROR
        
        self.stdout.write(style(f'\nOverall Status: {status.upper()}'))
        
        # Individual checks
        checks = health_data.get('checks', {})
        self.stdout.write('\n🔍 Health Checks:')
        
        for check_name, check_result in checks.items():
            check_status = check_result.get('status', 'unknown')
            if check_status == 'ok':
                self.stdout.write(self.style.SUCCESS(f'  ✅ {check_name}: OK'))
            elif check_status == 'warning':
                self.stdout.write(self.style.WARNING(f'  ⚠️ {check_name}: WARNING'))
            else:
                self.stdout.write(self.style.ERROR(f'  ❌ {check_name}: ERROR'))
                if 'error' in check_result:
                    self.stdout.write(f'     Error: {check_result["error"]}')
        
        # System resources
        system_resources = checks.get('system_resources', {})
        if system_resources:
            self.stdout.write('\n📊 System Resources:')
            self.stdout.write(f'  CPU Usage: {system_resources.get("cpu_usage", 0):.1f}%')
            self.stdout.write(f'  Memory Usage: {system_resources.get("memory_usage", 0):.1f}%')
        
        self._save_output(health_data, options['output'])

    def _handle_start_monitoring(self, options):
        """Handle starting performance monitoring"""
        interval = options['interval']
        continuous = options['continuous']
        
        self.stdout.write(
            self.style.SUCCESS(f'Starting performance monitoring (interval: {interval}s)')
        )
        
        start_performance_monitoring(interval)
        
        if continuous:
            self.stdout.write('Press Ctrl+C to stop monitoring...')
            try:
                while True:
                    time.sleep(5)
                    # Display current metrics every 30 seconds
                    if int(time.time()) % 30 == 0:
                        health_data = get_health_check()
                        status = health_data.get('status', 'unknown')
                        self.stdout.write(f'Status: {status} | {time.strftime("%H:%M:%S")}')
            except KeyboardInterrupt:
                self.stdout.write('\nStopping monitoring...')
                stop_performance_monitoring()
        else:
            self.stdout.write('Monitoring started in background')

    def _handle_stop_monitoring(self, options):
        """Handle stopping performance monitoring"""
        self.stdout.write('Stopping performance monitoring...')
        stop_performance_monitoring()
        self.stdout.write(self.style.SUCCESS('Performance monitoring stopped'))

    def _handle_optimization(self, options):
        """Handle performance optimization"""
        self.stdout.write(
            self.style.SUCCESS('Starting Performance Optimization...')
        )
        
        optimization_results = {
            'timestamp': time.time(),
            'database_optimization': {},
            'cache_optimization': {},
            'recommendations': []
        }
        
        # Database optimization
        self.stdout.write('\n🗄️ Database Optimization:')
        try:
            db_results = optimize_database_performance()
            optimization_results['database_optimization'] = db_results
            
            self.stdout.write('  ✅ Database performance analysis completed')
            
            # Display slow queries if any
            slow_queries = db_results.get('slow_queries', [])
            if slow_queries:
                self.stdout.write(f'  Found {len(slow_queries)} slow queries')
                for query in slow_queries[:3]:  # Show top 3
                    self.stdout.write(f'    • {query.get("mean_time_ms", 0):.1f}ms: {query.get("query", "")[:50]}...')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ Database optimization failed: {str(e)}'))
        
        # Cache optimization
        self.stdout.write('\n💾 Cache Optimization:')
        try:
            cache_results = optimize_cache_performance()
            optimization_results['cache_optimization'] = cache_results
            
            self.stdout.write('  ✅ Cache performance analysis completed')
            
            # Display cache recommendations
            cache_recommendations = cache_results.get('recommendations', [])
            for rec in cache_recommendations[:3]:  # Show top 3
                self.stdout.write(f'    • {rec}')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ Cache optimization failed: {str(e)}'))
        
        # General recommendations
        recommendations = [
            'Monitor performance metrics regularly',
            'Set up automated alerts for performance issues',
            'Consider implementing database connection pooling',
            'Review and optimize slow database queries',
            'Implement cache warming for critical data'
        ]
        
        optimization_results['recommendations'] = recommendations
        
        self.stdout.write('\n💡 Optimization Recommendations:')
        for i, rec in enumerate(recommendations, 1):
            self.stdout.write(f'  {i}. {rec}')
        
        self._save_output(optimization_results, options['output'])

    def _save_output(self, data, output_file):
        """Save output to file if specified"""
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            self.stdout.write(
                self.style.SUCCESS(f'\nResults saved to {output_file}')
            ) 