from django.core.management.base import BaseCommand
from cv_analyzer.models import CV, Analysis
from cv_analyzer.utils import process_cv_logic

class Command(BaseCommand):
    help = 'Process a CV'

    def add_arguments(self, parser):
        parser.add_argument('cv_id', type=int)

    def handle(self, *args, **options):
        cv_id = options['cv_id']
        cv = CV.objects.get(id=cv_id)
        process_cv_logic(cv)
        self.stdout.write(self.style.SUCCESS(f'Successfully processed CV {cv_id}'))
