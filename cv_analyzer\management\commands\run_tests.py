"""
Management command to run comprehensive test suite for CV Analyzer
Phase 6: Testing & Quality Assurance implementation
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.utils import timezone
from cv_analyzer.testing_framework import (
    TestingFrameworkManager, TestDataGenerator
)
from cv_analyzer.load_testing import LoadTestFramework, LoadTestReportGenerator
from cv_analyzer.security_monitoring import SecurityThreatDetector, VulnerabilityScanner

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Run comprehensive test suite for CV Analyzer (Phase 6: Testing & Quality Assurance)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-type',
            type=str,
            choices=['all', 'unit', 'integration', 'security', 'performance', 'load'],
            default='all',
            help='Type of tests to run'
        )
        
        parser.add_argument(
            '--coverage',
            action='store_true',
            help='Generate code coverage report'
        )
        
        parser.add_argument(
            '--load-users',
            type=int,
            default=100,
            help='Number of concurrent users for load testing'
        )
        
        parser.add_argument(
            '--load-duration',
            type=int,
            default=300,
            help='Duration of load testing in seconds'
        )
        
        parser.add_argument(
            '--report-format',
            type=str,
            choices=['json', 'html', 'both'],
            default='both',
            help='Report format'
        )
        
        parser.add_argument(
            '--output-dir',
            type=str,
            default='test_results',
            help='Output directory for test reports'
        )
        
        parser.add_argument(
            '--skip-cleanup',
            action='store_true',
            help='Skip test data cleanup'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Verbose output'
        )

    def handle(self, *args, **options):
        """Main command handler"""
        start_time = timezone.now()
        
        if options['verbose']:
            logging.basicConfig(level=logging.INFO)
            self.stdout.write(self.style.SUCCESS('🧪 Starting comprehensive test suite...'))
        
        try:
            # Setup test environment
            self._setup_test_environment(options)
            
            # Create test data
            if not options['skip_cleanup']:
                self._create_test_data()
            
            # Run tests based on type
            test_results = {}
            
            if options['test_type'] in ['all', 'unit']:
                test_results['unit'] = self._run_unit_tests(options)
            
            if options['test_type'] in ['all', 'integration']:
                test_results['integration'] = self._run_integration_tests(options)
            
            if options['test_type'] in ['all', 'security']:
                test_results['security'] = self._run_security_tests(options)
            
            if options['test_type'] in ['all', 'performance', 'load']:
                test_results['performance'] = self._run_performance_tests(options)
            
            # Generate comprehensive report
            self._generate_comprehensive_report(test_results, options)
            
            # Cleanup test data
            if not options['skip_cleanup']:
                self._cleanup_test_data()
            
            # Display summary
            self._display_test_summary(test_results, start_time)
            
        except Exception as e:
            logger.error(f"Test execution failed: {e}")
            raise CommandError(f'Test execution failed: {str(e)}')

    def _setup_test_environment(self, options):
        """Setup test environment and directories"""
        self.output_dir = options['output_dir']
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Create subdirectories
        for subdir in ['unit', 'integration', 'security', 'performance', 'reports']:
            os.makedirs(os.path.join(self.output_dir, subdir), exist_ok=True)
        
        if options['verbose']:
            self.stdout.write(f"📁 Test output directory: {self.output_dir}")

    def _create_test_data(self):
        """Create test data for testing"""
        self.stdout.write("🏗️  Creating test data...")
        
        try:
            # Create test users
            test_users = TestDataGenerator.create_test_users(10)
            self.stdout.write(f"   ✅ Created {len(test_users)} test users")
            
            # Create test companies
            test_companies = TestDataGenerator.create_test_companies(5)
            self.stdout.write(f"   ✅ Created {len(test_companies)} test companies")
            
            # Create test vacancies
            test_vacancies = TestDataGenerator.create_test_vacancies(test_companies, 10)
            self.stdout.write(f"   ✅ Created {len(test_vacancies)} test vacancies")
            
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"   ⚠️  Warning: Could not create full test data: {e}")
            )

    def _run_unit_tests(self, options):
        """Run unit tests"""
        self.stdout.write("🔬 Running unit tests...")
        
        try:
            framework = TestingFrameworkManager()
            
            # Run unit tests with coverage if requested
            if options['coverage']:
                unit_results = framework.run_unit_tests()
                coverage_results = framework.generate_coverage_report()
                
                results = {
                    'unit_tests': unit_results,
                    'coverage': coverage_results
                }
            else:
                results = {
                    'unit_tests': framework.run_unit_tests()
                }
            
            # Save results
            results_file = os.path.join(self.output_dir, 'unit', 'unit_test_results.json')
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            if results['unit_tests']['status'] == 'success':
                self.stdout.write("   ✅ Unit tests passed")
            else:
                self.stdout.write("   ❌ Unit tests failed")
            
            if options['coverage'] and 'coverage' in results:
                coverage_percent = results['coverage'].get('coverage_percent', 0)
                self.stdout.write(f"   📊 Code coverage: {coverage_percent:.1f}%")
            
            return results
            
        except Exception as e:
            error_msg = f"Unit tests failed: {e}"
            self.stdout.write(f"   ❌ {error_msg}")
            return {'error': error_msg}

    def _run_integration_tests(self, options):
        """Run integration tests"""
        self.stdout.write("🔗 Running integration tests...")
        
        try:
            framework = TestingFrameworkManager()
            results = framework.run_integration_tests()
            
            # Save results
            results_file = os.path.join(self.output_dir, 'integration', 'integration_test_results.json')
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            if results['status'] == 'passed':
                self.stdout.write(f"   ✅ Integration tests passed ({results['passed']}/{results['total']})")
            else:
                self.stdout.write(f"   ❌ Integration tests failed ({results['failed']} failures)")
            
            return results
            
        except Exception as e:
            error_msg = f"Integration tests failed: {e}"
            self.stdout.write(f"   ❌ {error_msg}")
            return {'error': error_msg}

    def _run_security_tests(self, options):
        """Run security tests"""
        self.stdout.write("🔒 Running security tests...")
        
        try:
            framework = TestingFrameworkManager()
            security_results = framework.run_security_tests()
            
            # Run additional security scans
            threat_detector = SecurityThreatDetector()
            vulnerability_scanner = VulnerabilityScanner()
            
            # Perform vulnerability scan
            vuln_results = vulnerability_scanner.scan_dependencies()
            
            combined_results = {
                'security_tests': security_results,
                'vulnerability_scan': vuln_results
            }
            
            # Save results
            results_file = os.path.join(self.output_dir, 'security', 'security_test_results.json')
            with open(results_file, 'w') as f:
                json.dump(combined_results, f, indent=2, default=str)
            
            if security_results['status'] == 'passed':
                self.stdout.write(f"   ✅ Security tests passed ({security_results['passed']}/{security_results['total']})")
            else:
                self.stdout.write(f"   ❌ Security tests failed ({security_results['failed']} failures)")
            
            # Report vulnerability scan results
            if vuln_results and vuln_results.get('vulnerabilities_found', 0) > 0:
                vuln_count = vuln_results['vulnerabilities_found']
                self.stdout.write(f"   ⚠️  Found {vuln_count} vulnerabilities")
            else:
                self.stdout.write("   ✅ No vulnerabilities found")
            
            return combined_results
            
        except Exception as e:
            error_msg = f"Security tests failed: {e}"
            self.stdout.write(f"   ❌ {error_msg}")
            return {'error': error_msg}

    def _run_performance_tests(self, options):
        """Run performance and load tests"""
        self.stdout.write("⚡ Running performance tests...")
        
        try:
            # Run basic performance tests first
            framework = TestingFrameworkManager()
            perf_results = framework.run_performance_tests()
            
            # Run load tests if requested
            if options['test_type'] in ['all', 'load', 'performance']:
                self.stdout.write(f"   🚀 Running load tests ({options['load_users']} users, {options['load_duration']}s)...")
                
                load_framework = LoadTestFramework()
                
                # Run async load tests
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    load_results = loop.run_until_complete(
                        load_framework.run_comprehensive_load_tests()
                    )
                finally:
                    loop.close()
                
                combined_results = {
                    'performance_tests': perf_results,
                    'load_tests': load_results
                }
                
                # Generate load test report
                report_generator = LoadTestReportGenerator(load_results)
                
                if options['report_format'] in ['html', 'both']:
                    html_report_path = os.path.join(self.output_dir, 'performance', 'load_test_report.html')
                    report_generator.save_report(html_report_path)
                    self.stdout.write(f"   📄 Load test HTML report: {html_report_path}")
                
            else:
                combined_results = {
                    'performance_tests': perf_results
                }
            
            # Save results
            results_file = os.path.join(self.output_dir, 'performance', 'performance_test_results.json')
            with open(results_file, 'w') as f:
                json.dump(combined_results, f, indent=2, default=str)
            
            if perf_results['status'] == 'passed':
                self.stdout.write(f"   ✅ Performance tests passed ({perf_results['passed']}/{perf_results['total']})")
            else:
                self.stdout.write(f"   ❌ Performance tests failed ({perf_results['failed']} failures)")
            
            # Report load test summary if available
            if 'load_tests' in combined_results:
                load_summary = combined_results['load_tests'].get('summary', {})
                if load_summary:
                    overall_status = load_summary.get('overall_status', 'unknown')
                    avg_response_time = load_summary.get('avg_response_time_ms', 0)
                    success_rate = load_summary.get('overall_success_rate', 0)
                    
                    if overall_status == 'passed':
                        self.stdout.write(f"   ✅ Load tests passed (avg: {avg_response_time:.0f}ms, success: {success_rate:.1f}%)")
                    else:
                        self.stdout.write(f"   ❌ Load tests failed (avg: {avg_response_time:.0f}ms, success: {success_rate:.1f}%)")
            
            return combined_results
            
        except Exception as e:
            error_msg = f"Performance tests failed: {e}"
            self.stdout.write(f"   ❌ {error_msg}")
            return {'error': error_msg}

    def _generate_comprehensive_report(self, test_results, options):
        """Generate comprehensive test report"""
        self.stdout.write("📊 Generating comprehensive report...")
        
        try:
            # Calculate overall metrics
            overall_summary = self._calculate_overall_summary(test_results)
            
            # Create comprehensive report data
            report_data = {
                'timestamp': timezone.now().isoformat(),
                'test_configuration': {
                    'test_type': options['test_type'],
                    'coverage_enabled': options['coverage'],
                    'load_users': options['load_users'],
                    'load_duration': options['load_duration']
                },
                'overall_summary': overall_summary,
                'detailed_results': test_results
            }
            
            # Save JSON report
            if options['report_format'] in ['json', 'both']:
                json_report_path = os.path.join(self.output_dir, 'reports', 'comprehensive_test_report.json')
                with open(json_report_path, 'w') as f:
                    json.dump(report_data, f, indent=2, default=str)
                self.stdout.write(f"   📄 JSON report: {json_report_path}")
            
            # Generate HTML report
            if options['report_format'] in ['html', 'both']:
                html_report_path = os.path.join(self.output_dir, 'reports', 'comprehensive_test_report.html')
                self._generate_html_report(report_data, html_report_path)
                self.stdout.write(f"   📄 HTML report: {html_report_path}")
            
        except Exception as e:
            self.stdout.write(f"   ⚠️  Warning: Could not generate comprehensive report: {e}")

    def _calculate_overall_summary(self, test_results):
        """Calculate overall test summary"""
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        overall_status = 'passed'
        
        # Count tests from each category
        for category, results in test_results.items():
            if isinstance(results, dict):
                if 'total' in results:
                    total_tests += results.get('total', 0)
                    passed_tests += results.get('passed', 0)
                    failed_tests += results.get('failed', 0)
                
                if results.get('status') in ['failed', 'error']:
                    overall_status = 'failed'
                
                # Check nested results
                for key, value in results.items():
                    if isinstance(value, dict):
                        if 'total' in value:
                            total_tests += value.get('total', 0)
                            passed_tests += value.get('passed', 0)
                            failed_tests += value.get('failed', 0)
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        return {
            'overall_status': overall_status,
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': success_rate,
            'categories_tested': list(test_results.keys())
        }

    def _generate_html_report(self, report_data, output_path):
        """Generate HTML comprehensive report"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>CV Analyzer - Comprehensive Test Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .header { text-align: center; border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
                .summary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
                .summary h2 { margin-top: 0; }
                .metric { display: inline-block; margin: 10px 20px 10px 0; padding: 10px; background: rgba(255,255,255,0.2); border-radius: 5px; }
                .metric-label { display: block; font-size: 12px; opacity: 0.8; }
                .metric-value { display: block; font-size: 24px; font-weight: bold; }
                .test-category { margin: 20px 0; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
                .category-header { background: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd; }
                .category-content { padding: 15px; }
                .status-passed { color: #28a745; }
                .status-failed { color: #dc3545; }
                .status-warning { color: #ffc107; }
                .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
                .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
                .recommendations { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
                .timestamp { text-align: right; color: #6c757d; font-size: 12px; margin-top: 20px; }
                table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🧪 CV Analyzer - Comprehensive Test Report</h1>
                    <p>Phase 6: Testing & Quality Assurance Results</p>
                </div>
                
                <div class="summary">
                    <h2>📊 Overall Summary</h2>
                    <div>
                        <div class="metric">
                            <span class="metric-label">Overall Status</span>
                            <span class="metric-value status-{overall_status}">{overall_status_display}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Total Tests</span>
                            <span class="metric-value">{total_tests:,}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Success Rate</span>
                            <span class="metric-value">{success_rate:.1f}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Categories</span>
                            <span class="metric-value">{categories_count}</span>
                        </div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {success_rate:.1f}%"></div>
                    </div>
                </div>
                
                {test_categories_html}
                
                <div class="timestamp">
                    Report generated on {timestamp}
                </div>
            </div>
        </body>
        </html>
        """
        
        overall_summary = report_data['overall_summary']
        
        # Generate test categories HTML
        test_categories_html = self._generate_test_categories_html(report_data['detailed_results'])
        
        # Format values
        overall_status = overall_summary.get('overall_status', 'unknown')
        overall_status_display = '✅ PASSED' if overall_status == 'passed' else '❌ FAILED'
        
        html_content = html_template.format(
            overall_status=overall_status,
            overall_status_display=overall_status_display,
            total_tests=overall_summary.get('total_tests', 0),
            success_rate=overall_summary.get('success_rate', 0),
            categories_count=len(overall_summary.get('categories_tested', [])),
            test_categories_html=test_categories_html,
            timestamp=report_data['timestamp']
        )
        
        with open(output_path, 'w') as f:
            f.write(html_content)

    def _generate_test_categories_html(self, detailed_results):
        """Generate HTML for test categories"""
        html_parts = []
        
        category_icons = {
            'unit': '🔬',
            'integration': '🔗',
            'security': '🔒',
            'performance': '⚡'
        }
        
        for category, results in detailed_results.items():
            if 'error' in results:
                status_class = 'status-failed'
                status_text = f"❌ ERROR: {results['error']}"
            elif isinstance(results, dict) and 'status' in results:
                if results['status'] in ['passed', 'success']:
                    status_class = 'status-passed'
                    status_text = '✅ PASSED'
                else:
                    status_class = 'status-failed'
                    status_text = '❌ FAILED'
            else:
                status_class = 'status-warning'
                status_text = '⚠️ UNKNOWN'
            
            icon = category_icons.get(category, '📋')
            category_title = category.replace('_', ' ').title()
            
            # Generate category details
            details_html = self._generate_category_details_html(results)
            
            html_parts.append(f'''
            <div class="test-category">
                <div class="category-header">
                    <h3>{icon} {category_title}</h3>
                    <span class="{status_class}">{status_text}</span>
                </div>
                <div class="category-content">
                    {details_html}
                </div>
            </div>
            ''')
        
        return '\n'.join(html_parts)

    def _generate_category_details_html(self, results):
        """Generate HTML for category details"""
        if 'error' in results:
            return f"<p>Error occurred during testing: {results['error']}</p>"
        
        details_parts = []
        
        # Handle different result structures
        if isinstance(results, dict):
            for key, value in results.items():
                if key in ['status', 'error']:
                    continue
                
                if isinstance(value, dict):
                    if 'total' in value:
                        total = value.get('total', 0)
                        passed = value.get('passed', 0)
                        failed = value.get('failed', 0)
                        success_rate = (passed / total * 100) if total > 0 else 0
                        
                        details_parts.append(f'''
                        <div>
                            <strong>{key.replace('_', ' ').title()}:</strong>
                            <span class="status-passed">{passed} passed</span> / 
                            <span class="status-failed">{failed} failed</span> / 
                            {total} total ({success_rate:.1f}% success rate)
                        </div>
                        ''')
                    elif 'coverage_percent' in value:
                        coverage = value.get('coverage_percent', 0)
                        target_met = value.get('target_met', False)
                        status_class = 'status-passed' if target_met else 'status-warning'
                        details_parts.append(f'''
                        <div>
                            <strong>Code Coverage:</strong>
                            <span class="{status_class}">{coverage:.1f}%</span>
                        </div>
                        ''')
                    elif 'overall_status' in value:
                        # Load test summary
                        summary = value
                        details_parts.append(f'''
                        <div>
                            <strong>Load Test Results:</strong><br/>
                            Status: <span class="status-{summary.get('overall_status', 'unknown')}">{summary.get('overall_status', 'unknown').upper()}</span><br/>
                            Total Requests: {summary.get('total_requests', 0):,}<br/>
                            Success Rate: {summary.get('overall_success_rate', 0):.1f}%<br/>
                            Avg Response Time: {summary.get('avg_response_time_ms', 0):.0f}ms<br/>
                            Avg RPS: {summary.get('avg_requests_per_second', 0):.1f}
                        </div>
                        ''')
        
        return '\n'.join(details_parts) if details_parts else "<p>No detailed results available.</p>"

    def _cleanup_test_data(self):
        """Clean up test data"""
        self.stdout.write("🧹 Cleaning up test data...")
        
        try:
            TestDataGenerator.cleanup_test_data()
            self.stdout.write("   ✅ Test data cleaned up")
        except Exception as e:
            self.stdout.write(f"   ⚠️  Warning: Could not clean up test data: {e}")

    def _display_test_summary(self, test_results, start_time):
        """Display final test summary"""
        end_time = timezone.now()
        duration = end_time - start_time
        
        self.stdout.write("\n" + "="*60)
        self.stdout.write("🎯 TEST EXECUTION SUMMARY")
        self.stdout.write("="*60)
        
        # Calculate overall metrics
        overall_summary = self._calculate_overall_summary(test_results)
        
        # Display summary
        overall_status = overall_summary.get('overall_status', 'unknown')
        if overall_status == 'passed':
            self.stdout.write(self.style.SUCCESS(f"✅ OVERALL STATUS: PASSED"))
        else:
            self.stdout.write(self.style.ERROR(f"❌ OVERALL STATUS: FAILED"))
        
        self.stdout.write(f"📊 Total Tests: {overall_summary.get('total_tests', 0):,}")
        self.stdout.write(f"✅ Passed: {overall_summary.get('passed_tests', 0):,}")
        self.stdout.write(f"❌ Failed: {overall_summary.get('failed_tests', 0):,}")
        self.stdout.write(f"📈 Success Rate: {overall_summary.get('success_rate', 0):.1f}%")
        self.stdout.write(f"⏱️  Duration: {duration}")
        self.stdout.write(f"📁 Reports: {self.output_dir}")
        
        # Display category breakdown
        self.stdout.write("\n📋 Category Breakdown:")
        for category in overall_summary.get('categories_tested', []):
            if category in test_results:
                result = test_results[category]
                if 'error' in result:
                    self.stdout.write(f"   {category}: ❌ ERROR")
                elif isinstance(result, dict) and 'status' in result:
                    status = result['status']
                    if status in ['passed', 'success']:
                        self.stdout.write(f"   {category}: ✅ PASSED")
                    else:
                        self.stdout.write(f"   {category}: ❌ FAILED")
                else:
                    self.stdout.write(f"   {category}: ⚠️ UNKNOWN")
        
        self.stdout.write("="*60)
        
        if overall_status == 'passed':
            self.stdout.write(
                self.style.SUCCESS("🎉 All tests completed successfully! System is ready for production.")
            )
        else:
            self.stdout.write(
                self.style.ERROR("⚠️  Some tests failed. Please review the reports and fix issues before production deployment.")
            ) 