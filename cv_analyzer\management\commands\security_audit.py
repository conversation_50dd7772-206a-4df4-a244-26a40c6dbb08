"""
Django management command for running security audits
"""

import json
from django.core.management.base import BaseCommand, CommandError
from cv_analyzer.server_hardening import perform_security_audit, monitor_security, check_system_hardening


class Command(BaseCommand):
    help = 'Perform comprehensive security audit of the CV Analyzer system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['audit', 'monitor', 'hardening', 'all'],
            default='audit',
            help='Type of security check to perform'
        )
        parser.add_argument(
            '--output',
            type=str,
            help='Output file for results (JSON format)'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Starting CV Analyzer Security Audit...')
        )

        results = {}

        try:
            if options['type'] in ['audit', 'all']:
                self.stdout.write('Running security audit...')
                results['security_audit'] = perform_security_audit()
                self._display_audit_results(results['security_audit'], options['verbose'])

            if options['type'] in ['monitor', 'all']:
                self.stdout.write('Running security monitoring...')
                results['security_monitoring'] = monitor_security()
                self._display_monitoring_results(results['security_monitoring'], options['verbose'])

            if options['type'] in ['hardening', 'all']:
                self.stdout.write('Checking system hardening...')
                results['system_hardening'] = check_system_hardening()
                self._display_hardening_results(results['system_hardening'], options['verbose'])

            # Save results to file if requested
            if options['output']:
                with open(options['output'], 'w') as f:
                    json.dump(results, f, indent=2, default=str)
                self.stdout.write(
                    self.style.SUCCESS(f'Results saved to {options["output"]}')
                )

            self.stdout.write(
                self.style.SUCCESS('Security audit completed successfully!')
            )

        except Exception as e:
            raise CommandError(f'Security audit failed: {str(e)}')

    def _display_audit_results(self, audit_results, verbose=False):
        """Display security audit results"""
        score = audit_results.get('overall_score', 0)
        
        if score >= 80:
            style = self.style.SUCCESS
        elif score >= 60:
            style = self.style.WARNING
        else:
            style = self.style.ERROR

        self.stdout.write(style(f'Overall Security Score: {score}/100'))

        # Display issues
        issues = audit_results.get('issues_found', [])
        if issues:
            self.stdout.write(self.style.ERROR('CRITICAL ISSUES FOUND:'))
            for issue in issues:
                self.stdout.write(f'  • {issue}')

        # Display warnings
        warnings = audit_results.get('warnings', [])
        if warnings:
            self.stdout.write(self.style.WARNING('WARNINGS:'))
            for warning in warnings:
                self.stdout.write(f'  • {warning}')

        # Display detailed results if verbose
        if verbose:
            self.stdout.write('\nDetailed Security Checks:')
            for check_name, check_result in audit_results.get('security_checks', {}).items():
                if isinstance(check_result, dict):
                    score = check_result.get('score', 0)
                    self.stdout.write(f'  {check_name.title()}: {score}/100')

    def _display_monitoring_results(self, monitor_results, verbose=False):
        """Display security monitoring results"""
        threat_detection = monitor_results.get('threat_detection', {})
        alerts = threat_detection.get('alerts', [])
        
        if alerts:
            self.stdout.write(self.style.ERROR('SECURITY ALERTS:'))
            for alert in alerts:
                self.stdout.write(f'  • {alert}')
        else:
            self.stdout.write(self.style.SUCCESS('No security alerts detected'))

        if verbose:
            resource_monitoring = monitor_results.get('resource_monitoring', {})
            self.stdout.write('\nResource Usage:')
            self.stdout.write(f'  CPU: {resource_monitoring.get("cpu_usage", 0):.1f}%')
            self.stdout.write(f'  Memory: {resource_monitoring.get("memory_usage", 0):.1f}%')

    def _display_hardening_results(self, hardening_results, verbose=False):
        """Display system hardening results"""
        firewall_status = hardening_results.get('firewall_status', {})
        status = firewall_status.get('status', 'unknown')
        
        if status == 'active':
            self.stdout.write(self.style.SUCCESS('Firewall: Active'))
        else:
            self.stdout.write(self.style.WARNING(f'Firewall: {status.title()}'))

        if verbose:
            recommendations = hardening_results.get('firewall_recommendations', [])
            if recommendations:
                self.stdout.write('\nFirewall Recommendations:')
                for rec in recommendations[:5]:  # Show first 5
                    self.stdout.write(f'  {rec}') 