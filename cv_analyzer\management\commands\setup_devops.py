"""
Management command to setup DevOps infrastructure
Sets up monitoring, security, CI/CD pipelines, and deployment configurations
"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
import os
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Setup DevOps infrastructure for CV Analyzer'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--pipeline',
            type=str,
            default='github_actions',
            choices=['github_actions', 'gitlab_ci', 'jenkins', 'azure_devops'],
            help='CI/CD pipeline type to setup'
        )
        
        parser.add_argument(
            '--environment',
            type=str,
            default='development',
            choices=['development', 'staging', 'production'],
            help='Target environment'
        )
        
        parser.add_argument(
            '--enable-monitoring',
            action='store_true',
            help='Enable monitoring and observability'
        )
        
        parser.add_argument(
            '--enable-security',
            action='store_true',
            help='Enable security monitoring'
        )
        
        parser.add_argument(
            '--create-docker',
            action='store_true',
            help='Create Docker configuration files'
        )
        
        parser.add_argument(
            '--create-k8s',
            action='store_true',
            help='Create Kubernetes configuration files'
        )
        
        parser.add_argument(
            '--run-security-scan',
            action='store_true',
            help='Run initial security vulnerability scan'
        )
        
        parser.add_argument(
            '--all',
            action='store_true',
            help='Setup all DevOps components'
        )
    
    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Starting CV Analyzer DevOps setup...')
        )
        
        pipeline_type = options['pipeline']
        environment = options['environment']
        
        setup_all = options['all']
        
        try:
            # Setup CI/CD Pipeline
            if setup_all or options.get('pipeline'):
                self.setup_cicd_pipeline(pipeline_type)
            
            # Setup Docker configuration
            if setup_all or options['create_docker']:
                self.setup_docker_config()
            
            # Setup Kubernetes configuration
            if setup_all or options['create_k8s']:
                self.setup_kubernetes_config()
            
            # Setup monitoring
            if setup_all or options['enable_monitoring']:
                self.setup_monitoring(environment)
            
            # Setup security monitoring
            if setup_all or options['enable_security']:
                self.setup_security_monitoring()
            
            # Run security scan
            if setup_all or options['run_security_scan']:
                self.run_security_scan()
            
            # Create deployment scripts
            if setup_all:
                self.create_deployment_scripts()
            
            self.stdout.write(
                self.style.SUCCESS('DevOps setup completed successfully!')
            )
            
            # Print next steps
            self.print_next_steps(pipeline_type, environment)
            
        except Exception as e:
            raise CommandError(f'DevOps setup failed: {str(e)}')
    
    def setup_cicd_pipeline(self, pipeline_type):
        """Setup CI/CD pipeline configuration"""
        self.stdout.write(f'Setting up {pipeline_type} CI/CD pipeline...')
        
        try:
            from cv_analyzer.devops_system import cicd_manager
            
            success = cicd_manager.create_pipeline_files(pipeline_type)
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f'✓ {pipeline_type} pipeline configuration created')
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f'✗ Failed to create {pipeline_type} pipeline configuration')
                )
        except ImportError:
            self.stdout.write(
                self.style.WARNING('DevOps system not available, creating basic pipeline files...')
            )
            self.create_basic_pipeline_files(pipeline_type)
    
    def create_basic_pipeline_files(self, pipeline_type):
        """Create basic pipeline files if DevOps system is not available"""
        if pipeline_type == 'github_actions':
            # Create .github/workflows directory
            workflows_dir = os.path.join(settings.BASE_DIR, '.github', 'workflows')
            os.makedirs(workflows_dir, exist_ok=True)
            
            # Create basic workflow file
            workflow_content = """name: CV Analyzer CI/CD
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
      - name: Run tests
        run: |
          python manage.py test
"""
            
            workflow_file = os.path.join(workflows_dir, 'ci-cd.yml')
            with open(workflow_file, 'w') as f:
                f.write(workflow_content)
            
            self.stdout.write(
                self.style.SUCCESS('✓ Basic GitHub Actions workflow created')
            )
    
    def setup_docker_config(self):
        """Setup Docker configuration"""
        self.stdout.write('Setting up Docker configuration...')
        
        try:
            from cv_analyzer.devops_system import docker_manager
            
            success = docker_manager.create_docker_files()
            if success:
                self.stdout.write(
                    self.style.SUCCESS('✓ Docker configuration files created')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('✗ Failed to create Docker configuration')
                )
        except ImportError:
            self.stdout.write(
                self.style.WARNING('Docker manager not available')
            )
    
    def setup_kubernetes_config(self):
        """Setup Kubernetes configuration"""
        self.stdout.write('Setting up Kubernetes configuration...')
        
        try:
            from cv_analyzer.devops_system import infrastructure_manager
            
            success = infrastructure_manager.create_kubernetes_files()
            if success:
                self.stdout.write(
                    self.style.SUCCESS('✓ Kubernetes configuration files created')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('✗ Failed to create Kubernetes configuration')
                )
        except ImportError:
            self.stdout.write(
                self.style.WARNING('Infrastructure manager not available')
            )
    
    def setup_monitoring(self, environment):
        """Setup monitoring and observability"""
        self.stdout.write('Setting up monitoring and observability...')
        
        try:
            from cv_analyzer.monitoring_system import start_monitoring, run_health_checks
            
            # Start monitoring system
            start_monitoring()
            
            # Run initial health checks
            health_status = run_health_checks()
            
            overall_status = health_status.get('overall_status', 'unknown')
            if overall_status == 'healthy':
                self.stdout.write(
                    self.style.SUCCESS('✓ Monitoring system started - All services healthy')
                )
            elif overall_status == 'degraded':
                self.stdout.write(
                    self.style.WARNING('⚠ Monitoring system started - Some services degraded')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('✗ Monitoring system started - Some services unhealthy')
                )
            
            # Display health check summary
            for check_name, check_result in health_status.get('checks', {}).items():
                status_icon = '✓' if check_result['status'] == 'healthy' else '⚠' if check_result['status'] == 'degraded' else '✗'
                self.stdout.write(f"  {status_icon} {check_name}: {check_result['message']}")
                
        except ImportError:
            self.stdout.write(
                self.style.WARNING('Monitoring system not available')
            )
    
    def setup_security_monitoring(self):
        """Setup security monitoring"""
        self.stdout.write('Setting up security monitoring...')
        
        try:
            from cv_analyzer.security_monitoring import threat_detector, audit_logger
            
            # Test security systems
            test_passed = True
            
            # Test threat detector
            if hasattr(threat_detector, 'threat_patterns'):
                pattern_count = sum(len(patterns) for patterns in threat_detector.threat_patterns.values())
                self.stdout.write(f"  ✓ Threat detection patterns loaded: {pattern_count}")
            else:
                test_passed = False
            
            # Test audit logger
            if hasattr(audit_logger, 'log_types'):
                logger_count = len(audit_logger.log_types)
                self.stdout.write(f"  ✓ Audit loggers configured: {logger_count}")
            else:
                test_passed = False
            
            if test_passed:
                self.stdout.write(
                    self.style.SUCCESS('✓ Security monitoring system configured')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('✗ Security monitoring system configuration failed')
                )
                
        except ImportError:
            self.stdout.write(
                self.style.WARNING('Security monitoring system not available')
            )
    
    def run_security_scan(self):
        """Run initial security vulnerability scan"""
        self.stdout.write('Running security vulnerability scan...')
        
        try:
            from cv_analyzer.security_monitoring import vulnerability_scanner
            
            scan_results = vulnerability_scanner.run_vulnerability_scan()
            
            # Display scan summary
            risk_summary = scan_results.get('risk_summary', {})
            total_vulns = sum(risk_summary.values())
            
            self.stdout.write(f"  Scan ID: {scan_results.get('scan_id', 'unknown')}")
            self.stdout.write(f"  Total vulnerabilities found: {total_vulns}")
            
            for risk_level, count in risk_summary.items():
                if count > 0:
                    color = self.style.ERROR if risk_level in ['critical', 'high'] else self.style.WARNING if risk_level == 'medium' else self.style.SUCCESS
                    self.stdout.write(f"    {risk_level.title()}: {count}", color)
            
            # Display top vulnerabilities
            vulnerabilities = scan_results.get('vulnerabilities', [])
            if vulnerabilities:
                self.stdout.write("  Top vulnerabilities:")
                for vuln in vulnerabilities[:5]:  # Show top 5
                    risk_level = vuln.get('risk_level', 'unknown')
                    title = vuln.get('title', 'Unknown vulnerability')
                    self.stdout.write(f"    - [{risk_level.upper()}] {title}")
            
            # Display recommendations
            recommendations = scan_results.get('recommendations', [])
            if recommendations:
                self.stdout.write("  Security recommendations:")
                for rec in recommendations[:3]:  # Show top 3
                    self.stdout.write(f"    • {rec}")
            
            if total_vulns == 0:
                self.stdout.write(
                    self.style.SUCCESS('✓ No vulnerabilities found in security scan')
                )
            elif risk_summary.get('critical', 0) > 0 or risk_summary.get('high', 0) > 0:
                self.stdout.write(
                    self.style.ERROR('✗ Critical or high-risk vulnerabilities found - immediate action required')
                )
            else:
                self.stdout.write(
                    self.style.WARNING('⚠ Some vulnerabilities found - review and address as needed')
                )
                
        except ImportError:
            self.stdout.write(
                self.style.WARNING('Security scanner not available')
            )
    
    def create_deployment_scripts(self):
        """Create deployment automation scripts"""
        self.stdout.write('Creating deployment scripts...')
        
        scripts_dir = os.path.join(settings.BASE_DIR, 'scripts')
        os.makedirs(scripts_dir, exist_ok=True)
        
        # Create basic deployment script
        deploy_script_content = """#!/bin/bash
set -e

echo "Starting CV Analyzer deployment..."

# Run database migrations
echo "Running database migrations..."
python manage.py migrate

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput

# Run health checks
echo "Running health checks..."
python manage.py check

echo "Deployment completed successfully!"
"""
        
        deploy_script = os.path.join(scripts_dir, 'deploy.sh')
        with open(deploy_script, 'w') as f:
            f.write(deploy_script_content)
        
        # Make script executable
        os.chmod(deploy_script, 0o755)
        
        self.stdout.write(
            self.style.SUCCESS('✓ Deployment scripts created')
        )
    
    def print_next_steps(self, pipeline_type, environment):
        """Print next steps for the user"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS('DevOps Setup Complete!'))
        self.stdout.write('='*60)
        
        self.stdout.write('\nNext Steps:')
        
        # CI/CD specific steps
        if pipeline_type == 'github_actions':
            self.stdout.write('1. Push your code to GitHub to trigger the workflow')
            self.stdout.write('2. Configure GitHub secrets for deployment')
            self.stdout.write('3. Review and customize .github/workflows/ci-cd.yml')
        elif pipeline_type == 'gitlab_ci':
            self.stdout.write('1. Push your code to GitLab to trigger the pipeline')
            self.stdout.write('2. Configure GitLab CI/CD variables')
            self.stdout.write('3. Review and customize .gitlab-ci.yml')
        
        # Environment specific steps
        self.stdout.write(f'4. Configure environment variables for {environment}')
        self.stdout.write('5. Set up monitoring dashboards')
        self.stdout.write('6. Configure alerting and notifications')
        self.stdout.write('7. Run security vulnerability scan regularly')
        self.stdout.write('8. Set up backup and disaster recovery procedures')
        
        # Monitoring and security
        self.stdout.write('\nMonitoring URLs (when deployed):')
        self.stdout.write('• Health Check: /health/')
        self.stdout.write('• Metrics: /metrics/')
        self.stdout.write('• Admin: /admin/')
        
        self.stdout.write('\nSecurity Notes:')
        self.stdout.write('• Review and address any vulnerabilities found in the security scan')
        self.stdout.write('• Configure proper firewall rules and network security')
        self.stdout.write('• Set up regular security audits and penetration testing')
        self.stdout.write('• Enable comprehensive audit logging')
        
        self.stdout.write('\n' + '='*60) 