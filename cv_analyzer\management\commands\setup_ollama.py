from django.core.management.base import BaseCommand
from django.core.management import CommandError
from cv_analyzer.models import AIAPIConfig
import requests


class Command(BaseCommand):
    help = 'Setup Ollama AI configuration for CV Analyzer'

    def add_arguments(self, parser):
        parser.add_argument(
            '--server',
            type=str,
            default='*************:11434',
            help='Ollama server URL or IP:port (default: *************:11434)'
        )
        parser.add_argument(
            '--model',
            type=str,
            default='qwen-qwq-32b',
            help='Model name to use (default: qwen-qwq-32b)'
        )
        parser.add_argument(
            '--priority',
            type=int,
            default=1,
            help='Priority for this configuration (default: 1)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update existing Ollama configuration'
        )

    def handle(self, *args, **options):
        server = options['server']
        model = options['model']
        priority = options['priority']
        force = options['force']

        # Format server URL properly
        if not server.startswith('http'):
            if ':' not in server:
                server = f"{server}:11434"
            server = f"http://{server}"

        self.stdout.write(f'🔍 Testing connection to Ollama server: {server}')

        try:
            # Test connection
            response = requests.get(f"{server}/api/tags", timeout=10)
            if response.status_code != 200:
                raise CommandError(f'❌ Cannot connect to Ollama server at {server}')

            data = response.json()
            available_models = [m['name'] for m in data.get('models', [])]
            
            self.stdout.write(f'✅ Connected! Found {len(available_models)} models')
            
            if model not in available_models:
                self.stdout.write(f'⚠️  Warning: Model "{model}" not found in available models')
                self.stdout.write(f'Available models: {", ".join(available_models)}')
                if available_models:
                    model = available_models[0]
                    self.stdout.write(f'🔄 Using first available model: {model}')
                else:
                    raise CommandError('❌ No models available on the server')

        except requests.exceptions.ConnectionError:
            raise CommandError(f'❌ Cannot connect to Ollama server at {server}. Make sure it\'s running!')
        except Exception as e:
            raise CommandError(f'❌ Error testing connection: {str(e)}')

        # Check if Ollama configuration already exists
        existing_config = AIAPIConfig.objects.filter(provider='ollama').first()
        
        if existing_config and not force:
            self.stdout.write(f'⚠️  Ollama configuration already exists (ID: {existing_config.id})')
            self.stdout.write('Use --force to update it, or update manually in Django Admin')
            return

        if existing_config and force:
            self.stdout.write(f'🔄 Updating existing Ollama configuration (ID: {existing_config.id})')
            config = existing_config
        else:
            self.stdout.write('➕ Creating new Ollama configuration')
            config = AIAPIConfig()

        # Configure the AI API
        config.provider = 'ollama'
        config.api_key = server
        config.model_name = model
        config.is_active = True
        config.priority = priority
        config.max_tokens = 2000
        config.temperature = 0.3

        # Deactivate other configurations if this has priority 1
        if priority == 1:
            AIAPIConfig.objects.exclude(id=config.id if config.id else None).update(is_active=False)
            self.stdout.write('🔄 Deactivated other AI configurations (priority 1)')

        config.save()

        self.stdout.write('✅ Ollama configuration saved successfully!')
        self.stdout.write('')
        self.stdout.write('📋 Configuration Summary:')
        self.stdout.write(f'   • Provider: ollama')
        self.stdout.write(f'   • Server: {server}')
        self.stdout.write(f'   • Model: {model}')
        self.stdout.write(f'   • Priority: {priority}')
        self.stdout.write(f'   • Active: ✅')
        self.stdout.write('')
        self.stdout.write('🎉 CV Analyzer is now ready to use Ollama!')
        self.stdout.write('You can test it by analyzing a CV in the web interface.') 