"""
Management command to test and implement custom scoring functionality
"""

from django.core.management.base import BaseCommand
from django.db import transaction
import json
import re
from typing import Dict, List, Optional, Tuple, Any
from difflib import SequenceMatcher
import logging

from cv_analyzer.models import (
    CVAnalysis, 
    Vacancy, 
    VacancyScoringRules, 
    CustomScoringResult,
    ScoringTemplate,
    Company
)

logger = logging.getLogger(__name__)


class CustomScoringEngine:
    """
    Custom scoring engine for CV analysis with configurable weighted scoring.
    """
    
    def __init__(self):
        self.similarity_threshold = 0.7
        self.debug_mode = False
        
    def calculate_custom_score(self, cv_analysis: CVAnalysis, vacancy: Vacancy) -> CustomScoringResult:
        """Calculate custom weighted score for a CV against a vacancy."""
        try:
            # Get or create scoring rules
            scoring_rules, created = VacancyScoringRules.objects.get_or_create(
                vacancy=vacancy,
                defaults={
                    'education_weight': 25.0,
                    'experience_weight': 35.0,
                    'skills_weight': 30.0,
                    'responsibilities_weight': 10.0,
                    'education_requirements': self._get_default_education_requirements(),
                    'experience_requirements': self._get_default_experience_requirements(),
                    'skills_requirements': self._get_default_skills_requirements(),
                    'responsibilities_requirements': self._get_default_responsibilities_requirements(),
                }
            )
            
            if created:
                logger.info(f"Created default scoring rules for vacancy {vacancy.id}")
            
            # Calculate component scores
            education_score = self._calculate_education_score(cv_analysis, scoring_rules)
            experience_score = self._calculate_experience_score(cv_analysis, scoring_rules)
            skills_score = self._calculate_skills_score(cv_analysis, scoring_rules)
            responsibilities_score = self._calculate_responsibilities_score(cv_analysis, scoring_rules)
            
            # Calculate weighted scores
            weighted_education = education_score * (scoring_rules.education_weight / 100.0)
            weighted_experience = experience_score * (scoring_rules.experience_weight / 100.0)
            weighted_skills = skills_score * (scoring_rules.skills_weight / 100.0)
            weighted_responsibilities = responsibilities_score * (scoring_rules.responsibilities_weight / 100.0)
            
            # Calculate final score
            final_score = weighted_education + weighted_experience + weighted_skills + weighted_responsibilities
            
            # Check requirements
            meets_requirements = self._check_minimum_requirements(
                education_score, experience_score, skills_score, final_score, scoring_rules
            )
            
            # Generate recommendation
            recommendation = self._generate_recommendation(final_score, meets_requirements)
            
            # AI vs custom difference
            ai_vs_custom_difference = 0.0
            if hasattr(cv_analysis, 'overall_score') and cv_analysis.overall_score:
                ai_vs_custom_difference = final_score - cv_analysis.overall_score
            
            # Create score breakdown
            score_breakdown = self._create_score_breakdown(
                education_score, experience_score, skills_score, responsibilities_score,
                weighted_education, weighted_experience, weighted_skills, weighted_responsibilities,
                scoring_rules, cv_analysis, vacancy
            )
            
            # Create or update result
            result, created = CustomScoringResult.objects.update_or_create(
                cv_analysis=cv_analysis,
                vacancy=vacancy,
                defaults={
                    'education_score': education_score,
                    'experience_score': experience_score,
                    'skills_score': skills_score,
                    'responsibilities_score': responsibilities_score,
                    'weighted_education_score': weighted_education,
                    'weighted_experience_score': weighted_experience,
                    'weighted_skills_score': weighted_skills,
                    'weighted_responsibilities_score': weighted_responsibilities,
                    'final_custom_score': final_score,
                    'score_breakdown': score_breakdown,
                    'meets_minimum_requirements': meets_requirements,
                    'custom_recommendation': recommendation,
                    'ai_vs_custom_difference': ai_vs_custom_difference,
                }
            )
            
            logger.info(f"Custom score calculated for CV {cv_analysis.id} vs Vacancy {vacancy.id}: {final_score:.1f}%")
            return result
            
        except Exception as e:
            logger.error(f"Error calculating custom score: {str(e)}")
            raise
    
    def _calculate_education_score(self, cv_analysis: CVAnalysis, scoring_rules: VacancyScoringRules) -> float:
        """Calculate education component score."""
        try:
            education_reqs = scoring_rules.education_requirements or self._get_default_education_requirements()
            candidate_education = (cv_analysis.education_level or '').lower()
            
            score = education_reqs.get('default_score', 30.0)
            
            for level, config in education_reqs.items():
                if level == 'default_score':
                    continue
                    
                if isinstance(config, dict) and 'keywords' in config:
                    keywords = config['keywords']
                    if any(keyword in candidate_education for keyword in keywords):
                        score = max(score, config.get('score', 0.0))
            
            return min(100.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"Error calculating education score: {str(e)}")
            return 30.0
    
    def _calculate_experience_score(self, cv_analysis: CVAnalysis, scoring_rules: VacancyScoringRules) -> float:
        """Calculate experience component score."""
        try:
            experience_reqs = scoring_rules.experience_requirements or self._get_default_experience_requirements()
            years_experience = cv_analysis.years_of_experience or 0
            
            score = experience_reqs.get('default_score', 30.0)
            ranges = experience_reqs.get('ranges', [])
            
            for range_config in ranges:
                min_years = range_config.get('min', 0)
                max_years = range_config.get('max', 999)
                
                if min_years <= years_experience <= max_years:
                    score = max(score, range_config.get('score', 0.0))
            
            return min(100.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"Error calculating experience score: {str(e)}")
            return 30.0
    
    def _calculate_skills_score(self, cv_analysis: CVAnalysis, scoring_rules: VacancyScoringRules) -> float:
        """Calculate skills component score."""
        try:
            skills_reqs = scoring_rules.skills_requirements or self._get_default_skills_requirements()
            candidate_skills = self._extract_skills(cv_analysis)
            
            if not candidate_skills:
                return skills_reqs.get('default_score', 50.0)
            
            required_skills = skills_reqs.get('required_skills', [])
            preferred_skills = skills_reqs.get('preferred_skills', [])
            matching_threshold = skills_reqs.get('skill_matching_threshold', 0.7)
            
            required_matches = sum(1 for skill in required_skills 
                                 if self._skill_matches(skill, candidate_skills, matching_threshold))
            preferred_matches = sum(1 for skill in preferred_skills 
                                  if self._skill_matches(skill, candidate_skills, matching_threshold))
            
            score = skills_reqs.get('default_score', 50.0)
            
            if required_skills:
                required_percentage = (required_matches / len(required_skills)) * 100
                score = max(score, required_percentage * 0.8)
            
            if preferred_skills:
                preferred_percentage = (preferred_matches / len(preferred_skills)) * 100
                score = max(score, score + (preferred_percentage * 0.2))
            
            return min(100.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"Error calculating skills score: {str(e)}")
            return 50.0
    
    def _calculate_responsibilities_score(self, cv_analysis: CVAnalysis, scoring_rules: VacancyScoringRules) -> float:
        """Calculate responsibilities component score."""
        try:
            resp_reqs = scoring_rules.responsibilities_requirements or self._get_default_responsibilities_requirements()
            candidate_responsibilities = self._extract_responsibilities(cv_analysis)
            
            if not candidate_responsibilities:
                return resp_reqs.get('default_score', 50.0)
            
            key_responsibilities = resp_reqs.get('key_responsibilities', [])
            matching_threshold = resp_reqs.get('responsibility_matching_threshold', 0.6)
            
            if not key_responsibilities:
                return resp_reqs.get('default_score', 50.0)
            
            matches = sum(1 for key_resp in key_responsibilities
                         if self._responsibility_matches(key_resp, candidate_responsibilities, matching_threshold))
            
            match_percentage = (matches / len(key_responsibilities)) * 100
            score = max(resp_reqs.get('default_score', 50.0), match_percentage)
            
            return min(100.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"Error calculating responsibilities score: {str(e)}")
            return 50.0
    
    def _extract_skills(self, cv_analysis: CVAnalysis) -> List[str]:
        """Extract skills from CV analysis."""
        skills = []
        if cv_analysis.skills:
            skills.extend(self._parse_skills_text(cv_analysis.skills))
        return [skill.lower().strip() for skill in skills if skill.strip()]
    
    def _extract_responsibilities(self, cv_analysis: CVAnalysis) -> List[str]:
        """Extract responsibilities from CV analysis."""
        responsibilities = []
        # Add logic to extract from structured data when available
        return [resp.lower().strip() for resp in responsibilities if resp.strip()]
    
    def _parse_skills_text(self, skills_text: str) -> List[str]:
        """Parse skills from text."""
        if not skills_text:
            return []
        
        separators = [',', ';', '|', '\n', '•', '-']
        skills = [skills_text]
        
        for separator in separators:
            new_skills = []
            for skill in skills:
                new_skills.extend(skill.split(separator))
            skills = new_skills
        
        return [skill.strip() for skill in skills if skill.strip()]
    
    def _skill_matches(self, required_skill: str, candidate_skills: List[str], threshold: float) -> bool:
        """Check if skill matches."""
        required_skill_lower = required_skill.lower()
        
        for candidate_skill in candidate_skills:
            if required_skill_lower == candidate_skill:
                return True
            if required_skill_lower in candidate_skill or candidate_skill in required_skill_lower:
                return True
            similarity = SequenceMatcher(None, required_skill_lower, candidate_skill).ratio()
            if similarity >= threshold:
                return True
        return False
    
    def _responsibility_matches(self, key_resp: str, candidate_responsibilities: List[str], threshold: float) -> bool:
        """Check if responsibility matches."""
        key_resp_lower = key_resp.lower()
        
        for candidate_resp in candidate_responsibilities:
            if key_resp_lower in candidate_resp or candidate_resp in key_resp_lower:
                return True
            similarity = SequenceMatcher(None, key_resp_lower, candidate_resp).ratio()
            if similarity >= threshold:
                return True
        return False
    
    def _check_minimum_requirements(self, education_score: float, experience_score: float, 
                                   skills_score: float, final_score: float, 
                                   scoring_rules: VacancyScoringRules) -> bool:
        """Check minimum requirements."""
        return (education_score >= scoring_rules.minimum_education_score and
                experience_score >= scoring_rules.minimum_experience_score and
                skills_score >= scoring_rules.minimum_skills_score and
                final_score >= scoring_rules.minimum_overall_score)
    
    def _generate_recommendation(self, final_score: float, meets_requirements: bool) -> str:
        """Generate recommendation."""
        if not meets_requirements:
            return 'not_recommended'
        if final_score >= 85:
            return 'highly_recommended'
        elif final_score >= 70:
            return 'recommended'
        else:
            return 'consider'
    
    def _create_score_breakdown(self, education_score: float, experience_score: float,
                               skills_score: float, responsibilities_score: float,
                               weighted_education: float, weighted_experience: float,
                               weighted_skills: float, weighted_responsibilities: float,
                               scoring_rules: VacancyScoringRules, cv_analysis: CVAnalysis,
                               vacancy: Vacancy) -> Dict[str, Any]:
        """Create score breakdown."""
        return {
            'component_scores': {
                'education': {
                    'raw_score': education_score,
                    'weight': scoring_rules.education_weight,
                    'weighted_score': weighted_education
                },
                'experience': {
                    'raw_score': experience_score,
                    'weight': scoring_rules.experience_weight,
                    'weighted_score': weighted_experience
                },
                'skills': {
                    'raw_score': skills_score,
                    'weight': scoring_rules.skills_weight,
                    'weighted_score': weighted_skills
                },
                'responsibilities': {
                    'raw_score': responsibilities_score,
                    'weight': scoring_rules.responsibilities_weight,
                    'weighted_score': weighted_responsibilities
                }
            },
            'candidate_data': {
                'education_level': cv_analysis.education_level,
                'years_of_experience': cv_analysis.years_of_experience,
                'skills_count': len(self._extract_skills(cv_analysis))
            },
            'vacancy_info': {
                'title': vacancy.title,
                'category': getattr(vacancy, 'category', 'General')
            }
        }
    
    def _get_default_education_requirements(self) -> Dict[str, Any]:
        """Default education requirements."""
        return {
            'high_school': {'score': 20, 'keywords': ['high school', 'secondary']},
            'associate': {'score': 40, 'keywords': ['associate', 'diploma']},
            'bachelor': {'score': 70, 'keywords': ['bachelor', 'ba', 'bs', 'bsc']},
            'master': {'score': 90, 'keywords': ['master', 'ma', 'ms', 'msc', 'mba']},
            'phd': {'score': 100, 'keywords': ['phd', 'doctorate', 'doctoral']},
            'default_score': 30
        }
    
    def _get_default_experience_requirements(self) -> Dict[str, Any]:
        """Default experience requirements."""
        return {
            'ranges': [
                {'min': 0, 'max': 1, 'score': 20},
                {'min': 1, 'max': 3, 'score': 50},
                {'min': 3, 'max': 5, 'score': 75},
                {'min': 5, 'max': 10, 'score': 90},
                {'min': 10, 'max': 999, 'score': 100}
            ],
            'default_score': 30
        }
    
    def _get_default_skills_requirements(self) -> Dict[str, Any]:
        """Default skills requirements."""
        return {
            'required_skills': [],
            'preferred_skills': [],
            'skill_matching_threshold': 0.7,
            'default_score': 50
        }
    
    def _get_default_responsibilities_requirements(self) -> Dict[str, Any]:
        """Default responsibilities requirements."""
        return {
            'key_responsibilities': [],
            'responsibility_matching_threshold': 0.6,
            'default_score': 50
        }


class Command(BaseCommand):
    help = 'Test custom scoring system implementation'

    def add_arguments(self, parser):
        parser.add_argument('--create-sample', action='store_true', help='Create sample data')
        parser.add_argument('--test-scoring', action='store_true', help='Test scoring calculations')
        parser.add_argument('--run-all', action='store_true', help='Run complete test suite')

    def handle(self, *args, **options):
        if options['create_sample'] or options['run_all']:
            self.create_sample_data()
            
        if options['test_scoring'] or options['run_all']:
            self.test_scoring_system()
    
    def create_sample_data(self):
        """Create sample scoring templates and test data."""
        self.stdout.write("🏗️  Creating sample data...")
        
        # Create sample company
        company, created = Company.objects.get_or_create(
            name="Tech Solutions Inc.",
            defaults={'email': '<EMAIL>'}
        )
        
        # Create sample vacancy
        vacancy, created = Vacancy.objects.get_or_create(
            title="Senior Python Developer",
            defaults={
                'company': company,
                'description': 'Looking for experienced Python developer',
                'requirements': 'Bachelor degree, 3+ years Python experience',
                'location': 'Remote',
                'salary_range': '80000-120000',
                'status': 'active'
            }
        )
        
        # Create scoring template
        template, created = ScoringTemplate.objects.get_or_create(
            name="Python Developer Template",
            defaults={
                'category': 'Technology',
                'description': 'Template for Python developer positions',
                'education_weight': 20.0,
                'experience_weight': 40.0,
                'skills_weight': 35.0,
                'responsibilities_weight': 5.0,
                'education_requirements': {
                    'preferred_levels': ['bachelor', 'master'],
                    'required_fields': ['computer science', 'software engineering']
                },
                'experience_requirements': {
                    'minimum_years': 3,
                    'preferred_years': 5
                },
                'skills_requirements': {
                    'required_skills': ['python', 'django', 'sql'],
                    'preferred_skills': ['react', 'aws', 'docker'],
                    'skill_matching_threshold': 0.7
                },
                'responsibilities_requirements': {
                    'key_responsibilities': ['development', 'testing', 'code review']
                }
            }
        )
        
        self.stdout.write(f"✅ Sample data created - Company: {company.name}, Vacancy: {vacancy.title}")
        
    def test_scoring_system(self):
        """Test the custom scoring system."""
        self.stdout.write("\n🧪 Testing Custom Scoring System...")
        
        engine = CustomScoringEngine()
        
        # Get test data
        cv_analyses = CVAnalysis.objects.all()[:5]
        vacancies = Vacancy.objects.all()[:3]
        
        if not cv_analyses.exists():
            self.stdout.write("❌ No CV analyses found for testing")
            return
            
        if not vacancies.exists():
            self.stdout.write("❌ No vacancies found for testing")
            return
        
        self.stdout.write(f"📊 Testing with {cv_analyses.count()} CVs and {vacancies.count()} vacancies")
        
        results = []
        
        with transaction.atomic():
            for vacancy in vacancies:
                self.stdout.write(f"\n🎯 Testing vacancy: {vacancy.title}")
                
                for cv_analysis in cv_analyses:
                    try:
                        result = engine.calculate_custom_score(cv_analysis, vacancy)
                        results.append(result)
                        
                        candidate_name = cv_analysis.full_name or f"CV-{cv_analysis.id}"
                        self.stdout.write(
                            f"   ✅ {candidate_name}: {result.final_custom_score:.1f}% "
                            f"({result.custom_recommendation})"
                        )
                        
                        # Show component breakdown
                        self.stdout.write(
                            f"      📋 Education: {result.education_score:.1f}%, "
                            f"Experience: {result.experience_score:.1f}%, "
                            f"Skills: {result.skills_score:.1f}%, "
                            f"Responsibilities: {result.responsibilities_score:.1f}%"
                        )
                        
                    except Exception as e:
                        self.stdout.write(f"   ❌ Error for {cv_analysis.id}: {str(e)}")
                        continue
        
        # Summary
        self.stdout.write(f"\n📈 Results Summary:")
        self.stdout.write(f"   • Total scoring results: {len(results)}")
        if results:
            avg_score = sum(r.final_custom_score for r in results) / len(results)
            self.stdout.write(f"   • Average score: {avg_score:.1f}%")
            
            recommendations = {}
            for result in results:
                rec = result.custom_recommendation
                recommendations[rec] = recommendations.get(rec, 0) + 1
            
            self.stdout.write("   • Recommendations:")
            for rec, count in recommendations.items():
                self.stdout.write(f"     - {rec}: {count}")
        
        self.stdout.write("\n✅ Custom scoring system test completed!")
        
        # Show database status
        self.stdout.write(f"\n📊 Database Status:")
        self.stdout.write(f"   • Scoring Templates: {ScoringTemplate.objects.count()}")
        self.stdout.write(f"   • Vacancy Scoring Rules: {VacancyScoringRules.objects.count()}")
        self.stdout.write(f"   • Custom Scoring Results: {CustomScoringResult.objects.count()}") 