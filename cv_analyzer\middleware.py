"""
Security middleware for CV Analyzer application.
Implements rate limiting, security headers, request validation, and authentication protection.
"""

import time
import json
import logging
from typing import Dict, Optional, Union
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.core.cache import cache
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.models import User
from django.core.exceptions import PermissionDenied
from django.urls import reverse
from django.utils import timezone
from .security import SecurityHeaders, InputValidator
import ipaddress

logger = logging.getLogger(__name__)

class SecurityHeadersMiddleware(MiddlewareMixin):
    """Add security headers to all responses."""
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """Add security headers to response."""
        security_headers = SecurityHeaders.get_security_headers()
        
        for header, value in security_headers.items():
            response[header] = value
        
        # Add additional headers based on request type
        if request.path.startswith('/api/'):
            response['X-API-Version'] = '1.0'
            response['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
        
        return response

class RateLimitMiddleware(MiddlewareMixin):
    """Rate limiting middleware with different limits for different endpoints."""
    
    # Rate limits: (requests, time_window_seconds)
    RATE_LIMITS = {
        'upload': (10, 3600),      # 10 uploads per hour
        'api': (100, 3600),        # 100 API calls per hour
        'login': (5, 300),         # 5 login attempts per 5 minutes
        'default': (1000, 3600),   # 1000 requests per hour
    }
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        """Check rate limits before processing request."""
        
        # Skip rate limiting for certain paths
        if self._should_skip_rate_limiting(request):
            return None
        
        # Get client IP
        client_ip = self._get_client_ip(request)
        
        # Determine rate limit type
        limit_type = self._get_limit_type(request)
        
        # Check rate limit
        if self._is_rate_limited(client_ip, limit_type, request):
            logger.warning(f"Rate limit exceeded for IP {client_ip} on {request.path}")
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'message': 'Too many requests. Please try again later.',
                'retry_after': self.RATE_LIMITS[limit_type][1]
            }, status=429)
        
        return None
    
    def _should_skip_rate_limiting(self, request: HttpRequest) -> bool:
        """Check if rate limiting should be skipped."""
        skip_paths = [
            '/static/',
            '/media/',
            '/admin/jsi18n/',
        ]
        
        return any(request.path.startswith(path) for path in skip_paths)
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address."""
        # Check for forwarded IP in headers
        forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if forwarded_for:
            # Take the first IP in the chain
            ip = forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', 'unknown')
        
        return ip
    
    def _get_limit_type(self, request: HttpRequest) -> str:
        """Determine the appropriate rate limit type."""
        path = request.path.lower()
        
        if '/upload' in path or request.method == 'POST' and 'file' in str(request.FILES):
            return 'upload'
        elif path.startswith('/api/'):
            return 'api'
        elif '/login' in path or '/accounts/login' in path:
            return 'login'
        else:
            return 'default'
    
    def _is_rate_limited(self, client_ip: str, limit_type: str, request: HttpRequest) -> bool:
        """Check if the client has exceeded rate limits."""
        max_requests, time_window = self.RATE_LIMITS[limit_type]
        
        # Create cache key
        cache_key = f"rate_limit:{limit_type}:{client_ip}"
        
        # Get current request count
        current_requests = cache.get(cache_key, 0)
        
        if current_requests >= max_requests:
            return True
        
        # Increment counter
        cache.set(cache_key, current_requests + 1, time_window)
        
        # Log high usage
        if current_requests > max_requests * 0.8:
            logger.warning(f"High rate limit usage for IP {client_ip}: {current_requests}/{max_requests}")
        
        return False

class AuthenticationAuditMiddleware(MiddlewareMixin):
    """Audit authentication and authorization attempts."""
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        """Log authentication and authorization events."""
        
        # Log login attempts
        if request.path in [reverse('login'), '/accounts/login/'] and request.method == 'POST':
            username = request.POST.get('username', 'unknown')
            client_ip = self._get_client_ip(request)
            
            # Create audit log entry
            self._create_audit_log(
                event_type='login_attempt',
                username=username,
                ip_address=client_ip,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                request_path=request.path
            )
        
        # Check for suspicious activity
        if self._detect_suspicious_activity(request):
            self._create_security_alert(request)
        
        return None
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """Log authentication results."""
        
        # Log successful logins
        if (request.path in [reverse('login'), '/accounts/login/'] and 
            request.method == 'POST' and 
            response.status_code == 302):  # Redirect indicates success
            
            username = request.POST.get('username', 'unknown')
            client_ip = self._get_client_ip(request)
            
            self._create_audit_log(
                event_type='login_success',
                username=username,
                ip_address=client_ip,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                request_path=request.path
            )
        
        # Log failed logins
        elif (request.path in [reverse('login'), '/accounts/login/'] and 
              request.method == 'POST' and 
              response.status_code == 200):  # Form redisplay indicates failure
            
            username = request.POST.get('username', 'unknown')
            client_ip = self._get_client_ip(request)
            
            self._create_audit_log(
                event_type='login_failure',
                username=username,
                ip_address=client_ip,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                request_path=request.path
            )
        
        return response
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address."""
        forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if forwarded_for:
            ip = forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', 'unknown')
        return ip
    
    def _create_audit_log(self, event_type: str, username: str, ip_address: str, 
                         user_agent: str, request_path: str) -> None:
        """Create an audit log entry."""
        from .models import SecurityAuditLog  # Import here to avoid circular imports
        
        try:
            SecurityAuditLog.objects.create(
                event_type=event_type,
                username=username,
                ip_address=ip_address,
                user_agent=user_agent,
                request_path=request_path,
                timestamp=timezone.now()
            )
        except Exception as e:
            logger.error(f"Failed to create audit log: {str(e)}")
    
    def _detect_suspicious_activity(self, request: HttpRequest) -> bool:
        """Detect suspicious activity patterns."""
        client_ip = self._get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Check for suspicious patterns
        suspicious_patterns = [
            'sqlmap',
            'nikto',
            'nmap',
            'masscan',
            'burp',
            'dirb',
            'gobuster',
            'hydra',
        ]
        
        if any(pattern in user_agent.lower() for pattern in suspicious_patterns):
            return True
        
        # Check for SQL injection patterns in query parameters
        for param_value in request.GET.values():
            if self._contains_sql_injection_patterns(param_value):
                return True
        
        # Check for XSS patterns
        for param_value in request.GET.values():
            if self._contains_xss_patterns(param_value):
                return True
        
        return False
    
    def _contains_sql_injection_patterns(self, value: str) -> bool:
        """Check for SQL injection patterns."""
        sql_patterns = [
            'union select',
            'drop table',
            'insert into',
            'delete from',
            'update set',
            'exec(',
            'execute(',
            'sp_',
            'xp_',
            '/*',
            '*/',
            '--',
            ';--',
            "' or 1=1",
            '" or 1=1',
        ]
        
        value_lower = value.lower()
        return any(pattern in value_lower for pattern in sql_patterns)
    
    def _contains_xss_patterns(self, value: str) -> bool:
        """Check for XSS patterns."""
        xss_patterns = [
            '<script',
            'javascript:',
            'vbscript:',
            'onload=',
            'onerror=',
            'onclick=',
            'onmouseover=',
            'onfocus=',
            'eval(',
            'alert(',
            'confirm(',
            'prompt(',
        ]
        
        value_lower = value.lower()
        return any(pattern in value_lower for pattern in xss_patterns)
    
    def _create_security_alert(self, request: HttpRequest) -> None:
        """Create a security alert for suspicious activity."""
        from .models import SecurityAlert  # Import here to avoid circular imports
        
        try:
            SecurityAlert.objects.create(
                alert_type='suspicious_activity',
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                request_path=request.path,
                request_method=request.method,
                query_params=dict(request.GET),
                timestamp=timezone.now(),
                severity='medium'
            )
            
            logger.warning(f"Suspicious activity detected from {self._get_client_ip(request)}")
        except Exception as e:
            logger.error(f"Failed to create security alert: {str(e)}")

class RequestValidationMiddleware(MiddlewareMixin):
    """Validate and sanitize incoming requests."""
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        """Validate incoming requests."""
        
        # Skip validation for certain paths
        if self._should_skip_validation(request):
            return None
        
        # Validate request size
        content_length = int(request.META.get('CONTENT_LENGTH', 0))
        max_size = getattr(settings, 'MAX_REQUEST_SIZE', 50 * 1024 * 1024)  # 50MB default
        
        if content_length > max_size:
            logger.warning(f"Request too large: {content_length} bytes from {self._get_client_ip(request)}")
            return JsonResponse({
                'error': 'Request too large',
                'message': f'Request size exceeds maximum allowed size of {max_size // (1024*1024)}MB'
            }, status=413)
        
        # Validate HTTP method
        if request.method not in ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS']:
            return JsonResponse({
                'error': 'Method not allowed',
                'message': 'HTTP method not supported'
            }, status=405)
        
        # Validate Content-Type for POST/PUT requests
        if request.method in ['POST', 'PUT', 'PATCH']:
            content_type = request.META.get('CONTENT_TYPE', '')
            if not self._is_valid_content_type(content_type, request):
                return JsonResponse({
                    'error': 'Invalid content type',
                    'message': 'Content-Type not supported for this endpoint'
                }, status=415)
        
        return None
    
    def _should_skip_validation(self, request: HttpRequest) -> bool:
        """Check if validation should be skipped."""
        skip_paths = [
            '/static/',
            '/media/',
            '/admin/jsi18n/',
        ]
        
        return any(request.path.startswith(path) for path in skip_paths)
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address."""
        forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if forwarded_for:
            ip = forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', 'unknown')
        return ip
    
    def _is_valid_content_type(self, content_type: str, request: HttpRequest) -> bool:
        """Validate Content-Type header."""
        # Extract base content type (ignore charset, boundary, etc.)
        base_content_type = content_type.split(';')[0].strip().lower()
        
        # Define allowed content types
        allowed_types = [
            'application/json',
            'application/x-www-form-urlencoded',
            'multipart/form-data',
            'text/plain',
        ]
        
        # For file upload endpoints, allow multipart/form-data
        if '/upload' in request.path or 'file' in str(request.FILES):
            return base_content_type in allowed_types or base_content_type.startswith('multipart/')
        
        return base_content_type in allowed_types

class SessionSecurityMiddleware(MiddlewareMixin):
    """Enhanced session security middleware."""
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        """Process session security."""
        
        if request.user.is_authenticated:
            # Check session timeout
            if self._is_session_expired(request):
                self._logout_user(request)
                return JsonResponse({
                    'error': 'Session expired',
                    'message': 'Your session has expired. Please log in again.',
                    'redirect': reverse('login')
                }, status=401)
            
            # Update last activity
            request.session['last_activity'] = time.time()
            
            # Validate session integrity
            if not self._is_session_valid(request):
                self._logout_user(request)
                return JsonResponse({
                    'error': 'Invalid session',
                    'message': 'Session validation failed. Please log in again.',
                    'redirect': reverse('login')
                }, status=401)
        
        return None
    
    def _is_session_expired(self, request: HttpRequest) -> bool:
        """Check if session has expired."""
        last_activity = request.session.get('last_activity')
        if not last_activity:
            return False
        
        timeout = getattr(settings, 'SESSION_TIMEOUT', 3600)  # 1 hour default
        return time.time() - last_activity > timeout
    
    def _is_session_valid(self, request: HttpRequest) -> bool:
        """Validate session integrity."""
        # Check if user agent changed
        stored_user_agent = request.session.get('user_agent')
        current_user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        if stored_user_agent is None:
            request.session['user_agent'] = current_user_agent
            return True
        
        return stored_user_agent == current_user_agent
    
    def _logout_user(self, request: HttpRequest) -> None:
        """Safely logout user."""
        from django.contrib.auth import logout
        logout(request)

class IPFilterMiddleware(MiddlewareMixin):
    """IP-based access control middleware."""
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        """Filter requests based on IP address."""
        
        client_ip = self._get_client_ip(request)
        
        # Check if IP is blocked
        if self._is_ip_blocked(client_ip):
            logger.warning(f"Blocked IP attempted access: {client_ip}")
            return JsonResponse({
                'error': 'Access denied',
                'message': 'Your IP address has been blocked'
            }, status=403)
        
        # Check if IP is in allowed list (if configured)
        if hasattr(settings, 'ALLOWED_IPS') and settings.ALLOWED_IPS:
            if not self._is_ip_allowed(client_ip):
                logger.warning(f"Non-whitelisted IP attempted access: {client_ip}")
                return JsonResponse({
                    'error': 'Access denied',
                    'message': 'Your IP address is not authorized'
                }, status=403)
        
        return None
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address."""
        forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if forwarded_for:
            ip = forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', 'unknown')
        return ip
    
    def _is_ip_blocked(self, ip: str) -> bool:
        """Check if IP is in the blocked list."""
        blocked_ips = getattr(settings, 'BLOCKED_IPS', [])
        
        try:
            client_ip = ipaddress.ip_address(ip)
            
            for blocked_ip in blocked_ips:
                if '/' in blocked_ip:  # CIDR notation
                    if client_ip in ipaddress.ip_network(blocked_ip, strict=False):
                        return True
                else:  # Single IP
                    if client_ip == ipaddress.ip_address(blocked_ip):
                        return True
        except ValueError:
            # Invalid IP format
            logger.warning(f"Invalid IP format: {ip}")
            return True  # Block invalid IPs
        
        return False
    
    def _is_ip_allowed(self, ip: str) -> bool:
        """Check if IP is in the allowed list."""
        allowed_ips = getattr(settings, 'ALLOWED_IPS', [])
        
        if not allowed_ips:  # No restriction if list is empty
            return True
        
        try:
            client_ip = ipaddress.ip_address(ip)
            
            for allowed_ip in allowed_ips:
                if '/' in allowed_ip:  # CIDR notation
                    if client_ip in ipaddress.ip_network(allowed_ip, strict=False):
                        return True
                else:  # Single IP
                    if client_ip == ipaddress.ip_address(allowed_ip):
                        return True
        except ValueError:
            # Invalid IP format
            logger.warning(f"Invalid IP format: {ip}")
            return False
        
        return False