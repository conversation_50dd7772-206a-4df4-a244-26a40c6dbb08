# Generated by Django 4.2.14 on 2024-08-02 11:59

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("cv_analyzer", "create_default_welcome_content"),
    ]

    operations = [
        migrations.CreateModel(
            name="Company",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField()),
                ("industry", models.CharField(max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.RemoveField(
            model_name="cv",
            name="job",
        ),
        migrations.AlterField(
            model_name="analysis",
            name="cv",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="cv_analyzer.cv"
            ),
        ),
        migrations.AlterField(
            model_name="cv",
            name="file",
            field=models.FileField(
                upload_to="cvs/",
                validators=[django.core.validators.FileExtensionValidator(["pdf"])],
            ),
        ),
        migrations.CreateModel(
            name="Vacancy",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                ("requirements", models.TextField()),
                ("category", models.CharField(max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cv_analyzer.company",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ApplicantProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("complete", models.BooleanField(default=False)),
                ("phone_number", models.CharField(blank=True, max_length=20)),
                ("address", models.TextField(blank=True)),
                ("linkedin_profile", models.URLField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "cv",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="profile",
                        to="cv_analyzer.cv",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="analysis",
            name="vacancy",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="cv_analyzer.vacancy",
            ),
        ),
        migrations.AddField(
            model_name="cv",
            name="applicant_profile",
            field=models.OneToOneField(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="uploaded_cv",
                to="cv_analyzer.applicantprofile",
            ),
        ),
    ]
