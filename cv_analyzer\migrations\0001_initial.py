# Generated by Django 4.2.7 on 2024-08-02 06:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Job",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                ("conditions", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CV",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("file", models.FileField(upload_to="cvs/")),
                ("uploaded_at", models.DateTimeField(auto_now_add=True)),
                (
                    "job",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cv_analyzer.job",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Analysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("location", models.CharField(max_length=200)),
                ("contact", models.CharField(max_length=200)),
                ("experience", models.TextField()),
                ("education", models.TextField()),
                ("skills", models.TextField()),
                ("past_roles", models.TextField()),
                ("certifications", models.TextField()),
                ("compatibility", models.IntegerField()),
                ("analysis_text", models.TextField()),
                ("experience_value", models.IntegerField()),
                ("education_value", models.IntegerField()),
                ("skills_value", models.IntegerField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "cv",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE, to="cv_analyzer.cv"
                    ),
                ),
            ],
        ),
    ]
