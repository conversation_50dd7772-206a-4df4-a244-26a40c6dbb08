# Generated by Django 4.2.14 on 2024-08-02 08:25

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("cv_analyzer", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="WelcomeContent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("welcome_message", models.TextField()),
                ("step_1_title", models.CharField(max_length=200)),
                ("step_1_description", models.TextField()),
                (
                    "step_1_image",
                    models.ImageField(
                        upload_to="welcome_images/",
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                ["png", "jpg", "jpeg"]
                            )
                        ],
                    ),
                ),
                ("step_2_title", models.CharField(max_length=200)),
                ("step_2_description", models.TextField()),
                (
                    "step_2_image",
                    models.ImageField(
                        upload_to="welcome_images/",
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                ["png", "jpg", "jpeg"]
                            )
                        ],
                    ),
                ),
                ("step_3_title", models.CharField(max_length=200)),
                ("step_3_description", models.TextField()),
                (
                    "step_3_image",
                    models.ImageField(
                        upload_to="welcome_images/",
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                ["png", "jpg", "jpeg"]
                            )
                        ],
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Welcome Content",
            },
        ),
    ]
