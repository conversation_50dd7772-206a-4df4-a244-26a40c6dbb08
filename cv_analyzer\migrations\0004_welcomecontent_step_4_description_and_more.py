# Generated by Django 4.2.14 on 2024-08-02 14:17

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("cv_analyzer", "0003_vacancy_status"),
    ]

    operations = [
        migrations.AddField(
            model_name="welcomecontent",
            name="step_4_description",
            field=models.TextField(
                default="Access comprehensive analysis reports, including compatibility scores, key skills, and areas for improvement for each candidate."
            ),
        ),
        migrations.AddField(
            model_name="welcomecontent",
            name="step_4_image",
            field=models.ImageField(
                blank=True,
                null=True,
                upload_to="welcome_images/",
                validators=[
                    django.core.validators.FileExtensionValidator(
                        ["png", "jpg", "jpeg"]
                    )
                ],
            ),
        ),
        migrations.AddField(
            model_name="welcomecontent",
            name="step_4_title",
            field=models.CharField(default="View Results", max_length=200),
        ),
    ]
