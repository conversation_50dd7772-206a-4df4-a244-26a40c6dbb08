# Generated by Django 4.2.14 on 2024-08-03 06:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("cv_analyzer", "0005_cv_source"),
    ]

    operations = [
        migrations.CreateModel(
            name="CompanyAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "company_size",
                    models.CharField(
                        choices=[
                            ("small", "Small (1-50 employees)"),
                            ("medium", "Medium (51-250 employees)"),
                            ("large", "Large (251+ employees)"),
                        ],
                        max_length=50,
                    ),
                ),
                ("founded_year", models.PositiveIntegerField(blank=True, null=True)),
                ("headquarters", models.CharField(max_length=200)),
                ("company_culture", models.TextField()),
                ("benefits", models.TextField()),
                ("growth_opportunities", models.TextField()),
                ("technology_stack", models.TextField()),
                ("main_competitors", models.TextField()),
                ("recent_news", models.TextField()),
                ("additional_info", models.JSONField(blank=True, default=dict)),
            ],
        ),
        migrations.CreateModel(
            name="CVAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("email", models.EmailField(max_length=254)),
                ("phone_number", models.CharField(max_length=20)),
                ("location", models.CharField(max_length=200)),
                ("years_of_experience", models.PositiveIntegerField()),
                ("education_level", models.CharField(max_length=100)),
                ("skills", models.TextField()),
                ("languages", models.TextField()),
                (
                    "preferred_job_type",
                    models.CharField(
                        choices=[
                            ("full_time", "Full Time"),
                            ("part_time", "Part Time"),
                            ("contract", "Contract"),
                            ("freelance", "Freelance"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "preferred_work_location",
                    models.CharField(
                        choices=[
                            ("on_site", "On Site"),
                            ("remote", "Remote"),
                            ("hybrid", "Hybrid"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "salary_expectation",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("availability", models.DateField(blank=True, null=True)),
                ("additional_info", models.JSONField(blank=True, default=dict)),
            ],
        ),
        migrations.CreateModel(
            name="VacancyAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("required_skills", models.TextField()),
                ("preferred_skills", models.TextField()),
                ("required_experience", models.PositiveIntegerField()),
                ("required_education", models.CharField(max_length=100)),
                ("job_responsibilities", models.TextField()),
                ("salary_range", models.CharField(max_length=100)),
                ("benefits", models.TextField()),
                ("work_hours", models.CharField(max_length=100)),
                ("travel_requirements", models.TextField()),
                ("remote_work_policy", models.CharField(max_length=100)),
                ("career_growth_opportunities", models.TextField()),
                ("application_deadline", models.DateField(blank=True, null=True)),
                ("interview_process", models.TextField()),
                ("additional_info", models.JSONField(blank=True, default=dict)),
            ],
        ),
        migrations.AddField(
            model_name="company",
            name="benefits",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="company",
            name="company_culture",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="company",
            name="company_size",
            field=models.CharField(
                choices=[
                    ("small", "Small (1-50 employees)"),
                    ("medium", "Medium (51-250 employees)"),
                    ("large", "Large (251+ employees)"),
                ],
                default="small",
                max_length=50,
            ),
        ),
        migrations.AddField(
            model_name="company",
            name="founded_year",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="company",
            name="growth_opportunities",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="company",
            name="headquarters",
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name="company",
            name="tags",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="company",
            name="technology_stack",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="cv",
            name="education_level",
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name="cv",
            name="job_type_preference",
            field=models.CharField(
                choices=[
                    ("full_time", "Full Time"),
                    ("part_time", "Part Time"),
                    ("contract", "Contract"),
                    ("freelance", "Freelance"),
                ],
                default="full_time",
                max_length=50,
            ),
        ),
        migrations.AddField(
            model_name="cv",
            name="languages",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="cv",
            name="preferred_industries",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="cv",
            name="skills",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="cv",
            name="tags",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="cv",
            name="work_location_preference",
            field=models.CharField(
                choices=[
                    ("on_site", "On Site"),
                    ("remote", "Remote"),
                    ("hybrid", "Hybrid"),
                ],
                default="on_site",
                max_length=50,
            ),
        ),
        migrations.AddField(
            model_name="cv",
            name="years_of_experience",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="vacancy",
            name="application_deadline",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="vacancy",
            name="job_type",
            field=models.CharField(
                choices=[
                    ("full_time", "Full Time"),
                    ("part_time", "Part Time"),
                    ("contract", "Contract"),
                    ("freelance", "Freelance"),
                ],
                default="full_time",
                max_length=50,
            ),
        ),
        migrations.AddField(
            model_name="vacancy",
            name="preferred_skills",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="vacancy",
            name="required_education",
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name="vacancy",
            name="required_experience",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="vacancy",
            name="required_skills",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="vacancy",
            name="salary_range",
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name="vacancy",
            name="tags",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="vacancy",
            name="work_location",
            field=models.CharField(
                choices=[
                    ("on_site", "On Site"),
                    ("remote", "Remote"),
                    ("hybrid", "Hybrid"),
                ],
                default="on_site",
                max_length=50,
            ),
        ),
        migrations.DeleteModel(
            name="Analysis",
        ),
        migrations.AddField(
            model_name="vacancyanalysis",
            name="vacancy",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="analysis",
                to="cv_analyzer.vacancy",
            ),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="cv",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="analysis",
                to="cv_analyzer.cv",
            ),
        ),
        migrations.AddField(
            model_name="companyanalysis",
            name="company",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="analysis",
                to="cv_analyzer.company",
            ),
        ),
    ]
