# Generated by Django 4.2.14 on 2024-08-03 06:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("cv_analyzer", "0006_companyanalysis_cvanalysis_vacancyanalysis_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="AIAPIConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "provider",
                    models.CharField(
                        choices=[
                            ("ollama", "Ollama"),
                            ("groq", "Groq"),
                            ("openai", "OpenAI"),
                        ],
                        max_length=10,
                    ),
                ),
                ("api_key", models.CharField(blank=True, max_length=255)),
                ("api_url", models.URLField(blank=True)),
                ("model_name", models.Char<PERSON>ield(max_length=100)),
                ("is_active", models.BooleanField(default=True)),
                ("priority", models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name="AIPromptConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("prompt", models.TextField()),
                ("instructions", models.TextField()),
            ],
        ),
        migrations.CreateModel(
            name="UnifiedAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("analysis_text", models.TextField()),
                ("compatibility", models.IntegerField()),
                ("experience_value", models.IntegerField()),
                ("education_value", models.IntegerField()),
                ("skills_value", models.IntegerField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "applicant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cv_analyzer.company",
                    ),
                ),
                (
                    "cv",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="cv_analyzer.cv"
                    ),
                ),
                (
                    "vacancy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cv_analyzer.vacancy",
                    ),
                ),
            ],
        ),
    ]
