# Generated by Django 4.2.14 on 2024-08-03 11:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("cv_analyzer", "0009_alter_cv_work_location_preference"),
    ]

    operations = [
        migrations.CreateModel(
            name="AIConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("prompt", models.TextField()),
                ("model_name", models.<PERSON>r<PERSON>ield(max_length=100)),
                (
                    "provider",
                    models.CharField(
                        choices=[
                            ("openai", "OpenAI"),
                            ("groq", "Groq"),
                            ("ollama", "Ollama"),
                        ],
                        max_length=50,
                    ),
                ),
                ("api_key", models.CharField(blank=True, max_length=255)),
                ("api_url", models.<PERSON><PERSON><PERSON><PERSON>(blank=True)),
                ("is_active", models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name="ComparisonAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("compatibility_score", models.FloatField()),
                ("analysis_text", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "ai_config",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="cv_analyzer.aiconfig",
                    ),
                ),
                (
                    "cv",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="cv_analyzer.cv"
                    ),
                ),
                (
                    "vacancy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cv_analyzer.vacancy",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="companyanalysis",
            name="ai_config",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="cv_analyzer.aiconfig",
            ),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="ai_config",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="cv_analyzer.aiconfig",
            ),
        ),
    ]
