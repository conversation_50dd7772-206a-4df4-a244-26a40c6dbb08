# Generated by Django 4.2.14 on 2024-08-03 14:08

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        (
            "cv_analyzer",
            "0011_remove_aiconfig_api_key_remove_aiconfig_api_url_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="cvanalysis",
            name="content_details",
            field=models.JSONField(default=dict),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="content_score",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="format_details",
            field=models.JSONField(default=dict),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="format_score",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="overall_score",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="sections_details",
            field=models.J<PERSON><PERSON>ield(default=dict),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="sections_score",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="skills_details",
            field=models.JSONField(default=dict),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="skills_score",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="style_details",
            field=models.JSONField(default=dict),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="style_score",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="cvanalysis",
            name="education_level",
            field=models.CharField(default="", max_length=100),
        ),
        migrations.AlterField(
            model_name="cvanalysis",
            name="email",
            field=models.EmailField(default="", max_length=254),
        ),
        migrations.AlterField(
            model_name="cvanalysis",
            name="languages",
            field=models.TextField(default=""),
        ),
        migrations.AlterField(
            model_name="cvanalysis",
            name="location",
            field=models.CharField(default="", max_length=200),
        ),
        migrations.AlterField(
            model_name="cvanalysis",
            name="name",
            field=models.CharField(default="", max_length=200),
        ),
        migrations.AlterField(
            model_name="cvanalysis",
            name="phone_number",
            field=models.CharField(default="", max_length=20),
        ),
        migrations.AlterField(
            model_name="cvanalysis",
            name="preferred_job_type",
            field=models.CharField(
                choices=[
                    ("full_time", "Full Time"),
                    ("part_time", "Part Time"),
                    ("contract", "Contract"),
                    ("freelance", "Freelance"),
                ],
                default="full_time",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="cvanalysis",
            name="preferred_work_location",
            field=models.CharField(
                choices=[
                    ("on_site", "On Site"),
                    ("remote", "Remote"),
                    ("hybrid", "Hybrid"),
                ],
                default="on_site",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="cvanalysis",
            name="skills",
            field=models.TextField(default=""),
        ),
        migrations.AlterField(
            model_name="cvanalysis",
            name="years_of_experience",
            field=models.PositiveIntegerField(default=0),
        ),
    ]
