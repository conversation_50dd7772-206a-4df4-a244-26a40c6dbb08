# Generated by Django 4.2.7 on 2024-12-18 18:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cv_analyzer', '0012_cvanalysis_content_details_cvanalysis_content_score_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='company',
            options={'verbose_name_plural': 'companies'},
        ),
        migrations.AlterModelOptions(
            name='vacancy',
            options={'verbose_name_plural': 'vacancies'},
        ),
        migrations.RemoveField(
            model_name='company',
            name='benefits',
        ),
        migrations.RemoveField(
            model_name='company',
            name='company_culture',
        ),
        migrations.RemoveField(
            model_name='company',
            name='company_size',
        ),
        migrations.RemoveField(
            model_name='company',
            name='founded_year',
        ),
        migrations.RemoveField(
            model_name='company',
            name='growth_opportunities',
        ),
        migrations.RemoveField(
            model_name='company',
            name='headquarters',
        ),
        migrations.RemoveField(
            model_name='company',
            name='tags',
        ),
        migrations.RemoveField(
            model_name='company',
            name='technology_stack',
        ),
        migrations.RemoveField(
            model_name='company',
            name='user',
        ),
        migrations.RemoveField(
            model_name='vacancy',
            name='application_deadline',
        ),
        migrations.RemoveField(
            model_name='vacancy',
            name='job_type',
        ),
        migrations.RemoveField(
            model_name='vacancy',
            name='preferred_skills',
        ),
        migrations.RemoveField(
            model_name='vacancy',
            name='required_education',
        ),
        migrations.RemoveField(
            model_name='vacancy',
            name='required_experience',
        ),
        migrations.RemoveField(
            model_name='vacancy',
            name='required_skills',
        ),
        migrations.RemoveField(
            model_name='vacancy',
            name='salary_range',
        ),
        migrations.RemoveField(
            model_name='vacancy',
            name='tags',
        ),
        migrations.RemoveField(
            model_name='vacancy',
            name='work_location',
        ),
        migrations.AddField(
            model_name='company',
            name='size',
            field=models.CharField(default=1, max_length=50),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='company',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='company',
            name='website',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='vacancy',
            name='expires_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='vacancy',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='name',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='vacancy',
            name='category',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='vacancy',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('closed', 'Closed'), ('archived', 'Archived')], default='draft', max_length=20),
        ),
        migrations.AlterField(
            model_name='vacancy',
            name='title',
            field=models.CharField(max_length=255),
        ),
    ]
