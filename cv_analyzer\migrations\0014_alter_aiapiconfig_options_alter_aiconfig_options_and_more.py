# Generated by Django 4.2.7 on 2024-12-18 19:21

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('cv_analyzer', '0013_alter_company_options_alter_vacancy_options_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='aiapiconfig',
            options={'ordering': ['priority'], 'verbose_name': 'AI API Configuration', 'verbose_name_plural': 'AI API Configurations'},
        ),
        migrations.AlterModelOptions(
            name='aiconfig',
            options={'verbose_name': 'AI Configuration', 'verbose_name_plural': 'AI Configurations'},
        ),
        migrations.RemoveField(
            model_name='aiapiconfig',
            name='ai_config',
        ),
        migrations.RemoveField(
            model_name='aiapiconfig',
            name='api_url',
        ),
        migrations.RemoveField(
            model_name='aiapiconfig',
            name='available_models',
        ),
        migrations.RemoveField(
            model_name='aiconfig',
            name='is_active',
        ),
        migrations.RemoveField(
            model_name='aiconfig',
            name='name',
        ),
        migrations.RemoveField(
            model_name='aiconfig',
            name='prompt',
        ),
        migrations.AddField(
            model_name='aiapiconfig',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='aiapiconfig',
            name='max_tokens',
            field=models.IntegerField(default=2000),
        ),
        migrations.AddField(
            model_name='aiapiconfig',
            name='temperature',
            field=models.FloatField(default=0.3, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)]),
        ),
        migrations.AddField(
            model_name='aiapiconfig',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='aiconfig',
            name='batch_size',
            field=models.IntegerField(default=10, help_text='Maximum number of CVs to process in parallel'),
        ),
        migrations.AddField(
            model_name='aiconfig',
            name='cache_ttl',
            field=models.IntegerField(default=86400, help_text='Cache TTL in seconds'),
        ),
        migrations.AddField(
            model_name='aiconfig',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='aiconfig',
            name='max_retries',
            field=models.IntegerField(default=3, help_text='Maximum number of retries for failed API calls'),
        ),
        migrations.AddField(
            model_name='aiconfig',
            name='retry_delay',
            field=models.IntegerField(default=1000, help_text='Delay between retries in milliseconds'),
        ),
        migrations.AddField(
            model_name='aiconfig',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='aiapiconfig',
            name='api_key',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='aiapiconfig',
            name='priority',
            field=models.IntegerField(help_text='Priority order for fallback (1 is highest)', validators=[django.core.validators.MinValueValidator(1)]),
        ),
        migrations.AlterField(
            model_name='aiapiconfig',
            name='provider',
            field=models.CharField(choices=[('openai', 'OpenAI'), ('groq', 'Groq')], max_length=50),
        ),
        migrations.CreateModel(
            name='AIUsageLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('provider', models.CharField(max_length=50)),
                ('operation', models.CharField(max_length=100)),
                ('tokens_used', models.IntegerField()),
                ('response_time', models.FloatField(help_text='Response time in seconds')),
                ('status', models.CharField(max_length=50)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'AI Usage Log',
                'verbose_name_plural': 'AI Usage Logs',
                'ordering': ['-created_at'],
            },
        ),
    ]
