# Generated by Django 4.2 on 2025-06-15 18:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('contenttypes', '0002_remove_content_type_name'),
        ('cv_analyzer', '0014_alter_aiapiconfig_options_alter_aiconfig_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccessibilityAudit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('page_url', models.CharField(max_length=200)),
                ('template_name', models.CharField(max_length=100)),
                ('audit_date', models.DateTimeField(auto_now_add=True)),
                ('wcag_level', models.CharField(choices=[('A', 'WCAG A'), ('AA', 'WCAG AA'), ('AAA', 'WCAG AAA'), ('NC', 'Non-Compliant')], max_length=3)),
                ('score', models.FloatField()),
                ('issues_count', models.IntegerField(default=0)),
                ('warnings_count', models.IntegerField(default=0)),
                ('audit_data', models.JSONField()),
            ],
            options={
                'db_table': 'cv_accessibility_audits',
            },
        ),
        migrations.CreateModel(
            name='AIProviderMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('provider_name', models.CharField(max_length=50)),
                ('request_count', models.PositiveIntegerField(default=0)),
                ('total_tokens', models.PositiveIntegerField(default=0)),
                ('total_cost', models.DecimalField(decimal_places=6, default=0, max_digits=10)),
                ('avg_response_time_ms', models.PositiveIntegerField(default=0)),
                ('success_count', models.PositiveIntegerField(default=0)),
                ('error_count', models.PositiveIntegerField(default=0)),
                ('last_health_check', models.DateTimeField(blank=True, null=True)),
                ('health_status', models.CharField(choices=[('healthy', 'Healthy'), ('warning', 'Warning'), ('error', 'Error'), ('unknown', 'Unknown')], default='unknown', max_length=20)),
                ('metadata', models.JSONField(default=dict)),
                ('date', models.DateField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='AlertConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('alert_type', models.CharField(choices=[('metric_threshold', 'Metric Threshold'), ('health_check', 'Health Check'), ('security_event', 'Security Event'), ('error_rate', 'Error Rate')], max_length=50)),
                ('enabled', models.BooleanField(default=True)),
                ('metric_name', models.CharField(blank=True, max_length=100)),
                ('threshold_value', models.FloatField(blank=True, null=True)),
                ('comparison_operator', models.CharField(default='>', max_length=10)),
                ('time_window_minutes', models.IntegerField(default=5)),
                ('notification_channels', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'alert_configurations',
            },
        ),
        migrations.CreateModel(
            name='APIUsageLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('endpoint', models.CharField(max_length=200)),
                ('method', models.CharField(max_length=10)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('response_time_ms', models.PositiveIntegerField()),
                ('status_code', models.PositiveIntegerField()),
                ('request_size', models.PositiveIntegerField(default=0)),
                ('response_size', models.PositiveIntegerField(default=0)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('event_type', models.CharField(max_length=50)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('resource', models.CharField(default='', max_length=100)),
                ('action', models.CharField(default='', max_length=50)),
                ('success', models.BooleanField(default=True)),
                ('old_value', models.JSONField(blank=True, null=True)),
                ('new_value', models.JSONField(blank=True, null=True)),
                ('additional_info', models.JSONField(blank=True, default=dict)),
            ],
            options={
                'db_table': 'audit_logs',
            },
        ),
        migrations.CreateModel(
            name='BusinessAuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(max_length=100)),
                ('entity_type', models.CharField(max_length=50)),
                ('entity_id', models.PositiveIntegerField()),
                ('details', models.JSONField(default=dict)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='BusinessRuleConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('rule_type', models.CharField(choices=[('validation', 'Validation'), ('business', 'Business'), ('security', 'Security'), ('notification', 'Notification')], max_length=20)),
                ('priority', models.PositiveIntegerField(default=5)),
                ('enabled', models.BooleanField(default=True)),
                ('rule_config', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-priority', 'name'],
            },
        ),
        migrations.CreateModel(
            name='DataProcessingActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_name', models.CharField(max_length=100)),
                ('purpose', models.CharField(choices=[('cv_analysis', 'CV Analysis Service'), ('user_management', 'User Account Management'), ('communication', 'User Communication'), ('analytics', 'Service Analytics'), ('security', 'Security Monitoring')], max_length=50)),
                ('data_categories', models.JSONField()),
                ('data_subjects', models.CharField(max_length=100)),
                ('recipients', models.JSONField(blank=True, null=True)),
                ('retention_period', models.CharField(max_length=100)),
                ('security_measures', models.TextField()),
                ('legal_basis', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'cv_analyzer_processing_activity',
            },
        ),
        migrations.CreateModel(
            name='DataProcessingConsent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('consent_type', models.CharField(choices=[('data_processing', 'General Data Processing'), ('marketing', 'Marketing Communications'), ('analytics', 'Analytics and Tracking'), ('third_party', 'Third-party Integrations')], max_length=50)),
                ('consent_given', models.BooleanField()),
                ('consent_date', models.DateTimeField(auto_now_add=True)),
                ('consent_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('consent_user_agent', models.TextField(blank=True, null=True)),
                ('withdrawal_date', models.DateTimeField(blank=True, null=True)),
                ('legal_basis', models.CharField(blank=True, max_length=100)),
            ],
            options={
                'db_table': 'cv_analyzer_data_consent',
            },
        ),
        migrations.CreateModel(
            name='DataRetentionPolicy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('data_type', models.CharField(max_length=100)),
                ('retention_period_days', models.PositiveIntegerField()),
                ('action', models.CharField(choices=[('delete', 'Delete Data'), ('anonymize', 'Anonymize Data'), ('archive', 'Archive Data')], max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'cv_analyzer_retention_policy',
            },
        ),
        migrations.CreateModel(
            name='DataSubjectRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_type', models.CharField(choices=[('access', 'Data Access Request'), ('rectification', 'Data Rectification Request'), ('erasure', 'Data Erasure Request'), ('portability', 'Data Portability Request'), ('restriction', 'Processing Restriction Request'), ('objection', 'Processing Objection')], max_length=20)),
                ('request_date', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('description', models.TextField(blank=True)),
                ('response_data', models.JSONField(blank=True, null=True)),
                ('completion_date', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'cv_analyzer_data_subject_request',
            },
        ),
        migrations.CreateModel(
            name='DeploymentLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deployment_id', models.CharField(max_length=50, unique=True)),
                ('environment', models.CharField(max_length=50)),
                ('version', models.CharField(max_length=50)),
                ('commit_hash', models.CharField(default='', max_length=40)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('success', 'Success'), ('failed', 'Failed'), ('rolled_back', 'Rolled Back')], default='pending', max_length=20)),
                ('pipeline_type', models.CharField(default='manual', max_length=50)),
                ('deployment_notes', models.TextField(blank=True)),
                ('rollback_version', models.CharField(blank=True, max_length=50)),
                ('metadata', models.JSONField(blank=True, default=dict)),
            ],
            options={
                'db_table': 'deployment_logs',
            },
        ),
        migrations.CreateModel(
            name='EncryptedData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveIntegerField()),
                ('field_name', models.CharField(max_length=100)),
                ('encrypted_value', models.TextField()),
                ('hash_value', models.CharField(max_length=64)),
                ('encryption_method', models.CharField(default='fernet', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'cv_analyzer_encrypted_data',
            },
        ),
        migrations.CreateModel(
            name='FileUploadLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filename', models.CharField(max_length=255)),
                ('original_filename', models.CharField(max_length=255)),
                ('file_size', models.BigIntegerField()),
                ('file_type', models.CharField(max_length=100)),
                ('mime_type', models.CharField(max_length=100)),
                ('md5_hash', models.CharField(max_length=32)),
                ('sha256_hash', models.CharField(max_length=64)),
                ('upload_path', models.TextField()),
                ('quarantine_path', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('uploaded', 'Uploaded'), ('validated', 'Validated'), ('quarantined', 'Quarantined'), ('rejected', 'Rejected'), ('deleted', 'Deleted')], max_length=20)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('validated_at', models.DateTimeField(blank=True, null=True)),
                ('validation_results', models.JSONField(blank=True, default=dict)),
                ('virus_scan_results', models.JSONField(blank=True, default=dict)),
                ('quarantine_reason', models.TextField(blank=True)),
            ],
            options={
                'verbose_name': 'File Upload Log',
                'verbose_name_plural': 'File Upload Logs',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='FrontendPerformanceMetric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('page_url', models.CharField(max_length=200)),
                ('load_time', models.FloatField()),
                ('dom_ready_time', models.FloatField()),
                ('first_paint_time', models.FloatField(blank=True, null=True)),
                ('largest_contentful_paint', models.FloatField(blank=True, null=True)),
                ('cumulative_layout_shift', models.FloatField(blank=True, null=True)),
                ('javascript_errors', models.IntegerField(default=0)),
                ('device_type', models.CharField(max_length=20)),
                ('browser', models.CharField(max_length=50)),
                ('recorded_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'cv_frontend_performance',
            },
        ),
        migrations.CreateModel(
            name='HealthCheckResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('check_name', models.CharField(max_length=50)),
                ('status', models.CharField(max_length=20)),
                ('response_time_ms', models.FloatField(default=0)),
                ('message', models.TextField(default='')),
                ('details', models.JSONField(blank=True, default=dict)),
            ],
            options={
                'db_table': 'health_check_results',
            },
        ),
        migrations.CreateModel(
            name='MonitoringMetric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('metric_type', models.CharField(max_length=50)),
                ('metric_name', models.CharField(max_length=100)),
                ('value', models.FloatField()),
                ('unit', models.CharField(default='', max_length=20)),
                ('source', models.CharField(default='system', max_length=50)),
                ('metadata', models.JSONField(blank=True, default=dict)),
            ],
            options={
                'db_table': 'monitoring_metrics',
            },
        ),
        migrations.CreateModel(
            name='NotificationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(max_length=50)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('delivery_status', models.CharField(choices=[('sent', 'Sent'), ('delivered', 'Delivered'), ('failed', 'Failed'), ('clicked', 'Clicked')], default='sent', max_length=20)),
            ],
            options={
                'db_table': 'cv_notification_logs',
            },
        ),
        migrations.CreateModel(
            name='OfflineData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data_type', models.CharField(max_length=50)),
                ('object_id', models.IntegerField()),
                ('data_content', models.JSONField()),
                ('cached_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('sync_status', models.CharField(choices=[('synced', 'Synced'), ('pending', 'Pending Sync'), ('failed', 'Sync Failed')], default='synced', max_length=20)),
            ],
            options={
                'db_table': 'cv_offline_data',
            },
        ),
        migrations.CreateModel(
            name='ProgressSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(max_length=100, unique=True)),
                ('operation_type', models.CharField(max_length=50)),
                ('total_steps', models.IntegerField()),
                ('current_step', models.IntegerField(default=0)),
                ('status', models.CharField(choices=[('initialized', 'Initialized'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('failed', 'Failed')], default='initialized', max_length=20)),
                ('progress_percentage', models.FloatField(default=0.0)),
                ('start_time', models.DateTimeField(auto_now_add=True)),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
            ],
            options={
                'db_table': 'cv_progress_sessions',
            },
        ),
        migrations.CreateModel(
            name='PushSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('endpoint', models.URLField()),
                ('p256dh_key', models.TextField()),
                ('auth_key', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'cv_push_subscriptions',
            },
        ),
        migrations.CreateModel(
            name='PWAInstallation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_type', models.CharField(choices=[('mobile', 'Mobile'), ('tablet', 'Tablet'), ('desktop', 'Desktop')], max_length=20)),
                ('platform', models.CharField(max_length=50)),
                ('browser', models.CharField(max_length=50)),
                ('installed_at', models.DateTimeField(auto_now_add=True)),
                ('last_used', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'cv_pwa_installations',
            },
        ),
        migrations.CreateModel(
            name='SecurityAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('suspicious_activity', 'Suspicious Activity'), ('brute_force', 'Brute Force Attack'), ('malicious_file', 'Malicious File Upload'), ('sql_injection', 'SQL Injection Attempt'), ('xss_attempt', 'XSS Attempt'), ('rate_limit_exceeded', 'Rate Limit Exceeded'), ('unauthorized_access', 'Unauthorized Access'), ('data_breach', 'Data Breach')], max_length=50)),
                ('severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=20)),
                ('status', models.CharField(choices=[('open', 'Open'), ('investigating', 'Investigating'), ('resolved', 'Resolved'), ('false_positive', 'False Positive')], default='open', max_length=20)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('request_path', models.TextField()),
                ('request_method', models.CharField(default='GET', max_length=10)),
                ('query_params', models.JSONField(blank=True, default=dict)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('description', models.TextField(blank=True)),
                ('details', models.JSONField(blank=True, default=dict)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('resolution_notes', models.TextField(blank=True)),
            ],
            options={
                'verbose_name': 'Security Alert',
                'verbose_name_plural': 'Security Alerts',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='SecurityAuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('login_attempt', 'Login Attempt'), ('login_success', 'Login Success'), ('login_failure', 'Login Failure'), ('logout', 'Logout'), ('password_change', 'Password Change'), ('permission_denied', 'Permission Denied'), ('file_upload', 'File Upload'), ('file_download', 'File Download'), ('data_access', 'Data Access'), ('admin_action', 'Admin Action')], max_length=50)),
                ('username', models.CharField(blank=True, max_length=150)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('request_path', models.TextField()),
                ('request_method', models.CharField(default='GET', max_length=10)),
                ('session_key', models.CharField(blank=True, max_length=40)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('details', models.JSONField(blank=True, default=dict)),
                ('success', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Security Audit Log',
                'verbose_name_plural': 'Security Audit Logs',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='SecurityEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('event_type', models.CharField(max_length=50)),
                ('severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], max_length=20)),
                ('ip_address', models.GenericIPAddressField()),
                ('request_path', models.CharField(default='', max_length=500)),
                ('threat_type', models.CharField(default='', max_length=50)),
                ('risk_score', models.IntegerField(default=0)),
                ('action_taken', models.CharField(default='none', max_length=50)),
                ('details', models.JSONField(default=dict)),
                ('resolved', models.BooleanField(default=False)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'security_events',
            },
        ),
        migrations.CreateModel(
            name='TaskMetricsLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_id', models.CharField(max_length=100, unique=True)),
                ('task_name', models.CharField(max_length=200)),
                ('priority', models.PositiveIntegerField(default=5)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('started', 'Started'), ('success', 'Success'), ('failure', 'Failure'), ('retry', 'Retry'), ('revoked', 'Revoked')], default='pending', max_length=20)),
                ('worker_id', models.CharField(blank=True, max_length=100)),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('duration_seconds', models.FloatField(blank=True, null=True)),
                ('retries', models.PositiveIntegerField(default=0)),
                ('error_message', models.TextField(blank=True)),
                ('metadata', models.JSONField(default=dict)),
            ],
            options={
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='TwoFactorAuth',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_enabled', models.BooleanField(default=False)),
                ('secret_key', models.CharField(blank=True, max_length=32)),
                ('backup_codes', models.JSONField(blank=True, default=list)),
                ('last_used_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Two Factor Authentication',
                'verbose_name_plural': 'Two Factor Authentication',
            },
        ),
        migrations.CreateModel(
            name='UserInteractionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(max_length=100)),
                ('interaction_type', models.CharField(max_length=50)),
                ('element_id', models.CharField(blank=True, max_length=100)),
                ('element_class', models.CharField(blank=True, max_length=100)),
                ('page_url', models.CharField(max_length=200)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('metadata', models.JSONField(default=dict)),
            ],
            options={
                'db_table': 'cv_user_interactions',
            },
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(max_length=40, unique=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('location', models.CharField(blank=True, max_length=200)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('expires_at', models.DateTimeField()),
            ],
            options={
                'verbose_name': 'User Session',
                'verbose_name_plural': 'User Sessions',
                'ordering': ['-last_activity'],
            },
        ),
        migrations.CreateModel(
            name='VulnerabilityReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scan_id', models.CharField(max_length=50, unique=True)),
                ('scan_date', models.DateTimeField(auto_now_add=True)),
                ('scan_type', models.CharField(max_length=50)),
                ('vulnerability_title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('risk_level', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], max_length=20)),
                ('affected_component', models.CharField(max_length=100)),
                ('recommendation', models.TextField()),
                ('fixed', models.BooleanField(default=False)),
                ('fixed_date', models.DateTimeField(blank=True, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
            ],
            options={
                'db_table': 'vulnerability_reports',
            },
        ),
        migrations.CreateModel(
            name='WorkflowInstance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('workflow_name', models.CharField(max_length=100)),
                ('workflow_id', models.CharField(max_length=200, unique=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('context_data', models.JSONField(default=dict)),
                ('current_step', models.PositiveIntegerField(default=0)),
                ('error_message', models.TextField(blank=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('initiated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        migrations.AddIndex(
            model_name='vulnerabilityreport',
            index=models.Index(fields=['scan_date', 'risk_level'], name='vulnerabili_scan_da_2da772_idx'),
        ),
        migrations.AddIndex(
            model_name='vulnerabilityreport',
            index=models.Index(fields=['scan_id'], name='vulnerabili_scan_id_d69183_idx'),
        ),
        migrations.AddField(
            model_name='usersession',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='userinteractionlog',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='twofactorauth',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='two_factor_auth', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='taskmetricslog',
            index=models.Index(fields=['task_name'], name='cv_analyzer_task_na_8664ef_idx'),
        ),
        migrations.AddIndex(
            model_name='taskmetricslog',
            index=models.Index(fields=['status'], name='cv_analyzer_status_c3bea8_idx'),
        ),
        migrations.AddIndex(
            model_name='taskmetricslog',
            index=models.Index(fields=['start_time'], name='cv_analyzer_start_t_531fe0_idx'),
        ),
        migrations.AddField(
            model_name='securityevent',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='securityauditlog',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='securityalert',
            name='resolved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='pwainstallation',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='pushsubscription',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='progresssession',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='offlinedata',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='notificationlog',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='monitoringmetric',
            index=models.Index(fields=['timestamp', 'metric_type'], name='monitoring__timesta_5d55dc_idx'),
        ),
        migrations.AddIndex(
            model_name='monitoringmetric',
            index=models.Index(fields=['metric_name'], name='monitoring__metric__0adb45_idx'),
        ),
        migrations.AddIndex(
            model_name='healthcheckresult',
            index=models.Index(fields=['timestamp', 'check_name'], name='health_chec_timesta_6440b0_idx'),
        ),
        migrations.AddIndex(
            model_name='healthcheckresult',
            index=models.Index(fields=['status'], name='health_chec_status_55814b_idx'),
        ),
        migrations.AddField(
            model_name='frontendperformancemetric',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='fileuploadlog',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='encrypteddata',
            name='content_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='deploymentlog',
            name='deployed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='datasubjectrequest',
            name='processed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_requests', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='datasubjectrequest',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='data_requests', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='dataprocessingconsent',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='consents', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='businessruleconfig',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='businessauditlog',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='auditlog',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='apiusagelog',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='aiprovidermetrics',
            unique_together={('provider_name', 'date')},
        ),
        migrations.AddIndex(
            model_name='workflowinstance',
            index=models.Index(fields=['workflow_name'], name='cv_analyzer_workflo_44cf70_idx'),
        ),
        migrations.AddIndex(
            model_name='workflowinstance',
            index=models.Index(fields=['status'], name='cv_analyzer_status_2746ed_idx'),
        ),
        migrations.AddIndex(
            model_name='workflowinstance',
            index=models.Index(fields=['started_at'], name='cv_analyzer_started_44795f_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['user', 'is_active'], name='cv_analyzer_user_id_98ada0_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['session_key'], name='cv_analyzer_session_94256c_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['expires_at'], name='cv_analyzer_expires_586560_idx'),
        ),
        migrations.AddIndex(
            model_name='securityevent',
            index=models.Index(fields=['timestamp', 'severity'], name='security_ev_timesta_aefd99_idx'),
        ),
        migrations.AddIndex(
            model_name='securityevent',
            index=models.Index(fields=['ip_address'], name='security_ev_ip_addr_286f33_idx'),
        ),
        migrations.AddIndex(
            model_name='securityevent',
            index=models.Index(fields=['event_type'], name='security_ev_event_t_77edde_idx'),
        ),
        migrations.AddIndex(
            model_name='securityauditlog',
            index=models.Index(fields=['event_type', 'timestamp'], name='cv_analyzer_event_t_fe3a38_idx'),
        ),
        migrations.AddIndex(
            model_name='securityauditlog',
            index=models.Index(fields=['ip_address', 'timestamp'], name='cv_analyzer_ip_addr_ee8de1_idx'),
        ),
        migrations.AddIndex(
            model_name='securityauditlog',
            index=models.Index(fields=['username', 'timestamp'], name='cv_analyzer_usernam_a0debd_idx'),
        ),
        migrations.AddIndex(
            model_name='securityalert',
            index=models.Index(fields=['alert_type', 'severity', 'timestamp'], name='cv_analyzer_alert_t_46acf7_idx'),
        ),
        migrations.AddIndex(
            model_name='securityalert',
            index=models.Index(fields=['ip_address', 'timestamp'], name='cv_analyzer_ip_addr_468f8f_idx'),
        ),
        migrations.AddIndex(
            model_name='securityalert',
            index=models.Index(fields=['status', 'timestamp'], name='cv_analyzer_status_63b298_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='pwainstallation',
            unique_together={('user', 'device_type', 'platform')},
        ),
        migrations.AlterUniqueTogether(
            name='pushsubscription',
            unique_together={('user', 'endpoint')},
        ),
        migrations.AlterUniqueTogether(
            name='offlinedata',
            unique_together={('user', 'data_type', 'object_id')},
        ),
        migrations.AddIndex(
            model_name='fileuploadlog',
            index=models.Index(fields=['user', 'uploaded_at'], name='cv_analyzer_user_id_4ac510_idx'),
        ),
        migrations.AddIndex(
            model_name='fileuploadlog',
            index=models.Index(fields=['status', 'uploaded_at'], name='cv_analyzer_status_f2fb2b_idx'),
        ),
        migrations.AddIndex(
            model_name='fileuploadlog',
            index=models.Index(fields=['md5_hash'], name='cv_analyzer_md5_has_3eaeec_idx'),
        ),
        migrations.AddIndex(
            model_name='fileuploadlog',
            index=models.Index(fields=['sha256_hash'], name='cv_analyzer_sha256__2fb492_idx'),
        ),
        migrations.AddIndex(
            model_name='encrypteddata',
            index=models.Index(fields=['content_type', 'object_id'], name='cv_analyzer_content_4ea5bd_idx'),
        ),
        migrations.AddIndex(
            model_name='encrypteddata',
            index=models.Index(fields=['hash_value'], name='cv_analyzer_hash_va_a3f90d_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='encrypteddata',
            unique_together={('content_type', 'object_id', 'field_name')},
        ),
        migrations.AddIndex(
            model_name='deploymentlog',
            index=models.Index(fields=['started_at', 'environment'], name='deployment__started_7efca0_idx'),
        ),
        migrations.AddIndex(
            model_name='deploymentlog',
            index=models.Index(fields=['status'], name='deployment__status_acf83c_idx'),
        ),
        migrations.AddIndex(
            model_name='datasubjectrequest',
            index=models.Index(fields=['user', 'request_type'], name='cv_analyzer_user_id_1d894c_idx'),
        ),
        migrations.AddIndex(
            model_name='datasubjectrequest',
            index=models.Index(fields=['request_date'], name='cv_analyzer_request_85a3d8_idx'),
        ),
        migrations.AddIndex(
            model_name='datasubjectrequest',
            index=models.Index(fields=['status'], name='cv_analyzer_status_4ed653_idx'),
        ),
        migrations.AddIndex(
            model_name='dataprocessingconsent',
            index=models.Index(fields=['user', 'consent_type'], name='cv_analyzer_user_id_24789f_idx'),
        ),
        migrations.AddIndex(
            model_name='dataprocessingconsent',
            index=models.Index(fields=['consent_date'], name='cv_analyzer_consent_ab8613_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='dataprocessingconsent',
            unique_together={('user', 'consent_type')},
        ),
        migrations.AddIndex(
            model_name='businessauditlog',
            index=models.Index(fields=['entity_type', 'entity_id'], name='cv_analyzer_entity__35cb7e_idx'),
        ),
        migrations.AddIndex(
            model_name='businessauditlog',
            index=models.Index(fields=['event_type'], name='cv_analyzer_event_t_a3e3c9_idx'),
        ),
        migrations.AddIndex(
            model_name='businessauditlog',
            index=models.Index(fields=['timestamp'], name='cv_analyzer_timesta_d50d13_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['timestamp', 'event_type'], name='audit_logs_timesta_da0de5_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['user', 'timestamp'], name='audit_logs_user_id_88267f_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['ip_address'], name='audit_logs_ip_addr_919cb4_idx'),
        ),
        migrations.AddIndex(
            model_name='apiusagelog',
            index=models.Index(fields=['endpoint'], name='cv_analyzer_endpoin_1d0ebb_idx'),
        ),
        migrations.AddIndex(
            model_name='apiusagelog',
            index=models.Index(fields=['timestamp'], name='cv_analyzer_timesta_c284c3_idx'),
        ),
        migrations.AddIndex(
            model_name='apiusagelog',
            index=models.Index(fields=['status_code'], name='cv_analyzer_status__81fe02_idx'),
        ),
    ]
