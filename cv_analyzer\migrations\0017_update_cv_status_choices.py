# Generated by Django 4.2 on 2025-06-20 07:37

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cv_analyzer', '0016_alter_aiapiconfig_provider'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cv',
            name='file',
            field=models.FileField(upload_to='cvs/', validators=[django.core.validators.FileExtensionValidator(['pdf', 'doc', 'docx'])]),
        ),
        migrations.AlterField(
            model_name='cv',
            name='status',
            field=models.CharField(choices=[('uploaded', 'Uploaded'), ('processing', 'Processing'), ('analyzed', 'Analyzed'), ('matched', 'Matched'), ('rejected', 'Rejected'), ('archived', 'Archived')], default='uploaded', max_length=20),
        ),
    ]
