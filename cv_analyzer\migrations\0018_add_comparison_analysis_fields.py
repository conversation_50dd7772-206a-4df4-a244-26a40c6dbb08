# Generated by Django 4.2 on 2025-06-20 16:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cv_analyzer', '0017_update_cv_status_choices'),
    ]

    operations = [
        migrations.AddField(
            model_name='comparisonanalysis',
            name='analysis_details',
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AddField(
            model_name='comparisonanalysis',
            name='recommendation_level',
            field=models.CharField(default='Consider', max_length=50),
        ),
        migrations.AddField(
            model_name='comparisonanalysis',
            name='skills_match_percentage',
            field=models.FloatField(default=0.0),
        ),
    ]
