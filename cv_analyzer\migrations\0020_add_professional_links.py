# Generated by Django 4.2 on 2025-06-26 07:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cv_analyzer', '0019_alter_cv_applicant_profile'),
    ]

    operations = [
        migrations.AddField(
            model_name='cvanalysis',
            name='github_profile',
            field=models.URLField(blank=True, help_text='GitHub profile URL'),
        ),
        migrations.AddField(
            model_name='cvanalysis',
            name='linkedin_profile',
            field=models.URLField(blank=True, help_text='LinkedIn profile URL'),
        ),
        migrations.AddField(
            model_name='cvanalysis',
            name='other_links',
            field=models.JSONField(blank=True, default=list, help_text='Other professional links'),
        ),
        migrations.AddField(
            model_name='cvanalysis',
            name='website_portfolio',
            field=models.URLField(blank=True, help_text='Personal website or portfolio URL'),
        ),
    ]
