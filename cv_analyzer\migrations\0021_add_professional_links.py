# Generated by Django 4.2 on 2025-06-26 07:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cv_analyzer', '0020_add_professional_links'),
    ]

    operations = [
        migrations.AddField(
            model_name='cvanalysis',
            name='linkedin_profile',
            field=models.URLField(blank=True, help_text="LinkedIn profile URL"),
        ),
        migrations.AddField(
            model_name='cvanalysis',
            name='website_portfolio',
            field=models.URLField(blank=True, help_text="Personal website or portfolio URL"),
        ),
        migrations.AddField(
            model_name='cvanalysis',
            name='github_profile',
            field=models.URLField(blank=True, help_text="GitHub profile URL"),
        ),
        migrations.AddField(
            model_name='cvanalysis',
            name='other_links',
            field=models.J<PERSON><PERSON>ield(blank=True, default=list, help_text="Other professional links"),
        ),
    ]
