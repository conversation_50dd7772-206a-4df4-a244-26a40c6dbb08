# Generated by Django 4.2 on 2025-06-27 13:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cv_analyzer', '0022_remove_job_model'),
    ]

    operations = [
        migrations.AddField(
            model_name='cvanalysis',
            name='certifications_data',
            field=models.JSONField(blank=True, default=dict, help_text='Structured certifications data'),
        ),
        migrations.AddField(
            model_name='cvanalysis',
            name='education_data',
            field=models.JSONField(blank=True, default=dict, help_text='Structured education data'),
        ),
        migrations.AddField(
            model_name='cvanalysis',
            name='experience_data',
            field=models.JSONField(blank=True, default=dict, help_text='Structured work experience data'),
        ),
    ]
