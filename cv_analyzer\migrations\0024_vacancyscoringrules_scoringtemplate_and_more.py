# Generated by Django 4.2.23 on 2025-07-03 05:29

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("cv_analyzer", "0023_add_enhanced_cv_analysis_fields"),
    ]

    operations = [
        migrations.CreateModel(
            name="VacancyScoringRules",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "education_weight",
                    models.FloatField(
                        default=25.0,
                        help_text="Weight for education scoring (0-100%)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "experience_weight",
                    models.FloatField(
                        default=35.0,
                        help_text="Weight for experience scoring (0-100%)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "skills_weight",
                    models.FloatField(
                        default=30.0,
                        help_text="Weight for skills scoring (0-100%)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "responsibilities_weight",
                    models.FloatField(
                        default=10.0,
                        help_text="Weight for responsibilities scoring (0-100%)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "education_requirements",
                    models.JSONField(
                        default=dict,
                        help_text="Education requirements and scoring rules",
                    ),
                ),
                (
                    "experience_requirements",
                    models.JSONField(
                        default=dict,
                        help_text="Experience requirements and scoring rules",
                    ),
                ),
                (
                    "skills_requirements",
                    models.JSONField(
                        default=dict, help_text="Skills requirements and scoring rules"
                    ),
                ),
                (
                    "responsibilities_requirements",
                    models.JSONField(
                        default=dict,
                        help_text="Responsibilities requirements and scoring rules",
                    ),
                ),
                (
                    "enable_custom_scoring",
                    models.BooleanField(
                        default=True,
                        help_text="Enable custom weighted scoring for this vacancy",
                    ),
                ),
                (
                    "minimum_education_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Minimum education score required (0-100%)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "minimum_experience_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Minimum experience score required (0-100%)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "minimum_skills_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Minimum skills score required (0-100%)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "minimum_overall_score",
                    models.FloatField(
                        default=50.0,
                        help_text="Minimum overall score required (0-100%)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "vacancy",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="scoring_rules",
                        to="cv_analyzer.vacancy",
                    ),
                ),
            ],
            options={
                "verbose_name": "Vacancy Scoring Rules",
                "verbose_name_plural": "Vacancy Scoring Rules",
                "db_table": "cv_vacancy_scoring_rules",
            },
        ),
        migrations.CreateModel(
            name="ScoringTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("category", models.CharField(max_length=50)),
                ("description", models.TextField(blank=True)),
                (
                    "education_weight",
                    models.FloatField(
                        default=25.0,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "experience_weight",
                    models.FloatField(
                        default=35.0,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "skills_weight",
                    models.FloatField(
                        default=30.0,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "responsibilities_weight",
                    models.FloatField(
                        default=10.0,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                ("education_requirements", models.JSONField(default=dict)),
                ("experience_requirements", models.JSONField(default=dict)),
                ("skills_requirements", models.JSONField(default=dict)),
                ("responsibilities_requirements", models.JSONField(default=dict)),
                ("is_default", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Scoring Template",
                "verbose_name_plural": "Scoring Templates",
                "db_table": "cv_scoring_templates",
            },
        ),
        migrations.CreateModel(
            name="CustomScoringResult",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "education_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Education component score (0-100%)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "experience_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Experience component score (0-100%)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "skills_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Skills component score (0-100%)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "responsibilities_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Responsibilities component score (0-100%)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "weighted_education_score",
                    models.FloatField(
                        default=0.0, help_text="Education score * education weight"
                    ),
                ),
                (
                    "weighted_experience_score",
                    models.FloatField(
                        default=0.0, help_text="Experience score * experience weight"
                    ),
                ),
                (
                    "weighted_skills_score",
                    models.FloatField(
                        default=0.0, help_text="Skills score * skills weight"
                    ),
                ),
                (
                    "weighted_responsibilities_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Responsibilities score * responsibilities weight",
                    ),
                ),
                (
                    "final_custom_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Final weighted custom score (0-100%)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "score_breakdown",
                    models.JSONField(
                        default=dict,
                        help_text="Detailed breakdown of score calculations",
                    ),
                ),
                (
                    "meets_minimum_requirements",
                    models.BooleanField(
                        default=False,
                        help_text="Whether candidate meets minimum score requirements",
                    ),
                ),
                (
                    "custom_recommendation",
                    models.CharField(
                        choices=[
                            ("highly_recommended", "Highly Recommended"),
                            ("recommended", "Recommended"),
                            ("consider", "Consider"),
                            ("not_recommended", "Not Recommended"),
                        ],
                        default="consider",
                        max_length=50,
                    ),
                ),
                (
                    "ai_vs_custom_difference",
                    models.FloatField(
                        default=0.0,
                        help_text="Difference between AI score and custom score",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "cv_analysis",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="custom_scoring",
                        to="cv_analyzer.cvanalysis",
                    ),
                ),
                (
                    "vacancy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="custom_scores",
                        to="cv_analyzer.vacancy",
                    ),
                ),
            ],
            options={
                "verbose_name": "Custom Scoring Result",
                "verbose_name_plural": "Custom Scoring Results",
                "db_table": "cv_custom_scoring_results",
                "unique_together": {("cv_analysis", "vacancy")},
            },
        ),
    ]
