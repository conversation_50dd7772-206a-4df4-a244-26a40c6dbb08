# Generated by Django 4.2.20 on 2025-07-04 03:15

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("cv_analyzer", "0024_vacancyscoringrules_scoringtemplate_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="cvanalysis",
            name="content_analysis",
            field=models.TextField(
                blank=True, default="", help_text="Detailed content analysis text"
            ),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="format_analysis",
            field=models.TextField(
                blank=True, default="", help_text="Detailed format analysis text"
            ),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="sections_analysis",
            field=models.TextField(
                blank=True, default="", help_text="Detailed sections analysis text"
            ),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="skills_analysis",
            field=models.TextField(
                blank=True, default="", help_text="Detailed skills analysis text"
            ),
        ),
        migrations.AddField(
            model_name="cvanalysis",
            name="style_analysis",
            field=models.TextField(
                blank=True, default="", help_text="Detailed style analysis text"
            ),
        ),
        migrations.AlterField(
            model_name="customscoringresult",
            name="cv_analysis",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="custom_scoring_results",
                to="cv_analyzer.cvanalysis",
            ),
        ),
    ]
