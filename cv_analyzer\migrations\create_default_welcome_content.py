from django.db import migrations
from django.core.files.base import ContentFile

def create_default_welcome_content(apps, schema_editor):
    WelcomeContent = apps.get_model('cv_analyzer', 'WelcomeContent')
    if not WelcomeContent.objects.exists():
        welcome_content = WelcomeContent(
            welcome_message="Welcome to CV Analyzer! Our advanced AI-powered tool helps you streamline your recruitment process by analyzing and evaluating CVs quickly and efficiently.",
            step_1_title="Upload CVs",
            step_1_description="Start by uploading multiple CVs for analysis. We support various file formats including PDF and Word documents.",
            step_2_title="Manage Vacancies",
            step_2_description="Create and manage job vacancies with detailed descriptions and requirements to match against candidate profiles.",
            step_3_title="AI Analysis",
            step_3_description="Our AI engine processes the CVs, extracting key information and evaluating them against job requirements for each vacancy.",
            step_4_title="View Results",
            step_4_description="Access comprehensive analysis reports, including compatibility scores, key skills, and areas for improvement for each candidate.",
            step_1_image=ContentFile(b"dummy image content"),
            step_2_image=ContentFile(b"dummy image content"),
            step_3_image=ContentFile(b"dummy image content"),
            step_4_image=ContentFile(b"dummy image content")
        )
        
        welcome_content.save()

def reverse_default_welcome_content(apps, schema_editor):
    WelcomeContent = apps.get_model('cv_analyzer', 'WelcomeContent')
    WelcomeContent.objects.filter(
        welcome_message="Welcome to CV Analyzer! Our advanced AI-powered tool helps you streamline your recruitment process by analyzing and evaluating CVs quickly and efficiently."
    ).delete()

class Migration(migrations.Migration):

    dependencies = [
        ('cv_analyzer', '0003_analysisprocess'),
    ]

    operations = [
        migrations.RunPython(create_default_welcome_content, reverse_default_welcome_content),
    ]