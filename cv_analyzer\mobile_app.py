"""
Mobile Application System for CV Analyzer
Provides mobile app backend support, API endpoints, and mobile-specific features.
"""

import json
import logging
import base64
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from django.conf import settings
from django.contrib.auth.models import User
from django.db import transaction
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth import authenticate
from django.core.cache import cache
from django.utils import timezone
from rest_framework.authtoken.models import Token
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .models import (
    PWAInstallation, PushSubscription, NotificationLog, OfflineData,
    CV, CVAnalysis, Company, Vacancy, ApplicantProfile
)

logger = logging.getLogger(__name__)

@dataclass
class MobileConfig:
    """Mobile app configuration"""
    app_name: str = "CV Analyzer"
    app_version: str = "1.0.0"
    api_version: str = "v1"
    supported_platforms: List[str] = None
    push_notification_enabled: bool = True
    offline_sync_enabled: bool = True
    max_offline_storage_days: int = 7
    
    def __post_init__(self):
        if self.supported_platforms is None:
            self.supported_platforms = ['iOS', 'Android']

class MobileAppManager:
    """Main mobile application manager"""
    
    def __init__(self):
        self.config = MobileConfig()
        self.cache_timeout = 300  # 5 minutes for mobile data
    
    def get_mobile_config(self) -> Dict[str, Any]:
        """Get mobile app configuration"""
        return {
            'app_name': self.config.app_name,
            'app_version': self.config.app_version,
            'api_version': self.config.api_version,
            'supported_platforms': self.config.supported_platforms,
            'features': {
                'push_notifications': self.config.push_notification_enabled,
                'offline_sync': self.config.offline_sync_enabled,
                'biometric_auth': True,
                'dark_mode': True,
                'file_upload': True,
                'cv_analysis': True,
                'job_matching': True
            },
            'api_endpoints': {
                'base_url': '/api/mobile/',
                'auth': '/api/mobile/auth/',
                'upload': '/api/mobile/upload/',
                'sync': '/api/mobile/sync/',
                'notifications': '/api/mobile/notifications/'
            },
            'limits': {
                'max_file_size_mb': 10,
                'max_uploads_per_day': 50,
                'offline_storage_days': self.config.max_offline_storage_days
            }
        }
    
    def register_installation(self, user: User, device_info: Dict[str, Any]) -> PWAInstallation:
        """Register mobile app installation"""
        try:
            installation, created = PWAInstallation.objects.get_or_create(
                user=user,
                device_type=device_info.get('device_type', 'mobile'),
                platform=device_info.get('platform', 'unknown'),
                defaults={
                    'browser': device_info.get('browser', 'mobile_app'),
                    'installed_at': timezone.now(),
                    'is_active': True
                }
            )
            
            if not created:
                installation.last_used = timezone.now()
                installation.is_active = True
                installation.save()
            
            logger.info(f"Mobile installation registered for user {user.username}")
            return installation
            
        except Exception as e:
            logger.error(f"Error registering mobile installation: {str(e)}")
            raise
    
    def get_user_mobile_data(self, user: User) -> Dict[str, Any]:
        """Get mobile-optimized user data"""
        try:
            cache_key = f"mobile_user_data_{user.id}"
            cached_data = cache.get(cache_key)
            
            if cached_data:
                return cached_data
            
            # Get user profile
            try:
                profile = ApplicantProfile.objects.get(user=user)
                profile_data = {
                    'phone_number': profile.phone_number,
                    'address': profile.address,
                    'linkedin_profile': profile.linkedin_profile,
                    'complete': profile.complete
                }
            except ApplicantProfile.DoesNotExist:
                profile_data = None
            
            # Get user's CVs (limited for mobile)
            cvs = CV.objects.filter(
                applicant_profile__user=user
            ).order_by('-uploaded_at')[:5]
            
            cv_data = []
            for cv in cvs:
                try:
                    analysis = cv.analysis
                    cv_data.append({
                        'id': cv.id,
                        'status': cv.status,
                        'uploaded_at': cv.uploaded_at.isoformat(),
                        'source': cv.source,
                        'overall_score': analysis.overall_score if analysis else 0,
                        'file_name': cv.file.name.split('/')[-1] if cv.file else None
                    })
                except CVAnalysis.DoesNotExist:
                    cv_data.append({
                        'id': cv.id,
                        'status': cv.status,
                        'uploaded_at': cv.uploaded_at.isoformat(),
                        'source': cv.source,
                        'overall_score': 0,
                        'file_name': cv.file.name.split('/')[-1] if cv.file else None
                    })
            
            # Get recent applications/matches
            recent_vacancies = Vacancy.objects.filter(
                status='active'
            ).order_by('-created_at')[:10]
            
            vacancy_data = [{
                'id': v.id,
                'title': v.title,
                'company': v.company.name,
                'industry': v.company.industry,
                'created_at': v.created_at.isoformat()
            } for v in recent_vacancies]
            
            mobile_data = {
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'date_joined': user.date_joined.isoformat()
                },
                'profile': profile_data,
                'cvs': cv_data,
                'recent_vacancies': vacancy_data,
                'stats': {
                    'total_cvs': len(cv_data),
                    'avg_score': sum(cv['overall_score'] for cv in cv_data) / max(len(cv_data), 1),
                    'available_jobs': len(vacancy_data)
                },
                'last_updated': timezone.now().isoformat()
            }
            
            # Cache the data
            cache.set(cache_key, mobile_data, self.cache_timeout)
            
            return mobile_data
            
        except Exception as e:
            logger.error(f"Error getting mobile user data: {str(e)}")
            raise

class MobilePushNotificationManager:
    """Manage push notifications for mobile app"""
    
    def __init__(self):
        self.vapid_public_key = getattr(settings, 'VAPID_PUBLIC_KEY', '')
        self.vapid_private_key = getattr(settings, 'VAPID_PRIVATE_KEY', '')
    
    def subscribe_user(self, user: User, subscription_data: Dict[str, Any]) -> PushSubscription:
        """Subscribe user to push notifications"""
        try:
            subscription, created = PushSubscription.objects.get_or_create(
                user=user,
                endpoint=subscription_data['endpoint'],
                defaults={
                    'p256dh_key': subscription_data['keys']['p256dh'],
                    'auth_key': subscription_data['keys']['auth'],
                    'is_active': True
                }
            )
            
            if not created and not subscription.is_active:
                subscription.is_active = True
                subscription.save()
            
            logger.info(f"Push subscription created for user {user.username}")
            return subscription
            
        except Exception as e:
            logger.error(f"Error creating push subscription: {str(e)}")
            raise
    
    def send_notification(self, user: User, title: str, message: str, 
                         notification_type: str = 'general', data: Dict[str, Any] = None) -> bool:
        """Send push notification to user"""
        try:
            # Get user's active subscriptions
            subscriptions = PushSubscription.objects.filter(
                user=user,
                is_active=True
            )
            
            if not subscriptions.exists():
                logger.warning(f"No active push subscriptions for user {user.username}")
                return False
            
            notification_payload = {
                'title': title,
                'body': message,
                'icon': '/static/icons/icon-192.png',
                'badge': '/static/icons/badge-72.png',
                'data': data or {},
                'actions': self._get_notification_actions(notification_type)
            }
            
            success_count = 0
            
            for subscription in subscriptions:
                try:
                    # Send notification using web push library
                    # This would require pywebpush library
                    success = self._send_web_push(subscription, notification_payload)
                    
                    if success:
                        success_count += 1
                    
                except Exception as e:
                    logger.error(f"Failed to send notification to subscription {subscription.id}: {str(e)}")
                    # Deactivate failed subscription
                    subscription.is_active = False
                    subscription.save()
            
            # Log notification
            NotificationLog.objects.create(
                user=user,
                notification_type=notification_type,
                title=title,
                message=message,
                delivery_status='sent' if success_count > 0 else 'failed'
            )
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error sending push notification: {str(e)}")
            return False
    
    def _send_web_push(self, subscription: PushSubscription, payload: Dict[str, Any]) -> bool:
        """Send web push notification (placeholder for actual implementation)"""
        try:
            # This would use pywebpush library in real implementation
            # from pywebpush import webpush, WebPushException
            
            # webpush(
            #     subscription_info={
            #         "endpoint": subscription.endpoint,
            #         "keys": {
            #             "p256dh": subscription.p256dh_key,
            #             "auth": subscription.auth_key
            #         }
            #     },
            #     data=json.dumps(payload),
            #     vapid_private_key=self.vapid_private_key,
            #     vapid_claims={"sub": "mailto:<EMAIL>"}
            # )
            
            # For now, just log the attempt
            logger.info(f"Push notification sent to endpoint: {subscription.endpoint[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Web push failed: {str(e)}")
            return False
    
    def _get_notification_actions(self, notification_type: str) -> List[Dict[str, str]]:
        """Get notification actions based on type"""
        actions_map = {
            'cv_analysis_complete': [
                {'action': 'view', 'title': 'View Results', 'icon': '/static/icons/view.png'},
                {'action': 'share', 'title': 'Share', 'icon': '/static/icons/share.png'}
            ],
            'job_match': [
                {'action': 'apply', 'title': 'Apply Now', 'icon': '/static/icons/apply.png'},
                {'action': 'save', 'title': 'Save Job', 'icon': '/static/icons/save.png'}
            ],
            'general': [
                {'action': 'view', 'title': 'View', 'icon': '/static/icons/view.png'}
            ]
        }
        
        return actions_map.get(notification_type, actions_map['general'])

class MobileOfflineManager:
    """Manage offline functionality for mobile app"""
    
    def __init__(self):
        self.max_offline_days = 7
    
    def sync_offline_data(self, user: User, device_data: Dict[str, Any]) -> Dict[str, Any]:
        """Sync offline data with server"""
        try:
            sync_result = {
                'uploaded': 0,
                'downloaded': 0,
                'conflicts': 0,
                'errors': []
            }
            
            # Process uploaded data from mobile
            if 'uploads' in device_data:
                for upload_item in device_data['uploads']:
                    try:
                        self._process_offline_upload(user, upload_item)
                        sync_result['uploaded'] += 1
                    except Exception as e:
                        sync_result['errors'].append(f"Upload failed: {str(e)}")
            
            # Prepare data for download to mobile
            downloadable_data = self._get_downloadable_data(user, device_data.get('last_sync'))
            sync_result['downloaded'] = len(downloadable_data.get('items', []))
            
            # Update sync timestamp
            self._update_last_sync(user, timezone.now())
            
            return {
                'status': 'success',
                'sync_result': sync_result,
                'data': downloadable_data,
                'sync_timestamp': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error syncing offline data: {str(e)}")
            return {
                'status': 'error',
                'message': str(e),
                'sync_timestamp': timezone.now().isoformat()
            }
    
    def _process_offline_upload(self, user: User, upload_item: Dict[str, Any]):
        """Process an offline upload item"""
        item_type = upload_item.get('type')
        
        if item_type == 'cv_analysis_feedback':
            # Process CV analysis feedback
            self._process_cv_feedback(user, upload_item['data'])
        elif item_type == 'job_application':
            # Process job application
            self._process_job_application(user, upload_item['data'])
        elif item_type == 'profile_update':
            # Process profile update
            self._process_profile_update(user, upload_item['data'])
        else:
            raise ValueError(f"Unknown upload type: {item_type}")
    
    def _get_downloadable_data(self, user: User, last_sync: str = None) -> Dict[str, Any]:
        """Get data that should be downloaded to mobile device"""
        try:
            if last_sync:
                last_sync_dt = datetime.fromisoformat(last_sync.replace('Z', '+00:00'))
            else:
                last_sync_dt = timezone.now() - timedelta(days=self.max_offline_days)
            
            # Get updated CVs
            updated_cvs = CV.objects.filter(
                applicant_profile__user=user,
                uploaded_at__gte=last_sync_dt
            ).select_related('analysis')
            
            # Get new job matches
            new_vacancies = Vacancy.objects.filter(
                status='active',
                created_at__gte=last_sync_dt
            ).select_related('company')[:20]
            
            downloadable_data = {
                'items': [],
                'cvs': [
                    {
                        'id': cv.id,
                        'status': cv.status,
                        'overall_score': cv.analysis.overall_score if hasattr(cv, 'analysis') else 0,
                        'uploaded_at': cv.uploaded_at.isoformat()
                    }
                    for cv in updated_cvs
                ],
                'vacancies': [
                    {
                        'id': v.id,
                        'title': v.title,
                        'company': v.company.name,
                        'industry': v.company.industry,
                        'description': v.description[:200] + '...' if len(v.description) > 200 else v.description
                    }
                    for v in new_vacancies
                ]
            }
            
            return downloadable_data
            
        except Exception as e:
            logger.error(f"Error getting downloadable data: {str(e)}")
            return {'items': [], 'cvs': [], 'vacancies': []}
    
    def _process_cv_feedback(self, user: User, feedback_data: Dict[str, Any]):
        """Process CV analysis feedback from mobile"""
        # Implementation for processing CV feedback
        pass
    
    def _process_job_application(self, user: User, application_data: Dict[str, Any]):
        """Process job application from mobile"""
        # Implementation for processing job application
        pass
    
    def _process_profile_update(self, user: User, profile_data: Dict[str, Any]):
        """Process profile update from mobile"""
        try:
            profile, created = ApplicantProfile.objects.get_or_create(user=user)
            
            # Update allowed fields
            allowed_fields = ['phone_number', 'address', 'linkedin_profile']
            for field in allowed_fields:
                if field in profile_data:
                    setattr(profile, field, profile_data[field])
            
            profile.save()
            
        except Exception as e:
            logger.error(f"Error processing profile update: {str(e)}")
            raise
    
    def _update_last_sync(self, user: User, sync_time: datetime):
        """Update last sync time for user"""
        cache.set(f"last_sync_{user.id}", sync_time.isoformat(), 86400 * 30)  # 30 days

class MobileAPIViews:
    """Mobile-specific API views"""
    
    @staticmethod
    @csrf_exempt
    @require_http_methods(["POST"])
    def mobile_auth(request):
        """Mobile authentication endpoint"""
        try:
            data = json.loads(request.body)
            username = data.get('username')
            password = data.get('password')
            device_info = data.get('device_info', {})
            
            if not username or not password:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Username and password required'
                }, status=400)
            
            user = authenticate(username=username, password=password)
            
            if user is None:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Invalid credentials'
                }, status=401)
            
            if not user.is_active:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Account is disabled'
                }, status=401)
            
            # Create or get authentication token
            token, created = Token.objects.get_or_create(user=user)
            
            # Register mobile installation
            mobile_manager = MobileAppManager()
            installation = mobile_manager.register_installation(user, device_info)
            
            # Get mobile user data
            user_data = mobile_manager.get_user_mobile_data(user)
            
            return JsonResponse({
                'status': 'success',
                'data': {
                    'token': token.key,
                    'user': user_data,
                    'installation_id': installation.id
                }
            })
            
        except Exception as e:
            logger.error(f"Mobile auth error: {str(e)}")
            return JsonResponse({
                'status': 'error',
                'message': 'Authentication failed'
            }, status=500)
    
    @staticmethod
    @api_view(['GET'])
    @permission_classes([IsAuthenticated])
    def mobile_config(request):
        """Get mobile app configuration"""
        try:
            mobile_manager = MobileAppManager()
            config = mobile_manager.get_mobile_config()
            
            return Response({
                'status': 'success',
                'data': config
            })
            
        except Exception as e:
            logger.error(f"Mobile config error: {str(e)}")
            return Response({
                'status': 'error',
                'message': 'Failed to get config'
            }, status=500)
    
    @staticmethod
    @api_view(['POST'])
    @permission_classes([IsAuthenticated])
    def mobile_push_subscribe(request):
        """Subscribe to push notifications"""
        try:
            subscription_data = request.data.get('subscription')
            
            if not subscription_data:
                return Response({
                    'status': 'error',
                    'message': 'Subscription data required'
                }, status=400)
            
            push_manager = MobilePushNotificationManager()
            subscription = push_manager.subscribe_user(request.user, subscription_data)
            
            return Response({
                'status': 'success',
                'data': {
                    'subscription_id': subscription.id,
                    'message': 'Successfully subscribed to push notifications'
                }
            })
            
        except Exception as e:
            logger.error(f"Push subscribe error: {str(e)}")
            return Response({
                'status': 'error',
                'message': 'Failed to subscribe'
            }, status=500)
    
    @staticmethod
    @api_view(['POST'])
    @permission_classes([IsAuthenticated])
    def mobile_sync(request):
        """Sync offline data"""
        try:
            device_data = request.data.get('data', {})
            
            offline_manager = MobileOfflineManager()
            sync_result = offline_manager.sync_offline_data(request.user, device_data)
            
            return Response(sync_result)
            
        except Exception as e:
            logger.error(f"Mobile sync error: {str(e)}")
            return Response({
                'status': 'error',
                'message': 'Sync failed'
            }, status=500)

# Mobile App Notification System
class MobileNotificationService:
    """Service for sending mobile notifications"""
    
    def __init__(self):
        self.push_manager = MobilePushNotificationManager()
    
    def notify_cv_analysis_complete(self, user: User, cv_id: int, overall_score: int):
        """Notify user that CV analysis is complete"""
        title = "CV Analysis Complete!"
        message = f"Your CV analysis is ready with a score of {overall_score}/100"
        
        self.push_manager.send_notification(
            user=user,
            title=title,
            message=message,
            notification_type='cv_analysis_complete',
            data={'cv_id': cv_id, 'score': overall_score}
        )
    
    def notify_job_match(self, user: User, vacancy_id: int, company_name: str, job_title: str):
        """Notify user about a job match"""
        title = "New Job Match!"
        message = f"We found a great match: {job_title} at {company_name}"
        
        self.push_manager.send_notification(
            user=user,
            title=title,
            message=message,
            notification_type='job_match',
            data={'vacancy_id': vacancy_id, 'company': company_name}
        )
    
    def notify_system_update(self, title: str, message: str):
        """Send system update notification to all mobile users"""
        mobile_users = User.objects.filter(
            pwainstallation__is_active=True
        ).distinct()
        
        for user in mobile_users:
            self.push_manager.send_notification(
                user=user,
                title=title,
                message=message,
                notification_type='system_update'
            )

# Usage Examples:
"""
# Initialize mobile app manager
mobile_manager = MobileAppManager()

# Get mobile configuration
config = mobile_manager.get_mobile_config()

# Register mobile installation
installation = mobile_manager.register_installation(user, device_info)

# Send push notification
notification_service = MobileNotificationService()
notification_service.notify_cv_analysis_complete(user, cv_id=123, overall_score=85)

# Sync offline data
offline_manager = MobileOfflineManager()
sync_result = offline_manager.sync_offline_data(user, device_data)
""" 