from django.db import models
from django.contrib.auth.models import User
from django.core.validators import FileExtensionValidator
from django.db.models import Count
from django.conf import settings
from pocketgroq import GroqProvider
from django.utils import timezone
import uuid
import json
from django.core.validators import MinValueValidator, MaxValueValidator

class Company(models.Model):
    """Company model for managing organizations"""
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    website = models.URLField(blank=True)
    industry = models.CharField(max_length=100)
    size = models.Char<PERSON>ield(max_length=50)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name_plural = "companies"
    
    def __str__(self):
        return self.name

class Vacancy(models.Model):
    """Vacancy model for job positions"""
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    title = models.CharField(max_length=255)
    description = models.TextField()
    requirements = models.TextField()
    category = models.CharField(max_length=100, blank=True)
    status = models.CharField(
        max_length=20,
        choices=[
            ('draft', 'Draft'),
            ('active', 'Active'),
            ('closed', 'Closed'),
            ('archived', 'Archived')
        ],
        default='draft'
    )
    published = models.BooleanField(
        default=False,
        help_text="Whether this vacancy is published and visible to public users"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        verbose_name_plural = "vacancies"
    
    def __str__(self):
        return f"{self.title} at {self.company.name}"

class ApplicantProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    cv = models.OneToOneField('CV', on_delete=models.SET_NULL, null=True, blank=True, related_name='profile')
    complete = models.BooleanField(default=False)
    phone_number = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    linkedin_profile = models.URLField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.user.username

class CV(models.Model):
    STATUS_CHOICES = [
        ('uploaded', 'Uploaded'),
        ('processing', 'Processing'),
        ('analyzed', 'Analyzed'),
        ('matched', 'Matched'),
        ('rejected', 'Rejected'),
        ('archived', 'Archived'),
    ]
    
    file = models.FileField(upload_to='cvs/', validators=[FileExtensionValidator(['pdf', 'doc', 'docx'])])
    uploaded_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='uploaded')
    category = models.CharField(max_length=100, blank=True)
    applicant_profile = models.ForeignKey(ApplicantProfile, on_delete=models.CASCADE, related_name='uploaded_cvs', null=True)
    source = models.CharField(max_length=50, default='local', choices=[
        ('local', 'Local Upload'),
        ('shared', 'Shared Folder'),
        ('onedrive', 'OneDrive'),
        ('googledrive', 'Google Drive'),
        ('email', 'Email'),
        ('whatsapp', 'WhatsApp'),
        ('telegram', 'Telegram'),
    ])
    job_type_preference = models.CharField(max_length=50, choices=[
        ('full_time', 'Full Time'),
        ('part_time', 'Part Time'),
        ('contract', 'Contract'),
        ('freelance', 'Freelance'),
    ], default='full_time')
    work_location_preference = models.CharField(max_length=50, choices=[
        ('on_site', 'On Site'),
        ('remote', 'Remote'),
        ('hybrid', 'Hyybrid'),
    ], default='on_site')
    years_of_experience = models.PositiveIntegerField(default=0)
    education_level = models.CharField(max_length=100, blank=True)
    skills = models.TextField(blank=True)
    languages = models.TextField(blank=True)
    preferred_industries = models.TextField(blank=True)
    tags = models.TextField(blank=True)  # Store as comma-separated values

    def __str__(self):
        return f"CV {self.id} - {self.source}"

class AIConfig(models.Model):
    """General AI configuration settings."""
    
    cache_ttl = models.IntegerField(
        default=86400,  # 24 hours
        help_text="Cache TTL in seconds"
    )
    batch_size = models.IntegerField(
        default=10,
        help_text="Maximum number of CVs to process in parallel"
    )
    max_retries = models.IntegerField(
        default=3,
        help_text="Maximum number of retries for failed API calls"
    )
    retry_delay = models.IntegerField(
        default=1000,
        help_text="Delay between retries in milliseconds"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AI Configuration"
        verbose_name_plural = "AI Configurations"

    def __str__(self):
        return f"AI Config (Updated: {self.updated_at})"

class AIAPIConfig(models.Model):
    """Configuration for AI API providers."""
    
    PROVIDER_CHOICES = [
        ('openai', 'OpenAI'),
        ('groq', 'Groq'),
        ('ollama', 'Ollama'),
    ]

    provider = models.CharField(max_length=50, choices=PROVIDER_CHOICES)
    api_key = models.CharField(max_length=255)
    model_name = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)
    priority = models.IntegerField(
        validators=[MinValueValidator(1)],
        help_text="Priority order for fallback (1 is highest)"
    )
    max_tokens = models.IntegerField(default=2000)
    temperature = models.FloatField(
        default=0.3,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)]
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['priority']
        verbose_name = "AI API Configuration"
        verbose_name_plural = "AI API Configurations"

    def __str__(self):
        return f"{self.provider} ({self.model_name})"
    
    def fetch_available_models(self):
        """Fetch available models from the AI provider."""
        try:
            if self.provider == 'groq':
                from pocketgroq import GroqProvider
                client = GroqProvider(api_key=self.api_key)
                # For Groq, return common models (API doesn't have list models endpoint)
                return [
                    'llama-3.1-405b-reasoning', 
                    'llama-3.1-70b-versatile', 
                    'llama-3.1-8b-instant',
                    'llama3-groq-70b-8192-tool-use-preview',
                    'llama3-groq-8b-8192-tool-use-preview',
                    'mixtral-8x7b-32768'
                ]
            elif self.provider == 'openai':
                import openai
                client = openai.OpenAI(api_key=self.api_key)
                models_response = client.models.list()
                return [model.id for model in models_response.data]
            elif self.provider == 'ollama':
                import requests
                # Default Ollama server URL - use your server or configured URL
                if self.api_key and self.api_key.startswith('http'):
                    ollama_url = self.api_key
                elif self.api_key and not self.api_key.startswith('http'):
                    # If API key is just an IP/hostname, format it properly
                    ollama_url = f"http://{self.api_key}:11434"
                else:
                    # Default to your Ollama server
                    ollama_url = 'http://*************:11434'
                
                try:
                    response = requests.get(f"{ollama_url}/api/tags", timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        models = [model['name'] for model in data.get('models', [])]
                        # Add qwen-qwq-32b to the top if available
                        if 'qwen-qwq-32b' in models:
                            models.remove('qwen-qwq-32b')
                            models.insert(0, 'qwen-qwq-32b')
                        return models
                    else:
                        return [f"Error: Ollama server returned status {response.status_code}"]
                except requests.exceptions.ConnectionError:
                    return ["Error: Ollama server not running or unreachable"]
                except Exception as e:
                    return [f"Error connecting to Ollama: {str(e)}"]
            else:
                return [f"Error: Unsupported provider '{self.provider}'"]
        except Exception as e:
            return [f"Error connecting to {self.provider}: {str(e)}"]

class AIPromptConfig(models.Model):
    name = models.CharField(max_length=100)
    prompt = models.TextField()
    instructions = models.TextField()

    def __str__(self):
        return self.name

class UnifiedAnalysis(models.Model):
    cv = models.ForeignKey('CV', on_delete=models.CASCADE)
    vacancy = models.ForeignKey('Vacancy', on_delete=models.CASCADE)
    company = models.ForeignKey('Company', on_delete=models.CASCADE)
    applicant = models.ForeignKey(User, on_delete=models.CASCADE)
    analysis_text = models.TextField()
    compatibility = models.IntegerField()
    experience_value = models.IntegerField()
    education_value = models.IntegerField()
    skills_value = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Analysis for {self.applicant.username} - {self.vacancy.title}"

class CVAnalysis(models.Model):
    cv = models.OneToOneField(CV, on_delete=models.CASCADE, related_name='analysis')
    overall_score = models.IntegerField(default=0)
    
    # Main categories
    content_score = models.IntegerField(default=0)
    format_score = models.IntegerField(default=0)
    sections_score = models.IntegerField(default=0)
    skills_score = models.IntegerField(default=0)
    style_score = models.IntegerField(default=0)
    
    # Sub-categories (you might want to use JSONField for more flexibility)
    content_details = models.JSONField(default=dict)
    format_details = models.JSONField(default=dict)
    sections_details = models.JSONField(default=dict)
    skills_details = models.JSONField(default=dict)
    style_details = models.JSONField(default=dict)
    
    # Analysis text fields (missing fields that were causing the error)
    content_analysis = models.TextField(default='', blank=True, help_text="Detailed content analysis text")
    format_analysis = models.TextField(default='', blank=True, help_text="Detailed format analysis text")
    sections_analysis = models.TextField(default='', blank=True, help_text="Detailed sections analysis text")
    skills_analysis = models.TextField(default='', blank=True, help_text="Detailed skills analysis text")
    style_analysis = models.TextField(default='', blank=True, help_text="Detailed style analysis text")

    name = models.CharField(max_length=200, default='')
    email = models.EmailField(default='')
    phone_number = models.CharField(max_length=20, default='')
    location = models.CharField(max_length=200, default='')
    years_of_experience = models.PositiveIntegerField(default=0)
    education_level = models.CharField(max_length=100, default='')
    skills = models.TextField(default='')
    languages = models.TextField(default='')
    preferred_job_type = models.CharField(max_length=50, choices=[
        ('full_time', 'Full Time'),
        ('part_time', 'Part Time'),
        ('contract', 'Contract'),
        ('freelance', 'Freelance'),
    ], default='full_time')
    preferred_work_location = models.CharField(max_length=50, choices=[
        ('on_site', 'On Site'),
        ('remote', 'Remote'),
        ('hybrid', 'Hybrid'),
    ], default='on_site')
    salary_expectation = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    availability = models.DateField(null=True, blank=True)
    
    # Professional Links
    linkedin_profile = models.URLField(blank=True, help_text="LinkedIn profile URL")
    website_portfolio = models.URLField(blank=True, help_text="Personal website or portfolio URL")
    github_profile = models.URLField(blank=True, help_text="GitHub profile URL")
    other_links = models.JSONField(default=list, blank=True, help_text="Other professional links")
    
    # Enhanced structured data for comprehensive analysis
    experience_data = models.JSONField(default=dict, blank=True, help_text="Structured work experience data")
    education_data = models.JSONField(default=dict, blank=True, help_text="Structured education data")
    certifications_data = models.JSONField(default=dict, blank=True, help_text="Structured certifications data")
    
    additional_info = models.JSONField(default=dict, blank=True)
    ai_config = models.ForeignKey(AIConfig, on_delete=models.SET_NULL, null=True)

    def __str__(self):
        return f"Analysis for {self.cv}"

class CompanyAnalysis(models.Model):
    company = models.OneToOneField(Company, on_delete=models.CASCADE, related_name='analysis')
    company_size = models.CharField(max_length=50, choices=[
        ('small', 'Small (1-50 employees)'),
        ('medium', 'Medium (51-250 employees)'),
        ('large', 'Large (251+ employees)'),
    ])
    founded_year = models.PositiveIntegerField(null=True, blank=True)
    headquarters = models.CharField(max_length=200)
    company_culture = models.TextField()
    benefits = models.TextField()
    growth_opportunities = models.TextField()
    technology_stack = models.TextField()
    main_competitors = models.TextField()
    recent_news = models.TextField()
    additional_info = models.JSONField(default=dict, blank=True)
    ai_config = models.ForeignKey(AIConfig, on_delete=models.SET_NULL, null=True)

    def __str__(self):
        return f"Analysis for {self.company}"

class VacancyAnalysis(models.Model):
    vacancy = models.OneToOneField(Vacancy, on_delete=models.CASCADE, related_name='analysis')
    required_skills = models.TextField()
    preferred_skills = models.TextField()
    required_experience = models.PositiveIntegerField()
    required_education = models.CharField(max_length=100)
    job_responsibilities = models.TextField()
    salary_range = models.CharField(max_length=100)
    benefits = models.TextField()
    work_hours = models.CharField(max_length=100)
    travel_requirements = models.TextField()
    remote_work_policy = models.CharField(max_length=100)
    career_growth_opportunities = models.TextField()
    application_deadline = models.DateField(null=True, blank=True)
    interview_process = models.TextField()
    additional_info = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"Analysis for {self.vacancy}"



class AnalysisProcess(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    vacancy = models.ForeignKey(Vacancy, on_delete=models.CASCADE, null=True, blank=True)
    cv = models.ForeignKey(CV, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)

    def __str__(self):
        return f"Analysis Process for CV {self.cv.id} - Vacancy {self.vacancy.title}"

class WelcomeContent(models.Model):
    welcome_message = models.TextField()
    step_1_title = models.CharField(max_length=200)
    step_1_description = models.TextField()
    step_1_image = models.ImageField(upload_to='welcome_images/', validators=[FileExtensionValidator(['png', 'jpg', 'jpeg'])])
    step_2_title = models.CharField(max_length=200)
    step_2_description = models.TextField()
    step_2_image = models.ImageField(upload_to='welcome_images/', validators=[FileExtensionValidator(['png', 'jpg', 'jpeg'])])
    step_3_title = models.CharField(max_length=200)
    step_3_description = models.TextField()
    step_3_image = models.ImageField(upload_to='welcome_images/', validators=[FileExtensionValidator(['png', 'jpg', 'jpeg'])])
    step_4_title = models.CharField(max_length=200, default="View Results")
    step_4_description = models.TextField(default="Access comprehensive analysis reports, including compatibility scores, key skills, and areas for improvement for each candidate.")
    step_4_image = models.ImageField(upload_to='welcome_images/', validators=[FileExtensionValidator(['png', 'jpg', 'jpeg'])], null=True, blank=True)
    
    def __str__(self):
        return "Welcome Content"
    
    class Meta:
        verbose_name_plural = "Welcome Content"

class ComparisonAnalysis(models.Model):
    vacancy = models.ForeignKey('Vacancy', on_delete=models.CASCADE)
    cv = models.ForeignKey('CV', on_delete=models.CASCADE)
    compatibility_score = models.FloatField()
    skills_match_percentage = models.FloatField(default=0.0)
    recommendation_level = models.CharField(max_length=50, default='Consider')
    analysis_text = models.TextField()
    analysis_details = models.JSONField(default=dict, blank=True)
    ai_config = models.ForeignKey('AIConfig', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Comparison: {self.vacancy.title} - CV {self.cv.id}"

class AIUsageLog(models.Model):
    """Log for tracking AI API usage."""
    
    provider = models.CharField(max_length=50)
    operation = models.CharField(max_length=100)
    tokens_used = models.IntegerField()
    response_time = models.FloatField(help_text="Response time in seconds")
    status = models.CharField(max_length=50)
    error_message = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True
    )

    class Meta:
        ordering = ['-created_at']
        verbose_name = "AI Usage Log"
        verbose_name_plural = "AI Usage Logs"

    def __str__(self):
        return f"{self.provider} - {self.operation} ({self.created_at})"

class SecurityAuditLog(models.Model):
    """Security audit log for tracking authentication and authorization events."""
    
    EVENT_TYPES = [
        ('login_attempt', 'Login Attempt'),
        ('login_success', 'Login Success'),
        ('login_failure', 'Login Failure'),
        ('logout', 'Logout'),
        ('password_change', 'Password Change'),
        ('permission_denied', 'Permission Denied'),
        ('file_upload', 'File Upload'),
        ('file_download', 'File Download'),
        ('data_access', 'Data Access'),
        ('admin_action', 'Admin Action'),
    ]
    
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    username = models.CharField(max_length=150, blank=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    request_path = models.TextField()
    request_method = models.CharField(max_length=10, default='GET')
    session_key = models.CharField(max_length=40, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    details = models.JSONField(default=dict, blank=True)
    success = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['-timestamp']
        verbose_name = "Security Audit Log"
        verbose_name_plural = "Security Audit Logs"
        indexes = [
            models.Index(fields=['event_type', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['username', 'timestamp']),
        ]
    
    def __str__(self):
        return f"{self.event_type} - {self.username} ({self.timestamp})"

class SecurityAlert(models.Model):
    """Security alerts for suspicious activities."""
    
    ALERT_TYPES = [
        ('suspicious_activity', 'Suspicious Activity'),
        ('brute_force', 'Brute Force Attack'),
        ('malicious_file', 'Malicious File Upload'),
        ('sql_injection', 'SQL Injection Attempt'),
        ('xss_attempt', 'XSS Attempt'),
        ('rate_limit_exceeded', 'Rate Limit Exceeded'),
        ('unauthorized_access', 'Unauthorized Access'),
        ('data_breach', 'Data Breach'),
    ]
    
    SEVERITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    STATUS_CHOICES = [
        ('open', 'Open'),
        ('investigating', 'Investigating'),
        ('resolved', 'Resolved'),
        ('false_positive', 'False Positive'),
    ]
    
    alert_type = models.CharField(max_length=50, choices=ALERT_TYPES)
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, default='medium')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    request_path = models.TextField()
    request_method = models.CharField(max_length=10, default='GET')
    query_params = models.JSONField(default=dict, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    description = models.TextField(blank=True)
    details = models.JSONField(default=dict, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    resolution_notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-timestamp']
        verbose_name = "Security Alert"
        verbose_name_plural = "Security Alerts"
        indexes = [
            models.Index(fields=['alert_type', 'severity', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['status', 'timestamp']),
        ]
    
    def __str__(self):
        return f"{self.alert_type} - {self.severity} ({self.timestamp})"

class FileUploadLog(models.Model):
    """Log for tracking file uploads and security validations."""
    
    STATUS_CHOICES = [
        ('uploaded', 'Uploaded'),
        ('validated', 'Validated'),
        ('quarantined', 'Quarantined'),
        ('rejected', 'Rejected'),
        ('deleted', 'Deleted'),
    ]
    
    filename = models.CharField(max_length=255)
    original_filename = models.CharField(max_length=255)
    file_size = models.BigIntegerField()
    file_type = models.CharField(max_length=100)
    mime_type = models.CharField(max_length=100)
    md5_hash = models.CharField(max_length=32)
    sha256_hash = models.CharField(max_length=64)
    upload_path = models.TextField()
    quarantine_path = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    validated_at = models.DateTimeField(null=True, blank=True)
    validation_results = models.JSONField(default=dict, blank=True)
    virus_scan_results = models.JSONField(default=dict, blank=True)
    quarantine_reason = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-uploaded_at']
        verbose_name = "File Upload Log"
        verbose_name_plural = "File Upload Logs"
        indexes = [
            models.Index(fields=['user', 'uploaded_at']),
            models.Index(fields=['status', 'uploaded_at']),
            models.Index(fields=['md5_hash']),
            models.Index(fields=['sha256_hash']),
        ]
    
    def __str__(self):
        return f"{self.original_filename} - {self.status} ({self.uploaded_at})"

class TwoFactorAuth(models.Model):
    """Two-factor authentication settings for users."""
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='two_factor_auth')
    is_enabled = models.BooleanField(default=False)
    secret_key = models.CharField(max_length=32, blank=True)
    backup_codes = models.JSONField(default=list, blank=True)
    last_used_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Two Factor Authentication"
        verbose_name_plural = "Two Factor Authentication"
    
    def __str__(self):
        return f"2FA for {self.user.username} ({'Enabled' if self.is_enabled else 'Disabled'})"

class UserSession(models.Model):
    """Enhanced user session tracking."""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    session_key = models.CharField(max_length=40, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    location = models.CharField(max_length=200, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField()
    
    class Meta:
        ordering = ['-last_activity']
        verbose_name = "User Session"
        verbose_name_plural = "User Sessions"
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['session_key']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.ip_address} ({self.created_at})"


# GDPR and Data Protection Models

class DataProcessingConsent(models.Model):
    """GDPR consent tracking"""
    CONSENT_TYPES = [
        ('data_processing', 'General Data Processing'),
        ('marketing', 'Marketing Communications'),
        ('analytics', 'Analytics and Tracking'),
        ('third_party', 'Third-party Integrations'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='consents')
    consent_type = models.CharField(max_length=50, choices=CONSENT_TYPES)
    consent_given = models.BooleanField()
    consent_date = models.DateTimeField(auto_now_add=True)
    consent_ip = models.GenericIPAddressField(null=True, blank=True)
    consent_user_agent = models.TextField(null=True, blank=True)
    withdrawal_date = models.DateTimeField(null=True, blank=True)
    legal_basis = models.CharField(max_length=100, blank=True)
    
    class Meta:
        db_table = 'cv_analyzer_data_consent'
        unique_together = ['user', 'consent_type']
        indexes = [
            models.Index(fields=['user', 'consent_type']),
            models.Index(fields=['consent_date']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.consent_type}: {'Given' if self.consent_given else 'Withdrawn'}"


class DataSubjectRequest(models.Model):
    """GDPR data subject request tracking"""
    REQUEST_TYPES = [
        ('access', 'Data Access Request'),
        ('rectification', 'Data Rectification Request'),
        ('erasure', 'Data Erasure Request'),
        ('portability', 'Data Portability Request'),
        ('restriction', 'Processing Restriction Request'),
        ('objection', 'Processing Objection'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='data_requests')
    request_type = models.CharField(max_length=20, choices=REQUEST_TYPES)
    request_date = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    description = models.TextField(blank=True)
    response_data = models.JSONField(null=True, blank=True)
    completion_date = models.DateTimeField(null=True, blank=True)
    processed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='processed_requests')
    
    class Meta:
        db_table = 'cv_analyzer_data_subject_request'
        indexes = [
            models.Index(fields=['user', 'request_type']),
            models.Index(fields=['request_date']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.get_request_type_display()} ({self.status})"


class EncryptedData(models.Model):
    """Model for storing encrypted sensitive data"""
    from django.contrib.contenttypes.models import ContentType
    from django.contrib.contenttypes.fields import GenericForeignKey
    
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    
    field_name = models.CharField(max_length=100)
    encrypted_value = models.TextField()
    hash_value = models.CharField(max_length=64)  # For indexing
    encryption_method = models.CharField(max_length=50, default='fernet')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'cv_analyzer_encrypted_data'
        unique_together = ['content_type', 'object_id', 'field_name']
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['hash_value']),
        ]
    
    def __str__(self):
        return f"Encrypted {self.field_name} for {self.content_object}"


class DataRetentionPolicy(models.Model):
    """Data retention policy configuration"""
    RETENTION_ACTIONS = [
        ('delete', 'Delete Data'),
        ('anonymize', 'Anonymize Data'),
        ('archive', 'Archive Data'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    data_type = models.CharField(max_length=100)  # e.g., 'cv_files', 'user_data'
    retention_period_days = models.PositiveIntegerField()
    action = models.CharField(max_length=20, choices=RETENTION_ACTIONS)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'cv_analyzer_retention_policy'
    
    def __str__(self):
        return f"{self.name} - {self.retention_period_days} days"


class DataProcessingActivity(models.Model):
    """Record of processing activities for GDPR compliance"""
    PROCESSING_PURPOSES = [
        ('cv_analysis', 'CV Analysis Service'),
        ('user_management', 'User Account Management'),
        ('communication', 'User Communication'),
        ('analytics', 'Service Analytics'),
        ('security', 'Security Monitoring'),
    ]
    
    activity_name = models.CharField(max_length=100)
    purpose = models.CharField(max_length=50, choices=PROCESSING_PURPOSES)
    data_categories = models.JSONField()  # List of data categories processed
    data_subjects = models.CharField(max_length=100)  # e.g., 'users', 'job_applicants'
    recipients = models.JSONField(null=True, blank=True)  # Third parties who receive data
    retention_period = models.CharField(max_length=100)
    security_measures = models.TextField()
    legal_basis = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'cv_analyzer_processing_activity'
    
    def __str__(self):
        return f"{self.activity_name} - {self.get_purpose_display()}"


# Phase 3: Application Enhancement Models

class BusinessAuditLog(models.Model):
    """Audit log for business operations"""
    event_type = models.CharField(max_length=100)
    entity_type = models.CharField(max_length=50)
    entity_id = models.PositiveIntegerField()
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    details = models.JSONField(default=dict)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['entity_type', 'entity_id']),
            models.Index(fields=['event_type']),
            models.Index(fields=['timestamp']),
        ]
    
    def __str__(self):
        return f"{self.event_type} - {self.entity_type}:{self.entity_id}"

class APIUsageLog(models.Model):
    """Log API usage for monitoring and analytics"""
    endpoint = models.CharField(max_length=200)
    method = models.CharField(max_length=10)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    response_time_ms = models.PositiveIntegerField()
    status_code = models.PositiveIntegerField()
    request_size = models.PositiveIntegerField(default=0)
    response_size = models.PositiveIntegerField(default=0)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['endpoint']),
            models.Index(fields=['timestamp']),
            models.Index(fields=['status_code']),
        ]
    
    def __str__(self):
        return f"{self.method} {self.endpoint} - {self.status_code}"

class WorkflowInstance(models.Model):
    """Workflow instance tracking"""
    workflow_name = models.CharField(max_length=100)
    workflow_id = models.CharField(max_length=200, unique=True)
    status = models.CharField(max_length=20, choices=[
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ], default='pending')
    initiated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    context_data = models.JSONField(default=dict)
    current_step = models.PositiveIntegerField(default=0)
    error_message = models.TextField(blank=True)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-started_at']
        indexes = [
            models.Index(fields=['workflow_name']),
            models.Index(fields=['status']),
            models.Index(fields=['started_at']),
        ]
    
    def __str__(self):
        return f"{self.workflow_name} - {self.status}"

class TaskMetricsLog(models.Model):
    """Task execution metrics for monitoring"""
    task_id = models.CharField(max_length=100, unique=True)
    task_name = models.CharField(max_length=200)
    priority = models.PositiveIntegerField(default=5)
    status = models.CharField(max_length=20, choices=[
        ('pending', 'Pending'),
        ('started', 'Started'),
        ('success', 'Success'),
        ('failure', 'Failure'),
        ('retry', 'Retry'),
        ('revoked', 'Revoked'),
    ], default='pending')
    worker_id = models.CharField(max_length=100, blank=True)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField(null=True, blank=True)
    duration_seconds = models.FloatField(null=True, blank=True)
    retries = models.PositiveIntegerField(default=0)
    error_message = models.TextField(blank=True)
    metadata = models.JSONField(default=dict)
    
    class Meta:
        ordering = ['-start_time']
        indexes = [
            models.Index(fields=['task_name']),
            models.Index(fields=['status']),
            models.Index(fields=['start_time']),
        ]
    
    def __str__(self):
        return f"{self.task_name} - {self.status}"

class AIProviderMetrics(models.Model):
    """AI provider usage and performance metrics"""
    provider_name = models.CharField(max_length=50)
    request_count = models.PositiveIntegerField(default=0)
    total_tokens = models.PositiveIntegerField(default=0)
    total_cost = models.DecimalField(max_digits=10, decimal_places=6, default=0)
    avg_response_time_ms = models.PositiveIntegerField(default=0)
    success_count = models.PositiveIntegerField(default=0)
    error_count = models.PositiveIntegerField(default=0)
    last_health_check = models.DateTimeField(null=True, blank=True)
    health_status = models.CharField(max_length=20, choices=[
        ('healthy', 'Healthy'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('unknown', 'Unknown'),
    ], default='unknown')
    metadata = models.JSONField(default=dict)
    date = models.DateField(auto_now_add=True)
    
    class Meta:
        unique_together = ['provider_name', 'date']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.provider_name} - {self.date}"

class BusinessRuleConfig(models.Model):
    """Business rules configuration"""
    name = models.CharField(max_length=100, unique=True)
    rule_type = models.CharField(max_length=20, choices=[
        ('validation', 'Validation'),
        ('business', 'Business'),
        ('security', 'Security'),
        ('notification', 'Notification'),
    ])
    priority = models.PositiveIntegerField(default=5)
    enabled = models.BooleanField(default=True)
    rule_config = models.JSONField(default=dict)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-priority', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.rule_type})"

class ProgressSession(models.Model):
    """Track real-time progress for long-running operations"""
    session_id = models.CharField(max_length=100, unique=True)
    operation_type = models.CharField(max_length=50)
    total_steps = models.IntegerField()
    current_step = models.IntegerField(default=0)
    status = models.CharField(max_length=20, choices=[
        ('initialized', 'Initialized'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ], default='initialized')
    progress_percentage = models.FloatField(default=0.0)
    start_time = models.DateTimeField(auto_now_add=True)
    end_time = models.DateTimeField(null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    error_message = models.TextField(blank=True)
    
    class Meta:
        db_table = 'cv_progress_sessions'
        
    def __str__(self):
        return f"{self.session_id} - {self.operation_type} ({self.status})"

class PWAInstallation(models.Model):
    """Track PWA installations and usage"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    device_type = models.CharField(max_length=20, choices=[
        ('mobile', 'Mobile'),
        ('tablet', 'Tablet'),
        ('desktop', 'Desktop')
    ])
    platform = models.CharField(max_length=50)  # iOS, Android, Windows, etc.
    browser = models.CharField(max_length=50)
    installed_at = models.DateTimeField(auto_now_add=True)
    last_used = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'cv_pwa_installations'
        unique_together = ['user', 'device_type', 'platform']
        
    def __str__(self):
        return f"{self.user.username} - {self.device_type} ({self.platform})"

class PushSubscription(models.Model):
    """Store push notification subscriptions"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    endpoint = models.URLField()
    p256dh_key = models.TextField()
    auth_key = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'cv_push_subscriptions'
        unique_together = ['user', 'endpoint']
        
    def __str__(self):
        return f"Push subscription for {self.user.username}"

class NotificationLog(models.Model):
    """Log sent notifications"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    notification_type = models.CharField(max_length=50)
    title = models.CharField(max_length=200)
    message = models.TextField()
    sent_at = models.DateTimeField(auto_now_add=True)
    delivery_status = models.CharField(max_length=20, choices=[
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('clicked', 'Clicked')
    ], default='sent')
    
    class Meta:
        db_table = 'cv_notification_logs'
        
    def __str__(self):
        return f"{self.notification_type} to {self.user.username}"

class AccessibilityAudit(models.Model):
    """Store accessibility audit results"""
    page_url = models.CharField(max_length=200)
    template_name = models.CharField(max_length=100)
    audit_date = models.DateTimeField(auto_now_add=True)
    wcag_level = models.CharField(max_length=3, choices=[
        ('A', 'WCAG A'),
        ('AA', 'WCAG AA'),
        ('AAA', 'WCAG AAA'),
        ('NC', 'Non-Compliant')
    ])
    score = models.FloatField()
    issues_count = models.IntegerField(default=0)
    warnings_count = models.IntegerField(default=0)
    audit_data = models.JSONField()  # Store full audit results
    
    class Meta:
        db_table = 'cv_accessibility_audits'
        
    def __str__(self):
        return f"Accessibility audit for {self.page_url} - {self.wcag_level}"

class UserInteractionLog(models.Model):
    """Log user interactions for UX analysis"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    session_id = models.CharField(max_length=100)
    interaction_type = models.CharField(max_length=50)  # click, scroll, form_submit, etc.
    element_id = models.CharField(max_length=100, blank=True)
    element_class = models.CharField(max_length=100, blank=True)
    page_url = models.CharField(max_length=200)
    timestamp = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict)  # Additional interaction data
    
    class Meta:
        db_table = 'cv_user_interactions'
        
    def __str__(self):
        return f"{self.interaction_type} on {self.page_url}"

class OfflineData(models.Model):
    """Store data for offline functionality"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    data_type = models.CharField(max_length=50)  # cv, analysis, profile, etc.
    object_id = models.IntegerField()
    data_content = models.JSONField()
    cached_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    sync_status = models.CharField(max_length=20, choices=[
        ('synced', 'Synced'),
        ('pending', 'Pending Sync'),
        ('failed', 'Sync Failed')
    ], default='synced')
    
    class Meta:
        db_table = 'cv_offline_data'
        unique_together = ['user', 'data_type', 'object_id']
        
    def __str__(self):
        return f"Offline {self.data_type} data for {self.user.username}"

class FrontendPerformanceMetric(models.Model):
    """Track frontend performance metrics"""
    page_url = models.CharField(max_length=200)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    load_time = models.FloatField()  # Page load time in seconds
    dom_ready_time = models.FloatField()
    first_paint_time = models.FloatField(null=True, blank=True)
    largest_contentful_paint = models.FloatField(null=True, blank=True)
    cumulative_layout_shift = models.FloatField(null=True, blank=True)
    javascript_errors = models.IntegerField(default=0)
    device_type = models.CharField(max_length=20)
    browser = models.CharField(max_length=50)
    recorded_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'cv_frontend_performance'
        
    def __str__(self):
        return f"Performance metrics for {self.page_url}"


# DevOps and Monitoring Models

class MonitoringMetric(models.Model):
    """Store monitoring metrics over time"""
    timestamp = models.DateTimeField(auto_now_add=True)
    metric_type = models.CharField(max_length=50)  # cpu, memory, response_time, etc.
    metric_name = models.CharField(max_length=100)
    value = models.FloatField()
    unit = models.CharField(max_length=20, default='')
    source = models.CharField(max_length=50, default='system')
    metadata = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'monitoring_metrics'
        indexes = [
            models.Index(fields=['timestamp', 'metric_type']),
            models.Index(fields=['metric_name']),
        ]

    def __str__(self):
        return f"{self.metric_name}: {self.value} {self.unit}"

class SecurityEvent(models.Model):
    """Log security events and threats"""
    SEVERITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    timestamp = models.DateTimeField(auto_now_add=True)
    event_type = models.CharField(max_length=50)
    severity = models.CharField(max_length=20, choices=SEVERITY_CHOICES)
    ip_address = models.GenericIPAddressField()
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    request_path = models.CharField(max_length=500, default='')
    threat_type = models.CharField(max_length=50, default='')
    risk_score = models.IntegerField(default=0)
    action_taken = models.CharField(max_length=50, default='none')
    details = models.JSONField(default=dict)
    resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'security_events'
        indexes = [
            models.Index(fields=['timestamp', 'severity']),
            models.Index(fields=['ip_address']),
            models.Index(fields=['event_type']),
        ]

    def __str__(self):
        return f"Security Event: {self.event_type} - {self.severity}"

class VulnerabilityReport(models.Model):
    """Store vulnerability scan results"""
    RISK_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    scan_id = models.CharField(max_length=50, unique=True)
    scan_date = models.DateTimeField(auto_now_add=True)
    scan_type = models.CharField(max_length=50)
    vulnerability_title = models.CharField(max_length=200)
    description = models.TextField()
    risk_level = models.CharField(max_length=20, choices=RISK_LEVELS)
    affected_component = models.CharField(max_length=100)
    recommendation = models.TextField()
    fixed = models.BooleanField(default=False)
    fixed_date = models.DateTimeField(null=True, blank=True)
    metadata = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'vulnerability_reports'
        indexes = [
            models.Index(fields=['scan_date', 'risk_level']),
            models.Index(fields=['scan_id']),
        ]

    def __str__(self):
        return f"Vulnerability: {self.vulnerability_title} ({self.risk_level})"

class DeploymentLog(models.Model):
    """Track deployment history and status"""
    DEPLOYMENT_STATUS = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('success', 'Success'),
        ('failed', 'Failed'),
        ('rolled_back', 'Rolled Back'),
    ]
    
    deployment_id = models.CharField(max_length=50, unique=True)
    environment = models.CharField(max_length=50)  # development, staging, production
    version = models.CharField(max_length=50)
    commit_hash = models.CharField(max_length=40, default='')
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=DEPLOYMENT_STATUS, default='pending')
    deployed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    pipeline_type = models.CharField(max_length=50, default='manual')
    deployment_notes = models.TextField(blank=True)
    rollback_version = models.CharField(max_length=50, blank=True)
    metadata = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'deployment_logs'
        indexes = [
            models.Index(fields=['started_at', 'environment']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"Deployment {self.deployment_id} - {self.status}"

class HealthCheckResult(models.Model):
    """Store health check results over time"""
    timestamp = models.DateTimeField(auto_now_add=True)
    check_name = models.CharField(max_length=50)
    status = models.CharField(max_length=20)  # healthy, degraded, unhealthy
    response_time_ms = models.FloatField(default=0)
    message = models.TextField(default='')
    details = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'health_check_results'
        indexes = [
            models.Index(fields=['timestamp', 'check_name']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"Health Check: {self.check_name} - {self.status}"

class AuditLog(models.Model):
    """Comprehensive audit logging"""
    timestamp = models.DateTimeField(auto_now_add=True)
    event_type = models.CharField(max_length=50)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    resource = models.CharField(max_length=100, default='')
    action = models.CharField(max_length=50, default='')
    success = models.BooleanField(default=True)
    old_value = models.JSONField(null=True, blank=True)
    new_value = models.JSONField(null=True, blank=True)
    additional_info = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'audit_logs'
        indexes = [
            models.Index(fields=['timestamp', 'event_type']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['ip_address']),
        ]

    def __str__(self):
        return f"Audit: {self.event_type} by {self.user or 'anonymous'}"

class AlertConfiguration(models.Model):
    """Configuration for monitoring alerts"""
    ALERT_TYPES = [
        ('metric_threshold', 'Metric Threshold'),
        ('health_check', 'Health Check'),
        ('security_event', 'Security Event'),
        ('error_rate', 'Error Rate'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    alert_type = models.CharField(max_length=50, choices=ALERT_TYPES)
    enabled = models.BooleanField(default=True)
    metric_name = models.CharField(max_length=100, blank=True)
    threshold_value = models.FloatField(null=True, blank=True)
    comparison_operator = models.CharField(max_length=10, default='>')  # >, <, >=, <=, ==
    time_window_minutes = models.IntegerField(default=5)
    notification_channels = models.JSONField(default=list)  # email, slack, etc.
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'alert_configurations'

    def __str__(self):
        return self.name

class VacancyScoringRules(models.Model):
    """Custom scoring rules for each vacancy with configurable weights"""
    
    vacancy = models.OneToOneField(
        'Vacancy', 
        on_delete=models.CASCADE, 
        related_name='scoring_rules'
    )
    
    # Weights (must sum to 100%)
    education_weight = models.FloatField(
        default=25.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Weight for education scoring (0-100%)"
    )
    experience_weight = models.FloatField(
        default=35.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Weight for experience scoring (0-100%)"
    )
    skills_weight = models.FloatField(
        default=30.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Weight for skills scoring (0-100%)"
    )
    responsibilities_weight = models.FloatField(
        default=10.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Weight for responsibilities scoring (0-100%)"
    )
    
    # Education scoring criteria
    education_requirements = models.JSONField(
        default=dict,
        help_text="Education requirements and scoring rules"
    )
    
    # Experience scoring criteria
    experience_requirements = models.JSONField(
        default=dict,
        help_text="Experience requirements and scoring rules"
    )
    
    # Skills scoring criteria
    skills_requirements = models.JSONField(
        default=dict,
        help_text="Skills requirements and scoring rules"
    )
    
    # Responsibilities scoring criteria
    responsibilities_requirements = models.JSONField(
        default=dict,
        help_text="Responsibilities requirements and scoring rules"
    )
    
    # Custom scoring configuration
    enable_custom_scoring = models.BooleanField(
        default=True,
        help_text="Enable custom weighted scoring for this vacancy"
    )
    
    # Minimum thresholds
    minimum_education_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Minimum education score required (0-100%)"
    )
    minimum_experience_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Minimum experience score required (0-100%)"
    )
    minimum_skills_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Minimum skills score required (0-100%)"
    )
    minimum_overall_score = models.FloatField(
        default=50.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Minimum overall score required (0-100%)"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'cv_vacancy_scoring_rules'
        verbose_name = "Vacancy Scoring Rules"
        verbose_name_plural = "Vacancy Scoring Rules"
    
    def __str__(self):
        return f"Scoring Rules for {self.vacancy.title}"
    
    def clean(self):
        """Validate that weights sum to 100%"""
        total_weight = (
            self.education_weight + 
            self.experience_weight + 
            self.skills_weight + 
            self.responsibilities_weight
        )
        
        if abs(total_weight - 100.0) > 0.01:  # Allow small floating point errors
            from django.core.exceptions import ValidationError
            raise ValidationError(
                f"Weights must sum to 100%. Current total: {total_weight}%"
            )
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)
    
    def get_default_education_requirements(self):
        """Get default education requirements structure"""
        return {
            'high_school': {'score': 20, 'keywords': ['high school', 'secondary']},
            'associate': {'score': 40, 'keywords': ['associate', 'diploma']},
            'bachelor': {'score': 70, 'keywords': ['bachelor', 'ba', 'bs', 'bsc']},
            'master': {'score': 90, 'keywords': ['master', 'ma', 'ms', 'msc', 'mba']},
            'phd': {'score': 100, 'keywords': ['phd', 'doctorate', 'doctoral']},
            'default_score': 30
        }
    
    def get_default_experience_requirements(self):
        """Get default experience requirements structure"""
        return {
            'ranges': [
                {'min': 0, 'max': 1, 'score': 20},
                {'min': 1, 'max': 3, 'score': 50},
                {'min': 3, 'max': 5, 'score': 75},
                {'min': 5, 'max': 10, 'score': 90},
                {'min': 10, 'max': 999, 'score': 100}
            ],
            'default_score': 30
        }
    
    def get_default_skills_requirements(self):
        """Get default skills requirements structure"""
        return {
            'required_skills': [],
            'preferred_skills': [],
            'skill_matching_threshold': 0.7,
            'default_score': 50
        }
    
    def get_default_responsibilities_requirements(self):
        """Get default responsibilities requirements structure"""
        return {
            'key_responsibilities': [],
            'responsibility_matching_threshold': 0.6,
            'default_score': 50
        }

class CustomScoringResult(models.Model):
    """Store results of custom weighted scoring calculations"""
    
    cv_analysis = models.ForeignKey(
        'CVAnalysis',
        on_delete=models.CASCADE,
        related_name='custom_scoring_results'
    )
    
    vacancy = models.ForeignKey(
        'Vacancy',
        on_delete=models.CASCADE,
        related_name='custom_scores'
    )
    
    # Individual component scores (0-100)
    education_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Education component score (0-100%)"
    )
    experience_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Experience component score (0-100%)"
    )
    skills_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Skills component score (0-100%)"
    )
    responsibilities_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Responsibilities component score (0-100%)"
    )
    
    # Weighted scores
    weighted_education_score = models.FloatField(
        default=0.0,
        help_text="Education score * education weight"
    )
    weighted_experience_score = models.FloatField(
        default=0.0,
        help_text="Experience score * experience weight"
    )
    weighted_skills_score = models.FloatField(
        default=0.0,
        help_text="Skills score * skills weight"
    )
    weighted_responsibilities_score = models.FloatField(
        default=0.0,
        help_text="Responsibilities score * responsibilities weight"
    )
    
    # Final custom score
    final_custom_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Final weighted custom score (0-100%)"
    )
    
    # Score breakdown details
    score_breakdown = models.JSONField(
        default=dict,
        help_text="Detailed breakdown of score calculations"
    )
    
    # Qualification status
    meets_minimum_requirements = models.BooleanField(
        default=False,
        help_text="Whether candidate meets minimum score requirements"
    )
    
    # Recommendation based on custom scoring
    custom_recommendation = models.CharField(
        max_length=50,
        choices=[
            ('highly_recommended', 'Highly Recommended'),
            ('recommended', 'Recommended'),
            ('consider', 'Consider'),
            ('not_recommended', 'Not Recommended'),
        ],
        default='consider'
    )
    
    # Comparison with AI score
    ai_vs_custom_difference = models.FloatField(
        default=0.0,
        help_text="Difference between AI score and custom score"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'cv_custom_scoring_results'
        verbose_name = "Custom Scoring Result"
        verbose_name_plural = "Custom Scoring Results"
        unique_together = ['cv_analysis', 'vacancy']
    
    def __str__(self):
        return f"Custom Score: {self.final_custom_score:.1f}% for {self.cv_analysis.cv}"
    
    def calculate_recommendation(self):
        """Calculate recommendation based on custom score"""
        if self.final_custom_score >= 85:
            return 'highly_recommended'
        elif self.final_custom_score >= 70:
            return 'recommended'
        elif self.final_custom_score >= 50:
            return 'consider'
        else:
            return 'not_recommended'
    
    def get_score_grade(self):
        """Get letter grade for the custom score"""
        if self.final_custom_score >= 90:
            return 'A+'
        elif self.final_custom_score >= 85:
            return 'A'
        elif self.final_custom_score >= 80:
            return 'A-'
        elif self.final_custom_score >= 75:
            return 'B+'
        elif self.final_custom_score >= 70:
            return 'B'
        elif self.final_custom_score >= 65:
            return 'B-'
        elif self.final_custom_score >= 60:
            return 'C+'
        elif self.final_custom_score >= 55:
            return 'C'
        elif self.final_custom_score >= 50:
            return 'C-'
        else:
            return 'F'

class ScoringTemplate(models.Model):
    """Reusable scoring templates for different job categories"""
    
    name = models.CharField(max_length=100, unique=True)
    category = models.CharField(max_length=50)
    description = models.TextField(blank=True)
    
    # Template weights
    education_weight = models.FloatField(
        default=25.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)]
    )
    experience_weight = models.FloatField(
        default=35.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)]
    )
    skills_weight = models.FloatField(
        default=30.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)]
    )
    responsibilities_weight = models.FloatField(
        default=10.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)]
    )
    
    # Template requirements
    education_requirements = models.JSONField(default=dict)
    experience_requirements = models.JSONField(default=dict)
    skills_requirements = models.JSONField(default=dict)
    responsibilities_requirements = models.JSONField(default=dict)
    
    # Template settings
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'cv_scoring_templates'
        verbose_name = "Scoring Template"
        verbose_name_plural = "Scoring Templates"
    
    def __str__(self):
        return f"{self.name} ({self.category})"
    
    def clean(self):
        """Validate that weights sum to 100%"""
        total_weight = (
            self.education_weight + 
            self.experience_weight + 
            self.skills_weight + 
            self.responsibilities_weight
        )
        
        if abs(total_weight - 100.0) > 0.01:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                f"Weights must sum to 100%. Current total: {total_weight}%"
            )
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)
    
    def apply_to_vacancy(self, vacancy):
        """Apply this template to a vacancy's scoring rules"""
        scoring_rules, created = VacancyScoringRules.objects.get_or_create(
            vacancy=vacancy,
            defaults={
                'education_weight': self.education_weight,
                'experience_weight': self.experience_weight,
                'skills_weight': self.skills_weight,
                'responsibilities_weight': self.responsibilities_weight,
                'education_requirements': self.education_requirements,
                'experience_requirements': self.experience_requirements,
                'skills_requirements': self.skills_requirements,
                'responsibilities_requirements': self.responsibilities_requirements,
            }
        )
        
        if not created:
            # Update existing rules
            scoring_rules.education_weight = self.education_weight
            scoring_rules.experience_weight = self.experience_weight
            scoring_rules.skills_weight = self.skills_weight
            scoring_rules.responsibilities_weight = self.responsibilities_weight
            scoring_rules.education_requirements = self.education_requirements
            scoring_rules.experience_requirements = self.experience_requirements
            scoring_rules.skills_requirements = self.skills_requirements
            scoring_rules.responsibilities_requirements = self.responsibilities_requirements
            scoring_rules.save()
        
        return scoring_rules