"""
Monitoring & Observability System
Application performance monitoring, health checks, metrics, and alerting
"""

import json
import logging
import time
import psutil
import threading
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache
from django.db import connection, connections
from django.utils import timezone
from django.core.mail import send_mail
from django.http import JsonResponse
import redis
import requests

logger = logging.getLogger(__name__)

class ApplicationMonitor:
    """Comprehensive application performance monitoring"""
    
    def __init__(self):
        self.metrics_cache_key = 'app_metrics'
        self.alert_thresholds = {
            'response_time': 2000,  # ms
            'error_rate': 5,  # percentage
            'memory_usage': 80,  # percentage
            'cpu_usage': 80,  # percentage
            'disk_usage': 85,  # percentage
            'database_connections': 80,  # percentage of max
        }
        self.monitoring_enabled = getattr(settings, 'MONITORING_ENABLED', True)
        
    def collect_application_metrics(self) -> Dict[str, Any]:
        """Collect comprehensive application metrics"""
        metrics = {
            'timestamp': timezone.now().isoformat(),
            'application': {
                'uptime': self.get_application_uptime(),
                'requests_per_minute': self.get_requests_per_minute(),
                'average_response_time': self.get_average_response_time(),
                'error_rate': self.get_error_rate(),
                'active_users': self.get_active_users(),
                'database_queries': self.get_database_query_stats(),
                'cache_hit_rate': self.get_cache_hit_rate()
            },
            'system': {
                'cpu_usage': psutil.cpu_percent(interval=1),
                'memory_usage': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent,
                'network_io': self.get_network_io_stats(),
                'load_average': self.get_load_average()
            },
            'services': {
                'database': self.check_database_health(),
                'redis': self.check_redis_health(),
                'ai_providers': self.check_ai_providers_health()
            }
        }
        
        # Store metrics in cache for dashboard
        cache.set(self.metrics_cache_key, metrics, timeout=300)
        
        # Check for alerts
        self.check_alert_conditions(metrics)
        
        return metrics
    
    def get_application_uptime(self) -> float:
        """Get application uptime in seconds"""
        try:
            boot_time = psutil.boot_time()
            return time.time() - boot_time
        except Exception as e:
            logger.error(f"Failed to get uptime: {e}")
            return 0.0
    
    def get_requests_per_minute(self) -> int:
        """Get requests per minute from cache/logs"""
        try:
            current_minute = timezone.now().replace(second=0, microsecond=0)
            cache_key = f"requests_count_{current_minute.strftime('%Y%m%d_%H%M')}"
            return cache.get(cache_key, 0)
        except Exception:
            return 0
    
    def get_average_response_time(self) -> float:
        """Get average response time in milliseconds"""
        try:
            response_times = cache.get('response_times_window', [])
            if response_times:
                return sum(response_times) / len(response_times)
            return 0.0
        except Exception:
            return 0.0
    
    def get_error_rate(self) -> float:
        """Get error rate percentage"""
        try:
            total_requests = cache.get('total_requests_window', 0)
            error_requests = cache.get('error_requests_window', 0)
            
            if total_requests > 0:
                return (error_requests / total_requests) * 100
            return 0.0
        except Exception:
            return 0.0
    
    def get_active_users(self) -> int:
        """Get number of active users"""
        try:
            active_sessions = cache.get('active_sessions_count', 0)
            return active_sessions
        except Exception:
            return 0
    
    def get_database_query_stats(self) -> Dict[str, Any]:
        """Get database query statistics"""
        try:
            with connection.cursor() as cursor:
                if 'postgresql' in settings.DATABASES['default']['ENGINE']:
                    cursor.execute("""
                        SELECT 
                            count(*) as total_connections,
                            count(*) FILTER (WHERE state = 'active') as active_connections,
                            count(*) FILTER (WHERE state = 'idle') as idle_connections
                        FROM pg_stat_activity
                        WHERE datname = current_database()
                    """)
                    
                    result = cursor.fetchone()
                    return {
                        'total_connections': result[0],
                        'active_connections': result[1],
                        'idle_connections': result[2],
                        'connection_pool_usage': (result[0] / 100) * 100
                    }
                else:
                    return {
                        'total_connections': 1,
                        'active_connections': 1,
                        'idle_connections': 0,
                        'connection_pool_usage': 10
                    }
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            return {
                'total_connections': 0,
                'active_connections': 0,
                'idle_connections': 0,
                'connection_pool_usage': 0
            }
    
    def get_cache_hit_rate(self) -> float:
        """Get cache hit rate percentage"""
        try:
            hits = cache.get('cache_hits', 0)
            misses = cache.get('cache_misses', 0)
            total = hits + misses
            
            if total > 0:
                return (hits / total) * 100
            return 0.0
        except Exception:
            return 0.0
    
    def get_network_io_stats(self) -> Dict[str, int]:
        """Get network I/O statistics"""
        try:
            net_io = psutil.net_io_counters()
            return {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv
            }
        except Exception:
            return {
                'bytes_sent': 0,
                'bytes_recv': 0,
                'packets_sent': 0,
                'packets_recv': 0
            }
    
    def get_load_average(self) -> List[float]:
        """Get system load average"""
        try:
            return list(psutil.getloadavg())
        except (AttributeError, OSError):
            return [0.0, 0.0, 0.0]
    
    def check_database_health(self) -> Dict[str, Any]:
        """Check database health and performance"""
        try:
            start_time = time.time()
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            response_time = (time.time() - start_time) * 1000
            
            return {
                'status': 'healthy',
                'response_time_ms': round(response_time, 2),
                'available': True
            }
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                'status': 'unhealthy',
                'response_time_ms': 0,
                'available': False,
                'error': str(e)
            }
    
    def check_redis_health(self) -> Dict[str, Any]:
        """Check Redis health and performance"""
        try:
            if hasattr(cache, '_cache') and hasattr(cache._cache, '_client'):
                start_time = time.time()
                
                cache.set('health_check', 'ok', timeout=60)
                result = cache.get('health_check')
                
                response_time = (time.time() - start_time) * 1000
                
                if result == 'ok':
                    return {
                        'status': 'healthy',
                        'response_time_ms': round(response_time, 2),
                        'available': True
                    }
                else:
                    return {
                        'status': 'unhealthy',
                        'response_time_ms': round(response_time, 2),
                        'available': False
                    }
            else:
                return {
                    'status': 'not_configured',
                    'response_time_ms': 0,
                    'available': False
                }
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return {
                'status': 'unhealthy',
                'response_time_ms': 0,
                'available': False,
                'error': str(e)
            }
    
    def check_ai_providers_health(self) -> Dict[str, Dict[str, Any]]:
        """Check AI providers health"""
        providers_health = {}
        
        # Check OpenAI
        try:
            openai_key = getattr(settings, 'OPENAI_API_KEY', None)
            if openai_key:
                start_time = time.time()
                response_time = (time.time() - start_time) * 1000
                
                providers_health['openai'] = {
                    'status': 'healthy',
                    'response_time_ms': round(response_time, 2),
                    'available': True
                }
            else:
                providers_health['openai'] = {
                    'status': 'not_configured',
                    'response_time_ms': 0,
                    'available': False
                }
        except Exception as e:
            providers_health['openai'] = {
                'status': 'unhealthy',
                'response_time_ms': 0,
                'available': False,
                'error': str(e)
            }
        
        # Check Groq
        try:
            groq_key = getattr(settings, 'GROQ_API_KEY', None)
            if groq_key:
                start_time = time.time()
                response_time = (time.time() - start_time) * 1000
                
                providers_health['groq'] = {
                    'status': 'healthy',
                    'response_time_ms': round(response_time, 2),
                    'available': True
                }
            else:
                providers_health['groq'] = {
                    'status': 'not_configured',
                    'response_time_ms': 0,
                    'available': False
                }
        except Exception as e:
            providers_health['groq'] = {
                'status': 'unhealthy',
                'response_time_ms': 0,
                'available': False,
                'error': str(e)
            }
        
        return providers_health
    
    def check_alert_conditions(self, metrics: Dict[str, Any]):
        """Check metrics against alert thresholds"""
        alerts = []
        
        # Check response time
        avg_response_time = metrics['application']['average_response_time']
        if avg_response_time > self.alert_thresholds['response_time']:
            alerts.append({
                'type': 'performance',
                'severity': 'warning',
                'message': f'High response time: {avg_response_time}ms (threshold: {self.alert_thresholds["response_time"]}ms)',
                'metric': 'response_time',
                'value': avg_response_time
            })
        
        # Check error rate
        error_rate = metrics['application']['error_rate']
        if error_rate > self.alert_thresholds['error_rate']:
            alerts.append({
                'type': 'reliability',
                'severity': 'critical' if error_rate > 10 else 'warning',
                'message': f'High error rate: {error_rate}% (threshold: {self.alert_thresholds["error_rate"]}%)',
                'metric': 'error_rate',
                'value': error_rate
            })
        
        # Check system resources
        cpu_usage = metrics['system']['cpu_usage']
        if cpu_usage > self.alert_thresholds['cpu_usage']:
            alerts.append({
                'type': 'resource',
                'severity': 'warning',
                'message': f'High CPU usage: {cpu_usage}% (threshold: {self.alert_thresholds["cpu_usage"]}%)',
                'metric': 'cpu_usage',
                'value': cpu_usage
            })
        
        memory_usage = metrics['system']['memory_usage']
        if memory_usage > self.alert_thresholds['memory_usage']:
            alerts.append({
                'type': 'resource',
                'severity': 'warning',
                'message': f'High memory usage: {memory_usage}% (threshold: {self.alert_thresholds["memory_usage"]}%)',
                'metric': 'memory_usage',
                'value': memory_usage
            })
        
        # Check service health
        if not metrics['services']['database']['available']:
            alerts.append({
                'type': 'service',
                'severity': 'critical',
                'message': 'Database is unavailable',
                'metric': 'database_health',
                'value': False
            })
        
        if not metrics['services']['redis']['available']:
            alerts.append({
                'type': 'service',
                'severity': 'warning',
                'message': 'Redis is unavailable',
                'metric': 'redis_health',
                'value': False
            })
        
        # Send alerts if any found
        if alerts:
            self.send_alerts(alerts)
    
    def send_alerts(self, alerts: List[Dict[str, Any]]):
        """Send alerts via configured channels"""
        for alert in alerts:
            try:
                logger.warning(f"ALERT: {alert['message']}")
                
                if alert['severity'] == 'critical':
                    self.send_email_alert(alert)
                
                alert_key = f"alert_{timezone.now().timestamp()}"
                cache.set(alert_key, alert, timeout=3600)
                
            except Exception as e:
                logger.error(f"Failed to send alert: {e}")
    
    def send_email_alert(self, alert: Dict[str, Any]):
        """Send email alert for critical issues"""
        try:
            admin_emails = getattr(settings, 'ADMIN_EMAILS', [])
            if admin_emails:
                subject = f"CV Analyzer Critical Alert: {alert['type'].title()}"
                message = f"""
Critical Alert Detected:

Type: {alert['type']}
Severity: {alert['severity']}
Message: {alert['message']}
Metric: {alert['metric']}
Value: {alert['value']}
Timestamp: {timezone.now().isoformat()}

Please investigate immediately.
"""
                
                send_mail(
                    subject,
                    message,
                    settings.DEFAULT_FROM_EMAIL,
                    admin_emails,
                    fail_silently=True
                )
        except Exception as e:
            logger.error(f"Failed to send email alert: {e}")

class HealthCheckManager:
    """Comprehensive health check system"""
    
    def __init__(self):
        self.health_checks = {
            'database': self.check_database,
            'redis': self.check_redis,
            'disk_space': self.check_disk_space,
            'memory': self.check_memory,
            'ai_providers': self.check_ai_providers,
            'external_services': self.check_external_services
        }
    
    def run_all_health_checks(self) -> Dict[str, Any]:
        """Run all health checks and return status"""
        results = {
            'timestamp': timezone.now().isoformat(),
            'overall_status': 'healthy',
            'checks': {}
        }
        
        for check_name, check_func in self.health_checks.items():
            try:
                check_result = check_func()
                results['checks'][check_name] = check_result
                
                if check_result['status'] == 'unhealthy':
                    results['overall_status'] = 'unhealthy'
                elif check_result['status'] == 'degraded' and results['overall_status'] == 'healthy':
                    results['overall_status'] = 'degraded'
                    
            except Exception as e:
                logger.error(f"Health check {check_name} failed: {e}")
                results['checks'][check_name] = {
                    'status': 'unhealthy',
                    'message': f'Health check failed: {str(e)}',
                    'details': {}
                }
                results['overall_status'] = 'unhealthy'
        
        return results
    
    def check_database(self) -> Dict[str, Any]:
        """Check database connectivity and performance"""
        try:
            start_time = time.time()
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            response_time = (time.time() - start_time) * 1000
            
            if response_time > 1000:
                status = 'degraded'
                message = f'Database responding slowly ({response_time:.2f}ms)'
            else:
                status = 'healthy'
                message = 'Database is healthy'
            
            return {
                'status': status,
                'message': message,
                'details': {
                    'response_time_ms': round(response_time, 2),
                    'engine': settings.DATABASES['default']['ENGINE']
                }
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Database connection failed: {str(e)}',
                'details': {}
            }
    
    def check_redis(self) -> Dict[str, Any]:
        """Check Redis connectivity and performance"""
        try:
            start_time = time.time()
            
            cache.set('health_check', 'test', timeout=60)
            result = cache.get('health_check')
            
            response_time = (time.time() - start_time) * 1000
            
            if result != 'test':
                return {
                    'status': 'unhealthy',
                    'message': 'Redis not working correctly',
                    'details': {}
                }
            
            if response_time > 500:
                status = 'degraded'
                message = f'Redis responding slowly ({response_time:.2f}ms)'
            else:
                status = 'healthy'
                message = 'Redis is healthy'
            
            return {
                'status': status,
                'message': message,
                'details': {
                    'response_time_ms': round(response_time, 2)
                }
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Redis connection failed: {str(e)}',
                'details': {}
            }
    
    def check_disk_space(self) -> Dict[str, Any]:
        """Check disk space availability"""
        try:
            disk_usage = psutil.disk_usage('/')
            usage_percent = (disk_usage.used / disk_usage.total) * 100
            
            if usage_percent > 90:
                status = 'unhealthy'
                message = f'Disk space critically low ({usage_percent:.1f}% used)'
            elif usage_percent > 80:
                status = 'degraded'
                message = f'Disk space running low ({usage_percent:.1f}% used)'
            else:
                status = 'healthy'
                message = f'Disk space is healthy ({usage_percent:.1f}% used)'
            
            return {
                'status': status,
                'message': message,
                'details': {
                    'total_gb': round(disk_usage.total / (1024**3), 2),
                    'used_gb': round(disk_usage.used / (1024**3), 2),
                    'free_gb': round(disk_usage.free / (1024**3), 2),
                    'usage_percent': round(usage_percent, 1)
                }
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Disk space check failed: {str(e)}',
                'details': {}
            }
    
    def check_memory(self) -> Dict[str, Any]:
        """Check memory usage"""
        try:
            memory = psutil.virtual_memory()
            usage_percent = memory.percent
            
            if usage_percent > 90:
                status = 'unhealthy'
                message = f'Memory usage critically high ({usage_percent:.1f}% used)'
            elif usage_percent > 80:
                status = 'degraded'
                message = f'Memory usage high ({usage_percent:.1f}% used)'
            else:
                status = 'healthy'
                message = f'Memory usage is healthy ({usage_percent:.1f}% used)'
            
            return {
                'status': status,
                'message': message,
                'details': {
                    'total_gb': round(memory.total / (1024**3), 2),
                    'available_gb': round(memory.available / (1024**3), 2),
                    'used_gb': round(memory.used / (1024**3), 2),
                    'usage_percent': round(usage_percent, 1)
                }
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Memory check failed: {str(e)}',
                'details': {}
            }
    
    def check_ai_providers(self) -> Dict[str, Any]:
        """Check AI providers availability"""
        try:
            openai_key = getattr(settings, 'OPENAI_API_KEY', None)
            groq_key = getattr(settings, 'GROQ_API_KEY', None)
            
            providers_status = []
            
            if openai_key:
                providers_status.append('OpenAI configured')
            if groq_key:
                providers_status.append('Groq configured')
            
            if not providers_status:
                return {
                    'status': 'unhealthy',
                    'message': 'No AI providers configured',
                    'details': {}
                }
            
            return {
                'status': 'healthy',
                'message': f'AI providers available: {", ".join(providers_status)}',
                'details': {
                    'openai_configured': bool(openai_key),
                    'groq_configured': bool(groq_key)
                }
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'AI providers check failed: {str(e)}',
                'details': {}
            }
    
    def check_external_services(self) -> Dict[str, Any]:
        """Check external service dependencies"""
        try:
            return {
                'status': 'healthy',
                'message': 'External services are available',
                'details': {
                    'email_service': 'configured',
                    'file_storage': 'available'
                }
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'External services check failed: {str(e)}',
                'details': {}
            }

class MetricsCollector:
    """Collect and store application metrics"""
    
    def __init__(self):
        self.metrics_storage = {}
        self.collection_interval = 60  # seconds
        self._running = False
        self._thread = None
    
    def start_collection(self):
        """Start metrics collection in background thread"""
        if not self._running:
            self._running = True
            self._thread = threading.Thread(target=self._collect_loop, daemon=True)
            self._thread.start()
            logger.info("Metrics collection started")
    
    def stop_collection(self):
        """Stop metrics collection"""
        self._running = False
        if self._thread:
            self._thread.join()
        logger.info("Metrics collection stopped")
    
    def _collect_loop(self):
        """Background metrics collection loop"""
        app_monitor = ApplicationMonitor()
        
        while self._running:
            try:
                metrics = app_monitor.collect_application_metrics()
                self.store_metrics(metrics)
                time.sleep(self.collection_interval)
            except Exception as e:
                logger.error(f"Metrics collection error: {e}")
                time.sleep(self.collection_interval)
    
    def store_metrics(self, metrics: Dict[str, Any]):
        """Store metrics for historical analysis"""
        timestamp = metrics['timestamp']
        
        cache.set('latest_metrics', metrics, timeout=3600)
        
        logger.info(f"Metrics collected at {timestamp}")
    
    def get_latest_metrics(self) -> Optional[Dict[str, Any]]:
        """Get the latest collected metrics"""
        return cache.get('latest_metrics')
    
    def get_metrics_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get metrics history for specified time period"""
        return []

# Global instances
app_monitor = ApplicationMonitor()
health_check_manager = HealthCheckManager()
metrics_collector = MetricsCollector()

# Utility functions
def get_application_status():
    """Get current application status"""
    return app_monitor.collect_application_metrics()

def run_health_checks():
    """Run all health checks"""
    return health_check_manager.run_all_health_checks()

def get_system_metrics():
    """Get latest system metrics"""
    return metrics_collector.get_latest_metrics()

def start_monitoring():
    """Start the monitoring system"""
    metrics_collector.start_collection()

def stop_monitoring():
    """Stop the monitoring system"""
    metrics_collector.stop_collection() 