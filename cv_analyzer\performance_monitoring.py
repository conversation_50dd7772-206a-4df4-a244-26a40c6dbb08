"""
Performance Monitoring and Optimization Module
Handles application performance monitoring, metrics collection, and optimization
"""

import time
import psutil
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from django.db import connection
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
from django.http import HttpRequest, HttpResponse
from functools import wraps
import json

logger = logging.getLogger(__name__)

class PerformanceMetrics:
    """Collects and manages performance metrics"""
    
    def __init__(self):
        self.metrics_storage = {}
        self.metric_history = []
        self.performance_thresholds = {
            'response_time_ms': 2000,  # 2 seconds
            'database_query_time_ms': 500,  # 500ms
            'memory_usage_percent': 80,
            'cpu_usage_percent': 75,
            'cache_hit_ratio': 70
        }
    
    def record_metric(self, metric_name: str, value: float, timestamp: datetime = None, 
                     tags: Dict[str, str] = None):
        """Record a performance metric"""
        if timestamp is None:
            timestamp = timezone.now()
        
        metric_entry = {
            'name': metric_name,
            'value': value,
            'timestamp': timestamp,
            'tags': tags or {}
        }
        
        # Store in memory (for development/testing)
        if metric_name not in self.metrics_storage:
            self.metrics_storage[metric_name] = []
        
        self.metrics_storage[metric_name].append(metric_entry)
        
        # Keep only last 1000 entries per metric
        if len(self.metrics_storage[metric_name]) > 1000:
            self.metrics_storage[metric_name] = self.metrics_storage[metric_name][-1000:]
        
        # Add to history
        self.metric_history.append(metric_entry)
        if len(self.metric_history) > 10000:
            self.metric_history = self.metric_history[-10000:]
    
    def get_metric_summary(self, metric_name: str, 
                          time_range: timedelta = timedelta(hours=1)) -> Dict[str, Any]:
        """Get summary statistics for a metric"""
        cutoff_time = timezone.now() - time_range
        
        metrics = self.metrics_storage.get(metric_name, [])
        recent_metrics = [
            m for m in metrics 
            if m['timestamp'] >= cutoff_time
        ]
        
        if not recent_metrics:
            return {
                'metric_name': metric_name,
                'count': 0,
                'time_range_hours': time_range.total_seconds() / 3600
            }
        
        values = [m['value'] for m in recent_metrics]
        
        return {
            'metric_name': metric_name,
            'count': len(values),
            'min': min(values),
            'max': max(values),
            'avg': sum(values) / len(values),
            'latest': values[-1] if values else None,
            'time_range_hours': time_range.total_seconds() / 3600,
            'threshold': self.performance_thresholds.get(metric_name),
            'threshold_violations': len([v for v in values if self._violates_threshold(metric_name, v)])
        }
    
    def _violates_threshold(self, metric_name: str, value: float) -> bool:
        """Check if metric value violates threshold"""
        threshold = self.performance_thresholds.get(metric_name)
        if threshold is None:
            return False
        
        # For cache hit ratio, lower is worse
        if 'hit_ratio' in metric_name:
            return value < threshold
        
        # For most metrics, higher is worse
        return value > threshold
    
    def get_performance_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive performance dashboard data"""
        dashboard = {
            'timestamp': timezone.now().isoformat(),
            'system_metrics': self._get_system_metrics(),
            'application_metrics': self._get_application_metrics(),
            'database_metrics': self._get_database_metrics(),
            'cache_metrics': self._get_cache_metrics(),
            'alerts': self._get_performance_alerts()
        }
        
        return dashboard
    
    def _get_system_metrics(self) -> Dict[str, Any]:
        """Get system-level performance metrics"""
        try:
            return {
                'cpu_usage_percent': psutil.cpu_percent(interval=1),
                'memory_usage_percent': psutil.virtual_memory().percent,
                'disk_usage_percent': psutil.disk_usage('/').percent,
                'load_average': list(psutil.getloadavg()) if hasattr(psutil, 'getloadavg') else None,
                'network_io': dict(psutil.net_io_counters()._asdict()),
                'process_count': len(psutil.pids())
            }
        except Exception as e:
            return {'error': str(e)}
    
    def _get_application_metrics(self) -> Dict[str, Any]:
        """Get application-level performance metrics"""
        return {
            'response_times': self.get_metric_summary('response_time_ms'),
            'request_count': self.get_metric_summary('request_count'),
            'error_rate': self.get_metric_summary('error_rate'),
            'active_sessions': self.get_metric_summary('active_sessions')
        }
    
    def _get_database_metrics(self) -> Dict[str, Any]:
        """Get database performance metrics"""
        return {
            'query_time': self.get_metric_summary('database_query_time_ms'),
            'query_count': self.get_metric_summary('database_query_count'),
            'connection_count': self.get_metric_summary('database_connections')
        }
    
    def _get_cache_metrics(self) -> Dict[str, Any]:
        """Get cache performance metrics"""
        return {
            'hit_ratio': self.get_metric_summary('cache_hit_ratio'),
            'miss_ratio': self.get_metric_summary('cache_miss_ratio'),
            'cache_operations': self.get_metric_summary('cache_operations')
        }
    
    def _get_performance_alerts(self) -> List[Dict[str, Any]]:
        """Get current performance alerts"""
        alerts = []
        
        # Check system metrics
        system_metrics = self._get_system_metrics()
        
        if system_metrics.get('cpu_usage_percent', 0) > 80:
            alerts.append({
                'level': 'warning',
                'metric': 'cpu_usage',
                'value': system_metrics['cpu_usage_percent'],
                'threshold': 80,
                'message': 'High CPU usage detected'
            })
        
        if system_metrics.get('memory_usage_percent', 0) > 85:
            alerts.append({
                'level': 'critical',
                'metric': 'memory_usage',
                'value': system_metrics['memory_usage_percent'],
                'threshold': 85,
                'message': 'Critical memory usage detected'
            })
        
        # Check application metrics
        response_time_summary = self.get_metric_summary('response_time_ms')
        if (response_time_summary.get('avg', 0) > self.performance_thresholds['response_time_ms']):
            alerts.append({
                'level': 'warning',
                'metric': 'response_time',
                'value': response_time_summary['avg'],
                'threshold': self.performance_thresholds['response_time_ms'],
                'message': 'Slow response times detected'
            })
        
        return alerts

class PerformanceProfiler:
    """Profiles application performance"""
    
    def __init__(self):
        self.metrics = PerformanceMetrics()
        self.profiling_enabled = True
    
    def profile_function(self, func_name: str = None):
        """Decorator to profile function execution time"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                if not self.profiling_enabled:
                    return func(*args, **kwargs)
                
                name = func_name or f"{func.__module__}.{func.__name__}"
                start_time = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    execution_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                    
                    self.metrics.record_metric(
                        f"function_execution_time_ms",
                        execution_time,
                        tags={'function_name': name, 'status': 'success'}
                    )
                    
                    return result
                
                except Exception as e:
                    execution_time = (time.time() - start_time) * 1000
                    
                    self.metrics.record_metric(
                        f"function_execution_time_ms",
                        execution_time,
                        tags={'function_name': name, 'status': 'error'}
                    )
                    
                    raise e
            
            return wrapper
        return decorator
    
    def profile_database_query(self, query_description: str = "query"):
        """Context manager to profile database queries"""
        class DatabaseProfiler:
            def __init__(self, profiler_instance, description):
                self.profiler = profiler_instance
                self.description = description
                self.start_time = None
                
            def __enter__(self):
                self.start_time = time.time()
                return self
                
            def __exit__(self, exc_type, exc_val, exc_tb):
                if self.start_time:
                    execution_time = (time.time() - self.start_time) * 1000
                    
                    self.profiler.metrics.record_metric(
                        "database_query_time_ms",
                        execution_time,
                        tags={
                            'query_description': self.description,
                            'status': 'success' if exc_type is None else 'error'
                        }
                    )
        
        return DatabaseProfiler(self, query_description)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        report = {
            'timestamp': timezone.now().isoformat(),
            'summary': {},
            'function_performance': {},
            'database_performance': {},
            'recommendations': []
        }
        
        # Function performance summary
        func_metrics = self.metrics.get_metric_summary('function_execution_time_ms')
        report['function_performance'] = func_metrics
        
        # Database performance summary
        db_metrics = self.metrics.get_metric_summary('database_query_time_ms')
        report['database_performance'] = db_metrics
        
        # Generate recommendations
        recommendations = []
        
        if func_metrics.get('avg', 0) > 1000:  # 1 second
            recommendations.append("Some functions are taking longer than 1 second to execute")
        
        if db_metrics.get('avg', 0) > 500:  # 500ms
            recommendations.append("Database queries are slower than recommended threshold")
        
        if func_metrics.get('threshold_violations', 0) > 10:
            recommendations.append("Multiple function performance threshold violations detected")
        
        recommendations.extend([
            "Consider implementing caching for slow operations",
            "Review and optimize database queries",
            "Monitor resource usage during peak hours",
            "Implement connection pooling for database connections"
        ])
        
        report['recommendations'] = recommendations
        
        return report

class PerformanceMiddleware:
    """Middleware to monitor request performance"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.metrics = PerformanceMetrics()
    
    def __call__(self, request: HttpRequest) -> HttpResponse:
        start_time = time.time()
        
        # Record request start
        self.metrics.record_metric(
            'request_count', 
            1, 
            tags={
                'method': request.method,
                'path': request.path[:100]  # Truncate long paths
            }
        )
        
        response = self.get_response(request)
        
        # Calculate response time
        response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        # Record response metrics
        self.metrics.record_metric(
            'response_time_ms',
            response_time,
            tags={
                'method': request.method,
                'path': request.path[:100],
                'status_code': str(response.status_code)
            }
        )
        
        # Record error rate
        error_occurred = response.status_code >= 400
        self.metrics.record_metric(
            'error_rate',
            1 if error_occurred else 0,
            tags={
                'method': request.method,
                'status_code': str(response.status_code)
            }
        )
        
        # Add performance headers for debugging
        if settings.DEBUG:
            response['X-Response-Time'] = f"{response_time:.2f}ms"
            response['X-Database-Queries'] = str(len(connection.queries))
        
        return response

class ResourceMonitor:
    """Monitors system resources and application health"""
    
    def __init__(self):
        self.monitoring_active = False
        self.monitor_thread = None
        self.metrics = PerformanceMetrics()
    
    def start_monitoring(self, interval_seconds: int = 60):
        """Start continuous resource monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval_seconds,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info(f"Started resource monitoring with {interval_seconds}s interval")
    
    def stop_monitoring(self):
        """Stop resource monitoring"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Stopped resource monitoring")
    
    def _monitoring_loop(self, interval_seconds: int):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                # System metrics
                self.metrics.record_metric('cpu_usage_percent', psutil.cpu_percent())
                self.metrics.record_metric('memory_usage_percent', psutil.virtual_memory().percent)
                self.metrics.record_metric('disk_usage_percent', psutil.disk_usage('/').percent)
                
                # Database connections
                try:
                    with connection.cursor() as cursor:
                        cursor.execute("SELECT 1")
                        self.metrics.record_metric('database_connection_status', 1)
                except Exception:
                    self.metrics.record_metric('database_connection_status', 0)
                
                # Cache status
                try:
                    cache.get('health_check_key')
                    self.metrics.record_metric('cache_connection_status', 1)
                except Exception:
                    self.metrics.record_metric('cache_connection_status', 0)
                
                # Application health
                self._record_application_health()
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {str(e)}")
            
            time.sleep(interval_seconds)
    
    def _record_application_health(self):
        """Record application-specific health metrics"""
        try:
            from .models import CV, Vacancy, SecurityAuditLog
            
            # Count recent activities
            now = timezone.now()
            last_hour = now - timedelta(hours=1)
            
            recent_uploads = CV.objects.filter(uploaded_at__gte=last_hour).count()
            recent_security_events = SecurityAuditLog.objects.filter(timestamp__gte=last_hour).count()
            
            self.metrics.record_metric('recent_cv_uploads', recent_uploads)
            self.metrics.record_metric('recent_security_events', recent_security_events)
            
            # Application status
            self.metrics.record_metric('application_health', 1)
            
        except Exception as e:
            self.metrics.record_metric('application_health', 0)
            logger.error(f"Error recording application health: {str(e)}")
    
    def get_health_check(self) -> Dict[str, Any]:
        """Get application health check status"""
        health_status = {
            'timestamp': timezone.now().isoformat(),
            'status': 'healthy',
            'checks': {},
            'metrics': {}
        }
        
        try:
            # Database check
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                health_status['checks']['database'] = {'status': 'ok'}
        except Exception as e:
            health_status['checks']['database'] = {'status': 'error', 'error': str(e)}
            health_status['status'] = 'unhealthy'
        
        # Cache check
        try:
            test_key = 'health_check_test'
            cache.set(test_key, 'test_value', 10)
            if cache.get(test_key) == 'test_value':
                health_status['checks']['cache'] = {'status': 'ok'}
            else:
                health_status['checks']['cache'] = {'status': 'error', 'error': 'Cache read/write failed'}
                health_status['status'] = 'degraded'
        except Exception as e:
            health_status['checks']['cache'] = {'status': 'error', 'error': str(e)}
            health_status['status'] = 'degraded'
        
        # System resources check
        cpu_usage = psutil.cpu_percent()
        memory_usage = psutil.virtual_memory().percent
        
        health_status['checks']['system_resources'] = {
            'status': 'ok' if cpu_usage < 90 and memory_usage < 90 else 'warning',
            'cpu_usage': cpu_usage,
            'memory_usage': memory_usage
        }
        
        if cpu_usage > 95 or memory_usage > 95:
            health_status['status'] = 'unhealthy'
        
        # Add performance metrics
        health_status['metrics'] = {
            'response_time': self.metrics.get_metric_summary('response_time_ms', timedelta(minutes=5)),
            'error_rate': self.metrics.get_metric_summary('error_rate', timedelta(minutes=5)),
            'system_load': {
                'cpu': cpu_usage,
                'memory': memory_usage
            }
        }
        
        return health_status

# Global instances
performance_metrics = PerformanceMetrics()
performance_profiler = PerformanceProfiler()
resource_monitor = ResourceMonitor()

# Utility functions
def get_performance_dashboard() -> Dict[str, Any]:
    """Get performance dashboard data"""
    return performance_metrics.get_performance_dashboard()

def get_performance_report() -> Dict[str, Any]:
    """Get comprehensive performance report"""
    return performance_profiler.get_performance_report()

def get_health_check() -> Dict[str, Any]:
    """Get application health check"""
    return resource_monitor.get_health_check()

def start_performance_monitoring(interval_seconds: int = 60):
    """Start performance monitoring"""
    resource_monitor.start_monitoring(interval_seconds)

def stop_performance_monitoring():
    """Stop performance monitoring"""
    resource_monitor.stop_monitoring()

# Decorators for easy use
def monitor_performance(func_name: str = None):
    """Decorator to monitor function performance"""
    return performance_profiler.profile_function(func_name)

def monitor_database_query(description: str = "query"):
    """Context manager to monitor database queries"""
    return performance_profiler.profile_database_query(description) 