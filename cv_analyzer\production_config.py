"""
Production Environment Configuration System
Handles production-specific configurations, SSL setup, load balancer configuration, and CDN setup.
"""

import os
import json
import yaml
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass, asdict
import boto3
import ssl
import socket
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

@dataclass
class SSLConfig:
    """SSL/TLS configuration"""
    certificate_arn: str
    domain_names: List[str]
    certificate_path: Optional[str] = None
    private_key_path: Optional[str] = None
    ca_bundle_path: Optional[str] = None
    ssl_protocols: List[str] = None
    cipher_suites: List[str] = None
    
    def __post_init__(self):
        if self.ssl_protocols is None:
            self.ssl_protocols = ['TLSv1.2', 'TLSv1.3']
        if self.cipher_suites is None:
            self.cipher_suites = [
                'ECDHE-RSA-AES256-GCM-SHA384',
                'ECDHE-RSA-AES128-GCM-SHA256',
                'ECDHE-RSA-AES256-SHA384',
                'ECDHE-RSA-AES128-SHA256'
            ]

@dataclass
class LoadBalancerConfig:
    """Load balancer configuration"""
    name: str
    scheme: str  # internet-facing or internal
    type: str    # application or network
    subnets: List[str]
    security_groups: List[str]
    target_groups: List[Dict[str, Any]]
    listeners: List[Dict[str, Any]]
    health_check: Dict[str, Any]
    sticky_sessions: bool = False
    cross_zone_load_balancing: bool = True

@dataclass
class CDNConfig:
    """CDN configuration"""
    distribution_id: Optional[str] = None
    domain_name: str = ""
    origin_domain: str = ""
    cache_behaviors: List[Dict[str, Any]] = None
    custom_error_responses: List[Dict[str, Any]] = None
    logging_config: Optional[Dict[str, str]] = None
    
    def __post_init__(self):
        if self.cache_behaviors is None:
            self.cache_behaviors = []
        if self.custom_error_responses is None:
            self.custom_error_responses = []

class ProductionEnvironmentConfig:
    """Production environment configuration manager"""
    
    def __init__(self, environment: str = "production"):
        self.environment = environment
        self.config_path = Path(f"config/{environment}")
        self.config_path.mkdir(parents=True, exist_ok=True)
        
        # AWS clients
        self.ec2_client = boto3.client('ec2')
        self.elbv2_client = boto3.client('elbv2')
        self.acm_client = boto3.client('acm')
        self.cloudfront_client = boto3.client('cloudfront')
        self.route53_client = boto3.client('route53')
        
        # Load environment configuration
        self.config = self._load_environment_config()
    
    def _load_environment_config(self) -> Dict[str, Any]:
        """Load environment-specific configuration"""
        config_file = self.config_path / "environment.yaml"
        
        if config_file.exists():
            with open(config_file, 'r') as f:
                return yaml.safe_load(f)
        
        # Default production configuration
        default_config = {
            'database': {
                'engine': 'postgresql',
                'host': os.getenv('PROD_DB_HOST', 'cv-analyzer-prod-db.cluster-xyz.us-east-1.rds.amazonaws.com'),
                'port': int(os.getenv('PROD_DB_PORT', '5432')),
                'name': os.getenv('PROD_DB_NAME', 'cv_analyzer_prod'),
                'user': os.getenv('PROD_DB_USER', 'cv_analyzer_admin'),
                'password': os.getenv('PROD_DB_PASSWORD'),
                'ssl_mode': 'require',
                'max_connections': 100,
                'connection_timeout': 30
            },
            'redis': {
                'host': os.getenv('PROD_REDIS_HOST', 'cv-analyzer-prod-redis.cluster-xyz.cache.amazonaws.com'),
                'port': int(os.getenv('PROD_REDIS_PORT', '6379')),
                'password': os.getenv('PROD_REDIS_PASSWORD'),
                'ssl': True,
                'db': 0,
                'max_connections': 50
            },
            'application': {
                'debug': False,
                'secret_key': os.getenv('DJANGO_SECRET_KEY'),
                'allowed_hosts': [
                    'cv-analyzer.com',
                    'www.cv-analyzer.com',
                    'api.cv-analyzer.com'
                ],
                'cors_allowed_origins': [
                    'https://cv-analyzer.com',
                    'https://www.cv-analyzer.com'
                ],
                'secure_ssl_redirect': True,
                'secure_proxy_ssl_header': ('HTTP_X_FORWARDED_PROTO', 'https'),
                'session_cookie_secure': True,
                'csrf_cookie_secure': True,
                'secure_browser_xss_filter': True,
                'secure_content_type_nosniff': True,
                'x_frame_options': 'DENY'
            },
            'logging': {
                'level': 'INFO',
                'format': 'json',
                'handlers': ['file', 'cloudwatch'],
                'log_group': 'cv-analyzer-production',
                'retention_days': 30
            },
            'monitoring': {
                'enabled': True,
                'metrics_endpoint': '/metrics/',
                'health_check_endpoint': '/health/',
                'apm_enabled': True,
                'error_tracking_enabled': True
            },
            'backup': {
                'enabled': True,
                'schedule': '0 2 * * *',  # Daily at 2 AM
                's3_bucket': 'cv-analyzer-prod-backups',
                'retention_days': 30,
                'encryption': True
            },
            'email': {
                'backend': 'django_ses.SESBackend',
                'ses_region': 'us-east-1',
                'default_from_email': '<EMAIL>',
                'admin_email': '<EMAIL>'
            },
            'file_storage': {
                'backend': 'storages.backends.s3boto3.S3Boto3Storage',
                's3_bucket': 'cv-analyzer-prod-media',
                's3_region': 'us-east-1',
                'cloudfront_domain': 'cdn.cv-analyzer.com',
                'max_file_size': 10 * 1024 * 1024,  # 10MB
                'allowed_extensions': ['.pdf', '.doc', '.docx', '.txt']
            },
            'security': {
                'rate_limiting': {
                    'enabled': True,
                    'default_rate': '100/hour',
                    'login_rate': '5/minute',
                    'api_rate': '1000/hour'
                },
                'csrf_protection': True,
                'content_security_policy': {
                    'default-src': "'self'",
                    'script-src': "'self' 'unsafe-inline'",
                    'style-src': "'self' 'unsafe-inline'",
                    'img-src': "'self' data: https:",
                    'font-src': "'self'",
                    'connect-src': "'self'",
                    'frame-ancestors': "'none'"
                }
            }
        }
        
        # Save default configuration
        with open(config_file, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False)
        
        return default_config
    
    def setup_ssl_certificates(self, ssl_config: SSLConfig) -> str:
        """Setup SSL certificates using AWS Certificate Manager"""
        logger.info(f"Setting up SSL certificates for domains: {ssl_config.domain_names}")
        
        try:
            # Request certificate
            response = self.acm_client.request_certificate(
                DomainName=ssl_config.domain_names[0],
                SubjectAlternativeNames=ssl_config.domain_names[1:] if len(ssl_config.domain_names) > 1 else [],
                ValidationMethod='DNS'
            )
            
            certificate_arn = response['CertificateArn']
            
            # Wait for certificate validation
            waiter = self.acm_client.get_waiter('certificate_validated')
            waiter.wait(CertificateArn=certificate_arn)
            
            logger.info(f"SSL certificate created: {certificate_arn}")
            return certificate_arn
            
        except Exception as e:
            logger.error(f"Failed to setup SSL certificate: {e}")
            raise
    
    def setup_load_balancer(self, lb_config: LoadBalancerConfig) -> str:
        """Setup Application Load Balancer"""
        logger.info(f"Setting up load balancer: {lb_config.name}")
        
        try:
            # Create load balancer
            response = self.elbv2_client.create_load_balancer(
                Name=lb_config.name,
                Subnets=lb_config.subnets,
                SecurityGroups=lb_config.security_groups,
                Scheme=lb_config.scheme,
                Type=lb_config.type,
                Tags=[
                    {'Key': 'Environment', 'Value': self.environment},
                    {'Key': 'Application', 'Value': 'cv-analyzer'}
                ]
            )
            
            lb_arn = response['LoadBalancers'][0]['LoadBalancerArn']
            lb_dns_name = response['LoadBalancers'][0]['DNSName']
            
            # Create target groups
            target_group_arns = []
            for tg_config in lb_config.target_groups:
                tg_response = self.elbv2_client.create_target_group(
                    Name=tg_config['name'],
                    Protocol=tg_config['protocol'],
                    Port=tg_config['port'],
                    VpcId=tg_config['vpc_id'],
                    HealthCheckProtocol=lb_config.health_check['protocol'],
                    HealthCheckPath=lb_config.health_check['path'],
                    HealthCheckIntervalSeconds=lb_config.health_check['interval'],
                    HealthyThresholdCount=lb_config.health_check['healthy_threshold'],
                    UnhealthyThresholdCount=lb_config.health_check['unhealthy_threshold'],
                    Tags=[
                        {'Key': 'Environment', 'Value': self.environment},
                        {'Key': 'Application', 'Value': 'cv-analyzer'}
                    ]
                )
                target_group_arns.append(tg_response['TargetGroups'][0]['TargetGroupArn'])
            
            # Create listeners
            for listener_config in lb_config.listeners:
                self.elbv2_client.create_listener(
                    LoadBalancerArn=lb_arn,
                    Protocol=listener_config['protocol'],
                    Port=listener_config['port'],
                    SslPolicy=listener_config.get('ssl_policy'),
                    Certificates=[{'CertificateArn': listener_config.get('certificate_arn')}] if listener_config.get('certificate_arn') else [],
                    DefaultActions=[{
                        'Type': 'forward',
                        'TargetGroupArn': target_group_arns[0]
                    }]
                )
            
            logger.info(f"Load balancer created: {lb_arn}")
            logger.info(f"Load balancer DNS: {lb_dns_name}")
            
            return lb_arn
            
        except Exception as e:
            logger.error(f"Failed to setup load balancer: {e}")
            raise
    
    def setup_cdn(self, cdn_config: CDNConfig) -> str:
        """Setup CloudFront CDN"""
        logger.info(f"Setting up CDN for domain: {cdn_config.domain_name}")
        
        try:
            distribution_config = {
                'CallerReference': f"cv-analyzer-{self.environment}-{int(os.urandom(4).hex(), 16)}",
                'Comment': f"CV Analyzer {self.environment} CDN",
                'Enabled': True,
                'Origins': {
                    'Quantity': 1,
                    'Items': [{
                        'Id': 'cv-analyzer-origin',
                        'DomainName': cdn_config.origin_domain,
                        'CustomOriginConfig': {
                            'HTTPPort': 80,
                            'HTTPSPort': 443,
                            'OriginProtocolPolicy': 'https-only',
                            'OriginSslProtocols': {
                                'Quantity': 2,
                                'Items': ['TLSv1.2', 'TLSv1.3']
                            }
                        }
                    }]
                },
                'DefaultCacheBehavior': {
                    'TargetOriginId': 'cv-analyzer-origin',
                    'ViewerProtocolPolicy': 'redirect-to-https',
                    'TrustedSigners': {
                        'Enabled': False,
                        'Quantity': 0
                    },
                    'ForwardedValues': {
                        'QueryString': True,
                        'Cookies': {'Forward': 'all'},
                        'Headers': {
                            'Quantity': 3,
                            'Items': ['Host', 'CloudFront-Forwarded-Proto', 'CloudFront-Is-Mobile-Viewer']
                        }
                    },
                    'MinTTL': 0,
                    'DefaultTTL': 86400,
                    'MaxTTL': 31536000,
                    'Compress': True
                },
                'PriceClass': 'PriceClass_100',
                'ViewerCertificate': {
                    'CloudFrontDefaultCertificate': True
                }
            }
            
            # Add custom cache behaviors
            if cdn_config.cache_behaviors:
                distribution_config['CacheBehaviors'] = {
                    'Quantity': len(cdn_config.cache_behaviors),
                    'Items': cdn_config.cache_behaviors
                }
            
            # Add custom error responses
            if cdn_config.custom_error_responses:
                distribution_config['CustomErrorResponses'] = {
                    'Quantity': len(cdn_config.custom_error_responses),
                    'Items': cdn_config.custom_error_responses
                }
            
            # Create distribution
            response = self.cloudfront_client.create_distribution(
                DistributionConfig=distribution_config
            )
            
            distribution_id = response['Distribution']['Id']
            domain_name = response['Distribution']['DomainName']
            
            logger.info(f"CDN distribution created: {distribution_id}")
            logger.info(f"CDN domain name: {domain_name}")
            
            return distribution_id
            
        except Exception as e:
            logger.error(f"Failed to setup CDN: {e}")
            raise
    
    def setup_dns_records(self, domain_name: str, load_balancer_dns: str, cdn_domain: str) -> bool:
        """Setup DNS records for the application"""
        logger.info(f"Setting up DNS records for domain: {domain_name}")
        
        try:
            # Get hosted zone
            hosted_zones = self.route53_client.list_hosted_zones()
            hosted_zone_id = None
            
            for zone in hosted_zones['HostedZones']:
                if domain_name.endswith(zone['Name'].rstrip('.')):
                    hosted_zone_id = zone['Id']
                    break
            
            if not hosted_zone_id:
                raise ValueError(f"Hosted zone not found for domain: {domain_name}")
            
            # Create A record for main domain
            self.route53_client.change_resource_record_sets(
                HostedZoneId=hosted_zone_id,
                ChangeBatch={
                    'Changes': [{
                        'Action': 'UPSERT',
                        'ResourceRecordSet': {
                            'Name': domain_name,
                            'Type': 'A',
                            'AliasTarget': {
                                'DNSName': load_balancer_dns,
                                'EvaluateTargetHealth': True,
                                'HostedZoneId': self._get_load_balancer_hosted_zone_id()
                            }
                        }
                    }]
                }
            )
            
            # Create CNAME record for CDN
            self.route53_client.change_resource_record_sets(
                HostedZoneId=hosted_zone_id,
                ChangeBatch={
                    'Changes': [{
                        'Action': 'UPSERT',
                        'ResourceRecordSet': {
                            'Name': f'cdn.{domain_name}',
                            'Type': 'CNAME',
                            'TTL': 300,
                            'ResourceRecords': [{'Value': cdn_domain}]
                        }
                    }]
                }
            )
            
            logger.info(f"DNS records created for domain: {domain_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup DNS records: {e}")
            raise
    
    def _get_load_balancer_hosted_zone_id(self) -> str:
        """Get the hosted zone ID for the load balancer region"""
        # AWS Load Balancer hosted zone IDs by region
        zone_ids = {
            'us-east-1': 'Z35SXDOTRQ7X7K',
            'us-east-2': 'Z3AADJGX6KTTL2',
            'us-west-1': 'Z368ELLRRE2KJ0',
            'us-west-2': 'Z1H1FL5HABSF5',
            'eu-west-1': 'Z32O12XQLNTSW2',
            'eu-central-1': 'Z3F0SRJ5LGBH90',
            'ap-southeast-1': 'Z1LMS91P8CMLE5',
            'ap-southeast-2': 'Z1GM3OXH4ZPM65'
        }
        
        region = boto3.Session().region_name
        return zone_ids.get(region, 'Z35SXDOTRQ7X7K')  # Default to us-east-1
    
    def validate_ssl_certificate(self, domain: str, port: int = 443) -> Dict[str, Any]:
        """Validate SSL certificate for a domain"""
        logger.info(f"Validating SSL certificate for {domain}:{port}")
        
        try:
            context = ssl.create_default_context()
            with socket.create_connection((domain, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=domain) as ssock:
                    cert = ssock.getpeercert()
                    
                    return {
                        'valid': True,
                        'subject': dict(x[0] for x in cert['subject']),
                        'issuer': dict(x[0] for x in cert['issuer']),
                        'version': cert['version'],
                        'serial_number': cert['serialNumber'],
                        'not_before': cert['notBefore'],
                        'not_after': cert['notAfter'],
                        'subject_alt_names': [x[1] for x in cert.get('subjectAltName', [])],
                        'protocol': ssock.version(),
                        'cipher': ssock.cipher()
                    }
                    
        except Exception as e:
            logger.error(f"SSL validation failed for {domain}: {e}")
            return {
                'valid': False,
                'error': str(e)
            }
    
    def generate_security_config(self) -> Dict[str, Any]:
        """Generate security configuration"""
        return {
            'security_headers': {
                'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': 'DENY',
                'X-XSS-Protection': '1; mode=block',
                'Referrer-Policy': 'strict-origin-when-cross-origin',
                'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';"
            },
            'cors_settings': {
                'allowed_origins': self.config['application']['cors_allowed_origins'],
                'allowed_methods': ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
                'allowed_headers': ['Accept', 'Authorization', 'Content-Type', 'X-CSRFToken'],
                'max_age': 86400
            },
            'rate_limiting': self.config['security']['rate_limiting']
        }
    
    def generate_kubernetes_config(self) -> Dict[str, Any]:
        """Generate Kubernetes configuration"""
        return {
            'apiVersion': 'v1',
            'kind': 'ConfigMap',
            'metadata': {
                'name': f'cv-analyzer-{self.environment}-config',
                'namespace': self.environment
            },
            'data': {
                'DATABASE_URL': f"postgresql://{self.config['database']['user']}:{self.config['database']['password']}@{self.config['database']['host']}:{self.config['database']['port']}/{self.config['database']['name']}",
                'REDIS_URL': f"redis://:{self.config['redis']['password']}@{self.config['redis']['host']}:{self.config['redis']['port']}/{self.config['redis']['db']}",
                'DJANGO_SETTINGS_MODULE': f'cv_analyzer.settings.{self.environment}',
                'DJANGO_SECRET_KEY': self.config['application']['secret_key'],
                'ALLOWED_HOSTS': ','.join(self.config['application']['allowed_hosts']),
                'DEBUG': str(self.config['application']['debug']).lower()
            }
        }
    
    def save_config(self, config_type: str, config_data: Dict[str, Any]):
        """Save configuration to file"""
        config_file = self.config_path / f"{config_type}.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False)
        
        logger.info(f"Configuration saved: {config_file}")
    
    def load_config(self, config_type: str) -> Optional[Dict[str, Any]]:
        """Load configuration from file"""
        config_file = self.config_path / f"{config_type}.yaml"
        if config_file.exists():
            with open(config_file, 'r') as f:
                return yaml.safe_load(f)
        return None
    
    def get_environment_variables(self) -> Dict[str, str]:
        """Get environment variables for production deployment"""
        return {
            'DJANGO_SETTINGS_MODULE': f'cv_analyzer.settings.{self.environment}',
            'DATABASE_URL': f"postgresql://{self.config['database']['user']}:{self.config['database']['password']}@{self.config['database']['host']}:{self.config['database']['port']}/{self.config['database']['name']}",
            'REDIS_URL': f"redis://:{self.config['redis']['password']}@{self.config['redis']['host']}:{self.config['redis']['port']}/{self.config['redis']['db']}",
            'DJANGO_SECRET_KEY': self.config['application']['secret_key'],
            'ALLOWED_HOSTS': ','.join(self.config['application']['allowed_hosts']),
            'DEBUG': str(self.config['application']['debug']).lower(),
            'EMAIL_BACKEND': self.config['email']['backend'],
            'DEFAULT_FROM_EMAIL': self.config['email']['default_from_email'],
            'AWS_STORAGE_BUCKET_NAME': self.config['file_storage']['s3_bucket'],
            'AWS_S3_REGION_NAME': self.config['file_storage']['s3_region'],
            'CLOUDFRONT_DOMAIN': self.config['file_storage']['cloudfront_domain'],
            'SENTRY_DSN': os.getenv('SENTRY_DSN', ''),
            'ENVIRONMENT': self.environment
        }

# Example usage
if __name__ == "__main__":
    # Initialize production environment
    prod_env = ProductionEnvironmentConfig('production')
    
    # Setup SSL certificates
    ssl_config = SSLConfig(
        certificate_arn="",
        domain_names=['cv-analyzer.com', 'www.cv-analyzer.com', 'api.cv-analyzer.com']
    )
    
    # Setup load balancer
    lb_config = LoadBalancerConfig(
        name='cv-analyzer-prod-lb',
        scheme='internet-facing',
        type='application',
        subnets=['subnet-12345', 'subnet-67890'],
        security_groups=['sg-12345'],
        target_groups=[{
            'name': 'cv-analyzer-web-tg',
            'protocol': 'HTTP',
            'port': 80,
            'vpc_id': 'vpc-12345'
        }],
        listeners=[{
            'protocol': 'HTTPS',
            'port': 443,
            'ssl_policy': 'ELBSecurityPolicy-TLS-1-2-2017-01',
            'certificate_arn': ssl_config.certificate_arn
        }],
        health_check={
            'protocol': 'HTTP',
            'path': '/health/',
            'interval': 30,
            'healthy_threshold': 2,
            'unhealthy_threshold': 5
        }
    )
    
    # Setup CDN
    cdn_config = CDNConfig(
        domain_name='cdn.cv-analyzer.com',
        origin_domain='cv-analyzer.com'
    )
    
    print("Production environment configuration loaded successfully")
    print(f"Environment variables: {len(prod_env.get_environment_variables())} variables configured") 