"""
Progressive Web App Features
Offline support, push notifications, and app-like experience
"""

import json
import logging
from typing import Dict, List, Optional, Any
from django.http import JsonResponse, HttpResponse
from django.template.loader import render_to_string
from django.conf import settings
from django.contrib.auth.models import User
from django.utils import timezone

logger = logging.getLogger(__name__)

class PWAManager:
    """Progressive Web App features manager"""
    
    def __init__(self):
        self.app_config = {
            'name': 'CV Analyzer',
            'short_name': 'CVAnalyzer',
            'description': 'Professional CV Analysis Tool',
            'version': '1.0.0',
            'theme_color': '#007bff',
            'background_color': '#ffffff'
        }
    
    def generate_manifest(self):
        """Generate PWA manifest.json"""
        manifest = {
            "name": self.app_config['name'],
            "short_name": self.app_config['short_name'],
            "description": self.app_config['description'],
            "start_url": "/",
            "display": "standalone",
            "orientation": "portrait-primary",
            "theme_color": self.app_config['theme_color'],
            "background_color": self.app_config['background_color'],
            "scope": "/",
            "lang": "en",
            "icons": [
                {
                    "src": "/static/images/icons/icon-72x72.png",
                    "sizes": "72x72",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/images/icons/icon-96x96.png",
                    "sizes": "96x96",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/images/icons/icon-128x128.png",
                    "sizes": "128x128",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/images/icons/icon-144x144.png",
                    "sizes": "144x144",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/images/icons/icon-152x152.png",
                    "sizes": "152x152",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/images/icons/icon-192x192.png",
                    "sizes": "192x192",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/images/icons/icon-384x384.png",
                    "sizes": "384x384",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/images/icons/icon-512x512.png",
                    "sizes": "512x512",
                    "type": "image/png",
                    "purpose": "maskable any"
                }
            ],
            "categories": ["productivity", "business", "utilities"],
            "prefer_related_applications": False
        }
        
        return manifest
    
    def generate_service_worker(self):
        """Generate service worker for offline functionality"""
        service_worker = """
const CACHE_NAME = 'cv-analyzer-v1.0.0';
const OFFLINE_URL = '/offline/';

// Files to cache for offline use
const FILES_TO_CACHE = [
    '/',
    '/static/css/main.css',
    '/static/css/pwa.css',
    '/static/js/main.js',
    '/static/js/pwa.js',
    '/static/images/logo.png',
    '/static/images/icons/icon-192x192.png',
    '/static/images/icons/icon-512x512.png',
    '/offline/',
    '/dashboard/',
    '/cvs/',
    '/api/v1/user/profile/'
];

// Install event - cache files
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Caching app shell files');
                return cache.addAll(FILES_TO_CACHE);
            })
            .then(() => {
                console.log('Service Worker installed successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Failed to cache files:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker activated');
            return self.clients.claim();
        })
    );
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', event => {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }
    
    // Skip requests to different origins
    if (!event.request.url.startsWith(self.location.origin)) {
        return;
    }
    
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                // Return cached version if available
                if (response) {
                    return response;
                }
                
                // Otherwise, fetch from network
                return fetch(event.request)
                    .then(response => {
                        // Don't cache if not a valid response
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }
                        
                        // Clone the response for caching
                        const responseToCache = response.clone();
                        
                        caches.open(CACHE_NAME)
                            .then(cache => {
                                cache.put(event.request, responseToCache);
                            });
                        
                        return response;
                    })
                    .catch(error => {
                        console.log('Fetch failed, serving offline page:', error);
                        
                        // Serve offline page for navigation requests
                        if (event.request.mode === 'navigate') {
                            return caches.match(OFFLINE_URL);
                        }
                        
                        // For other requests, throw the error
                        throw error;
                    });
            })
    );
});

// Background sync for offline actions
self.addEventListener('sync', event => {
    console.log('Background sync triggered:', event.tag);
    
    if (event.tag === 'cv-upload') {
        event.waitUntil(syncCVUploads());
    } else if (event.tag === 'user-actions') {
        event.waitUntil(syncUserActions());
    }
});

// Push notification handling
self.addEventListener('push', event => {
    console.log('Push notification received:', event);
    
    let notificationData = {
        title: 'CV Analyzer',
        body: 'You have a new notification',
        icon: '/static/images/icons/icon-192x192.png',
        badge: '/static/images/icons/icon-72x72.png',
        tag: 'general',
        requireInteraction: false
    };
    
    if (event.data) {
        try {
            const data = event.data.json();
            notificationData = { ...notificationData, ...data };
        } catch (error) {
            console.error('Error parsing push data:', error);
        }
    }
    
    event.waitUntil(
        self.registration.showNotification(notificationData.title, {
            body: notificationData.body,
            icon: notificationData.icon,
            badge: notificationData.badge,
            tag: notificationData.tag,
            requireInteraction: notificationData.requireInteraction,
            actions: [
                {
                    action: 'view',
                    title: 'View',
                    icon: '/static/images/icons/view.png'
                },
                {
                    action: 'dismiss',
                    title: 'Dismiss',
                    icon: '/static/images/icons/dismiss.png'
                }
            ],
            data: notificationData.data || {}
        })
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    console.log('Notification clicked:', event);
    
    event.notification.close();
    
    const urlToOpen = event.notification.data?.url || '/';
    
    if (event.action === 'view') {
        event.waitUntil(
            clients.matchAll({ type: 'window', includeUncontrolled: true })
                .then(clientList => {
                    // Check if the app is already open
                    for (const client of clientList) {
                        if (client.url.includes(self.location.origin) && 'focus' in client) {
                            client.focus();
                            client.navigate(urlToOpen);
                            return;
                        }
                    }
                    
                    // Open new window if app is not open
                    if (clients.openWindow) {
                        return clients.openWindow(urlToOpen);
                    }
                })
        );
    } else if (event.action === 'dismiss') {
        // Just close the notification
        return;
    } else {
        // Default action - open the app
        event.waitUntil(
            clients.openWindow(urlToOpen)
        );
    }
});

// Helper functions for background sync
async function syncCVUploads() {
    try {
        const pendingUploads = await getStoredData('pendingUploads') || [];
        
        for (const upload of pendingUploads) {
            try {
                const response = await fetch('/api/v1/cvs/upload/', {
                    method: 'POST',
                    body: upload.formData,
                    headers: upload.headers
                });
                
                if (response.ok) {
                    // Remove from pending uploads
                    await removeStoredData('pendingUploads', upload.id);
                    
                    // Show success notification
                    await self.registration.showNotification('Upload Successful', {
                        body: `${upload.filename} has been uploaded successfully`,
                        icon: '/static/images/icons/icon-192x192.png',
                        tag: 'upload-success'
                    });
                }
            } catch (error) {
                console.error('Failed to sync upload:', error);
            }
        }
    } catch (error) {
        console.error('Error in syncCVUploads:', error);
    }
}

async function syncUserActions() {
    try {
        const pendingActions = await getStoredData('pendingActions') || [];
        
        for (const action of pendingActions) {
            try {
                const response = await fetch(action.url, {
                    method: action.method,
                    headers: action.headers,
                    body: action.body
                });
                
                if (response.ok) {
                    await removeStoredData('pendingActions', action.id);
                }
            } catch (error) {
                console.error('Failed to sync action:', error);
            }
        }
    } catch (error) {
        console.error('Error in syncUserActions:', error);
    }
}

// IndexedDB helpers for offline storage
async function getStoredData(storeName) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('CVAnalyzerDB', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
            const db = request.result;
            const transaction = db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const getRequest = store.getAll();
            
            getRequest.onsuccess = () => resolve(getRequest.result);
            getRequest.onerror = () => reject(getRequest.error);
        };
        
        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains(storeName)) {
                db.createObjectStore(storeName, { keyPath: 'id' });
            }
        };
    });
}

async function removeStoredData(storeName, id) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('CVAnalyzerDB', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
            const db = request.result;
            const transaction = db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const deleteRequest = store.delete(id);
            
            deleteRequest.onsuccess = () => resolve();
            deleteRequest.onerror = () => reject(deleteRequest.error);
        };
    });
}
"""
        return service_worker
    
    def generate_pwa_javascript(self):
        """Generate PWA-specific JavaScript"""
        return """
// PWA JavaScript Module
class PWAManager {
    constructor() {
        this.isOnline = navigator.onLine;
        this.installPrompt = null;
        this.registration = null;
        this.init();
    }
    
    async init() {
        this.setupEventListeners();
        await this.registerServiceWorker();
        this.setupInstallPrompt();
        this.setupPushNotifications();
        this.initOfflineDetection();
    }
    
    setupEventListeners() {
        // Online/offline detection
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.handleOnlineStatus();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.handleOfflineStatus();
        });
        
        // Before install prompt
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.installPrompt = e;
            this.showInstallButton();
        });
        
        // App installed
        window.addEventListener('appinstalled', () => {
            this.hideInstallButton();
            this.showNotification('App installed successfully!', 'success');
        });
    }
    
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                this.registration = await navigator.serviceWorker.register('/sw.js');
                console.log('Service Worker registered successfully');
                
                // Listen for updates
                this.registration.addEventListener('updatefound', () => {
                    const newWorker = this.registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showUpdateNotification();
                        }
                    });
                });
                
            } catch (error) {
                console.error('Service Worker registration failed:', error);
            }
        }
    }
    
    setupInstallPrompt() {
        const installButton = document.getElementById('install-app-btn');
        if (installButton) {
            installButton.addEventListener('click', () => {
                this.promptInstall();
            });
        }
    }
    
    async promptInstall() {
        if (this.installPrompt) {
            this.installPrompt.prompt();
            const { outcome } = await this.installPrompt.userChoice;
            
            if (outcome === 'accepted') {
                console.log('User accepted the install prompt');
            } else {
                console.log('User dismissed the install prompt');
            }
            
            this.installPrompt = null;
        }
    }
    
    showInstallButton() {
        const installButton = document.getElementById('install-app-btn');
        if (installButton) {
            installButton.style.display = 'block';
        }
    }
    
    hideInstallButton() {
        const installButton = document.getElementById('install-app-btn');
        if (installButton) {
            installButton.style.display = 'none';
        }
    }
    
    async setupPushNotifications() {
        if ('PushManager' in window && 'serviceWorker' in navigator) {
            try {
                const permission = await this.requestNotificationPermission();
                if (permission === 'granted') {
                    await this.subscribeToPushNotifications();
                }
            } catch (error) {
                console.error('Push notification setup failed:', error);
            }
        }
    }
    
    async requestNotificationPermission() {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            console.log('Notification permission:', permission);
            return permission;
        }
        return 'denied';
    }
    
    async subscribeToPushNotifications() {
        try {
            if (!this.registration) {
                await this.registerServiceWorker();
            }
            
            const subscription = await this.registration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: this.urlBase64ToUint8Array(VAPID_PUBLIC_KEY)
            });
            
            // Send subscription to server
            await this.sendSubscriptionToServer(subscription);
            
        } catch (error) {
            console.error('Push subscription failed:', error);
        }
    }
    
    async sendSubscriptionToServer(subscription) {
        try {
            const response = await fetch('/api/v1/push/subscribe/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    subscription: subscription.toJSON()
                })
            });
            
            if (response.ok) {
                console.log('Push subscription sent to server');
            }
        } catch (error) {
            console.error('Failed to send subscription to server:', error);
        }
    }
    
    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/\\-/g, '+')
            .replace(/_/g, '/');
        
        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);
        
        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }
    
    initOfflineDetection() {
        this.updateOnlineStatus();
        
        // Intercept fetch requests to handle offline scenarios
        this.setupOfflineHandling();
    }
    
    updateOnlineStatus() {
        const statusIndicator = document.getElementById('online-status');
        if (statusIndicator) {
            statusIndicator.textContent = this.isOnline ? 'Online' : 'Offline';
            statusIndicator.className = this.isOnline ? 'status-online' : 'status-offline';
        }
    }
    
    handleOnlineStatus() {
        this.updateOnlineStatus();
        this.showNotification('You are back online!', 'success');
        
        // Sync pending data
        this.syncPendingData();
    }
    
    handleOfflineStatus() {
        this.updateOnlineStatus();
        this.showNotification('You are offline. Some features may be limited.', 'warning');
    }
    
    setupOfflineHandling() {
        // Store failed requests for retry when online
        window.addEventListener('fetch', (event) => {
            if (!this.isOnline) {
                this.handleOfflineRequest(event);
            }
        });
    }
    
    async handleOfflineRequest(event) {
        // Store CV uploads for background sync
        if (event.request.url.includes('/api/v1/cvs/upload/')) {
            const formData = await event.request.formData();
            const uploadData = {
                id: Date.now(),
                url: event.request.url,
                method: event.request.method,
                formData: formData,
                timestamp: Date.now()
            };
            
            await this.storeOfflineData('pendingUploads', uploadData);
            
            // Register background sync
            if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
                await this.registration.sync.register('cv-upload');
            }
        }
    }
    
    async storeOfflineData(storeName, data) {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open('CVAnalyzerDB', 1);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                const db = request.result;
                const transaction = db.transaction([storeName], 'readwrite');
                const store = transaction.objectStore(storeName);
                const addRequest = store.add(data);
                
                addRequest.onsuccess = () => resolve();
                addRequest.onerror = () => reject(addRequest.error);
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains(storeName)) {
                    db.createObjectStore(storeName, { keyPath: 'id' });
                }
            };
        });
    }
    
    async syncPendingData() {
        // Trigger background sync
        if (this.registration && 'sync' in window.ServiceWorkerRegistration.prototype) {
            try {
                await this.registration.sync.register('cv-upload');
                await this.registration.sync.register('user-actions');
            } catch (error) {
                console.error('Background sync registration failed:', error);
            }
        }
    }
    
    showUpdateNotification() {
        const updateBanner = document.createElement('div');
        updateBanner.className = 'update-banner';
        updateBanner.innerHTML = `
            <div class="update-content">
                <span>A new version is available!</span>
                <button onclick="location.reload()" class="btn btn-sm btn-primary">Update Now</button>
                <button onclick="this.parentElement.parentElement.remove()" class="btn btn-sm btn-secondary">Later</button>
            </div>
        `;
        
        document.body.appendChild(updateBanner);
        
        setTimeout(() => {
            updateBanner.classList.add('visible');
        }, 100);
    }
    
    showNotification(message, type = 'info') {
        // Use existing notification system or create simple one
        if (window.uxEnhancer && window.uxEnhancer.showNotification) {
            window.uxEnhancer.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
}

// Initialize PWA Manager
const VAPID_PUBLIC_KEY = 'YOUR_VAPID_PUBLIC_KEY_HERE'; // Replace with actual key

document.addEventListener('DOMContentLoaded', () => {
    window.pwaManager = new PWAManager();
});
"""

class OfflineStorageManager:
    """Manages offline data storage and synchronization"""
    
    def __init__(self):
        self.storage_version = 1
        self.stores = {
            'cvs': 'id',
            'analyses': 'id',
            'user_data': 'id',
            'pending_uploads': 'id',
            'pending_actions': 'id'
        }
    
    def generate_offline_storage_script(self):
        """Generate offline storage management JavaScript"""
        return """
// Offline Storage Manager
class OfflineStorageManager {
    constructor() {
        this.dbName = 'CVAnalyzerDB';
        this.dbVersion = 1;
        this.db = null;
        this.init();
    }
    
    async init() {
        try {
            this.db = await this.openDatabase();
            console.log('Offline storage initialized');
        } catch (error) {
            console.error('Failed to initialize offline storage:', error);
        }
    }
    
    openDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve(request.result);
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create object stores
                if (!db.objectStoreNames.contains('cvs')) {
                    db.createObjectStore('cvs', { keyPath: 'id' });
                }
                
                if (!db.objectStoreNames.contains('analyses')) {
                    db.createObjectStore('analyses', { keyPath: 'id' });
                }
                
                if (!db.objectStoreNames.contains('user_data')) {
                    db.createObjectStore('user_data', { keyPath: 'id' });
                }
                
                if (!db.objectStoreNames.contains('pending_uploads')) {
                    db.createObjectStore('pending_uploads', { keyPath: 'id' });
                }
                
                if (!db.objectStoreNames.contains('pending_actions')) {
                    db.createObjectStore('pending_actions', { keyPath: 'id' });
                }
            };
        });
    }
    
    async storeData(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.put(data);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }
    
    async getData(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(id);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }
    
    async getAllData(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }
    
    async deleteData(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(id);
            
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }
    
    async clearStore(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.clear();
            
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }
    
    // Cache CV data for offline access
    async cacheCVData(cvData) {
        try {
            await this.storeData('cvs', {
                id: cvData.id,
                filename: cvData.filename,
                content: cvData.content,
                metadata: cvData.metadata,
                cached_at: Date.now()
            });
            
            console.log('CV data cached for offline access');
        } catch (error) {
            console.error('Failed to cache CV data:', error);
        }
    }
    
    // Get cached CV data
    async getCachedCVs() {
        try {
            return await this.getAllData('cvs');
        } catch (error) {
            console.error('Failed to get cached CVs:', error);
            return [];
        }
    }
    
    // Store pending upload for sync
    async storePendingUpload(uploadData) {
        try {
            const pendingUpload = {
                id: Date.now(),
                filename: uploadData.filename,
                file_data: uploadData.file_data,
                metadata: uploadData.metadata,
                created_at: Date.now()
            };
            
            await this.storeData('pending_uploads', pendingUpload);
            console.log('Upload stored for background sync');
            
            return pendingUpload.id;
        } catch (error) {
            console.error('Failed to store pending upload:', error);
            throw error;
        }
    }
    
    // Get pending uploads for sync
    async getPendingUploads() {
        try {
            return await this.getAllData('pending_uploads');
        } catch (error) {
            console.error('Failed to get pending uploads:', error);
            return [];
        }
    }
    
    // Remove synced upload
    async removePendingUpload(id) {
        try {
            await this.deleteData('pending_uploads', id);
            console.log('Pending upload removed after sync');
        } catch (error) {
            console.error('Failed to remove pending upload:', error);
        }
    }
}

// Initialize offline storage manager
window.offlineStorage = new OfflineStorageManager();
"""

class NotificationManager:
    """Manages push notifications"""
    
    def __init__(self):
        self.notification_types = {
            'analysis_complete': {
                'title': 'CV Analysis Complete',
                'body': 'Your CV analysis results are ready to view',
                'icon': '/static/images/icons/analysis-complete.png',
                'url': '/analyses/'
            },
            'upload_success': {
                'title': 'Upload Successful',
                'body': 'Your CV has been uploaded successfully',
                'icon': '/static/images/icons/upload-success.png',
                'url': '/cvs/'
            },
            'system_update': {
                'title': 'System Update',
                'body': 'CV Analyzer has been updated with new features',
                'icon': '/static/images/icons/system-update.png',
                'url': '/'
            }
        }
    
    def create_notification_payload(self, notification_type: str, custom_data: Dict = None):
        """Create notification payload for push service"""
        base_notification = self.notification_types.get(notification_type, {})
        
        payload = {
            'title': base_notification.get('title', 'CV Analyzer'),
            'body': base_notification.get('body', 'You have a new notification'),
            'icon': base_notification.get('icon', '/static/images/icons/icon-192x192.png'),
            'badge': '/static/images/icons/icon-72x72.png',
            'tag': notification_type,
            'requireInteraction': False,
            'data': {
                'url': base_notification.get('url', '/'),
                'timestamp': timezone.now().isoformat(),
                **(custom_data or {})
            }
        }
        
        return payload

# Global instances
pwa_manager = PWAManager()
offline_storage = OfflineStorageManager()
notification_manager = NotificationManager()

def get_pwa_manifest():
    """Get PWA manifest.json"""
    return pwa_manager.generate_manifest()

def get_service_worker():
    """Get service worker code"""
    return pwa_manager.generate_service_worker()

def get_pwa_scripts():
    """Get PWA JavaScript code"""
    return pwa_manager.generate_pwa_javascript()

def create_push_notification(notification_type: str, custom_data: Dict = None):
    """Create push notification payload"""
    return notification_manager.create_notification_payload(notification_type, custom_data) 