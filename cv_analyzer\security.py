"""
Security utilities for CV Analyzer application.
Implements comprehensive file upload security, validation, and protection measures.
"""

import os
import magic
import hashlib
import tempfile
import subprocess
import logging
import hashlib
import time
from typing import Dict, List, Optional, Tuple, Union
from django.core.exceptions import ValidationError
from django.conf import settings
from django.core.files.uploadedfile import UploadedFile
from django.utils.translation import gettext_lazy as _
from pathlib import Path
import mimetypes

# Optional imports for enhanced security features
try:
    import clamd
    CLAMD_AVAILABLE = True
except ImportError:
    CLAMD_AVAILABLE = False

try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False

logger = logging.getLogger(__name__)

class FileSecurityError(Exception):
    """Custom exception for file security issues."""
    pass

class FileUploadValidator:
    """Comprehensive file upload security validator."""
    
    # Allowed file types with their magic numbers
    ALLOWED_TYPES = {
        'application/pdf': [
            b'%PDF-',  # PDF signature
        ],
        'application/msword': [
            b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1',  # DOC signature
        ],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
            b'PK\x03\x04',  # DOCX signature (ZIP-based)
        ],
    }
    
    # Maximum file sizes in bytes
    MAX_FILE_SIZES = {
        'application/pdf': 10 * 1024 * 1024,  # 10MB
        'application/msword': 5 * 1024 * 1024,  # 5MB
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 5 * 1024 * 1024,  # 5MB
    }
    
    # Dangerous file patterns to block
    DANGEROUS_PATTERNS = [
        b'<script',
        b'javascript:',
        b'vbscript:',
        b'onload=',
        b'onerror=',
        b'eval(',
        b'document.cookie',
        b'document.write',
        b'ActiveXObject',
        b'WScript.Shell',
        b'Shell.Application',
    ]
    
    def __init__(self):
        """Initialize the file validator."""
        self.virus_scanner = VirusScanner()
        
    def validate_file(self, uploaded_file: UploadedFile) -> Dict[str, Union[bool, str, List[str]]]:
        """
        Comprehensive file validation.
        
        Args:
            uploaded_file: Django UploadedFile object
            
        Returns:
            Dict containing validation results
        """
        results = {
            'is_valid': False,
            'errors': [],
            'warnings': [],
            'file_info': {},
        }
        
        try:
            # Basic file validation
            basic_validation = self._validate_basic_properties(uploaded_file)
            if not basic_validation['is_valid']:
                results['errors'].extend(basic_validation['errors'])
                return results
            
            # File type validation using magic numbers
            type_validation = self._validate_file_type(uploaded_file)
            if not type_validation['is_valid']:
                results['errors'].extend(type_validation['errors'])
                return results
            
            # Content security validation
            content_validation = self._validate_file_content(uploaded_file)
            if not content_validation['is_valid']:
                results['errors'].extend(content_validation['errors'])
                return results
            
            # Virus scanning
            virus_scan = self._scan_for_viruses(uploaded_file)
            if not virus_scan['is_clean']:
                results['errors'].append(f"Security threat detected: {virus_scan['threat']}")
                return results
            
            # File integrity check
            integrity_check = self._check_file_integrity(uploaded_file)
            results['file_info'] = integrity_check
            
            results['is_valid'] = True
            logger.info(f"File validation passed for: {uploaded_file.name}")
            
        except Exception as e:
            logger.error(f"Error during file validation: {str(e)}")
            results['errors'].append("File validation failed due to internal error")
            
        return results
    
    def _validate_basic_properties(self, uploaded_file: UploadedFile) -> Dict[str, Union[bool, List[str]]]:
        """Validate basic file properties."""
        errors = []
        
        # Check if file exists and has content
        if not uploaded_file:
            errors.append("No file provided")
            return {'is_valid': False, 'errors': errors}
        
        # Check file size
        if uploaded_file.size == 0:
            errors.append("File is empty")
        elif uploaded_file.size > max(self.MAX_FILE_SIZES.values()):
            errors.append(f"File too large. Maximum size: {max(self.MAX_FILE_SIZES.values()) // (1024*1024)}MB")
        
        # Check filename
        if not uploaded_file.name:
            errors.append("Invalid filename")
        elif len(uploaded_file.name) > 255:
            errors.append("Filename too long")
        elif any(char in uploaded_file.name for char in ['<', '>', ':', '"', '|', '?', '*']):
            errors.append("Filename contains invalid characters")
        
        return {'is_valid': len(errors) == 0, 'errors': errors}
    
    def _validate_file_type(self, uploaded_file: UploadedFile) -> Dict[str, Union[bool, List[str]]]:
        """Validate file type using magic numbers."""
        errors = []
        
        try:
            # Read first 1024 bytes for magic number detection
            uploaded_file.seek(0)
            file_header = uploaded_file.read(1024)
            uploaded_file.seek(0)
            
            # Detect MIME type using python-magic if available
            mime_type = None
            if MAGIC_AVAILABLE:
                mime_type = magic.from_buffer(file_header, mime=True)
            else:
                # Fallback to basic file extension detection
                import os
                _, ext = os.path.splitext(uploaded_file.name.lower())
                mime_type_map = {
                    '.pdf': 'application/pdf',
                    '.doc': 'application/msword',
                    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                }
                mime_type = mime_type_map.get(ext, 'application/octet-stream')
            
            # Validate against allowed types
            if mime_type not in self.ALLOWED_TYPES:
                errors.append(f"File type not allowed: {mime_type}")
                return {'is_valid': False, 'errors': errors}
            
            # Validate magic numbers
            valid_signature = False
            for signature in self.ALLOWED_TYPES[mime_type]:
                if file_header.startswith(signature):
                    valid_signature = True
                    break
            
            if not valid_signature:
                errors.append("File signature does not match declared type")
            
            # Check file size against type-specific limits
            if uploaded_file.size > self.MAX_FILE_SIZES.get(mime_type, 0):
                max_size_mb = self.MAX_FILE_SIZES[mime_type] // (1024 * 1024)
                errors.append(f"File too large for type {mime_type}. Maximum: {max_size_mb}MB")
                
        except Exception as e:
            logger.error(f"Error validating file type: {str(e)}")
            errors.append("Could not validate file type")
        
        return {'is_valid': len(errors) == 0, 'errors': errors}
    
    def _validate_file_content(self, uploaded_file: UploadedFile) -> Dict[str, Union[bool, List[str]]]:
        """Validate file content for malicious patterns."""
        errors = []
        
        try:
            uploaded_file.seek(0)
            content = uploaded_file.read()
            uploaded_file.seek(0)
            
            # Check for dangerous patterns
            for pattern in self.DANGEROUS_PATTERNS:
                if pattern in content.lower():
                    errors.append(f"Potentially malicious content detected")
                    logger.warning(f"Dangerous pattern found in file: {uploaded_file.name}")
                    break
            
            # Additional PDF-specific checks
            if content.startswith(b'%PDF-'):
                if b'/JavaScript' in content or b'/JS' in content:
                    errors.append("PDF contains JavaScript code")
                if b'/EmbeddedFile' in content:
                    errors.append("PDF contains embedded files")
                if b'/Launch' in content:
                    errors.append("PDF contains launch actions")
                    
        except Exception as e:
            logger.error(f"Error validating file content: {str(e)}")
            errors.append("Could not validate file content")
        
        return {'is_valid': len(errors) == 0, 'errors': errors}
    
    def _scan_for_viruses(self, uploaded_file: UploadedFile) -> Dict[str, Union[bool, str]]:
        """Scan file for viruses using ClamAV."""
        if not CLAMD_AVAILABLE:
            # Skip virus scanning if clamd is not available
            return {'is_clean': True, 'threat': None}
        try:
            return self.virus_scanner.scan_file(uploaded_file)
        except Exception as e:
            logger.error(f"Virus scanning failed: {str(e)}")
            return {'is_clean': False, 'threat': 'Virus scan failed'}
    
    def _check_file_integrity(self, uploaded_file: UploadedFile) -> Dict[str, str]:
        """Generate file integrity information."""
        uploaded_file.seek(0)
        content = uploaded_file.read()
        uploaded_file.seek(0)
        
        # Determine mime type
        mime_type = 'application/octet-stream'
        if MAGIC_AVAILABLE:
            mime_type = magic.from_buffer(content[:1024], mime=True)
        else:
            # Fallback to basic file extension detection
            import os
            _, ext = os.path.splitext(uploaded_file.name.lower())
            mime_type_map = {
                '.pdf': 'application/pdf',
                '.doc': 'application/msword',
                '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            }
            mime_type = mime_type_map.get(ext, 'application/octet-stream')
        
        return {
            'md5': hashlib.md5(content).hexdigest(),
            'sha256': hashlib.sha256(content).hexdigest(),
            'size': str(len(content)),
            'mime_type': mime_type,
        }

class VirusScanner:
    """ClamAV virus scanner integration."""
    
    def __init__(self):
        """Initialize virus scanner."""
        self.clamd_socket = getattr(settings, 'CLAMD_SOCKET', '/var/run/clamav/clamd.ctl')
        self.clamd_host = getattr(settings, 'CLAMD_HOST', 'localhost')
        self.clamd_port = getattr(settings, 'CLAMD_PORT', 3310)
        
    def scan_file(self, uploaded_file: UploadedFile) -> Dict[str, Union[bool, str]]:
        """
        Scan file for viruses.
        
        Args:
            uploaded_file: Django UploadedFile object
            
        Returns:
            Dict with scan results
        """
        try:
            # Try socket connection first
            try:
                cd = clamd.ClamdUnixSocket(self.clamd_socket)
                cd.ping()
            except:
                # Fallback to network connection
                cd = clamd.ClamdNetworkSocket(self.clamd_host, self.clamd_port)
                cd.ping()
            
            # Scan file content
            uploaded_file.seek(0)
            scan_result = cd.instream(uploaded_file)
            uploaded_file.seek(0)
            
            # Parse results
            if scan_result['stream'][0] == 'OK':
                return {'is_clean': True, 'threat': None}
            else:
                threat = scan_result['stream'][1] if len(scan_result['stream']) > 1 else 'Unknown threat'
                logger.warning(f"Virus detected in file {uploaded_file.name}: {threat}")
                return {'is_clean': False, 'threat': threat}
                
        except clamd.ConnectionError:
            logger.error("ClamAV daemon not available")
            # In production, you might want to fail closed (reject file)
            # For development, we'll allow the file but log the issue
            if settings.DEBUG:
                return {'is_clean': True, 'threat': None}
            else:
                return {'is_clean': False, 'threat': 'Virus scanner unavailable'}
        except Exception as e:
            logger.error(f"Error during virus scan: {str(e)}")
            return {'is_clean': False, 'threat': 'Scan failed'}

class FileQuarantine:
    """File quarantine system for suspicious files."""
    
    def __init__(self):
        """Initialize quarantine system."""
        self.quarantine_dir = Path(settings.MEDIA_ROOT) / 'quarantine'
        self.quarantine_dir.mkdir(exist_ok=True, mode=0o700)
        
    def quarantine_file(self, uploaded_file: UploadedFile, reason: str) -> str:
        """
        Move file to quarantine.
        
        Args:
            uploaded_file: The file to quarantine
            reason: Reason for quarantine
            
        Returns:
            Quarantine file path
        """
        try:
            # Generate unique quarantine filename
            timestamp = str(int(time.time()))
            safe_filename = self._sanitize_filename(uploaded_file.name)
            quarantine_filename = f"{timestamp}_{safe_filename}"
            quarantine_path = self.quarantine_dir / quarantine_filename
            
            # Save file to quarantine
            with open(quarantine_path, 'wb') as qf:
                uploaded_file.seek(0)
                for chunk in uploaded_file.chunks():
                    qf.write(chunk)
            
            # Log quarantine action
            logger.warning(f"File quarantined: {uploaded_file.name} -> {quarantine_path} (Reason: {reason})")
            
            # Create metadata file
            metadata = {
                'original_name': uploaded_file.name,
                'quarantine_time': timestamp,
                'reason': reason,
                'size': uploaded_file.size,
                'content_type': uploaded_file.content_type,
            }
            
            metadata_path = quarantine_path.with_suffix('.metadata')
            with open(metadata_path, 'w') as mf:
                import json
                json.dump(metadata, mf, indent=2)
            
            return str(quarantine_path)
            
        except Exception as e:
            logger.error(f"Error quarantining file: {str(e)}")
            raise FileSecurityError(f"Could not quarantine file: {str(e)}")
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe storage."""
        import re
        # Remove or replace dangerous characters
        safe_filename = re.sub(r'[^\w\-_\.]', '_', filename)
        return safe_filename[:100]  # Limit length

# Input validation and sanitization utilities
class InputValidator:
    """Comprehensive input validation and sanitization."""
    
    @staticmethod
    def sanitize_string(value: str, max_length: int = 1000) -> str:
        """Sanitize string input."""
        if not isinstance(value, str):
            raise ValidationError("Value must be a string")
        
        # Remove null bytes and control characters
        sanitized = value.replace('\x00', '').strip()
        
        # Limit length
        if len(sanitized) > max_length:
            raise ValidationError(f"Value too long. Maximum length: {max_length}")
        
        return sanitized
    
    @staticmethod
    def validate_email(email: str) -> str:
        """Validate and sanitize email address."""
        from django.core.validators import validate_email as django_validate_email
        
        try:
            sanitized_email = InputValidator.sanitize_string(email, 254)
            django_validate_email(sanitized_email)
            return sanitized_email.lower()
        except ValidationError:
            raise ValidationError("Invalid email address")
    
    @staticmethod
    def validate_phone(phone: str) -> str:
        """Validate and sanitize phone number."""
        import re
        
        # Remove all non-digit characters except + and -
        sanitized = re.sub(r'[^\d\+\-\s\(\)]', '', phone.strip())
        
        # Basic phone validation (can be enhanced based on requirements)
        if len(sanitized) < 7 or len(sanitized) > 20:
            raise ValidationError("Invalid phone number length")
        
        return sanitized
    
    @staticmethod
    def validate_url(url: str) -> str:
        """Validate and sanitize URL."""
        from django.core.validators import URLValidator
        
        try:
            sanitized_url = InputValidator.sanitize_string(url, 2000)
            validator = URLValidator()
            validator(sanitized_url)
            return sanitized_url
        except ValidationError:
            raise ValidationError("Invalid URL")

# Security middleware utilities
class SecurityHeaders:
    """Security headers for HTTP responses."""
    
    @staticmethod
    def get_security_headers() -> Dict[str, str]:
        """Get recommended security headers."""
        return {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
            'Content-Security-Policy': (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; "
                "style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; "
                "img-src 'self' data: https:; "
                "font-src 'self' https://cdnjs.cloudflare.com; "
                "connect-src 'self'; "
                "frame-ancestors 'none';"
            ),
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
        }

import time 