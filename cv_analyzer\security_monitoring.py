"""
Security Monitoring & Compliance System
Threat detection, vulnerability scanning, audit logging, and security compliance
"""

import logging
import hashlib
import json
import time
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from django.conf import settings
from django.contrib.auth.models import User
from django.core.cache import cache
from django.utils import timezone
from django.db import models
from django.http import HttpRequest
import ipaddress
from collections import defaultdict

logger = logging.getLogger(__name__)

class SecurityThreatDetector:
    """Real-time security threat detection and monitoring"""
    
    def __init__(self):
        self.threat_patterns = {
            'sql_injection': [
                r"(\%27)|(\')|(\-\-)|(\%23)|(#)",
                r"((\%3D)|(=))[^\n]*((\%27)|(\')|(\-\-)|(\%23)|(#))",
                r"w*((\%27)|(\'))((\%6F)|o|(\%4F))((\%72)|r|(\%52))",
                r"((\%27)|(\'))union",
            ],
            'xss': [
                r"<[^>]*script[^>]*>",
                r"javascript:",
                r"on\w+\s*=",
                r"<[^>]*iframe[^>]*>",
            ],
            'command_injection': [
                r"[;&|`]",
                r"\$\([^)]*\)",
                r"`[^`]*`",
                r"\|\s*\w+",
            ],
            'path_traversal': [
                r"\.\./",
                r"\.\.\\",
                r"%2e%2e%2f",
                r"%2e%2e\\",
            ],
            'suspicious_headers': [
                r"User-Agent:.*sqlmap",
                r"User-Agent:.*nikto",
                r"User-Agent:.*nessus",
                r"X-Forwarded-For:.*proxy",
            ]
        }
        
        self.rate_limits = {
            'login_attempts': {'limit': 5, 'window': 300},  # 5 attempts per 5 minutes
            'api_requests': {'limit': 100, 'window': 60},   # 100 requests per minute
            'file_uploads': {'limit': 10, 'window': 600},   # 10 uploads per 10 minutes
        }
        
        self.blocked_ips = set()
        self.suspicious_activities = defaultdict(list)
        
    def analyze_request(self, request: HttpRequest) -> Dict[str, Any]:
        """Analyze incoming request for security threats"""
        analysis_result = {
            'timestamp': timezone.now().isoformat(),
            'ip_address': self.get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'path': request.path,
            'method': request.method,
            'threats_detected': [],
            'risk_score': 0,
            'action_taken': 'none'
        }
        
        # Check for blocked IPs
        if self.is_ip_blocked(analysis_result['ip_address']):
            analysis_result['threats_detected'].append('blocked_ip')
            analysis_result['risk_score'] += 100
            analysis_result['action_taken'] = 'blocked'
            return analysis_result
        
        # Check rate limiting
        rate_limit_result = self.check_rate_limits(request, analysis_result['ip_address'])
        if rate_limit_result['exceeded']:
            analysis_result['threats_detected'].append('rate_limit_exceeded')
            analysis_result['risk_score'] += 50
            analysis_result['action_taken'] = 'rate_limited'
        
        # Analyze request content for attack patterns
        content_threats = self.detect_attack_patterns(request)
        analysis_result['threats_detected'].extend(content_threats)
        analysis_result['risk_score'] += len(content_threats) * 25
        
        # Check for suspicious user behavior
        if request.user.is_authenticated:
            behavior_threats = self.analyze_user_behavior(request.user, request)
            analysis_result['threats_detected'].extend(behavior_threats)
            analysis_result['risk_score'] += len(behavior_threats) * 15
        
        # Determine final action
        if analysis_result['risk_score'] >= 75:
            analysis_result['action_taken'] = 'blocked'
            self.block_ip(analysis_result['ip_address'], 'high_risk_score')
        elif analysis_result['risk_score'] >= 50:
            analysis_result['action_taken'] = 'flagged'
            self.flag_suspicious_activity(analysis_result)
        
        # Log security event
        self.log_security_event(analysis_result)
        
        return analysis_result
    
    def get_client_ip(self, request: HttpRequest) -> str:
        """Get real client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '')
        
        return ip
    
    def is_ip_blocked(self, ip_address: str) -> bool:
        """Check if IP address is blocked"""
        return ip_address in self.blocked_ips
    
    def block_ip(self, ip_address: str, reason: str, duration: int = 3600):
        """Block IP address for specified duration"""
        self.blocked_ips.add(ip_address)
        
        # Store in cache with expiration
        cache_key = f"blocked_ip_{ip_address}"
        cache.set(cache_key, {
            'reason': reason,
            'blocked_at': timezone.now().isoformat(),
            'duration': duration
        }, timeout=duration)
        
        logger.warning(f"IP {ip_address} blocked for {reason}")
    
    def check_rate_limits(self, request: HttpRequest, ip_address: str) -> Dict[str, Any]:
        """Check various rate limits"""
        result = {'exceeded': False, 'limits_hit': []}
        
        # API rate limiting
        if request.path.startswith('/api/'):
            api_limit = self.rate_limits['api_requests']
            if self.is_rate_limit_exceeded(ip_address, 'api', api_limit):
                result['exceeded'] = True
                result['limits_hit'].append('api_requests')
        
        # Login attempt rate limiting
        if request.path == '/login/' and request.method == 'POST':
            login_limit = self.rate_limits['login_attempts']
            if self.is_rate_limit_exceeded(ip_address, 'login', login_limit):
                result['exceeded'] = True
                result['limits_hit'].append('login_attempts')
        
        # File upload rate limiting
        if request.FILES:
            upload_limit = self.rate_limits['file_uploads']
            if self.is_rate_limit_exceeded(ip_address, 'upload', upload_limit):
                result['exceeded'] = True
                result['limits_hit'].append('file_uploads')
        
        return result
    
    def is_rate_limit_exceeded(self, ip_address: str, action_type: str, limit_config: Dict[str, int]) -> bool:
        """Check if rate limit is exceeded for specific action"""
        cache_key = f"rate_limit_{action_type}_{ip_address}"
        current_count = cache.get(cache_key, 0)
        
        if current_count >= limit_config['limit']:
            return True
        
        # Increment counter
        cache.set(cache_key, current_count + 1, timeout=limit_config['window'])
        return False
    
    def detect_attack_patterns(self, request: HttpRequest) -> List[str]:
        """Detect common attack patterns in request"""
        threats = []
        
        # Get request data to analyze
        data_to_check = []
        
        # Check query parameters
        if request.GET:
            data_to_check.extend(request.GET.values())
        
        # Check POST data
        if request.POST:
            data_to_check.extend(request.POST.values())
        
        # Check headers
        headers_to_check = [
            request.META.get('HTTP_USER_AGENT', ''),
            request.META.get('HTTP_REFERER', ''),
            request.META.get('HTTP_X_FORWARDED_FOR', ''),
        ]
        data_to_check.extend(headers_to_check)
        
        # Check URL path
        data_to_check.append(request.path)
        
        # Analyze each piece of data against threat patterns
        for data in data_to_check:
            if data:
                for threat_type, patterns in self.threat_patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, str(data), re.IGNORECASE):
                            if threat_type not in threats:
                                threats.append(threat_type)
                            break
        
        return threats
    
    def analyze_user_behavior(self, user: User, request: HttpRequest) -> List[str]:
        """Analyze user behavior for suspicious patterns"""
        threats = []
        
        # Check for unusual access patterns
        user_session_key = f"user_behavior_{user.id}"
        user_activity = cache.get(user_session_key, {
            'requests': [],
            'last_seen': None,
            'locations': set()
        })
        
        current_time = timezone.now()
        current_ip = self.get_client_ip(request)
        
        # Add current request to activity log
        user_activity['requests'].append({
            'timestamp': current_time.isoformat(),
            'ip': current_ip,
            'path': request.path,
            'user_agent': request.META.get('HTTP_USER_AGENT', '')
        })
        
        # Keep only last 100 requests
        user_activity['requests'] = user_activity['requests'][-100:]
        
        # Check for rapid successive requests (possible bot behavior)
        recent_requests = [
            req for req in user_activity['requests']
            if timezone.now() - datetime.fromisoformat(req['timestamp'].replace('Z', '+00:00')) < timedelta(minutes=1)
        ]
        
        if len(recent_requests) > 20:
            threats.append('rapid_requests')
        
        # Check for multiple IP addresses (possible account sharing/compromise)
        recent_ips = set(req['ip'] for req in user_activity['requests'][-10:])
        if len(recent_ips) > 3:
            threats.append('multiple_ips')
        
        # Check for access from unusual locations (would need GeoIP in production)
        user_activity['locations'].add(current_ip)
        if len(user_activity['locations']) > 5:
            threats.append('unusual_locations')
        
        # Update cache
        cache.set(user_session_key, user_activity, timeout=3600)
        
        return threats
    
    def flag_suspicious_activity(self, analysis_result: Dict[str, Any]):
        """Flag suspicious activity for manual review"""
        flag_key = f"suspicious_activity_{analysis_result['ip_address']}_{int(time.time())}"
        cache.set(flag_key, analysis_result, timeout=86400)  # 24 hours
        
        logger.warning(f"Suspicious activity flagged: {analysis_result}")
    
    def log_security_event(self, analysis_result: Dict[str, Any]):
        """Log security event for audit and analysis"""
        # In production, this would go to a SIEM system
        if analysis_result['threats_detected'] or analysis_result['risk_score'] > 0:
            logger.info(f"Security event: {json.dumps(analysis_result)}")

class VulnerabilityScanner:
    """Automated vulnerability scanning and assessment"""
    
    def __init__(self):
        self.scan_types = {
            'dependency_check': self.scan_dependencies,
            'configuration_check': self.scan_configuration,
            'ssl_check': self.scan_ssl_configuration,
            'header_check': self.scan_security_headers,
            'file_permissions': self.scan_file_permissions
        }
        
    def run_vulnerability_scan(self) -> Dict[str, Any]:
        """Run comprehensive vulnerability scan"""
        scan_results = {
            'timestamp': timezone.now().isoformat(),
            'scan_id': hashlib.md5(str(time.time()).encode()).hexdigest()[:8],
            'vulnerabilities': [],
            'risk_summary': {
                'critical': 0,
                'high': 0,
                'medium': 0,
                'low': 0
            },
            'recommendations': []
        }
        
        for scan_type, scan_function in self.scan_types.items():
            try:
                vulnerabilities = scan_function()
                for vuln in vulnerabilities:
                    vuln['scan_type'] = scan_type
                    scan_results['vulnerabilities'].append(vuln)
                    
                    # Update risk summary
                    risk_level = vuln.get('risk_level', 'low')
                    scan_results['risk_summary'][risk_level] += 1
                    
            except Exception as e:
                logger.error(f"Vulnerability scan {scan_type} failed: {e}")
        
        # Generate recommendations
        scan_results['recommendations'] = self.generate_recommendations(scan_results)
        
        return scan_results
    
    def scan_dependencies(self) -> List[Dict[str, Any]]:
        """Scan for vulnerable dependencies"""
        vulnerabilities = []
        
        # Check Django version
        import django
        django_version = django.get_version()
        
        # This would integrate with vulnerability databases in production
        known_vulnerable_versions = ['3.2.0', '4.0.0']  # Example
        
        if django_version in known_vulnerable_versions:
            vulnerabilities.append({
                'title': 'Vulnerable Django Version',
                'description': f'Django version {django_version} has known vulnerabilities',
                'risk_level': 'high',
                'affected_component': 'django',
                'recommendation': 'Update Django to the latest stable version'
            })
        
        return vulnerabilities
    
    def scan_configuration(self) -> List[Dict[str, Any]]:
        """Scan Django configuration for security issues"""
        vulnerabilities = []
        
        # Check DEBUG setting
        if getattr(settings, 'DEBUG', False):
            vulnerabilities.append({
                'title': 'Debug Mode Enabled',
                'description': 'DEBUG=True in production exposes sensitive information',
                'risk_level': 'high',
                'affected_component': 'django_settings',
                'recommendation': 'Set DEBUG=False in production'
            })
        
        # Check SECRET_KEY
        secret_key = getattr(settings, 'SECRET_KEY', '')
        if len(secret_key) < 50:
            vulnerabilities.append({
                'title': 'Weak Secret Key',
                'description': 'SECRET_KEY is too short or using default value',
                'risk_level': 'critical',
                'affected_component': 'django_settings',
                'recommendation': 'Generate a strong, unique SECRET_KEY'
            })
        
        # Check ALLOWED_HOSTS
        allowed_hosts = getattr(settings, 'ALLOWED_HOSTS', [])
        if '*' in allowed_hosts:
            vulnerabilities.append({
                'title': 'Wildcard in ALLOWED_HOSTS',
                'description': 'Using * in ALLOWED_HOSTS allows any host',
                'risk_level': 'medium',
                'affected_component': 'django_settings',
                'recommendation': 'Specify exact hostnames in ALLOWED_HOSTS'
            })
        
        # Check security middleware
        middleware = getattr(settings, 'MIDDLEWARE', [])
        security_middleware = [
            'django.middleware.security.SecurityMiddleware',
            'django.contrib.sessions.middleware.SessionMiddleware',
            'django.middleware.csrf.CsrfViewMiddleware',
        ]
        
        for mw in security_middleware:
            if mw not in middleware:
                vulnerabilities.append({
                    'title': f'Missing Security Middleware: {mw}',
                    'description': f'Security middleware {mw} is not configured',
                    'risk_level': 'medium',
                    'affected_component': 'django_middleware',
                    'recommendation': f'Add {mw} to MIDDLEWARE setting'
                })
        
        return vulnerabilities
    
    def scan_ssl_configuration(self) -> List[Dict[str, Any]]:
        """Scan SSL/TLS configuration"""
        vulnerabilities = []
        
        # Check SECURE_SSL_REDIRECT
        if not getattr(settings, 'SECURE_SSL_REDIRECT', False):
            vulnerabilities.append({
                'title': 'HTTP Connections Allowed',
                'description': 'SECURE_SSL_REDIRECT is not enabled',
                'risk_level': 'medium',
                'affected_component': 'ssl_configuration',
                'recommendation': 'Enable SECURE_SSL_REDIRECT=True to force HTTPS'
            })
        
        # Check SECURE_HSTS_SECONDS
        hsts_seconds = getattr(settings, 'SECURE_HSTS_SECONDS', 0)
        if hsts_seconds < 31536000:  # 1 year
            vulnerabilities.append({
                'title': 'Insufficient HSTS Configuration',
                'description': 'SECURE_HSTS_SECONDS is too low or not set',
                'risk_level': 'medium',
                'affected_component': 'ssl_configuration',
                'recommendation': 'Set SECURE_HSTS_SECONDS to at least 31536000 (1 year)'
            })
        
        return vulnerabilities
    
    def scan_security_headers(self) -> List[Dict[str, Any]]:
        """Scan security headers configuration"""
        vulnerabilities = []
        
        # Check X-Frame-Options
        if not getattr(settings, 'X_FRAME_OPTIONS', None):
            vulnerabilities.append({
                'title': 'Missing X-Frame-Options Header',
                'description': 'X-Frame-Options header is not configured',
                'risk_level': 'medium',
                'affected_component': 'security_headers',
                'recommendation': 'Set X_FRAME_OPTIONS = "DENY" or "SAMEORIGIN"'
            })
        
        # Check SECURE_CONTENT_TYPE_NOSNIFF
        if not getattr(settings, 'SECURE_CONTENT_TYPE_NOSNIFF', False):
            vulnerabilities.append({
                'title': 'Missing Content-Type Nosniff Header',
                'description': 'SECURE_CONTENT_TYPE_NOSNIFF is not enabled',
                'risk_level': 'low',
                'affected_component': 'security_headers',
                'recommendation': 'Enable SECURE_CONTENT_TYPE_NOSNIFF=True'
            })
        
        return vulnerabilities
    
    def scan_file_permissions(self) -> List[Dict[str, Any]]:
        """Scan file and directory permissions"""
        vulnerabilities = []
        
        # This would check file permissions in production
        # For now, return empty list as file system access varies by environment
        
        return vulnerabilities
    
    def generate_recommendations(self, scan_results: Dict[str, Any]) -> List[str]:
        """Generate prioritized security recommendations"""
        recommendations = []
        
        critical_count = scan_results['risk_summary']['critical']
        high_count = scan_results['risk_summary']['high']
        
        if critical_count > 0:
            recommendations.append(f"URGENT: Address {critical_count} critical vulnerabilities immediately")
        
        if high_count > 0:
            recommendations.append(f"HIGH PRIORITY: Fix {high_count} high-risk vulnerabilities")
        
        recommendations.append("Implement automated vulnerability scanning in CI/CD pipeline")
        recommendations.append("Set up regular security audits and penetration testing")
        recommendations.append("Enable comprehensive security monitoring and alerting")
        
        return recommendations

class AuditLogger:
    """Comprehensive audit logging system"""
    
    def __init__(self):
        self.log_types = {
            'authentication': self.log_authentication_event,
            'authorization': self.log_authorization_event,
            'data_access': self.log_data_access_event,
            'configuration': self.log_configuration_change,
            'security': self.log_security_event
        }
    
    def log_event(self, event_type: str, event_data: Dict[str, Any]):
        """Log audit event"""
        if event_type in self.log_types:
            self.log_types[event_type](event_data)
        else:
            self.log_generic_event(event_type, event_data)
    
    def log_authentication_event(self, event_data: Dict[str, Any]):
        """Log authentication events"""
        audit_entry = {
            'timestamp': timezone.now().isoformat(),
            'event_type': 'authentication',
            'user_id': event_data.get('user_id'),
            'username': event_data.get('username'),
            'ip_address': event_data.get('ip_address'),
            'user_agent': event_data.get('user_agent'),
            'action': event_data.get('action'),  # login, logout, failed_login
            'success': event_data.get('success', False),
            'additional_info': event_data.get('additional_info', {})
        }
        
        logger.info(f"AUTH_AUDIT: {json.dumps(audit_entry)}")
    
    def log_authorization_event(self, event_data: Dict[str, Any]):
        """Log authorization events"""
        audit_entry = {
            'timestamp': timezone.now().isoformat(),
            'event_type': 'authorization',
            'user_id': event_data.get('user_id'),
            'resource': event_data.get('resource'),
            'action': event_data.get('action'),
            'permission_required': event_data.get('permission_required'),
            'granted': event_data.get('granted', False),
            'ip_address': event_data.get('ip_address')
        }
        
        logger.info(f"AUTHZ_AUDIT: {json.dumps(audit_entry)}")
    
    def log_data_access_event(self, event_data: Dict[str, Any]):
        """Log data access events"""
        audit_entry = {
            'timestamp': timezone.now().isoformat(),
            'event_type': 'data_access',
            'user_id': event_data.get('user_id'),
            'data_type': event_data.get('data_type'),
            'action': event_data.get('action'),  # read, create, update, delete
            'record_id': event_data.get('record_id'),
            'ip_address': event_data.get('ip_address'),
            'sensitive_data': event_data.get('sensitive_data', False)
        }
        
        logger.info(f"DATA_AUDIT: {json.dumps(audit_entry)}")
    
    def log_configuration_change(self, event_data: Dict[str, Any]):
        """Log configuration changes"""
        audit_entry = {
            'timestamp': timezone.now().isoformat(),
            'event_type': 'configuration',
            'user_id': event_data.get('user_id'),
            'component': event_data.get('component'),
            'setting': event_data.get('setting'),
            'old_value': event_data.get('old_value'),
            'new_value': event_data.get('new_value'),
            'ip_address': event_data.get('ip_address')
        }
        
        logger.info(f"CONFIG_AUDIT: {json.dumps(audit_entry)}")
    
    def log_security_event(self, event_data: Dict[str, Any]):
        """Log security events"""
        audit_entry = {
            'timestamp': timezone.now().isoformat(),
            'event_type': 'security',
            'threat_type': event_data.get('threat_type'),
            'ip_address': event_data.get('ip_address'),
            'user_id': event_data.get('user_id'),
            'action_taken': event_data.get('action_taken'),
            'risk_score': event_data.get('risk_score'),
            'details': event_data.get('details', {})
        }
        
        logger.warning(f"SECURITY_AUDIT: {json.dumps(audit_entry)}")
    
    def log_generic_event(self, event_type: str, event_data: Dict[str, Any]):
        """Log generic audit event"""
        audit_entry = {
            'timestamp': timezone.now().isoformat(),
            'event_type': event_type,
            'data': event_data
        }
        
        logger.info(f"AUDIT: {json.dumps(audit_entry)}")

# Global instances
threat_detector = SecurityThreatDetector()
vulnerability_scanner = VulnerabilityScanner()
audit_logger = AuditLogger()

# Utility functions
def analyze_request_security(request: HttpRequest):
    """Analyze request for security threats"""
    return threat_detector.analyze_request(request)

def run_security_scan():
    """Run comprehensive security scan"""
    return vulnerability_scanner.run_vulnerability_scan()

def log_audit_event(event_type: str, event_data: Dict[str, Any]):
    """Log audit event"""
    return audit_logger.log_event(event_type, event_data) 