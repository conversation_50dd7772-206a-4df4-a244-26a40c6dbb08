"""
Server Hardening and Security Configuration Module
Implements system-level security measures and monitoring
"""

import os
import logging
import subprocess
import json
import socket
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.conf import settings
from django.core.management.base import BaseCommand
from django.utils import timezone

logger = logging.getLogger(__name__)

class SystemSecurityAuditor:
    """Audits and reports system security status"""
    
    def __init__(self):
        self.security_issues = []
        self.warnings = []
        self.recommendations = []
    
    def perform_security_audit(self) -> Dict[str, Any]:
        """Perform comprehensive security audit"""
        audit_results = {
            'timestamp': timezone.now().isoformat(),
            'system_info': self._get_system_info(),
            'security_checks': {},
            'overall_score': 0,
            'issues_found': [],
            'warnings': [],
            'recommendations': []
        }
        
        # File system security
        audit_results['security_checks']['filesystem'] = self._audit_filesystem()
        
        # Network security
        audit_results['security_checks']['network'] = self._audit_network()
        
        # Process security
        audit_results['security_checks']['processes'] = self._audit_processes()
        
        # Django settings security
        audit_results['security_checks']['django'] = self._audit_django_settings()
        
        # SSL/TLS configuration
        audit_results['security_checks']['ssl_tls'] = self._audit_ssl_configuration()
        
        # Database security
        audit_results['security_checks']['database'] = self._audit_database_security()
        
        # Calculate overall score
        audit_results['overall_score'] = self._calculate_security_score(audit_results['security_checks'])
        
        # Consolidate issues
        audit_results['issues_found'] = self.security_issues
        audit_results['warnings'] = self.warnings
        audit_results['recommendations'] = self.recommendations
        
        return audit_results
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get basic system information"""
        return {
            'platform': os.name,
            'python_version': os.sys.version,
            'django_version': getattr(settings, 'DJANGO_VERSION', 'Unknown'),
            'cpu_count': os.cpu_count(),
            'memory_gb': round(psutil.virtual_memory().total / (1024**3), 2),
            'disk_usage': {
                path: {
                    'total_gb': round(psutil.disk_usage(path).total / (1024**3), 2),
                    'free_gb': round(psutil.disk_usage(path).free / (1024**3), 2),
                    'used_percent': round((psutil.disk_usage(path).used / psutil.disk_usage(path).total) * 100, 2)
                } for path in ['/'] if os.path.exists(path)
            }
        }
    
    def _audit_filesystem(self) -> Dict[str, Any]:
        """Audit file system security"""
        issues = []
        warnings = []
        score = 100
        
        # Check file permissions for sensitive directories
        sensitive_dirs = [
            settings.MEDIA_ROOT,
            settings.STATIC_ROOT,
            getattr(settings, 'LOG_DIR', '/var/log'),
        ]
        
        for dir_path in sensitive_dirs:
            if os.path.exists(dir_path):
                stat_info = os.stat(dir_path)
                permissions = oct(stat_info.st_mode)[-3:]
                
                # Check if directory is world-writable
                if permissions.endswith('7'):
                    issues.append(f"Directory {dir_path} is world-writable ({permissions})")
                    score -= 20
                elif permissions.endswith('6'):
                    warnings.append(f"Directory {dir_path} is group-writable ({permissions})")
                    score -= 10
        
        # Check for temporary files in sensitive locations
        temp_locations = ['/tmp', '/var/tmp', settings.MEDIA_ROOT]
        for location in temp_locations:
            if os.path.exists(location):
                try:
                    old_files = []
                    for root, dirs, files in os.walk(location):
                        for file in files:
                            file_path = os.path.join(root, file)
                            if os.path.getmtime(file_path) < (timezone.now() - timedelta(days=7)).timestamp():
                                old_files.append(file_path)
                    
                    if len(old_files) > 100:
                        warnings.append(f"Many old temporary files found in {location}")
                        score -= 5
                except Exception:
                    pass
        
        # Store issues in class attributes
        self.security_issues.extend(issues)
        self.warnings.extend(warnings)
        
        return {
            'score': max(0, score),
            'issues': issues,
            'warnings': warnings,
            'checks_performed': ['file_permissions', 'temporary_files']
        }
    
    def _audit_network(self) -> Dict[str, Any]:
        """Audit network security configuration"""
        issues = []
        warnings = []
        score = 100
        
        # Check for open ports
        open_ports = []
        for conn in psutil.net_connections():
            if conn.status == 'LISTEN' and conn.laddr:
                open_ports.append(conn.laddr.port)
        
        # Common problematic ports
        dangerous_ports = [21, 23, 25, 53, 110, 143, 993, 995]
        for port in dangerous_ports:
            if port in open_ports:
                issues.append(f"Potentially dangerous port {port} is open")
                score -= 15
        
        # Check if running on privileged port without proper setup
        if 80 in open_ports or 443 in open_ports:
            if os.getuid() == 0:  # Running as root
                warnings.append("Application running as root with privileged ports")
                score -= 10
        
        # Check for unusual port usage
        if len(open_ports) > 20:
            warnings.append(f"Large number of open ports detected: {len(open_ports)}")
            score -= 5
        
        self.security_issues.extend(issues)
        self.warnings.extend(warnings)
        
        return {
            'score': max(0, score),
            'issues': issues,
            'warnings': warnings,
            'open_ports': sorted(set(open_ports)),
            'checks_performed': ['open_ports', 'privileged_ports']
        }
    
    def _audit_processes(self) -> Dict[str, Any]:
        """Audit running processes for security issues"""
        issues = []
        warnings = []
        score = 100
        
        # Check for processes running as root
        root_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'username']):
            try:
                if proc.info['username'] == 'root':
                    root_processes.append(proc.info['name'])
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        # Check if Django/Python is running as root
        python_as_root = any('python' in proc.lower() for proc in root_processes)
        if python_as_root:
            issues.append("Python/Django process running as root")
            score -= 25
        
        # Check memory usage
        memory_percent = psutil.virtual_memory().percent
        if memory_percent > 90:
            warnings.append(f"High memory usage: {memory_percent}%")
            score -= 10
        elif memory_percent > 80:
            warnings.append(f"Elevated memory usage: {memory_percent}%")
            score -= 5
        
        # Check CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        if cpu_percent > 90:
            warnings.append(f"High CPU usage: {cpu_percent}%")
            score -= 10
        
        self.security_issues.extend(issues)
        self.warnings.extend(warnings)
        
        return {
            'score': max(0, score),
            'issues': issues,
            'warnings': warnings,
            'memory_usage_percent': memory_percent,
            'cpu_usage_percent': cpu_percent,
            'checks_performed': ['root_processes', 'resource_usage']
        }
    
    def _audit_django_settings(self) -> Dict[str, Any]:
        """Audit Django security settings"""
        issues = []
        warnings = []
        score = 100
        
        # Check DEBUG setting
        if getattr(settings, 'DEBUG', False):
            issues.append("DEBUG is enabled in production")
            score -= 30
        
        # Check SECRET_KEY
        secret_key = getattr(settings, 'SECRET_KEY', '')
        if not secret_key:
            issues.append("SECRET_KEY is not set")
            score -= 40
        elif len(secret_key) < 50:
            warnings.append("SECRET_KEY is too short")
            score -= 10
        elif secret_key == 'django-insecure-':
            issues.append("Using default Django SECRET_KEY")
            score -= 30
        
        # Check ALLOWED_HOSTS
        allowed_hosts = getattr(settings, 'ALLOWED_HOSTS', [])
        if '*' in allowed_hosts:
            issues.append("ALLOWED_HOSTS contains wildcard '*'")
            score -= 20
        elif not allowed_hosts:
            warnings.append("ALLOWED_HOSTS is empty")
            score -= 10
        
        # Check security headers
        security_settings = [
            ('SECURE_SSL_REDIRECT', True),
            ('SECURE_HSTS_SECONDS', lambda x: x > 0),
            ('SECURE_CONTENT_TYPE_NOSNIFF', True),
            ('SECURE_BROWSER_XSS_FILTER', True),
            ('X_FRAME_OPTIONS', lambda x: x in ['DENY', 'SAMEORIGIN']),
        ]
        
        for setting_name, expected in security_settings:
            value = getattr(settings, setting_name, None)
            if callable(expected):
                if not expected(value):
                    warnings.append(f"Security setting {setting_name} not properly configured")
                    score -= 5
            elif value != expected:
                warnings.append(f"Security setting {setting_name} not set to {expected}")
                score -= 5
        
        self.security_issues.extend(issues)
        self.warnings.extend(warnings)
        
        return {
            'score': max(0, score),
            'issues': issues,
            'warnings': warnings,
            'checks_performed': ['debug_mode', 'secret_key', 'allowed_hosts', 'security_headers']
        }
    
    def _audit_ssl_configuration(self) -> Dict[str, Any]:
        """Audit SSL/TLS configuration"""
        issues = []
        warnings = []
        score = 100
        
        # Check if HTTPS is enforced
        if not getattr(settings, 'SECURE_SSL_REDIRECT', False):
            warnings.append("HTTPS redirect not enforced")
            score -= 15
        
        # Check HSTS configuration
        hsts_seconds = getattr(settings, 'SECURE_HSTS_SECONDS', 0)
        if hsts_seconds == 0:
            warnings.append("HSTS not configured")
            score -= 10
        elif hsts_seconds < 31536000:  # 1 year
            warnings.append("HSTS timeout is less than 1 year")
            score -= 5
        
        # Check secure cookie settings
        if not getattr(settings, 'SESSION_COOKIE_SECURE', False):
            warnings.append("Session cookies not set to secure")
            score -= 10
        
        if not getattr(settings, 'CSRF_COOKIE_SECURE', False):
            warnings.append("CSRF cookies not set to secure")
            score -= 10
        
        self.security_issues.extend(issues)
        self.warnings.extend(warnings)
        
        return {
            'score': max(0, score),
            'issues': issues,
            'warnings': warnings,
            'checks_performed': ['https_redirect', 'hsts', 'secure_cookies']
        }
    
    def _audit_database_security(self) -> Dict[str, Any]:
        """Audit database security configuration"""
        issues = []
        warnings = []
        score = 100
        
        # Check database configuration
        db_config = getattr(settings, 'DATABASES', {}).get('default', {})
        
        # Check if using SQLite in production
        if db_config.get('ENGINE', '').endswith('sqlite3'):
            warnings.append("Using SQLite database (not recommended for production)")
            score -= 15
        
        # Check database password
        db_password = db_config.get('PASSWORD', '')
        if not db_password:
            issues.append("Database password not set")
            score -= 25
        elif len(db_password) < 12:
            warnings.append("Database password is too short")
            score -= 10
        
        # Check database host
        db_host = db_config.get('HOST', '')
        if not db_host:
            warnings.append("Database host not specified")
            score -= 5
        elif db_host in ['127.0.0.1', 'localhost']:
            warnings.append("Database on localhost (ensure proper isolation)")
            score -= 5
        
        self.security_issues.extend(issues)
        self.warnings.extend(warnings)
        
        return {
            'score': max(0, score),
            'issues': issues,
            'warnings': warnings,
            'checks_performed': ['database_engine', 'database_password', 'database_host']
        }
    
    def _calculate_security_score(self, checks: Dict[str, Any]) -> int:
        """Calculate overall security score"""
        total_score = 0
        check_count = 0
        
        for check_name, check_result in checks.items():
            if isinstance(check_result, dict) and 'score' in check_result:
                total_score += check_result['score']
                check_count += 1
        
        return round(total_score / check_count) if check_count > 0 else 0


class SecurityMonitor:
    """Monitors system security in real-time"""
    
    def __init__(self):
        self.alert_thresholds = {
            'cpu_usage': 90,
            'memory_usage': 85,
            'disk_usage': 90,
            'failed_logins': 10,
            'suspicious_ips': 5
        }
    
    def monitor_system_resources(self) -> Dict[str, Any]:
        """Monitor system resource usage"""
        return {
            'timestamp': timezone.now().isoformat(),
            'cpu_usage': psutil.cpu_percent(interval=1),
            'memory_usage': psutil.virtual_memory().percent,
            'disk_usage': {
                '/': psutil.disk_usage('/').percent
            } if os.path.exists('/') else {},
            'network_io': dict(psutil.net_io_counters()._asdict()),
            'process_count': len(psutil.pids()),
            'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None
        }
    
    def check_suspicious_activity(self) -> Dict[str, Any]:
        """Check for suspicious activity patterns"""
        from .models import SecurityAuditLog
        
        now = timezone.now()
        last_hour = now - timedelta(hours=1)
        
        # Count failed login attempts
        failed_logins = SecurityAuditLog.objects.filter(
            event_type='login_failure',
            timestamp__gte=last_hour
        ).count()
        
        # Count unique IPs with failed attempts
        suspicious_ips = SecurityAuditLog.objects.filter(
            event_type='login_failure',
            timestamp__gte=last_hour
        ).values('ip_address').distinct().count()
        
        # Count file upload rejections
        upload_rejections = SecurityAuditLog.objects.filter(
            event_type='file_upload',
            success=False,
            timestamp__gte=last_hour
        ).count()
        
        alerts = []
        if failed_logins > self.alert_thresholds['failed_logins']:
            alerts.append(f"High number of failed logins: {failed_logins}")
        
        if suspicious_ips > self.alert_thresholds['suspicious_ips']:
            alerts.append(f"Multiple IPs with failed attempts: {suspicious_ips}")
        
        if upload_rejections > 20:
            alerts.append(f"High number of rejected uploads: {upload_rejections}")
        
        return {
            'timestamp': now.isoformat(),
            'failed_logins_last_hour': failed_logins,
            'suspicious_ips_last_hour': suspicious_ips,
            'upload_rejections_last_hour': upload_rejections,
            'alerts': alerts,
            'risk_level': 'high' if alerts else 'low'
        }
    
    def generate_security_report(self) -> Dict[str, Any]:
        """Generate comprehensive security report"""
        auditor = SystemSecurityAuditor()
        audit_results = auditor.perform_security_audit()
        
        resource_usage = self.monitor_system_resources()
        suspicious_activity = self.check_suspicious_activity()
        
        return {
            'report_timestamp': timezone.now().isoformat(),
            'security_audit': audit_results,
            'resource_monitoring': resource_usage,
            'threat_detection': suspicious_activity,
            'recommendations': self._generate_recommendations(audit_results)
        }
    
    def _generate_recommendations(self, audit_results: Dict[str, Any]) -> List[str]:
        """Generate security recommendations based on audit results"""
        recommendations = []
        
        if audit_results['overall_score'] < 70:
            recommendations.append("Overall security score is low. Address critical issues immediately.")
        
        if audit_results['issues_found']:
            recommendations.append("Critical security issues found. Review and fix immediately.")
        
        if len(audit_results['warnings']) > 5:
            recommendations.append("Multiple warnings detected. Consider addressing them.")
        
        recommendations.extend([
            "Regularly update all dependencies and security patches",
            "Implement regular security audits and monitoring",
            "Set up automated backup and disaster recovery procedures",
            "Consider implementing Web Application Firewall (WAF)",
            "Enable comprehensive logging and monitoring",
            "Conduct regular penetration testing"
        ])
        
        return recommendations


class FirewallManager:
    """Manages firewall rules and network security"""
    
    def __init__(self):
        self.allowed_ports = [22, 80, 443, 8000]  # SSH, HTTP, HTTPS, Django dev
        self.blocked_ips = set()
    
    def get_firewall_status(self) -> Dict[str, Any]:
        """Get current firewall status"""
        try:
            # Try to get iptables status (Linux)
            result = subprocess.run(['iptables', '-L'], capture_output=True, text=True)
            if result.returncode == 0:
                return {
                    'status': 'active',
                    'type': 'iptables',
                    'rules': result.stdout.split('\n')
                }
        except FileNotFoundError:
            pass
        
        try:
            # Try to get ufw status (Ubuntu)
            result = subprocess.run(['ufw', 'status'], capture_output=True, text=True)
            if result.returncode == 0:
                return {
                    'status': 'active' if 'Status: active' in result.stdout else 'inactive',
                    'type': 'ufw',
                    'rules': result.stdout.split('\n')
                }
        except FileNotFoundError:
            pass
        
        return {
            'status': 'unknown',
            'type': 'unknown',
            'rules': []
        }
    
    def suggest_firewall_rules(self) -> List[str]:
        """Suggest firewall rules for security"""
        rules = [
            "# Basic firewall rules for CV Analyzer",
            "ufw default deny incoming",
            "ufw default allow outgoing",
            "ufw allow ssh",
            "ufw allow 80/tcp",
            "ufw allow 443/tcp",
            "ufw --force enable"
        ]
        
        return rules


# Utility functions
def perform_security_audit() -> Dict[str, Any]:
    """Utility function to perform security audit"""
    auditor = SystemSecurityAuditor()
    return auditor.perform_security_audit()

def monitor_security() -> Dict[str, Any]:
    """Utility function to monitor security"""
    monitor = SecurityMonitor()
    return monitor.generate_security_report()

def check_system_hardening() -> Dict[str, Any]:
    """Utility function to check system hardening status"""
    auditor = SystemSecurityAuditor()
    firewall = FirewallManager()
    
    return {
        'security_audit': auditor.perform_security_audit(),
        'firewall_status': firewall.get_firewall_status(),
        'firewall_recommendations': firewall.suggest_firewall_rules(),
        'timestamp': timezone.now().isoformat()
    } 