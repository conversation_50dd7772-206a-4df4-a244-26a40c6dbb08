import os
from pathlib import Path
from cryptography.fernet import <PERSON>rnet

# At the very top of the file, before any other imports
def generate_key():
    from cryptography.fernet import Fernet
    return Fernet.generate_key()

# Set the encryption key
FIELD_ENCRYPTION_KEY = generate_key()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Load environment variables
from dotenv import load_dotenv
load_dotenv(os.path.join(BASE_DIR, '.env'))

# Now we can import Django modules
from django.core.exceptions import ImproperlyConfigured

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('SECRET_KEY', 'django-insecure-your-secret-key-here')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = bool(int(os.getenv('DEBUG', '1')))

ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', 'localhost,127.0.0.1').split(',')

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'cv_analyzer',
    'encrypted_model_fields',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'cv_analyzer.middleware.SecurityHeadersMiddleware',
    'cv_analyzer.middleware.RateLimitMiddleware',
    'cv_analyzer.middleware.IPFilterMiddleware',
    'cv_analyzer.performance_monitoring.PerformanceMiddleware',
    'cv_analyzer.caching_optimization.CacheMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'cv_analyzer.middleware.SessionSecurityMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'cv_analyzer.middleware.RequestValidationMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'cv_analyzer.middleware.AuthenticationAuditMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'cv_analyzer.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'cv_analyzer.wsgi.application'

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    },
    'postgresql': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('POSTGRES_DB', 'cv_analyzer'),
        'USER': os.getenv('POSTGRES_USER', 'postgres'),
        'PASSWORD': os.getenv('POSTGRES_PASSWORD', ''),
        'HOST': os.getenv('POSTGRES_HOST', 'localhost'),
        'PORT': os.getenv('POSTGRES_PORT', '5432'),
        'OPTIONS': {
            'options': '-c search_path=public'
        },
        'CONN_MAX_AGE': 600,  # Connection pooling
    }
}

# Database performance settings
DATABASE_CONNECTION_POOLING = {
    'MAX_CONNS': 20,
    'MIN_CONNS': 5,
    'MAX_LIFETIME': 3600,  # 1 hour
    'HEALTH_CHECK_PERIOD': 30,  # 30 seconds
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = 'static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Celery Configuration
CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 600  # 10 minutes
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_WORKER_MAX_TASKS_PER_CHILD = 50
CELERY_WORKER_SEND_TASK_EVENTS = True

# Redis Configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.getenv('REDIS_URL', 'redis://localhost:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'PARSER_CLASS': 'redis.connection.HiredisParser',
            'SOCKET_TIMEOUT': 5,
            'SOCKET_CONNECT_TIMEOUT': 5,
            'CONNECTION_POOL_CLASS': 'redis.BlockingConnectionPool',
            'CONNECTION_POOL_CLASS_KWARGS': {
                'max_connections': 50,
                'timeout': 20,
            },
            'MAX_CONNECTIONS': 1000,
            'RETRY_ON_TIMEOUT': True,
        }
    }
}

# Cache time to live is 15 minutes
CACHE_TTL = int(os.getenv('CACHE_TTL', 900))

# Session Configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'

# CV Upload Settings
ALLOWED_CV_TYPES = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
MAX_CV_SIZE = 10 * 1024 * 1024  # 10MB

# AI Configuration
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
GROQ_API_KEY = os.getenv('GROQ_API_KEY')
AI_ANALYSIS_TIMEOUT = 60  # seconds

# Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'cv_analyzer.log'),
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'cv_analyzer': {
            'handlers': ['console', 'file'],
            'level': os.getenv('LOG_LEVEL', 'INFO'),
            'propagate': True,
        },
        'celery': {
            'handlers': ['console', 'file'],
            'level': os.getenv('LOG_LEVEL', 'INFO'),
            'propagate': True,
        },
    },
}

# Create logs directory if it doesn't exist
LOGS_DIR = os.path.join(BASE_DIR, 'logs')
if not os.path.exists(LOGS_DIR):
    os.makedirs(LOGS_DIR)

# Security Settings
SECURE_SSL_REDIRECT = bool(int(os.getenv('SECURE_SSL_REDIRECT', '0')))
SESSION_COOKIE_SECURE = bool(int(os.getenv('SESSION_COOKIE_SECURE', '0')))
CSRF_COOKIE_SECURE = bool(int(os.getenv('CSRF_COOKIE_SECURE', '0')))
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Email Configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.getenv('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.getenv('EMAIL_PORT', '587'))
EMAIL_USE_TLS = bool(int(os.getenv('EMAIL_USE_TLS', '1')))
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD')
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '<EMAIL>')

# Security Configuration
MAX_REQUEST_SIZE = 50 * 1024 * 1024  # 50MB
SESSION_TIMEOUT = 3600  # 1 hour in seconds
BLOCKED_IPS = []  # List of blocked IP addresses
ALLOWED_IPS = []  # List of allowed IP addresses (empty = allow all)

# File Upload Security
MAX_UPLOAD_SIZE = 10 * 1024 * 1024  # 10MB
ALLOWED_FILE_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
]

# ClamAV Configuration
CLAMD_HOST = os.getenv('CLAMD_HOST', 'localhost')
CLAMD_PORT = int(os.getenv('CLAMD_PORT', '3310'))
CLAMD_SOCKET = os.getenv('CLAMD_SOCKET', '/var/run/clamav/clamd.ctl')

# Two-Factor Authentication
TWO_FACTOR_ENABLED = bool(int(os.getenv('TWO_FACTOR_ENABLED', '0')))

# Password Policy
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 12,  # Increased from default 8
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
    {
        'NAME': 'cv_analyzer.validators.ComplexPasswordValidator',
    },
]

# Session Security
SESSION_COOKIE_AGE = 3600  # 1 hour
SESSION_COOKIE_SECURE = not DEBUG
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Strict'
SESSION_SAVE_EVERY_REQUEST = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = True

# CSRF Security
CSRF_COOKIE_SECURE = not DEBUG
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Strict'
CSRF_USE_SESSIONS = True

# Additional Security Headers
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'

# Performance Monitoring Configuration
PERFORMANCE_MONITORING = {
    'ENABLED': bool(int(os.getenv('PERFORMANCE_MONITORING_ENABLED', '1'))),
    'RESPONSE_TIME_THRESHOLD': 2000,  # 2 seconds
    'DATABASE_QUERY_THRESHOLD': 500,  # 500ms
    'MEMORY_THRESHOLD': 80,  # 80%
    'CPU_THRESHOLD': 75,  # 75%
    'MONITORING_INTERVAL': 60,  # 60 seconds
    'METRICS_RETENTION_DAYS': 30,
}

# Cache Optimization Configuration
CACHE_OPTIMIZATION = {
    'STRATEGY_ENABLED': True,
    'QUERY_CACHE_TIMEOUT': 3600,  # 1 hour
    'TEMPLATE_CACHE_TIMEOUT': 1800,  # 30 minutes
    'API_CACHE_TIMEOUT': 900,  # 15 minutes
    'FILE_METADATA_CACHE_TIMEOUT': 7200,  # 2 hours
    'USER_SESSION_CACHE_TIMEOUT': 1800,  # 30 minutes
    'CACHE_WARMUP_ENABLED': True,
    'CACHE_COMPRESSION_ENABLED': True,
}

# Database Optimization Configuration
DATABASE_OPTIMIZATION = {
    'QUERY_LOGGING_ENABLED': bool(int(os.getenv('DATABASE_QUERY_LOGGING', '0'))),
    'SLOW_QUERY_THRESHOLD': 500,  # 500ms
    'CONNECTION_POOL_SIZE': 20,
    'CONNECTION_POOL_OVERFLOW': 5,
    'CONNECTION_POOL_TIMEOUT': 30,
    'QUERY_CACHE_SIZE': 50,  # Number of queries to cache
    'INDEX_OPTIMIZATION_ENABLED': True,
    'VACUUM_SCHEDULE': 'daily',  # PostgreSQL maintenance
}

# Redis Cluster Configuration
REDIS_CLUSTER = {
    'ENABLED': bool(int(os.getenv('REDIS_CLUSTER_ENABLED', '0'))),
    'NODES': [
        {'host': os.getenv('REDIS_NODE1_HOST', 'localhost'), 'port': int(os.getenv('REDIS_NODE1_PORT', '7000'))},
        {'host': os.getenv('REDIS_NODE2_HOST', 'localhost'), 'port': int(os.getenv('REDIS_NODE2_PORT', '7001'))},
        {'host': os.getenv('REDIS_NODE3_HOST', 'localhost'), 'port': int(os.getenv('REDIS_NODE3_PORT', '7002'))},
    ],
    'FAILOVER_ENABLED': True,
    'READ_PREFERENCE': 'primary_preferred',
    'HEALTH_CHECK_INTERVAL': 30,  # seconds
}

# Application Performance Targets
PERFORMANCE_TARGETS = {
    'PAGE_LOAD_TIME_MS': 2000,  # 2 seconds
    'API_RESPONSE_TIME_MS': 500,  # 500ms
    'FILE_UPLOAD_TIME_MS': 30000,  # 30 seconds for 10MB
    'AI_ANALYSIS_TIME_MS': 60000,  # 60 seconds
    'SYSTEM_UPTIME_PERCENT': 99.9,
    'CONCURRENT_USERS': 1000,
    'CACHE_HIT_RATIO_PERCENT': 70,
    'DATABASE_QUERY_TIME_MS': 100,  # Average
}

# Health Check Configuration
HEALTH_CHECK = {
    'ENABLED': True,
    'ENDPOINT': '/health/',
    'DATABASE_CHECK': True,
    'CACHE_CHECK': True,
    'DISK_SPACE_CHECK': True,
    'MEMORY_CHECK': True,
    'CPU_CHECK': True,
    'EXTERNAL_SERVICES_CHECK': True,
    'RESPONSE_FORMAT': 'json',
    'DETAILED_CHECKS': True,
}