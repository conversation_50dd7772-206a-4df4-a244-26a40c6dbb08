/* Custom styles for CV Analyzer */

body {
    font-family: 'Roboto', sans-serif;
    background-color: #f8f9fa;
    padding-top: 70px;
}

.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,.2);
}

.navbar-dark {
    background-color: #1a1a1a !important;
}

.navbar-brand, .navbar-nav .nav-link {
    color: #ffffff !important;
    font-weight: 500;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.5);
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    color: rgba(255, 255, 255, 0.75);
}

.navbar-nav .nav-item.active .nav-link {
    color: #ffffff;
    font-weight: bold;
}

.navbar-nav .nav-link:hover {
    color: #007bff !important;
}

.navbar-nav .nav-item {
    margin-right: 10px;
}

.navbar-nav .nav-link i {
    margin-right: 5px;
}

.container {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,.1);
    padding: 20px;
    margin-top: 20px;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.progress {
    height: 10px;
    font-size: 0.8rem;
    background-color: #ecf0f1;
    border-radius: 5px;
    overflow: hidden;
}

.progress-bar {
    background-color: #2ecc71;
    transition: width 1s ease-in-out;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 8px rgba(0,0,0,.15);
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.welcome-container {
    max-width: 900px;
    margin: 0 auto;
}

.card {
    transition: transform 0.3s ease-in-out;
}

.card:hover {
    transform: translateY(-5px);
}

:root {
    --bg-color: #ffffff;
    --text-color: #333333;
    --navbar-bg-color: #007bff;
    --card-bg-color: #f8f9fa;
}

[data-theme="dark"] {
    --bg-color: #333333;
    --text-color: #ffffff;
    --navbar-bg-color: #0056b3;
    --card-bg-color: #444444;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.navbar-dark {
    background-color: var(--navbar-bg-color) !important;
}

.card, .container {
    background-color: var(--card-bg-color);
    color: var(--text-color);
}

.btn-primary {
    background-color: var(--navbar-bg-color);
    border-color: var(--navbar-bg-color);
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.navbar .container-fluid {
    flex-wrap: nowrap;
}

.navbar h1 {
    font-size: 1.5rem;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

@media (max-width: 991.98px) {
    .navbar h1 {
        font-size: 1.2rem;
    }
}