/* Theme Colors */
:root {
    --primary-color: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #22c55e;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
}

/* Dark Mode Colors */
[data-theme="dark"] {
    --primary-color: #60a5fa;
    --secondary-color: #94a3b8;
    --success-color: #4ade80;
    --danger-color: #f87171;
    --warning-color: #fbbf24;
    --info-color: #60a5fa;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Custom Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes scaleIn {
    from { transform: scale(0.95); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.scale-in {
    animation: scaleIn 0.2s ease-out;
}

/* Custom Card Styles */
.custom-card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
}

/* Custom Button Styles */
.btn-primary {
    @apply px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 transition-all duration-200 hover:-translate-y-0.5 active:translate-y-0;
}

.btn-secondary {
    @apply px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:ring-4 focus:ring-gray-300 transition-all duration-200 hover:-translate-y-0.5 active:translate-y-0;
}

.btn-success {
    @apply px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-4 focus:ring-green-300 transition-all duration-200 hover:-translate-y-0.5 active:translate-y-0;
}

.btn-danger {
    @apply px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:ring-4 focus:ring-red-300 transition-all duration-200 hover:-translate-y-0.5 active:translate-y-0;
}

/* Custom Form Styles */
.form-input {
    @apply bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 transition-colors duration-200;
}

.form-label {
    @apply block mb-2 text-sm font-medium text-gray-900 dark:text-white;
}

/* Custom Badge Styles */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-all duration-200 hover:scale-105;
}

.badge-primary {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
}

.badge-success {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
}

.badge-danger {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
}

/* Custom Table Styles */
.custom-table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700 rounded-lg overflow-hidden;
}

.custom-table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400 bg-gray-50 dark:bg-gray-800;
}

.custom-table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
}

.custom-table tr {
    @apply bg-white dark:bg-gray-800 transition-colors duration-200;
}

.custom-table tr:nth-child(even) {
    @apply bg-gray-50 dark:bg-gray-700;
}

.custom-table tr:hover {
    @apply bg-gray-100 dark:bg-gray-600;
}

/* Custom Alert Styles */
.alert {
    @apply p-4 mb-4 rounded-lg border transition-all duration-200 hover:shadow-md;
}

.alert-success {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 border-green-200 dark:border-green-800;
}

.alert-danger {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 border-red-200 dark:border-red-800;
}

.alert-warning {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800;
}

.alert-info {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 border-blue-200 dark:border-blue-800;
}

/* Custom Modal Styles */
.modal {
    @apply fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 transition-opacity duration-200;
}

.modal-content {
    @apply relative bg-white dark:bg-gray-800 rounded-lg shadow-xl mx-auto my-8 max-w-lg p-6 transform transition-all duration-300 scale-in;
}

/* Custom Tooltip Styles */
.tooltip {
    @apply relative inline-block;
}

.tooltip-text {
    @apply invisible absolute z-10 py-2 px-3 text-sm text-white bg-gray-900 rounded-lg opacity-0 transition-all duration-200 scale-95;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
}

.tooltip:hover .tooltip-text {
    @apply visible opacity-100 scale-100;
}

/* Custom Progress Bar Styles */
.progress {
    @apply w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 overflow-hidden;
}

.progress-bar {
    @apply bg-blue-600 h-2.5 rounded-full transition-all duration-500 ease-out;
}

/* Custom Popup Styles - High Priority */
div.popup-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 9999 !important;
    display: none !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    backdrop-filter: blur(4px) !important;
}

div.popup-overlay.show {
    display: flex !important;
}

div.popup-content {
    position: relative !important;
    background-color: white !important;
    border-radius: 8px !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
    margin: 16px !important;
    padding: 24px !important;
    max-width: 56rem !important;
    width: 100% !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    transform: scale(0.95) !important;
    opacity: 0 !important;
    transition: all 0.3s ease-out !important;
    animation: popupEnter 0.3s ease-out forwards !important;
}

/* Dark mode support */
html.dark div.popup-content {
    background-color: #1f2937 !important;
    color: white !important;
}

div.popup-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 16px !important;
    padding-bottom: 16px !important;
    border-bottom: 1px solid #e5e7eb !important;
}

button.popup-close {
    color: #9ca3af !important;
    background-color: transparent !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    width: 32px !important;
    height: 32px !important;
    margin-left: auto !important;
    display: inline-flex !important;
    justify-content: center !important;
    align-items: center !important;
    cursor: pointer !important;
    border: none !important;
    transition: all 0.2s ease !important;
}

button.popup-close:hover {
    background-color: #f3f4f6 !important;
    color: #111827 !important;
}

/* Dark mode support for header elements */
html.dark div.popup-header {
    border-bottom-color: #374151 !important;
}

html.dark button.popup-close:hover {
    background-color: #4b5563 !important;
    color: white !important;
}

.tab-btn {
    @apply px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 hover:text-gray-700 focus:z-10 focus:ring-2 focus:ring-blue-500 focus:text-blue-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 transition-all duration-200;
}

.tab-btn.active {
    @apply text-blue-600 bg-blue-50 border-blue-500 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-500;
}

.upload-method {
    @apply transition-all duration-300;
}

.upload-method.active {
    @apply block;
}

.dropzone {
    @apply border-2 border-dashed border-blue-300 rounded-lg p-8 text-center cursor-pointer hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 dark:border-blue-600 dark:hover:border-blue-500 dark:hover:bg-blue-900;
}

.file-upload-icon {
    @apply w-16 h-16 mx-auto mb-4 bg-blue-600 rounded-full flex items-center justify-center;
}

.option-card {
    @apply transition-all duration-200 hover:shadow-md hover:-translate-y-1;
}

.option-card.selected {
    @apply border-blue-500 bg-blue-50 dark:bg-blue-900 shadow-md;
}

.form-control {
    @apply bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 transition-colors duration-200;
}

.form-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.form-group {
    @apply mb-4;
}

.btn {
    @apply inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg focus:ring-4 focus:outline-none transition-all duration-200 hover:-translate-y-0.5 active:translate-y-0;
}

.btn-sm {
    @apply px-3 py-1.5 text-xs;
}

.progress-container {
    @apply w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mb-2;
}

.progress-fill {
    @apply bg-blue-600 h-2.5 rounded-full transition-all duration-500 ease-out;
}

.progress-text {
    @apply text-sm text-gray-600 dark:text-gray-400 text-center;
}

.integration-card {
    @apply transition-all duration-200 hover:shadow-md hover:-translate-y-1;
}

.cv-list {
    @apply space-y-2;
}

.analysis-type {
    @apply transition-all duration-300;
}

@keyframes popupEnter {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* Dashboard Quick Actions */
.quick-actions {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4;
}

.quick-action-btn {
    @apply p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 dark:bg-gray-800 dark:hover:bg-gray-700 text-left cursor-pointer border-none w-full;
}

.quick-action-btn:hover {
    @apply transform -translate-y-1;
}

/* Custom Chart Styles */
.chart-container {
    @apply bg-white dark:bg-gray-800 rounded-lg p-4 shadow-md transition-all duration-300 hover:shadow-lg;
    position: relative;
    overflow: hidden;
}

.chart-container canvas {
    max-width: 100% !important;
    max-height: 100% !important;
}

.metric-section .chart-container {
    background: transparent;
    box-shadow: none;
    padding: 0;
}

.metric-section .chart-container:hover {
    box-shadow: none;
}

.chart-title {
    @apply text-lg font-semibold mb-4 text-gray-900 dark:text-white;
}

/* Custom Dropdown Styles */
.dropdown {
    @apply relative inline-block;
}

.dropdown-content {
    @apply hidden absolute right-0 mt-2 w-48 rounded-lg shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 transform transition-all duration-200 origin-top-right scale-95;
}

.dropdown-content.show {
    @apply block scale-100;
}

.dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200;
}

/* Custom Switch Styles */
.switch {
    @apply relative inline-flex items-center cursor-pointer;
}

.switch-input {
    @apply sr-only;
}

.switch-slider {
    @apply w-11 h-6 bg-gray-200 rounded-full dark:bg-gray-700 transition-all duration-200;
}

.switch-slider:before {
    @apply content-[''] absolute h-5 w-5 left-1 bottom-1 bg-white rounded-full transition-all duration-200;
}

.switch-input:checked + .switch-slider {
    @apply bg-blue-600;
}

.switch-input:checked + .switch-slider:before {
    @apply transform translate-x-full;
}

/* Custom File Upload Styles */
.file-upload {
    @apply relative border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center transition-all duration-200 hover:border-blue-500 dark:hover:border-blue-400;
}

.file-upload:hover {
    @apply bg-blue-50 dark:bg-blue-900/20;
}

.file-upload input[type="file"] {
    @apply absolute inset-0 w-full h-full opacity-0 cursor-pointer;
}

/* Custom List Styles */
.custom-list {
    @apply space-y-2;
}

.custom-list-item {
    @apply flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700;
}

/* Custom Breadcrumb Styles */
.breadcrumb {
    @apply flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400;
}

.breadcrumb-item {
    @apply hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200;
}

.breadcrumb-separator {
    @apply text-gray-400;
}

/* Custom Skeleton Loading Styles */
.skeleton {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
}

/* Custom Animations for Dropdown */
@keyframes dropdownEnter {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.animate-dropdown {
    animation: dropdownEnter 0.2s ease-out;
}

/* Custom Animations for Page Transitions */
@keyframes pageEnter {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.page-transition {
    animation: pageEnter 0.3s ease-out;
}

/* Custom Animations for Loading States */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom Animations for Success/Error States */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.bounce {
    animation: bounce 0.5s ease-in-out;
}

/* Enhanced Action Button Styles for CV Detail Popup */
.action-btn {
    @apply relative flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-all duration-300 cursor-pointer shadow-sm hover:shadow-md group overflow-hidden;
}

.action-btn:hover {
    @apply transform -translate-y-0.5;
}

.action-btn-primary {
    @apply hover:bg-blue-50;
}

.action-btn-secondary {
    @apply hover:bg-gray-50;
}

.action-btn-danger {
    @apply hover:bg-red-50;
}

/* Black Theme Action Buttons */
.action-btn-black {
    @apply bg-gray-800 border-gray-600 hover:border-gray-500 shadow-lg hover:shadow-xl;
}

.action-btn-black.action-btn-primary {
    @apply hover:bg-gray-700;
}

.action-btn-black.action-btn-secondary {
    @apply hover:bg-gray-700;
}

.action-btn-black.action-btn-danger {
    @apply hover:bg-gray-700;
}

.action-btn-icon {
    @apply w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300 mr-3 text-sm font-medium;
}

.action-btn-content {
    @apply flex flex-col text-left min-w-0 flex-1;
}

.action-btn-title {
    @apply text-sm font-semibold text-gray-900 leading-tight;
}

.action-btn-subtitle {
    @apply text-xs text-gray-500 leading-tight mt-0.5;
}

/* Compact Button Style (for smaller spaces) */
.btn-compact {
    @apply px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 flex items-center space-x-2 hover:transform hover:-translate-y-0.5 active:translate-y-0 shadow-sm hover:shadow-md;
}

.btn-compact i {
    @apply text-sm;
}

/* Quick Action Responsive Adjustments */
@media (max-width: 640px) {
    .action-btn {
        @apply p-2;
    }
    
    .action-btn-icon {
        @apply w-8 h-8 mr-2;
    }
    
    .action-btn-title {
        @apply text-xs;
    }
    
    .action-btn-subtitle {
        @apply text-xs;
    }
} 