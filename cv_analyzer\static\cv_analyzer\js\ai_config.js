/**
 * AI Configuration Popup JavaScript
 * Handles all functionality for the AI configuration popup in the analysis hub
 */

// Global variables
let currentProviders = [];
let currentPrompts = [];
let currentGeneralSettings = {};

// Popup management
function openAIConfigPopup() {
    document.getElementById('aiConfigPopup').classList.remove('hidden');
    loadAIConfigurations();
    // Switch to providers tab by default
    switchConfigTab('providers');
}

function closePopup(popupId) {
    document.getElementById(popupId).classList.add('hidden');
    // Reset forms when closing
    resetAllForms();
}

// Tab switching
function switchConfigTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.config-tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected tab
    document.getElementById(tabName + 'Tab').classList.remove('hidden');
    
    // Add active class to selected tab button
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // Load data for the selected tab
    switch(tabName) {
        case 'providers':
            loadProviders();
            break;
        case 'settings':
            loadGeneralSettings();
            break;
        case 'prompts':
            loadPrompts();
            break;
    }
}

// Load all AI configurations
async function loadAIConfigurations() {
    try {
        showLoading('Loading AI configurations...');
        
        // Load providers, settings, and prompts in parallel
        await Promise.all([
            loadProviders(),
            loadGeneralSettings(),
            loadPrompts()
        ]);
        
        hideLoading();
    } catch (error) {
        hideLoading();
        showNotification('Error loading AI configurations: ' + error.message, 'error');
    }
}

// Provider management
async function loadProviders() {
    try {
        const response = await fetch('/api/ai-config/providers/', {
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        });
        
        if (!response.ok) throw new Error('Failed to load providers');
        
        const data = await response.json();
        currentProviders = data.providers || [];
        renderProvidersList();
    } catch (error) {
        showNotification('Error loading AI providers: ' + error.message, 'error');
    }
}

function renderProvidersList() {
    const container = document.getElementById('providersList');
    
    if (currentProviders.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-cloud text-4xl mb-3 opacity-50"></i>
                <p>No AI providers configured yet.</p>
                <p class="text-sm">Click "Add Provider" to get started.</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = currentProviders.map(provider => `
        <div class="provider-card ${provider.is_active ? 'active' : 'inactive'}">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <div class="flex items-center gap-2 mb-2">
                        <h5 class="font-semibold text-gray-900">${provider.provider}</h5>
                        <span class="status-indicator ${provider.is_active ? 'active' : 'inactive'}">
                            ${provider.is_active ? 'Active' : 'Inactive'}
                        </span>
                        ${provider.priority === 1 ? '<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">Primary</span>' : ''}
                    </div>
                    <p class="text-sm text-gray-600 mb-1">
                        <strong>Model:</strong> ${provider.model_name}
                    </p>
                    <p class="text-sm text-gray-600 mb-1">
                        <strong>Priority:</strong> ${provider.priority}
                    </p>
                    <p class="text-sm text-gray-600">
                        <strong>Tokens:</strong> ${provider.max_tokens} | 
                        <strong>Temperature:</strong> ${provider.temperature}
                    </p>
                </div>
                <div class="flex gap-2">
                    <button class="btn btn-sm btn-secondary" onclick="editProvider(${provider.id})" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="testProviderConnection(${provider.id})" title="Test Connection">
                        <i class="fas fa-plug"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteProvider(${provider.id})" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

function addNewProvider() {
    resetProviderForm();
    document.getElementById('providerForm').classList.remove('hidden');
    document.getElementById('providerSelect').focus();
}

function editProvider(providerId) {
    const provider = currentProviders.find(p => p.id === providerId);
    if (!provider) return;
    
    // Populate form
    document.getElementById('providerId').value = provider.id;
    document.getElementById('providerSelect').value = provider.provider;
    document.getElementById('modelName').value = provider.model_name;
    document.getElementById('apiKey').value = provider.api_key;
    document.getElementById('priority').value = provider.priority;
    document.getElementById('maxTokens').value = provider.max_tokens;
    document.getElementById('temperature').value = provider.temperature;
    document.getElementById('isActive').checked = provider.is_active;
    
    // Update UI
    updateProviderGuidance();
    document.getElementById('providerForm').classList.remove('hidden');
    document.getElementById('providerSelect').focus();
}

async function saveProvider() {
    try {
        const formData = new FormData(document.getElementById('aiProviderForm'));
        const providerId = document.getElementById('providerId').value;
        
        const url = providerId ? 
            `/api/ai-config/providers/${providerId}/` : 
            '/api/ai-config/providers/';
        
        const method = providerId ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: formData
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to save provider');
        }
        
        showNotification('Provider saved successfully!', 'success');
        cancelProviderForm();
        loadProviders();
        
    } catch (error) {
        showNotification('Error saving provider: ' + error.message, 'error');
    }
}

async function deleteProvider(providerId) {
    if (!confirm('Are you sure you want to delete this AI provider?')) return;
    
    try {
        const response = await fetch(`/api/ai-config/providers/${providerId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        });
        
        if (!response.ok) throw new Error('Failed to delete provider');
        
        showNotification('Provider deleted successfully!', 'success');
        loadProviders();
        
    } catch (error) {
        showNotification('Error deleting provider: ' + error.message, 'error');
    }
}

async function testProviderConnection(providerId) {
    try {
        showLoading('Testing connection...');
        
        const response = await fetch(`/api/ai-config/providers/${providerId}/test/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        });
        
        const data = await response.json();
        hideLoading();
        
        if (data.success) {
            showNotification('Connection test successful!', 'success');
        } else {
            showNotification('Connection test failed: ' + data.error, 'error');
        }
        
    } catch (error) {
        hideLoading();
        showNotification('Error testing connection: ' + error.message, 'error');
    }
}

function cancelProviderForm() {
    document.getElementById('providerForm').classList.add('hidden');
    resetProviderForm();
}

function resetProviderForm() {
    document.getElementById('aiProviderForm').reset();
    document.getElementById('providerId').value = '';
    document.getElementById('fetchModelsBtn').disabled = true;
    document.getElementById('testConnectionBtn').disabled = true;
    updateProviderGuidance();
}

// Provider guidance and model fetching
function updateProviderGuidance() {
    const provider = document.getElementById('providerSelect').value;
    const guidanceDiv = document.getElementById('providerGuidance');
    const fetchBtn = document.getElementById('fetchModelsBtn');
    const testBtn = document.getElementById('testConnectionBtn');
    
    let guidance = '';
    
    switch(provider) {
        case 'openai':
            guidance = `
                <strong class="text-blue-800">OpenAI Configuration:</strong><br>
                • <strong>API Key:</strong> Enter your OpenAI API key from openai.com<br>
                • <strong>Model:</strong> Recommended models: gpt-4, gpt-3.5-turbo<br>
                • <strong>Usage:</strong> Pay-per-token pricing
            `;
            fetchBtn.disabled = false;
            testBtn.disabled = false;
            break;
        case 'groq':
            guidance = `
                <strong class="text-blue-800">Groq Configuration:</strong><br>
                • <strong>API Key:</strong> Enter your Groq API key from console.groq.com<br>
                • <strong>Model:</strong> Recommended: llama-3.1-70b-versatile, mixtral-8x7b-32768<br>
                • <strong>Usage:</strong> Fast inference with free tier available
            `;
            fetchBtn.disabled = false;
            testBtn.disabled = false;
            break;
        case 'ollama':
            guidance = `
                <strong class="text-blue-800">Ollama Configuration:</strong><br>
                • <strong>Server URL:</strong> Enter server URL (e.g., http://*************:11434) or IP only<br>
                • <strong>Model:</strong> Use "Fetch Models" to see available models<br>
                • <strong>Recommended:</strong> qwen-qwq-32b for CV analysis<br>
                • <strong>Note:</strong> Make sure Ollama server is running
            `;
            fetchBtn.disabled = false;
            testBtn.disabled = false;
            break;
        default:
            guidance = '<strong class="text-blue-800">Select a provider to see configuration guidance.</strong>';
            fetchBtn.disabled = true;
            testBtn.disabled = true;
    }
    
    guidanceDiv.innerHTML = guidance;
}

async function fetchModelsForProvider() {
    const provider = document.getElementById('providerSelect').value;
    const apiKey = document.getElementById('apiKey').value;
    
    if (!provider) {
        showNotification('Please select a provider first', 'warning');
        return;
    }
    
    if (!apiKey) {
        showNotification('Please enter API key or server URL first', 'warning');
        return;
    }
    
    try {
        showLoading('Fetching available models...');
        
        const response = await fetch('/api/ai-config/fetch-models/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                provider: provider,
                api_key: apiKey
            })
        });
        
        const data = await response.json();
        hideLoading();
        
        if (data.success && data.models) {
            showModelSelectionModal(data.models);
        } else {
            showNotification('Failed to fetch models: ' + (data.error || 'Unknown error'), 'error');
        }
        
    } catch (error) {
        hideLoading();
        showNotification('Error fetching models: ' + error.message, 'error');
    }
}

function showModelSelectionModal(models) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-semibold mb-4">Select Model</h3>
            <div class="max-h-60 overflow-y-auto space-y-2">
                ${models.map(model => `
                    <button class="w-full text-left p-2 hover:bg-gray-100 rounded" onclick="selectModel('${model}')">
                        ${model}
                    </button>
                `).join('')}
            </div>
            <div class="flex gap-2 mt-4">
                <button class="btn btn-secondary flex-1" onclick="this.closest('.fixed').remove()">Cancel</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

function selectModel(modelName) {
    document.getElementById('modelName').value = modelName;
    document.querySelector('.fixed.inset-0').remove();
}

// General settings management
async function loadGeneralSettings() {
    try {
        const response = await fetch('/api/ai-config/general/', {
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        });
        
        if (!response.ok) throw new Error('Failed to load general settings');
        
        const data = await response.json();
        currentGeneralSettings = data.settings || {};
        
        // Populate form
        document.getElementById('cacheTtl').value = currentGeneralSettings.cache_ttl || 86400;
        document.getElementById('batchSize').value = currentGeneralSettings.batch_size || 10;
        document.getElementById('maxRetries').value = currentGeneralSettings.max_retries || 3;
        document.getElementById('retryDelay').value = currentGeneralSettings.retry_delay || 1000;
        
    } catch (error) {
        showNotification('Error loading general settings: ' + error.message, 'error');
    }
}

async function saveGeneralSettings() {
    try {
        const formData = new FormData(document.getElementById('generalSettingsForm'));
        
        const response = await fetch('/api/ai-config/general/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: formData
        });
        
        if (!response.ok) throw new Error('Failed to save general settings');
        
        showNotification('General settings saved successfully!', 'success');
        loadGeneralSettings();
        
    } catch (error) {
        showNotification('Error saving general settings: ' + error.message, 'error');
    }
}

// Prompt management
async function loadPrompts() {
    try {
        const response = await fetch('/api/ai-config/prompts/', {
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        });
        
        if (!response.ok) throw new Error('Failed to load prompts');
        
        const data = await response.json();
        currentPrompts = data.prompts || [];
        renderPromptsList();
        
    } catch (error) {
        showNotification('Error loading prompts: ' + error.message, 'error');
    }
}

function renderPromptsList() {
    const container = document.getElementById('promptsList');
    
    if (currentPrompts.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-edit text-4xl mb-3 opacity-50"></i>
                <p>No AI prompts configured yet.</p>
                <p class="text-sm">Click "Add Prompt" to create your first prompt template.</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = currentPrompts.map(prompt => `
        <div class="provider-card">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <h5 class="font-semibold text-gray-900 mb-2">${prompt.name}</h5>
                    <p class="text-sm text-gray-600 mb-2">${prompt.instructions}</p>
                    <p class="text-xs text-gray-500 truncate">${prompt.prompt.substring(0, 100)}...</p>
                </div>
                <div class="flex gap-2">
                    <button class="btn btn-sm btn-secondary" onclick="editPrompt(${prompt.id})" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deletePrompt(${prompt.id})" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

function addNewPrompt() {
    resetPromptForm();
    document.getElementById('promptForm').classList.remove('hidden');
    document.getElementById('promptName').focus();
}

function editPrompt(promptId) {
    const prompt = currentPrompts.find(p => p.id === promptId);
    if (!prompt) return;
    
    // Populate form
    document.getElementById('promptId').value = prompt.id;
    document.getElementById('promptName').value = prompt.name;
    document.getElementById('promptTemplate').value = prompt.prompt;
    document.getElementById('promptInstructions').value = prompt.instructions;
    
    document.getElementById('promptForm').classList.remove('hidden');
    document.getElementById('promptName').focus();
}

async function savePrompt() {
    try {
        const formData = new FormData(document.getElementById('aiPromptForm'));
        const promptId = document.getElementById('promptId').value;
        
        const url = promptId ? 
            `/api/ai-config/prompts/${promptId}/` : 
            '/api/ai-config/prompts/';
        
        const method = promptId ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: formData
        });
        
        if (!response.ok) throw new Error('Failed to save prompt');
        
        showNotification('Prompt saved successfully!', 'success');
        cancelPromptForm();
        loadPrompts();
        
    } catch (error) {
        showNotification('Error saving prompt: ' + error.message, 'error');
    }
}

async function deletePrompt(promptId) {
    if (!confirm('Are you sure you want to delete this prompt?')) return;
    
    try {
        const response = await fetch(`/api/ai-config/prompts/${promptId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        });
        
        if (!response.ok) throw new Error('Failed to delete prompt');
        
        showNotification('Prompt deleted successfully!', 'success');
        loadPrompts();
        
    } catch (error) {
        showNotification('Error deleting prompt: ' + error.message, 'error');
    }
}

function cancelPromptForm() {
    document.getElementById('promptForm').classList.add('hidden');
    resetPromptForm();
}

function resetPromptForm() {
    document.getElementById('aiPromptForm').reset();
    document.getElementById('promptId').value = '';
}

// Utility functions
function resetAllForms() {
    resetProviderForm();
    resetPromptForm();
    cancelProviderForm();
    cancelPromptForm();
}

function showLoading(message = 'Loading...') {
    // Show loading indicator in popup
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'configLoading';
    loadingDiv.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60';
    loadingDiv.innerHTML = `
        <div class="bg-white rounded-lg p-6 text-center">
            <i class="fas fa-spinner fa-spin text-2xl text-blue-500 mb-2"></i>
            <p class="text-gray-700">${message}</p>
        </div>
    `;
    document.body.appendChild(loadingDiv);
}

function hideLoading() {
    const loading = document.getElementById('configLoading');
    if (loading) loading.remove();
}

function showNotification(message, type = 'info') {
    const alert = document.createElement('div');
    alert.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white max-w-sm ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
    }`;
    alert.innerHTML = `
        <div class="flex items-center gap-2">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation' : 'info'}-circle"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(alert);
    setTimeout(() => alert.remove(), 5000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners if needed
    console.log('AI Configuration popup initialized');
}); 