// Theme handling
document.addEventListener('DOMContentLoaded', function() {
    const themeToggleDarkIcon = document.getElementById('theme-toggle-dark-icon');
    const themeToggleLightIcon = document.getElementById('theme-toggle-light-icon');
    const themeToggleBtn = document.getElementById('theme-toggle');

    // Change the icons inside the button based on previous settings
    if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        if (themeToggleLightIcon) themeToggleLightIcon.classList.remove('hidden');
        document.documentElement.classList.add('dark');
    } else {
        if (themeToggleDarkIcon) themeToggleDarkIcon.classList.remove('hidden');
        document.documentElement.classList.remove('dark');
    }

    themeToggleBtn?.addEventListener('click', function() {
        // Toggle icons
        if (themeToggleDarkIcon) themeToggleDarkIcon.classList.toggle('hidden');
        if (themeToggleLightIcon) themeToggleLightIcon.classList.toggle('hidden');

        // If is set in localStorage
        if (localStorage.getItem('color-theme')) {
            if (localStorage.getItem('color-theme') === 'light') {
                document.documentElement.classList.add('dark');
                localStorage.setItem('color-theme', 'dark');
            } else {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('color-theme', 'light');
            }
        } else {
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('color-theme', 'light');
            } else {
                document.documentElement.classList.add('dark');
                localStorage.setItem('color-theme', 'dark');
            }
        }
    });
});

// Custom alert component
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.getElementById('alert-container');
    if (!alertContainer) {
        const container = document.createElement('div');
        container.id = 'alert-container';
        container.className = 'fixed top-4 right-4 z-50 space-y-4';
        document.body.appendChild(container);
    }

    const alert = document.createElement('div');
    alert.className = `alert alert-${type} fade-in`;
    alert.innerHTML = `
        <div class="flex items-center">
            <span class="mr-2">
                ${getAlertIcon(type)}
            </span>
            <span>${message}</span>
            <button class="ml-auto" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.getElementById('alert-container').appendChild(alert);

    if (duration > 0) {
        setTimeout(() => {
            alert.remove();
        }, duration);
    }
}

function getAlertIcon(type) {
    switch (type) {
        case 'success':
            return '<i class="fas fa-check-circle"></i>';
        case 'error':
            return '<i class="fas fa-exclamation-circle"></i>';
        case 'warning':
            return '<i class="fas fa-exclamation-triangle"></i>';
        default:
            return '<i class="fas fa-info-circle"></i>';
    }
}

// Custom tooltip initialization
function initTooltips() {
    const tooltips = document.querySelectorAll('[data-tooltip]');
    tooltips.forEach(element => {
        const tooltip = document.createElement('span');
        tooltip.className = 'tooltip-text';
        tooltip.textContent = element.getAttribute('data-tooltip');
        element.appendChild(tooltip);
    });
}

// Custom modal component
function showModal(title, content, onConfirm = null) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">${title}</h3>
                <button class="text-gray-500 hover:text-gray-700" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mb-6">${content}</div>
            <div class="flex justify-end space-x-2">
                <button class="btn-secondary" onclick="this.closest('.modal').remove()">Cancel</button>
                ${onConfirm ? `<button class="btn-primary" onclick="handleConfirm(this)">Confirm</button>` : ''}
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    if (onConfirm) {
        window.handleConfirm = function(button) {
            onConfirm();
            button.closest('.modal').remove();
        };
    }
}

// Enhanced Popup functionality
function openPopup(popupId) {
    console.log('Opening popup:', popupId);
    const popup = document.getElementById(popupId);
    if (popup) {
        console.log('Popup found:', popup);
        
        // Close any other open popups first
        const openPopups = document.querySelectorAll('.popup-overlay.show');
        openPopups.forEach(p => p.classList.remove('show'));
        
        // Show the popup with animation
        popup.classList.add('show');
        document.body.style.overflow = 'hidden';
        
        // Initialize any dynamic content
        initializePopupContent(popupId);
        
        // Add focus trap
        trapFocus(popup);
        
    } else {
        console.error('Popup not found:', popupId);
    }
}

function closePopup(popupId) {
    const popup = document.getElementById(popupId);
    if (popup) {
        popup.classList.remove('show');
        document.body.style.overflow = 'auto';
        
        // Clear any form data if needed
        clearPopupForms(popup);
        
        // Return focus to the button that opened the popup
        restoreFocus();
    }
}

// Initialize popup content based on type
function initializePopupContent(popupId) {
    switch (popupId) {
        case 'uploadPopup':
            initializeUploadPopup();
            break;
        case 'analyzePopup':
            initializeAnalyzePopup();
            break;
        case 'managePopup':
            initializeManagePopup();
            break;
        case 'reportsPopup':
            initializeReportsPopup();
            break;
    }
}

// Upload popup initialization
function initializeUploadPopup() {
    // Reset to single upload by default
    switchUploadMethod('single');
    
    // Setup drag and drop
    setupAdvancedFileUpload();
}

// Analyze popup initialization
function initializeAnalyzePopup() {
    // Reset analysis type selection
    document.querySelectorAll('.analysis-type').forEach(el => {
        el.style.display = 'none';
    });
    document.querySelectorAll('.option-card').forEach(card => {
        card.classList.remove('selected');
    });
}

// Manage popup initialization
function initializeManagePopup() {
    // Switch to companies tab by default
    if (typeof switchManageTab === 'function') {
        switchManageTab('companies');
    }
}

// Reports popup initialization
function initializeReportsPopup() {
    // Initialize any charts or data
    console.log('Reports popup initialized');
}

// Clear popup forms
function clearPopupForms(popup) {
    const forms = popup.querySelectorAll('form');
    forms.forEach(form => {
        form.reset();
        
        // Clear file inputs specifically
        const fileInputs = form.querySelectorAll('input[type="file"]');
        fileInputs.forEach(input => input.value = '');
        
        // Hide file info displays
        const fileInfos = form.querySelectorAll('.file-info, .files-info');
        fileInfos.forEach(info => info.style.display = 'none');
        
        // Disable submit buttons
        const submitBtns = form.querySelectorAll('button[type="submit"]');
        submitBtns.forEach(btn => btn.disabled = true);
    });
}

// Enhanced file upload with better feedback
function setupAdvancedFileUpload() {
    const singleDropzone = document.getElementById('singleDropzone');
    const batchDropzone = document.getElementById('batchDropzone');
    
    if (singleDropzone) {
        setupDropzone(singleDropzone, false);
    }
    
    if (batchDropzone) {
        setupDropzone(batchDropzone, true);
    }
}

function setupDropzone(dropzone, isMultiple) {
    const input = dropzone.querySelector('input[type="file"]');
    if (!input) return;
    
    dropzone.addEventListener('click', () => input.click());
    
    dropzone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropzone.style.borderColor = '#667eea';
        dropzone.style.background = 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)';
    });
    
    dropzone.addEventListener('dragleave', () => {
        dropzone.style.borderColor = '#cbd5e1';
        dropzone.style.background = '#f8fafc';
    });
    
    dropzone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropzone.style.borderColor = '#cbd5e1';
        dropzone.style.background = '#f8fafc';
        
        if (e.dataTransfer.files.length > 0) {
            input.files = e.dataTransfer.files;
            handleAdvancedFileSelection(input, isMultiple);
        }
    });
    
    input.addEventListener('change', () => handleAdvancedFileSelection(input, isMultiple));
}

function handleAdvancedFileSelection(input, isMultiple) {
    const files = Array.from(input.files);
    if (files.length === 0) return;
    
    const uploadMethod = input.closest('.upload-method');
    const fileInfo = uploadMethod.querySelector('.file-info, .files-info');
    const submitBtn = uploadMethod.querySelector('button[type="submit"]');
    
    if (!isMultiple && files.length > 0) {
        // Single file
        const file = files[0];
        const fileName = fileInfo.querySelector('.file-name');
        if (fileName) fileName.textContent = file.name;
        
        // Show file size
        const fileSize = formatFileSize(file.size);
        const fileSizeEl = fileInfo.querySelector('.file-size');
        if (fileSizeEl) fileSizeEl.textContent = fileSize;
        
    } else if (isMultiple) {
        // Multiple files
        const filesList = fileInfo.querySelector('.files-list');
        if (filesList) {
            filesList.innerHTML = files.map(file => `
                <div class="file-item flex justify-between items-center py-2 px-3 bg-gray-50 rounded mb-2">
                    <span class="file-name">${file.name}</span>
                    <span class="file-size text-sm text-gray-500">${formatFileSize(file.size)}</span>
                </div>
            `).join('');
        }
    }
    
    if (fileInfo) fileInfo.style.display = 'block';
    if (submitBtn) submitBtn.disabled = false;
    
    // Validate files
    validateFiles(files);
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function validateFiles(files) {
    const allowedTypes = ['.pdf', '.doc', '.docx'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    files.forEach(file => {
        const extension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(extension)) {
            showNotification(`File "${file.name}" has unsupported format. Please use PDF, DOC, or DOCX.`, 'error');
            return false;
        }
        
        if (file.size > maxSize) {
            showNotification(`File "${file.name}" is too large. Maximum size is 10MB.`, 'error');
            return false;
        }
    });
    
    return true;
}

// Focus trap for accessibility
function trapFocus(popup) {
    const focusableElements = popup.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    if (focusableElements.length === 0) return;
    
    const firstFocusable = focusableElements[0];
    const lastFocusable = focusableElements[focusableElements.length - 1];
    
    firstFocusable.focus();
    
    popup.addEventListener('keydown', (e) => {
        if (e.key === 'Tab') {
            if (e.shiftKey) {
                if (document.activeElement === firstFocusable) {
                    e.preventDefault();
                    lastFocusable.focus();
                }
            } else {
                if (document.activeElement === lastFocusable) {
                    e.preventDefault();
                    firstFocusable.focus();
                }
            }
        }
    });
}

// Store the element that opened the popup for focus restoration
let lastFocusedElement = null;

// Enhanced open popup to store focus
function openPopupWithFocus(popupId, triggerElement) {
    lastFocusedElement = triggerElement;
    openPopup(popupId);
}

function restoreFocus() {
    if (lastFocusedElement) {
        lastFocusedElement.focus();
        lastFocusedElement = null;
    }
}

// Notification system
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 max-w-sm p-4 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 ${getNotificationClasses(type)}`;
    
    notification.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0">
                ${getNotificationIcon(type)}
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <button class="ml-4 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove
    if (duration > 0) {
        setTimeout(() => {
            notification.style.transform = 'translateX(full)';
            setTimeout(() => notification.remove(), 300);
        }, duration);
    }
}

function getNotificationClasses(type) {
    const classes = {
        success: 'bg-green-100 border border-green-200 text-green-800',
        error: 'bg-red-100 border border-red-200 text-red-800',
        warning: 'bg-yellow-100 border border-yellow-200 text-yellow-800',
        info: 'bg-blue-100 border border-blue-200 text-blue-800'
    };
    return classes[type] || classes.info;
}

function getNotificationIcon(type) {
    const icons = {
        success: '<i class="fas fa-check-circle text-green-500"></i>',
        error: '<i class="fas fa-exclamation-circle text-red-500"></i>',
        warning: '<i class="fas fa-exclamation-triangle text-yellow-500"></i>',
        info: '<i class="fas fa-info-circle text-blue-500"></i>'
    };
    return icons[type] || icons.info;
}

// Tab switching for popups
function switchUploadMethod(method) {
    // Hide all upload methods
    document.querySelectorAll('.upload-method').forEach(el => {
        el.style.display = 'none';
        el.classList.remove('active');
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.upload-methods .tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected method
    const targetMethod = document.getElementById(method + '-upload');
    if (targetMethod) {
        targetMethod.style.display = 'block';
        targetMethod.classList.add('active');
    }
    
    // Add active class to clicked tab
    if (event && event.target) {
        event.target.classList.add('active');
    }
}

// Analysis type selection
function selectAnalysisType(type) {
    // Hide all analysis types
    document.querySelectorAll('.analysis-type').forEach(el => {
        el.style.display = 'none';
    });
    
    // Remove selected class from all option cards
    document.querySelectorAll('.option-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Show selected analysis type
    const targetType = document.getElementById(type + '-analysis');
    if (targetType) {
        targetType.style.display = 'block';
    }
    
    // Add selected class to clicked card
    if (event && event.target) {
        const card = event.target.closest('.option-card');
        if (card) {
            card.classList.add('selected');
        }
    }
}

// File upload handling
function setupFileUpload() {
    // Single file upload
    const singleDropzone = document.getElementById('singleDropzone');
    const singleInput = singleDropzone?.querySelector('input[type="file"]');
    
    if (singleDropzone && singleInput) {
        singleDropzone.addEventListener('click', () => singleInput.click());
        singleDropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            singleDropzone.classList.add('border-blue-500', 'bg-blue-50');
        });
        singleDropzone.addEventListener('dragleave', () => {
            singleDropzone.classList.remove('border-blue-500', 'bg-blue-50');
        });
        singleDropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            singleDropzone.classList.remove('border-blue-500', 'bg-blue-50');
            if (e.dataTransfer.files.length > 0) {
                singleInput.files = e.dataTransfer.files;
                handleFileSelection(singleInput);
            }
        });
        singleInput.addEventListener('change', () => handleFileSelection(singleInput));
    }
}

function handleFileSelection(input) {
    const file = input.files[0];
    if (file) {
        const fileInfo = input.closest('.upload-method').querySelector('.file-info');
        const fileName = fileInfo?.querySelector('.file-name');
        const submitBtn = input.closest('form').querySelector('button[type="submit"]');
        
        if (fileName) fileName.textContent = file.name;
        if (fileInfo) fileInfo.style.display = 'block';
        if (submitBtn) submitBtn.disabled = false;
    }
}

function clearFile() {
    const activeUpload = document.querySelector('.upload-method.active');
    const input = activeUpload?.querySelector('input[type="file"]');
    const fileInfo = activeUpload?.querySelector('.file-info');
    const submitBtn = activeUpload?.querySelector('button[type="submit"]');
    
    if (input) input.value = '';
    if (fileInfo) fileInfo.style.display = 'none';
    if (submitBtn) submitBtn.disabled = true;
}

// Global popup event listeners
function initPopupListeners() {
    // Close popup when clicking outside
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('popup-overlay')) {
            const popupId = event.target.id;
            if (popupId) {
                closePopup(popupId);
            }
        }
        
        // Close popup when clicking close button
        if (event.target.classList.contains('popup-close') || 
            event.target.closest('.popup-close')) {
            const popup = event.target.closest('.popup-overlay');
            if (popup) {
                closePopup(popup.id);
            }
        }
    });

    // Escape key to close popups
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const visiblePopups = document.querySelectorAll('.popup-overlay.show');
            visiblePopups.forEach(popup => {
                closePopup(popup.id);
            });
        }
    });
}

// Initialize components
document.addEventListener('DOMContentLoaded', function() {
    initTooltips();
    setupFileUpload();
    initPopupListeners();
    
    console.log('CV Analyzer popups initialized successfully');
}); 