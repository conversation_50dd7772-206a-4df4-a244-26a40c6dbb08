"""
Task Queue Optimization and Celery Enhancement Module
Handles priority queues, monitoring, retry strategies, and distributed execution
"""

import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass
from celery import Celery, Task, current_task
from celery.signals import task_prerun, task_postrun, task_failure, task_success
from celery.exceptions import Retry, WorkerLostError
from celery.utils.log import get_task_logger
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
import redis
import psutil

logger = get_task_logger(__name__)

class TaskPriority(Enum):
    """Task priority levels"""
    CRITICAL = 10
    HIGH = 7
    NORMAL = 5
    LOW = 3
    BACKGROUND = 1

class TaskStatus(Enum):
    """Task status enumeration"""
    PENDING = "pending"
    STARTED = "started"
    SUCCESS = "success"
    FAILURE = "failure"
    RETRY = "retry"
    REVOKED = "revoked"

@dataclass
class TaskMetrics:
    """Task execution metrics"""
    task_id: str
    task_name: str
    priority: TaskPriority
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    status: TaskStatus = TaskStatus.PENDING
    worker_id: Optional[str] = None
    retries: int = 0
    max_retries: int = 3
    error_message: Optional[str] = None

class TaskQueueMonitor:
    """Monitors task queue performance and health"""
    
    def __init__(self):
        self.redis_client = redis.Redis.from_url(settings.CELERY_BROKER_URL)
        self.metrics_key = "task_queue_metrics"
        self.metrics_retention = timedelta(days=7)
    
    def record_task_metrics(self, metrics: TaskMetrics):
        """Record task execution metrics"""
        metric_data = {
            'task_id': metrics.task_id,
            'task_name': metrics.task_name,
            'priority': metrics.priority.value,
            'start_time': metrics.start_time.isoformat(),
            'end_time': metrics.end_time.isoformat() if metrics.end_time else None,
            'duration': metrics.duration,
            'status': metrics.status.value,
            'worker_id': metrics.worker_id,
            'retries': metrics.retries,
            'error_message': metrics.error_message
        }
        
        # Store in Redis with expiration
        key = f"{self.metrics_key}:{metrics.task_id}"
        self.redis_client.setex(
            key, 
            int(self.metrics_retention.total_seconds()), 
            json.dumps(metric_data)
        )
        
        # Add to metrics list for aggregation
        list_key = f"{self.metrics_key}:list"
        self.redis_client.lpush(list_key, json.dumps(metric_data))
        self.redis_client.ltrim(list_key, 0, 10000)  # Keep last 10k entries
    
    def get_queue_statistics(self) -> Dict[str, Any]:
        """Get comprehensive queue statistics"""
        try:
            # Get Celery inspect API
            from celery import current_app
            inspect = current_app.control.inspect()
            
            # Active tasks
            active_tasks = inspect.active() or {}
            scheduled_tasks = inspect.scheduled() or {}
            reserved_tasks = inspect.reserved() or {}
            
            # Queue lengths
            queue_lengths = {}
            for queue_name in ['high_priority', 'normal', 'low_priority', 'background']:
                try:
                    queue_length = self.redis_client.llen(f"celery:queue:{queue_name}")
                    queue_lengths[queue_name] = queue_length
                except Exception:
                    queue_lengths[queue_name] = 0
            
            # Worker statistics
            worker_stats = {}
            for worker, tasks in active_tasks.items():
                worker_stats[worker] = {
                    'active_tasks': len(tasks),
                    'task_types': {}
                }
                
                for task in tasks:
                    task_name = task.get('name', 'unknown')
                    worker_stats[worker]['task_types'][task_name] = \
                        worker_stats[worker]['task_types'].get(task_name, 0) + 1
            
            # Recent performance metrics
            performance_metrics = self._get_recent_performance_metrics()
            
            return {
                'timestamp': timezone.now().isoformat(),
                'queue_lengths': queue_lengths,
                'active_tasks_count': sum(len(tasks) for tasks in active_tasks.values()),
                'scheduled_tasks_count': sum(len(tasks) for tasks in scheduled_tasks.values()),
                'worker_count': len(active_tasks),
                'worker_statistics': worker_stats,
                'performance_metrics': performance_metrics,
                'system_load': {
                    'cpu_percent': psutil.cpu_percent(),
                    'memory_percent': psutil.virtual_memory().percent,
                    'disk_usage_percent': psutil.disk_usage('/').percent
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting queue statistics: {e}")
            return {
                'error': str(e),
                'timestamp': timezone.now().isoformat()
            }
    
    def _get_recent_performance_metrics(self) -> Dict[str, Any]:
        """Get recent performance metrics from stored data"""
        try:
            # Get recent metrics from Redis list
            list_key = f"{self.metrics_key}:list"
            recent_entries = self.redis_client.lrange(list_key, 0, 1000)
            
            if not recent_entries:
                return {}
            
            metrics = []
            cutoff_time = timezone.now() - timedelta(hours=24)
            
            for entry in recent_entries:
                try:
                    data = json.loads(entry)
                    start_time = datetime.fromisoformat(data['start_time'].replace('Z', '+00:00'))
                    
                    if start_time >= cutoff_time:
                        metrics.append(data)
                except (json.JSONDecodeError, ValueError, KeyError):
                    continue
            
            if not metrics:
                return {}
            
            # Calculate statistics
            durations = [m['duration'] for m in metrics if m.get('duration')]
            successful_tasks = [m for m in metrics if m['status'] == 'success']
            failed_tasks = [m for m in metrics if m['status'] == 'failure']
            
            # Task type distribution
            task_types = {}
            for metric in metrics:
                task_name = metric['task_name']
                task_types[task_name] = task_types.get(task_name, 0) + 1
            
            return {
                'total_tasks_24h': len(metrics),
                'successful_tasks': len(successful_tasks),
                'failed_tasks': len(failed_tasks),
                'success_rate': (len(successful_tasks) / len(metrics)) * 100 if metrics else 0,
                'average_duration': sum(durations) / len(durations) if durations else 0,
                'max_duration': max(durations) if durations else 0,
                'min_duration': min(durations) if durations else 0,
                'task_type_distribution': task_types
            }
            
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {}
    
    def get_task_performance_analysis(self) -> Dict[str, Any]:
        """Analyze task performance and provide recommendations"""
        stats = self.get_queue_statistics()
        performance = stats.get('performance_metrics', {})
        
        analysis = {
            'timestamp': timezone.now().isoformat(),
            'performance_score': 100,  # Start with perfect score
            'issues': [],
            'recommendations': [],
            'queue_health': 'healthy'
        }
        
        # Analyze success rate
        success_rate = performance.get('success_rate', 100)
        if success_rate < 95:
            analysis['issues'].append(f"Low success rate: {success_rate:.1f}%")
            analysis['recommendations'].append("Review failed tasks and improve error handling")
            analysis['performance_score'] -= 20
            analysis['queue_health'] = 'degraded'
        
        # Analyze average duration
        avg_duration = performance.get('average_duration', 0)
        if avg_duration > 60:  # 60 seconds
            analysis['issues'].append(f"High average duration: {avg_duration:.1f}s")
            analysis['recommendations'].append("Optimize long-running tasks or split them")
            analysis['performance_score'] -= 15
        
        # Analyze queue lengths
        queue_lengths = stats.get('queue_lengths', {})
        total_queued = sum(queue_lengths.values())
        if total_queued > 100:
            analysis['issues'].append(f"High queue backlog: {total_queued} tasks")
            analysis['recommendations'].append("Consider adding more workers or optimizing tasks")
            analysis['performance_score'] -= 10
            analysis['queue_health'] = 'overloaded'
        
        # Analyze system load
        system_load = stats.get('system_load', {})
        cpu_percent = system_load.get('cpu_percent', 0)
        memory_percent = system_load.get('memory_percent', 0)
        
        if cpu_percent > 80:
            analysis['issues'].append(f"High CPU usage: {cpu_percent:.1f}%")
            analysis['recommendations'].append("Scale workers or optimize CPU-intensive tasks")
            analysis['performance_score'] -= 15
        
        if memory_percent > 85:
            analysis['issues'].append(f"High memory usage: {memory_percent:.1f}%")
            analysis['recommendations'].append("Optimize memory usage in tasks or add more RAM")
            analysis['performance_score'] -= 15
        
        # Final health assessment
        if analysis['performance_score'] < 60:
            analysis['queue_health'] = 'critical'
        elif analysis['performance_score'] < 80:
            analysis['queue_health'] = 'warning'
        
        return analysis

class PriorityTaskRouter:
    """Routes tasks to appropriate priority queues"""
    
    def __init__(self):
        self.queue_mapping = {
            TaskPriority.CRITICAL: 'critical',
            TaskPriority.HIGH: 'high_priority',
            TaskPriority.NORMAL: 'normal',
            TaskPriority.LOW: 'low_priority',
            TaskPriority.BACKGROUND: 'background'
        }
    
    def route_task(self, task_name: str, priority: TaskPriority = TaskPriority.NORMAL) -> str:
        """Route task to appropriate queue based on priority"""
        return self.queue_mapping.get(priority, 'normal')
    
    def get_routing_info(self) -> Dict[str, Any]:
        """Get routing configuration information"""
        return {
            'queue_mapping': {p.name: q for p, q in self.queue_mapping.items()},
            'available_queues': list(self.queue_mapping.values()),
            'default_queue': 'normal'
        }

class RetryStrategy:
    """Advanced retry strategies for failed tasks"""
    
    def __init__(self):
        self.retry_delays = [1, 5, 15, 60, 300]  # Exponential backoff
        self.max_retries = 5
    
    def get_retry_delay(self, retry_count: int, task_name: str = None) -> int:
        """Get retry delay based on retry count and task type"""
        if retry_count >= len(self.retry_delays):
            return self.retry_delays[-1]
        
        base_delay = self.retry_delays[retry_count]
        
        # Task-specific modifications
        if task_name:
            if 'ai_analysis' in task_name.lower():
                # AI tasks might need longer delays due to rate limits
                return base_delay * 2
            elif 'email' in task_name.lower():
                # Email tasks can retry quickly
                return max(base_delay // 2, 1)
        
        return base_delay
    
    def should_retry(self, exc: Exception, retry_count: int, task_name: str = None) -> bool:
        """Determine if task should be retried"""
        if retry_count >= self.max_retries:
            return False
        
        # Don't retry certain types of errors
        if isinstance(exc, (ValueError, TypeError)):
            return False
        
        # Retry network-related errors
        if 'network' in str(exc).lower() or 'timeout' in str(exc).lower():
            return True
        
        # Retry rate limit errors
        if 'rate limit' in str(exc).lower() or 'quota' in str(exc).lower():
            return True
        
        # Default retry behavior
        return True

class BaseOptimizedTask(Task):
    """Base task class with optimization features"""
    
    def __init__(self):
        super().__init__()
        self.monitor = TaskQueueMonitor()
        self.retry_strategy = RetryStrategy()
        self.metrics = None
    
    def before_start(self, task_id, args, kwargs):
        """Called before task execution"""
        self.metrics = TaskMetrics(
            task_id=task_id,
            task_name=self.name,
            priority=TaskPriority.NORMAL,  # Default, can be overridden
            start_time=timezone.now()
        )
        
        logger.info(f"Starting task {self.name} with ID {task_id}")
    
    def on_success(self, retval, task_id, args, kwargs):
        """Called on successful task completion"""
        if self.metrics:
            self.metrics.end_time = timezone.now()
            self.metrics.duration = (self.metrics.end_time - self.metrics.start_time).total_seconds()
            self.metrics.status = TaskStatus.SUCCESS
            self.monitor.record_task_metrics(self.metrics)
        
        logger.info(f"Task {self.name} completed successfully in {self.metrics.duration:.2f}s")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Called on task failure"""
        if self.metrics:
            self.metrics.end_time = timezone.now()
            self.metrics.duration = (self.metrics.end_time - self.metrics.start_time).total_seconds()
            self.metrics.status = TaskStatus.FAILURE
            self.metrics.error_message = str(exc)
            self.monitor.record_task_metrics(self.metrics)
        
        logger.error(f"Task {self.name} failed: {exc}")
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """Called on task retry"""
        if self.metrics:
            self.metrics.retries += 1
            self.metrics.status = TaskStatus.RETRY
            self.metrics.error_message = str(exc)
            self.monitor.record_task_metrics(self.metrics)
        
        logger.warning(f"Task {self.name} retry {self.metrics.retries}/{self.metrics.max_retries}: {exc}")
    
    def apply_async_with_priority(self, args=None, kwargs=None, priority=TaskPriority.NORMAL, **options):
        """Apply task async with priority routing"""
        router = PriorityTaskRouter()
        queue = router.route_task(self.name, priority)
        
        return self.apply_async(
            args=args,
            kwargs=kwargs,
            queue=queue,
            priority=priority.value,
            **options
        )

class TaskPerformanceProfiler:
    """Profiles task performance and provides optimization insights"""
    
    def __init__(self):
        self.monitor = TaskQueueMonitor()
    
    def profile_task_performance(self, task_name: str = None, 
                               time_range: timedelta = timedelta(hours=24)) -> Dict[str, Any]:
        """Profile performance for specific task or all tasks"""
        try:
            # Get recent metrics
            list_key = f"{self.monitor.metrics_key}:list"
            recent_entries = self.monitor.redis_client.lrange(list_key, 0, 5000)
            
            if not recent_entries:
                return {'error': 'No metrics data available'}
            
            cutoff_time = timezone.now() - time_range
            filtered_metrics = []
            
            for entry in recent_entries:
                try:
                    data = json.loads(entry)
                    start_time = datetime.fromisoformat(data['start_time'].replace('Z', '+00:00'))
                    
                    if start_time >= cutoff_time:
                        if task_name is None or data['task_name'] == task_name:
                            filtered_metrics.append(data)
                except (json.JSONDecodeError, ValueError, KeyError):
                    continue
            
            if not filtered_metrics:
                return {'error': f'No metrics found for task: {task_name}'}
            
            # Analyze performance
            durations = [m['duration'] for m in filtered_metrics if m.get('duration')]
            successful = [m for m in filtered_metrics if m['status'] == 'success']
            failed = [m for m in filtered_metrics if m['status'] == 'failure']
            
            # Performance percentiles
            if durations:
                durations.sort()
                p50 = durations[len(durations) // 2]
                p95 = durations[int(len(durations) * 0.95)]
                p99 = durations[int(len(durations) * 0.99)]
            else:
                p50 = p95 = p99 = 0
            
            # Error analysis
            error_types = {}
            for failed_task in failed:
                error_msg = failed_task.get('error_message', 'Unknown error')
                error_type = error_msg.split(':')[0] if ':' in error_msg else error_msg
                error_types[error_type] = error_types.get(error_type, 0) + 1
            
            # Worker distribution
            worker_distribution = {}
            for metric in filtered_metrics:
                worker = metric.get('worker_id', 'Unknown')
                worker_distribution[worker] = worker_distribution.get(worker, 0) + 1
            
            return {
                'task_name': task_name or 'All tasks',
                'time_range_hours': time_range.total_seconds() / 3600,
                'total_executions': len(filtered_metrics),
                'successful_executions': len(successful),
                'failed_executions': len(failed),
                'success_rate': (len(successful) / len(filtered_metrics)) * 100,
                'performance': {
                    'average_duration': sum(durations) / len(durations) if durations else 0,
                    'median_duration': p50,
                    'p95_duration': p95,
                    'p99_duration': p99,
                    'min_duration': min(durations) if durations else 0,
                    'max_duration': max(durations) if durations else 0
                },
                'error_analysis': error_types,
                'worker_distribution': worker_distribution,
                'optimization_recommendations': self._get_optimization_recommendations(
                    filtered_metrics, durations
                )
            }
            
        except Exception as e:
            logger.error(f"Error profiling task performance: {e}")
            return {'error': str(e)}
    
    def _get_optimization_recommendations(self, metrics: List[Dict], 
                                        durations: List[float]) -> List[str]:
        """Generate optimization recommendations based on metrics"""
        recommendations = []
        
        if not metrics:
            return recommendations
        
        # Duration analysis
        if durations:
            avg_duration = sum(durations) / len(durations)
            max_duration = max(durations)
            
            if avg_duration > 30:  # 30 seconds
                recommendations.append(
                    f"High average duration ({avg_duration:.1f}s). Consider task optimization or chunking."
                )
            
            if max_duration > 300:  # 5 minutes
                recommendations.append(
                    f"Very long max duration ({max_duration:.1f}s). Implement timeouts and progress tracking."
                )
        
        # Failure rate analysis
        failed_count = len([m for m in metrics if m['status'] == 'failure'])
        failure_rate = (failed_count / len(metrics)) * 100
        
        if failure_rate > 10:
            recommendations.append(
                f"High failure rate ({failure_rate:.1f}%). Review error handling and retry logic."
            )
        
        # Retry analysis
        high_retry_tasks = [m for m in metrics if m.get('retries', 0) > 2]
        if len(high_retry_tasks) > len(metrics) * 0.1:  # More than 10% need multiple retries
            recommendations.append(
                "Many tasks require multiple retries. Review task reliability and external dependencies."
            )
        
        return recommendations

# Global instances
task_monitor = TaskQueueMonitor()
priority_router = PriorityTaskRouter()
retry_strategy = RetryStrategy()
performance_profiler = TaskPerformanceProfiler()

# Utility functions
def get_queue_health() -> Dict[str, Any]:
    """Get task queue health status"""
    return task_monitor.get_task_performance_analysis()

def get_queue_statistics() -> Dict[str, Any]:
    """Get comprehensive queue statistics"""
    return task_monitor.get_queue_statistics()

def profile_task(task_name: str = None, hours: int = 24) -> Dict[str, Any]:
    """Profile specific task performance"""
    return performance_profiler.profile_task_performance(
        task_name, timedelta(hours=hours)
    )

def route_task_by_priority(task_name: str, priority: TaskPriority = TaskPriority.NORMAL) -> str:
    """Route task to appropriate priority queue"""
    return priority_router.route_task(task_name, priority)

def should_retry_task(exc: Exception, retry_count: int, task_name: str = None) -> bool:
    """Determine if task should be retried"""
    return retry_strategy.should_retry(exc, retry_count, task_name)

def get_retry_delay(retry_count: int, task_name: str = None) -> int:
    """Get appropriate retry delay"""
    return retry_strategy.get_retry_delay(retry_count, task_name) 