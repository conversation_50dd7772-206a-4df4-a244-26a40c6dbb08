from celery import shared_task
from django.conf import settings
from .models import CVAnalysis, AIAPIConfig, ComparisonAnalysis
import json
import os
import logging
import asyncio
from datetime import datetime, timedelta
from django.core.files.storage import default_storage
from django.utils import timezone
from .ai.service import CVAnalysisService
from .text_extraction.extractor import CVTextExtractor

logger = logging.getLogger(__name__)

@shared_task(
    bind=True,
    max_retries=3,
    default_retry_delay=60,
    rate_limit='10/m'
)
def analyze_cv(self, analysis_id: int) -> None:
    """
    Analyze a CV using AI providers.
    This task:
    1. Extracts text and metadata from the CV file
    2. Gets analysis from AI providers
    3. Updates the analysis record with results
    """
    try:
        # Get the analysis object
        analysis = CVAnalysis.objects.select_related('vacancy', 'vacancy__company').get(id=analysis_id)
        
        # Initialize extractors and services
        extractor = CVTextExtractor()
        service = CVAnalysisService()
        
        try:
            # Extract text and metadata
            file_path = os.path.join(settings.MEDIA_ROOT, analysis.cv_file.name)
            text, metadata = extractor.extract_from_file(file_path)
            
            # Store metadata
            analysis.metadata = json.dumps(asdict(metadata))
            analysis.save(update_fields=['metadata'])
            
            if not text:
                analysis.status = 'failed'
                analysis.error_message = "Could not extract text from CV"
                analysis.save()
                return
            
            # Extract additional metadata
            additional_metadata = extractor.extract_metadata(text)
            analysis.metadata = json.dumps({
                **json.loads(analysis.metadata),
                'entities': additional_metadata['entities'],
                'key_phrases': additional_metadata['key_phrases']
            })
            analysis.save(update_fields=['metadata'])
            
            # Get section-specific content
            experience = extractor.get_section_content(text, 'experience') or ""
            education = extractor.get_section_content(text, 'education') or ""
            skills = extractor.get_section_content(text, 'skills') or ""
            
            # Prepare vacancy details
            vacancy_details = {
                'position': analysis.vacancy.title,
                'company': analysis.vacancy.company.name,
                'requirements': analysis.vacancy.requirements
            }
            
            # Run AI analysis
            loop = asyncio.get_event_loop()
            result = loop.run_until_complete(
                service.analyze_cv(
                    cv_text=text,
                    position=vacancy_details['position'],
                    company=vacancy_details['company'],
                    requirements=vacancy_details['requirements']
                )
            )
            
            if not result:
                analysis.status = 'failed'
                analysis.error_message = "AI analysis failed"
                analysis.save()
                return
            
            # Update analysis with results
            analysis.overall_score = result.get('overall_score', 0)
            analysis.content_score = result.get('content_score', 0)
            analysis.format_score = result.get('format_score', 0)
            analysis.skills_score = result.get('skills_score', 0)
            analysis.experience_score = result.get('experience_score', 0)
            
            # Store detailed analysis data
            analysis.skills_data = json.dumps(result.get('skills_analysis', []))
            analysis.experience_data = json.dumps(result.get('experience_analysis', []))
            analysis.education_data = json.dumps(result.get('education', []))
            analysis.certifications_data = json.dumps(result.get('certifications', []))
            analysis.recommendations_data = json.dumps(result.get('recommendations', []))
            
            analysis.status = 'completed'
            analysis.completed_at = timezone.now()
            analysis.save()
            
            # Clean up temporary files if using cloud storage
            if settings.DEFAULT_FILE_STORAGE != 'django.core.files.storage.FileSystemStorage':
                default_storage.delete(file_path)
            
        except Exception as e:
            logger.error(f"Error in CV analysis task: {e}")
            analysis.status = 'failed'
            analysis.error_message = str(e)
            analysis.save()
            
            # Retry the task if appropriate
            if self.request.retries < self.max_retries:
                raise self.retry(exc=e)
            raise
            
    except Exception as e:
        logger.error(f"Error in CV analysis task: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(exc=e)
        raise

@shared_task(
    bind=True,
    max_retries=3,
    default_retry_delay=60,
    rate_limit='5/m'
)
def compare_cvs(self, cv1_id: int, cv2_id: int) -> None:
    """Compare two CVs using AI providers"""
    try:
        # Get the CV analysis objects
        cv1 = CVAnalysis.objects.select_related('vacancy').get(id=cv1_id)
        cv2 = CVAnalysis.objects.select_related('vacancy').get(id=cv2_id)
        
        # Initialize extractor
        extractor = CVTextExtractor()
        
        # Extract text from both CVs
        cv1_text, cv1_metadata = extractor.extract_from_file(cv1.cv_file.path)
        cv2_text, cv2_metadata = extractor.extract_from_file(cv2.cv_file.path)
        
        # Initialize AI service
        service = CVAnalysisService()
        
        # Run comparison
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(
            service.compare_cvs(
                cv1_text=cv1_text,
                cv2_text=cv2_text,
                position=cv1.vacancy.title,
                requirements=cv1.vacancy.requirements
            )
        )
        
        # Create comparison record
        comparison = ComparisonAnalysis.objects.create(
            cv1=cv1,
            cv2=cv2,
            vacancy=cv1.vacancy,
            comparison_data=json.dumps(result),
            cv1_metadata=json.dumps(asdict(cv1_metadata)),
            cv2_metadata=json.dumps(asdict(cv2_metadata)),
            status='completed',
            completed_at=timezone.now()
        )
        
        return comparison.id
        
    except Exception as e:
        logger.error(f"Error in CV comparison task: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(exc=e)
        raise

@shared_task
def analyze_cvs_batch(analysis_ids: list[int]) -> None:
    """Analyze multiple CVs in parallel"""
    try:
        # Initialize services
        extractor = CVTextExtractor()
        service = CVAnalysisService()
        
        # Get all analyses
        analyses = CVAnalysis.objects.select_related('vacancy', 'vacancy__company').filter(id__in=analysis_ids)
        
        # Extract text from all CVs
        cv_data = []
        for analysis in analyses:
            try:
                text, metadata = extractor.extract_from_file(analysis.cv_file.path)
                cv_data.append({
                    'analysis': analysis,
                    'text': text,
                    'metadata': metadata
                })
                
                # Store metadata
                analysis.metadata = json.dumps(asdict(metadata))
                analysis.save(update_fields=['metadata'])
                
            except Exception as e:
                logger.error(f"Error extracting text from CV {analysis.id}: {e}")
                analysis.status = 'failed'
                analysis.error_message = f"Text extraction failed: {str(e)}"
                analysis.save()
        
        if not cv_data:
            return
        
        # Run batch analysis
        loop = asyncio.get_event_loop()
        results = loop.run_until_complete(
            service.analyze_cvs_batch(
                cvs=[{'text': data['text']} for data in cv_data],
                position=cv_data[0]['analysis'].vacancy.title,
                company=cv_data[0]['analysis'].vacancy.company.name,
                requirements=cv_data[0]['analysis'].vacancy.requirements
            )
        )
        
        # Update analyses with results
        for data, result in zip(cv_data, results):
            analysis = data['analysis']
            
            if isinstance(result, dict) and 'error' not in result:
                analysis.overall_score = result.get('overall_score', 0)
                analysis.content_score = result.get('content_score', 0)
                analysis.format_score = result.get('format_score', 0)
                analysis.skills_score = result.get('skills_score', 0)
                analysis.experience_score = result.get('experience_score', 0)
                
                analysis.skills_data = json.dumps(result.get('skills_analysis', []))
                analysis.experience_data = json.dumps(result.get('experience_analysis', []))
                analysis.education_data = json.dumps(result.get('education', []))
                analysis.certifications_data = json.dumps(result.get('certifications', []))
                analysis.recommendations_data = json.dumps(result.get('recommendations', []))
                
                analysis.status = 'completed'
                analysis.completed_at = timezone.now()
            else:
                analysis.status = 'failed'
                analysis.error_message = result.get('error', 'Analysis failed')
            
            analysis.save()
        
    except Exception as e:
        logger.error(f"Error in batch analysis task: {e}")
        raise

@shared_task
def cleanup_old_analyses():
    """Clean up old analyses and their files"""
    try:
        # Get analyses older than 30 days
        threshold = timezone.now() - timedelta(days=30)
        old_analyses = CVAnalysis.objects.filter(
            created_at__lt=threshold,
            status__in=['completed', 'failed']
        )
        
        for analysis in old_analyses:
            # Delete the CV file
            if analysis.cv_file:
                try:
                    analysis.cv_file.delete()
                except:
                    pass
            
            # Delete the analysis record
            analysis.delete()
            
    except Exception as e:
        logger.error(f"Error in cleanup task: {e}")
        raise