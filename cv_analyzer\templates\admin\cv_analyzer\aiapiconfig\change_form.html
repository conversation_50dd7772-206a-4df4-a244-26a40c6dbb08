{% extends "admin/change_form.html" %}
{% load i18n %}

{% block submit_buttons_bottom %}
    {{ block.super }}
    <div class="submit-row">
        <input type="button" value="{% trans 'Fetch Models' %}" id="fetch-models-btn">
    </div>
{% endblock %}

{% block admin_change_form_document_ready %}
{{ block.super }}
<script type="text/javascript">
(function($) {
    $(document).ready(function() {
        var $modelNameField = $('#id_model_name');
        var $providerField = $('#id_provider');
        var $apiKeyField = $('#id_api_key');
        var $apiUrlField = $('#id_api_url');

        $('#fetch-models-btn').click(function(e) {
            e.preventDefault();
            var provider = $providerField.val();
            var apiKey = $apiKeyField.val();
            var apiUrl = $apiUrlField.val();

            $.ajax({
                url: '{% url "fetch_models" %}',
                method: 'POST',
                data: {
                    provider: provider,
                    api_key: apiKey,
                    api_url: apiUrl,
                    csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val()
                },
                success: function(data) {
                    $modelNameField.empty();
                    $.each(data.models, function(index, model) {
                        $modelNameField.append($('<option></option>').attr('value', model).text(model));
                    });
                    $modelNameField.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    alert('Error fetching models: ' + error);
                }
            });
        });
    });
})(django.jQuery);
</script>
{% endblock %}