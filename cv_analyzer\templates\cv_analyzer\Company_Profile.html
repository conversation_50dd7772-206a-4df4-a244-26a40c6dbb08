{% extends "cv_analyzer/base.html" %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header Section -->
    <div class="max-w-2xl mx-auto mb-8 text-center">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-3">Create Company Profile</h1>
        <p class="text-lg text-gray-600 dark:text-gray-400">Set up your company information to start managing vacancies and analyzing CVs</p>
    </div>

    <!-- Form Card -->
    <div class="max-w-2xl mx-auto">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <form method="POST" action="{% url 'create_company_profile' %}" class="space-y-6">
                {% csrf_token %}

                <!-- Company Name -->
                <div>
                    <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Company Name
                    </label>
                    {{ form.name|add_class:"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white" }}
                    {% if form.name.errors %}
                    <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ form.name.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Industry -->
                <div>
                    <label for="{{ form.industry.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Industry
                    </label>
                    {{ form.industry|add_class:"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white" }}
                    {% if form.industry.errors %}
                    <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ form.industry.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Company Size -->
                <div>
                    <label for="{{ form.size.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Company Size
                    </label>
                    {{ form.size|add_class:"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white" }}
                    {% if form.size.errors %}
                    <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ form.size.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Description -->
                <div>
                    <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Company Description
                    </label>
                    {{ form.description|add_class:"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white h-32" }}
                    {% if form.description.errors %}
                    <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ form.description.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Website -->
                <div>
                    <label for="{{ form.website.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Website
                    </label>
                    <div class="flex">
                        <span class="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border border-r-0 border-gray-300 rounded-l-lg dark:bg-gray-600 dark:text-gray-400 dark:border-gray-600">
                            <i class="fas fa-globe"></i>
                        </span>
                        {{ form.website|add_class:"rounded-none rounded-r-lg bg-gray-50 border border-gray-300 text-gray-900 focus:ring-blue-500 focus:border-blue-500 block flex-1 min-w-0 w-full text-sm p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" }}
                    </div>
                    {% if form.website.errors %}
                    <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ form.website.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Location -->
                <div>
                    <label for="{{ form.location.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Location
                    </label>
                    <div class="flex">
                        <span class="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border border-r-0 border-gray-300 rounded-l-lg dark:bg-gray-600 dark:text-gray-400 dark:border-gray-600">
                            <i class="fas fa-location-dot"></i>
                        </span>
                        {{ form.location|add_class:"rounded-none rounded-r-lg bg-gray-50 border border-gray-300 text-gray-900 focus:ring-blue-500 focus:border-blue-500 block flex-1 min-w-0 w-full text-sm p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" }}
                    </div>
                    {% if form.location.errors %}
                    <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ form.location.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-4 pt-4">
                    <button type="button" onclick="history.back()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-4 focus:ring-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700 transition-colors duration-200">
                        Cancel
                    </button>
                    <button type="submit" class="px-5 py-2.5 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 rounded-lg dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:ring-blue-800 transition-colors duration-200">
                        <i class="fas fa-building mr-2"></i>
                        Create Company Profile
                    </button>
                </div>
            </form>
        </div>

        <!-- Help Text -->
        <div class="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-600 dark:text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-300">Why create a company profile?</h3>
                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-400">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Post job vacancies and manage applications</li>
                            <li>Customize CV analysis criteria for your company</li>
                            <li>Access detailed analytics and reporting</li>
                            <li>Build your employer brand on our platform</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}