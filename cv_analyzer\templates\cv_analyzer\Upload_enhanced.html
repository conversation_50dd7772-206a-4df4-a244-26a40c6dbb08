{% extends "cv_analyzer/base.html" %}
{% load static %}

{% block title %}Upload CV - CV Analyzer{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .upload-container {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        padding: 3rem 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .upload-container::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .upload-method-tab {
        background: white;
        border: 2px solid #e5e7eb;
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    
    .upload-method-tab:hover {
        border-color: #3b82f6;
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(59, 130, 246, 0.2);
    }
    
    .upload-method-tab.active {
        border-color: #3b82f6;
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    }
    
    .dark .upload-method-tab {
        background: #1f2937;
        border-color: #374151;
    }
    
    .dark .upload-method-tab.active {
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
        color: white;
    }

    .dropzone {
        border: 3px dashed #d1d5db;
        border-radius: 1rem;
        padding: 3rem 2rem;
        text-align: center;
        transition: all 0.3s ease;
        background: #f9fafb;
        position: relative;
        overflow: hidden;
    }
    
    .dropzone.dragover {
        border-color: #3b82f6;
        background: #eff6ff;
        transform: scale(1.02);
    }
    
    .dropzone:hover {
        border-color: #6b7280;
        background: #f3f4f6;
    }
    
    .dark .dropzone {
        background: #1f2937;
        border-color: #4b5563;
    }
    
    .dark .dropzone.dragover {
        background: #1e3a8a;
        border-color: #3b82f6;
    }

    .file-upload-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .process-flow {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 2rem 0;
    }
    
    .process-step {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid #e5e7eb;
        position: relative;
        transition: all 0.3s ease;
    }
    
    .process-step:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
    
    .dark .process-step {
        background: #1f2937;
        border-color: #374151;
    }
    
    .process-step::after {
        content: '→';
        position: absolute;
        right: -15px;
        top: 50%;
        transform: translateY(-50%);
        color: #3b82f6;
        font-size: 1.5rem;
        font-weight: bold;
    }
    
    .process-step:last-child::after {
        display: none;
    }
    
    @media (max-width: 768px) {
        .process-step::after {
            content: '↓';
            right: 50%;
            top: auto;
            bottom: -15px;
            transform: translateX(50%);
        }
    }

    .feature-card {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }
    
    .feature-card:hover::before {
        transform: scaleX(1);
    }
    
    .feature-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }
    
    .dark .feature-card {
        background: #1f2937;
        border-color: #374151;
    }

    .cloud-option {
        background: white;
        border: 2px solid #e5e7eb;
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .cloud-option:hover {
        border-color: #3b82f6;
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(59, 130, 246, 0.15);
    }
    
    .dark .cloud-option {
        background: #1f2937;
        border-color: #374151;
    }

    .progress-container {
        background: white;
        border-radius: 0.75rem;
        padding: 2rem;
        border: 1px solid #e5e7eb;
        margin-top: 2rem;
        display: none;
    }
    
    .dark .progress-container {
        background: #1f2937;
        border-color: #374151;
    }

    .section-header {
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }
    
    .dark .section-header {
        border-color: #374151;
    }

    .form-group {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid #e5e7eb;
        margin-bottom: 1.5rem;
    }
    
    .dark .form-group {
        background: #1f2937;
        border-color: #374151;
    }

    .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        border: none;
        font-weight: 600;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
    }
    
    .btn-primary:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .file-types {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 1rem;
        flex-wrap: wrap;
    }
    
    .file-type {
        background: #f3f4f6;
        color: #6b7280;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .dark .file-type {
        background: #374151;
        color: #9ca3af;
    }

    .upload-tabs {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .tab-content {
        display: none;
    }
    
    .tab-content.active {
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="upload-container">
        <h1 class="text-3xl font-bold mb-2 relative z-10">
            <i class="fas fa-cloud-upload-alt mr-3"></i>Upload CV for Analysis
        </h1>
        <p class="text-blue-100 text-lg max-w-3xl mx-auto relative z-10">
            Upload your CV using our advanced AI-powered analysis system. Get instant insights, compatibility scores, and match with the perfect job opportunities.
        </p>
        
        <!-- Process Flow -->
        <div class="process-flow relative z-10">
            <div class="process-step">
                <div class="text-3xl text-blue-500 mb-2">
                    <i class="fas fa-upload"></i>
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white">Upload</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">Select your CV file</p>
            </div>
            <div class="process-step">
                <div class="text-3xl text-green-500 mb-2">
                    <i class="fas fa-cog"></i>
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white">Process</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">AI extracts information</p>
            </div>
            <div class="process-step">
                <div class="text-3xl text-purple-500 mb-2">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white">Analyze</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">Generate insights</p>
            </div>
            <div class="process-step">
                <div class="text-3xl text-orange-500 mb-2">
                    <i class="fas fa-trophy"></i>
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white">Results</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">View detailed report</p>
            </div>
        </div>
    </div>

    <!-- Upload Methods -->
    <div class="section-header">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-upload mr-3 text-blue-500"></i>Choose Upload Method
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">Select the most convenient way to upload your CV</p>
    </div>

    <div class="upload-tabs">
        <div class="upload-method-tab active" data-tab="local">
            <div class="flex items-center">
                <i class="fas fa-laptop text-blue-500 text-xl mr-3"></i>
                <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">Local Upload</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">From your device</p>
                </div>
            </div>
        </div>
        
        <div class="upload-method-tab" data-tab="batch">
            <div class="flex items-center">
                <i class="fas fa-layer-group text-green-500 text-xl mr-3"></i>
                <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">Batch Upload</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Multiple files</p>
                </div>
            </div>
        </div>
        
        <div class="upload-method-tab" data-tab="cloud">
            <div class="flex items-center">
                <i class="fas fa-cloud text-purple-500 text-xl mr-3"></i>
                <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">Cloud Storage</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">From cloud services</p>
                </div>
            </div>
        </div>
        
        <div class="upload-method-tab" data-tab="email">
            <div class="flex items-center">
                <i class="fas fa-envelope text-orange-500 text-xl mr-3"></i>
                <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">Email Import</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">From email attachments</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Contents -->
    <!-- Local Upload Tab -->
    <div class="tab-content active" id="local-content">
        <form method="post" enctype="multipart/form-data" id="uploadForm">
            {% csrf_token %}
            
            <div class="dropzone" id="dropzone">
                <div class="file-upload-icon">
                    <i class="fas fa-cloud-upload-alt text-white text-3xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                    Drop your CV here or click to browse
                </h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    Supports PDF, DOC, and DOCX files up to 10MB
                </p>
                
                <input type="file" name="cv_file" id="fileInput" class="hidden" accept=".pdf,.doc,.docx" required>
                
                <button type="button" class="btn-primary" onclick="document.getElementById('fileInput').click()">
                    <i class="fas fa-folder-open mr-2"></i>Browse Files
                </button>
                
                <div class="file-types">
                    <span class="file-type">
                        <i class="far fa-file-pdf text-red-500"></i>PDF
                    </span>
                    <span class="file-type">
                        <i class="far fa-file-word text-blue-500"></i>DOC
                    </span>
                    <span class="file-type">
                        <i class="far fa-file-word text-blue-500"></i>DOCX
                    </span>
                </div>
            </div>

            <!-- Position Selection -->
            <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    <i class="fas fa-bullseye mr-2 text-blue-500"></i>Select Target Position (Optional)
                </label>
                <select name="vacancy_id" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="">General CV Analysis (No specific position)</option>
                    {% for vacancy in vacancies %}
                        <option value="{{ vacancy.id }}">{{ vacancy.title }} - {{ vacancy.company.name }}</option>
                    {% endfor %}
                </select>
                <small class="text-gray-500 dark:text-gray-400 mt-2 block">You can upload and analyze your CV without targeting a specific position. This provides general insights about your CV quality.</small>
            </div>

            <!-- Analysis Options -->
            <div class="form-group">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-sliders-h mr-2 text-purple-500"></i>Analysis Options
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <label class="flex items-center p-3 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors">
                        <input type="checkbox" name="auto_match" checked class="mr-3 text-blue-600">
                        <div>
                            <span class="font-medium text-gray-900 dark:text-white">Auto-match with all positions</span>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Find best matching opportunities</p>
                        </div>
                    </label>
                    <label class="flex items-center p-3 bg-gray-50 dark:bg-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors">
                        <input type="checkbox" name="detailed_analysis" checked class="mr-3 text-blue-600">
                        <div>
                            <span class="font-medium text-gray-900 dark:text-white">Detailed skill analysis</span>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Extract and evaluate all skills</p>
                        </div>
                    </label>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="btn-primary" id="uploadBtn">
                    <i class="fas fa-magic mr-2"></i>Analyze CV
                </button>
            </div>
        </form>
    </div>

    <!-- Batch Upload Tab -->
    <div class="tab-content" id="batch-content">
        <div class="form-group">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <i class="fas fa-layer-group mr-2 text-green-500"></i>Batch Upload Multiple CVs
            </h3>
            <div class="dropzone">
                <div class="file-upload-icon">
                    <i class="fas fa-copy text-white text-3xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                    Drop multiple CV files here
                </h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    Upload up to 50 CVs at once for bulk analysis
                </p>
                
                <input type="file" multiple class="hidden" accept=".pdf,.doc,.docx">
                <button type="button" class="btn-primary">
                    <i class="fas fa-folder-open mr-2"></i>Select Multiple Files
                </button>
            </div>
        </div>
    </div>

    <!-- Cloud Storage Tab -->
    <div class="tab-content" id="cloud-content">
        <div class="form-group">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <i class="fas fa-cloud mr-2 text-purple-500"></i>Import from Cloud Storage
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="cloud-option">
                    <i class="fab fa-google-drive text-4xl text-green-500 mb-3"></i>
                    <h4 class="font-semibold text-gray-900 dark:text-white">Google Drive</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Connect your Google Drive</p>
                    <button class="btn-primary w-full">
                        <i class="fab fa-google mr-2"></i>Connect
                    </button>
                </div>
                
                <div class="cloud-option">
                    <i class="fab fa-microsoft text-4xl text-blue-500 mb-3"></i>
                    <h4 class="font-semibold text-gray-900 dark:text-white">OneDrive</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Connect your OneDrive</p>
                    <button class="btn-primary w-full">
                        <i class="fab fa-microsoft mr-2"></i>Connect
                    </button>
                </div>
                
                <div class="cloud-option">
                    <i class="fab fa-dropbox text-4xl text-blue-600 mb-3"></i>
                    <h4 class="font-semibold text-gray-900 dark:text-white">Dropbox</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Connect your Dropbox</p>
                    <button class="btn-primary w-full">
                        <i class="fab fa-dropbox mr-2"></i>Connect
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Import Tab -->
    <div class="tab-content" id="email-content">
        <div class="form-group">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <i class="fas fa-envelope mr-2 text-orange-500"></i>Import from Email
            </h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Address</label>
                    <input type="email" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="Enter email address to scan for CV attachments">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Password</label>
                    <input type="password" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="Email password">
                </div>
                <button type="button" class="btn-primary">
                    <i class="fas fa-search mr-2"></i>Scan for CVs
                </button>
            </div>
        </div>
    </div>

    <!-- Progress Container -->
    <div class="progress-container" id="progressContainer">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <i class="fas fa-cog fa-spin mr-2 text-blue-500"></i>Processing your CV...
        </h3>
        <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-4">
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500" style="width: 0%" id="progressBar"></div>
        </div>
        <div class="text-center text-sm text-gray-600 dark:text-gray-400" id="progressText">
            Uploading file...
        </div>
    </div>

    <!-- AI Features Showcase -->
    <div class="section-header">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-brain mr-3 text-purple-500"></i>AI Analysis Features
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">Discover what our advanced AI can extract from your CV</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div class="feature-card">
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-user text-white text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Personal Information</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">Extract name, contact details, and professional summary automatically</p>
            </div>
        </div>

        <div class="feature-card">
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-tools text-white text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Skills Analysis</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">Identify technical and soft skills with proficiency levels</p>
            </div>
        </div>

        <div class="feature-card">
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-briefcase text-white text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Experience Evaluation</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">Analyze work history, roles, and career progression</p>
            </div>
        </div>

        <div class="feature-card">
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-graduation-cap text-white text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Education Assessment</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">Review educational background and qualifications</p>
            </div>
        </div>

        <div class="feature-card">
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-chart-line text-white text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Compatibility Score</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">Generate precise matching scores against job requirements</p>
            </div>
        </div>

        <div class="feature-card">
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-lightbulb text-white text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">AI Recommendations</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">Receive intelligent suggestions for career improvement</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching
    const tabs = document.querySelectorAll('.upload-method-tab');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            tabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(content => {
                content.classList.remove('active');
                content.style.display = 'none';
            });
            
            // Add active class to clicked tab
            this.classList.add('active');
            const targetTab = this.dataset.tab;
            const targetContent = document.getElementById(targetTab + '-content');
            if (targetContent) {
                targetContent.classList.add('active');
                targetContent.style.display = 'block';
            }
        });
    });
    
    // File upload functionality
    const dropzone = document.getElementById('dropzone');
    const fileInput = document.getElementById('fileInput');
    const uploadForm = document.getElementById('uploadForm');
    const progressContainer = document.getElementById('progressContainer');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    // Drag and drop
    dropzone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    dropzone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    dropzone.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect(files[0]);
        }
    });
    
    dropzone.addEventListener('click', function() {
        fileInput.click();
    });
    
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });
    
    function handleFileSelect(file) {
        // Validate file type
        const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please select a PDF, DOC, or DOCX file.');
            return;
        }
        
        // Validate file size (10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('File size must be less than 10MB.');
            return;
        }
        
        // Update dropzone to show selected file
        dropzone.innerHTML = `
            <div class="file-upload-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                <i class="fas fa-check text-white text-3xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                File Selected: ${file.name}
            </h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">
                ${(file.size / (1024 * 1024)).toFixed(2)} MB
            </p>
            <button type="button" class="btn-primary" onclick="document.getElementById('fileInput').click()">
                <i class="fas fa-exchange-alt mr-2"></i>Change File
            </button>
        `;
    }
    
    // Form submission with progress
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!fileInput.files.length) {
            alert('Please select a file to upload.');
            return;
        }
        
        // Show progress container
        progressContainer.style.display = 'block';
        
        // Simulate upload progress
        let progress = 0;
        const progressSteps = [
            { percent: 20, text: 'Uploading file...' },
            { percent: 40, text: 'Extracting text content...' },
            { percent: 60, text: 'Analyzing with AI...' },
            { percent: 80, text: 'Generating compatibility scores...' },
            { percent: 100, text: 'Complete! Redirecting to results...' }
        ];
        
        let stepIndex = 0;
        const interval = setInterval(function() {
            if (stepIndex < progressSteps.length) {
                const step = progressSteps[stepIndex];
                progressBar.style.width = step.percent + '%';
                progressText.textContent = step.text;
                stepIndex++;
                
                if (step.percent === 100) {
                    clearInterval(interval);
                    setTimeout(function() {
                        // Submit the actual form
                        uploadForm.submit();
                    }, 1000);
                }
            }
        }, 1500);
    });
    
    // Animate feature cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe feature cards
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = `all 0.6s ease ${index * 0.1}s`;
        observer.observe(card);
    });
});
</script>
{% endblock %}