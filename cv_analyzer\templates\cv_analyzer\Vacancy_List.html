{% extends "cv_analyzer/base.html" %}
{% load static %}

{% block title %}Available Vacancies - <PERSON><PERSON> Analyzer{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .page-header {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        padding: 3rem 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .metric-card {
        background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
        border: none;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        border-radius: 0.75rem;
        padding: 1.5rem;
        color: white;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }
    
    .metric-card.blue {
        --gradient-start: #3b82f6;
        --gradient-end: #1d4ed8;
    }
    
    .metric-card.green {
        --gradient-start: #10b981;
        --gradient-end: #047857;
    }
    
    .metric-card.purple {
        --gradient-start: #8b5cf6;
        --gradient-end: #6d28d9;
    }
    
    .metric-card.orange {
        --gradient-start: #f59e0b;
        --gradient-end: #d97706;
    }

    .vacancy-card {
        background: white;
        border-radius: 0.75rem;
        padding: 2rem;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .vacancy-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }
    
    .vacancy-card:hover::before {
        transform: scaleX(1);
    }
    
    .vacancy-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }
    
    .dark .vacancy-card {
        background: #1f2937;
        border-color: #374151;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        border: 1px solid;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        border-color: transparent;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
        text-decoration: none;
        color: white;
    }
    
    .btn-secondary {
        background: white;
        color: #6b7280;
        border-color: #d1d5db;
    }
    
    .btn-secondary:hover {
        background: #f9fafb;
        border-color: #9ca3af;
        text-decoration: none;
        color: #374151;
    }
    
    .dark .btn-secondary {
        background: #374151;
        color: #d1d5db;
        border-color: #4b5563;
    }
    
    .dark .btn-secondary:hover {
        background: #4b5563;
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: 1rem;
        border: 1px solid #e5e7eb;
    }
    
    .dark .empty-state {
        background: #1f2937;
        border-color: #374151;
    }

    .company-logo {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .status-badge.active {
        background: #d1fae5;
        color: #065f46;
    }
    
    .dark .status-badge.active {
        background: #047857;
        color: #d1fae5;
    }

    .search-filter-bar {
        background: white;
        border-radius: 0.75rem;
        padding: 2rem;
        border: 1px solid #e5e7eb;
        margin-bottom: 2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
    
    .dark .search-filter-bar {
        background: #1f2937;
        border-color: #374151;
    }
</style>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="text-3xl font-bold mb-2 relative z-10">
            <i class="fas fa-search mr-3"></i>Available Vacancies
        </h1>
        <p class="text-blue-100 text-lg max-w-3xl mx-auto relative z-10">
            Discover exciting career opportunities and find your perfect match with our intelligent job matching system
        </p>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="metric-card blue">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold opacity-90 mb-1">Total Jobs</h3>
                    <div class="text-3xl font-bold">{{ vacancies|length|default:0 }}</div>
                    <div class="text-sm opacity-75 mt-2 flex items-center">
                        <i class="fas fa-briefcase mr-1"></i>
                        Available positions
                    </div>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-briefcase"></i>
                </div>
            </div>
        </div>

        <div class="metric-card green">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold opacity-90 mb-1">Active Companies</h3>
                    <div class="text-3xl font-bold">15</div>
                    <div class="text-sm opacity-75 mt-2 flex items-center">
                        <i class="fas fa-building mr-1"></i>
                        Hiring partners
                    </div>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-building"></i>
                </div>
            </div>
        </div>

        <div class="metric-card purple">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold opacity-90 mb-1">New This Week</h3>
                    <div class="text-3xl font-bold">8</div>
                    <div class="text-sm opacity-75 mt-2 flex items-center">
                        <i class="fas fa-plus mr-1"></i>
                        Fresh opportunities
                    </div>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-star"></i>
                </div>
            </div>
        </div>

        <div class="metric-card orange">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold opacity-90 mb-1">Remote Jobs</h3>
                    <div class="text-3xl font-bold">12</div>
                    <div class="text-sm opacity-75 mt-2 flex items-center">
                        <i class="fas fa-home mr-1"></i>
                        Work from anywhere
                    </div>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-laptop"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Bar -->
    <div class="search-filter-bar">
        <div class="flex flex-col lg:flex-row gap-4">
            <div class="flex-1">
                <div class="relative">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    <input type="text" placeholder="Search job titles, companies, or keywords..." class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
            </div>
            
            <div class="flex gap-4">
                <select class="px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="">All Locations</option>
                    <option value="remote">Remote</option>
                    <option value="onsite">On-site</option>
                    <option value="hybrid">Hybrid</option>
                </select>
                
                <select class="px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="">All Industries</option>
                    <option value="technology">Technology</option>
                    <option value="finance">Finance</option>
                    <option value="healthcare">Healthcare</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Vacancies Section -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <i class="fas fa-briefcase mr-3 text-blue-500"></i>Open Positions
        </h2>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {% for vacancy in vacancies %}
            <div class="vacancy-card">
                <div class="flex items-start justify-between mb-4">
                    <div class="company-logo">
                        {{ vacancy.company.name|first|upper|default:"T" }}
                    </div>
                    <span class="status-badge active">
                        <i class="fas fa-circle"></i>Active
                    </span>
                </div>
                
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    {{ vacancy.title|default:"Senior Software Engineer" }}
                </h3>
                
                <div class="flex items-center gap-4 mb-3 text-sm text-gray-600 dark:text-gray-400">
                    <span class="flex items-center">
                        <i class="fas fa-building mr-1"></i>{{ vacancy.company.name|default:"TechCorp Inc" }}
                    </span>
                    <span class="flex items-center">
                        <i class="fas fa-map-marker-alt mr-1"></i>{{ vacancy.location|default:"Remote" }}
                    </span>
                    <span class="flex items-center">
                        <i class="fas fa-clock mr-1"></i>Full-time
                    </span>
                </div>
                
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    {{ vacancy.description|default:"Join our innovative team and work on cutting-edge projects using the latest technologies. We offer excellent benefits and growth opportunities."|truncatewords:20 }}
                </p>
                
                <div class="grid grid-cols-3 gap-4 mb-4 text-center py-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div>
                        <div class="text-lg font-bold text-blue-600 dark:text-blue-400">3-5</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Years Exp</div>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-green-600 dark:text-green-400">24</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Applicants</div>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-purple-600 dark:text-purple-400">85%</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Match</div>
                    </div>
                </div>
                
                <div class="flex gap-3">
                    <a href="{% url 'apply_to_vacancy' vacancy.id %}" class="flex-1 btn btn-primary">
                        <i class="fas fa-paper-plane"></i>Apply Now
                    </a>
                    <button class="btn btn-secondary">
                        <i class="fas fa-heart"></i>Save
                    </button>
                </div>
            </div>
            {% empty %}
            <div class="col-span-full">
                <div class="empty-state">
                    <div class="text-gray-500 dark:text-gray-400">
                        <i class="fas fa-search text-6xl mb-4 opacity-30"></i>
                        <h3 class="text-xl font-semibold mb-2">No vacancies available</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-6">
                            We're working hard to bring you new opportunities. Check back soon or upload your CV to get notified about matching positions.
                        </p>
                        <div class="flex gap-3 justify-center">
                            <a href="{% url 'upload_cv' %}" class="btn btn-primary">
                                <i class="fas fa-upload mr-2"></i>Upload Your CV
                            </a>
                            <button class="btn btn-secondary" onclick="location.reload()">
                                <i class="fas fa-refresh mr-2"></i>Refresh Page
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate metric cards
    const metricCards = document.querySelectorAll('.metric-card');
    metricCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });
    
    // Animate vacancy cards
    const vacancyCards = document.querySelectorAll('.vacancy-card');
    vacancyCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, (index * 100) + 600);
    });
    
    // Search functionality
    const searchInput = document.querySelector('input[type="text"]');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const vacancyCards = document.querySelectorAll('.vacancy-card');
            
            vacancyCards.forEach(card => {
                const title = card.querySelector('h3').textContent.toLowerCase();
                const description = card.querySelector('p').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || description.includes(searchTerm)) {
                    card.style.display = 'block';
                    card.style.opacity = '1';
                } else {
                    card.style.opacity = '0.3';
                }
            });
        });
    }
});
</script>
{% endblock %}