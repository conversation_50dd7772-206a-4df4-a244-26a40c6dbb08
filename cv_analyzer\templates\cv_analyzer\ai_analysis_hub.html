{% extends "cv_analyzer/base.html" %}
{% load static %}

{% block title %}AI Analysis Hub - CV Analyzer{% endblock %}

{% block extra_css %}
<style>
    .analysis-card {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .analysis-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .dark .analysis-card {
        background: #1f2937;
        border-color: #374151;
    }
    
    .metric-card {
        background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
        border: none;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        border-radius: 0.75rem;
        padding: 1.5rem;
        color: white;
    }
    
    .metric-card.blue { --gradient-start: #3b82f6; --gradient-end: #1d4ed8; }
    .metric-card.green { --gradient-start: #10b981; --gradient-end: #047857; }
    .metric-card.purple { --gradient-start: #8b5cf6; --gradient-end: #7c3aed; }
    .metric-card.orange { --gradient-start: #f59e0b; --gradient-end: #d97706; }
    .metric-card.red { --gradient-start: #ef4444; --gradient-end: #dc2626; }
    .metric-card.indigo { --gradient-start: #6366f1; --gradient-end: #4f46e5; }
    
    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        border: 1px solid;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        border-color: transparent;
    }
    
    .btn-success {
        background: linear-gradient(135deg, #10b981, #047857);
        color: white;
        border-color: transparent;
    }
    
    .btn-warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
        border-color: transparent;
    }
    
    .btn-secondary {
        background: white;
        color: #6b7280;
        border-color: #d1d5db;
    }
    
    .dark .btn-secondary {
        background: #374151;
        color: #d1d5db;
        border-color: #4b5563;
    }
    
    .progress-bar {
        background: #e5e7eb;
        border-radius: 9999px;
        overflow: hidden;
        height: 0.5rem;
    }
    
    .progress-fill {
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        height: 100%;
        transition: width 0.3s ease;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .status-success { background: #d1fae5; color: #065f46; }
    .status-warning { background: #fef3c7; color: #92400e; }
    .status-error { background: #fee2e2; color: #991b1b; }
    .status-info { background: #dbeafe; color: #1e40af; }
    
    .dark .status-success { background: #047857; color: #d1fae5; }
    .dark .status-warning { background: #d97706; color: #fef3c7; }
    .dark .status-error { background: #dc2626; color: #fee2e2; }
    .dark .status-info { background: #1e40af; color: #dbeafe; }
    
    .selection-checkbox {
        width: 1.25rem;
        height: 1.25rem;
        border-radius: 0.25rem;
        border: 2px solid #d1d5db;
        cursor: pointer;
    }
    
    .selection-checkbox:checked {
        background: #3b82f6;
        border-color: #3b82f6;
    }
    
    /* Popup overlay styles */
    .popup-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }
    
    .popup-overlay.hidden {
        display: none;
    }
    
    .popup-content {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    
    .popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .popup-close {
        background: none;
        border: none;
        font-size: 1.25rem;
        color: #6b7280;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.375rem;
        transition: all 0.2s ease;
    }

    .popup-close:hover {
        background: #f3f4f6;
        color: #374151;
    }
    
    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .btn-danger {
        background: #ef4444;
        color: white;
        border-color: #ef4444;
    }
    
    .btn-danger:hover {
        background: #dc2626;
        border-color: #dc2626;
    }
    
    .btn-info {
        background: #06b6d4;
        color: white;
        border-color: #06b6d4;
    }
    
    .btn-info:hover {
        background: #0891b2;
        border-color: #0891b2;
    }
    
    /* AI Configuration Popup Styles - No dimming or blur effect */
    div#aiConfigPopup.popup-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: none !important;
        background-color: transparent !important;
        backdrop-filter: none !important;
        filter: none !important;
        z-index: 99999 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 20px !important;
        visibility: visible !important;
        opacity: 1 !important;
        pointer-events: auto !important;
    }
    
    div#aiConfigPopup.popup-overlay.hidden {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
    }
    
    /* Force show when not hidden */
    div#aiConfigPopup.popup-overlay:not(.hidden) {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        pointer-events: auto !important;
    }
    
    /* Ensure popup content is clearly visible */
    div#aiConfigPopup .popup-content {
        position: relative !important;
        z-index: 100000 !important;
        pointer-events: auto !important;
        background: white !important;
        border: 3px solid #3b82f6 !important;
        border-radius: 12px !important;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
        max-width: 800px !important;
        width: 90vw !important;
        max-height: 90vh !important;
        overflow-y: auto !important;
        margin: auto !important;
    }
    
    /* Style the popup header */
    div#aiConfigPopup .popup-header {
        background: #f8fafc !important;
        border-bottom: 2px solid #e2e8f0 !important;
        padding: 20px !important;
        border-radius: 12px 12px 0 0 !important;
    }
    
    /* Style the popup content area */
    div#aiConfigPopup .ai-config-content {
        padding: 20px !important;
        background: white !important;
    }
    
    /* Make sure tab buttons are visible */
    div#aiConfigPopup .tab-btn {
        background: #f1f5f9 !important;
        padding: 12px 16px !important;
        border: 1px solid #cbd5e1 !important;
        margin-right: 4px !important;
        border-radius: 6px 6px 0 0 !important;
        cursor: pointer !important;
    }
    
    div#aiConfigPopup .tab-btn.active {
        background: white !important;
        border-bottom: 1px solid white !important;
        font-weight: 600 !important;
        color: #3b82f6 !important;
    }
    
    .popup-content {
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        max-height: 90vh;
        overflow-y: auto;
        position: relative;
        width: 100%;
        max-width: 800px;
    }
    
    .popup-header {
        padding: 24px 24px 0 24px;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .popup-close {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #6b7280;
        padding: 8px;
        border-radius: 6px;
        transition: all 0.2s;
    }
    
    .popup-close:hover {
        background: #f3f4f6;
        color: #374151;
    }
    
    .ai-config-content {
        padding: 0 24px 24px 24px;
    }
    
    .config-tabs {
        border-bottom: 1px solid #e5e7eb;
        margin-bottom: 20px;
    }
    
    .tab-btn {
        padding: 12px 16px;
        background: none;
        border: none;
        border-bottom: 2px solid transparent;
        cursor: pointer;
        font-weight: 500;
        color: #6b7280;
        transition: all 0.2s;
    }
    
    .tab-btn.active {
        color: #3b82f6;
        border-bottom-color: #3b82f6;
    }
    
    .tab-btn:hover {
        color: #374151;
    }
    
    .config-tab-content {
        min-height: 300px;
    }
    
    .provider-card {
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        transition: all 0.2s;
    }
    
    .provider-card:hover {
        border-color: #d1d5db;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }
    
    .provider-card.active {
        border-color: #10b981;
        background-color: #f0fdf4;
    }
    
    .provider-card.inactive {
        border-color: #e5e7eb;
        background-color: #f9fafb;
    }
    
    .status-indicator {
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: 500;
    }
    
    .status-indicator.active {
        background: #dcfce7;
        color: #166534;
    }
    
    .status-indicator.inactive {
        background: #f3f4f6;
        color: #6b7280;
    }
    
    .form-group {
        margin-bottom: 16px;
    }
    
    .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                <i class="fas fa-robot mr-3 text-blue-500"></i>AI Analysis Hub
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">
                Manage and run AI-powered CV analysis and matching
            </p>
        </div>
        <div class="flex gap-3">
            <button id="refreshBtn" class="btn btn-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i>Refresh
            </button>
            <button id="testPopupBtn" class="btn btn-info" onclick="testAIConfigPopup()" title="Test Popup">
                <i class="fas fa-bug"></i>Test Popup
            </button>
            <button id="bulkAnalyzeBtn" class="btn btn-success" onclick="startBulkAnalysis()">
                <i class="fas fa-play"></i>Start Analysis
            </button>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <div class="metric-card blue">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-semibold opacity-90 mb-1">Total CVs</h3>
                    <div class="text-2xl font-bold">{{ stats.total_cvs }}</div>
                </div>
                <i class="fas fa-file-alt text-2xl opacity-80"></i>
            </div>
        </div>
        
        <div class="metric-card green">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-semibold opacity-90 mb-1">Analyzed</h3>
                    <div class="text-2xl font-bold">{{ stats.analyzed_cvs }}</div>
                </div>
                <i class="fas fa-check-circle text-2xl opacity-80"></i>
            </div>
        </div>
        
        <div class="metric-card orange">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-semibold opacity-90 mb-1">Pending</h3>
                    <div class="text-2xl font-bold">{{ stats.pending_analysis }}</div>
                </div>
                <i class="fas fa-clock text-2xl opacity-80"></i>
            </div>
        </div>
        
        <div class="metric-card purple">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-semibold opacity-90 mb-1">Vacancies</h3>
                    <div class="text-2xl font-bold">{{ stats.total_vacancies }}</div>
                </div>
                <i class="fas fa-briefcase text-2xl opacity-80"></i>
            </div>
        </div>
        
        <div class="metric-card indigo">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-semibold opacity-90 mb-1">Active Jobs</h3>
                    <div class="text-2xl font-bold">{{ stats.active_vacancies }}</div>
                </div>
                <i class="fas fa-bolt text-2xl opacity-80"></i>
            </div>
        </div>
        
        <div class="metric-card red">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-semibold opacity-90 mb-1">Comparisons</h3>
                    <div class="text-2xl font-bold">{{ stats.total_comparisons }}</div>
                </div>
                <i class="fas fa-chart-line text-2xl opacity-80"></i>
            </div>
        </div>
    </div>
    
    <!-- AI Status -->
    <div class="analysis-card">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-cog mr-2 text-blue-500"></i>AI Configuration Status
            </h2>
            {% if ai_status %}
            <span class="status-badge status-success">
                <i class="fas fa-check mr-1"></i>Active
            </span>
            {% else %}
            <span class="status-badge status-warning">
                <i class="fas fa-exclamation-triangle mr-1"></i>Not Configured
            </span>
            {% endif %}
        </div>
        
        {% if ai_status %}
        <p class="text-green-600 dark:text-green-400 mb-3">
            <i class="fas fa-info-circle mr-1"></i>AI is configured and ready for analysis
        </p>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {% for config in ai_configs %}
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <h4 class="font-medium text-gray-900 dark:text-white">{{ config.provider|title }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ config.model_name }}</p>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                    {% if config.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                    {% if config.is_active %}Active{% else %}Inactive{% endif %}
                </span>
            </div>
            {% endfor %}
        </div>
        <div class="flex gap-2">
            <button class="btn btn-secondary" onclick="window.openAIConfigPopup ? window.openAIConfigPopup() : alert('AI Configuration function not loaded')">
                <i class="fas fa-cog mr-1"></i>Manage Configuration
            </button>
        </div>
        {% else %}
        <p class="text-yellow-600 dark:text-yellow-400 mb-3">
            <i class="fas fa-exclamation-triangle mr-1"></i>Please configure AI settings before running analysis
        </p>
        <div class="flex gap-2">
            <button class="btn btn-primary" onclick="window.openAIConfigPopup ? window.openAIConfigPopup() : alert('AI Configuration function not loaded')">
                <i class="fas fa-cog mr-1"></i>Configure AI
            </button>
        </div>
        {% endif %}
    </div>
    
    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Pending CVs -->
        <div class="analysis-card">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                    <i class="fas fa-file-import mr-2 text-orange-500"></i>Pending Analysis
                </h2>
                <div class="flex gap-2">
                    <button onclick="selectAllCVs()" class="btn btn-secondary text-sm">
                        <i class="fas fa-check-square"></i>Select All
                    </button>
                    <button onclick="analyzeSelectedCVs()" class="btn btn-primary text-sm">
                        <i class="fas fa-play"></i>Analyze
                    </button>
                </div>
            </div>
            
            <div class="space-y-3 max-h-96 overflow-y-auto">
                {% for cv in pending_cvs %}
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox" class="selection-checkbox cv-checkbox" data-cv-id="{{ cv.id }}">
                        <div>
                            <h4 class="font-medium text-gray-900 dark:text-white">{{ cv.candidate_name }}</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                <i class="fas fa-calendar mr-1"></i>
                                {{ cv.uploaded_at|date:"M d, Y" }}
                            </p>
                        </div>
                    </div>
                    <button onclick="analyzeSingleCV({{ cv.id }})" class="btn btn-primary text-sm">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
                {% empty %}
                <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-check-circle text-4xl mb-3 opacity-50"></i>
                    <p>All CVs have been analyzed!</p>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Active Vacancies -->
        <div class="analysis-card">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                    <i class="fas fa-briefcase mr-2 text-purple-500"></i>Active Vacancies
                </h2>
                <div class="flex gap-2">
                    <button onclick="selectAllVacancies()" class="btn btn-secondary text-sm">
                        <i class="fas fa-check-square"></i>Select All
                    </button>
                    <button onclick="matchSelected()" class="btn btn-success text-sm">
                        <i class="fas fa-search"></i>Match
                    </button>
                </div>
            </div>
            
            <div class="space-y-3 max-h-96 overflow-y-auto">
                {% for vacancy in active_vacancies %}
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox" class="selection-checkbox vacancy-checkbox" data-vacancy-id="{{ vacancy.id }}">
                        <div>
                            <h4 class="font-medium text-gray-900 dark:text-white">{{ vacancy.title }}</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                <i class="fas fa-building mr-1"></i>
                                {{ vacancy.company.name }}
                            </p>
                        </div>
                    </div>
                    <a href="{% url 'vacancy_candidates' vacancy.id %}" class="btn btn-primary text-sm">
                        <i class="fas fa-users"></i>View
                    </a>
                </div>
                {% empty %}
                <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-briefcase text-4xl mb-3 opacity-50"></i>
                    <p>No active vacancies found</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- Recent Analysis Activity -->
    <div class="analysis-card">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-history mr-2 text-green-500"></i>Recent Analysis Activity
            </h2>
            <a href="{% url 'cv_matching_analysis' %}" class="btn btn-secondary">
                <i class="fas fa-chart-bar"></i>View All
            </a>
        </div>
        
        <div class="space-y-3">
            {% for analysis in recent_analyses %}
            <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                        {{ analysis.compatibility_score }}%
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">
                            CV Match for {{ analysis.vacancy.title }}
                        </h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            <i class="fas fa-clock mr-1"></i>
                            {{ analysis.created_at|timesince }} ago
                        </p>
                    </div>
                </div>
                <div class="flex gap-2">
                    <a href="{% url 'analysis_detail' analysis.id %}" class="btn btn-secondary text-sm">
                        <i class="fas fa-eye"></i>View
                    </a>
                </div>
            </div>
            {% empty %}
            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                <i class="fas fa-chart-line text-4xl mb-3 opacity-50"></i>
                <p>No recent analysis activity</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Progress Modal -->
<div id="progressModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
        <div class="text-center">
            <div class="mb-4">
                <i class="fas fa-cog fa-spin text-4xl text-blue-500 mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Running AI Analysis</h3>
                <p class="text-gray-600 dark:text-gray-400 mt-2">Please wait while we process your request...</p>
            </div>
            
            <div class="progress-bar mb-4">
                <div id="progressFill" class="progress-fill" style="width: 0%"></div>
            </div>
            
            <div id="progressText" class="text-sm text-gray-600 dark:text-gray-400">
                Initializing...
            </div>
        </div>
    </div>
</div>

<!-- Include AI Configuration Popup -->
{% include 'cv_analyzer/components/ai_config_popup.html' %}

<!-- Backup Simple AI Config Modal -->
<div id="simpleAIConfigModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold">Quick AI Configuration</h3>
            <button onclick="closeSimpleModal()" class="text-gray-500 hover:text-gray-700">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="space-y-4">
            <div class="text-sm text-gray-600">
                <p>Current Status: <span id="aiStatusText" class="font-semibold">Loading...</span></p>
            </div>
            
            <div class="border-t pt-4">
                <h4 class="font-semibold mb-2">Quick Actions:</h4>
                <div class="space-y-2">
                    <button onclick="quickTestAI()" class="w-full btn btn-secondary text-left">
                        <i class="fas fa-bolt mr-2"></i>Test AI Connection
                    </button>
                    <button onclick="loadProvidersSummary()" class="w-full btn btn-secondary text-left">
                        <i class="fas fa-list mr-2"></i>View Current Providers
                    </button>
                    <a href="/admin/cv_analyzer/aiapiconfig/" class="w-full btn btn-primary text-left block text-center text-white no-underline">
                        <i class="fas fa-cog mr-2"></i>Configure via Admin Panel
                    </a>
                </div>
            </div>
            
            <div id="quickStatusArea" class="text-sm p-3 bg-gray-50 rounded hidden">
                <!-- Status messages will appear here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'cv_analyzer/js/ai_config.js' %}"></script>
<script>
// Debug function to test popup
function testAIConfigPopup() {
    console.log('Testing AI Config Popup...');
    const popup = document.getElementById('aiConfigPopup');
    if (popup) {
        console.log('Popup element found');
        popup.classList.remove('hidden');
    } else {
        console.error('Popup element not found!');
    }
}

// Ensure openAIConfigPopup is globally available
window.openAIConfigPopup = function() {
    console.log('openAIConfigPopup called');
    const popup = document.getElementById('aiConfigPopup');
    
    if (!popup) {
        console.error('AI Config popup element not found! Creating simple popup...');
        // Fallback to creating a simple popup
        createSimpleConfigPopup();
        return;
    }
    
    console.log('Popup found, checking classes...');
    console.log('Popup classes before:', popup.className);
    console.log('Popup style display before:', popup.style.display);
    console.log('Popup computed style before:', window.getComputedStyle(popup).display);
    
    // Force remove hidden class and set display style
    popup.classList.remove('hidden');
    
    // Force all styles inline to override any CSS conflicts and remove blur
    popup.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: none !important;
        background-color: transparent !important;
        backdrop-filter: none !important;
        filter: none !important;
        z-index: 99999 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 20px !important;
        visibility: visible !important;
        opacity: 1 !important;
    `;
    
    // Additional forced styling
    popup.setAttribute('style', popup.style.cssText);
    
    // Force reflow
    popup.offsetHeight;
    
    // Prevent any click events on the backdrop from closing the popup
    popup.onclick = function(e) {
        if (e.target === popup) {
            e.stopPropagation();
            e.preventDefault();
            console.log('Click on backdrop prevented');
            return false;
        }
    };
    
    // Prevent other event handlers from interfering
    popup.addEventListener('click', function(e) {
        if (e.target === popup) {
            e.stopImmediatePropagation();
            console.log('Backdrop click stopped');
        }
    }, true);
    
    console.log('Popup classes after:', popup.className);
    console.log('Popup style display after:', popup.style.display);
    console.log('Popup computed style after:', window.getComputedStyle(popup).display);
    console.log('Popup computed visibility:', window.getComputedStyle(popup).visibility);
    console.log('Popup computed opacity:', window.getComputedStyle(popup).opacity);
    console.log('Popup computed z-index:', window.getComputedStyle(popup).zIndex);
    console.log('Popup computed position:', window.getComputedStyle(popup).position);
    
    // Check for any CSS rules that might be overriding
    const allStyles = window.getComputedStyle(popup);
    console.log('All computed styles for popup:', {
        display: allStyles.display,
        visibility: allStyles.visibility,
        opacity: allStyles.opacity,
        position: allStyles.position,
        zIndex: allStyles.zIndex,
        width: allStyles.width,
        height: allStyles.height
    });
    
    // Try to detect if popup is actually visible
    const rect = popup.getBoundingClientRect();
    console.log('Popup bounding rect:', rect);
    console.log('Is popup visible on screen?', rect.width > 0 && rect.height > 0);
    
    // Check if popup content is visible
    const popupContent = popup.querySelector('.popup-content');
    if (popupContent) {
        const contentRect = popupContent.getBoundingClientRect();
        console.log('Popup content found:', {
            width: contentRect.width,
            height: contentRect.height,
            visible: contentRect.width > 0 && contentRect.height > 0
        });
        
        // Make sure content is styled properly and no blur effects
        popupContent.style.background = 'white';
        popupContent.style.border = '3px solid #3b82f6';
        popupContent.style.borderRadius = '12px';
        popupContent.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
        popupContent.style.zIndex = '100001';
        popupContent.style.filter = 'none';
        popupContent.style.backdropFilter = 'none';
        
        // Also ensure the body behind doesn't have blur
        document.body.style.filter = 'none';
        document.body.style.backdropFilter = 'none';
        
        // Remove any possible blur classes that might be applied by frameworks
        removeBlurEffects();
    } else {
        console.error('Popup content (.popup-content) not found!');
    }
    
        // Double check by setting a background color to see if we can detect it
    setTimeout(() => {
        const finalStyle = window.getComputedStyle(popup);
        console.log('Final check - popup display:', finalStyle.display);
        console.log('Final check - popup visibility:', finalStyle.visibility);
        console.log('Final check - popup classes:', popup.className);
        
        if (finalStyle.display === 'none' || popup.classList.contains('hidden')) {
            console.error('POPUP WAS CLOSED! Something is auto-closing it. Creating alternative popup...');
            createSimpleConfigPopup();
        } else {
            console.log('✅ Popup is still visible and working correctly!');
        }
    }, 500); // Increased delay to 500ms
    
    // Add debugging to track when popup gets closed
    const originalClosePopup = window.closePopup;
    window.closePopup = function(popupId) {
        console.log('⚠️ closePopup called for:', popupId);
        console.trace('Close popup call stack:');
        if (originalClosePopup) {
            return originalClosePopup(popupId);
        }
    };
    
    // Load configurations using functions from ai_config.js
    if (typeof window.loadAIConfigurations === 'function') {
        console.log('Loading AI configurations...');
        window.loadAIConfigurations();
    } else if (typeof loadAIConfigurations === 'function') {
        console.log('Loading AI configurations (global)...');
        loadAIConfigurations();
    } else {
        console.error('loadAIConfigurations function not found - trying to load providers directly');
        // Fallback to load providers directly
        loadProvidersDirectly();
    }
    
    // Switch to providers tab by default
    if (typeof window.switchConfigTab === 'function') {
        console.log('Switching to providers tab...');
        window.switchConfigTab('providers');
    } else if (typeof switchConfigTab === 'function') {
        console.log('Switching to providers tab (global)...');
        switchConfigTab('providers');
    } else {
        console.error('switchConfigTab function not found - activating first tab manually');
        activateFirstTab();
    }
    
    console.log('openAIConfigPopup function completed');
    
    // Prevent escape key from closing popup immediately
    const handleEscape = function(e) {
        if (e.key === 'Escape') {
            console.log('Escape key pressed - preventing default popup close');
            e.stopPropagation();
            e.preventDefault();
            return false;
        }
    };
    
    document.addEventListener('keydown', handleEscape, true);
    
    // Remove the escape handler after 2 seconds to allow normal escape behavior later
    setTimeout(() => {
        document.removeEventListener('keydown', handleEscape, true);
        console.log('Escape key handler removed - normal escape behavior restored');
    }, 2000);
};

// Simple modal functions
function openSimpleModal() {
    console.log('Opening simple AI config modal');
    const modal = document.getElementById('simpleAIConfigModal');
    if (modal) {
        modal.classList.remove('hidden');
        updateSimpleModalStatus();
    } else {
        alert('No configuration interface available. Please use the admin panel.');
    }
}

function closeSimpleModal() {
    const modal = document.getElementById('simpleAIConfigModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

async function updateSimpleModalStatus() {
    try {
        const response = await fetch('/api/ai-config/providers/');
        const data = await response.json();
        const providers = data.providers || [];
        
        const statusText = document.getElementById('aiStatusText');
        if (providers.length > 0) {
            const activeCount = providers.filter(p => p.is_active).length;
            statusText.textContent = `${activeCount} active provider(s) configured`;
            statusText.className = 'font-semibold text-green-600';
        } else {
            statusText.textContent = 'No providers configured';
            statusText.className = 'font-semibold text-red-600';
        }
    } catch (error) {
        const statusText = document.getElementById('aiStatusText');
        statusText.textContent = 'Error loading status';
        statusText.className = 'font-semibold text-red-600';
    }
}

async function quickTestAI() {
    const statusArea = document.getElementById('quickStatusArea');
    statusArea.classList.remove('hidden');
    statusArea.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing AI connection...';
    
    try {
        const response = await fetch('/api/ai-config/providers/');
        const data = await response.json();
        const providers = data.providers || [];
        
        if (providers.length === 0) {
            statusArea.innerHTML = '<div class="text-red-600"><i class="fas fa-exclamation-triangle mr-2"></i>No AI providers configured</div>';
            return;
        }
        
        const activeProviders = providers.filter(p => p.is_active);
        if (activeProviders.length === 0) {
            statusArea.innerHTML = '<div class="text-yellow-600"><i class="fas fa-exclamation-triangle mr-2"></i>No active providers found</div>';
            return;
        }
        
        statusArea.innerHTML = `<div class="text-green-600"><i class="fas fa-check mr-2"></i>Found ${activeProviders.length} active provider(s): ${activeProviders.map(p => p.provider).join(', ')}</div>`;
        
    } catch (error) {
        statusArea.innerHTML = `<div class="text-red-600"><i class="fas fa-times mr-2"></i>Error: ${error.message}</div>`;
    }
}

async function loadProvidersSummary() {
    const statusArea = document.getElementById('quickStatusArea');
    statusArea.classList.remove('hidden');
    statusArea.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading providers...';
    
    try {
        const response = await fetch('/api/ai-config/providers/');
        const data = await response.json();
        const providers = data.providers || [];
        
        if (providers.length === 0) {
            statusArea.innerHTML = '<div class="text-gray-600">No providers configured yet.</div>';
            return;
        }
        
        const providersList = providers.map(p => 
            `<div class="flex justify-between items-center py-1">
                <span>${p.provider} (${p.model_name})</span>
                <span class="text-xs px-2 py-1 rounded ${p.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}">${p.is_active ? 'Active' : 'Inactive'}</span>
            </div>`
        ).join('');
        
        statusArea.innerHTML = `<div><strong>Configured Providers:</strong><div class="mt-2 space-y-1">${providersList}</div></div>`;
        
    } catch (error) {
        statusArea.innerHTML = `<div class="text-red-600">Error loading providers: ${error.message}</div>`;
    }
}

// Global closePopup function for the AI config popup
window.closePopup = function(popupId) {
    console.log('Closing popup:', popupId);
    const popup = document.getElementById(popupId);
    if (popup) {
        popup.classList.add('hidden');
        popup.style.display = 'none';
        console.log('Popup closed successfully');
    } else {
        console.error('Popup not found:', popupId);
    }
}

// Fallback functions if ai_config.js doesn't load properly
function loadProvidersDirectly() {
    console.log('Loading providers directly via fetch...');
    fetch('/api/ai-config/providers/')
        .then(response => response.json())
        .then(data => {
            console.log('Providers loaded:', data);
            const providersList = document.getElementById('providersList');
            if (providersList) {
                if (data.providers && data.providers.length > 0) {
                    providersList.innerHTML = data.providers.map(provider => `
                        <div class="provider-card ${provider.is_active ? 'active' : 'inactive'}">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <div class="flex items-center gap-2 mb-2">
                                        <h5 class="font-semibold text-gray-900">${provider.provider}</h5>
                                        <span class="status-indicator ${provider.is_active ? 'active' : 'inactive'}">
                                            ${provider.is_active ? 'Active' : 'Inactive'}
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-1">
                                        <strong>Model:</strong> ${provider.model_name}
                                    </p>
                                    <p class="text-sm text-gray-600">
                                        <strong>Priority:</strong> ${provider.priority}
                                    </p>
                                </div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    providersList.innerHTML = `
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-cloud text-4xl mb-3 opacity-50"></i>
                            <p>No AI providers configured yet.</p>
                            <p class="text-sm">Use the Django admin panel to configure providers.</p>
                            <a href="/admin/cv_analyzer/aiapiconfig/" class="btn btn-primary mt-3">
                                <i class="fas fa-cog mr-2"></i>Configure via Admin
                            </a>
                        </div>
                    `;
                }
            }
        })
        .catch(error => {
            console.error('Error loading providers:', error);
            const providersList = document.getElementById('providersList');
            if (providersList) {
                providersList.innerHTML = `
                    <div class="text-center py-8 text-red-500">
                        <i class="fas fa-exclamation-triangle text-4xl mb-3"></i>
                        <p>Error loading AI providers</p>
                        <p class="text-sm">${error.message}</p>
                    </div>
                `;
            }
        });
}

function activateFirstTab() {
    console.log('Activating first tab manually...');
    // Show providers tab by default
    const providersTab = document.getElementById('providersTab');
    const settingsTab = document.getElementById('settingsTab');
    const promptsTab = document.getElementById('promptsTab');
    
    if (providersTab) providersTab.classList.remove('hidden');
    if (settingsTab) settingsTab.classList.add('hidden');
    if (promptsTab) promptsTab.classList.add('hidden');
    
    // Activate the providers tab button
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => btn.classList.remove('active'));
    
    const providersButton = document.querySelector('[data-tab="providers"]');
    if (providersButton) {
        providersButton.classList.add('active');
    }
    
    // Load providers
    loadProvidersDirectly();
}

// Global fallback functions for popup functionality
window.switchConfigTab = function(tabName) {
    console.log('Switching to tab:', tabName);
    
    // Hide all tab contents
    document.querySelectorAll('.config-tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected tab
    const selectedTab = document.getElementById(tabName + 'Tab');
    if (selectedTab) {
        selectedTab.classList.remove('hidden');
    }
    
    // Add active class to selected tab button
    const selectedButton = document.querySelector(`[data-tab="${tabName}"]`);
    if (selectedButton) {
        selectedButton.classList.add('active');
    }
    
    // Load data for the selected tab
    switch(tabName) {
        case 'providers':
            loadProvidersDirectly();
            break;
        case 'settings':
            loadGeneralSettingsDirectly();
            break;
        case 'prompts':
            loadPromptsDirectly();
            break;
    }
}

function loadGeneralSettingsDirectly() {
    console.log('Loading general settings...');
    fetch('/api/ai-config/general/')
        .then(response => response.json())
        .then(data => {
            console.log('General settings loaded:', data);
            // Populate the form fields if they exist
            if (data.cache_ttl && document.getElementById('cacheTtl')) {
                document.getElementById('cacheTtl').value = data.cache_ttl;
            }
            if (data.batch_size && document.getElementById('batchSize')) {
                document.getElementById('batchSize').value = data.batch_size;
            }
            if (data.max_retries && document.getElementById('maxRetries')) {
                document.getElementById('maxRetries').value = data.max_retries;
            }
            if (data.retry_delay && document.getElementById('retryDelay')) {
                document.getElementById('retryDelay').value = data.retry_delay;
            }
        })
        .catch(error => {
            console.error('Error loading general settings:', error);
        });
}

function loadPromptsDirectly() {
    console.log('Loading prompts...');
    fetch('/api/ai-config/prompts/')
        .then(response => response.json())
        .then(data => {
            console.log('Prompts loaded:', data);
            const promptsList = document.getElementById('promptsList');
            if (promptsList) {
                if (data.prompts && data.prompts.length > 0) {
                    promptsList.innerHTML = data.prompts.map(prompt => `
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h5 class="font-semibold text-gray-900 mb-2">${prompt.name}</h5>
                            <p class="text-sm text-gray-600 mb-2">${prompt.instructions || 'No instructions provided'}</p>
                            <div class="text-xs text-gray-500">
                                ${prompt.prompt.substring(0, 100)}${prompt.prompt.length > 100 ? '...' : ''}
                            </div>
                        </div>
                    `).join('');
                } else {
                    promptsList.innerHTML = `
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-edit text-4xl mb-3 opacity-50"></i>
                            <p>No prompts configured yet.</p>
                            <p class="text-sm">Use the Django admin panel to configure prompts.</p>
                        </div>
                    `;
                }
            }
        })
        .catch(error => {
            console.error('Error loading prompts:', error);
            const promptsList = document.getElementById('promptsList');
            if (promptsList) {
                promptsList.innerHTML = `
                    <div class="text-center py-8 text-red-500">
                        <i class="fas fa-exclamation-triangle text-4xl mb-3"></i>
                        <p>Error loading prompts</p>
                        <p class="text-sm">${error.message}</p>
                    </div>
                `;
            }
        });
}

// Fallback functions for buttons that might not have handlers
window.addNewProvider = function() {
    alert('Add Provider functionality is available in the Django admin panel.\n\nGo to: /admin/cv_analyzer/aiapiconfig/');
}

window.addNewPrompt = function() {
    alert('Add Prompt functionality is available in the Django admin panel.\n\nGo to: /admin/cv_analyzer/aipromptconfig/');
}

window.saveProvider = function() {
    alert('Save Provider functionality is available in the Django admin panel.');
}

window.savePrompt = function() {
    alert('Save Prompt functionality is available in the Django admin panel.');
}

window.saveGeneralSettings = function() {
    alert('Save General Settings functionality is available in the Django admin panel.');
}

// Function to remove all possible blur effects
function removeBlurEffects() {
    console.log('Removing all blur effects...');
    
    // Remove blur from common elements
    const elementsToCheck = [
        document.body,
        document.documentElement,
        document.querySelector('main'),
        document.querySelector('.container'),
        document.querySelector('.app'),
        ...document.querySelectorAll('[class*="blur"]'),
        ...document.querySelectorAll('[style*="blur"]'),
        ...document.querySelectorAll('[style*="filter"]')
    ];
    
    elementsToCheck.forEach(el => {
        if (el) {
            el.style.filter = 'none !important';
            el.style.backdropFilter = 'none !important';
            el.style.webkitFilter = 'none !important';
            el.style.webkitBackdropFilter = 'none !important';
            
            // Remove common blur classes
            const blurClasses = ['blur', 'blur-sm', 'blur-md', 'blur-lg', 'blur-xl', 'blur-2xl', 'blur-3xl'];
            blurClasses.forEach(cls => el.classList.remove(cls));
            
            console.log('Removed blur from:', el.tagName || el.className);
        }
    });
    
    // Force remove backdrop-filter from all elements
    const allElements = document.querySelectorAll('*');
    allElements.forEach(el => {
        const computedStyle = window.getComputedStyle(el);
        if (computedStyle.backdropFilter && computedStyle.backdropFilter !== 'none') {
            el.style.backdropFilter = 'none !important';
            console.log('Removed backdrop-filter from:', el.tagName, el.className);
        }
        if (computedStyle.filter && computedStyle.filter !== 'none' && computedStyle.filter.includes('blur')) {
            el.style.filter = 'none !important';
            console.log('Removed filter blur from:', el.tagName, el.className);
        }
    });
    
    console.log('All blur effects removed');
}

// Emergency function to force show popup
function emergencyShowPopup(popup) {
    console.log('EMERGENCY: Forcing popup to show...');
    
    // Remove the popup from DOM and re-add it
    const parent = popup.parentNode;
    const nextSibling = popup.nextSibling;
    parent.removeChild(popup);
    
    // Clear all classes and styles
    popup.className = 'popup-overlay';
    popup.removeAttribute('style');
    
    // Re-add to DOM
    parent.insertBefore(popup, nextSibling);
    
    // Force show with direct styling
    popup.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(255, 0, 0, 0.8) !important;
        z-index: 999999 !important;
        display: block !important;
        color: white !important;
        font-size: 24px !important;
        text-align: center !important;
        padding: 50px !important;
    `;
    
    popup.innerHTML = `
        <div style="background: white; color: black; padding: 20px; border-radius: 10px; max-width: 600px; margin: auto;">
            <h2>AI Configuration</h2>
            <p>The popup is now visible! Let me restore the proper content...</p>
            <button onclick="location.reload()" style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">
                Reload Page
            </button>
            <button onclick="document.getElementById('aiConfigPopup').style.display='none'" style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                Close
            </button>
        </div>
    `;
    
    console.log('Emergency popup should now be visible with red background');
}

// Alternative simple popup creator
function createSimpleConfigPopup() {
    // Remove any existing popup
    const existing = document.getElementById('simpleConfigPopup');
    if (existing) existing.remove();
    
    // Create new popup element
    const popup = document.createElement('div');
    popup.id = 'simpleConfigPopup';
    popup.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(0, 0, 0, 0.7) !important;
        z-index: 999999 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 20px !important;
    `;
    
    popup.innerHTML = `
        <div style="background: white; border-radius: 12px; max-width: 800px; width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
            <div style="padding: 24px; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center;">
                <h3 style="margin: 0; font-size: 20px; font-weight: bold; color: #1f2937;">
                    <i class="fas fa-robot" style="color: #3b82f6; margin-right: 8px;"></i>
                    AI Configuration Center
                </h3>
                <button onclick="document.getElementById('simpleConfigPopup').remove()" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #6b7280; padding: 8px; border-radius: 6px;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div style="padding: 24px;">
                <div id="simpleConfigContent">
                    <div style="text-align: center; padding: 20px;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: #3b82f6; margin-bottom: 10px;"></i>
                        <p>Loading AI configuration...</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(popup);
    
    // Load the configuration data
    loadSimpleConfigContent();
    
    return popup;
}

function loadSimpleConfigContent() {
    fetch('/api/ai-config/providers/')
        .then(response => response.json())
        .then(data => {
            const content = document.getElementById('simpleConfigContent');
            if (!content) return;
            
            const providers = data.providers || [];
            
            content.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <h4 style="font-size: 18px; font-weight: 600; margin-bottom: 15px; color: #1f2937;">
                        AI Providers Status
                    </h4>
                    ${providers.length > 0 ? `
                        <div style="space-y: 12px;">
                            ${providers.map(provider => `
                                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px; margin-bottom: 12px; ${provider.is_active ? 'background: #f0fdf4; border-color: #10b981;' : 'background: #f9fafb;'}">
                                    <div style="display: flex; justify-content: space-between; align-items: start;">
                                        <div>
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <h5 style="margin: 0; font-weight: 600; color: #1f2937;">${provider.provider}</h5>
                                                <span style="font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500; ${provider.is_active ? 'background: #dcfce7; color: #166534;' : 'background: #f3f4f6; color: #6b7280;'}">
                                                    ${provider.is_active ? 'Active' : 'Inactive'}
                                                </span>
                                                ${provider.priority === 1 ? '<span style="background: #dbeafe; color: #1d4ed8; font-size: 12px; padding: 4px 8px; border-radius: 4px;">Primary</span>' : ''}
                                            </div>
                                            <p style="margin: 4px 0; font-size: 14px; color: #6b7280;">
                                                <strong>Model:</strong> ${provider.model_name}
                                            </p>
                                            <p style="margin: 4px 0; font-size: 14px; color: #6b7280;">
                                                <strong>Priority:</strong> ${provider.priority} | 
                                                <strong>Tokens:</strong> ${provider.max_tokens} | 
                                                <strong>Temperature:</strong> ${provider.temperature}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    ` : `
                        <div style="text-align: center; padding: 40px; color: #6b7280;">
                            <i class="fas fa-cloud" style="font-size: 48px; opacity: 0.5; margin-bottom: 16px;"></i>
                            <p style="margin-bottom: 8px;">No AI providers configured yet.</p>
                            <p style="font-size: 14px; margin-bottom: 20px;">Configure providers to start using AI features.</p>
                            <a href="/admin/cv_analyzer/aiapiconfig/" target="_blank" style="display: inline-block; padding: 10px 20px; background: #3b82f6; color: white; text-decoration: none; border-radius: 6px; font-weight: 500;">
                                <i class="fas fa-cog" style="margin-right: 8px;"></i>Configure via Admin Panel
                            </a>
                        </div>
                    `}
                </div>
                
                <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center;">
                    <p style="color: #6b7280; font-size: 14px; margin-bottom: 15px;">
                        For advanced configuration, use the Django admin panel.
                    </p>
                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <a href="/admin/cv_analyzer/aiapiconfig/" target="_blank" style="padding: 8px 16px; background: #6b7280; color: white; text-decoration: none; border-radius: 4px; font-size: 14px;">
                            <i class="fas fa-cog" style="margin-right: 4px;"></i>AI Providers
                        </a>
                        <a href="/admin/cv_analyzer/aiconfig/" target="_blank" style="padding: 8px 16px; background: #6b7280; color: white; text-decoration: none; border-radius: 4px; font-size: 14px;">
                            <i class="fas fa-sliders-h" style="margin-right: 4px;"></i>General Settings
                        </a>
                        <a href="/admin/cv_analyzer/aipromptconfig/" target="_blank" style="padding: 8px 16px; background: #6b7280; color: white; text-decoration: none; border-radius: 4px; font-size: 14px;">
                            <i class="fas fa-edit" style="margin-right: 4px;"></i>Prompts
                        </a>
                    </div>
                </div>
            `;
        })
        .catch(error => {
            const content = document.getElementById('simpleConfigContent');
            if (content) {
                content.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #dc2626;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 16px;"></i>
                        <p style="margin-bottom: 8px;">Error loading AI configuration</p>
                        <p style="font-size: 14px; margin-bottom: 20px;">${error.message}</p>
                        <button onclick="loadSimpleConfigContent()" style="padding: 10px 20px; background: #dc2626; color: white; border: none; border-radius: 6px; cursor: pointer;">
                            <i class="fas fa-redo" style="margin-right: 8px;"></i>Retry
                        </button>
                    </div>
                `;
            }
        });
}

// Test the popup on page load for debugging
document.addEventListener('DOMContentLoaded', function() {
    console.log('AI Analysis Hub loaded');
    
    // Check if popup exists
    const popup = document.getElementById('aiConfigPopup');
    if (popup) {
        console.log('AI Config popup found in DOM');
    } else {
        console.error('AI Config popup NOT found in DOM');
    }
    
    // Test if openAIConfigPopup function exists
    if (typeof openAIConfigPopup === 'function') {
        console.log('openAIConfigPopup function is available');
    } else {
        console.error('openAIConfigPopup function NOT available');
    }
});
let selectedCVs = [];
let selectedVacancies = [];

// Selection management
function selectAllCVs() {
    const checkboxes = document.querySelectorAll('.cv-checkbox');
    const allSelected = Array.from(checkboxes).every(cb => cb.checked);
    
    checkboxes.forEach(cb => {
        cb.checked = !allSelected;
        updateCVSelection(cb);
    });
}

function selectAllVacancies() {
    const checkboxes = document.querySelectorAll('.vacancy-checkbox');
    const allSelected = Array.from(checkboxes).every(cb => cb.checked);
    
    checkboxes.forEach(cb => {
        cb.checked = !allSelected;
        updateVacancySelection(cb);
    });
}

function updateCVSelection(checkbox) {
    const cvId = parseInt(checkbox.dataset.cvId);
    if (checkbox.checked) {
        if (!selectedCVs.includes(cvId)) {
            selectedCVs.push(cvId);
        }
    } else {
        selectedCVs = selectedCVs.filter(id => id !== cvId);
    }
}

function updateVacancySelection(checkbox) {
    const vacancyId = parseInt(checkbox.dataset.vacancyId);
    if (checkbox.checked) {
        if (!selectedVacancies.includes(vacancyId)) {
            selectedVacancies.push(vacancyId);
        }
    } else {
        selectedVacancies = selectedVacancies.filter(id => id !== vacancyId);
    }
}

// Analysis functions
async function analyzeSingleCV(cvId) {
    showProgress();
    updateProgress(20, 'Analyzing CV...');
    
    try {
        const response = await fetch('/api/start-ai-analysis/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                cv_ids: [cvId],
                analysis_type: 'analyze'
            })
        });
        
        const result = await response.json();
        updateProgress(100, 'Analysis completed!');
        
        setTimeout(() => {
            hideProgress();
            if (result.success) {
                showAlert('CV analysis completed successfully!', 'success');
                location.reload();
            } else {
                showAlert(result.message, 'error');
            }
        }, 1000);
        
    } catch (error) {
        hideProgress();
        showAlert('Error running analysis: ' + error.message, 'error');
    }
}

async function analyzeSelectedCVs() {
    if (selectedCVs.length === 0) {
        showAlert('Please select at least one CV to analyze', 'warning');
        return;
    }
    
    showProgress();
    updateProgress(20, `Analyzing ${selectedCVs.length} CVs...`);
    
    try {
        const response = await fetch('/api/start-ai-analysis/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                cv_ids: selectedCVs,
                analysis_type: 'analyze'
            })
        });
        
        const result = await response.json();
        updateProgress(100, `Analyzed ${result.successful || 0} CVs successfully!`);
        
        setTimeout(() => {
            hideProgress();
            if (result.success) {
                showAlert(`Analysis completed! ${result.successful}/${result.total_processed} CVs processed successfully.`, 'success');
                location.reload();
            } else {
                showAlert(result.message, 'error');
            }
        }, 1500);
        
    } catch (error) {
        hideProgress();
        showAlert('Error running bulk analysis: ' + error.message, 'error');
    }
}

async function matchSelected() {
    if (selectedCVs.length === 0) {
        showAlert('Please select at least one CV', 'warning');
        return;
    }
    
    if (selectedVacancies.length === 0) {
        showAlert('Please select at least one vacancy', 'warning');
        return;
    }
    
    showProgress();
    updateProgress(20, `Matching ${selectedCVs.length} CVs to ${selectedVacancies.length} vacancies...`);
    
    try {
        const response = await fetch('/api/start-ai-analysis/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                cv_ids: selectedCVs,
                vacancy_ids: selectedVacancies,
                analysis_type: 'match'
            })
        });
        
        const result = await response.json();
        updateProgress(100, `Matching completed! ${result.successful || 0} matches created.`);
        
        setTimeout(() => {
            hideProgress();
            if (result.success) {
                showAlert(`Matching completed! ${result.successful}/${result.total_processed} analyses created successfully.`, 'success');
                location.reload();
            } else {
                showAlert(result.message, 'error');
            }
        }, 1500);
        
    } catch (error) {
        hideProgress();
        showAlert('Error running matching: ' + error.message, 'error');
    }
}

function startBulkAnalysis() {
    if (selectedCVs.length === 0 && selectedVacancies.length === 0) {
        showAlert('Please select CVs and/or vacancies to analyze', 'warning');
        return;
    }
    
    if (selectedCVs.length > 0 && selectedVacancies.length > 0) {
        matchSelected();
    } else if (selectedCVs.length > 0) {
        analyzeSelectedCVs();
    } else {
        showAlert('Please select some CVs to analyze', 'warning');
    }
}

// Progress modal functions
function showProgress() {
    document.getElementById('progressModal').classList.remove('hidden');
}

function hideProgress() {
    document.getElementById('progressModal').classList.add('hidden');
    updateProgress(0, 'Initializing...');
}

function updateProgress(percentage, text) {
    document.getElementById('progressFill').style.width = percentage + '%';
    document.getElementById('progressText').textContent = text;
}

// Alert function
function showAlert(message, type = 'info') {
    const alert = document.createElement('div');
    alert.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
    }`;
    alert.textContent = message;
    
    document.body.appendChild(alert);
    setTimeout(() => alert.remove(), 5000);
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // CV checkboxes
    document.querySelectorAll('.cv-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateCVSelection(this);
        });
    });
    
    // Vacancy checkboxes
    document.querySelectorAll('.vacancy-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateVacancySelection(this);
        });
    });
    
    // Add CSRF token if not present
    if (!document.querySelector('[name=csrfmiddlewaretoken]')) {
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrfmiddlewaretoken';
        csrf.value = '{{ csrf_token }}';
        document.body.appendChild(csrf);
    }
});
</script>
{% endblock %}