{% extends 'cv_analyzer/base.html' %}
{% load static %}

{% block title %}Analysis Results{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header Section -->
    <div class="mb-8">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">CV Analysis Results</h1>
                <p class="mt-2 text-gray-600 dark:text-gray-400">
                    Analysis for {{ analysis.candidate_name }} - {{ analysis.vacancy.title }}
                </p>
            </div>
            <div class="flex gap-3">
                <button type="button" onclick="window.print()" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                    </svg>
                    Print Report
                </button>
                <button type="button" id="shareButton" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"></path>
                    </svg>
                    Share Results
                </button>
            </div>
        </div>
    </div>

    <!-- Overall Score Card -->
    <div class="mb-8">
        <div class="bg-white rounded-lg shadow-sm dark:bg-gray-800 p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Overall Score</h2>
                <div class="relative inline-flex items-center justify-center">
                    <svg class="w-20 h-20">
                        <circle class="text-gray-200 dark:text-gray-700" stroke="currentColor" stroke-width="8" fill="none" r="32" cx="40" cy="40"/>
                        <circle class="text-blue-600 dark:text-blue-500" stroke="currentColor" stroke-width="8" fill="none" r="32" cx="40" cy="40"
                            style="stroke-dasharray: 201.06; stroke-dashoffset: {{ 201.06|add:(-201.06|multiply:analysis.overall_score|divide:100) }}"/>
                    </svg>
                    <span class="absolute text-xl font-bold text-gray-900 dark:text-white">{{ analysis.overall_score }}%</span>
                </div>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Content Score</p>
                    <p class="mt-2 text-lg font-semibold text-gray-900 dark:text-white">{{ analysis.content_score }}%</p>
                </div>
                <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Format Score</p>
                    <p class="mt-2 text-lg font-semibold text-gray-900 dark:text-white">{{ analysis.format_score }}%</p>
                </div>
                <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Skills Match</p>
                    <p class="mt-2 text-lg font-semibold text-gray-900 dark:text-white">{{ analysis.skills_score }}%</p>
                </div>
                <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Experience Match</p>
                    <p class="mt-2 text-lg font-semibold text-gray-900 dark:text-white">{{ analysis.experience_score }}%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analysis -->
    <div class="grid gap-8 mb-8 md:grid-cols-2">
        <!-- Skills Analysis -->
        <div class="bg-white rounded-lg shadow-sm dark:bg-gray-800 p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Skills Analysis</h2>
            <div class="space-y-4">
                {% for skill in analysis.skills_analysis %}
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ skill.name }}</span>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ skill.score }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div class="bg-blue-600 h-2.5 rounded-full dark:bg-blue-500" style="width: {{ skill.score }}%"></div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Experience Analysis -->
        <div class="bg-white rounded-lg shadow-sm dark:bg-gray-800 p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Experience Analysis</h2>
            <div class="space-y-4">
                {% for exp in analysis.experience_analysis %}
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ exp.role }}</span>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ exp.relevance }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div class="bg-green-600 h-2.5 rounded-full dark:bg-green-500" style="width: {{ exp.relevance }}%"></div>
                    </div>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">{{ exp.duration }} at {{ exp.company }}</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Education and Certifications -->
    <div class="grid gap-8 mb-8 md:grid-cols-2">
        <!-- Education -->
        <div class="bg-white rounded-lg shadow-sm dark:bg-gray-800 p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Education</h2>
            <div class="space-y-4">
                {% for edu in analysis.education %}
                <div class="border-l-4 border-blue-500 pl-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ edu.degree }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ edu.institution }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-500">{{ edu.year }}</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Certifications -->
        <div class="bg-white rounded-lg shadow-sm dark:bg-gray-800 p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Certifications</h2>
            <div class="space-y-4">
                {% for cert in analysis.certifications %}
                <div class="border-l-4 border-green-500 pl-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ cert.name }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ cert.issuer }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-500">{{ cert.year }}</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- AI Recommendations -->
    <div class="mb-8">
        <div class="bg-white rounded-lg shadow-sm dark:bg-gray-800 p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">AI Recommendations</h2>
            <div class="space-y-4">
                {% for rec in analysis.recommendations %}
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ rec.title }}</h3>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">{{ rec.description }}</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Share Modal -->
    <div id="shareModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Share Analysis Results</h3>
                <div class="mt-4">
                    <input type="text" id="shareLink" readonly value="{{ request.build_absolute_uri }}" class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                    <div class="mt-4 flex justify-center gap-4">
                        <button type="button" onclick="copyShareLink()" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600">
                            Copy Link
                        </button>
                        <button type="button" onclick="closeShareModal()" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Share modal functionality
    const shareButton = document.getElementById('shareButton');
    const shareModal = document.getElementById('shareModal');
    
    shareButton.addEventListener('click', function() {
        shareModal.classList.remove('hidden');
    });

    window.closeShareModal = function() {
        shareModal.classList.add('hidden');
    }

    window.copyShareLink = function() {
        const shareLink = document.getElementById('shareLink');
        shareLink.select();
        document.execCommand('copy');
        alert('Link copied to clipboard!');
    }

    // Close modal when clicking outside
    shareModal.addEventListener('click', function(e) {
        if (e.target === shareModal) {
            closeShareModal();
        }
    });
});
</script>
{% endblock %} 