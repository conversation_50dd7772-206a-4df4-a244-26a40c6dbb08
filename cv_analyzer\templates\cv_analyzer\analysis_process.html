{% extends "cv_analyzer/base.html" %}

{% block content %}
<div class="container">
    <h2 class="mb-4">Analysis Process</h2>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Job Title</th>
                <th>CV</th>
                <th>Status</th>
                <th>Started At</th>
                <th>Completed At</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            {% for process in processes %}
            <tr>
                <td>{{ process.job.title }}</td>
                <td>{{ process.cv.file.name }}</td>
                <td>{{ process.get_status_display }}</td>
                <td>{{ process.started_at }}</td>
                <td>{{ process.completed_at|default_if_none:"-" }}</td>
                <td>
                    {% if process.status != 'completed' and process.status != 'failed' %}
                    <a href="{% url 'update_analysis_status' process.id %}" class="btn btn-sm btn-primary">Update Status</a>
                    {% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6">No analysis processes found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
