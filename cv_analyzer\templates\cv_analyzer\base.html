{% load static %}
<!DOCTYPE html>
<html lang="en" data-theme="{{ theme|default:'light' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>{% block title %}CV Analyzer{% endblock %}</title>
    
    <!-- Favicon - Using Font Awesome -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><path fill='%234A90E2' d='M64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V160H256c-17.7 0-32-14.3-32-32V0H64zM256 0V128H384L256 0zM112 256H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16z'/></svg>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            "50": "#eff6ff",
                            "100": "#dbeafe",
                            "200": "#bfdbfe",
                            "300": "#93c5fd",
                            "400": "#60a5fa",
                            "500": "#3b82f6",
                            "600": "#2563eb",
                            "700": "#1d4ed8",
                            "800": "#1e40af",
                            "900": "#1e3a8a",
                            "950": "#172554"
                        }
                    }
                },
                fontFamily: {
                    'body': [
                        'Inter', 
                        'ui-sans-serif', 
                        'system-ui'
                    ],
                    'sans': [
                        'Inter', 
                        'ui-sans-serif', 
                        'system-ui'
                    ]
                }
            }
        }
    </script>
    
    <!-- Flowbite CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{% static 'cv_analyzer/css/styles.css' %}" rel="stylesheet">
    
    <!-- Navigation CSS Fixes -->
    <style>
    /* Prevent CSS conflicts and ensure navigation works properly */
    .nav-link {
        transition: all 0.2s ease !important;
        position: relative;
    }
    
    .nav-link.active,
    .nav-link.bg-blue-600 {
        background-color: #2563eb !important;
        color: white !important;
    }
    
    .nav-link.active i,
    .nav-link.bg-blue-600 i {
        color: white !important;
    }
    
    /* Mobile menu positioning */
    #mobile-menu {
        z-index: 60;
        max-height: 50vh;
        overflow-y: auto;
    }
    
    /* Prevent theme conflicts */
    [data-theme="dark"] .nav-link.bg-blue-600 {
        background-color: #2563eb !important;
        color: white !important;
    }
    
    [data-theme="dark"] .nav-link.bg-blue-600 i {
        color: white !important;
    }
    </style>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- In the head section -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <!-- Navbar -->
    <nav class="fixed top-0 z-50 w-full bg-white border-b border-gray-200 dark:bg-gray-800 dark:border-gray-700">
        <div class="px-3 py-3 lg:px-5 lg:pl-3">
                            <div class="flex items-center justify-between">
                    <!-- Left: Logo -->
                    <div class="flex items-center justify-start rtl:justify-end">
                        <a href="{% url 'welcome' %}" class="flex">
                            <div class="flex items-center justify-center w-10 h-10 me-3 bg-blue-100 rounded-lg dark:bg-blue-900">
                                <i class="fas fa-file-lines text-2xl text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <span class="self-center text-2xl font-bold whitespace-nowrap dark:text-white tracking-tight">CV Analyzer</span>
                        </a>
                        
                        <!-- Mobile Navigation Dropdown -->
                        {% if user.is_authenticated %}
                        <div class="md:hidden ml-4">
                            <button id="mobile-menu-button" type="button" class="inline-flex items-center p-2 text-sm text-gray-500 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600">
                                <span class="sr-only">Open mobile menu</span>
                                {% include 'cv_analyzer/components/icon.html' with icon='fa-bars' solid=True %}
                            </button>
                            <div id="mobile-menu" class="hidden absolute top-full left-0 right-0 bg-white border-b border-gray-200 dark:bg-gray-800 dark:border-gray-700">
                                <div class="px-4 py-2 space-y-2">
                                    <a href="{% url 'dashboard' %}" class="nav-link block px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                                        {% include 'cv_analyzer/components/icon.html' with icon='fa-gauge' solid=True class='me-2 text-gray-500 dark:text-gray-400' %}
                                         Dashboard
                                    </a>
                                    <a href="{% url 'operations_hub' %}" class="nav-link block px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                                        {% include 'cv_analyzer/components/icon.html' with icon='fa-cogs' solid=True class='me-2 text-gray-500 dark:text-gray-400' %}
                                        Operations Hub
                                    </a>
                                                            <a href="{% url 'vacancy_management' %}" class="nav-link block px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                            {% include 'cv_analyzer/components/icon.html' with icon='fa-cogs' solid=True class='me-2 text-gray-500 dark:text-gray-400' %}
                            Vacancy Management
                        </a>
                        <a href="{% url 'ai_analysis_hub' %}" class="nav-link block px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                            {% include 'cv_analyzer/components/icon.html' with icon='fa-robot' solid=True class='me-2 text-gray-500 dark:text-gray-400' %}
                            AI Analysis Hub
                        </a>
                                    {% if user.is_staff %}
                                    <a href="{% url 'admin:index' %}" class="nav-link block px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                                        {% include 'cv_analyzer/components/icon.html' with icon='fa-cog' solid=True class='me-2 text-gray-500 dark:text-gray-400' %}
                                        Admin
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Center: Main Navigation Links -->
                    {% if user.is_authenticated %}
                    <div class="hidden md:flex items-center space-x-8 absolute left-1/2 transform -translate-x-1/2">
                        <a href="{% url 'dashboard' %}" class="nav-link flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                            {% include 'cv_analyzer/components/icon.html' with icon='fa-gauge' solid=True class='me-2 text-gray-500 dark:text-gray-400' %}
                            Dashboard
                        </a>
                        <a href="{% url 'operations_hub' %}" class="nav-link flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                            {% include 'cv_analyzer/components/icon.html' with icon='fa-cogs' solid=True class='me-2 text-gray-500 dark:text-gray-400' %}
                            Operations Hub
                        </a>
                        <a href="{% url 'vacancy_management' %}" class="nav-link flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                            {% include 'cv_analyzer/components/icon.html' with icon='fa-cogs' solid=True class='me-2 text-gray-500 dark:text-gray-400' %}
                            Vacancy Management
                        </a>
                        <a href="{% url 'ai_analysis_hub' %}" class="nav-link flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700">
                            {% include 'cv_analyzer/components/icon.html' with icon='fa-robot' solid=True class='me-2 text-gray-500 dark:text-gray-400' %}
                            AI Analysis Hub
                        </a>
                    </div>
                    {% endif %}
                    
                                        <!-- Right: Admin, Theme Toggle, User Menu -->
                    <div class="flex items-center">
                        <div class="flex items-center ms-3">
                            {% if user.is_authenticated and user.is_staff %}
                            <a href="{% url 'admin:index' %}" class="nav-link text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 me-2" title="Admin">
                                {% include 'cv_analyzer/components/icon.html' with icon='fa-cog' solid=True %}
                            </a>
                            {% endif %}
                            
                            <!-- Theme Toggle -->
                            <button id="theme-toggle" type="button" class="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 me-2" title="Toggle theme">
                                <svg id="theme-toggle-dark-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path></svg>
                                <svg id="theme-toggle-light-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path></svg>
                            </button>
                          
                        {% if user.is_authenticated %}
                        <div class="flex items-center ms-3">
                            <div>
                                <button type="button" class="flex text-sm bg-gray-800 rounded-full focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600" aria-expanded="false" data-dropdown-toggle="dropdown-user">
                                    <span class="sr-only">Open user menu</span>
                                    {% include 'cv_analyzer/components/icon.html' with icon='fa-user-circle' solid=True class='text-2xl text-white' %}
                                </button>
                            </div>
                            <div class="z-50 hidden my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600" id="dropdown-user">
                                <div class="px-4 py-3" role="none">
                                    <p class="text-sm text-gray-900 dark:text-white" role="none">
                                        {{ user.get_full_name|default:user.username }}
                                    </p>
                                    <p class="text-sm font-medium text-gray-900 truncate dark:text-gray-300" role="none">
                                        {{ user.email }}
                                    </p>
                                </div>
                                <ul class="py-1" role="none">
                                    <li>
                                        <a href="{% url 'upload_cv' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">
                                            {% include 'cv_analyzer/components/icon.html' with icon='fa-upload' solid=True class='me-2' %}
                                            Upload CV
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{% url 'vacancy_list' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">
                                            {% include 'cv_analyzer/components/icon.html' with icon='fa-briefcase' solid=True class='me-2' %}
                                            Vacancies
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{% url 'cv_management' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">
                                            {% include 'cv_analyzer/components/icon.html' with icon='fa-file-alt' solid=True class='me-2' %}
                                            CV Management
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{% url 'company_management' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">
                                            {% include 'cv_analyzer/components/icon.html' with icon='fa-building' solid=True class='me-2' %}
                                            Companies
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{% url 'logout' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">
                                            {% include 'cv_analyzer/components/icon.html' with icon='fa-sign-out-alt' solid=True class='me-2' %}
                                            Sign out
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main content -->
    <div class="p-4 mt-14">
        <div class="p-4 border-2 border-gray-200 border-dashed rounded-lg dark:border-gray-700">
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Flowbite JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{% static 'cv_analyzer/js/scripts.js' %}"></script>
    
    <!-- Navigation JavaScript -->
    <script>
    // Wrap in IIFE to avoid global scope pollution
    (function() {
        'use strict';
        
        // Wait for DOM and other scripts to load
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initNavigation);
        } else {
            initNavigation();
        }
        
        function initNavigation() {
            try {
                setupMobileMenu();
                setupActiveNavigation();
            } catch (error) {
                console.warn('Navigation setup error:', error);
            }
        }
        
        function setupMobileMenu() {
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            
            if (!mobileMenuButton || !mobileMenu) return;
            
            mobileMenuButton.addEventListener('click', function(e) {
                e.stopPropagation();
                mobileMenu.classList.toggle('hidden');
            });
            
            // Close mobile menu when clicking outside
            document.addEventListener('click', function(event) {
                if (!mobileMenuButton.contains(event.target) && !mobileMenu.contains(event.target)) {
                    mobileMenu.classList.add('hidden');
                }
            });
        }
        
        function setupActiveNavigation() {
            const currentPath = window.location.pathname;
            
            // Simple active link detection
            const navLinks = document.querySelectorAll('.nav-link');
            
            navLinks.forEach(link => {
                const href = link.getAttribute('href') || '';
                let isActive = false;
                
                // Check if this link should be active
                if (href.includes('dashboard') && (currentPath.includes('/dashboard') || currentPath === '/')) {
                    isActive = true;
                } else if (href.includes('operations-hub') && currentPath.includes('/operations-hub')) {
                    isActive = true;
                } else if (href.includes('admin:index') && currentPath.includes('/admin/')) {
                    isActive = true;
                }
                
                if (isActive) {
                    setActiveLink(link);
                } else {
                    resetLink(link);
                }
            });
            
            // Handle admin icon separately
            if (currentPath.includes('/admin/')) {
                const adminIcon = document.querySelector('a[href*="admin:index"]:not(.nav-link)');
                if (adminIcon) {
                    setActiveIcon(adminIcon);
                }
            }
        }
        
        function setActiveLink(link) {
            // Add active styling
            link.classList.add('bg-blue-600', 'text-white');
            link.classList.remove('text-gray-700', 'hover:bg-gray-100');
            
            // Update icon color with simple approach
            const icon = link.querySelector('i');
            if (icon) {
                icon.style.color = 'white';
            }
        }
        
        function resetLink(link) {
            // Remove active styling
            link.classList.remove('bg-blue-600', 'text-white');
            link.classList.add('text-gray-700', 'hover:bg-gray-100');
            
            // Reset icon color
            const icon = link.querySelector('i');
            if (icon) {
                icon.style.color = '';
            }
        }
        
        function setActiveIcon(iconLink) {
            iconLink.classList.add('bg-blue-600', 'text-white');
            iconLink.classList.remove('text-gray-500');
            
            const icon = iconLink.querySelector('i');
            if (icon) {
                icon.style.color = 'white';
            }
        }
        
    })();
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>