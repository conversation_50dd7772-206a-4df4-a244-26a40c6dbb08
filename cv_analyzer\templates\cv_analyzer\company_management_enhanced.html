{% extends "cv_analyzer/base.html" %}
{% load static %}

{% block title %}Company Management - CV Analyzer{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .page-header {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        padding: 3rem 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .process-flow {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 2rem 0;
    }
    
    .process-step {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid #e5e7eb;
        position: relative;
        transition: all 0.3s ease;
    }
    
    .process-step:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
    
    .dark .process-step {
        background: #1f2937;
        border-color: #374151;
    }
    
    .process-step::after {
        content: '→';
        position: absolute;
        right: -15px;
        top: 50%;
        transform: translateY(-50%);
        color: #3b82f6;
        font-size: 1.5rem;
        font-weight: bold;
    }
    
    .process-step:last-child::after {
        display: none;
    }
    
    @media (max-width: 768px) {
        .process-step::after {
            content: '↓';
            right: 50%;
            top: auto;
            bottom: -15px;
            transform: translateX(50%);
        }
    }

    .metric-card {
        background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
        border: none;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        border-radius: 0.75rem;
        padding: 1.5rem;
        color: white;
    }
    
    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .metric-card:hover::before {
        opacity: 1;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }
    
    .metric-card.blue {
        --gradient-start: #3b82f6;
        --gradient-end: #1d4ed8;
    }
    
    .metric-card.green {
        --gradient-start: #10b981;
        --gradient-end: #047857;
    }
    
    .metric-card.purple {
        --gradient-start: #8b5cf6;
        --gradient-end: #6d28d9;
    }
    
    .metric-card.orange {
        --gradient-start: #f59e0b;
        --gradient-end: #d97706;
    }

    .content-grid {
        display: grid;
        grid-template-columns: 1fr 350px;
        gap: 2rem;
        align-items: start;
    }
    
    @media (max-width: 1280px) {
        .content-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
    }

    .main-content {
        min-height: 0;
    }

    .sidebar-content {
        position: sticky;
        top: 2rem;
        max-height: calc(100vh - 4rem);
        overflow-y: auto;
        padding-right: 0.5rem;
    }
    
    .sidebar-content::-webkit-scrollbar {
        width: 4px;
    }
    
    .sidebar-content::-webkit-scrollbar-track {
        background: transparent;
    }
    
    .sidebar-content::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 2px;
    }
    
    .dark .sidebar-content::-webkit-scrollbar-thumb {
        background: #4b5563;
    }

    .company-card {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .company-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }
    
    .company-card:hover::before {
        transform: scaleX(1);
    }
    
    .company-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }
    
    .dark .company-card {
        background: #1f2937;
        border-color: #374151;
    }

    .company-logo {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .status-badge.active {
        background: #d1fae5;
        color: #065f46;
    }
    
    .status-badge.pending {
        background: #fef3c7;
        color: #92400e;
    }
    
    .status-badge.inactive {
        background: #fee2e2;
        color: #991b1b;
    }
    
    .dark .status-badge.active {
        background: #047857;
        color: #d1fae5;
    }
    
    .dark .status-badge.pending {
        background: #d97706;
        color: #fef3c7;
    }
    
    .dark .status-badge.inactive {
        background: #dc2626;
        color: #fee2e2;
    }

    .industry-badge {
        background: #f3f4f6;
        color: #6b7280;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }
    
    .dark .industry-badge {
        background: #374151;
        color: #9ca3af;
    }

    .search-filter-bar {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid #e5e7eb;
        margin-bottom: 2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
    
    .dark .search-filter-bar {
        background: #1f2937;
        border-color: #374151;
    }

    .filter-group {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }

    .btn {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        border: 1px solid;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        border-color: transparent;
    }
    
    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
    
    .btn-secondary {
        background: white;
        color: #6b7280;
        border-color: #d1d5db;
    }
    
    .btn-secondary:hover {
        background: #f9fafb;
        border-color: #9ca3af;
    }
    
    .dark .btn-secondary {
        background: #374151;
        color: #d1d5db;
        border-color: #4b5563;
    }
    
    .dark .btn-secondary:hover {
        background: #4b5563;
    }

    .section-header {
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }
    
    .dark .section-header {
        border-color: #374151;
    }

    .sidebar-section {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .dark .sidebar-section {
        background: #1f2937;
        border-color: #374151;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .companies-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
    }

    .add-company-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 50;
    }
    
    .modal-content {
        background: white;
        border-radius: 0.75rem;
        padding: 2rem;
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .dark .modal-content {
        background: #1f2937;
    }

    .quick-actions {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
        flex-wrap: wrap;
    }
</style>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="text-3xl font-bold mb-2 relative z-10">
            <i class="fas fa-building mr-3"></i>Company Management
        </h1>
        <p class="text-blue-100 text-lg max-w-3xl mx-auto relative z-10">
            Manage partner companies, track collaborations, and optimize business relationships with comprehensive analytics
        </p>
        
        <!-- Partnership Process Flow -->
        <div class="process-flow relative z-10">
            <div class="process-step">
                <div class="text-3xl text-green-500 mb-2">
                    <i class="fas fa-user-plus"></i>
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white">Register</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">Add new company</p>
            </div>
            <div class="process-step">
                <div class="text-3xl text-orange-500 mb-2">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white">Verify</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">Validate credentials</p>
            </div>
            <div class="process-step">
                <div class="text-3xl text-blue-500 mb-2">
                    <i class="fas fa-handshake"></i>
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white">Onboard</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">Setup partnership</p>
            </div>
            <div class="process-step">
                <div class="text-3xl text-purple-500 mb-2">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white">Collaborate</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">Work together</p>
            </div>
            <div class="process-step">
                <div class="text-3xl text-red-500 mb-2">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white">Grow</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">Expand partnership</p>
            </div>
        </div>
    </div>

    <!-- Content Grid -->
    <div class="content-grid">
        <!-- Main Content Area -->
        <div class="main-content space-y-6">
            <!-- KPI Metrics -->
            <div class="stats-grid">
                <div class="metric-card blue">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold opacity-90 mb-1">Total Companies</h3>
                            <div class="text-3xl font-bold">{{ total_companies|default:0 }}</div>
                            <div class="text-sm opacity-75 mt-2 flex items-center">
                                <i class="fas fa-arrow-up mr-1"></i>
                                +{{ new_companies_this_month|default:3 }} this month
                            </div>
                        </div>
                        <div class="text-4xl opacity-80">
                            <i class="fas fa-building"></i>
                        </div>
                    </div>
                </div>

                <div class="metric-card green">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold opacity-90 mb-1">Active Partners</h3>
                            <div class="text-3xl font-bold">{{ active_partners|default:0 }}</div>
                            <div class="text-sm opacity-75 mt-2 flex items-center">
                                <i class="fas fa-handshake mr-1"></i>
                                {{ collaborating_count|default:0 }} collaborating
                            </div>
                        </div>
                        <div class="text-4xl opacity-80">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>

                <div class="metric-card purple">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold opacity-90 mb-1">Total Vacancies</h3>
                            <div class="text-3xl font-bold">{{ total_vacancies|default:0 }}</div>
                            <div class="text-sm opacity-75 mt-2 flex items-center">
                                <i class="fas fa-briefcase mr-1"></i>
                                Across all companies
                            </div>
                        </div>
                        <div class="text-4xl opacity-80">
                            <i class="fas fa-briefcase"></i>
                        </div>
                    </div>
                </div>

                <div class="metric-card orange">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold opacity-90 mb-1">Industries</h3>
                            <div class="text-3xl font-bold">{{ industries_count|default:0 }}</div>
                            <div class="text-sm opacity-75 mt-2 flex items-center">
                                <i class="fas fa-industry mr-1"></i>
                                {{ sectors_covered|default:0 }} sectors covered
                            </div>
                        </div>
                        <div class="text-4xl opacity-80">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter Bar -->
            <div class="search-filter-bar">
                <div class="flex flex-col lg:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="Search companies..." class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        </div>
                    </div>
                    
                    <div class="filter-group">
                        <select class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="">All Industries</option>
                            <option value="technology">Technology</option>
                            <option value="finance">Finance</option>
                            <option value="healthcare">Healthcare</option>
                            <option value="education">Education</option>
                        </select>
                        
                        <select class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="pending">Pending</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <button class="btn btn-primary" onclick="showAddCompanyModal()">
                    <i class="fas fa-plus"></i>Add New Company
                </button>
                <button class="btn btn-secondary">
                    <i class="fas fa-download"></i>Export Data
                </button>
                <button class="btn btn-secondary">
                    <i class="fas fa-upload"></i>Import Companies
                </button>
                <button class="btn btn-secondary">
                    <i class="fas fa-filter"></i>Advanced Filters
                </button>
            </div>

            <!-- Companies Section -->
            <div class="section-header">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-building mr-3 text-blue-500"></i>Partner Companies
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Manage and track your business partnerships</p>
            </div>

            <div class="companies-grid">
                {% for company in companies %}
                <div class="company-card">
                    <div class="flex items-start justify-between mb-4">
                        <div class="company-logo">
                            {{ company.name|first|upper }}
                        </div>
                        <div class="flex gap-2">
                            <span class="status-badge active">Active</span>
                            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </div>
                    </div>
                    
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {{ company.name|default:"TechCorp Inc" }}
                    </h3>
                    
                    <div class="flex items-center gap-2 mb-3">
                        <span class="industry-badge">
                            <i class="fas fa-laptop-code"></i>Technology
                        </span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            • {{ company.employee_count|default:"250" }} employees
                        </span>
                    </div>
                    
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        {{ company.description|default:"Leading technology company specializing in software development and digital solutions." }}
                    </p>
                    
                    <div class="grid grid-cols-3 gap-4 mb-4 text-center">
                        <div>
                            <div class="text-lg font-semibold text-blue-600 dark:text-blue-400">
                                {{ company.active_vacancies|default:12 }}
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Open Roles</div>
                        </div>
                        <div>
                            <div class="text-lg font-semibold text-green-600 dark:text-green-400">
                                {{ company.hired_candidates|default:45 }}
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Hired</div>
                        </div>
                        <div>
                            <div class="text-lg font-semibold text-purple-600 dark:text-purple-400">
                                {{ company.partnership_score|default:85 }}%
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Score</div>
                        </div>
                    </div>
                    
                    <div class="flex gap-2">
                        <button class="flex-1 btn btn-primary">
                            <i class="fas fa-eye"></i>View Details
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-edit"></i>Edit
                        </button>
                    </div>
                </div>
                {% empty %}
                <div class="col-span-full text-center py-12">
                    <div class="text-gray-500 dark:text-gray-400">
                        <i class="fas fa-building text-6xl mb-4 opacity-30"></i>
                        <h3 class="text-lg font-medium mb-2">No companies found</h3>
                        <p class="text-sm mb-4">Start by adding your first partner company</p>
                        <button class="btn btn-primary" onclick="showAddCompanyModal()">
                            <i class="fas fa-plus mr-2"></i>Add First Company
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Sidebar Content -->
        <div class="sidebar-content space-y-6">
            <!-- Industry Distribution Chart -->
            <div class="sidebar-section">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-chart-pie mr-2 text-green-500"></i>Industry Mix
                </h3>
                <div class="relative mb-4">
                    <canvas id="industryChart" width="280" height="280"></canvas>
                </div>
                <div class="space-y-2 text-sm">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-blue-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">Technology</span>
                        </div>
                        <span class="font-semibold text-gray-900 dark:text-white">45%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">Finance</span>
                        </div>
                        <span class="font-semibold text-gray-900 dark:text-white">25%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-purple-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">Healthcare</span>
                        </div>
                        <span class="font-semibold text-gray-900 dark:text-white">20%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-orange-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">Others</span>
                        </div>
                        <span class="font-semibold text-gray-900 dark:text-white">10%</span>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="sidebar-section">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-clock mr-2 text-purple-500"></i>Recent Activity
                </h3>
                <div class="space-y-3">
                    <div class="flex items-start">
                        <div class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <div class="flex-1">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">TechCorp Inc registered</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">2 hours ago</div>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <div class="flex-1">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">New vacancy posted</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">4 hours ago</div>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <div class="flex-1">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">Partnership agreement signed</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">1 day ago</div>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View all activity <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>

            <!-- Partnership Analytics -->
            <div class="sidebar-section">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-chart-bar mr-2 text-red-500"></i>Partnership Analytics
                </h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-handshake text-blue-500 mr-2"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Success Rate</span>
                        </div>
                        <span class="text-sm font-semibold text-green-600 dark:text-green-400">92%</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-clock text-yellow-500 mr-2"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Avg. Response</span>
                        </div>
                        <span class="text-sm font-semibold text-gray-900 dark:text-white">24h</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-star text-purple-500 mr-2"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Satisfaction</span>
                        </div>
                        <span class="text-sm font-semibold text-purple-600 dark:text-purple-400">4.8/5</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-chart-line text-green-500 mr-2"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Growth Rate</span>
                        </div>
                        <span class="text-sm font-semibold text-green-600 dark:text-green-400">+15%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Company Modal -->
<div class="add-company-modal" id="addCompanyModal">
    <div class="modal-content">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Add New Company</h2>
            <button onclick="hideAddCompanyModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Company Name</label>
                <input type="text" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="Enter company name">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Industry</label>
                <select class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="">Select industry</option>
                    <option value="technology">Technology</option>
                    <option value="finance">Finance</option>
                    <option value="healthcare">Healthcare</option>
                    <option value="education">Education</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                <textarea rows="3" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="Brief company description"></textarea>
            </div>
            
            <div class="flex gap-3 pt-4">
                <button type="submit" class="flex-1 btn btn-primary">
                    <i class="fas fa-plus"></i>Add Company
                </button>
                <button type="button" onclick="hideAddCompanyModal()" class="flex-1 btn btn-secondary">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Industry Distribution Chart
    const ctx = document.getElementById('industryChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Technology', 'Finance', 'Healthcare', 'Others'],
            datasets: [{
                data: [45, 25, 20, 10],
                backgroundColor: [
                    '#3b82f6',
                    '#10b981',
                    '#8b5cf6',
                    '#f59e0b'
                ],
                borderWidth: 0,
                cutout: '70%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Animate metric cards
    const metricCards = document.querySelectorAll('.metric-card');
    metricCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });
    
    // Animate company cards
    const companyCards = document.querySelectorAll('.company-card');
    companyCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, (index * 100) + 600);
    });
});

function showAddCompanyModal() {
    document.getElementById('addCompanyModal').style.display = 'flex';
}

function hideAddCompanyModal() {
    document.getElementById('addCompanyModal').style.display = 'none';
}

// Close modal when clicking outside
document.getElementById('addCompanyModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideAddCompanyModal();
    }
});
</script>
{% endblock %}