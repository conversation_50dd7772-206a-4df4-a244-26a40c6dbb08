<!-- AI Configuration Popup -->
<div id="aiConfigPopup" class="popup-overlay hidden" onclick="event.stopPropagation();">
    <div class="popup-content" style="max-width: 800px; width: 90vw;" onclick="event.stopPropagation();">
        <div class="popup-header">
            <h3 class="text-xl font-bold">
                <i class="fas fa-robot mr-2 text-blue-600"></i>
                AI Configuration Center
            </h3>
            <button class="popup-close" onclick="closePopup('aiConfigPopup')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="ai-config-content">
            <!-- Configuration Tabs -->
            <div class="config-tabs mb-6">
                <div class="flex border-b border-gray-200">
                    <button class="tab-btn active" onclick="switchConfigTab('providers')" data-tab="providers">
                        <i class="fas fa-cloud mr-2"></i>AI Providers
                    </button>
                    <button class="tab-btn" onclick="switchConfigTab('settings')" data-tab="settings">
                        <i class="fas fa-cog mr-2"></i>General Settings
                    </button>
                    <button class="tab-btn" onclick="switchConfigTab('prompts')" data-tab="prompts">
                        <i class="fas fa-edit mr-2"></i>Prompts
                    </button>
                </div>
            </div>

            <!-- AI Providers Tab -->
            <div id="providersTab" class="config-tab-content">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-semibold">AI Provider Configurations</h4>
                    <button class="btn btn-primary" onclick="addNewProvider()">
                        <i class="fas fa-plus mr-2"></i>Add Provider
                    </button>
                </div>
                
                <div id="providersList" class="space-y-4">
                    <!-- Provider configurations will be loaded here -->
                </div>
                
                <!-- Add/Edit Provider Form -->
                <div id="providerForm" class="hidden bg-gray-50 p-4 rounded-lg mt-4">
                    <h5 class="font-semibold mb-3">Configure AI Provider</h5>
                    <form id="aiProviderForm">
                        <input type="hidden" id="providerId" name="id">
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Provider</label>
                                <select id="providerSelect" name="provider" class="form-control" onchange="updateProviderGuidance()">
                                    <option value="">Select Provider</option>
                                    <option value="openai">OpenAI</option>
                                    <option value="groq">Groq</option>
                                    <option value="ollama">Ollama</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Model Name</label>
                                <div class="flex gap-2">
                                    <input type="text" id="modelName" name="model_name" class="form-control" placeholder="Enter model name">
                                    <button type="button" class="btn btn-secondary" onclick="fetchModelsForProvider()" id="fetchModelsBtn" disabled>
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">API Key / Server URL</label>
                            <input type="text" id="apiKey" name="api_key" class="form-control" placeholder="Enter API key or server URL">
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                                <input type="number" id="priority" name="priority" class="form-control" min="1" value="1">
                            </div>
                            
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Max Tokens</label>
                                <input type="number" id="maxTokens" name="max_tokens" class="form-control" value="2000">
                            </div>
                            
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Temperature</label>
                                <input type="number" id="temperature" name="temperature" class="form-control" step="0.1" min="0" max="1" value="0.3">
                            </div>
                        </div>
                        
                        <div class="form-group mb-4">
                            <label class="flex items-center">
                                <input type="checkbox" id="isActive" name="is_active" class="mr-2" checked>
                                <span class="text-sm text-gray-700">Active</span>
                            </label>
                        </div>
                        
                        <!-- Provider Guidance -->
                        <div id="providerGuidance" class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 text-sm">
                            <strong class="text-blue-800">Select a provider to see configuration guidance.</strong>
                        </div>
                        
                        <div class="flex gap-2">
                            <button type="button" class="btn btn-primary" onclick="saveProvider()">
                                <i class="fas fa-save mr-2"></i>Save Provider
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="testConnection()" id="testConnectionBtn" disabled>
                                <i class="fas fa-plug mr-2"></i>Test Connection
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="cancelProviderForm()">
                                <i class="fas fa-times mr-2"></i>Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- General Settings Tab -->
            <div id="settingsTab" class="config-tab-content hidden">
                <h4 class="text-lg font-semibold mb-4">General AI Settings</h4>
                
                <form id="generalSettingsForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Cache TTL (seconds)</label>
                            <input type="number" id="cacheTtl" name="cache_ttl" class="form-control" value="86400">
                            <p class="text-xs text-gray-500 mt-1">How long to cache AI responses</p>
                        </div>
                        
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Batch Size</label>
                            <input type="number" id="batchSize" name="batch_size" class="form-control" value="10">
                            <p class="text-xs text-gray-500 mt-1">Maximum CVs to process in parallel</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Max Retries</label>
                            <input type="number" id="maxRetries" name="max_retries" class="form-control" value="3">
                            <p class="text-xs text-gray-500 mt-1">Retry attempts for failed API calls</p>
                        </div>
                        
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Retry Delay (ms)</label>
                            <input type="number" id="retryDelay" name="retry_delay" class="form-control" value="1000">
                            <p class="text-xs text-gray-500 mt-1">Delay between retry attempts</p>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-primary" onclick="saveGeneralSettings()">
                        <i class="fas fa-save mr-2"></i>Save Settings
                    </button>
                </form>
            </div>

            <!-- Prompts Tab -->
            <div id="promptsTab" class="config-tab-content hidden">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-semibold">AI Prompt Templates</h4>
                    <button class="btn btn-primary" onclick="addNewPrompt()">
                        <i class="fas fa-plus mr-2"></i>Add Prompt
                    </button>
                </div>
                
                <div id="promptsList" class="space-y-4">
                    <!-- Prompt configurations will be loaded here -->
                </div>
                
                <!-- Add/Edit Prompt Form -->
                <div id="promptForm" class="hidden bg-gray-50 p-4 rounded-lg mt-4">
                    <h5 class="font-semibold mb-3">Configure AI Prompt</h5>
                    <form id="aiPromptForm">
                        <input type="hidden" id="promptId" name="id">
                        
                        <div class="form-group mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Prompt Name</label>
                            <input type="text" id="promptName" name="name" class="form-control" placeholder="e.g., CV Analysis, Skills Extraction">
                        </div>
                        
                        <div class="form-group mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Prompt Template</label>
                            <textarea id="promptTemplate" name="prompt" class="form-control" rows="6" placeholder="Enter your prompt template here..."></textarea>
                        </div>
                        
                        <div class="form-group mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Instructions</label>
                            <textarea id="promptInstructions" name="instructions" class="form-control" rows="3" placeholder="Additional instructions for this prompt..."></textarea>
                        </div>
                        
                        <div class="flex gap-2">
                            <button type="button" class="btn btn-primary" onclick="savePrompt()">
                                <i class="fas fa-save mr-2"></i>Save Prompt
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="cancelPromptForm()">
                                <i class="fas fa-times mr-2"></i>Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.config-tabs .tab-btn {
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    color: #6b7280;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.config-tabs .tab-btn.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
}

.config-tabs .tab-btn:hover {
    color: #1f2937;
}

.provider-card {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    background: white;
    transition: all 0.3s ease;
}

.provider-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.provider-card.active {
    border-color: #10b981;
    background: #f0fdf4;
}

.provider-card.inactive {
    opacity: 0.6;
    background: #f9fafb;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-indicator.active {
    background: #dcfce7;
    color: #166534;
}

.status-indicator.inactive {
    background: #fef2f2;
    color: #991b1b;
}

.form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group {
    margin-bottom: 1rem;
}
</style> 