{% comment %}
    Usage: {% include 'cv_analyzer/components/alert.html' with type='success' message='Operation completed successfully' %}
{% endcomment %}

<div class="alert alert-{{ type|default:'info' }} {% if class %}{{ class }}{% endif %}">
    <div class="flex items-center">
        <span class="me-2">
            {% if type == 'success' %}
                {% include 'cv_analyzer/components/icon.html' with icon='fa-check-circle' solid=True %}
            {% elif type == 'error' %}
                {% include 'cv_analyzer/components/icon.html' with icon='fa-exclamation-circle' solid=True %}
            {% elif type == 'warning' %}
                {% include 'cv_analyzer/components/icon.html' with icon='fa-exclamation-triangle' solid=True %}
            {% else %}
                {% include 'cv_analyzer/components/icon.html' with icon='fa-info-circle' solid=True %}
            {% endif %}
        </span>
        <span>{{ message }}</span>
        {% if dismissible %}
        <button class="ms-auto" onclick="this.parentElement.parentElement.remove()">
            {% include 'cv_analyzer/components/icon.html' with icon='fa-times' solid=True %}
        </button>
        {% endif %}
    </div>
</div> 