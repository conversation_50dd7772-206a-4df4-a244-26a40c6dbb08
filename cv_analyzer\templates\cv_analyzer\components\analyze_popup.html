<!-- Analyze CVs Popup -->
<div id="analyzePopup" class="popup-overlay">
    <div class="popup-content" style="width: 100vw; max-width: 100vw;">
        <div class="popup-header">
            <h3 class="text-xl font-bold">
                <i class="fas fa-brain mr-2 text-green-600"></i>
                CV Analysis Center
            </h3>
            <button class="popup-close" onclick="closePopup('analyzePopup')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="analyze-content">
            <!-- Analysis Options -->
            <div class="analysis-options mb-6">
                <h4 class="text-lg font-semibold mb-4">Analysis Options</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="option-card p-4 border rounded-lg cursor-pointer hover:border-blue-500" onclick="selectAnalysisType('single')">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-file-alt text-2xl text-blue-600 mr-3"></i>
                            <h5 class="font-semibold">Single CV Analysis</h5>
                        </div>
                        <p class="text-sm text-gray-600">Analyze a specific CV in detail</p>
                    </div>
                    
                    <div class="option-card p-4 border rounded-lg cursor-pointer hover:border-blue-500" onclick="selectAnalysisType('batch')">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-files text-2xl text-green-600 mr-3"></i>
                            <h5 class="font-semibold">Batch Analysis</h5>
                        </div>
                        <p class="text-sm text-gray-600">Analyze multiple CVs at once</p>
                    </div>
                    
                    <div class="option-card p-4 border rounded-lg cursor-pointer hover:border-blue-500" onclick="selectAnalysisType('matching')">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-search text-2xl text-purple-600 mr-3"></i>
                            <h5 class="font-semibold">CV Matching</h5>
                        </div>
                        <p class="text-sm text-gray-600">Match CVs to job vacancies</p>
                    </div>
                    
                    <div class="option-card p-4 border rounded-lg cursor-pointer hover:border-blue-500" onclick="selectAnalysisType('comparison')">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-balance-scale text-2xl text-orange-600 mr-3"></i>
                            <h5 class="font-semibold">CV Comparison</h5>
                        </div>
                        <p class="text-sm text-gray-600">Compare multiple CVs side by side</p>
                    </div>
                </div>
            </div>
            
            <!-- Single CV Analysis -->
            <div id="single-analysis" class="analysis-type" style="display: none;">
                <h4 class="text-lg font-semibold mb-4">Select CV to Analyze</h4>
                <!-- Debug: CV count = {{ unanalyzed_cvs|length }} -->
                <div class="cv-selection">
                    <select id="singleCVSelect" class="form-control mb-4">
                        <option value="">Choose a CV...</option>
                        {% for cv in unanalyzed_cvs %}
                        <option value="{{ cv.id }}">{{ cv.candidate_name|default:cv.file.name }} - {{ cv.uploaded_at|date:"M d, Y" }}</option>
                        {% empty %}
                        <option value="" disabled>No CVs available</option>
                        {% endfor %}
                    </select>
                    
                    <div class="analysis-options-detailed">
                        <h5 class="font-semibold mb-3">Analysis Settings</h5>
                        <div class="form-group mb-3">
                            <label class="flex items-center">
                                <input type="checkbox" checked class="mr-2">
                                <span>Content Analysis</span>
                            </label>
                        </div>
                        <div class="form-group mb-3">
                            <label class="flex items-center">
                                <input type="checkbox" checked class="mr-2">
                                <span>Format & Structure</span>
                            </label>
                        </div>
                        <div class="form-group mb-3">
                            <label class="flex items-center">
                                <input type="checkbox" checked class="mr-2">
                                <span>Skills Extraction</span>
                            </label>
                        </div>
                        <div class="form-group mb-3">
                            <label class="flex items-center">
                                <input type="checkbox" checked class="mr-2">
                                <span>Experience Evaluation</span>
                            </label>
                        </div>
                    </div>
                    
                    <button class="btn btn-primary" onclick="startSingleAnalysis()">
                        <i class="fas fa-play mr-2"></i>
                        Start Analysis
                    </button>
                </div>
            </div>
            
            <!-- Batch Analysis -->
            <div id="batch-analysis" class="analysis-type" style="display: none;">
                <h4 class="text-lg font-semibold mb-4">Batch CV Analysis</h4>
                <!-- Debug: Batch CV count = {{ unanalyzed_cvs|length }} -->
                <div class="batch-selection">
                    <div class="form-group mb-4">
                        <label class="block text-sm font-medium mb-2">Select CVs</label>
                        <div class="cv-list max-h-60 overflow-y-auto border rounded p-3">
                            {% for cv in unanalyzed_cvs %}
                            <label class="flex items-center mb-2">
                                <input type="checkbox" name="batch_cvs" value="{{ cv.id }}" class="mr-2">
                                <span>{{ cv.candidate_name|default:cv.file.name }} - {{ cv.uploaded_at|date:"M d, Y" }}</span>
                            </label>
                            {% empty %}
                            <p class="text-gray-500">No CVs available for batch analysis</p>
                            {% endfor %}
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-secondary btn-sm" onclick="selectAllCVs()">Select All</button>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="clearAllCVs()">Clear All</button>
                        </div>
                    </div>
                    
                    <div class="progress-container mb-4" id="batchProgress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <div class="progress-text">0% Complete</div>
                    </div>
                    
                    <button class="btn btn-primary" onclick="startBatchAnalysis()">
                        <i class="fas fa-play mr-2"></i>
                        Start Batch Analysis
                    </button>
                </div>
            </div>
            
            <!-- CV Matching -->
            <div id="matching-analysis" class="analysis-type" style="display: none;">
                <h4 class="text-lg font-semibold mb-4">CV to Job Matching</h4>
                <div class="matching-selection">
                    <div class="form-grid mb-4">
                        <div class="form-group">
                            <label class="block text-sm font-medium mb-2">Select Vacancy</label>
                            <select id="matchingVacancySelect" class="form-control">
                                <option value="">Choose a vacancy...</option>
                                {% for vacancy in active_vacancies %}
                                <option value="{{ vacancy.id }}">{{ vacancy.title }} - {{ vacancy.company.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="matching-results mb-4" id="matchingResults" style="display: none;">
                        <h5 class="font-semibold mb-3">Matching Results</h5>
                        <div class="results-list"></div>
                    </div>
                    
                    <button class="btn btn-primary" onclick="startMatching()">
                        <i class="fas fa-search mr-2"></i>
                        Find Matches
                    </button>
                </div>
            </div>
            
            <!-- CV Comparison -->
            <div id="comparison-analysis" class="analysis-type" style="display: none;">
                <h4 class="text-lg font-semibold mb-4">Compare CVs</h4>
                <div class="comparison-selection">
                    <div class="form-grid mb-4">
                        <div class="form-group">
                            <label class="block text-sm font-medium mb-2">First CV</label>
                            <select id="firstCVSelect" class="form-control">
                                <option value="">Choose first CV...</option>
                                {% for cv in analyzed_cvs %}
                                <option value="{{ cv.id }}">{{ cv.candidate_name|default:cv.file.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="block text-sm font-medium mb-2">Second CV</label>
                            <select id="secondCVSelect" class="form-control">
                                <option value="">Choose second CV...</option>
                                {% for cv in analyzed_cvs %}
                                <option value="{{ cv.id }}">{{ cv.candidate_name|default:cv.file.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="comparison-criteria mb-4">
                        <h5 class="font-semibold mb-3">Comparison Criteria</h5>
                        <div class="grid grid-cols-2 gap-2">
                            <label class="flex items-center">
                                <input type="checkbox" checked class="mr-2">
                                <span>Skills Match</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" checked class="mr-2">
                                <span>Experience Level</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" checked class="mr-2">
                                <span>Education</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" checked class="mr-2">
                                <span>Overall Score</span>
                            </label>
                        </div>
                    </div>
                    
                    <button class="btn btn-primary" onclick="compareCVs()">
                        <i class="fas fa-balance-scale mr-2"></i>
                        Compare CVs
                    </button>
                </div>
            </div>
            
            <!-- Analysis Progress -->
            <div id="analysisProgress" class="analysis-progress" style="display: none;">
                <div class="progress-container">
                    <!-- Central Loading Animation -->
                    <div class="loading-animation">
                        <div class="spinner-wrapper">
                            <div class="spinner-ring">
                                <div class="spinner-inner"></div>
                            </div>
                            <i class="fas fa-brain brain-icon"></i>
                        </div>
                    </div>
                    
                    <!-- Progress Info -->
                    <div class="progress-info">
                        <h4 class="progress-title">Analysis in Progress</h4>
                        <p class="progress-subtitle" id="progressSubtitle">Initializing AI analysis...</p>
                    </div>
                    
                    <!-- Enhanced Progress Bar -->
                    <div class="progress-bar-container">
                        <div class="progress-bar-wrapper">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                                <div class="progress-glow"></div>
                            </div>
                        </div>
                        <div class="progress-details">
                            <span class="progress-percentage" id="progressPercentage">0%</span>
                            <span class="progress-status" id="progressStatus">Starting...</span>
                        </div>
                    </div>
                    
                    <!-- Progress Steps -->
                    <div class="progress-steps">
                        <div class="step active" id="step1">
                            <div class="step-icon"><i class="fas fa-upload"></i></div>
                            <span class="step-text">Uploading</span>
                        </div>
                        <div class="step" id="step2">
                            <div class="step-icon"><i class="fas fa-file-text"></i></div>
                            <span class="step-text">Extracting</span>
                        </div>
                        <div class="step" id="step3">
                            <div class="step-icon"><i class="fas fa-brain"></i></div>
                            <span class="step-text">Analyzing</span>
                        </div>
                        <div class="step" id="step4">
                            <div class="step-icon"><i class="fas fa-chart-line"></i></div>
                            <span class="step-text">Scoring</span>
                        </div>
                        <div class="step" id="step5">
                            <div class="step-icon"><i class="fas fa-check"></i></div>
                            <span class="step-text">Complete</span>
                        </div>
                    </div>
                    
                    <!-- Cancel Button -->
                    <div class="progress-actions">
                        <button class="btn btn-secondary" onclick="cancelAnalysis()">
                            <i class="fas fa-times mr-2"></i>Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.option-card {
    transition: all 0.3s ease;
}

.option-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.option-card.selected {
    border-color: #3b82f6;
    background: #eff6ff;
}

.cv-list {
    background: #f9fafb;
}

.analysis-type {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Enhanced Progress Styles */
.progress-container {
    text-align: center;
    padding: 3rem 2rem;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 1rem;
    margin: 1rem 0;
}

.loading-animation {
    margin-bottom: 2rem;
}

.spinner-wrapper {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
}

.spinner-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1.5s linear infinite;
}

.spinner-inner {
    position: absolute;
    top: 10px;
    left: 10px;
    width: calc(100% - 20px);
    height: calc(100% - 20px);
    border: 2px solid transparent;
    border-top: 2px solid #8b5cf6;
    border-radius: 50%;
    animation: spin 1s linear infinite reverse;
}

.brain-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2.5rem;
    color: #3b82f6;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
}

.progress-info {
    margin-bottom: 2rem;
}

.progress-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.progress-subtitle {
    font-size: 1rem;
    color: #6b7280;
    margin: 0;
}

.progress-bar-container {
    width: 100%;
    max-width: 400px;
    margin-bottom: 2rem;
}

.progress-bar-wrapper {
    position: relative;
    margin-bottom: 0.75rem;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: #e5e7eb;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06d6a0 100%);
    border-radius: 6px;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    width: 0%;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    border-radius: 8px;
    opacity: 0;
    filter: blur(8px);
    transition: opacity 0.3s ease;
    z-index: -1;
}

.progress-bar-wrapper:hover .progress-glow {
    opacity: 0.3;
}

.progress-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-percentage {
    font-size: 1.25rem;
    font-weight: 700;
    color: #3b82f6;
}

.progress-status {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 500px;
    margin-bottom: 2rem;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 40px;
    right: 40px;
    height: 2px;
    background: #e5e7eb;
    z-index: 0;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    border: 3px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.step-icon i {
    font-size: 0.875rem;
    color: #9ca3af;
    transition: color 0.3s ease;
}

.step-text {
    font-size: 0.75rem;
    color: #9ca3af;
    font-weight: 500;
    transition: color 0.3s ease;
}

.step.active .step-icon {
    background: #3b82f6;
    transform: scale(1.1);
}

.step.active .step-icon i {
    color: white;
}

.step.active .step-text {
    color: #3b82f6;
    font-weight: 600;
}

.step.completed .step-icon {
    background: #10b981;
}

.step.completed .step-icon i {
    color: white;
}

.step.completed .step-text {
    color: #10b981;
}

.progress-actions {
    margin-top: 1rem;
}

.progress-actions .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.progress-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>