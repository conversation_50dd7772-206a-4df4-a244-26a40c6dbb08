{% comment %}
    Usage: {% include 'cv_analyzer/components/chart.html' with id='myChart' type='line' title='Performance Over Time' %}
{% endcomment %}

<div class="chart-container">
    {% if title %}
    <div class="chart-title">
        {% if icon %}
            {% include 'cv_analyzer/components/icon.html' with icon=icon solid=True class='me-2' %}
        {% endif %}
        {{ title }}
    </div>
    {% endif %}
    <canvas id="{{ id }}" {% if width %}width="{{ width }}"{% endif %} {% if height %}height="{{ height }}"{% endif %}></canvas>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('{{ id }}').getContext('2d');
    const chart = new Chart(ctx, {
        type: '{{ type|default:"line" }}',
        data: {{ data|safe }},
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        color: document.documentElement.classList.contains('dark') ? '#fff' : '#666'
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        color: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb'
                    },
                    ticks: {
                        color: document.documentElement.classList.contains('dark') ? '#fff' : '#666'
                    }
                },
                y: {
                    grid: {
                        color: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb'
                    },
                    ticks: {
                        color: document.documentElement.classList.contains('dark') ? '#fff' : '#666'
                    }
                }
            }
        }
    });

    // Update chart colors when theme changes
    document.getElementById('theme-toggle')?.addEventListener('click', function() {
        const isDark = document.documentElement.classList.contains('dark');
        chart.options.plugins.legend.labels.color = isDark ? '#fff' : '#666';
        chart.options.scales.x.grid.color = isDark ? '#374151' : '#e5e7eb';
        chart.options.scales.x.ticks.color = isDark ? '#fff' : '#666';
        chart.options.scales.y.grid.color = isDark ? '#374151' : '#e5e7eb';
        chart.options.scales.y.ticks.color = isDark ? '#fff' : '#666';
        chart.update();
    });
});
</script>
{% endblock %} 