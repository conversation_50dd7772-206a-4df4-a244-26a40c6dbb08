<!-- CV Detail Popup -->
<div id="cvDetailPopup" class="popup-overlay">
    <div class="popup-content" style="width: 98vw; max-width: 1800px; max-height: 90vh; overflow-y: auto;">
        <div class="popup-header bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <h3 class="text-lg font-bold flex items-center justify-center flex-1">
                <i class="fas fa-file-alt mr-2"></i>
                CV Details & Analysis
            </h3>
            <button class="popup-close text-white hover:text-gray-200" onclick="closePopup('cvDetailPopup')">
                <i class="fas fa-times text-lg"></i>
            </button>
        </div>
        
        <div id="cvDetailContent" class="cv-detail-content p-4">
            <!-- Content will be loaded dynamically -->
            <div class="loading-state text-center py-8">
                <i class="fas fa-spinner fa-spin text-4xl text-blue-600 mb-4"></i>
                <p>Loading CV details...</p>
            </div>
        </div>
    </div>
</div>

<!-- CV Detail Template (will be populated dynamically) -->
<template id="cvDetailTemplate">
    <div class="cv-detail-wrapper space-y-4">
        <!-- Compact Header with Key Info -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
            <div class="flex justify-between items-center">
                <div class="flex-1">
                    <h2 class="text-lg font-bold text-gray-800 mb-1" data-field="candidate_name">Candidate Name</h2>
                    <div class="flex flex-wrap gap-4 text-sm text-gray-600">
                        <span class="flex items-center"><i class="fas fa-envelope mr-1 text-blue-500"></i><span data-field="email">--</span></span>
                        <span class="flex items-center"><i class="fas fa-phone mr-1 text-green-500"></i><span data-field="phone">--</span></span>
                        <span class="flex items-center"><i class="fas fa-map-marker-alt mr-1 text-red-500"></i><span data-field="location">--</span></span>
                        <span class="flex items-center"><i class="fas fa-briefcase mr-1 text-orange-500"></i><span data-field="years_of_experience">--</span> years</span>
                    </div>
                </div>
                <div class="text-center bg-white rounded-lg p-3 shadow-sm border">
                    <div class="text-2xl font-bold text-blue-600 mb-1" data-field="overall_score">--</div>
                    <div class="text-xs text-gray-500">Overall Score</div>
                </div>
            </div>
        </div>
        
        <!-- Enhanced Quick Actions - White Theme -->
        <div class="bg-gradient-to-r from-gray-50 to-white rounded-xl p-4 border border-gray-200 shadow-sm">
            <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                <i class="fas fa-bolt mr-2 text-yellow-500"></i>Quick Actions
            </h3>
            <div class="grid grid-cols-2 lg:grid-cols-3 gap-3">
                <!-- Primary Actions (Row 1) -->
                <button onclick="reanalyzeCV()" class="action-btn action-btn-primary group">
                    <div class="action-btn-icon bg-purple-100 text-purple-600 group-hover:bg-purple-600 group-hover:text-white">
                        <i class="fas fa-redo"></i>
                    </div>
                    <div class="action-btn-content">
                        <span class="action-btn-title text-gray-900">Re-analyze</span>
                        <span class="action-btn-subtitle text-gray-600">Run AI analysis</span>
                    </div>
                </button>
                
                <button onclick="matchToJobs()" class="action-btn action-btn-primary group">
                    <div class="action-btn-icon bg-blue-100 text-blue-600 group-hover:bg-blue-600 group-hover:text-white">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="action-btn-content">
                        <span class="action-btn-title text-gray-900">Find Jobs</span>
                        <span class="action-btn-subtitle text-gray-600">Match positions</span>
                    </div>
                </button>
                
                <button onclick="downloadCV()" class="action-btn action-btn-primary group">
                    <div class="action-btn-icon bg-green-100 text-green-600 group-hover:bg-green-600 group-hover:text-white">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="action-btn-content">
                        <span class="action-btn-title text-gray-900">Download</span>
                        <span class="action-btn-subtitle text-gray-600">Save file</span>
                    </div>
                </button>
                
                <!-- Secondary Actions (Row 2) -->
                <button onclick="shareCV()" class="action-btn action-btn-secondary group">
                    <div class="action-btn-icon bg-cyan-100 text-cyan-600 group-hover:bg-cyan-600 group-hover:text-white">
                        <i class="fas fa-share"></i>
                    </div>
                    <div class="action-btn-content">
                        <span class="action-btn-title text-gray-900">Share</span>
                        <span class="action-btn-subtitle text-gray-600">Copy link</span>
                    </div>
                </button>
                
                <button onclick="addNotes()" class="action-btn action-btn-secondary group">
                    <div class="action-btn-icon bg-yellow-100 text-yellow-600 group-hover:bg-yellow-600 group-hover:text-white">
                        <i class="fas fa-sticky-note"></i>
                    </div>
                    <div class="action-btn-content">
                        <span class="action-btn-title text-gray-900">Notes</span>
                        <span class="action-btn-subtitle text-gray-600">Add memo</span>
                    </div>
                </button>
                
                <button onclick="archiveCV()" class="action-btn action-btn-danger group">
                    <div class="action-btn-icon bg-red-100 text-red-600 group-hover:bg-red-600 group-hover:text-white">
                        <i class="fas fa-archive"></i>
                    </div>
                    <div class="action-btn-content">
                        <span class="action-btn-title text-gray-900">Archive</span>
                        <span class="action-btn-subtitle text-gray-600">Store away</span>
                    </div>
                </button>
            </div>
        </div>

        <!-- Main Content - Two Column Layout -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            
            <!-- Left Column -->
            <div class="space-y-4">
                
                <!-- Analysis Scores - Compact Grid -->
                <div class="bg-white rounded-lg border border-gray-200 p-4">
                    <h3 class="text-base font-semibold mb-3 text-gray-800 flex items-center">
                        <i class="fas fa-chart-bar mr-2 text-purple-600"></i>Analysis Scores
                    </h3>
                    <div class="grid grid-cols-3 gap-3">
                        <div class="text-center bg-blue-50 rounded-lg p-2">
                            <div class="text-lg font-bold text-blue-600" data-field="content_score">--</div>
                            <div class="text-xs text-gray-600">Content</div>
                        </div>
                        <div class="text-center bg-green-50 rounded-lg p-2">
                            <div class="text-lg font-bold text-green-600" data-field="format_score">--</div>
                            <div class="text-xs text-gray-600">Format</div>
                        </div>
                        <div class="text-center bg-purple-50 rounded-lg p-2">
                            <div class="text-lg font-bold text-purple-600" data-field="skills_score">--</div>
                            <div class="text-xs text-gray-600">Skills</div>
                        </div>
                        <div class="text-center bg-orange-50 rounded-lg p-2">
                            <div class="text-lg font-bold text-orange-600" data-field="sections_score">--</div>
                            <div class="text-xs text-gray-600">Sections</div>
                        </div>
                        <div class="text-center bg-gray-50 rounded-lg p-2">
                            <div class="text-lg font-bold text-gray-600" data-field="style_score">--</div>
                            <div class="text-xs text-gray-600">Style</div>
                        </div>
                        <div class="text-center bg-indigo-50 rounded-lg p-2">
                            <div class="text-lg font-bold text-indigo-600" data-field="overall_score">--</div>
                            <div class="text-xs text-gray-600">Overall</div>
                        </div>
                    </div>
                </div>

                <!-- Personal Details - Compact -->
                <div class="bg-white rounded-lg border border-gray-200 p-4">
                    <h3 class="text-base font-semibold mb-3 text-gray-800 flex items-center">
                        <i class="fas fa-user mr-2 text-blue-600"></i>Personal Information
                    </h3>
                    <div class="grid grid-cols-2 gap-3 text-sm">
                        <div>
                            <span class="text-gray-500">Full Name:</span>
                            <div class="font-medium" data-field="candidate_name">--</div>
                        </div>
                        <div>
                            <span class="text-gray-500">Education:</span>
                            <div class="font-medium" data-field="education_level">--</div>
                        </div>
                        <div>
                            <span class="text-gray-500">Email:</span>
                            <div class="font-medium" data-field="email">--</div>
                        </div>
                        <div>
                            <span class="text-gray-500">Location:</span>
                            <div class="font-medium" data-field="location">--</div>
                        </div>
                        <div>
                            <span class="text-gray-500">Phone:</span>
                            <div class="font-medium" data-field="phone">--</div>
                        </div>
                        <div>
                            <span class="text-gray-500">Experience:</span>
                            <div class="font-medium" data-field="years_of_experience">--</div>
                        </div>
                    </div>
                </div>

                <!-- Preferences Compact -->
                <div class="bg-white rounded-lg border border-gray-200 p-4">
                    <h3 class="text-base font-semibold mb-3 text-gray-800 flex items-center">
                        <i class="fas fa-cog mr-2 text-green-600"></i>Job Preferences
                    </h3>
                    <div class="grid grid-cols-2 gap-3 text-sm">
                        <div>
                            <span class="text-gray-500">Job Type:</span>
                            <div class="mt-1">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs" data-field="preferred_job_type">--</span>
                            </div>
                        </div>
                        <div>
                            <span class="text-gray-500">Work Location:</span>
                            <div class="mt-1">
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs" data-field="preferred_work_location">--</span>
                            </div>
                        </div>
                        <div>
                            <span class="text-gray-500">Salary:</span>
                            <div class="font-medium" data-field="salary_expectation">--</div>
                        </div>
                        <div>
                            <span class="text-gray-500">Available:</span>
                            <div class="font-medium" data-field="availability">--</div>
                        </div>
                    </div>
                </div>

                <!-- CV Status Compact -->
                <div class="bg-white rounded-lg border border-gray-200 p-4">
                    <h3 class="text-base font-semibold mb-3 text-gray-800 flex items-center">
                        <i class="fas fa-info-circle mr-2 text-orange-600"></i>CV Status
                    </h3>
                    <div class="grid grid-cols-2 gap-3 text-sm">
                        <div>
                            <span class="text-gray-500">Status:</span>
                            <div class="mt-1" data-field="status">--</div>
                        </div>
                        <div>
                            <span class="text-gray-500">Source:</span>
                            <div class="font-medium" data-field="source">--</div>
                        </div>
                        <div>
                            <span class="text-gray-500">Uploaded:</span>
                            <div class="font-medium" data-field="uploaded_at">--</div>
                        </div>
                        <div>
                            <span class="text-gray-500">Analyzed:</span>
                            <div class="font-medium" data-field="analyzed_at">--</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-4">
                
                <!-- Professional Links -->
                <div class="bg-white rounded-lg border border-gray-200 p-4">
                    <h3 class="text-base font-semibold mb-3 text-gray-800 flex items-center">
                        <i class="fas fa-link mr-2 text-cyan-600"></i>Professional Links
                    </h3>
                    <div class="space-y-2">
                        <div data-field="linkedin_profile" class="flex items-center">
                            <i class="fab fa-linkedin mr-2 text-blue-600"></i>
                            <div class="linkedin-link text-sm flex-1">--</div>
                        </div>
                        <div data-field="website_portfolio" class="flex items-center">
                            <i class="fas fa-globe mr-2 text-green-600"></i>
                            <div class="website-link text-sm flex-1">--</div>
                        </div>
                        <div data-field="github_profile" class="flex items-center">
                            <i class="fab fa-github mr-2 text-gray-800"></i>
                            <div class="github-link text-sm flex-1">--</div>
                        </div>
                        <div data-field="other_links" class="flex items-center">
                            <i class="fas fa-external-link-alt mr-2 text-purple-600"></i>
                            <div class="other-links text-sm flex-1">--</div>
                        </div>
                    </div>
                </div>

                <!-- Skills & Languages -->
                <div class="bg-white rounded-lg border border-gray-200 p-4">
                    <h3 class="text-base font-semibold mb-3 text-gray-800 flex items-center">
                        <i class="fas fa-tools mr-2 text-blue-600"></i>Skills & Languages
                    </h3>
                    <div class="space-y-3">
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Technical Skills</h4>
                            <div class="skills-list flex flex-wrap gap-1" data-field="skills_list">
                                <!-- Skills will be populated here -->
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Languages</h4>
                            <div class="languages-list flex flex-wrap gap-1" data-field="languages_list">
                                <!-- Languages will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Collapsible Analysis Details -->
                <div class="bg-white rounded-lg border border-gray-200">
                    <button onclick="toggleAnalysisDetails()" class="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 rounded-lg transition-colors">
                        <h3 class="text-base font-semibold text-gray-800 flex items-center">
                            <i class="fas fa-microscope mr-2 text-purple-600"></i>Detailed Analysis
                    </h3>
                        <i class="fas fa-chevron-down text-gray-400 transform transition-transform" id="analysisChevron"></i>
                    </button>
                    <div id="analysisDetails" class="hidden border-t border-gray-200">
                        <div class="p-4 space-y-3">
                            <div class="analysis-section">
                                <h4 class="text-sm font-medium text-gray-700 mb-1">Content Analysis</h4>
                                <p class="text-xs text-gray-600 bg-gray-50 p-2 rounded" data-field="content_analysis">Analysis pending...</p>
                        </div>
                            <div class="analysis-section">
                                <h4 class="text-sm font-medium text-gray-700 mb-1">Format Analysis</h4>
                                <p class="text-xs text-gray-600 bg-gray-50 p-2 rounded" data-field="format_analysis">Analysis pending...</p>
                    </div>
                            <div class="analysis-section">
                                <h4 class="text-sm font-medium text-gray-700 mb-1">Skills Analysis</h4>
                                <p class="text-xs text-gray-600 bg-gray-50 p-2 rounded" data-field="skills_analysis">Analysis pending...</p>
                            </div>
                            <div class="analysis-section">
                                <h4 class="text-sm font-medium text-gray-700 mb-1">Sections Analysis</h4>
                                <p class="text-xs text-gray-600 bg-gray-50 p-2 rounded" data-field="sections_analysis">Analysis pending...</p>
                            </div>
                            <div class="analysis-section">
                                <h4 class="text-sm font-medium text-gray-700 mb-1">Style Analysis</h4>
                                <p class="text-xs text-gray-600 bg-gray-50 p-2 rounded" data-field="style_analysis">Analysis pending...</p>
                            </div>
                            </div>
                        </div>
                    </div>
                    
                <!-- AI Analysis Info - Collapsible -->
                <div class="bg-white rounded-lg border border-gray-200">
                    <button onclick="toggleAIDetails()" class="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 rounded-lg transition-colors">
                        <h3 class="text-base font-semibold text-gray-800 flex items-center">
                            <i class="fas fa-robot mr-2 text-blue-600"></i>AI Analysis Details
                        </h3>
                        <i class="fas fa-chevron-down text-gray-400 transform transition-transform" id="aiChevron"></i>
                            </button>
                    <div id="aiDetails" class="hidden border-t border-gray-200">
                        <div class="p-4 space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-500">AI Provider:</span>
                                <span class="font-medium" data-field="ai_provider">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-500">Model Used:</span>
                                <span class="font-medium" data-field="ai_model">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-500">Processing Time:</span>
                                <span class="font-medium" data-field="processing_time">--</span>
                            </div>
                            <div class="mt-3">
                                <span class="text-gray-500">Raw AI Response:</span>
                                <div class="mt-1 bg-gray-50 p-2 rounded text-xs max-h-32 overflow-y-auto" data-field="ai_response">AI response will be displayed here...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style>
.btn-compact {
    @apply px-3 py-1.5 rounded text-sm font-medium transition-colors duration-200;
}

.skill-tag {
    @apply inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-1 mb-1;
}

.language-tag {
    @apply inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mr-1 mb-1;
}

.analysis-section {
    @apply border-l-2 border-blue-200 pl-3;
}

/* Smooth transitions for collapsible sections */
#analysisDetails, #aiDetails {
    transition: all 0.3s ease-in-out;
}

.popup-header {
    @apply sticky top-0 z-10;
}

/* Compact scrollbar */
.cv-detail-content::-webkit-scrollbar {
    width: 6px;
}

.cv-detail-content::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.cv-detail-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.cv-detail-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

<script>
function toggleAnalysisDetails() {
    const details = document.getElementById('analysisDetails');
    const chevron = document.getElementById('analysisChevron');
    
    if (details.classList.contains('hidden')) {
        details.classList.remove('hidden');
        chevron.style.transform = 'rotate(180deg)';
        } else {
        details.classList.add('hidden');
        chevron.style.transform = 'rotate(0deg)';
    }
}

function toggleAIDetails() {
    const details = document.getElementById('aiDetails');
    const chevron = document.getElementById('aiChevron');
    
    if (details.classList.contains('hidden')) {
        details.classList.remove('hidden');
        chevron.style.transform = 'rotate(180deg)';
    } else {
        details.classList.add('hidden');
        chevron.style.transform = 'rotate(0deg)';
    }
}
</script>