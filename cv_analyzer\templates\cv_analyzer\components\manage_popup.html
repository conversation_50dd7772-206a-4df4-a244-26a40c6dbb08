<!-- Manage Data Popup -->
<div id="managePopup" class="popup-overlay">
    <div class="popup-content" style="width: 100vw; max-width: 100vw;">
        <div class="popup-header">
            <h3 class="text-xl font-bold">
                <i class="fas fa-cogs mr-2 text-purple-600"></i>
                Manage Data
            </h3>
            <button class="popup-close" onclick="closePopup('managePopup')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="manage-tabs mb-6">
            <div class="flex space-x-2 mb-4">
                <button class="tab-btn active" onclick="switchManageTab('companies')">Companies</button>
                <button class="tab-btn" onclick="switchManageTab('vacancies')">Vacancies</button>
                <button class="tab-btn" onclick="switchManageTab('bulk')">Bulk Actions</button>
            </div>
            
            <!-- Companies Management -->
            <div id="companies-manage" class="manage-section active">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-semibold">Companies</h4>
                    <button class="btn btn-primary" onclick="showAddCompanyForm()">
                        <i class="fas fa-plus mr-2"></i>
                        Add Company
                    </button>
                </div>
                
                <!-- Add Company Form -->
                <div id="addCompanyForm" class="hidden bg-gray-50 p-4 rounded-lg mb-4">
                    <form id="companyForm">
                        {% csrf_token %}
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="block text-sm font-medium mb-2">Company Name</label>
                                <input type="text" name="name" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label class="block text-sm font-medium mb-2">Industry</label>
                                <select name="industry" class="form-control" required>
                                    <option value="">Select industry...</option>
                                    <option value="technology">Technology</option>
                                    <option value="finance">Finance</option>
                                    <option value="healthcare">Healthcare</option>
                                    <option value="education">Education</option>
                                    <option value="manufacturing">Manufacturing</option>
                                    <option value="retail">Retail</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="block text-sm font-medium mb-2">Location</label>
                                <input type="text" name="location" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="block text-sm font-medium mb-2">Website</label>
                                <input type="url" name="website" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="block text-sm font-medium mb-2">Description</label>
                            <textarea name="description" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="flex justify-end space-x-3 mt-4">
                            <button type="button" class="btn btn-secondary" onclick="hideAddCompanyForm()">
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-2"></i>
                                Save Company
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Companies List -->
                <div class="overflow-x-auto">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Industry</th>
                                <th>Location</th>
                                <th>Vacancies</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="companiesTableBody">
                            {% for company in companies %}
                            <tr>
                                <td>{{ company.name }}</td>
                                <td>{{ company.get_industry_display }}</td>
                                <td>{{ company.location|default:"--" }}</td>
                                <td>{{ company.vacancy_count|default:"0" }}</td>
                                <td>
                                    <button class="btn btn-secondary btn-sm" onclick="editCompany({{ company.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="deleteCompany({{ company.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-gray-500">No companies found</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Vacancies Management -->
            <div id="vacancies-manage" class="manage-section" style="display: none;">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-semibold">Vacancies</h4>
                    <button class="btn btn-primary" onclick="showAddVacancyForm()">
                        <i class="fas fa-plus mr-2"></i>
                        Add Vacancy
                    </button>
                </div>
                
                <!-- Add Vacancy Form -->
                <div id="addVacancyForm" class="hidden bg-gray-50 p-4 rounded-lg mb-4">
                    <form id="vacancyForm">
                        {% csrf_token %}
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="block text-sm font-medium mb-2">Job Title</label>
                                <input type="text" name="title" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label class="block text-sm font-medium mb-2">Company</label>
                                <select name="company" class="form-control" required>
                                    <option value="">Select company...</option>
                                    {% for company in companies %}
                                    <option value="{{ company.id }}">{{ company.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="block text-sm font-medium mb-2">Department</label>
                                <input type="text" name="department" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="block text-sm font-medium mb-2">Experience Level</label>
                                <select name="experience_level" class="form-control">
                                    <option value="">Select level...</option>
                                    <option value="entry">Entry Level</option>
                                    <option value="mid">Mid Level</option>
                                    <option value="senior">Senior Level</option>
                                    <option value="executive">Executive</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="block text-sm font-medium mb-2">Employment Type</label>
                                <select name="employment_type" class="form-control">
                                    <option value="full_time">Full Time</option>
                                    <option value="part_time">Part Time</option>
                                    <option value="contract">Contract</option>
                                    <option value="freelance">Freelance</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="block text-sm font-medium mb-2">Location</label>
                                <input type="text" name="location" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="block text-sm font-medium mb-2">Job Description</label>
                            <textarea name="description" class="form-control" rows="4" required></textarea>
                        </div>
                        <div class="form-group">
                            <label class="block text-sm font-medium mb-2">Requirements</label>
                            <textarea name="requirements" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="flex justify-end space-x-3 mt-4">
                            <button type="button" class="btn btn-secondary" onclick="hideAddVacancyForm()">
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-2"></i>
                                Save Vacancy
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Vacancies List -->
                <div class="overflow-x-auto">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Company</th>
                                <th>Status</th>
                                <th>Applications</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="vacanciesTableBody">
                            {% for vacancy in recent_vacancies %}
                            <tr>
                                <td>{{ vacancy.title }}</td>
                                <td>{{ vacancy.company.name }}</td>
                                <td>
                                    <span class="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                                        {{ vacancy.get_status_display }}
                                    </span>
                                </td>
                                <td>{{ vacancy.applications_count|default:"0" }}</td>
                                <td>
                                    <button class="btn btn-secondary btn-sm" onclick="editVacancy({{ vacancy.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="deleteVacancy({{ vacancy.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-gray-500">No vacancies found</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Bulk Actions -->
            <div id="bulk-manage" class="manage-section" style="display: none;">
                <h4 class="text-lg font-semibold mb-4">Bulk Operations</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bulk-action-card p-4 border rounded-lg">
                        <h5 class="font-semibold mb-2">
                            <i class="fas fa-brain mr-2 text-blue-600"></i>
                            Bulk CV Analysis
                        </h5>
                        <p class="text-sm text-gray-600 mb-4">Analyze all pending CVs at once</p>
                        <button class="btn btn-primary" onclick="bulkAnalyze()">
                            Start Bulk Analysis
                        </button>
                    </div>
                    
                    <div class="bulk-action-card p-4 border rounded-lg">
                        <h5 class="font-semibold mb-2">
                            <i class="fas fa-match mr-2 text-green-600"></i>
                            Bulk Matching
                        </h5>
                        <p class="text-sm text-gray-600 mb-4">Match all analyzed CVs to vacancies</p>
                        <button class="btn btn-primary" onclick="bulkMatch()">
                            Start Bulk Matching
                        </button>
                    </div>
                    
                    <div class="bulk-action-card p-4 border rounded-lg">
                        <h5 class="font-semibold mb-2">
                            <i class="fas fa-download mr-2 text-purple-600"></i>
                            Export Data
                        </h5>
                        <p class="text-sm text-gray-600 mb-4">Export CVs and analysis results</p>
                        <button class="btn btn-primary" onclick="exportData()">
                            Export to Excel
                        </button>
                    </div>
                    
                    <div class="bulk-action-card p-4 border rounded-lg">
                        <h5 class="font-semibold mb-2">
                            <i class="fas fa-trash mr-2 text-red-600"></i>
                            Cleanup
                        </h5>
                        <p class="text-sm text-gray-600 mb-4">Remove old and inactive records</p>
                        <button class="btn btn-danger" onclick="cleanup()">
                            Start Cleanup
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>