{% comment %}
    Usage: {% include 'cv_analyzer/components/modal.html' with id='myModal' title='Modal Title' content=content %}
{% endcomment %}

<div id="{{ id }}" tabindex="-1" aria-hidden="true" class="modal hidden">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                {% if icon %}
                    {% include 'cv_analyzer/components/icon.html' with icon=icon solid=True class='me-2' %}
                {% endif %}
                {{ title }}
            </h3>
            <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="{{ id }}">
                {% include 'cv_analyzer/components/icon.html' with icon='fa-times' solid=True %}
                <span class="sr-only">Close modal</span>
            </button>
        </div>
        <div class="mb-6">
            {{ content }}
        </div>
        <div class="flex justify-end space-x-2">
            {% if show_cancel %}
            <button type="button" class="btn-secondary" data-modal-hide="{{ id }}">
                {% include 'cv_analyzer/components/icon.html' with icon='fa-times' solid=True class='me-1' %}
                Cancel
            </button>
            {% endif %}
            {% if show_confirm %}
            <button type="button" class="btn-primary" id="{{ id }}-confirm">
                {% include 'cv_analyzer/components/icon.html' with icon='fa-check' solid=True class='me-1' %}
                Confirm
            </button>
            {% endif %}
            {{ footer }}
        </div>
    </div>
</div> 