{% comment %}
    Usage: {% include 'cv_analyzer/components/pagination.html' with page_obj=page_obj %}
{% endcomment %}

{% if page_obj.paginator.num_pages > 1 %}
<nav class="flex items-center justify-between pt-4" aria-label="Table navigation">
    <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
        Showing <span class="font-semibold text-gray-900 dark:text-white">{{ page_obj.start_index }}</span> to 
        <span class="font-semibold text-gray-900 dark:text-white">{{ page_obj.end_index }}</span> of 
        <span class="font-semibold text-gray-900 dark:text-white">{{ page_obj.paginator.count }}</span>
    </span>
    <ul class="inline-flex -space-x-px text-sm h-8">
        {% if page_obj.has_previous %}
        <li>
            <a href="?page={{ page_obj.previous_page_number }}" class="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                {% include 'cv_analyzer/components/icon.html' with icon='fa-chevron-left' solid=True class='me-1' %}
                Previous
            </a>
        </li>
        {% endif %}
        
        {% for i in page_obj.paginator.page_range %}
            {% if page_obj.number == i %}
            <li>
                <span class="flex items-center justify-center px-3 h-8 text-blue-600 border border-gray-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white">{{ i }}</span>
            </li>
            {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
            <li>
                <a href="?page={{ i }}" class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">{{ i }}</a>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if page_obj.has_next %}
        <li>
            <a href="?page={{ page_obj.next_page_number }}" class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                Next
                {% include 'cv_analyzer/components/icon.html' with icon='fa-chevron-right' solid=True class='ms-1' %}
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %} 