<!-- Reports Popup -->
<div id="reportsPopup" class="popup-overlay">
    <div class="popup-content" style="width: 100vw; max-width: 100vw;">
        <div class="popup-header">
            <h3 class="text-xl font-bold">
                <i class="fas fa-chart-bar mr-2 text-orange-600"></i>
                Reports & Analytics
            </h3>
            <button class="popup-close" onclick="closePopup('reportsPopup')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="reports-content">
            <!-- Report Type Selection -->
            <div class="report-types mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="report-card p-4 border rounded-lg cursor-pointer hover:border-blue-500" onclick="generateReport('summary')">
                        <div class="text-center">
                            <i class="fas fa-chart-pie text-3xl text-blue-600 mb-3"></i>
                            <h5 class="font-semibold mb-2">Summary Report</h5>
                            <p class="text-sm text-gray-600">Overall system statistics and metrics</p>
                        </div>
                    </div>
                    
                    <div class="report-card p-4 border rounded-lg cursor-pointer hover:border-blue-500" onclick="generateReport('cv-analysis')">
                        <div class="text-center">
                            <i class="fas fa-file-alt text-3xl text-green-600 mb-3"></i>
                            <h5 class="font-semibold mb-2">CV Analysis Report</h5>
                            <p class="text-sm text-gray-600">Detailed CV analysis results</p>
                        </div>
                    </div>
                    
                    <div class="report-card p-4 border rounded-lg cursor-pointer hover:border-blue-500" onclick="generateReport('matching')">
                        <div class="text-center">
                            <i class="fas fa-search text-3xl text-purple-600 mb-3"></i>
                            <h5 class="font-semibold mb-2">Matching Report</h5>
                            <p class="text-sm text-gray-600">CV to job matching results</p>
                        </div>
                    </div>
                    
                    <div class="report-card p-4 border rounded-lg cursor-pointer hover:border-blue-500" onclick="generateReport('performance')">
                        <div class="text-center">
                            <i class="fas fa-tachometer-alt text-3xl text-orange-600 mb-3"></i>
                            <h5 class="font-semibold mb-2">Performance Report</h5>
                            <p class="text-sm text-gray-600">System performance metrics</p>
                        </div>
                    </div>
                    
                    <div class="report-card p-4 border rounded-lg cursor-pointer hover:border-blue-500" onclick="generateReport('trends')">
                        <div class="text-center">
                            <i class="fas fa-chart-line text-3xl text-red-600 mb-3"></i>
                            <h5 class="font-semibold mb-2">Trends Report</h5>
                            <p class="text-sm text-gray-600">Hiring trends and patterns</p>
                        </div>
                    </div>
                    
                    <div class="report-card p-4 border rounded-lg cursor-pointer hover:border-blue-500" onclick="generateReport('custom')">
                        <div class="text-center">
                            <i class="fas fa-cog text-3xl text-gray-600 mb-3"></i>
                            <h5 class="font-semibold mb-2">Custom Report</h5>
                            <p class="text-sm text-gray-600">Build your own custom report</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Report Configuration -->
            <div id="reportConfig" class="report-config mb-6" style="display: none;">
                <h4 class="text-lg font-semibold mb-4">Report Configuration</h4>
                
                <div class="form-grid mb-4">
                    <div class="form-group">
                        <label class="block text-sm font-medium mb-2">Date Range</label>
                        <select id="dateRange" class="form-control">
                            <option value="7">Last 7 days</option>
                            <option value="30" selected>Last 30 days</option>
                            <option value="90">Last 3 months</option>
                            <option value="365">Last year</option>
                            <option value="custom">Custom range</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="customDateRange" style="display: none;">
                        <label class="block text-sm font-medium mb-2">From</label>
                        <input type="date" id="fromDate" class="form-control">
                    </div>
                    
                    <div class="form-group" id="customDateRangeTo" style="display: none;">
                        <label class="block text-sm font-medium mb-2">To</label>
                        <input type="date" id="toDate" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label class="block text-sm font-medium mb-2">Format</label>
                        <select id="reportFormat" class="form-control">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                            <option value="html">HTML</option>
                        </select>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="report-filters mb-4">
                    <h5 class="font-semibold mb-3">Filters</h5>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="block text-sm font-medium mb-2">Company</label>
                            <select id="companyFilter" class="form-control">
                                <option value="">All Companies</option>
                                {% for company in companies %}
                                <option value="{{ company.id }}">{{ company.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="block text-sm font-medium mb-2">Vacancy</label>
                            <select id="vacancyFilter" class="form-control">
                                <option value="">All Vacancies</option>
                                {% for vacancy in active_vacancies %}
                                <option value="{{ vacancy.id }}">{{ vacancy.title }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="block text-sm font-medium mb-2">CV Status</label>
                            <select id="statusFilter" class="form-control">
                                <option value="">All Statuses</option>
                                <option value="uploaded">Uploaded</option>
                                <option value="processing">Processing</option>
                                <option value="analyzed">Analyzed</option>
                                <option value="matched">Matched</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="block text-sm font-medium mb-2">Score Range</label>
                            <select id="scoreRange" class="form-control">
                                <option value="">All Scores</option>
                                <option value="90-100">90-100%</option>
                                <option value="80-89">80-89%</option>
                                <option value="70-79">70-79%</option>
                                <option value="60-69">60-69%</option>
                                <option value="0-59">Below 60%</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button class="btn btn-secondary" onclick="resetReportConfig()">
                        Reset
                    </button>
                    <button class="btn btn-primary" onclick="generateSelectedReport()">
                        <i class="fas fa-download mr-2"></i>
                        Generate Report
                    </button>
                </div>
            </div>
            
            <!-- Quick Stats Preview -->
            <div class="quick-stats-preview">
                <h4 class="text-lg font-semibold mb-4">Quick Statistics</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div class="stat-card bg-blue-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">{{ total_cvs }}</div>
                        <div class="text-sm text-blue-600">Total CVs</div>
                    </div>
                    
                    <div class="stat-card bg-green-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">{{ analyzed_cvs_count }}</div>
                        <div class="text-sm text-green-600">Analyzed</div>
                    </div>
                    
                    <div class="stat-card bg-purple-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600">{{ matched_cvs_count }}</div>
                        <div class="text-sm text-purple-600">Matched</div>
                    </div>
                    
                    <div class="stat-card bg-orange-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-orange-600">{{ avg_score|floatformat:1 }}%</div>
                        <div class="text-sm text-orange-600">Avg Score</div>
                    </div>
                </div>
                
                <!-- Mini Charts -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="chart-container bg-white p-4 rounded-lg border">
                        <h5 class="font-semibold mb-3">CV Analysis Trend</h5>
                        <canvas id="miniTrendChart" height="150"></canvas>
                    </div>
                    
                    <div class="chart-container bg-white p-4 rounded-lg border">
                        <h5 class="font-semibold mb-3">Score Distribution</h5>
                        <canvas id="miniScoreChart" height="150"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Report Generation Progress -->
            <div id="reportProgress" class="report-progress" style="display: none;">
                <div class="text-center">
                    <div class="spinner mb-4">
                        <i class="fas fa-spinner fa-spin text-4xl text-orange-600"></i>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">Generating Report</h4>
                    <p class="text-gray-600 mb-4">Please wait while we prepare your report...</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text">0% Complete</div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.report-card {
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.report-card.selected {
    border-color: #3b82f6;
    background: #eff6ff;
}

.stat-card {
    text-align: center;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: scale(1.05);
}

.chart-container {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>