{% comment %}
    Usage: {% include 'cv_analyzer/components/stats-card.html' with title='Total Users' value=100 icon='fa-users' trend=10 %}
{% endcomment %}

<div class="custom-card">
    <div class="flex items-center">
        {% if icon %}
        <div class="p-3 me-4 text-{{ color|default:'blue' }}-500 bg-{{ color|default:'blue' }}-100 rounded-full dark:text-{{ color|default:'blue' }}-400 dark:bg-{{ color|default:'blue' }}-900">
            {% include 'cv_analyzer/components/icon.html' with icon=icon solid=True class='w-6 h-6' %}
        </div>
        {% endif %}
        <div>
            <div class="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                {{ title }}
            </div>
            <div class="text-2xl font-semibold text-gray-900 dark:text-white">
                {{ value }}
            </div>
            {% if trend is not None %}
            <div class="flex items-center mt-1">
                {% if trend >= 0 %}
                <span class="text-green-500 dark:text-green-400">
                    {% include 'cv_analyzer/components/icon.html' with icon='fa-arrow-up' solid=True class='me-1' %}
                    {{ trend }}%
                </span>
                {% else %}
                <span class="text-red-500 dark:text-red-400">
                    {% include 'cv_analyzer/components/icon.html' with icon='fa-arrow-down' solid=True class='me-1' %}
                    {{ trend|abs }}%
                </span>
                {% endif %}
                <span class="text-sm text-gray-500 dark:text-gray-400 ms-2">vs last month</span>
            </div>
            {% endif %}
        </div>
    </div>
</div> 