{% comment %}
    Usage: {% include 'cv_analyzer/components/table.html' with headers=headers rows=rows %}
{% endcomment %}

<div class="relative overflow-x-auto shadow-md sm:rounded-lg">
    <table class="custom-table">
        <thead>
            <tr>
                {% for header in headers %}
                <th scope="col">
                    {% if header.icon %}
                        {% include 'cv_analyzer/components/icon.html' with icon=header.icon solid=True class='me-1' %}
                    {% endif %}
                    {{ header.text }}
                </th>
                {% endfor %}
            </tr>
        </thead>
        <tbody>
            {% for row in rows %}
            <tr class="{% cycle '' 'bg-gray-50 dark:bg-gray-700' %}">
                {% for cell in row %}
                <td>{{ cell }}</td>
                {% endfor %}
            </tr>
            {% empty %}
            <tr>
                <td colspan="{{ headers|length }}" class="text-center py-4">
                    {% include 'cv_analyzer/components/icon.html' with icon='fa-info-circle' solid=True class='me-2' %}
                    No data available
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div> 