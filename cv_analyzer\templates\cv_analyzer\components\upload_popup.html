<!-- Upload Popup -->
<div id="uploadPopup" class="popup-overlay">
    <div class="popup-content" style="width: 100vw; max-width: 100vw;">
        <div class="popup-header">
            <h3 class="text-xl font-bold">
                <i class="fas fa-upload mr-2 text-blue-600"></i>
                Upload CVs
            </h3>
            <button class="popup-close" onclick="closePopup('uploadPopup')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="upload-methods mb-6">
            <div class="flex space-x-2 mb-4">
                <button class="tab-btn active" onclick="switchUploadMethod('single')">Single Upload</button>
                <button class="tab-btn" onclick="switchUploadMethod('batch')">Batch Upload</button>
                <button class="tab-btn" onclick="switchUploadMethod('integrations')">Integrations</button>
            </div>
            
            <!-- Single Upload -->
            <div id="single-upload" class="upload-method active">
                <form id="singleUploadForm" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="form-group mb-4">
                        <label class="block text-sm font-medium mb-2">Select Vacancy (Optional)</label>
                        <select name="vacancy" class="form-control">
                            <option value="">General CV Upload (No specific vacancy)</option>
                            {% for vacancy in vacancies %}
                            <option value="{{ vacancy.id }}">{{ vacancy.title }} - {{ vacancy.company.name }}</option>
                            {% endfor %}
                        </select>
                        <small class="text-gray-500 mt-1">You can upload CVs without selecting a specific vacancy. They will be available for analysis and matching later.</small>
                    </div>
                    
                    <div class="dropzone" id="singleDropzone">
                        <div class="file-upload-icon">
                            <i class="fas fa-file-upload text-3xl text-white"></i>
                        </div>
                        <h4 class="text-xl font-semibold mb-2">Drop your CV here</h4>
                        <p class="text-gray-600 mb-4">or click to browse files</p>
                        <input type="file" name="cv_file" accept=".pdf,.doc,.docx" style="display: none;">
                        <div class="file-info" style="display: none;">
                            <div class="selected-file bg-blue-50 p-3 rounded-lg border border-blue-200">
                                <div class="flex items-center justify-between">
                                    <span class="file-name font-medium"></span>
                                    <button type="button" class="text-red-500 hover:text-red-700" onclick="clearFile()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" class="btn btn-secondary" onclick="closePopup('uploadPopup')">
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary" disabled>
                            <i class="fas fa-upload mr-2"></i>
                            Upload CV
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Batch Upload -->
            <div id="batch-upload" class="upload-method" style="display: none;">
                <form id="batchUploadForm" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="form-group mb-4">
                        <label class="block text-sm font-medium mb-2">Select Vacancy (Optional)</label>
                        <select name="vacancy" class="form-control">
                            <option value="">General CV Upload (No specific vacancy)</option>
                            {% for vacancy in vacancies %}
                            <option value="{{ vacancy.id }}">{{ vacancy.title }} - {{ vacancy.company.name }}</option>
                            {% endfor %}
                        </select>
                        <small class="text-gray-500 mt-1">Upload multiple CVs without selecting a specific vacancy. They will be available for analysis and matching later.</small>
                    </div>
                    
                    <div class="dropzone" id="batchDropzone">
                        <div class="file-upload-icon">
                            <i class="fas fa-files text-3xl text-white"></i>
                        </div>
                        <h4 class="text-xl font-semibold mb-2">Drop multiple CVs here</h4>
                        <p class="text-gray-600 mb-4">Maximum 10 files, 50MB total</p>
                        <input type="file" name="cv_files" accept=".pdf,.doc,.docx" multiple style="display: none;">
                        <div class="files-info" style="display: none;">
                            <div class="selected-files bg-blue-50 p-3 rounded-lg border border-blue-200">
                                <div class="files-list"></div>
                                <button type="button" class="text-red-500 hover:text-red-700 mt-2" onclick="clearAllFiles()">
                                    <i class="fas fa-times mr-1"></i>
                                    Clear All
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" class="btn btn-secondary" onclick="closePopup('uploadPopup')">
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary" disabled>
                            <i class="fas fa-upload mr-2"></i>
                            Upload CVs
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Integrations -->
            <div id="integrations-upload" class="upload-method" style="display: none;">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="integration-card p-4 border rounded-lg text-center hover:border-blue-500 cursor-pointer">
                        <i class="fab fa-google-drive text-4xl text-blue-600 mb-3"></i>
                        <h5 class="font-semibold mb-2">Google Drive</h5>
                        <p class="text-sm text-gray-600">Import CVs from Google Drive</p>
                        <button class="btn btn-primary mt-3" onclick="connectGoogleDrive()">
                            Connect
                        </button>
                    </div>
                    
                    <div class="integration-card p-4 border rounded-lg text-center hover:border-blue-500 cursor-pointer">
                        <i class="fab fa-microsoft text-4xl text-blue-600 mb-3"></i>
                        <h5 class="font-semibold mb-2">OneDrive</h5>
                        <p class="text-sm text-gray-600">Import CVs from OneDrive</p>
                        <button class="btn btn-primary mt-3" onclick="connectOneDrive()">
                            Connect
                        </button>
                    </div>
                    
                    <div class="integration-card p-4 border rounded-lg text-center hover:border-blue-500 cursor-pointer">
                        <i class="fas fa-envelope text-4xl text-green-600 mb-3"></i>
                        <h5 class="font-semibold mb-2">Email</h5>
                        <p class="text-sm text-gray-600">Process CV from emails</p>
                        <button class="btn btn-primary mt-3" onclick="setupEmailIntegration()">
                            Setup
                        </button>
                    </div>
                    
                    <div class="integration-card p-4 border rounded-lg text-center hover:border-blue-500 cursor-pointer">
                        <i class="fas fa-folder text-4xl text-orange-600 mb-3"></i>
                        <h5 class="font-semibold mb-2">Shared Folder</h5>
                        <p class="text-sm text-gray-600">Monitor shared folders</p>
                        <button class="btn btn-primary mt-3" onclick="setupFolderMonitoring()">
                            Setup
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>