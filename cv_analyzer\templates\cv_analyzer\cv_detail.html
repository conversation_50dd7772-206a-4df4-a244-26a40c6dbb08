{% load static %}
{% load form_tags %}



{% block extra_css %}
<style>
    .progress-ring {
        transform: rotate(-90deg);
    }
    
    .progress-ring__circle {
        transition: stroke-dasharray 0.35s;
        transform: rotate(-90deg);
        transform-origin: 50% 50%;
    }
    
    .skill-tag {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        margin: 0.25rem;
        background: #f3f4f6;
        border-radius: 9999px;
        font-size: 0.875rem;
        color: #374151;
        border: 1px solid #e5e7eb;
        transition: all 0.2s ease;
    }
    
    .skill-tag:hover {
        background: #e5e7eb;
        transform: translateY(-1px);
    }
    
    .timeline-item {
        position: relative;
        padding-left: 3rem;
        padding-bottom: 2rem;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: 0.75rem;
        top: 0;
        bottom: -2rem;
        width: 2px;
        background: #e5e7eb;
    }
    
    .timeline-item:last-child::before {
        display: none;
    }
    
    .timeline-icon {
        position: absolute;
        left: 0;
        top: 0;
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid;
        background: white;
        z-index: 10;
    }
    
    .analysis-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .quick-action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s ease;
        color: white;
        border: none;
        cursor: pointer;
        min-width: 110px;
    }

    .quick-action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .quick-action-btn i {
        font-size: 0.875rem;
        margin-right: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
      <!-- Quick Actions -->
        <div class="flex flex-wrap gap-3 justify-center">
            <button onclick="downloadCV()" class="quick-action-btn bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-download"></i>Download
            </button>
            <button onclick="shareCV()" class="quick-action-btn bg-green-600 hover:bg-green-700">
                <i class="fas fa-share"></i>Share
            </button>
            <button onclick="reanalyzeCV()" class="quick-action-btn bg-purple-600 hover:bg-purple-700">
                <i class="fas fa-redo"></i>Re-analyze
            </button>
            <button onclick="matchToJobs()" class="quick-action-btn bg-gray-800 hover:bg-gray-900 text-yellow-400">
                <i class="fas fa-search"></i>Match Jobs
            </button>
            <button onclick="addNotes()" class="quick-action-btn bg-yellow-600 hover:bg-yellow-700">
                <i class="fas fa-sticky-note"></i>Notes
            </button>
            <button onclick="archiveCV()" class="quick-action-btn bg-red-600 hover:bg-red-700">
                <i class="fas fa-archive"></i>Archive
            </button>
        </div>
    </div>
</div>
    <div class="mb-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ cv.candidate_name|default:"Unknown Candidate" }}
                </h1>
                <p class="text-gray-600 dark:text-gray-400">
                    CV #{{ cv.id }} • Uploaded {{ cv.uploaded_at|date:"M d, Y" }}
                </p>
            </div>
        </div>
        
       

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Latest Analysis Card - Hidden -->
            {% comment %}
            {% if latest_analysis %}
            <div class="analysis-card">
                <div class="flex justify-between items-start mb-6">
                    <div>
                        <h3 class="text-xl font-bold mb-2">Latest Analysis Results</h3>
                        <p class="text-blue-100">
                            Analyzed {{ latest_analysis.created_at|timesince }} ago
                        </p>
                    </div>
                    <div class="text-center">
                        <div class="relative inline-flex items-center justify-center">
                            <svg class="progress-ring w-20 h-20" width="80" height="80">
                                <circle class="progress-ring__circle" stroke="rgba(255,255,255,0.2)" stroke-width="4" fill="transparent" r="36" cx="40" cy="40"/>
                                <circle class="progress-ring__circle" stroke="white" stroke-width="4" fill="transparent" r="36" cx="40" cy="40"
                                        stroke-dasharray="{{ latest_analysis.overall_score|mul:'2.26'|floatformat:0 }} 226"/>
                            </svg>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-2xl font-bold">{{ latest_analysis.overall_score }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold">{{ latest_analysis.technical_score|default:0 }}%</div>
                        <div class="text-sm text-blue-100">Technical</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold">{{ latest_analysis.experience_score|default:0 }}%</div>
                        <div class="text-sm text-blue-100">Experience</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold">{{ latest_analysis.education_score|default:0 }}%</div>
                        <div class="text-sm text-blue-100">Education</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold">{{ latest_analysis.soft_skills_score|default:0 }}%</div>
                        <div class="text-sm text-blue-100">Soft Skills</div>
                    </div>
                </div>
            </div>
            {% endif %}
            {% endcomment %}

            <!-- CV Information -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    <i class="fas fa-user mr-2 text-blue-600"></i>Candidate Information
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-user mr-2 text-blue-500"></i>Full Name
                        </label>
                        <p class="text-gray-900 dark:text-white">{{ latest_analysis.name|default:cv.candidate_name|default:"Not provided" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-envelope mr-2 text-green-500"></i>Email
                        </label>
                        <p class="text-gray-900 dark:text-white">{{ latest_analysis.email|default:cv.email|default:"Not provided" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-phone mr-2 text-purple-500"></i>Phone
                        </label>
                        <p class="text-gray-900 dark:text-white">{{ latest_analysis.phone_number|default:"Not provided" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-map-marker-alt mr-2 text-red-500"></i>Location
                        </label>
                        <p class="text-gray-900 dark:text-white">{{ latest_analysis.location|default:"Not provided" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-briefcase mr-2 text-orange-500"></i>Years of Experience
                        </label>
                        <p class="text-gray-900 dark:text-white">{{ latest_analysis.years_of_experience|default:"Not provided" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-graduation-cap mr-2 text-indigo-500"></i>Education Level
                        </label>
                        <p class="text-gray-900 dark:text-white">{{ latest_analysis.education_level|default:"Not provided" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-flag mr-2 text-yellow-500"></i>Status
                        </label>
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                            {{ cv.get_status_display }}
                        </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-tag mr-2 text-pink-500"></i>Category
                        </label>
                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                            {{ cv.category|default:"Not specified" }}
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- Detailed Analysis Scores -->
            {% if latest_analysis %}
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    <i class="fas fa-chart-bar mr-2 text-purple-600"></i>Detailed Analysis Scores
                </h3>
                
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg text-center">
                        <div class="text-blue-600 dark:text-blue-300 mb-2">
                            <i class="fas fa-file-alt text-2xl"></i>
                        </div>
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-300">{{ latest_analysis.content_score }}%</div>
                        <div class="text-sm text-blue-600 dark:text-blue-300">Content Score</div>
                    </div>
                    <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg text-center">
                        <div class="text-green-600 dark:text-green-300 mb-2">
                            <i class="fas fa-palette text-2xl"></i>
                        </div>
                        <div class="text-2xl font-bold text-green-600 dark:text-green-300">{{ latest_analysis.format_score }}%</div>
                        <div class="text-sm text-green-600 dark:text-green-300">Format Score</div>
                    </div>
                    <div class="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg text-center">
                        <div class="text-purple-600 dark:text-purple-300 mb-2">
                            <i class="fas fa-layer-group text-2xl"></i>
                        </div>
                        <div class="text-2xl font-bold text-purple-600 dark:text-purple-300">{{ latest_analysis.sections_score }}%</div>
                        <div class="text-sm text-purple-600 dark:text-purple-300">Sections Score</div>
                    </div>
                    <div class="bg-orange-50 dark:bg-orange-900 p-4 rounded-lg text-center">
                        <div class="text-orange-600 dark:text-orange-300 mb-2">
                            <i class="fas fa-tools text-2xl"></i>
                        </div>
                        <div class="text-2xl font-bold text-orange-600 dark:text-orange-300">{{ latest_analysis.skills_score }}%</div>
                        <div class="text-sm text-orange-600 dark:text-orange-300">Skills Score</div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                        <div class="text-gray-600 dark:text-gray-300 mb-2">
                            <i class="fas fa-paint-brush text-2xl"></i>
                        </div>
                        <div class="text-2xl font-bold text-gray-600 dark:text-gray-300">{{ latest_analysis.style_score }}%</div>
                        <div class="text-sm text-gray-600 dark:text-gray-300">Style Score</div>
                    </div>
                    <div class="bg-indigo-50 dark:bg-indigo-900 p-4 rounded-lg text-center">
                        <div class="text-indigo-600 dark:text-indigo-300 mb-2">
                            <i class="fas fa-trophy text-2xl"></i>
                        </div>
                        <div class="text-2xl font-bold text-indigo-600 dark:text-indigo-300">{{ latest_analysis.overall_score }}%</div>
                        <div class="text-sm text-indigo-600 dark:text-indigo-300">Overall Score</div>
                    </div>
                </div>
            </div>
            
            <!-- Skills and Languages -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    <i class="fas fa-tools mr-2 text-blue-600"></i>Skills & Languages
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium mb-3 text-gray-900 dark:text-white">Technical Skills</h4>
                        {% if latest_analysis.skills %}
                            <div class="flex flex-wrap gap-2">
                                {% for skill in latest_analysis.skills|split:"," %}
                                    <span class="skill-tag">{{ skill|trim }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-gray-500">No skills listed</p>
                        {% endif %}
                    </div>
                    <div>
                        <h4 class="font-medium mb-3 text-gray-900 dark:text-white">Languages</h4>
                        {% if latest_analysis.languages %}
                            <div class="flex flex-wrap gap-2">
                                {% for language in latest_analysis.languages|split:"," %}
                                    <span class="skill-tag bg-green-100 text-green-800">{{ language|trim }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-gray-500">No languages listed</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Job Preferences -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    <i class="fas fa-cog mr-2 text-green-600"></i>Job Preferences
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Preferred Job Type</label>
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                            {{ latest_analysis.get_preferred_job_type_display|default:"Not specified" }}
                        </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Work Location Preference</label>
                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                            {{ latest_analysis.get_preferred_work_location_display|default:"Not specified" }}
                        </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Salary Expectation</label>
                        <p class="text-gray-900 dark:text-white">
                            {% if latest_analysis.salary_expectation %}
                                ${{ latest_analysis.salary_expectation|floatformat:2 }}
                            {% else %}
                                Not specified
                            {% endif %}
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Availability</label>
                        <p class="text-gray-900 dark:text-white">
                            {{ latest_analysis.availability|default:"Immediate" }}
                        </p>
                    </div>
                </div>
            </div>
            
            {% endif %}

            <!-- Analysis History -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    <i class="fas fa-history mr-2 text-orange-600"></i>Analysis History
                </h3>
                
                {% if analyses %}
                <div class="space-y-4">
                    {% for analysis in analyses %}
                    <div class="timeline-item">
                        <div class="timeline-icon 
                            {% if analysis.status == 'completed' %}border-green-500 text-green-500
                            {% elif analysis.status == 'processing' %}border-blue-500 text-blue-500
                            {% else %}border-red-500 text-red-500{% endif %}">
                            <i class="fas fa-{% if analysis.status == 'completed' %}check{% elif analysis.status == 'processing' %}clock{% else %}times{% endif %}"></i>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="font-medium text-gray-900 dark:text-white">
                                    Analysis #{{ analysis.id }}
                                </h4>
                                <span class="text-sm text-gray-500">
                                    {{ analysis.created_at|date:"M d, Y H:i" }}
                                </span>
                            </div>
                            <div class="flex items-center space-x-4 mb-3">
                                <span class="px-2 py-1 text-xs rounded-full
                                    {% if analysis.status == 'completed' %}bg-green-100 text-green-800
                                    {% elif analysis.status == 'processing' %}bg-blue-100 text-blue-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                                    {{ analysis.get_status_display }}
                                </span>
                                {% if analysis.overall_score %}
                                <span class="text-sm font-medium text-gray-900 dark:text-white">
                                    Score: {{ analysis.overall_score }}%
                                </span>
                                {% endif %}
                            </div>
                            {% if analysis.analysis_text %}
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                {{ analysis.analysis_text|truncatewords:30 }}
                            </p>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8">
                    <div class="text-gray-400 text-6xl mb-4">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Analysis Yet</h4>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">This CV hasn't been analyzed yet.</p>
                    <button onclick="startAnalysis()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-play mr-2"></i>Start Analysis
                    </button>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 space-y-6">
            <!-- CV Status -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-info-circle mr-2"></i>CV Status
                </h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Current Status</span>
                        <span class="px-3 py-1 text-xs font-medium rounded-full
                            {% if cv.status == 'uploaded' %}bg-yellow-100 text-yellow-800
                            {% elif cv.status == 'processing' %}bg-blue-100 text-blue-800
                            {% elif cv.status == 'analyzed' %}bg-purple-100 text-purple-800
                            {% elif cv.status == 'matched' %}bg-green-100 text-green-800
                            {% else %}bg-red-100 text-red-800{% endif %}">
                            {{ cv.get_status_display }}
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Upload Date</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ cv.uploaded_at|date:"M d, Y" }}
                        </span>
                    </div>
                </div>
            </div>


        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'cv_analyzer/js/unified_dashboard.js' %}"></script>
<script>
function startAnalysis() {
    fetch(`/cv/{{ cv.id }}/analyze/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Analysis started successfully', 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showNotification(data.message, 'error');
        }
    });
}

function reanalyzeCV() {
    startAnalysis();
}

function matchToJobs() {
    window.location.href = `/cv/{{ cv.id }}/match/`;
}

function addNotes() {
    // TODO: Implement add notes functionality
    showNotification('Add notes functionality coming soon', 'info');
}

function archiveCV() {
    if (confirm('Are you sure you want to archive this CV?')) {
        // TODO: Implement archive functionality
        showNotification('Archive functionality coming soon', 'info');
    }
}

function downloadCV() {
    window.open('{{ cv.file.url }}', '_blank');
}

function shareCV() {
    if (navigator.share) {
        navigator.share({
            title: 'CV - {{ cv.candidate_name }}',
            text: 'Check out this CV',
            url: window.location.href
        });
    } else {
        navigator.clipboard.writeText(window.location.href);
        showNotification('Link copied to clipboard', 'success');
    }
}

function exportCV() {
    showNotification('Export feature coming soon', 'info');
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        type === 'info' ? 'bg-blue-500' : 'bg-gray-500'
    }`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
{% endblock %}