{% extends "cv_analyzer/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .duplication-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .duplication-header {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .stats-container {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 25px;
    }
    
    .stat-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        border-left: 4px solid #ff6b6b;
    }
    
    .stat-number {
        font-size: 28px;
        font-weight: bold;
        color: #ff6b6b;
        margin-bottom: 5px;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 14px;
        font-weight: 600;
    }
    
    .scan-controls {
        text-align: center;
        margin-top: 25px;
    }
    
    .btn-scan {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        padding: 15px 40px;
        border: none;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 0 10px;
    }
    
    .btn-scan:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
    }
    
    .btn-scan:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
    
    .scan-progress {
        display: none;
        text-align: center;
        padding: 30px;
        background: white;
        border-radius: 15px;
        margin-top: 20px;
    }
    
    .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #ff6b6b;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }
    
    .results-container {
        display: none;
        margin-top: 30px;
    }
    
    .duplicates-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e1e5e9;
    }
    
    .duplicate-item {
        border: 1px solid #dee2e6;
        border-radius: 10px;
        margin-bottom: 15px;
        overflow: hidden;
    }
    
    .duplicate-header {
        background: #f8f9fa;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
    }
    
    .duplicate-header:hover {
        background: #e9ecef;
    }
    
    .duplicate-info {
        flex: 1;
    }
    
    .duplicate-title {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }
    
    .duplicate-meta {
        color: #6c757d;
        font-size: 14px;
    }
    
    .duplicate-count {
        background: #ff6b6b;
        color: white;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 600;
    }
    
    .duplicate-details {
        display: none;
        padding: 20px;
        background: #fff;
    }
    
    .duplicate-details.show {
        display: block;
    }
    
    .analysis-list, .cv-list {
        margin-bottom: 20px;
    }
    
    .analysis-item, .cv-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        margin-bottom: 8px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #dee2e6;
    }
    
    .analysis-item.latest, .cv-item.latest {
        border-left-color: #28a745;
        background: #d4edda;
    }
    
    .analysis-item.duplicate, .cv-item.duplicate {
        border-left-color: #dc3545;
    }
    
    .item-info {
        flex: 1;
    }
    
    .item-score {
        background: #6c757d;
        color: white;
        padding: 4px 8px;
        border-radius: 10px;
        font-size: 12px;
        margin-right: 10px;
    }
    
    .item-date {
        color: #6c757d;
        font-size: 12px;
    }
    
    .item-actions {
        display: flex;
        gap: 10px;
    }
    
    .btn-merge, .btn-delete {
        padding: 5px 12px;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .btn-merge {
        background: #28a745;
        color: white;
    }
    
    .btn-merge:hover {
        background: #218838;
    }
    
    .btn-delete {
        background: #dc3545;
        color: white;
    }
    
    .btn-delete:hover {
        background: #c82333;
    }
    
    .alert {
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    
    .alert-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    
    .alert-danger {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    
    .alert-info {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
    }
    
    .no-duplicates {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }
    
    .no-duplicates i {
        font-size: 48px;
        margin-bottom: 15px;
        color: #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="duplication-container">
    <!-- Header -->
    <div class="duplication-header">
        <h1><i class="fas fa-search"></i> CV Duplication Check</h1>
        <p class="mb-0">Scan database for duplicate analyses and CV files to optimize data integrity</p>
    </div>
    
    <!-- Statistics -->
    <div class="stats-container">
        <h3><i class="fas fa-chart-pie"></i> Database Overview</h3>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ total_analyses }}</div>
                <div class="stat-label">Total Analyses</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ total_cvs }}</div>
                <div class="stat-label">Total CVs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ total_vacancies }}</div>
                <div class="stat-label">Total Vacancies</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="duplicate-count">-</div>
                <div class="stat-label">Duplicates Found</div>
            </div>
        </div>
        
        <div class="scan-controls">
            <button type="button" class="btn-scan" id="scan-btn" onclick="startScan()">
                <i class="fas fa-search"></i> Scan for Duplicates
            </button>
        </div>
    </div>
    
    <!-- Scan Progress -->
    <div class="scan-progress" id="scan-progress">
        <div class="spinner"></div>
        <h4>Scanning Database...</h4>
        <p class="text-muted">Checking for duplicate analyses and CV files</p>
    </div>
    
    <!-- Alert Messages -->
    <div id="alert-container"></div>
    
    <!-- Results Container -->
    <div class="results-container" id="results-container">
        <!-- Analysis Duplicates Section -->
        <div class="duplicates-section" id="analysis-duplicates-section">
            <div class="section-header">
                <h3><i class="fas fa-analytics"></i> Duplicate Analyses</h3>
                <span class="duplicate-count" id="analysis-duplicate-count">0</span>
            </div>
            <div id="analysis-duplicates-list"></div>
        </div>
        
        <!-- CV Duplicates Section -->
        <div class="duplicates-section" id="cv-duplicates-section">
            <div class="section-header">
                <h3><i class="fas fa-file-duplicate"></i> Duplicate CVs</h3>
                <span class="duplicate-count" id="cv-duplicate-count">0</span>
            </div>
            <div id="cv-duplicates-list"></div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-scan on page load
    setTimeout(startScan, 1000);
});

function startScan() {
    const scanBtn = document.getElementById('scan-btn');
    const progressDiv = document.getElementById('scan-progress');
    const resultsDiv = document.getElementById('results-container');
    const alertContainer = document.getElementById('alert-container');
    
    // Show progress
    scanBtn.disabled = true;
    progressDiv.style.display = 'block';
    resultsDiv.style.display = 'none';
    alertContainer.innerHTML = '';
    
    // Submit scan request
    const formData = new FormData();
    formData.append('action', 'scan');
    formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');
    
    fetch('{% url "cv_duplication_check" %}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        progressDiv.style.display = 'none';
        scanBtn.disabled = false;
        
        if (data.success) {
            showResults(data);
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        progressDiv.style.display = 'none';
        scanBtn.disabled = false;
        showAlert('danger', 'Scan failed: ' + error.message);
    });
}

function showResults(data) {
    const resultsDiv = document.getElementById('results-container');
    
    // Update duplicate count in stats
    const totalDuplicates = data.total_analysis_duplicates + data.total_cv_duplicates;
    document.getElementById('duplicate-count').textContent = totalDuplicates;
    
    // Show success message
    showAlert('success', data.message);
    
    // Show results container
    resultsDiv.style.display = 'block';
}

function showAlert(type, message) {
    const alertContainer = document.getElementById('alert-container');
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.innerHTML = `
        <strong>${type === 'success' ? 'Success!' : 'Error!'}</strong>
        ${message}
    `;
    alertContainer.appendChild(alert);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %} 