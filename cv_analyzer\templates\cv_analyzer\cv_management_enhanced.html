{% extends "cv_analyzer/base.html" %}
{% load static %}

{% block title %}CV Management Dashboard{% endblock %}

{% block extra_css %}
<style>
    .process-step {
        position: relative;
        padding: 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .process-step:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .process-arrow {
        position: absolute;
        top: 50%;
        right: -10px;
        transform: translateY(-50%);
        width: 20px;
        height: 20px;
        background: white;
        border: 2px solid #e5e7eb;
        transform: rotate(45deg) translateY(-50%);
        z-index: 10;
    }
    
    .filter-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        background: #3b82f6;
        color: white;
        border-radius: 1rem;
        font-size: 0.875rem;
        margin: 0.25rem;
    }
    
    .cv-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    
    .cv-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    
    .cv-card.uploaded {
        border-left-color: #fbbf24;
    }
    
    .cv-card.processed {
        border-left-color: #3b82f6;
    }
    
    .cv-card.analyzed {
        border-left-color: #8b5cf6;
    }
    
    .cv-card.matched {
        border-left-color: #10b981;
    }
    
    .cv-card.archived {
        border-left-color: #6b7280;
    }
    
    .status-timeline {
        display: flex;
        align-items: center;
        margin: 1rem 0;
    }
    
    .timeline-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        position: relative;
    }
    
    .timeline-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid;
        background: white;
        z-index: 2;
    }
    
    .timeline-connector {
        position: absolute;
        top: 20px;
        left: 50%;
        right: -50%;
        height: 3px;
        background: #e5e7eb;
        z-index: 1;
    }
    
    .timeline-step.completed .timeline-icon {
        border-color: #10b981;
        color: #10b981;
    }
    
    .timeline-step.current .timeline-icon {
        border-color: #3b82f6;
        color: #3b82f6;
        animation: pulse 2s infinite;
    }
    
    .timeline-step.pending .timeline-icon {
        border-color: #d1d5db;
        color: #9ca3af;
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }
    
    .metric-card {
        background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
        color: white;
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        transition: transform 0.3s ease;
    }
    
    .metric-card:hover {
        transform: scale(1.05);
    }
    
    .metric-card.blue {
        --gradient-start: #3b82f6;
        --gradient-end: #1e40af;
    }
    
    .metric-card.green {
        --gradient-start: #10b981;
        --gradient-end: #059669;
    }
    
    .metric-card.yellow {
        --gradient-start: #fbbf24;
        --gradient-end: #d97706;
    }
    
    .metric-card.purple {
        --gradient-start: #8b5cf6;
        --gradient-end: #7c3aed;
    }
    
    .metric-card.red {
        --gradient-start: #ef4444;
        --gradient-end: #dc2626;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header with Process Flow -->
    <div class="mb-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    <i class="fas fa-file-alt mr-3 text-blue-600"></i>CV Management Dashboard
                </h1>
                <p class="text-gray-600 dark:text-gray-400">Track, analyze, and manage CV submissions through the complete workflow</p>
            </div>
            <div class="flex space-x-3">
                <button onclick="showBulkActions()" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                    <i class="fas fa-tasks mr-2"></i>Bulk Actions
                </button>
                <a href="{% url 'upload_local' %}" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>Upload New CV
                </a>
            </div>
        </div>
        
        <!-- CV Process Flow Timeline -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">CV Processing Workflow</h3>
            <div class="status-timeline">
                <div class="timeline-step completed">
                    <div class="timeline-icon">
                        <i class="fas fa-upload"></i>
                    </div>
                    <span class="text-sm font-medium mt-2">Upload</span>
                    <div class="timeline-connector"></div>
                </div>
                <div class="timeline-step current">
                    <div class="timeline-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <span class="text-sm font-medium mt-2">Process</span>
                    <div class="timeline-connector"></div>
                </div>
                <div class="timeline-step pending">
                    <div class="timeline-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <span class="text-sm font-medium mt-2">AI Analysis</span>
                    <div class="timeline-connector"></div>
                </div>
                <div class="timeline-step pending">
                    <div class="timeline-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <span class="text-sm font-medium mt-2">Match</span>
                    <div class="timeline-connector"></div>
                </div>
                <div class="timeline-step pending">
                    <div class="timeline-icon">
                        <i class="fas fa-check"></i>
                    </div>
                    <span class="text-sm font-medium mt-2">Review</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Metrics Dashboard -->
    <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
        <div class="metric-card blue">
            <div class="text-2xl font-bold">{{ total_cvs }}</div>
            <div class="text-sm opacity-90">Total CVs</div>
        </div>
        <div class="metric-card yellow">
            <div class="text-2xl font-bold">{{ processing_cvs }}</div>
            <div class="text-sm opacity-90">Processing</div>
        </div>
        <div class="metric-card purple">
            <div class="text-2xl font-bold">{{ analyzed_cvs }}</div>
            <div class="text-sm opacity-90">Analyzed</div>
        </div>
        <div class="metric-card green">
            <div class="text-2xl font-bold">{{ matched_cvs }}</div>
            <div class="text-sm opacity-90">Matched</div>
        </div>
        <div class="metric-card red">
            <div class="text-2xl font-bold">{{ rejected_cvs }}</div>
            <div class="text-sm opacity-90">Rejected</div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-filter mr-2"></i>Advanced Filters
            </h3>
            <button onclick="clearAllFilters()" class="text-sm text-gray-500 hover:text-gray-700">
                <i class="fas fa-times mr-1"></i>Clear All
            </button>
        </div>
        
        <form method="get" id="filterForm" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                <!-- Search -->
                <div class="col-span-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-search mr-1"></i>Search
                    </label>
                    <input type="text" name="search" value="{{ request.GET.search }}" 
                           placeholder="Name, email, skills..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-flag mr-1"></i>Status
                    </label>
                    <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option value="">All Statuses</option>
                        <option value="uploaded" {% if request.GET.status == 'uploaded' %}selected{% endif %}>Uploaded</option>
                        <option value="processing" {% if request.GET.status == 'processing' %}selected{% endif %}>Processing</option>
                        <option value="analyzed" {% if request.GET.status == 'analyzed' %}selected{% endif %}>Analyzed</option>
                        <option value="matched" {% if request.GET.status == 'matched' %}selected{% endif %}>Matched</option>
                        <option value="rejected" {% if request.GET.status == 'rejected' %}selected{% endif %}>Rejected</option>
                    </select>
                </div>
                
                <!-- Experience Level -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-medal mr-1"></i>Experience
                    </label>
                    <select name="experience" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option value="">All Levels</option>
                        <option value="entry" {% if request.GET.experience == 'entry' %}selected{% endif %}>Entry Level</option>
                        <option value="mid" {% if request.GET.experience == 'mid' %}selected{% endif %}>Mid Level</option>
                        <option value="senior" {% if request.GET.experience == 'senior' %}selected{% endif %}>Senior Level</option>
                        <option value="executive" {% if request.GET.experience == 'executive' %}selected{% endif %}>Executive</option>
                    </select>
                </div>
                
                <!-- Score Range -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-chart-line mr-1"></i>Min Score
                    </label>
                    <select name="min_score" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option value="">Any Score</option>
                        <option value="90" {% if request.GET.min_score == '90' %}selected{% endif %}>90%+</option>
                        <option value="80" {% if request.GET.min_score == '80' %}selected{% endif %}>80%+</option>
                        <option value="70" {% if request.GET.min_score == '70' %}selected{% endif %}>70%+</option>
                        <option value="60" {% if request.GET.min_score == '60' %}selected{% endif %}>60%+</option>
                    </select>
                </div>
                
                <!-- Date Range -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-calendar mr-1"></i>Date Range
                    </label>
                    <select name="date_range" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option value="">All Time</option>
                        <option value="today" {% if request.GET.date_range == 'today' %}selected{% endif %}>Today</option>
                        <option value="week" {% if request.GET.date_range == 'week' %}selected{% endif %}>This Week</option>
                        <option value="month" {% if request.GET.date_range == 'month' %}selected{% endif %}>This Month</option>
                        <option value="quarter" {% if request.GET.date_range == 'quarter' %}selected{% endif %}>This Quarter</option>
                    </select>
                </div>
            </div>
            
            <!-- Quick Filters Row -->
            <div class="flex flex-wrap items-center gap-2 pt-4 border-t border-gray-200 dark:border-gray-700">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">Quick Filters:</span>
                <button type="button" onclick="setQuickFilter('high_score')" class="px-3 py-1 text-sm bg-green-100 text-green-800 rounded-full hover:bg-green-200 transition-colors">
                    High Score (80%+)
                </button>
                <button type="button" onclick="setQuickFilter('needs_review')" class="px-3 py-1 text-sm bg-yellow-100 text-yellow-800 rounded-full hover:bg-yellow-200 transition-colors">
                    Needs Review
                </button>
                <button type="button" onclick="setQuickFilter('recent')" class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors">
                    Recent (This Week)
                </button>
                <button type="button" onclick="setQuickFilter('matched')" class="px-3 py-1 text-sm bg-purple-100 text-purple-800 rounded-full hover:bg-purple-200 transition-colors">
                    Matched to Jobs
                </button>
            </div>
            
            <!-- Filter Actions -->
            <div class="flex justify-between items-center pt-4">
                <div class="flex items-center space-x-4">
                    <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search mr-2"></i>Apply Filters
                    </button>
                    <button type="button" onclick="exportFiltered()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-download mr-2"></i>Export
                    </button>
                </div>
                
                <!-- Active Filters Display -->
                <div id="activeFilters" class="flex flex-wrap gap-2"></div>
            </div>
        </form>
    </div>

    <!-- CV List with Enhanced Cards -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main CV List -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                            CV Records ({{ cvs.count }})
                        </h3>
                        <div class="flex items-center space-x-2">
                            <button onclick="toggleView('card')" class="p-2 text-gray-500 hover:text-blue-600 transition-colors" id="cardViewBtn">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button onclick="toggleView('list')" class="p-2 text-gray-500 hover:text-blue-600 transition-colors" id="listViewBtn">
                                <i class="fas fa-list"></i>
                            </button>
                            <select onchange="sortCVs(this.value)" class="px-3 py-1 text-sm border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option value="newest">Newest First</option>
                                <option value="oldest">Oldest First</option>
                                <option value="score_high">Highest Score</option>
                                <option value="score_low">Lowest Score</option>
                                <option value="name">Name A-Z</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div id="cvList" class="p-6">
                    {% for cv in cvs %}
                    <div class="cv-card bg-white dark:bg-gray-700 rounded-lg shadow-sm p-6 mb-4 {{ cv.status }}">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-4">
                                <!-- CV Avatar/Icon -->
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                        {{ cv.candidate_name|first|upper }}
                                    </div>
                                </div>
                                
                                <!-- CV Info -->
                                <div class="flex-1">
                                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                                        {{ cv.candidate_name|default:"Unknown Candidate" }}
                                    </h4>
                                    <p class="text-gray-600 dark:text-gray-400 text-sm">
                                        {{ cv.email|default:"No email provided" }}
                                    </p>
                                    <div class="flex items-center space-x-4 mt-2">
                                        <span class="text-xs text-gray-500">
                                            <i class="fas fa-calendar mr-1"></i>
                                            {{ cv.uploaded_at|date:"M d, Y" }}
                                        </span>
                                        <span class="text-xs text-gray-500">
                                            <i class="fas fa-file mr-1"></i>
                                            {{ cv.file.name|truncatechars:20 }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Status & Actions -->
                            <div class="flex flex-col items-end space-y-2">
                                <!-- Status Badge -->
                                <span class="px-3 py-1 text-xs font-medium rounded-full 
                                    {% if cv.status == 'uploaded' %}bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300
                                    {% elif cv.status == 'processing' %}bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                    {% elif cv.status == 'analyzed' %}bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300
                                    {% elif cv.status == 'matched' %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                    {% else %}bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300{% endif %}">
                                    {{ cv.get_status_display }}
                                </span>
                                
                                <!-- Score Display -->
                                {% if cv.latest_analysis %}
                                <div class="flex items-center space-x-2">
                                    <div class="w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold
                                        {% if cv.latest_analysis.overall_score >= 80 %}bg-green-500 text-white
                                        {% elif cv.latest_analysis.overall_score >= 60 %}bg-yellow-500 text-white
                                        {% else %}bg-red-500 text-white{% endif %}">
                                        {{ cv.latest_analysis.overall_score }}
                                    </div>
                                </div>
                                {% endif %}
                                
                                <!-- Action Buttons -->
                                <div class="flex space-x-1">
                                    <button onclick="viewCV({{ cv.id }})" class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button onclick="analyzeCV({{ cv.id }})" class="p-2 text-purple-600 hover:bg-purple-50 rounded-lg transition-colors" title="Analyze">
                                        <i class="fas fa-brain"></i>
                                    </button>
                                    <button onclick="matchCV({{ cv.id }})" class="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors" title="Match Jobs">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <div class="relative">
                                        <button onclick="toggleCVMenu({{ cv.id }})" class="p-2 text-gray-500 hover:bg-gray-50 rounded-lg transition-colors">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <div id="menu-{{ cv.id }}" class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10">
                                            <a href="#" onclick="downloadCV({{ cv.id }})" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                                <i class="fas fa-download mr-2"></i>Download
                                            </a>
                                            <a href="#" onclick="editCV({{ cv.id }})" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                                <i class="fas fa-edit mr-2"></i>Edit Info
                                            </a>
                                            <a href="#" onclick="shareCV({{ cv.id }})" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                                <i class="fas fa-share mr-2"></i>Share
                                            </a>
                                            <hr class="my-1 border-gray-200 dark:border-gray-700">
                                            <a href="#" onclick="archiveCV({{ cv.id }})" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900">
                                                <i class="fas fa-archive mr-2"></i>Archive
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- CV Skills/Tags (if available) -->
                        {% if cv.skills %}
                        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                            <div class="flex flex-wrap gap-2">
                                {% for skill in cv.skills|slice:":5" %}
                                <span class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full">
                                    {{ skill }}
                                </span>
                                {% endfor %}
                                {% if cv.skills|length > 5 %}
                                <span class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400 rounded-full">
                                    +{{ cv.skills|length|add:"-5" }} more
                                </span>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% empty %}
                    <div class="text-center py-12">
                        <div class="text-6xl text-gray-300 dark:text-gray-600 mb-4">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No CVs Found</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-6">Try adjusting your filters or upload some CVs to get started.</p>
                        <a href="{% url 'upload_local' %}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-plus mr-2"></i>Upload Your First CV
                        </a>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-700 dark:text-gray-300">
                            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
                        </div>
                        <nav class="flex space-x-1">
                            {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">Previous</a>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <span class="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg">{{ num }}</span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">{{ num }}</a>
                            {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">Next</a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Analytics Sidebar -->
        <div class="lg:col-span-1">
            <div class="space-y-6">
                <!-- Processing Analytics -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-chart-pie mr-2"></i>Processing Analytics
                    </h3>
                    <canvas id="processingChart" width="300" height="300"></canvas>
                </div>
                
                <!-- Recent Activity -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-clock mr-2"></i>Recent Activity
                    </h3>
                    <div class="space-y-3">
                        {% for activity in recent_activities|slice:":5" %}
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-{{ activity.icon }} text-xs"></i>
                                </div>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-900 dark:text-white">{{ activity.description }}</p>
                                <p class="text-xs text-gray-500">{{ activity.timestamp|timesince }} ago</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                
                <!-- Quick Stats -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-tachometer-alt mr-2"></i>Quick Stats
                    </h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Avg. Processing Time</span>
                            <span class="font-semibold text-gray-900 dark:text-white">{{ avg_processing_time }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Success Rate</span>
                            <span class="font-semibold text-green-600">{{ success_rate }}%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Pending Review</span>
                            <span class="font-semibold text-yellow-600">{{ pending_review }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Top Skill</span>
                            <span class="font-semibold text-blue-600">{{ top_skill }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals and Overlays -->
<div id="bulkActionsModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 w-full max-w-md">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Bulk Actions</h3>
        <div class="space-y-3">
            <button onclick="bulkAnalyze()" class="w-full px-4 py-2 text-left bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors">
                <i class="fas fa-brain mr-2"></i>Analyze Selected CVs
            </button>
            <button onclick="bulkMatch()" class="w-full px-4 py-2 text-left bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors">
                <i class="fas fa-search mr-2"></i>Match to Jobs
            </button>
            <button onclick="bulkExport()" class="w-full px-4 py-2 text-left bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors">
                <i class="fas fa-download mr-2"></i>Export to Excel
            </button>
            <button onclick="bulkArchive()" class="w-full px-4 py-2 text-left bg-red-50 text-red-700 rounded-lg hover:bg-red-100 transition-colors">
                <i class="fas fa-archive mr-2"></i>Archive Selected
            </button>
        </div>
        <div class="flex justify-end space-x-3 mt-6">
            <button onclick="hideBulkActions()" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">Cancel</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Chart Configuration
const processingData = {
    labels: ['Uploaded', 'Processing', 'Analyzed', 'Matched', 'Rejected'],
    datasets: [{
        data: [{{ uploaded_count }}, {{ processing_count }}, {{ analyzed_count }}, {{ matched_count }}, {{ rejected_count }}],
        backgroundColor: [
            '#fbbf24',
            '#3b82f6', 
            '#8b5cf6',
            '#10b981',
            '#ef4444'
        ],
        borderWidth: 0
    }]
};

const ctx = document.getElementById('processingChart').getContext('2d');
new Chart(ctx, {
    type: 'doughnut',
    data: processingData,
    options: {
        responsive: true,
        maintainAspectRatio: true,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            }
        }
    }
});

// Filter Functions
function setQuickFilter(type) {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    
    // Clear existing filters
    form.reset();
    
    switch(type) {
        case 'high_score':
            form.querySelector('[name="min_score"]').value = '80';
            break;
        case 'needs_review':
            form.querySelector('[name="status"]').value = 'analyzed';
            break;
        case 'recent':
            form.querySelector('[name="date_range"]').value = 'week';
            break;
        case 'matched':
            form.querySelector('[name="status"]').value = 'matched';
            break;
    }
    
    form.submit();
}

function clearAllFilters() {
    window.location.href = window.location.pathname;
}

function exportFiltered() {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    window.location.href = `{% url 'export_cvs' %}?${params.toString()}`;
}

// CV Actions
function viewCV(cvId) {
    window.location.href = `/cv/${cvId}/detail/`;
}

function analyzeCV(cvId) {
    // Send AJAX request to analyze CV
    fetch(`/cv/${cvId}/analyze/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('CV analysis started successfully', 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showNotification('Failed to start analysis', 'error');
        }
    });
}

function matchCV(cvId) {
    window.location.href = `/cv/${cvId}/match/`;
}

function toggleCVMenu(cvId) {
    const menu = document.getElementById(`menu-${cvId}`);
    menu.classList.toggle('hidden');
}

// Bulk Actions
function showBulkActions() {
    document.getElementById('bulkActionsModal').classList.remove('hidden');
}

function hideBulkActions() {
    document.getElementById('bulkActionsModal').classList.add('hidden');
}

// View Toggle
function toggleView(viewType) {
    const cardBtn = document.getElementById('cardViewBtn');
    const listBtn = document.getElementById('listViewBtn');
    
    if (viewType === 'card') {
        cardBtn.classList.add('text-blue-600');
        listBtn.classList.remove('text-blue-600');
        // Implement card view
    } else {
        listBtn.classList.add('text-blue-600');
        cardBtn.classList.remove('text-blue-600');
        // Implement list view
    }
}

// Sort Functions
function sortCVs(sortType) {
    const url = new URL(window.location);
    url.searchParams.set('sort', sortType);
    window.location.href = url.toString();
}

// Notification System
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    }`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Initialize filters display
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const activeFilters = document.getElementById('activeFilters');
    
    urlParams.forEach((value, key) => {
        if (value && key !== 'page') {
            const badge = document.createElement('span');
            badge.className = 'filter-badge';
            badge.innerHTML = `${key}: ${value} <button onclick="removeFilter('${key}')" class="ml-1 text-white hover:text-gray-200">×</button>`;
            activeFilters.appendChild(badge);
        }
    });
});

function removeFilter(filterKey) {
    const url = new URL(window.location);
    url.searchParams.delete(filterKey);
    window.location.href = url.toString();
}

// Auto-refresh for processing CVs
setInterval(() => {
    const processingCards = document.querySelectorAll('.cv-card.processing');
    if (processingCards.length > 0) {
        location.reload();
    }
}, 30000); // Refresh every 30 seconds if there are processing CVs
</script>
{% endblock %} 