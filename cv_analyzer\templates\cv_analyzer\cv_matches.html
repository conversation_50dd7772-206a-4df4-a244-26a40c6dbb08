{% extends "cv_analyzer/base.html" %}
{% load static %}

{% block title %}Job Matches - {{ cv.candidate_name }}{% endblock %}

{% block extra_css %}
<style>
    .match-card {
        transition: all 0.3s ease;
        border-left: 4px solid #e5e7eb;
    }
    
    .match-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    
    .match-card.excellent {
        border-left-color: #10b981;
    }
    
    .match-card.good {
        border-left-color: #3b82f6;
    }
    
    .match-card.fair {
        border-left-color: #f59e0b;
    }
    
    .match-card.poor {
        border-left-color: #ef4444;
    }
    
    .score-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
    }
    
    .score-excellent {
        background: linear-gradient(135deg, #10b981, #059669);
    }
    
    .score-good {
        background: linear-gradient(135deg, #3b82f6, #1e40af);
    }
    
    .score-fair {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }
    
    .score-poor {
        background: linear-gradient(135deg, #ef4444, #dc2626);
    }
    
    .skill-match {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        margin: 0.125rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .skill-match.matched {
        background: #dcfce7;
        color: #166534;
    }
    
    .skill-match.missing {
        background: #fef2f2;
        color: #991b1b;
    }
    
    .skill-match.partial {
        background: #fef3c7;
        color: #92400e;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-8">
        <div class="flex items-center space-x-4">
            <a href="{% url 'cv_detail_view' cv.id %}" class="p-2 text-gray-500 hover:text-blue-600 rounded-lg transition-colors">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    Job Matches for {{ cv.candidate_name }}
                </h1>
                <p class="text-gray-600 dark:text-gray-400">
                    Found {{ total_matches }} potential matches • CV #{{ cv.id }}
                </p>
            </div>
        </div>
        
        <div class="flex space-x-3">
            <button onclick="refreshMatches()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-sync mr-2"></i>Refresh Matches
            </button>
            <button onclick="exportMatches()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-download mr-2"></i>Export Results
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Filters Sidebar -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-filter mr-2"></i>Filter Matches
                </h3>
                
                <form method="get" class="space-y-4">
                    <!-- Score Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Minimum Match Score
                        </label>
                        <select name="min_score" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="0">All Matches</option>
                            <option value="90">90%+ (Excellent)</option>
                            <option value="80">80%+ (Good)</option>
                            <option value="70">70%+ (Fair)</option>
                            <option value="60">60%+ (Acceptable)</option>
                        </select>
                    </div>
                    
                    <!-- Company Type -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Company Type
                        </label>
                        <select name="company_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="">All Companies</option>
                            <option value="startup">Startup</option>
                            <option value="corporate">Corporate</option>
                            <option value="enterprise">Enterprise</option>
                            <option value="nonprofit">Non-profit</option>
                        </select>
                    </div>
                    
                    <!-- Experience Level -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Experience Level
                        </label>
                        <select name="experience" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="">Any Level</option>
                            <option value="entry">Entry Level</option>
                            <option value="mid">Mid Level</option>
                            <option value="senior">Senior Level</option>
                            <option value="executive">Executive</option>
                        </select>
                    </div>
                    
                    <!-- Remote Work -->
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="remote" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Remote Work Available</span>
                        </label>
                    </div>
                    
                    <button type="submit" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Apply Filters
                    </button>
                </form>
            </div>
            
            <!-- Match Summary -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-chart-pie mr-2"></i>Match Summary
                </h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Excellent (90%+)</span>
                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                            {{ matches|length|default:0 }}
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Good (80-89%)</span>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                            0
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Fair (70-79%)</span>
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                            0
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Poor (60-69%)</span>
                        <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
                            0
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Matches List -->
        <div class="lg:col-span-3">
            {% if matches %}
            <div class="space-y-6">
                {% for match in matches %}
                <div class="match-card bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 
                    {% if match.score >= 90 %}excellent
                    {% elif match.score >= 80 %}good
                    {% elif match.score >= 70 %}fair
                    {% else %}poor{% endif %}">
                    
                    <div class="flex items-start justify-between mb-6">
                        <div class="flex-1">
                            <div class="flex items-start space-x-4">
                                <!-- Company Logo Placeholder -->
                                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                                    {{ match.vacancy.company.name|first|upper }}
                                </div>
                                
                                <div class="flex-1">
                                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                        {{ match.vacancy.title }}
                                    </h3>
                                    <p class="text-gray-600 dark:text-gray-400 mb-2">
                                        {{ match.vacancy.company.name }}
                                    </p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span>
                                            <i class="fas fa-map-marker-alt mr-1"></i>
                                            {{ match.vacancy.location|default:"Remote" }}
                                        </span>
                                        <span>
                                            <i class="fas fa-briefcase mr-1"></i>
                                            {{ match.vacancy.get_job_type_display|default:"Full-time" }}
                                        </span>
                                        <span>
                                            <i class="fas fa-calendar mr-1"></i>
                                            Posted {{ match.vacancy.created_at|timesince }} ago
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Match Score -->
                        <div class="text-center">
                            <div class="score-circle 
                                {% if match.score >= 90 %}score-excellent
                                {% elif match.score >= 80 %}score-good
                                {% elif match.score >= 70 %}score-fair
                                {% else %}score-poor{% endif %}">
                                {{ match.score }}%
                            </div>
                            <p class="text-xs text-gray-500 mt-2">Match Score</p>
                        </div>
                    </div>
                    
                    <!-- Job Description -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 dark:text-white mb-2">Job Description</h4>
                        <p class="text-gray-600 dark:text-gray-400 text-sm line-clamp-3">
                            {{ match.vacancy.description|truncatewords:50 }}
                        </p>
                    </div>
                    
                    <!-- Skills Matching -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 dark:text-white mb-3">Skills Analysis</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <p class="text-xs font-medium text-gray-500 mb-2">MATCHED SKILLS</p>
                                <div class="flex flex-wrap">
                                    <span class="skill-match matched">Python</span>
                                    <span class="skill-match matched">Django</span>
                                    <span class="skill-match matched">JavaScript</span>
                                </div>
                            </div>
                            <div>
                                <p class="text-xs font-medium text-gray-500 mb-2">PARTIAL MATCHES</p>
                                <div class="flex flex-wrap">
                                    <span class="skill-match partial">React</span>
                                    <span class="skill-match partial">AWS</span>
                                </div>
                            </div>
                            <div>
                                <p class="text-xs font-medium text-gray-500 mb-2">MISSING SKILLS</p>
                                <div class="flex flex-wrap">
                                    <span class="skill-match missing">Docker</span>
                                    <span class="skill-match missing">Kubernetes</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Salary & Benefits -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h4 class="font-medium text-gray-900 dark:text-white mb-2">Compensation</h4>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">
                                {% if match.vacancy.salary_min and match.vacancy.salary_max %}
                                    ${{ match.vacancy.salary_min|floatformat:0 }} - ${{ match.vacancy.salary_max|floatformat:0 }}
                                {% else %}
                                    Competitive salary
                                {% endif %}
                            </p>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900 dark:text-white mb-2">Benefits</h4>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">
                                Health insurance, 401k, Remote work, Flexible hours
                            </p>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div class="flex space-x-3">
                            <button onclick="viewJobDetails({{ match.vacancy.id }})" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-eye mr-2"></i>View Details
                            </button>
                            <button onclick="applyToJob({{ match.vacancy.id }})" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-paper-plane mr-2"></i>Apply Now
                            </button>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button onclick="saveJob({{ match.vacancy.id }})" class="p-2 text-gray-500 hover:text-yellow-600 transition-colors" title="Save Job">
                                <i class="fas fa-bookmark"></i>
                            </button>
                            <button onclick="shareJob({{ match.vacancy.id }})" class="p-2 text-gray-500 hover:text-blue-600 transition-colors" title="Share Job">
                                <i class="fas fa-share"></i>
                            </button>
                            <button onclick="hideJob({{ match.vacancy.id }})" class="p-2 text-gray-500 hover:text-red-600 transition-colors" title="Hide Job">
                                <i class="fas fa-eye-slash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Load More Button -->
            <div class="text-center mt-8">
                <button onclick="loadMoreMatches()" class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>Load More Matches
                </button>
            </div>
            {% else %}
            <!-- No Matches Found -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-12 text-center">
                <div class="text-gray-400 text-8xl mb-6">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="text-2xl font-semibold text-gray-900 dark:text-white mb-4">No Matches Found</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
                    We couldn't find any job vacancies that match this CV profile. 
                    Try adjusting your filters or check back later for new opportunities.
                </p>
                <div class="flex justify-center space-x-4">
                    <button onclick="brodenSearch()" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-expand mr-2"></i>Broaden Search
                    </button>
                    <button onclick="createJobAlert()" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-bell mr-2"></i>Create Job Alert
                    </button>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewJobDetails(vacancyId) {
    window.open(`/vacancy/${vacancyId}/detail/`, '_blank');
}

function applyToJob(vacancyId) {
    if (confirm('Apply to this position with the current CV?')) {
        fetch(`/apply-to-vacancy/${vacancyId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                'cv_id': {{ cv.id }}
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Application submitted successfully!', 'success');
            } else {
                showNotification(data.message || 'Failed to submit application', 'error');
            }
        });
    }
}

function saveJob(vacancyId) {
    showNotification('Job saved to your favorites', 'success');
}

function shareJob(vacancyId) {
    if (navigator.share) {
        navigator.share({
            title: 'Job Opportunity',
            text: 'Check out this job opportunity',
            url: `/vacancy/${vacancyId}/detail/`
        });
    } else {
        navigator.clipboard.writeText(`${window.location.origin}/vacancy/${vacancyId}/detail/`);
        showNotification('Job link copied to clipboard', 'success');
    }
}

function hideJob(vacancyId) {
    if (confirm('Hide this job from future recommendations?')) {
        showNotification('Job hidden from recommendations', 'info');
    }
}

function refreshMatches() {
    window.location.reload();
}

function exportMatches() {
    window.open(`/cv/{{ cv.id }}/matches/export/`, '_blank');
}

function loadMoreMatches() {
    showNotification('Loading more matches...', 'info');
    // Implement pagination or infinite scroll
}

function brodenSearch() {
    const url = new URL(window.location);
    url.searchParams.delete('min_score');
    url.searchParams.delete('company_type');
    url.searchParams.delete('experience');
    window.location.href = url.toString();
}

function createJobAlert() {
    showNotification('Job alert feature coming soon', 'info');
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        type === 'info' ? 'bg-blue-500' : 'bg-gray-500'
    }`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
{% endblock %}