{% extends "cv_analyzer/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .analysis-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .analysis-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .vacancy-selector {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-control {
        padding: 12px;
        border: 2px solid #e1e5e9;
        border-radius: 10px;
        font-size: 16px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-analyze {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 12px 30px;
        border: none;
        border-radius: 10px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-analyze:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .btn-analyze:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
    
    .analysis-progress {
        display: none;
        text-align: center;
        padding: 30px;
    }
    
    .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .results-container {
        display: none;
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-top: 30px;
    }
    
    .results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e1e5e9;
    }
    
    .results-stats {
        display: flex;
        gap: 20px;
        margin-bottom: 25px;
    }
    
    .stat-card {
        background: #f8f9fa;
        padding: 15px 20px;
        border-radius: 10px;
        text-align: center;
        flex: 1;
    }
    
    .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #667eea;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 14px;
    }
    
    .matches-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }
    
    .matches-table th,
    .matches-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }
    
    .matches-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
    }
    
    .score-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-weight: 600;
        font-size: 12px;
    }
    
    .score-excellent { background-color: #d4edda; color: #155724; }
    .score-good { background-color: #d1ecf1; color: #0c5460; }
    .score-average { background-color: #fff3cd; color: #856404; }
    .score-poor { background-color: #f8d7da; color: #721c24; }
    
    .recommendation-badge {
        padding: 5px 10px;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 600;
    }
    
    .rec-highly { background-color: #28a745; color: white; }
    .rec-recommended { background-color: #17a2b8; color: white; }
    .rec-consider { background-color: #ffc107; color: #212529; }
    .rec-not-suitable { background-color: #dc3545; color: white; }
    
    .alert {
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    
    .alert-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    
    .alert-danger {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    
    .alert-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
    }
</style>
{% endblock %}

{% block content %}
<div class="analysis-container">
    <!-- Header -->
    <div class="analysis-header">
        <h1><i class="fas fa-search-plus"></i> CV Matching Analysis</h1>
        <p class="mb-0">Analyze all CVs against a selected vacancy and generate detailed compatibility reports</p>
    </div>
    
    <!-- Vacancy Selection Form -->
    <div class="vacancy-selector">
        <h3><i class="fas fa-briefcase"></i> Select Vacancy for Analysis</h3>
        <p class="text-muted mb-4">Choose a vacancy to analyze all available CVs against it. The AI will provide compatibility scores and recommendations.</p>
        
        <form id="analysis-form">
            {% csrf_token %}
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group">
                        <label for="vacancy_id" class="form-label">
                            <i class="fas fa-building"></i> Available Vacancies
                        </label>
                        <select class="form-control" id="vacancy_id" name="vacancy_id" required>
                            <option value="">Select a vacancy...</option>
                            {% for vacancy in vacancies %}
                            <option value="{{ vacancy.id }}">
                                {{ vacancy.title }} - {{ vacancy.company.name }} ({{ vacancy.category }})
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-file-pdf"></i> Available CVs
                        </label>
                        <div class="form-control" style="background-color: #f8f9fa;">
                            {{ cvs_count }} CVs ready for analysis
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <button type="submit" class="btn-analyze" id="analyze-btn">
                    <i class="fas fa-robot"></i> Start AI Analysis
                </button>
            </div>
        </form>
    </div>
    
    <!-- Analysis Progress -->
    <div class="analysis-progress" id="analysis-progress">
        <div class="spinner"></div>
        <h4>AI Analysis in Progress...</h4>
        <p class="text-muted">Analyzing CVs against the selected vacancy. This may take a few minutes.</p>
    </div>
    
    <!-- Results Container -->
    <div class="results-container" id="results-container">
        <div class="results-header">
            <h3><i class="fas fa-chart-line"></i> Analysis Results</h3>
            <button class="btn btn-secondary" onclick="location.reload()">
                <i class="fas fa-redo"></i> New Analysis
            </button>
        </div>
        
        <div class="alert alert-success" id="success-message" style="display: none;"></div>
        <div class="alert alert-danger" id="error-message" style="display: none;"></div>
        
        <div class="results-stats" id="results-stats" style="display: none;">
            <div class="stat-card">
                <div class="stat-number" id="total-analyzed">0</div>
                <div class="stat-label">CVs Analyzed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="top-matches">0</div>
                <div class="stat-label">Top Matches</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avg-score">0%</div>
                <div class="stat-label">Average Score</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="recommended">0</div>
                <div class="stat-label">Recommended</div>
            </div>
        </div>
        
        <div id="vacancy-info" style="display: none;">
            <h4><i class="fas fa-info-circle"></i> Analyzed Vacancy</h4>
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title" id="vacancy-title"></h5>
                    <p class="card-text" id="company-name"></p>
                </div>
            </div>
        </div>
        
        <div id="matches-results" style="display: none;">
            <h4><i class="fas fa-trophy"></i> Top 20 Compatible CVs</h4>
            <div class="table-responsive">
                <table class="matches-table">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>CV File</th>
                            <th>Compatibility Score</th>
                            <th>Skills Match</th>
                            <th>Recommendation</th>
                            <th>Summary</th>
                        </tr>
                    </thead>
                    <tbody id="matches-table-body">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('analysis-form');
    const analyzeBtn = document.getElementById('analyze-btn');
    const progressDiv = document.getElementById('analysis-progress');
    const resultsDiv = document.getElementById('results-container');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const vacancyId = document.getElementById('vacancy_id').value;
        if (!vacancyId) {
            alert('Please select a vacancy for analysis');
            return;
        }
        
        // Show progress
        analyzeBtn.disabled = true;
        progressDiv.style.display = 'block';
        resultsDiv.style.display = 'none';
        
        // Submit form
        const formData = new FormData(form);
        
        fetch('{% url "cv_matching_analysis" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            progressDiv.style.display = 'none';
            analyzeBtn.disabled = false;
            
            if (data.success) {
                showResults(data);
            } else {
                showError(data.error);
            }
        })
        .catch(error => {
            progressDiv.style.display = 'none';
            analyzeBtn.disabled = false;
            showError('Analysis failed: ' + error.message);
        });
    });
    
    function showResults(data) {
        resultsDiv.style.display = 'block';
        
        // Show success message
        const successMsg = document.getElementById('success-message');
        successMsg.textContent = data.message;
        successMsg.style.display = 'block';
        
        // Update vacancy info
        document.getElementById('vacancy-title').textContent = data.vacancy_title;
        document.getElementById('company-name').textContent = data.company_name;
        document.getElementById('vacancy-info').style.display = 'block';
        
        // Update stats
        const matches = data.top_matches || [];
        const avgScore = matches.length > 0 
            ? Math.round(matches.reduce((sum, match) => sum + match.compatibility_score, 0) / matches.length)
            : 0;
        const recommended = matches.filter(match => 
            match.recommendation.includes('Recommended')).length;
        
        document.getElementById('total-analyzed').textContent = data.total_analyzed;
        document.getElementById('top-matches').textContent = matches.length;
        document.getElementById('avg-score').textContent = avgScore + '%';
        document.getElementById('recommended').textContent = recommended;
        document.getElementById('results-stats').style.display = 'flex';
        
        // Show matches table
        if (matches.length > 0) {
            const tbody = document.getElementById('matches-table-body');
            tbody.innerHTML = '';
            
            matches.forEach((match, index) => {
                const row = document.createElement('tr');
                
                const scoreClass = getScoreClass(match.compatibility_score);
                const recClass = getRecommendationClass(match.recommendation);
                
                row.innerHTML = `
                    <td><strong>#${index + 1}</strong></td>
                    <td>${match.cv_filename}</td>
                    <td><span class="score-badge ${scoreClass}">${match.compatibility_score}%</span></td>
                    <td><span class="score-badge ${getScoreClass(match.skills_match)}">${match.skills_match}%</span></td>
                    <td><span class="recommendation-badge ${recClass}">${match.recommendation}</span></td>
                    <td>${match.summary}</td>
                `;
                
                tbody.appendChild(row);
            });
            
            document.getElementById('matches-results').style.display = 'block';
        }
    }
    
    function showError(message) {
        resultsDiv.style.display = 'block';
        const errorMsg = document.getElementById('error-message');
        errorMsg.textContent = message;
        errorMsg.style.display = 'block';
    }
    
    function getScoreClass(score) {
        if (score >= 80) return 'score-excellent';
        if (score >= 60) return 'score-good';
        if (score >= 40) return 'score-average';
        return 'score-poor';
    }
    
    function getRecommendationClass(recommendation) {
        if (recommendation.includes('Highly')) return 'rec-highly';
        if (recommendation.includes('Recommended')) return 'rec-recommended';
        if (recommendation.includes('Consider')) return 'rec-consider';
        return 'rec-not-suitable';
    }
});
</script>
{% endblock %} 