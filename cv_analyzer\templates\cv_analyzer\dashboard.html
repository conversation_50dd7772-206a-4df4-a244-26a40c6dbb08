{% extends 'cv_analyzer/base.html' %}
{% load static %}

{% block title %}Dashboard{% endblock %}

{% block extra_css %}
<style>
/* Enhanced Popup Styling - Complete Fix */
div.popup-overlay {
    display: none !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 999999 !important;
    align-items: center !important;
    justify-content: center !important;
    background: rgba(0, 0, 0, 0.8) !important;
    backdrop-filter: blur(12px) !important;
    -webkit-backdrop-filter: blur(12px) !important;
    opacity: 0 !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
}

div.popup-overlay.show {
    display: flex !important;
    opacity: 1 !important;
    pointer-events: all !important;
}

/* Force overlay to cover everything */
div.popup-overlay::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.85) !important;
    z-index: -1 !important;
}

/* Enhanced Popup Content */
div.popup-content {
    position: relative !important;
    background: white !important;
    border-radius: 16px !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.6) !important;
    margin: 20px !important;
    padding: 0 !important;
    max-width: 800px !important;
    width: 90% !important;
    max-height: 90vh !important;
    overflow: hidden !important;
    transform: scale(0.9) translateY(50px) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    z-index: 1000000 !important;
}

div.popup-overlay.show div.popup-content {
    transform: scale(1) translateY(0) !important;
}

/* Header Styling */
div.popup-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 24px 32px 16px 32px !important;
    border-bottom: 1px solid #e5e7eb !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    margin-bottom: 0 !important;
}

div.popup-header h3 {
    margin: 0 !important;
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: white !important;
}

div.popup-header i {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Close Button */
button.popup-close {
    color: rgba(255, 255, 255, 0.8) !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    width: 36px !important;
    height: 36px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    font-size: 16px !important;
}

button.popup-close:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    transform: scale(1.1) !important;
}

/* Content Area */
div.popup-content > div:not(.popup-header) {
    padding: 32px !important;
    max-height: calc(90vh - 100px) !important;
    overflow-y: auto !important;
}

/* Tab Buttons */
button.tab-btn {
    padding: 10px 20px !important;
    border: 2px solid #e5e7eb !important;
    background: white !important;
    color: #6b7280 !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    cursor: pointer !important;
    margin-right: 8px !important;
}

button.tab-btn.active,
button.tab-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-color: #667eea !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

/* Dropzone Enhancement */
div.dropzone {
    border: 3px dashed #cbd5e1 !important;
    border-radius: 12px !important;
    padding: 40px 20px !important;
    text-align: center !important;
    background: #f8fafc !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

div.dropzone:hover {
    border-color: #667eea !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15) !important;
}

div.dropzone::before {
    content: '';
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent) !important;
    transition: left 0.5s ease !important;
}

div.dropzone:hover::before {
    left: 100% !important;
}

/* Upload Icon */
div.file-upload-icon {
    width: 80px !important;
    height: 80px !important;
    margin: 0 auto 20px auto !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
}

/* Form Controls */
select.form-control,
input.form-control {
    width: 100% !important;
    padding: 12px 16px !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    background: white !important;
    color: #374151 !important;
    font-size: 14px !important;
    transition: all 0.2s ease !important;
}

select.form-control:focus,
input.form-control:focus {
    outline: none !important;
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* Buttons */
button.btn {
    padding: 12px 24px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    border: none !important;
    font-size: 14px !important;
}

button.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

button.btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

button.btn-secondary {
    background: #f3f4f6 !important;
    color: #6b7280 !important;
    border: 2px solid #e5e7eb !important;
}

button.btn-secondary:hover {
    background: #e5e7eb !important;
    transform: translateY(-1px) !important;
}

/* Dark Mode Support */
html.dark div.popup-content {
    background: #1f2937 !important;
    color: white !important;
}

html.dark div.dropzone {
    background: #374151 !important;
    border-color: #4b5563 !important;
}

html.dark select.form-control,
html.dark input.form-control {
    background: #374151 !important;
    border-color: #4b5563 !important;
    color: white !important;
}

/* Specific Popup IDs */
#uploadPopup, #analyzePopup, #managePopup, #reportsPopup, #cvDetailPopup, #vacancyPopup, #companyPopup {
    display: none !important;
}

#uploadPopup.show, #analyzePopup.show, #managePopup.show, #reportsPopup.show, #cvDetailPopup.show, #vacancyPopup.show, #companyPopup.show {
    display: flex !important;
}

/* Additional backdrop enforcement */
.popup-overlay.show {
    background: rgba(0, 0, 0, 0.85) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
}

/* Hide main content when popup is open */
body.popup-open {
    overflow: hidden !important;
}

body.popup-open > *:not(.popup-overlay) {
    filter: blur(2px) !important;
    pointer-events: none !important;
}

/* Force absolute positioning for problematic cases */
div[id$="Popup"] {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.9) !important;
    z-index: 999999 !important;
}

div[id$="Popup"].show {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}
</style>
{% endblock %}

{% block content %}
<!-- Statistics Overview -->
<div class="grid gap-6 mb-8 md:grid-cols-2 xl:grid-cols-4">
    <!-- Total CVs Analyzed -->
    <div class="p-4 bg-white rounded-lg shadow-sm dark:bg-gray-800">
        <div class="flex items-center">
            <div class="p-3 mr-4 text-blue-500 bg-blue-100 rounded-full dark:text-blue-100 dark:bg-blue-500">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div>
                <p class="mb-2 text-sm font-medium text-gray-600 dark:text-gray-400">Total CVs Analyzed</p>
                <p class="text-lg font-semibold text-gray-700 dark:text-gray-200">{{ total_cvs }}</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="{% url 'cv_management' %}" class="inline-flex items-center text-sm text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300">
                View all CVs
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>
            </a>
        </div>
    </div>

    <!-- Average Score -->
    <div class="p-4 bg-white rounded-lg shadow-sm dark:bg-gray-800">
        <div class="flex items-center">
            <div class="p-3 mr-4 text-green-500 bg-green-100 rounded-full dark:text-green-100 dark:bg-green-500">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div>
                <p class="mb-2 text-sm font-medium text-gray-600 dark:text-gray-400">Average Score</p>
                <p class="text-lg font-semibold text-gray-700 dark:text-gray-200">{{ avg_score|floatformat:1 }}%</p>
            </div>
        </div>
        <div class="mt-4">
            <div class="text-sm text-gray-500 dark:text-gray-400">
                Based on overall CV analysis scores
            </div>
        </div>
    </div>

    <!-- Active Vacancies -->
    <div class="p-4 bg-white rounded-lg shadow-sm dark:bg-gray-800">
        <div class="flex items-center">
            <div class="p-3 mr-4 text-purple-500 bg-purple-100 rounded-full dark:text-purple-100 dark:bg-purple-500">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path>
                </svg>
            </div>
            <div>
                <p class="mb-2 text-sm font-medium text-gray-600 dark:text-gray-400">Active Vacancies</p>
                <p class="text-lg font-semibold text-gray-700 dark:text-gray-200">{{ active_vacancies_count }}</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="{% url 'vacancy_management' %}" class="inline-flex items-center text-sm text-purple-500 hover:text-purple-600 dark:text-purple-400 dark:hover:text-purple-300">
                Manage vacancies
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>
            </a>
        </div>
    </div>

    <!-- Companies -->
    <div class="p-4 bg-white rounded-lg shadow-sm dark:bg-gray-800">
        <div class="flex items-center">
            <div class="p-3 mr-4 text-orange-500 bg-orange-100 rounded-full dark:text-orange-100 dark:bg-orange-500">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div>
                <p class="mb-2 text-sm font-medium text-gray-600 dark:text-gray-400">Companies</p>
                <p class="text-lg font-semibold text-gray-700 dark:text-gray-200">{{ total_companies }}</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="{% url 'company_management' %}" class="inline-flex items-center text-sm text-orange-500 hover:text-orange-600 dark:text-orange-400 dark:hover:text-orange-300">
                View companies
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>
            </a>
        </div>
    </div>
</div>



<!-- AI Analysis Tools -->
<div class="mb-8">
    <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
        <i class="fas fa-robot mr-2"></i>AI Analysis Tools
    </h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <a href="{% url 'cv_matching_analysis' %}" class="p-6 bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 dark:bg-gray-800 dark:hover:bg-gray-700 text-left border-l-4 border-purple-500">
            <div class="flex items-center mb-3">
                <div class="p-3 mr-4 text-purple-500 bg-purple-100 rounded-full dark:text-purple-100 dark:bg-purple-500">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">CV Matching Analysis</h3>
            </div>
            <p class="text-gray-600 dark:text-gray-400 mb-3">
                Analyze all CVs against a selected vacancy and generate detailed compatibility reports with top 20 matches.
            </p>
            <div class="flex items-center text-sm text-purple-600 dark:text-purple-400">
                <span>Start AI Analysis</span>
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </div>
        </a>
        
        <a href="{% url 'cv_duplication_check' %}" class="p-6 bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 dark:bg-gray-800 dark:hover:bg-gray-700 text-left border-l-4 border-red-500">
            <div class="flex items-center mb-3">
                <div class="p-3 mr-4 text-red-500 bg-red-100 rounded-full dark:text-red-100 dark:bg-red-500">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h2m0 0h2a2 2 0 002-2V7a2 2 0 00-2-2h-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v10a2 2 0 01-2 2H9"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">CV Duplication Check</h3>
            </div>
            <p class="text-gray-600 dark:text-gray-400 mb-3">
                Scan database for duplicate analyses and CV files to optimize data integrity and generate merge reports.
            </p>
            <div class="flex items-center text-sm text-red-600 dark:text-red-400">
                <span>Check Duplicates</span>
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </div>
        </a>
    </div>
</div>

<!-- Recent CV Analyses -->
<div class="mb-8">
    <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Recent CV Analyses</h2>
    <div class="overflow-hidden bg-white rounded-lg shadow-sm dark:bg-gray-800">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Name</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Score</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Vacancy</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Date</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                {% for analysis in recent_analyses %}
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="relative w-10 h-10 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600">
                                    <svg class="absolute w-12 h-12 text-gray-400 -left-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900 dark:text-gray-200">{{ analysis.candidate_name }}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">{{ analysis.email }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if analysis.overall_score >= 80 %}bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                {% elif analysis.overall_score >= 60 %}bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                                {% else %}bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100{% endif %}">
                                {{ analysis.overall_score }}%
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 dark:text-gray-200">{{ analysis.vacancy.title }}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ analysis.vacancy.company.name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {{ analysis.created_at|date:"M d, Y" }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <a href="{% url 'analysis_detail' analysis.id %}" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">View</a>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        No recent CV analyses
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <div class="mt-4">
        <a href="{% url 'cv_management' %}" class="inline-flex items-center text-sm text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300">
            View all analyses
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>
        </a>
    </div>
</div>

<!-- Performance Chart -->
<div class="mb-8">
    <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">CV Analysis Performance</h2>
    <div class="bg-white p-4 rounded-lg shadow-sm dark:bg-gray-800">
        <canvas id="performanceChart" class="w-full" style="max-height: 300px;"></canvas>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('performanceChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: {{ chart_labels|safe }},
            datasets: [{
                label: 'Average Score',
                data: {{ chart_data|safe }},
                borderColor: '#3B82F6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    grid: {
                        color: 'rgba(156, 163, 175, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
});
</script>

<!-- Include all popup components -->
{% include 'cv_analyzer/components/upload_popup.html' %}
{% include 'cv_analyzer/components/manage_popup.html' %}
{% include 'cv_analyzer/components/analyze_popup.html' %}
{% include 'cv_analyzer/components/reports_popup.html' %}
{% include 'cv_analyzer/components/cv_detail_popup.html' %}
{% include 'cv_analyzer/components/vacancy_popup.html' %}
{% include 'cv_analyzer/components/company_popup.html' %}

<script>
// Popup management functions
function openPopup(popupId) {
    const popup = document.getElementById(popupId);
    if (popup) {
        // Add popup-open class to body
        document.body.classList.add('popup-open');
        document.body.style.overflow = 'hidden';
        
        popup.classList.add('show');
        
        console.log('Popup opened:', popupId);
    }
}

function closePopup(popupId) {
    const popup = document.getElementById(popupId);
    if (popup) {
        popup.classList.remove('show');
        document.body.classList.remove('popup-open');
        document.body.style.overflow = 'auto';
        
        console.log('Popup closed:', popupId);
    }
}

// Close popup when clicking outside
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('popup-overlay')) {
        event.target.classList.remove('show');
        document.body.classList.remove('popup-open');
        document.body.style.overflow = 'auto';
    }
});

// Upload functionality
function handleSingleUpload(form) {
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.textContent = 'Uploading...';
    submitBtn.disabled = true;
    
    fetch('/upload/local/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('CV uploaded successfully!', 'success');
            closePopup('uploadPopup');
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(data.error || 'Upload failed', 'error');
        }
    })
    .catch(error => {
        showNotification('Upload error: ' + error.message, 'error');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
}

function handleBatchUpload(form) {
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.textContent = 'Uploading...';
    submitBtn.disabled = true;
    
    fetch('/upload/batch/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Successfully uploaded ${data.uploaded_count} CVs!`, 'success');
            closePopup('uploadPopup');
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(data.error || 'Batch upload failed', 'error');
        }
    })
    .catch(error => {
        showNotification('Upload error: ' + error.message, 'error');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
}

// File drop zone handlers
function setupDropZones() {
    // Single file dropzone
    const singleDropzone = document.getElementById('singleDropzone');
    if (singleDropzone) {
        const fileInput = singleDropzone.querySelector('input[type="file"]');
        
        singleDropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            singleDropzone.classList.add('drag-over');
        });
        
        singleDropzone.addEventListener('dragleave', () => {
            singleDropzone.classList.remove('drag-over');
        });
        
        singleDropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            singleDropzone.classList.remove('drag-over');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                updateSingleFileDisplay(files[0]);
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                updateSingleFileDisplay(e.target.files[0]);
            }
        });
    }
    
    // Batch file dropzone
    const batchDropzone = document.getElementById('batchDropzone');
    if (batchDropzone) {
        const fileInput = batchDropzone.querySelector('input[type="file"]');
        
        batchDropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            batchDropzone.classList.add('drag-over');
        });
        
        batchDropzone.addEventListener('dragleave', () => {
            batchDropzone.classList.remove('drag-over');
        });
        
        batchDropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            batchDropzone.classList.remove('drag-over');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                updateBatchFileDisplay(files);
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                updateBatchFileDisplay(e.target.files);
            }
        });
    }
}

function updateSingleFileDisplay(file) {
    const singleDropzone = document.getElementById('singleDropzone');
    const fileInfo = singleDropzone.querySelector('.file-info');
    const fileName = fileInfo.querySelector('.file-name');
    const fileSize = fileInfo.querySelector('.file-size');
    
    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);
    fileInfo.style.display = 'block';
    
    // Enable submit button
    const form = document.getElementById('singleUploadForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    submitBtn.disabled = false;
}

function updateBatchFileDisplay(files) {
    const batchDropzone = document.getElementById('batchDropzone');
    const filesInfo = batchDropzone.querySelector('.files-info');
    const filesList = filesInfo.querySelector('.files-list');
    
    filesList.innerHTML = '';
    
    Array.from(files).forEach((file, index) => {
        if (index < 10) { // Limit to 10 files
            const fileDiv = document.createElement('div');
            fileDiv.className = 'flex items-center justify-between mb-2';
            fileDiv.innerHTML = `
                <span class="font-medium">${file.name}</span>
                <span class="text-sm text-gray-500">${formatFileSize(file.size)}</span>
            `;
            filesList.appendChild(fileDiv);
        }
    });
    
    filesInfo.style.display = 'block';
    
    // Enable submit button
    const form = document.getElementById('batchUploadForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    submitBtn.disabled = false;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        type === 'info' ? 'bg-blue-500' : 'bg-gray-500'
    }`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setupDropZones();
    
    // Setup form handlers
    const singleForm = document.getElementById('singleUploadForm');
    if (singleForm) {
        singleForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleSingleUpload(this);
        });
    }
    
    const batchForm = document.getElementById('batchUploadForm');
    if (batchForm) {
        batchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleBatchUpload(this);
        });
    }
});
</script>

{% endblock %}