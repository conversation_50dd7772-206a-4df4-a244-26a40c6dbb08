{% extends "cv_analyzer/base.html" %}
{% load static %}

{% block title %}Dashboard - CV Analyzer{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .metric-card {
        background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
        border: none;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .metric-card:hover::before {
        opacity: 1;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }
    
    .metric-card.blue {
        --gradient-start: #3b82f6;
        --gradient-end: #1d4ed8;
    }
    
    .metric-card.green {
        --gradient-start: #10b981;
        --gradient-end: #047857;
    }
    
    .metric-card.purple {
        --gradient-start: #8b5cf6;
        --gradient-end: #6d28d9;
    }
    
    .metric-card.orange {
        --gradient-start: #f59e0b;
        --gradient-end: #d97706;
    }

    .quick-action-card {
        border: 1px solid #e5e7eb;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        background: white;
    }
    
    .quick-action-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        border-color: #3b82f6;
    }
    
    .dark .quick-action-card {
        background: #374151;
        border-color: #4b5563;
    }
    
    .dark .quick-action-card:hover {
        border-color: #6366f1;
    }

    .chart-container {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
    }
    
    .dark .chart-container {
        background: #1f2937;
        border-color: #374151;
    }

    .activity-timeline::before {
        content: '';
        position: absolute;
        left: 6px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
        border-radius: 1px;
    }

    .activity-item {
        background: linear-gradient(to right, rgba(59, 130, 246, 0.05), transparent);
        border-left: 3px solid transparent;
        transition: all 0.3s ease;
    }
    
    .activity-item:hover {
        background: linear-gradient(to right, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.05));
        border-left-color: #3b82f6;
        transform: translateX(4px);
    }

    .section-header {
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }
    
    .dark .section-header {
        border-color: #374151;
    }

    .content-grid {
        display: grid;
        grid-template-columns: 1fr 380px;
        gap: 2rem;
        align-items: start;
    }
    
    @media (max-width: 1280px) {
        .content-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
    }

    .main-content {
        min-height: 0;
    }

    .sidebar-content {
        position: sticky;
        top: 2rem;
        max-height: calc(100vh - 4rem);
        overflow-y: auto;
        padding-right: 0.5rem;
    }
    
    .sidebar-content::-webkit-scrollbar {
        width: 4px;
    }
    
    .sidebar-content::-webkit-scrollbar-track {
        background: transparent;
    }
    
    .sidebar-content::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 2px;
    }
    
    .dark .sidebar-content::-webkit-scrollbar-thumb {
        background: #4b5563;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .quick-actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
    }

    .table-wrapper {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
        overflow: hidden;
    }
    
    .dark .table-wrapper {
        background: #1f2937;
        border-color: #374151;
    }

    .page-header {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        padding: 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .sidebar-section {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .dark .sidebar-section {
        background: #1f2937;
        border-color: #374151;
    }
</style>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="text-3xl font-bold mb-2">
            <i class="fas fa-tachometer-alt mr-3"></i>Dashboard Overview
        </h1>
        <p class="text-blue-100 text-lg max-w-2xl mx-auto">
            Monitor your CV analysis performance, track system metrics, and manage recruitment activities from your central command center
        </p>
    </div>

    <!-- Content Grid -->
    <div class="content-grid">
        <!-- Main Content Area -->
        <div class="main-content space-y-6">
            <!-- KPI Metrics Grid -->
            <div class="stats-grid">
                <div class="metric-card blue text-white rounded-xl p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold opacity-90 mb-1">Total CVs</h3>
                            <div class="text-3xl font-bold">{{ total_cvs|default:0 }}</div>
                            <div class="text-sm opacity-75 mt-2 flex items-center">
                                <i class="fas fa-arrow-up mr-1"></i>
                                +{{ new_cvs_this_month|default:0 }} this month
                            </div>
                        </div>
                        <div class="text-4xl opacity-80">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                </div>

                <div class="metric-card green text-white rounded-xl p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold opacity-90 mb-1">Active Vacancies</h3>
                            <div class="text-3xl font-bold">{{ active_vacancies|default:0 }}</div>
                            <div class="text-sm opacity-75 mt-2 flex items-center">
                                <i class="fas fa-briefcase mr-1"></i>
                                {{ companies_count|default:0 }} companies
                            </div>
                        </div>
                        <div class="text-4xl opacity-80">
                            <i class="fas fa-briefcase"></i>
                        </div>
                    </div>
                </div>

                <div class="metric-card purple text-white rounded-xl p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold opacity-90 mb-1">Matches Made</h3>
                            <div class="text-3xl font-bold">{{ matches_made|default:0 }}</div>
                            <div class="text-sm opacity-75 mt-2 flex items-center">
                                <i class="fas fa-percentage mr-1"></i>
                                {{ match_rate|default:0 }}% success rate
                            </div>
                        </div>
                        <div class="text-4xl opacity-80">
                            <i class="fas fa-handshake"></i>
                        </div>
                    </div>
                </div>

                <div class="metric-card orange text-white rounded-xl p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold opacity-90 mb-1">Avg Score</h3>
                            <div class="text-3xl font-bold">{{ average_score|default:0 }}%</div>
                            <div class="text-sm opacity-75 mt-2 flex items-center">
                                <i class="fas fa-chart-line mr-1"></i>
                                Quality indicator
                            </div>
                        </div>
                        <div class="text-4xl opacity-80">
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Section -->
            <div class="section-header">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-bolt mr-3 text-yellow-500"></i>Quick Actions
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Frequently used tools and shortcuts</p>
            </div>

            <div class="quick-actions-grid">
                <a href="{% url 'upload_local' %}" class="quick-action-card rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="bg-blue-100 dark:bg-blue-900 p-4 rounded-full mr-4">
                            <i class="fas fa-upload text-blue-600 dark:text-blue-400 text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 dark:text-white mb-1">Upload CV</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Analyze new resumes</p>
                        </div>
                        <i class="fas fa-arrow-right text-gray-400"></i>
                    </div>
                </a>

                <a href="{% url 'vacancy_list' %}?action=create" class="quick-action-card rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="bg-green-100 dark:bg-green-900 p-4 rounded-full mr-4">
                            <i class="fas fa-plus-circle text-green-600 dark:text-green-400 text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 dark:text-white mb-1">Create Vacancy</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Post new job openings</p>
                        </div>
                        <i class="fas fa-arrow-right text-gray-400"></i>
                    </div>
                </a>

                <a href="{% url 'company_management' %}?action=new" class="quick-action-card rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="bg-purple-100 dark:bg-purple-900 p-4 rounded-full mr-4">
                            <i class="fas fa-building text-purple-600 dark:text-purple-400 text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 dark:text-white mb-1">Add Company</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Register new partners</p>
                        </div>
                        <i class="fas fa-arrow-right text-gray-400"></i>
                    </div>
                </a>

                <a href="{% url 'cv_management' %}" class="quick-action-card rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="bg-orange-100 dark:bg-orange-900 p-4 rounded-full mr-4">
                            <i class="fas fa-chart-bar text-orange-600 dark:text-orange-400 text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 dark:text-white mb-1">View Analytics</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Analyze performance data</p>
                        </div>
                        <i class="fas fa-arrow-right text-gray-400"></i>
                    </div>
                </a>
            </div>

            <!-- Recent CV Analyses Section -->
            <div class="section-header">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-history mr-3 text-blue-500"></i>Recent CV Analyses
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Latest candidate evaluations and scores</p>
            </div>

            <div class="table-wrapper">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Latest Analyses</h3>
                        <a href="{% url 'cv_management' %}" class="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center">
                            View all <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Candidate</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Score</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Position</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                            {% for analysis in recent_analyses %}
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                                            {{ analysis.candidate_name|first|upper|default:"?" }}
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">{{ analysis.candidate_name|default:"Unknown" }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ analysis.email|default:"No email" }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mr-3">
                                            <div class="bg-gradient-to-r from-green-500 to-green-600 h-2.5 rounded-full transition-all duration-500" style="width: {{ analysis.overall_score|default:0 }}%"></div>
                                        </div>
                                        <span class="text-sm font-semibold text-gray-900 dark:text-white">{{ analysis.overall_score|default:0 }}%</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ analysis.vacancy.title|default:"No position" }}</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ analysis.vacancy.company.name|default:"No company" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    {{ analysis.created_at|date:"M d, Y"|default:"Unknown" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{% url 'analysis_detail' analysis.id %}" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 flex items-center">
                                        <i class="fas fa-eye mr-1"></i>View Details
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="px-6 py-12 text-center">
                                    <div class="text-gray-500 dark:text-gray-400">
                                        <i class="fas fa-inbox text-6xl mb-4 opacity-30"></i>
                                        <h3 class="text-lg font-medium mb-2">No recent CV analyses found</h3>
                                        <p class="text-sm mb-4">Start by uploading your first CV to see analysis results here</p>
                                        <a href="{% url 'upload_local' %}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                            <i class="fas fa-upload mr-2"></i>Upload your first CV
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Sidebar Content -->
        <div class="sidebar-content space-y-6">
            <!-- System Performance Chart -->
            <div class="sidebar-section">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-chart-pie mr-2 text-green-500"></i>System Performance
                </h3>
                <div class="relative mb-4">
                    <canvas id="performanceChart" width="280" height="280"></canvas>
                </div>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="text-green-600 dark:text-green-400 font-bold text-lg">98.5%</div>
                        <div class="text-gray-500 dark:text-gray-400">Uptime</div>
                    </div>
                    <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="text-blue-600 dark:text-blue-400 font-bold text-lg">1.2s</div>
                        <div class="text-gray-500 dark:text-gray-400">Avg Response</div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="sidebar-section">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-clock mr-2 text-purple-500"></i>Recent Activity
                </h3>
                <div class="activity-timeline relative space-y-3">
                    <div class="activity-item relative pl-8 py-3 rounded-lg">
                        <div class="absolute left-2 top-4 w-3 h-3 bg-blue-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white">New CV uploaded</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">John Doe • 5 minutes ago</div>
                    </div>
                    <div class="activity-item relative pl-8 py-3 rounded-lg">
                        <div class="absolute left-2 top-4 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white">Analysis completed</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Jane Smith • 15 minutes ago</div>
                    </div>
                    <div class="activity-item relative pl-8 py-3 rounded-lg">
                        <div class="absolute left-2 top-4 w-3 h-3 bg-purple-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white">New vacancy created</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Software Engineer • 1 hour ago</div>
                    </div>
                    <div class="activity-item relative pl-8 py-3 rounded-lg">
                        <div class="absolute left-2 top-4 w-3 h-3 bg-orange-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white">Company registered</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">TechCorp Inc • 2 hours ago</div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View all activity <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="sidebar-section">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-tachometer-alt mr-2 text-red-500"></i>Quick Stats
                </h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-clock text-blue-500 mr-2"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Processing Time</span>
                        </div>
                        <span class="text-sm font-semibold text-gray-900 dark:text-white">2.3s avg</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Success Rate</span>
                        </div>
                        <span class="text-sm font-semibold text-green-600 dark:text-green-400">94.8%</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-hourglass-half text-yellow-500 mr-2"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Queue Length</span>
                        </div>
                        <span class="text-sm font-semibold text-blue-600 dark:text-blue-400">3 pending</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-hdd text-orange-500 mr-2"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Storage Used</span>
                        </div>
                        <span class="text-sm font-semibold text-orange-600 dark:text-orange-400">2.4 GB</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Performance Chart
    const ctx = document.getElementById('performanceChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Successful', 'Pending', 'Failed'],
            datasets: [{
                data: [85, 10, 5],
                backgroundColor: [
                    '#10b981',
                    '#f59e0b',
                    '#ef4444'
                ],
                borderWidth: 0,
                cutout: '70%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        color: '#6b7280'
                    }
                }
            }
        }
    });
    
    // Add smooth animations to metric cards
    const metricCards = document.querySelectorAll('.metric-card');
    metricCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });
});
</script>
{% endblock %}