<!DOCTYPE html>
<html>
<head>
    <title>Re-analyze Diagnostic</title>
    <meta name="csrf-token" content="{{ csrf_token }}">
</head>
<body>
    <h1>Re-analyze Diagnostic Test</h1>
    
    <button id="testBtn" onclick="testReAnalyze()">Test Re-analyze API</button>
    
    <div id="results" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;">
        <h3>Results:</h3>
        <pre id="output">Click the button to test...</pre>
    </div>

    <script>
    function getCSRFToken() {
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        if (metaTag) {
            return metaTag.getAttribute('content');
        }
        
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        
        return '{{ csrf_token }}';
    }

    async function testReAnalyze() {
        const output = document.getElementById('output');
        output.textContent = 'Testing...\n';
        
        try {
            // Test data - CV IDs from vacancy 9
            const testData = {
                cv_ids: [9, 10, 11],
                vacancy_ids: [9],
                analysis_type: 'reanalyze'
            };
            
            output.textContent += `CSRF Token: ${getCSRFToken()}\n`;
            output.textContent += `Request Data: ${JSON.stringify(testData, null, 2)}\n\n`;
            
            const response = await fetch('/api/start-ai-analysis/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify(testData)
            });
            
            output.textContent += `Response Status: ${response.status}\n`;
            output.textContent += `Response OK: ${response.ok}\n\n`;
            
            const result = await response.json();
            output.textContent += `Response Data:\n${JSON.stringify(result, null, 2)}\n`;
            
            if (result.success) {
                output.textContent += `\n✅ SUCCESS! ${result.successful} of ${result.total_processed} candidates processed.\n`;
            } else {
                output.textContent += `\n❌ FAILED: ${result.message}\n`;
            }
            
        } catch (error) {
            output.textContent += `\n💥 ERROR: ${error.message}\n`;
            output.textContent += `Stack: ${error.stack}\n`;
        }
    }
    </script>
</body>
</html> 