{% extends "cv_analyzer/base.html" %}
{% load static %}

{% block title %}Operations Hub - CV Analyzer{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    /* Global Styles */
    .dashboard-container {
        max-width: 100%;
        margin: 0 auto;
        padding: 1rem;
    }

    /* Header Styles */
    .unified-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .unified-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    /* Quick Actions Bar */
    .quick-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 2rem;
        padding: 1rem;
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .action-btn {
        flex: 1;
        min-width: 200px;
        padding: 1rem;
        background: linear-gradient(135deg, var(--btn-start), var(--btn-end));
        color: white;
        border: none;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-weight: 600;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    }

    .action-btn.upload { --btn-start: #4f46e5; --btn-end: #7c3aed; }
    .action-btn.analyze { --btn-start: #059669; --btn-end: #0d9488; }
    .action-btn.manage { --btn-start: #dc2626; --btn-end: #ea580c; }
    .action-btn.reports { --btn-start: #7c2d12; --btn-end: #a16207; }
    .action-btn.duplication { --btn-start: #9333ea; --btn-end: #a855f7; }

    /* Main Content Grid */
    .dashboard-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    @media (max-width: 1024px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
    }

    /* Operations Layout */
    .operations-layout {
        display: flex;
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .operations-sidebar {
        flex: 0 0 300px;
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        height: fit-content;
        position: sticky;
        top: 1rem;
    }

    .operations-main {
        flex: 1;
        min-width: 0; /* Prevent flex item from overflowing */
    }

    @media (max-width: 1024px) {
        .operations-layout {
            flex-direction: column;
        }
        
        .operations-sidebar {
            flex: none;
            position: static;
        }
    }

    /* Filter Section */
    .filter-section {
        background: white;
        padding: 1rem;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
    }

    /* Filter Banner - Enhanced styling for the main content filter */
    .operations-main .filter-section {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: 1px solid #cbd5e1;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .operations-main .filter-section h3 {
        color: #1e293b;
        margin-bottom: 1rem;
    }

    /* Combined Controls Row */
    .combined-controls-row {
        display: flex;
        gap: 2rem;
        align-items: flex-start;
    }

    .controls-group {
        flex: 1;
        min-width: 0; /* Prevents flex items from overflowing */
    }

    .controls-group h3 {
        margin-bottom: 0.75rem !important;
    }

    /* Responsive design for combined controls */
    @media (max-width: 1024px) {
        .combined-controls-row {
            flex-direction: column;
            gap: 1.5rem;
        }
    }

    .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.75rem;
        margin-top: 0.75rem;
    }

    .filter-control {
        padding: 0.5rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }

    .filter-control:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* Tabbed Content */
    .tab-container {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .tab-navigation {
        display: flex;
        background: #f8fafc;
        border-bottom: 1px solid #e2e8f0;
    }

    .tab-btn {
        flex: 1;
        padding: 1rem;
        background: transparent;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        color: #64748b;
    }

    .tab-btn.active {
        background: white;
        color: #3b82f6;
        border-bottom: 3px solid #3b82f6;
    }

    .tab-content {
        padding: 1.5rem;
        min-height: 400px;
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
    }

    /* Data Tables */
    .data-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .data-table th,
    .data-table td {
        padding: 0.75rem 1rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
        vertical-align: middle;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
    }

    .data-table th {
        background: #f9fafb;
        font-weight: 600;
        color: #374151;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    /* Grid View Styles */
    .operations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
        padding: 1rem;
    }

    .grid-card {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        position: relative;
    }

    .grid-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        border-color: #3b82f6;
    }

    .grid-card-header {
        display: flex;
        justify-content: between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .grid-card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.25rem;
    }

    .grid-card-subtitle {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .grid-card-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
    }

    .grid-card-actions button {
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .grid-card-checkbox {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    .grid-card-meta {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin: 1rem 0;
    }

    .grid-card-meta-item {
        display: flex;
        justify-content: between;
        align-items: center;
        font-size: 0.875rem;
    }

    .grid-card-meta-label {
        color: #6b7280;
        font-weight: 500;
    }

    .grid-card-meta-value {
        color: #1f2937;
        font-weight: 600;
    }

    /* View Controls */
    .view-controls {
        display: flex;
        gap: 0.5rem;
    }

    .view-controls button.active {
        background-color: #3b82f6;
        color: white;
    }

    .data-table tbody tr:hover {
        background: #f9fafb;
    }

    .data-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Status Badges */
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-badge.uploaded { background: #fef3c7; color: #92400e; }
    .status-badge.processing { background: #ddd6fe; color: #5b21b6; }
    .status-badge.analyzed { background: #d1fae5; color: #065f46; }
    .status-badge.matched { background: #dcfce7; color: #166534; }
    .status-badge.active { background: #d1fae5; color: #065f46; }
    .status-badge.inactive { background: #f3f4f6; color: #6b7280; }

    /* Action Buttons */
    .btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.375rem;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background: #3b82f6;
        color: white;
    }

    .btn-primary:hover {
        background: #2563eb;
        transform: translateY(-1px);
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
    }

    .btn-secondary:hover {
        background: #4b5563;
        transform: translateY(-1px);
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .action-btn-group {
        display: flex;
        gap: 0.25rem;
    }

    .action-btn-mini {
        width: 2rem;
        height: 2rem;
        padding: 0;
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .action-btn-mini.view { background: #3b82f6; color: white; }
    .action-btn-mini.edit { background: #10b981; color: white; }
    .action-btn-mini.delete { background: #ef4444; color: white; }
    .action-btn-mini.analyze { background: #8b5cf6; color: white; }

    .action-btn-mini:hover {
        transform: scale(1.1);
    }

    /* Search Bar */
    .search-bar {
        display: flex;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
        align-items: center;
    }

    .search-input {
        flex: 1;
        padding: 0.5rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 0.875rem;
    }

    .search-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* List View */
    .list-view {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .list-header {
        background: #f8fafc;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e2e8f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .list-content {
        max-height: 600px;
        overflow-y: auto;
    }

    .view-controls {
        display: flex;
        gap: 0.5rem;
    }

    /* Pagination */
    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem;
        background: #f8fafc;
        border-top: 1px solid #e2e8f0;
    }

    .pagination-btn {
        padding: 0.5rem 1rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        background: white;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .pagination-btn:hover {
        background: #f3f4f6;
        border-color: #9ca3af;
    }

    .pagination-btn.active {
        background: #3b82f6;
        color: white;
        border-color: #3b82f6;
    }

    /* Bulk Actions */
    .bulk-actions {
        background: #f8fafc;
        padding: 1rem;
        border-top: 1px solid #e2e8f0;
        display: none;
    }

    .bulk-actions.show {
        display: block;
    }

    .bulk-action-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .btn-danger {
        background: #ef4444;
        color: white;
    }

    .btn-danger:hover {
        background: #dc2626;
        transform: translateY(-1px);
    }

    /* Operations Grid for Grid View */
    .operations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
    }

    .grid-item {
        background: white;
        border-radius: 0.5rem;
        padding: 1rem;
        border: 1px solid #e2e8f0;
        transition: all 0.2s ease;
    }

    .grid-item:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-container {
            padding: 0.5rem;
        }
        
        .quick-actions {
            flex-direction: column;
        }
        
        .action-btn {
            min-width: 100%;
        }
        
        .filter-grid {
            grid-template-columns: 1fr;
        }
        
        .tab-navigation {
            flex-wrap: wrap;
        }
        
        .tab-btn {
            flex: 1;
            min-width: 120px;
        }
    }

    /* Loading States */
    .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 2rem;
        color: #6b7280;
    }

    .spinner {
        width: 2rem;
        height: 2rem;
        border: 3px solid #e5e7eb;
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 0.5rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* BULLETPROOF POPUP STYLES - Override ALL conflicts */
    body .popup-overlay,
    html .popup-overlay,
    div.popup-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(0, 0, 0, 0.1) !important;
        z-index: 999999 !important;
        display: none !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 20px !important;
        box-sizing: border-box !important;
        overflow: auto !important;
        backdrop-filter: none !important;
        filter: none !important;
        opacity: 1 !important;
        visibility: hidden !important;
        pointer-events: none !important;
    }

    body .popup-overlay.show,
    html .popup-overlay.show,
    div.popup-overlay.show {
        display: flex !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    body .popup-overlay .popup-content,
    html .popup-overlay .popup-content,
    div.popup-overlay .popup-content {
        background: #ffffff !important;
        border: 2px solid #007bff !important;
        border-radius: 8px !important;
        padding: 10px !important;
        width: 99% !important;
        max-width: 2400px !important;
        height: auto !important;
        max-height: 90vh !important;
        overflow-y: auto !important;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
        position: relative !important;
        z-index: 1000000 !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        transform: none !important;
        margin: auto !important;
        backdrop-filter: none !important;
        filter: none !important;
        pointer-events: auto !important;
        font-family: inherit !important;
        line-height: 1.5 !important;
        color: #333 !important;
    }

    /* Remove any blur effects from the page */
    body:has(.popup-overlay.show) {
        filter: none !important;
        backdrop-filter: none !important;
    }

    /* For very large screens, use even more space */
    @media (min-width: 1600px) {
        body .popup-overlay .popup-content,
        html .popup-overlay .popup-content,
        div.popup-overlay .popup-content {
            width: 98% !important;
            max-width: 2600px !important;
        }
    }

    /* For ultra-wide screens, maximize space */
    @media (min-width: 2000px) {
        body .popup-overlay .popup-content,
        html .popup-overlay .popup-content,
        div.popup-overlay .popup-content {
            width: 97% !important;
            max-width: 2800px !important;
        }
    }

    /* For extremely wide screens, use almost full width */
    @media (min-width: 2560px) {
        body .popup-overlay .popup-content,
        html .popup-overlay .popup-content,
        div.popup-overlay .popup-content {
            width: 96% !important;
            max-width: 3200px !important;
        }
    }

    /* Ensure popup header and content are visible */
    .popup-overlay .popup-header,
    .popup-overlay .popup-content > * {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Fix any potential Tailwind conflicts */
    .popup-overlay * {
        box-sizing: border-box !important;
    }

    /* CLOSE BUTTON STYLES */
    .popup-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 20px !important;
        padding-bottom: 15px !important;
        border-bottom: 1px solid #eee !important;
    }

    .popup-close,
    .close-btn {
        position: absolute !important;
        top: 15px !important;
        right: 15px !important;
        background: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
        border-radius: 50% !important;
        width: 35px !important;
        height: 35px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        z-index: 1000001 !important;
        color: #6c757d !important;
        font-size: 16px !important;
        font-weight: bold !important;
        transition: all 0.2s ease !important;
        line-height: 1 !important;
        padding: 0 !important;
        text-decoration: none !important;
    }

    .popup-close:hover,
    .close-btn:hover {
        background: #e9ecef !important;
        color: #495057 !important;
        border-color: #adb5bd !important;
        transform: scale(1.1) !important;
    }

    .popup-close:active,
    .close-btn:active {
        transform: scale(0.95) !important;
    }

    /* Ensure close button icon is visible */
    .popup-close i,
    .close-btn i {
        font-size: 14px !important;
        line-height: 1 !important;
    }

    /* Flex utilities */
    .flex {
        display: flex;
    }

    .items-center {
        align-items: center;
    }

    .justify-between {
        justify-content: space-between;
    }

    .ml-3 {
        margin-left: 0.75rem;
    }

    .ml-2 {
        margin-left: 0.5rem;
    }

    .mr-1 {
        margin-right: 0.25rem;
    }

    .mr-2 {
        margin-right: 0.5rem;
    }

    .text-center {
        text-align: center;
    }

    .text-gray-500 {
        color: #6b7280;
    }

    .text-red-500 {
        color: #ef4444;
    }

    .p-4 {
        padding: 1rem;
    }

    /* Additional Popup Styles */
    .popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e2e8f0;
    }
    
    /* Analysis Option Card Styles */
    .option-card.selected {
        border-color: #3b82f6 !important;
        background-color: #eff6ff !important;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;
        transform: translateY(-2px);
    }
    
    .option-card {
        transition: all 0.3s ease;
    }

    .popup-close {
        background: none;
        border: none;
        font-size: 1.25rem;
        color: #6b7280;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.375rem;
        transition: all 0.2s ease;
    }

    .popup-close:hover {
        background: #f3f4f6;
        color: #374151;
    }

    /* Form and Modal Styles */
    .form-control {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        font-size: 0.875rem;
    }

    .form-control:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .flex {
        display: flex;
    }

    .flex-1 {
        flex: 1;
    }

    .gap-2 {
        gap: 0.5rem;
    }

    .justify-end {
        justify-content: flex-end;
    }

    .mb-4 {
        margin-bottom: 1rem;
    }

    /* Upload Method Tabs */
    .upload-methods .tab-btn {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-bottom: none;
        color: #64748b;
        padding: 0.75rem 1rem;
        margin-right: 0.25rem;
        border-radius: 0.5rem 0.5rem 0 0;
    }

    .upload-methods .tab-btn.active {
        background: white;
        color: #3b82f6;
        border-color: #3b82f6;
        position: relative;
        z-index: 1;
    }

    .upload-method {
        border: 1px solid #e2e8f0;
        border-radius: 0 0.5rem 0.5rem 0.5rem;
        padding: 1.5rem;
        background: white;
    }

    /* Form Styles */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* Dropzone Styles */
    .dropzone {
        border: 2px dashed #d1d5db;
        border-radius: 0.75rem;
        padding: 3rem 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }

    .dropzone:hover {
        border-color: #3b82f6;
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    }

    .dropzone.dragover {
        border-color: #10b981;
        background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    }

    .file-upload-icon {
        width: 4rem;
        height: 4rem;
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
    }

    /* Integration Cards */
    .integration-card {
        transition: all 0.3s ease;
    }

    .integration-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .grid {
        display: grid;
    }

    .grid-cols-1 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .gap-4 {
        gap: 1rem;
    }

    .space-x-2 > * + * {
        margin-left: 0.5rem;
    }

    .space-x-3 > * + * {
        margin-left: 0.75rem;
    }

    .mb-2 {
        margin-bottom: 0.5rem;
    }

    .mb-3 {
        margin-bottom: 0.75rem;
    }

    .mb-4 {
        margin-bottom: 1rem;
    }

    .mb-6 {
        margin-bottom: 1.5rem;
    }

    .mt-3 {
        margin-top: 0.75rem;
    }

    .mt-6 {
        margin-top: 1.5rem;
    }

    .block {
        display: block;
    }

    .font-medium {
        font-weight: 500;
    }

    .font-semibold {
        font-weight: 600;
    }

    .font-bold {
        font-weight: 700;
    }

    .text-sm {
        font-size: 0.875rem;
    }

    .text-xl {
        font-size: 1.25rem;
    }

    .text-3xl {
        font-size: 1.875rem;
    }

    .text-4xl {
        font-size: 2.25rem;
    }

    .text-blue-600 {
        color: #2563eb;
    }

    .text-green-600 {
        color: #16a34a;
    }

    .text-orange-600 {
        color: #ea580c;
    }

    .text-gray-600 {
        color: #4b5563;
    }

    .text-white {
        color: #ffffff;
    }

    .bg-blue-50 {
        background-color: #eff6ff;
    }

    .border {
        border-width: 1px;
    }

    .border-blue-200 {
        border-color: #bfdbfe;
    }

    .border-blue-500 {
        border-color: #3b82f6;
    }

    .rounded-lg {
        border-radius: 0.5rem;
    }

    .p-3 {
        padding: 0.75rem;
    }

    .hover\:border-blue-500:hover {
        border-color: #3b82f6;
    }

    .hover\:text-red-700:hover {
        color: #b91c1c;
    }

    .fab {
        font-family: "Font Awesome 5 Brands";
    }

    @media (min-width: 768px) {
        .md\:grid-cols-2 {
            grid-template-columns: repeat(2, minmax(0, 1fr));
        }
    }

    /* Duplication Check Styles */
    .duplication-controls {
        padding: 1rem;
        background: #f8fafc;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .bg-blue-100 { background-color: #dbeafe; }
    .text-blue-800 { color: #1e40af; }
    .bg-yellow-100 { background-color: #fef3c7; }
    .text-yellow-800 { color: #92400e; }
    .bg-green-100 { background-color: #dcfce7; }
    .text-green-500 { color: #10b981; }

    .duplicate-row:hover {
        background-color: #f9fafb;
    }

    .duplicate-checkbox {
        width: 1rem;
        height: 1rem;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 1.25rem;
        color: #6b7280;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
    }

    .close-btn:hover {
        background: #f3f4f6;
        color: #374151;
    }

    .data-table-container {
        max-height: 60vh;
        overflow-y: auto;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
    }

    .mb-4 {
        margin-bottom: 1rem;
    }


</style>
{% endblock %}

{% block content %}
{% csrf_token %}
<div class="dashboard-container">
    <!-- Operations Header -->
    

    <!-- Quick Actions Bar -->
    <div class="quick-actions">
        <button class="action-btn upload" onclick="openPopup('uploadPopup')">
            <i class="fas fa-upload"></i>
            Upload CVs
        </button>
        <button class="action-btn analyze" onclick="openPopup('analyzePopup')">
            <i class="fas fa-brain"></i>
            Analyze CVs
        </button>
        <button class="action-btn manage" onclick="openPopup('managePopup')">
            <i class="fas fa-cogs"></i>
            Manage Data
        </button>
        <button class="action-btn reports" onclick="openPopup('reportsPopup')">
            <i class="fas fa-chart-bar"></i>
            Reports
        </button>
        <button class="action-btn duplication" onclick="openDuplicationCheck()">
            <i class="fas fa-copy"></i>
            Duplication Check Table
        </button>
    </div>

    <!-- Main Content Container -->
    <div class="operations-layout">
        <!-- Main Content Area (Full Width) -->
        <div class="operations-main" style="flex: 1;">
            <!-- Combined Filter and Search Banner -->
            <div class="filter-section" style="margin-bottom: 1.5rem;">
                <div class="combined-controls-row">
                    <!-- Filters Section -->
                    <div class="controls-group">
                    
                        <div class="filter-grid">
                            <select class="filter-control" id="entityType" onchange="changeEntityType()">
                                <option value="cvs">CVs</option>
                                <option value="vacancies">Vacancies</option>
                                <option value="companies">Companies</option>
                                <option value="analyses">Analyses</option>
                            </select>
                            
                            <select class="filter-control" id="statusFilter" onchange="applyFilters()">
                                <option value="">All Statuses</option>
                                <option value="uploaded">Uploaded</option>
                                <option value="processing">Processing</option>
                                <option value="analyzed">Analyzed</option>
                                <option value="matched">Matched</option>
                            </select>
                            
                            <select class="filter-control" id="dateRange" onchange="applyFilters()">
                                <option value="all">All Time</option>
                                <option value="today">Today</option>
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                                <option value="quarter">This Quarter</option>
                            </select>
                        </div>
                    </div>

                    <!-- Search and Sort Section -->
                    <div class="controls-group">
                        <h3 class="text-lg font-semibold mb-3">
                            
                        </h3>
                        <div class="search-bar">
                            <input type="text" id="searchInput" class="search-input" placeholder="Search..." onkeyup="performSearch()">
                            
                            <select class="filter-control" id="sortBy" onchange="applySorting()">
                                <option value="newest">Newest First</option>
                                <option value="oldest">Oldest First</option>
                                <option value="name">Name A-Z</option>
                                <option value="score">Highest Score</option>
                                <option value="status">Status</option>
                            </select>
                            
                            <select class="filter-control" id="itemsPerPage" onchange="changeItemsPerPage()">
                                <option value="10">10 per page</option>
                                <option value="25">25 per page</option>
                                <option value="50">50 per page</option>
                                <option value="100">100 per page</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content List -->
    <div class="tab-container">
        <div class="tab-navigation">
            <div class="flex items-center">
                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                <span class="ml-3 font-semibold" id="listTitle">CV Management</span>
            </div>
            
            <div class="view-controls" style="margin-left: 2rem;">
                <button class="btn btn-secondary btn-sm active" onclick="toggleView('table')" id="tableViewBtn">
                    <i class="fas fa-table mr-1"></i> Table
                </button>
                <button class="btn btn-secondary btn-sm" onclick="toggleView('grid')" id="gridViewBtn">
                    <i class="fas fa-th mr-1"></i> Grid
                </button>
            </div>
        </div>
        
        <div class="tab-content">
            <!-- Table View -->
            <div id="tableView" class="table-view">
                <table class="data-table">
                    <thead id="tableHeader">
                        <!-- Headers will be populated based on entity type -->
                    </thead>
                    <tbody id="tableBody">
                        <!-- Content will be populated dynamically -->
                    </tbody>
                </table>
            </div>
            
            <!-- Grid View -->
            <div id="gridView" class="grid-view" style="display: none;">
                <div id="gridContainer" class="operations-grid p-4">
                    <!-- Grid items will be populated dynamically -->
                </div>
            </div>
        </div>
        
        <!-- Bulk Actions Bar -->
        <div id="bulkActions" class="bulk-actions">
            <div class="flex items-center justify-between">
                <span id="selectedCount">0 items selected</span>
                <div class="bulk-action-buttons">
                    <button class="btn btn-primary btn-sm" onclick="bulkAnalyze()">
                        <i class="fas fa-brain mr-1"></i> Analyze
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="bulkExport()">
                        <i class="fas fa-download mr-1"></i> Export
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="bulkDelete()">
                        <i class="fas fa-trash mr-1"></i> Delete
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Pagination -->
        <div class="pagination" id="pagination">
            <!-- Pagination will be populated dynamically -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include all popup components -->
{% include 'cv_analyzer/components/upload_popup.html' %}
{% include 'cv_analyzer/components/manage_popup.html' %}
{% include 'cv_analyzer/components/analyze_popup.html' %}
{% include 'cv_analyzer/components/reports_popup.html' %}
{% include 'cv_analyzer/components/cv_detail_popup.html' %}
{% include 'cv_analyzer/components/vacancy_popup.html' %}
{% include 'cv_analyzer/components/company_popup.html' %}

{% endblock %}

{% block extra_js %}
<script src="{% static 'cv_analyzer/js/unified_dashboard.js' %}"></script>
<script>
// Global variables
let currentEntityType = 'cvs';
let currentPage = 1;
let itemsPerPage = 25;
let selectedItems = new Set();
let allData = {};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadInitialData();
    setupUploadHandlers(); // Add upload form handlers
    // Don't setup file upload immediately - wait for popup to open
    setupPopupClickHandlers(); // Add popup click handlers
    
    // Ensure all close buttons work
    document.querySelectorAll('.popup-close, .close-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Get popup ID from onclick attribute or find parent popup
            const popupId = this.getAttribute('onclick')?.match(/closePopup\('([^']+)'\)/)?.[1];
            const parentPopup = this.closest('.popup-overlay');
            
            if (popupId) {
                closePopup(popupId);
            } else if (parentPopup) {
                closePopup(parentPopup.id);
            } else {
                closePopup();
            }
        });
    });
});

// Load initial data from the server
function loadInitialData() {
    // Initialize with CVs data
    populateWithServerData('cvs');
    updateTableHeader('cvs');
}

// Populate table with server data instead of API calls
function populateWithServerData(entityType) {
    const tbody = document.getElementById('tableBody');
    tbody.innerHTML = '';
    
    let items = [];
    
    switch(entityType) {
        case 'cvs':
            items = {{ recent_cvs_json|safe|default:"[]" }};
            break;
        case 'vacancies':
            items = {{ recent_vacancies_json|safe|default:"[]" }};
            break;
        case 'companies':
            items = {{ companies_json|safe|default:"[]" }};
            break;
        case 'analyses':
            items = {{ recent_analyses_json|safe|default:"[]" }};
            break;
    }
    
    if (items && items.length > 0) {
        items.forEach(item => {
            const row = createTableRowFromServer(item, entityType);
            tbody.appendChild(row);
        });
    } else {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-gray-500">No data available</td></tr>';
    }
}

// Create table row from server data
function createTableRowFromServer(item, entityType) {
    const row = document.createElement('tr');
    let cells = [];
    
    switch(entityType) {
        case 'cvs':
            cells = [
                `<input type="checkbox" onchange="toggleItemSelection('${item.id}')">`,
                item.candidate_name || 'Unknown',
                item.email || '--',
                `<span class="status-badge ${item.status || 'uploaded'}">${(item.status || 'uploaded').charAt(0).toUpperCase() + (item.status || 'uploaded').slice(1)}</span>`,
                `${item.overall_score || '--'}%`,
                formatDate(item.uploaded_at),
                createActionButtons(item.id, 'cv')
            ];
            break;
        case 'vacancies':
            cells = [
                `<input type="checkbox" onchange="toggleItemSelection('${item.id}')">`,
                item.title || 'Unknown',
                item.company_name || '--',
                `<span class="status-badge active">Active</span>`,
                item.applications_count || 0,
                formatDate(item.created_at),
                createActionButtons(item.id, 'vacancy')
            ];
            break;
        case 'companies':
            cells = [
                `<input type="checkbox" onchange="toggleItemSelection('${item.id}')">`,
                item.name || 'Unknown',
                item.industry || '--',
                item.location || '--',
                item.vacancy_count || 0,
                formatDate(item.created_at),
                createActionButtons(item.id, 'company')
            ];
            break;
        case 'analyses':
            cells = [
                `<input type="checkbox" onchange="toggleItemSelection('${item.id}')">`,
                item.cv_name || 'Unknown',
                item.vacancy_title || '--',
                `${item.compatibility_score || '--'}%`,
                `<span class="status-badge analyzed">Analyzed</span>`,
                formatDate(item.created_at),
                createActionButtons(item.id, 'analysis')
            ];
            break;
    }
    
    row.innerHTML = cells.map(cell => `<td>${cell}</td>`).join('');
    return row;
}

// Toggle between table and grid view
function toggleView(viewType) {
    const tableView = document.getElementById('tableView');
    const gridView = document.getElementById('gridView');
    const tableBtn = document.getElementById('tableViewBtn');
    const gridBtn = document.getElementById('gridViewBtn');
    
    if (viewType === 'table') {
        tableView.style.display = 'block';
        gridView.style.display = 'none';
        tableBtn.classList.add('active');
        gridBtn.classList.remove('active');
    } else {
        tableView.style.display = 'none';
        gridView.style.display = 'block';
        tableBtn.classList.remove('active');
        gridBtn.classList.add('active');
        populateGridView();
    }
}

// Populate grid view
function populateGridView() {
    const gridContainer = document.getElementById('gridContainer');
    gridContainer.innerHTML = '';
    
    let items = [];
    
    switch(currentEntityType) {
        case 'cvs':
            items = {{ recent_cvs_json|safe }};
            break;
        case 'vacancies':
            items = {{ recent_vacancies_json|safe }};
            break;
        case 'companies':
            items = {{ companies_json|safe }};
            break;
        case 'analyses':
            items = {{ recent_analyses_json|safe }};
            break;
    }
    
    if (items && items.length > 0) {
        items.forEach(item => {
            const card = createGridCard(item, currentEntityType);
            gridContainer.appendChild(card);
        });
    } else {
        gridContainer.innerHTML = '<div class="col-span-full text-center text-gray-500 py-8">No data available</div>';
    }
}

// Create grid card
function createGridCard(item, entityType) {
    const card = document.createElement('div');
    card.className = 'grid-card';
    
    let cardContent = '';
    
    switch(entityType) {
        case 'cvs':
            cardContent = `
                <input type="checkbox" class="grid-card-checkbox" onchange="toggleItemSelection('${item.id}')">
                <div class="grid-card-header">
                    <div>
                        <h3 class="grid-card-title">${item.candidate_name || 'Unknown'}</h3>
                        <p class="grid-card-subtitle">${item.email || 'No email'}</p>
                    </div>
                </div>
                <div class="grid-card-meta">
                    <div class="grid-card-meta-item">
                        <span class="grid-card-meta-label">Status:</span>
                        <span class="status-badge ${item.status || 'uploaded'}">${(item.status || 'uploaded').charAt(0).toUpperCase() + (item.status || 'uploaded').slice(1)}</span>
                    </div>
                    <div class="grid-card-meta-item">
                        <span class="grid-card-meta-label">Score:</span>
                        <span class="grid-card-meta-value">${item.overall_score || '--'}%</span>
                    </div>
                    <div class="grid-card-meta-item">
                        <span class="grid-card-meta-label">Date:</span>
                        <span class="grid-card-meta-value">${formatDate(item.uploaded_at)}</span>
                    </div>
                </div>
                <div class="grid-card-actions">
                    ${createActionButtons(item.id, 'cv')}
                </div>
            `;
            break;
        case 'vacancies':
            cardContent = `
                <input type="checkbox" class="grid-card-checkbox" onchange="toggleItemSelection('${item.id}')">
                <div class="grid-card-header">
                    <div>
                        <h3 class="grid-card-title">${item.title || 'Unknown'}</h3>
                        <p class="grid-card-subtitle">${item.company_name || 'No company'}</p>
                    </div>
                </div>
                <div class="grid-card-meta">
                    <div class="grid-card-meta-item">
                        <span class="grid-card-meta-label">Status:</span>
                        <span class="status-badge active">Active</span>
                    </div>
                    <div class="grid-card-meta-item">
                        <span class="grid-card-meta-label">Applications:</span>
                        <span class="grid-card-meta-value">${item.applications_count || 0}</span>
                    </div>
                    <div class="grid-card-meta-item">
                        <span class="grid-card-meta-label">Date:</span>
                        <span class="grid-card-meta-value">${formatDate(item.created_at)}</span>
                    </div>
                </div>
                <div class="grid-card-actions">
                    ${createActionButtons(item.id, 'vacancy')}
                </div>
            `;
            break;
        case 'companies':
            cardContent = `
                <input type="checkbox" class="grid-card-checkbox" onchange="toggleItemSelection('${item.id}')">
                <div class="grid-card-header">
                    <div>
                        <h3 class="grid-card-title">${item.name || 'Unknown'}</h3>
                        <p class="grid-card-subtitle">${item.industry || 'No industry'}</p>
                    </div>
                </div>
                <div class="grid-card-meta">
                    <div class="grid-card-meta-item">
                        <span class="grid-card-meta-label">Location:</span>
                        <span class="grid-card-meta-value">${item.location || '--'}</span>
                    </div>
                    <div class="grid-card-meta-item">
                        <span class="grid-card-meta-label">Vacancies:</span>
                        <span class="grid-card-meta-value">${item.vacancy_count || 0}</span>
                    </div>
                    <div class="grid-card-meta-item">
                        <span class="grid-card-meta-label">Date:</span>
                        <span class="grid-card-meta-value">${formatDate(item.created_at)}</span>
                    </div>
                </div>
                <div class="grid-card-actions">
                    ${createActionButtons(item.id, 'company')}
                </div>
            `;
            break;
        case 'analyses':
            cardContent = `
                <input type="checkbox" class="grid-card-checkbox" onchange="toggleItemSelection('${item.id}')">
                <div class="grid-card-header">
                    <div>
                        <h3 class="grid-card-title">${item.cv_name || 'Unknown'}</h3>
                        <p class="grid-card-subtitle">${item.vacancy_title || 'No vacancy'}</p>
                    </div>
                </div>
                <div class="grid-card-meta">
                    <div class="grid-card-meta-item">
                        <span class="grid-card-meta-label">Score:</span>
                        <span class="grid-card-meta-value">${item.compatibility_score || '--'}%</span>
                    </div>
                    <div class="grid-card-meta-item">
                        <span class="grid-card-meta-label">Status:</span>
                        <span class="status-badge analyzed">Analyzed</span>
                    </div>
                    <div class="grid-card-meta-item">
                        <span class="grid-card-meta-label">Date:</span>
                        <span class="grid-card-meta-value">${formatDate(item.created_at)}</span>
                    </div>
                </div>
                <div class="grid-card-actions">
                    ${createActionButtons(item.id, 'analysis')}
                </div>
            `;
            break;
    }
    
    card.innerHTML = cardContent;
    return card;
}

// Change entity type
function changeEntityType() {
    const entityType = document.getElementById('entityType').value;
    currentEntityType = entityType;
    currentPage = 1;
    selectedItems.clear();
    updateTableHeader(entityType);
    populateWithServerData(entityType);
    updateItemCount();
    
    // Update title
    const titleMap = {
        'cvs': 'CV Management',
        'vacancies': 'Vacancy Management',
        'companies': 'Company Management',
        'analyses': 'Analysis Management'
    };
    document.getElementById('listTitle').textContent = titleMap[entityType];
    
    // If grid view is active, update it too
    if (document.getElementById('gridView').style.display !== 'none') {
        populateGridView();
    }
}

// Update item count based on entity type
function updateItemCount() {
    const totalItems = {
        'cvs': {{ total_cvs|default:0 }},
        'vacancies': {{ total_vacancies|default:0 }},
        'companies': {{ total_companies|default:0 }},
        'analyses': {{ total_analyses|default:0 }}
    };
    const count = totalItems[currentEntityType] || 0;
    document.getElementById('itemCount').textContent = `(${count} items)`;
}

// BULLETPROOF Popup Management Functions
function openPopup(popupId) {
    console.log('Opening popup:', popupId);
    const popup = document.getElementById(popupId);
    
    if (popup) {
        // Remove any existing show classes from other popups
        document.querySelectorAll('.popup-overlay.show').forEach(p => {
            p.classList.remove('show');
        });
        
        // Force position the popup
        popup.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: rgba(0, 0, 0, 0.1) !important;
            visibility: visible !important;
            opacity: 1 !important;
            pointer-events: auto !important;
        `;
        
        // Force style the popup content
        const content = popup.querySelector('.popup-content');
        if (content) {
            content.style.cssText = `
                background: white !important;
                border: 2px solid #007bff !important;
                border-radius: 8px !important;
                padding: 30px !important;
                width: 90% !important;
                max-width: 800px !important;
                max-height: 90vh !important;
                overflow-y: auto !important;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
                z-index: 1000000 !important;
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: relative !important;
            `;
            
            // Ensure close button exists and is functional
            let closeBtn = content.querySelector('.popup-close, .close-btn');
            if (!closeBtn) {
                // Create close button if it doesn't exist
                closeBtn = document.createElement('button');
                closeBtn.className = 'popup-close';
                closeBtn.innerHTML = '<i class="fas fa-times"></i>';
                closeBtn.setAttribute('onclick', `closePopup('${popupId}')`);
                content.appendChild(closeBtn);
            }
            
            // Ensure close button is positioned correctly
            if (closeBtn) {
                closeBtn.style.cssText = `
                    position: absolute !important;
                    top: 15px !important;
                    right: 15px !important;
                    background: #f8f9fa !important;
                    border: 1px solid #dee2e6 !important;
                    border-radius: 50% !important;
                    width: 35px !important;
                    height: 35px !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    cursor: pointer !important;
                    z-index: 1000001 !important;
                    color: #6c757d !important;
                    font-size: 16px !important;
                `;
            }
        }
        
        popup.classList.add('show');
        
        // Move to end of body to ensure it's on top
        document.body.appendChild(popup);
        
        console.log('Popup opened and styled:', popupId);
        
        // Remove any page filters/blur
        document.body.style.filter = 'none';
        document.body.style.backdropFilter = 'none';
        
        // Initialize upload functionality if this is the upload popup
        if (popupId === 'uploadPopup') {
            setTimeout(() => {
                console.log('Initializing upload popup functionality...');
                setupSingleUpload();
                setupBatchUpload();
                
                // Also setup tab switching
                document.querySelectorAll('.upload-methods .tab-btn').forEach(btn => {
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        const method = this.textContent.toLowerCase().includes('single') ? 'single' : 
                                     this.textContent.toLowerCase().includes('batch') ? 'batch' : 'integrations';
                        switchUploadMethod(method, this);
                    });
                });
            }, 200);
        }
        
    } else {
        console.error('Popup element not found:', popupId);
    }
}

// ENHANCED Close Popup Function
function closePopup(popupId) {
    console.log('Closing popup:', popupId);
    
    let popup;
    
    // If popupId is provided, find specific popup
    if (popupId) {
        popup = document.getElementById(popupId);
    } else {
        // Find any open popup
        popup = document.querySelector('.popup-overlay.show');
    }
    
    if (popup) {
        popup.classList.remove('show');
        
        // Also hide with inline styles as backup
        popup.style.display = 'none';
        popup.style.visibility = 'hidden';
        
        console.log('Popup closed:', popupId || 'auto-detected');
    } else {
        // Fallback: close any visible popup overlays
        document.querySelectorAll('.popup-overlay').forEach(p => {
            p.classList.remove('show');
            p.style.display = 'none';
            p.style.visibility = 'hidden';
        });
        
        // Close any fallback popups
        const fallbackPopup = document.getElementById('fallback-popup');
        if (fallbackPopup) {
            fallbackPopup.remove();
        }
        
        console.log('Force closed all popups');
    }
    
    // Remove any page filters
    document.body.style.filter = 'none';
    document.body.style.backdropFilter = 'none';
}

// Universal close function for any popup
function closeAllPopups() {
    closePopup();
}

// Close popup when clicking outside content
function setupPopupClickHandlers() {
    document.addEventListener('click', function(event) {
        // If clicking on popup overlay (not content), close popup
        if (event.target.classList.contains('popup-overlay')) {
            closePopup();
        }
    });
    
    // Close popup on Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closePopup();
        }
    });
}

// Close popup when clicking outside
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('popup-overlay')) {
        event.target.classList.remove('show');
    }
});

// View functions
function viewCV(cvId) {
    currentCVId = cvId; // Store the current CV ID for action buttons
    
    // Load CV detail JSON data and populate the template
    fetch(`/cv/${cvId}/detail/json/`)
        .then(response => response.json())
        .then(data => {
            // Check if this is an error response (has success: false)
            if (data.success === false) {
                console.error('Error loading CV data:', data.error);
                alert('Failed to load CV details');
            } else {
                // Success response - data contains CV information directly
                populateCVDetailPopup(data);
                openPopup('cvDetailPopup');
            }
        })
        .catch(error => {
            console.error('Error loading CV data:', error);
            console.error('Error details:', error.message, error.stack);
            alert('Failed to get CV details: ' + (error.message || 'Unknown error'));
        });
}

function populateCVDetailPopup(cvData) {
    console.log('CV Data received:', cvData); // Debug logging
    
    // Show the template content
    const template = document.getElementById('cvDetailTemplate');
    const detailContent = document.getElementById('cvDetailContent');
    
    if (!template) {
        console.error('CV Detail Template not found');
        return;
    }
    if (!detailContent) {
        console.error('CV Detail Content container not found');
        return;
    }
    
    try {
        // Clone the template content
        const content = template.content.cloneNode(true);
        
        // Extract CV and analysis data (backend returns fields directly at top level)
        const cv = cvData; // Data is already at top level
        
        // Helper function to set field value (finds ALL elements with the data-field)
        function setField(selector, value) {
            const elements = content.querySelectorAll(`[data-field="${selector}"]`);
            elements.forEach(element => {
                // Handle "not specified" and empty values
                if (!value || value === 'not specified' || value === '') {
                    element.textContent = '--';
                    element.classList.add('text-gray-500');
                } else {
                    element.textContent = value;
                    element.classList.remove('text-gray-500');
                }
            });
        }
        
        // Populate header and overall score
        setField('overall_score', cv.overall_score);
        
        // Populate individual scores
        setField('content_score', cv.content_score);
        setField('format_score', cv.format_score);
        setField('sections_score', cv.sections_score);
        setField('skills_score', cv.skills_score);
        setField('style_score', cv.style_score);
        
        // Populate personal info (backend returns these directly)
        setField('candidate_name', cv.candidate_name);
        setField('email', cv.email);
        setField('phone', cv.phone);
        setField('location', cv.location);
        setField('years_of_experience', cv.years_of_experience);
        setField('education_level', cv.education_level);
        
        // Populate job preferences
        setField('preferred_job_type', cv.preferred_job_type);
        setField('preferred_work_location', cv.preferred_work_location);
        setField('salary_expectation', cv.salary_expectation);
        setField('availability', cv.availability);
        
        // Populate professional links
        populateProfessionalLinks(content, cv);
        
        // Populate CV status info
        populateCVStatus(content, cv);
        
        // Populate skills and languages if available
        if (cv.skills_list) {
            const skillsContainer = content.querySelector('[data-field="skills_list"]');
            if (skillsContainer) {
                if (Array.isArray(cv.skills_list) && cv.skills_list.length > 0) {
                    skillsContainer.innerHTML = cv.skills_list.map(skill => 
                        `<span class="skill-tag">${skill}</span>`
                    ).join('');
                } else {
                    skillsContainer.innerHTML = '<span class="text-gray-500">None specified</span>';
                }
            }
        }
        
        if (cv.languages_list) {
            const languagesContainer = content.querySelector('[data-field="languages_list"]');
            if (languagesContainer) {
                if (Array.isArray(cv.languages_list) && cv.languages_list.length > 0) {
                    languagesContainer.innerHTML = cv.languages_list.map(lang => 
                        `<span class="language-tag">${lang}</span>`
                    ).join('');
                } else {
                    languagesContainer.innerHTML = '<span class="text-gray-500">None specified</span>';
                }
            }
        }
        
        // Populate detailed analysis sections (backend returns these directly)
        setField('content_analysis', cv.content_analysis || 'CV has not been analyzed yet. Please run analysis to see detailed results.');
        setField('format_analysis', cv.format_analysis || 'CV has not been analyzed yet. Please run analysis to see detailed results.');
        setField('sections_analysis', cv.sections_analysis || 'CV has not been analyzed yet. Please run analysis to see detailed results.');
        setField('skills_analysis', cv.skills_analysis || 'CV has not been analyzed yet. Please run analysis to see detailed results.');
        setField('style_analysis', cv.style_analysis || 'CV has not been analyzed yet. Please run analysis to see detailed results.');
        
        // Populate AI analysis details
        setField('ai_provider', cv.ai_provider || 'Not specified');
        setField('ai_model', cv.ai_model || 'Not specified');
        setField('processing_time', cv.processing_time || 'Not specified');
        setField('ai_response', cv.ai_response || 'Raw AI response not available');
        
        // Clear and append the new content
        detailContent.innerHTML = '';
        detailContent.appendChild(content);
        
        console.log('CV Detail popup populated successfully');
        
    } catch (error) {
        console.error('Error populating CV detail popup:', error);
        console.error('Error details:', error.message, error.stack);
        throw error; // Re-throw to be caught by the fetch error handler
    }
}

// Helper function to format analysis details from JSON
function formatAnalysisDetails(detailsJson) {
    if (!detailsJson) return null;
    
    // If it's already a string, return it
    if (typeof detailsJson === 'string') {
        return detailsJson;
    }
    
    // If it's an object, format it nicely
    if (typeof detailsJson === 'object') {
        if (Object.keys(detailsJson).length === 0) {
            return null; // Empty object
        }
        
        // Convert object to readable text
        let formatted = '';
        for (const [key, value] of Object.entries(detailsJson)) {
            if (value) {
                formatted += `${key.replace(/_/g, ' ').toUpperCase()}: ${value}\n`;
            }
        }
        return formatted || null;
    }
    
    return null;
}

function populateProfessionalLinks(content, cv) {
    if (!cv) return;
    
    // Helper function to ensure URL has protocol
    function ensureProtocol(url) {
        if (!url || url === 'not specified') return '';
        if (url.startsWith('http://') || url.startsWith('https://')) {
            return url;
        }
        return 'https://' + url;
    }
    
    // LinkedIn profile
    const linkedinEl = content.querySelector('[data-field="linkedin_profile"] .linkedin-link');
    if (linkedinEl) {
        if (cv.linkedin_profile && cv.linkedin_profile !== 'not specified') {
            const linkedinUrl = ensureProtocol(cv.linkedin_profile);
            linkedinEl.innerHTML = `<a href="${linkedinUrl}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                ${cv.linkedin_profile}
            </a>`;
        } else {
            linkedinEl.textContent = '--';
            linkedinEl.className = 'text-gray-500 text-sm';
        }
    }
    
    // Website/Portfolio
    const websiteEl = content.querySelector('[data-field="website_portfolio"] .website-link');
    if (websiteEl) {
        if (cv.website_portfolio && cv.website_portfolio !== 'not specified') {
            const websiteUrl = ensureProtocol(cv.website_portfolio);
            websiteEl.innerHTML = `<a href="${websiteUrl}" target="_blank" class="text-green-600 hover:text-green-800 text-sm">
                ${cv.website_portfolio}
            </a>`;
        } else {
            websiteEl.textContent = '--';
            websiteEl.className = 'text-gray-500 text-sm';
        }
    }
    
    // GitHub profile
    const githubEl = content.querySelector('[data-field="github_profile"] .github-link');
    if (githubEl) {
        if (cv.github_profile && cv.github_profile !== 'not specified') {
            const githubUrl = ensureProtocol(cv.github_profile);
            githubEl.innerHTML = `<a href="${githubUrl}" target="_blank" class="text-gray-800 hover:text-gray-600 text-sm">
                ${cv.github_profile}
            </a>`;
        } else {
            githubEl.textContent = '--';
            githubEl.className = 'text-gray-500 text-sm';
        }
    }
    
    // Other links
    const otherLinksEl = content.querySelector('[data-field="other_links"] .other-links');
    if (otherLinksEl) {
        if (cv.other_links && Array.isArray(cv.other_links) && cv.other_links.length > 0) {
            const linksHtml = cv.other_links.map(link => {
                const cleanUrl = ensureProtocol(link);
                return `<div class="mb-1">
                    <a href="${cleanUrl}" target="_blank" class="text-purple-600 hover:text-purple-800 text-sm">
                        ${link}
                    </a>
                </div>`;
            }).join('');
            otherLinksEl.innerHTML = linksHtml;
        } else {
            otherLinksEl.textContent = '--';
            otherLinksEl.className = 'text-gray-500 text-sm';
        }
    }
}

function populateCVStatus(content, cvData) {
    // Helper function to set field value
    function setStatusField(selector, value) {
        const element = content.querySelector(`[data-field="${selector}"]`);
        if (element) {
            element.textContent = value || '--';
        }
    }
    
    // Populate CV status fields
    const statusEl = content.querySelector('[data-field="status"]');
    if (statusEl) {
        const status = cvData.status || 'uploaded';
        statusEl.innerHTML = `<span class="px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(status)}">${status}</span>`;
    }
    
    // Format and populate dates
    const uploadedAtEl = content.querySelector('[data-field="uploaded_at"]');
    if (uploadedAtEl) {
        if (cvData.uploaded_at) {
            const date = new Date(cvData.uploaded_at);
            uploadedAtEl.textContent = date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        } else {
            uploadedAtEl.textContent = '--';
        }
    }
    
    // Analyzed date (if CV has been analyzed)
    setStatusField('analyzed_at', cvData.analysis ? 'Analyzed' : '--');
    
    // CV source and category
    setStatusField('source', cvData.source);
    setStatusField('category', cvData.category);
}

function getStatusBadgeClass(status) {
    const statusClasses = {
        'uploaded': 'bg-blue-100 text-blue-800',
        'processing': 'bg-yellow-100 text-yellow-800',
        'analyzed': 'bg-green-100 text-green-800',
        'matched': 'bg-purple-100 text-purple-800',
        'rejected': 'bg-red-100 text-red-800',
        'archived': 'bg-gray-100 text-gray-800'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
}

function viewVacancy(vacancyId) {
    console.log('Viewing vacancy:', vacancyId);
    openPopup('vacancyPopup');
}

function viewCompany(companyId) {
    console.log('Viewing company:', companyId);
    openPopup('companyPopup');
}

function viewAnalysis(analysisId) {
    console.log('Viewing analysis:', analysisId);
    // You can add specific analysis viewing logic here
}

// Duplicate functions removed - using the first ones

// Bulk actions
function bulkAnalyze() {
    if (selectedItems.size === 0) {
        alert('Please select items to analyze');
        return;
    }
    
    if (confirm(`Analyze ${selectedItems.size} selected items?`)) {
        console.log('Bulk analyzing:', Array.from(selectedItems));
        // Add your bulk analyze logic here
    }
}

function bulkExport() {
    if (selectedItems.size === 0) {
        alert('Please select items to export');
        return;
    }
    
    console.log('Bulk exporting:', Array.from(selectedItems));
    // Add your bulk export logic here
}

function bulkDelete() {
    if (selectedItems.size === 0) {
        alert('Please select items to delete');
        return;
    }
    
    if (confirm(`Delete ${selectedItems.size} selected items? This action cannot be undone.`)) {
        console.log('Bulk deleting:', Array.from(selectedItems));
        // Add your bulk delete logic here
    }
}

// Filter and search functions  
function applyFilters() {
    const statusFilter = document.getElementById('statusFilter').value;
    const dateRange = document.getElementById('dateRange').value;
    
    console.log('Applying filters:', { statusFilter, dateRange });
    // Add actual filtering logic here
    populateWithServerData(currentEntityType);
}

function performSearch() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    console.log('Searching for:', searchTerm);
    // Add actual search logic here
}

function applySorting() {
    const sortBy = document.getElementById('sortBy').value;
    console.log('Sorting by:', sortBy);
    // Add actual sorting logic here
    populateWithServerData(currentEntityType);
}

function changeItemsPerPage() {
    const newItemsPerPage = document.getElementById('itemsPerPage').value;
    itemsPerPage = parseInt(newItemsPerPage);
    console.log('Items per page changed to:', itemsPerPage);
    populateWithServerData(currentEntityType);
}

function refreshData() {
    console.log('Refreshing data for:', currentEntityType);
    populateWithServerData(currentEntityType);
    selectedItems.clear();
    updateBulkActions();
}

// Update table header based on entity type
function updateTableHeader(entityType) {
    const header = document.getElementById('tableHeader');
    const title = document.getElementById('listTitle');
    
    let headers = [];
    
    switch(entityType) {
        case 'cvs':
            headers = ['', 'Name', 'Email', 'Status', 'Score', 'Date', 'Actions'];
            title.textContent = 'CV Management';
            break;
        case 'vacancies':
            headers = ['', 'Title', 'Company', 'Status', 'Applications', 'Date', 'Actions'];
            title.textContent = 'Vacancy Management';
            break;
        case 'companies':
            headers = ['', 'Name', 'Industry', 'Location', 'Vacancies', 'Date', 'Actions'];
            title.textContent = 'Company Management';
            break;
        case 'analyses':
            headers = ['', 'CV', 'Vacancy', 'Score', 'Status', 'Date', 'Actions'];
            title.textContent = 'Analysis Results';
            break;
    }
    
    header.innerHTML = `<tr>${headers.map(h => `<th>${h}</th>`).join('')}</tr>`;
}

// Selection management
function toggleItemSelection(itemId) {
    if (selectedItems.has(itemId)) {
        selectedItems.delete(itemId);
    } else {
        selectedItems.add(itemId);
    }
    updateBulkActions();
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        const itemId = checkbox.getAttribute('onchange').match(/'([^']+)'/)[1];
        if (selectAll.checked) {
            selectedItems.add(itemId);
        } else {
            selectedItems.delete(itemId);
        }
    });
    
    updateBulkActions();
}

function updateBulkActions() {
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');
    
    if (selectedItems.size > 0) {
        bulkActions.classList.add('show');
        selectedCount.textContent = `${selectedItems.size} items selected`;
    } else {
        bulkActions.classList.remove('show');
    }
}

// Utility functions
function formatDate(dateString) {
    if (!dateString) return '--';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function showLoading() {
    const tbody = document.getElementById('tableBody');
    if (tbody) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center loading"><div class="spinner"></div>Loading...</td></tr>';
    }
}

function hideLoading() {
    // Loading will be replaced by actual content
}

function showError(message) {
    const tbody = document.getElementById('tableBody');
    if (tbody) {
        tbody.innerHTML = `<tr><td colspan="7" class="text-center text-red-500">${message}</td></tr>`;
    }
}

function updatePagination(total, page, pages) {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;
    
    let paginationHTML = '';
    
    // Previous button
    if (page > 1) {
        paginationHTML += `<button class="pagination-btn" onclick="changePage(${page - 1})">Previous</button>`;
    }
    
    // Page numbers
    for (let i = Math.max(1, page - 2); i <= Math.min(pages, page + 2); i++) {
        const activeClass = i === page ? 'active' : '';
        paginationHTML += `<button class="pagination-btn ${activeClass}" onclick="changePage(${i})">${i}</button>`;
    }
    
    // Next button
    if (page < pages) {
        paginationHTML += `<button class="pagination-btn" onclick="changePage(${page + 1})">Next</button>`;
    }
    
    pagination.innerHTML = paginationHTML;
}

function changePage(page) {
    currentPage = page;
    populateWithServerData(currentEntityType);
}

// Create action buttons
function createActionButtons(itemId, itemType) {
    return `
        <div class="action-btn-group">
            <button class="action-btn-mini view" onclick="viewItem('${itemId}', '${itemType}')" title="View">
                <i class="fas fa-eye"></i>
            </button>
            <button class="action-btn-mini edit" onclick="editItem('${itemId}', '${itemType}')" title="Edit">
                <i class="fas fa-edit"></i>
            </button>
            ${itemType === 'cv' ? `
            <button class="action-btn-mini analyze" onclick="analyzeItem('${itemId}')" title="Analyze">
                <i class="fas fa-brain"></i>
            </button>
            ` : ''}
            <button class="action-btn-mini delete" onclick="deleteItem('${itemId}', '${itemType}')" title="Delete">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
}

// Item actions
function viewItem(itemId, itemType) {
    switch(itemType) {
        case 'cv':
            viewCV(itemId);
            break;
        case 'vacancy':
            viewVacancy(itemId);
            break;
        case 'company':
            viewCompany(itemId);
            break;
        case 'analysis':
            viewAnalysis(itemId);
            break;
    }
}

function editItem(itemId, itemType) {
    switch(itemType) {
        case 'cv':
            openPopup('managePopup');
            break;
        case 'vacancy':
            openPopup('vacancyPopup');
            break;
        case 'company':
            openPopup('companyPopup');
            break;
        default:
            console.log(`Editing ${itemType} ${itemId}`);
    }
}

function deleteItem(itemId, itemType) {
    if (confirm(`Are you sure you want to delete this ${itemType}?`)) {
        console.log(`Deleting ${itemType} ${itemId}`);
        // Add actual delete logic here
    }
}

function analyzeItem(itemId) {
    console.log(`Analyzing CV ${itemId}`);
    
    // Open the analyze popup
    openPopup('analyzePopup');
    
    // Auto-select single CV analysis and highlight it
    setTimeout(() => {
        // Select single analysis type
        selectAnalysisType('single');
        
        // Manually highlight the single analysis option card
        document.querySelectorAll('.option-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        const singleCard = document.querySelector('.option-card[onclick*="single"]');
        if (singleCard) {
            singleCard.classList.add('selected');
            singleCard.style.borderColor = '#3b82f6';
            singleCard.style.backgroundColor = '#eff6ff';
        }
        
        // Pre-select the CV in the dropdown
        const cvSelect = document.getElementById('singleCVSelect');
        if (cvSelect) {
            cvSelect.value = itemId;
            // Trigger change event if needed
            cvSelect.dispatchEvent(new Event('change'));
            
            // Show the CV name in console for debugging
            const selectedOption = cvSelect.options[cvSelect.selectedIndex];
            if (selectedOption) {
                console.log('Auto-selected CV:', selectedOption.text);
            }
        }
    }, 150); // Small delay to ensure the popup and dropdown are fully loaded
}

// Upload Method Functions
function switchUploadMethod(method, buttonElement) {
    // Hide all upload methods
    document.querySelectorAll('.upload-method').forEach(el => {
        el.style.display = 'none';
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.upload-methods .tab-btn').forEach(el => {
        el.classList.remove('active');
    });
    
    // Show selected method
    const selectedMethod = document.getElementById(`${method}-upload`);
    if (selectedMethod) {
        selectedMethod.style.display = 'block';
    }
    
    // Add active class to clicked tab
    if (buttonElement) {
        buttonElement.classList.add('active');
    } else if (event && event.target) {
    event.target.classList.add('active');
}

    // Setup file upload for the selected method
    setTimeout(() => {
        if (method === 'single') {
            setupSingleUpload();
        } else if (method === 'batch') {
            setupBatchUpload();
        }
    }, 50);
}

// File Upload Handling - Fixed to prevent multiple event listeners
function setupFileUpload() {
    // Remove any existing event listeners first
    document.querySelectorAll('.dropzone').forEach(zone => {
        zone.replaceWith(zone.cloneNode(true));
    });
    
    // Single upload - setup once popup is opened
    setupSingleUpload();
    
    // Batch upload - setup once popup is opened
    setupBatchUpload();
}

function setupSingleUpload() {
    const dropzone = document.getElementById('singleDropzone');
    if (!dropzone) {
        console.log('Single dropzone not found');
        return;
    }
    
    const fileInput = dropzone.querySelector('input[type="file"]');
    if (!fileInput) {
        console.log('Single file input not found');
        return;
    }
    
    console.log('Setting up single upload...', dropzone, fileInput);
    
    // Remove existing click listeners by cloning (but keep the structure)
    const clone = dropzone.cloneNode(true);
    dropzone.parentNode.replaceChild(clone, dropzone);
    
    // Get fresh references
    const newDropzone = document.getElementById('singleDropzone');
    const newFileInput = newDropzone.querySelector('input[type="file"]');
    
    // Simple click handler - just open file browser
    newDropzone.addEventListener('click', function(e) {
        console.log('Dropzone clicked', e.target);
        
        // Don't interfere if clicking directly on file input
        if (e.target === newFileInput) {
            return;
        }
        
        // Prevent default and open file browser
        e.preventDefault();
        e.stopPropagation();
        
        console.log('Opening file browser...');
        newFileInput.click();
    });
    
    // File selection handler
    newFileInput.addEventListener('change', function(e) {
        console.log('File selected:', e.target.files);
        if (e.target.files && e.target.files[0]) {
        handleFileSelect(e.target.files[0], 'single');
        }
    });
    
    // Drag and drop handlers
    newDropzone.addEventListener('dragover', function(e) {
        e.preventDefault();
        newDropzone.classList.add('dragover');
    });
    
    newDropzone.addEventListener('dragleave', function(e) {
        if (!newDropzone.contains(e.relatedTarget)) {
            newDropzone.classList.remove('dragover');
        }
    });
    
    newDropzone.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        newDropzone.classList.remove('dragover');
        
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
        handleFileSelect(e.dataTransfer.files[0], 'single');
        }
    });
    
    console.log('Single upload setup complete');
}

function setupBatchUpload() {
    const dropzone = document.getElementById('batchDropzone');
    if (!dropzone) {
        console.log('Batch dropzone not found');
        return;
    }
    
    const fileInput = dropzone.querySelector('input[type="file"]');
    if (!fileInput) {
        console.log('Batch file input not found');
        return;
    }
    
    console.log('Setting up batch upload...', dropzone, fileInput);
    
    // Remove existing click listeners by cloning (but keep the structure)
    const clone = dropzone.cloneNode(true);
    dropzone.parentNode.replaceChild(clone, dropzone);
    
    // Get fresh references
    const newDropzone = document.getElementById('batchDropzone');
    const newFileInput = newDropzone.querySelector('input[type="file"]');
    
    // Simple click handler - just open file browser
    newDropzone.addEventListener('click', function(e) {
        console.log('Batch dropzone clicked', e.target);
        
        // Don't interfere if clicking directly on file input
        if (e.target === newFileInput) {
            return;
        }
        
        // Prevent default and open file browser
        e.preventDefault();
        e.stopPropagation();
        
        console.log('Opening file browser for batch...');
        newFileInput.click();
    });
    
    // File selection handler
    newFileInput.addEventListener('change', function(e) {
        console.log('Files selected:', e.target.files);
        if (e.target.files && e.target.files.length > 0) {
        handleMultipleFileSelect(e.target.files);
        }
    });
    
    // Drag and drop handlers
    newDropzone.addEventListener('dragover', function(e) {
        e.preventDefault();
        newDropzone.classList.add('dragover');
    });
    
    newDropzone.addEventListener('dragleave', function(e) {
        if (!newDropzone.contains(e.relatedTarget)) {
            newDropzone.classList.remove('dragover');
        }
    });
    
    newDropzone.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        newDropzone.classList.remove('dragover');
        
        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        handleMultipleFileSelect(e.dataTransfer.files);
        }
    });
    
    console.log('Batch upload setup complete');
}

function handleFileSelect(file, type) {
    if (!file) return;
    
    // Validate file type by extension (more reliable than MIME type)
    const fileName = file.name.toLowerCase();
    const allowedExtensions = ['.pdf', '.doc', '.docx'];
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
    
    if (!hasValidExtension) {
        showNotification('Please select a PDF, DOC, or DOCX file.', 'error');
        return;
    }
    
    // Also check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
        showNotification('File too large. Maximum size is 10MB.', 'error');
        return;
    }
    
    // Show file info
    const dropzone = document.getElementById(`${type}Dropzone`);
    const fileInfo = dropzone.querySelector('.file-info');
    const fileNameElement = dropzone.querySelector('.file-name');
    
    fileNameElement.textContent = file.name;
    fileInfo.style.display = 'block';
    
    // Enable submit button
    const form = dropzone.closest('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    submitBtn.disabled = false;
}

function handleMultipleFileSelect(files) {
    if (!files || files.length === 0) return;
    
    const filesList = document.querySelector('.files-list');
    const filesInfo = document.querySelector('.files-info');
    
    filesList.innerHTML = '';
    
    // Validate all files first
    const allowedExtensions = ['.pdf', '.doc', '.docx'];
    let totalSize = 0;
    
    for (let file of files) {
        const fileNameLower = file.name.toLowerCase();
        const hasValidExtension = allowedExtensions.some(ext => fileNameLower.endsWith(ext));
        
        if (!hasValidExtension) {
            showNotification(`Invalid file type: ${file.name}. Please select PDF, DOC, or DOCX files only.`, 'error');
            return;
        }
        
        totalSize += file.size;
    }
    
    // Check total size (50MB limit for batch)
    if (totalSize > 50 * 1024 * 1024) {
        showNotification('Total file size exceeds 50MB limit.', 'error');
        return;
    }
    
    Array.from(files).forEach((file, index) => {
        if (index < 10) { // Limit to 10 files
            const fileDiv = document.createElement('div');
            fileDiv.className = 'flex items-center justify-between mb-2';
            fileDiv.innerHTML = `
                <span class="font-medium">${file.name}</span>
                <button type="button" class="text-red-500 hover:text-red-700" onclick="removeFile(${index})">
                    <i class="fas fa-times"></i>
                </button>
            `;
            filesList.appendChild(fileDiv);
        }
    });
    
    filesInfo.style.display = 'block';
    
    // Enable submit button
    const form = document.getElementById('batchUploadForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    submitBtn.disabled = false;
}

function clearFile() {
    const singleDropzone = document.getElementById('singleDropzone');
    const fileInfo = singleDropzone.querySelector('.file-info');
    const fileInput = singleDropzone.querySelector('input[type="file"]');
    
    fileInfo.style.display = 'none';
    fileInput.value = '';
    
    // Disable submit button
    const form = document.getElementById('singleUploadForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
}

function clearAllFiles() {
    const batchDropzone = document.getElementById('batchDropzone');
    const filesInfo = batchDropzone.querySelector('.files-info');
    const fileInput = batchDropzone.querySelector('input[type="file"]');
    
    filesInfo.style.display = 'none';
    fileInput.value = '';
    
    // Disable submit button
    const form = document.getElementById('batchUploadForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
}

// Integration Functions
function connectGoogleDrive() {
    alert('Google Drive integration coming soon!');
}

function connectOneDrive() {
    alert('OneDrive integration coming soon!');
}

function setupEmailIntegration() {
    alert('Email integration coming soon!');
}

function setupFolderMonitoring() {
    alert('Folder monitoring coming soon!');
}

// Additional functions for operations hub
function analyzeCV(cvId) {
    if (cvId) {
        console.log(`Analyzing CV ${cvId}`);
        fetch(`/cv/${cvId}/analyze/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log('Analysis response:', data);
            if (data.success) {
                showNotification('Analysis started successfully', 'success');
                setTimeout(() => location.reload(), 2000);
            } else {
                // Show the actual error message from the server
                const errorMessage = data.message || data.error || 'Unknown error occurred';
                console.error('Analysis failed:', errorMessage);
                showNotification(errorMessage, 'error');
            }
        })
        .catch(error => {
            console.error('Analysis request failed:', error);
            showNotification('Network error: Unable to analyze CV. Please check your connection and try again.', 'error');
        });
    } else {
        openPopup('analyzePopup');
    }
}

function matchJobs(cvId) {
    if (cvId) {
        window.location.href = `/cv/${cvId}/match/`;
    } else {
        showNotification('Please select a CV first', 'info');
    }
}

function downloadCV() {
    if (!currentCVId) {
        showNotification('No CV selected for download', 'error');
        return;
    }
    
    showNotification('Downloading CV...', 'info');
    
    // Create a temporary link to download the file
    const link = document.createElement('a');
    link.href = `/api/cv/${currentCVId}/download/`;
    link.download = ''; // Browser will use the filename from the server
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('CV download started', 'success');
}

function shareCV(cvId) {
    if (cvId) {
        const shareUrl = window.location.origin + `/cv/${cvId}/detail/`;
        if (navigator.share) {
            navigator.share({
                title: 'CV Details',
                text: 'Check out this CV',
                url: shareUrl
            });
        } else {
            navigator.clipboard.writeText(shareUrl);
            showNotification('Link copied to clipboard', 'success');
        }
    } else {
        showNotification('Please select a CV first', 'info');
    }
}

function exportCV(cvId) {
    showNotification('Export feature coming soon', 'info');
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        type === 'info' ? 'bg-blue-500' : 'bg-gray-500'
    }`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Setup upload form handlers
function setupUploadHandlers() {
    // Single upload form handler
    const singleForm = document.getElementById('singleUploadForm');
    if (singleForm) {
        singleForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleSingleUpload(this);
        });
    }
    
    // Batch upload form handler
    const batchForm = document.getElementById('batchUploadForm');
    if (batchForm) {
        batchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleBatchUpload(this);
        });
    }
}

// Validate file before upload
function validateFile(file) {
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!allowedTypes.includes(file.type)) {
        return 'Please upload only PDF, DOC, or DOCX files.';
    }
    
    if (file.size > maxSize) {
        return 'File size must be less than 10MB.';
    }
    
    return null; // No error
}

// Handle single CV upload
function handleSingleUpload(form) {
    const formData = new FormData(form);
    const fileInput = form.querySelector('input[type="file"]');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    if (!fileInput.files || fileInput.files.length === 0) {
        showNotification('Please select a file to upload', 'error');
        return;
    }
    
    // Validate file before upload
    const file = fileInput.files[0];
    const validationError = validateFile(file);
    if (validationError) {
        showNotification(validationError, 'error');
        return;
    }
    
    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...';
    
    // Show progress notification for longer uploads
    const progressTimeout = setTimeout(() => {
        showNotification('Upload is taking longer than expected. Please wait...', 'info', 8000);
    }, 5000);
    
    // Submit to upload endpoint with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout
    
    fetch('/upload/local/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        signal: controller.signal
    })
    .then(response => {
        // Check if response is ok
        if (!response.ok) {
            // Handle different types of errors
            if (response.status === 500) {
                throw new Error('Server error occurred. Please try again later or contact support.');
            } else if (response.status === 413) {
                throw new Error('File is too large. Please upload a smaller file.');
            } else if (response.status === 400) {
                // Try to get the error message from response
                return response.json().then(data => {
                    throw new Error(data.error || 'Invalid file or request. Please check your file and try again.');
                });
            } else if (response.status === 403) {
                throw new Error('You do not have permission to upload files.');
            } else if (response.status === 401) {
                throw new Error('Please log in again to upload files.');
            } else {
                throw new Error(`Upload failed with error ${response.status}. Please try again.`);
            }
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'CV uploaded successfully!', 'success');
            closePopup('uploadPopup');
            // Reload the page or refresh the CV list
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showNotification(data.error || 'Upload failed. Please try again.', 'error');
        }
    })
    .catch(error => {
        console.error('Upload error:', error);
        // Show user-friendly error message
        let errorMessage = 'Upload failed. ';
        
        if (error.name === 'AbortError') {
            errorMessage += 'Upload timed out. Please try with a smaller file or check your connection.';
        } else if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
            errorMessage += 'Please check your internet connection and try again.';
        } else if (error.message.includes('timeout')) {
            errorMessage += 'Upload is taking too long. Please try with a smaller file.';
        } else if (error.message) {
            errorMessage += error.message;
        } else {
            errorMessage += 'An unexpected error occurred. Please try again later.';
        }
        
        showNotification(errorMessage, 'error');
    })
    .finally(() => {
        // Clear timeouts
        clearTimeout(progressTimeout);
        clearTimeout(timeoutId);
        
        // Reset button state
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-upload mr-2"></i>Upload CV';
    });
}

// Handle batch CV upload
function handleBatchUpload(form) {
    const formData = new FormData(form);
    const fileInput = form.querySelector('input[type="file"]');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    if (!fileInput.files || fileInput.files.length === 0) {
        showNotification('Please select files to upload', 'error');
        return;
    }
    
    // Validate all files before upload
    const files = Array.from(fileInput.files);
    for (let i = 0; i < files.length; i++) {
        const validationError = validateFile(files[i]);
        if (validationError) {
            showNotification(`File "${files[i].name}": ${validationError}`, 'error');
            return;
        }
    }
    
    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...';
    
    // Show progress notification for longer uploads
    const progressTimeout = setTimeout(() => {
        showNotification('Batch upload is taking longer than expected. Please wait...', 'info', 8000);
    }, 8000);
    
    // Submit to batch upload endpoint with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout for batch
    
    fetch('/upload/batch/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        signal: controller.signal
    })
    .then(response => {
        // Check if response is ok
        if (!response.ok) {
            // Handle different types of errors
            if (response.status === 500) {
                throw new Error('Server error occurred during batch upload. Please try again later or contact support.');
            } else if (response.status === 413) {
                throw new Error('One or more files are too large. Please upload smaller files.');
            } else if (response.status === 400) {
                // Try to get the error message from response
                return response.json().then(data => {
                    throw new Error(data.error || 'Invalid files or request. Please check your files and try again.');
                });
            } else if (response.status === 403) {
                throw new Error('You do not have permission to upload files.');
            } else if (response.status === 401) {
                throw new Error('Please log in again to upload files.');
            } else {
                throw new Error(`Batch upload failed with error ${response.status}. Please try again.`);
            }
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'CVs uploaded successfully!', 'success');
            closePopup('uploadPopup');
            // Reload the page or refresh the CV list
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showNotification(data.error || 'Batch upload failed. Please try again.', 'error');
        }
    })
    .catch(error => {
        console.error('Upload error:', error);
        // Show user-friendly error message
        let errorMessage = 'Batch upload failed. ';
        
        if (error.name === 'AbortError') {
            errorMessage += 'Upload timed out. Please try with smaller files or check your connection.';
        } else if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
            errorMessage += 'Please check your internet connection and try again.';
        } else if (error.message.includes('timeout')) {
            errorMessage += 'Upload is taking too long. Please try with smaller files.';
        } else if (error.message) {
            errorMessage += error.message;
        } else {
            errorMessage += 'An unexpected error occurred. Please try again later.';
        }
        
        showNotification(errorMessage, 'error');
    })
    .finally(() => {
        // Clear timeouts
        clearTimeout(progressTimeout);
        clearTimeout(timeoutId);
        
        // Reset button state
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-upload mr-2"></i>Upload CVs';
    });
}

// Enhanced CV matching function with AI analysis
function startMatching() {
    const vacancySelect = document.getElementById('matchingVacancySelect');
    const vacancyId = vacancySelect.value;
    
    if (!vacancyId) {
        showNotification('Please select a vacancy', 'error');
        return;
    }
    
    // Show loading state
    const findMatchesBtn = document.querySelector('button[onclick="startMatching()"]');
    const originalBtnText = findMatchesBtn.innerHTML;
    findMatchesBtn.disabled = true;
    findMatchesBtn.innerHTML = '<i class="fas fa-brain fa-spin mr-2"></i>AI Analyzing CVs...';
    
    // Hide previous results
    const resultsDiv = document.getElementById('matchingResults');
    if (resultsDiv) {
        resultsDiv.style.display = 'none';
    }
    
    showNotification('🤖 AI is analyzing CVs against the selected vacancy...', 'info', 5000);
    
    // Call the enhanced CV matching API
    fetch(`/api/match-cvs/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            vacancy_id: vacancyId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`🎉 ${data.message || 'AI matching completed successfully!'}`, 'success');
            // Display results
            const resultsDiv = document.getElementById('matchingResults');
            if (resultsDiv) {
                resultsDiv.style.display = 'block';
                
                // Create an enhanced results display with detailed information
                if (data.results && data.results.length > 0) {
                    let resultsHtml = `
                        <div class="matching-results-header mb-4">
                            <h4 class="text-lg font-semibold">🎯 CV Analysis Results (${data.matches_found} candidates)</h4>
                            <p class="text-sm text-gray-600">Position: ${data.vacancy_title || ''} | Company: ${data.company_name || ''}</p>
                            <p class="text-sm text-gray-500">AI Method: ${data.analysis_method || 'Enhanced AI Comparison'} | Scored 0-100%</p>
                        </div>
                        <div class="results-table">
                            <table class="table table-striped">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2">Candidate</th>
                                        <th class="px-4 py-2">Experience</th>
                                        <th class="px-4 py-2">Education</th>
                                        <th class="px-4 py-2">Compatibility</th>
                                        <th class="px-4 py-2">Skills Match</th>
                                        <th class="px-4 py-2">Location</th>
                                        <th class="px-4 py-2">Recommendation</th>
                                        <th class="px-4 py-2">Strengths</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;
                    
                    data.results.forEach((result, index) => {
                        const scoreClass = result.score >= 80 ? 'text-green-600' : 
                                         result.score >= 60 ? 'text-blue-600' :
                                         result.score >= 40 ? 'text-yellow-600' : 'text-red-600';
                        
                        const skillsClass = result.skills_match >= 80 ? 'text-green-600' : 
                                          result.skills_match >= 60 ? 'text-blue-600' :
                                          result.skills_match >= 40 ? 'text-yellow-600' : 'text-red-600';
                        
                        const recommendationBadge = result.recommendation === 'Highly Recommended' ? 'bg-green-100 text-green-800' :
                                                  result.recommendation === 'Recommended' ? 'bg-blue-100 text-blue-800' :
                                                  result.recommendation === 'Consider' ? 'bg-yellow-100 text-yellow-800' : 
                                                  'bg-red-100 text-red-800';
                        
                        const experienceMatch = result.experience_match || 'Good';
                        const locationMatch = result.location_match || 'Compatible';
                        const keyStrengths = result.key_strengths || [];
                        
                        resultsHtml += `
                            <tr class="hover:bg-gray-50 ${index < 3 ? 'border-l-4 border-green-400' : ''}">
                                <td class="px-4 py-3">
                                    <div class="font-medium">${result.cv_name}</div>
                                    <div class="text-xs text-gray-500">${result.filename}</div>
                                    <div class="text-xs text-gray-400">CV Quality: ${result.cv_quality || 'N/A'}/100</div>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="font-medium">${result.experience_years} years</div>
                                    <div class="text-xs ${experienceMatch === 'Excellent' ? 'text-green-600' : experienceMatch === 'Good' ? 'text-blue-600' : experienceMatch === 'Fair' ? 'text-yellow-600' : 'text-red-600'}">${experienceMatch}</div>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="text-sm">${result.education || 'Not specified'}</div>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <span class="${scoreClass} font-semibold text-lg">${result.score}%</span>
                                        <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-600 h-2 rounded-full" style="width: ${result.score}%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="${skillsClass} font-medium">${result.skills_match || result.score}%</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="text-sm">${result.location || 'Not specified'}</div>
                                    <div class="text-xs ${locationMatch === 'Compatible' ? 'text-green-600' : locationMatch === 'Partial' ? 'text-yellow-600' : 'text-red-600'}">${locationMatch}</div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="badge ${recommendationBadge} px-2 py-1 rounded-full text-xs font-medium">${result.recommendation}</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="text-xs">
                                        ${keyStrengths.length > 0 ? 
                                            keyStrengths.map(strength => `<div class="text-green-600">• ${strength}</div>`).join('') :
                                            '<div class="text-gray-400">Analysis in progress</div>'
                                        }
                                    </div>
                                </td>
                            </tr>
                        `;
                    });
                    
                    resultsHtml += `
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                            <div class="text-sm text-blue-800">
                                <strong>💡 Analysis Notes:</strong>
                                <ul class="mt-1 ml-4 list-disc">
                                    <li>All candidates scored from 0-100% without minimum threshold</li>
                                    <li>Top 3 candidates highlighted with green border</li>
                                    <li>AI analyzes skills, experience, education, and location compatibility</li>
                                    <li>Recommendations range from "Highly Recommended" to "Not Suitable"</li>
                                    <li>Results sorted by compatibility score (best matches first)</li>
                                </ul>
                            </div>
                        </div>
                    `;
                    
                    resultsDiv.querySelector('.results-list').innerHTML = resultsHtml;
        } else {
                    resultsDiv.querySelector('.results-list').innerHTML = `
                        <div class="text-center p-4">
                            <i class="fas fa-info-circle text-blue-500 text-3xl mb-2"></i>
                            <h4>No new matches found</h4>
                            <p>All CVs have already been analyzed against this vacancy.</p>
                        </div>
                    `;
                }
            }
        } else {
            showNotification('❌ Matching failed: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('CV matching error:', error);
        showNotification('❌ AI matching failed: ' + error.message, 'error');
    })
    .finally(() => {
        // Restore button state
        if (findMatchesBtn) {
            findMatchesBtn.disabled = false;
            findMatchesBtn.innerHTML = originalBtnText;
        }
    });
}

function compareCVs() {
    const firstCVSelect = document.getElementById('firstCVSelect');
    const secondCVSelect = document.getElementById('secondCVSelect');
    
    const firstCV = firstCVSelect.value;
    const secondCV = secondCVSelect.value;
    
    if (!firstCV || !secondCV) {
        showNotification('Please select two CVs to compare', 'error');
        return;
    }
    
    if (firstCV === secondCV) {
        showNotification('Please select different CVs to compare', 'error');
        return;
    }
    
    showNotification('CV comparison started', 'success');
    
    // Here you would implement the actual comparison logic
    fetch(`/api/compare-cvs/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            cv1_id: firstCV,
            cv2_id: secondCV
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Comparison completed successfully!', 'success');
            closePopup('analyzePopup');
            // You could open a new window/page to show comparison results
            window.open(`/cv/compare/${firstCV}/${secondCV}/`, '_blank');
        } else {
            showNotification('Comparison failed: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        showNotification('Comparison failed: ' + error.message, 'error');
    });
}

// Open Duplication Check Table
function openDuplicationCheck() {
    // Create and show duplication check modal
    const modal = document.createElement('div');
    modal.className = 'popup-overlay show';
    modal.id = 'duplicationCheckModal';
    modal.innerHTML = `
        <div class="popup-content" style="max-width: 1200px; width: 90vw;">
            <div class="popup-header">
                <h2><i class="fas fa-copy mr-2"></i>CV Duplication Check Table</h2>
                <button onclick="closeDuplicationCheck()" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="duplication-controls mb-4">
                <button class="btn btn-primary btn-sm" onclick="scanForDuplicates()">
                    <i class="fas fa-search mr-1"></i> Scan for Duplicates
                </button>
                <button class="btn btn-secondary btn-sm" onclick="mergeDuplicates()">
                    <i class="fas fa-compress-alt mr-1"></i> Merge Selected
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteDuplicates()">
                    <i class="fas fa-trash mr-1"></i> Delete Selected
                </button>
            </div>
            <div id="duplicationResults">
                <div class="loading">
                    <div class="spinner"></div>
                    Scanning for duplicate CVs...
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Start scanning immediately
    setTimeout(() => {
        scanForDuplicates();
    }, 500);
}

// Close duplication check modal
function closeDuplicationCheck() {
    const modal = document.getElementById('duplicationCheckModal');
    if (modal) {
        modal.remove();
    }
}

// Scan for duplicate CVs
function scanForDuplicates() {
    const resultsDiv = document.getElementById('duplicationResults');
    resultsDiv.innerHTML = `
        <div class="loading">
            <div class="spinner"></div>
            Scanning for duplicate CVs...
        </div>
    `;
    
    fetch('/api/check-duplicates/', {
        method: 'GET',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayDuplicationResults(data.duplicates);
        } else {
            resultsDiv.innerHTML = `<div class="text-center text-red-500">Error: ${data.error}</div>`;
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = `
            <div class="text-center text-red-500">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                Error scanning for duplicates: ${error.message}
            </div>
        `;
    });
}

// Display duplication results
function displayDuplicationResults(duplicates) {
    const resultsDiv = document.getElementById('duplicationResults');
    
    if (!duplicates || duplicates.length === 0) {
        resultsDiv.innerHTML = `
            <div class="text-center text-gray-500 p-4">
                <i class="fas fa-check-circle text-green-500 text-4xl mb-2"></i>
                <h3>No duplicates found!</h3>
                <p>All CVs appear to be unique.</p>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="mb-4">
            <h3 class="text-lg font-semibold">Found ${duplicates.length} groups of duplicate CVs</h3>
        </div>
        <div class="data-table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th><input type="checkbox" onchange="toggleAllDuplicates(this)"></th>
                        <th>Group</th>
                        <th>CV Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Name</th>
                        <th>Upload Date</th>
                        <th>Match Reason</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    duplicates.forEach((group, groupIndex) => {
        group.cvs.forEach((cv, cvIndex) => {
            html += `
                <tr class="duplicate-row" data-group="${groupIndex}" data-cv-id="${cv.id}">
                    <td><input type="checkbox" class="duplicate-checkbox" data-cv-id="${cv.id}"></td>
                    <td><span class="badge bg-blue-100 text-blue-800">Group ${groupIndex + 1}</span></td>
                    <td>${cv.filename || 'N/A'}</td>
                    <td>${cv.email || 'N/A'}</td>
                    <td>${cv.phone || 'N/A'}</td>
                    <td>${cv.name || 'N/A'}</td>
                    <td>${new Date(cv.upload_date).toLocaleDateString()}</td>
                    <td><span class="badge bg-yellow-100 text-yellow-800">${group.match_reason}</span></td>
                    <td>
                        <div class="action-btn-group">
                            <button class="action-btn-mini view" onclick="viewCV(${cv.id})" title="View">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn-mini delete" onclick="deleteCV(${cv.id})" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    resultsDiv.innerHTML = html;
}

// Toggle all duplicate checkboxes
function toggleAllDuplicates(checkbox) {
    const duplicateCheckboxes = document.querySelectorAll('.duplicate-checkbox');
    duplicateCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
}

// Merge selected duplicates
function mergeDuplicates() {
    const selectedCVs = Array.from(document.querySelectorAll('.duplicate-checkbox:checked'))
                            .map(cb => cb.dataset.cvId);
    
    if (selectedCVs.length < 2) {
        showNotification('Please select at least 2 CVs to merge', 'error');
        return;
    }
    
    if (confirm(`Are you sure you want to merge ${selectedCVs.length} selected CVs?`)) {
        showNotification('Merging CVs...', 'info');
        
        fetch('/api/merge-cvs/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                cv_ids: selectedCVs
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('CVs merged successfully!', 'success');
                scanForDuplicates(); // Refresh the results
            } else {
                showNotification('Merge failed: ' + (data.error || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            showNotification('Merge failed: ' + error.message, 'error');
        });
    }
}

// Delete selected duplicates
function deleteDuplicates() {
    const selectedCVs = Array.from(document.querySelectorAll('.duplicate-checkbox:checked'))
                            .map(cb => cb.dataset.cvId);
    
    if (selectedCVs.length === 0) {
        showNotification('Please select CVs to delete', 'error');
        return;
    }
    
    if (confirm(`Are you sure you want to delete ${selectedCVs.length} selected CVs? This action cannot be undone.`)) {
        showNotification('Deleting CVs...', 'info');
        
        fetch('/api/delete-cvs/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                cv_ids: selectedCVs
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('CVs deleted successfully!', 'success');
                scanForDuplicates(); // Refresh the results
            } else {
                showNotification('Delete failed: ' + (data.error || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            showNotification('Delete failed: ' + error.message, 'error');
        });
    }
}

// CV Detail Popup Action Functions
let currentCVId = null;

function reanalyzeCV() {
    if (!currentCVId) {
        showNotification('No CV selected for analysis', 'error');
        return;
    }
    
    if (confirm('Are you sure you want to re-analyze this CV? This will override existing analysis results.')) {
        showNotification('Re-analyzing CV...', 'info');
        
        fetch(`/api/cv/${currentCVId}/analyze/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('CV re-analysis started successfully!', 'success');
                // Refresh the popup content
                setTimeout(() => {
                    viewCV(currentCVId);
                }, 2000);
            } else {
                showNotification('Re-analysis failed: ' + (data.error || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            showNotification('Re-analysis failed: ' + error.message, 'error');
        });
    }
}

function matchToJobs() {
    if (!currentCVId) {
        showNotification('No CV selected for job matching', 'error');
        return;
    }
    
    showNotification('Finding job matches...', 'info');
    
    fetch(`/api/cv/${currentCVId}/match-jobs/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Found ${data.matches || 0} job matches!`, 'success');
            // You can add code here to display the matches in a modal or navigate to results
            if (data.matches > 0) {
                // Open job matches view
                window.open(`/cv/${currentCVId}/job-matches/`, '_blank');
            }
        } else {
            showNotification('Job matching failed: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        showNotification('Job matching failed: ' + error.message, 'error');
    });
}

function shareCV() {
    if (!currentCVId) {
        showNotification('No CV selected for sharing', 'error');
        return;
    }
    
    // Create shareable link
    const shareUrl = `${window.location.origin}/cv/${currentCVId}/public/`;
    
    // Copy to clipboard
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(shareUrl).then(() => {
            showNotification('Shareable link copied to clipboard!', 'success');
        }).catch(() => {
            // Fallback for older browsers
            fallbackCopyToClipboard(shareUrl);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyToClipboard(shareUrl);
    }
    
    // Also show a modal with sharing options
    showShareModal(shareUrl);
}

function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
        document.execCommand('copy');
        showNotification('Shareable link copied to clipboard!', 'success');
    } catch (err) {
        showNotification('Unable to copy link. Please copy manually: ' + text, 'error');
    }
    document.body.removeChild(textArea);
}

function showShareModal(shareUrl) {
    const modal = document.createElement('div');
    modal.className = 'popup-overlay show';
    modal.innerHTML = `
        <div class="popup-content" style="max-width: 500px;">
            <div class="popup-header">
                <h3>Share CV</h3>
                <button class="popup-close" onclick="this.closest('.popup-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-4">
                <p class="mb-4">Share this CV with others using the link below:</p>
                <div class="flex gap-2 mb-4">
                    <input type="text" value="${shareUrl}" class="form-control flex-1" readonly>
                    <button class="btn btn-primary" onclick="navigator.clipboard.writeText('${shareUrl}'); showNotification('Link copied!', 'success');">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <div class="flex gap-2">
                    <button class="btn btn-secondary" onclick="window.open('mailto:?subject=CV%20Share&body=${encodeURIComponent(shareUrl)}', '_blank')">
                        <i class="fas fa-envelope mr-1"></i> Email
                    </button>
                    <button class="btn btn-secondary" onclick="window.open('https://wa.me/?text=${encodeURIComponent(shareUrl)}', '_blank')">
                        <i class="fab fa-whatsapp mr-1"></i> WhatsApp
                    </button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

function addNotes() {
    if (!currentCVId) {
        showNotification('No CV selected for adding notes', 'error');
        return;
    }
    
    // First, get existing notes
    fetch(`/api/cv/${currentCVId}/notes/`)
        .then(response => response.json())
        .then(data => {
            const existingNotes = data.notes || '';
            showNotesModal(existingNotes);
        })
        .catch(() => {
            showNotesModal('');
        });
}

function showNotesModal(existingNotes) {
    const modal = document.createElement('div');
    modal.className = 'popup-overlay show';
    modal.innerHTML = `
        <div class="popup-content" style="max-width: 600px;">
            <div class="popup-header">
                <h3>CV Notes</h3>
                <button class="popup-close" onclick="this.closest('.popup-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-4">
                <div class="form-group mb-4">
                    <label class="block text-sm font-medium mb-2">Notes for this CV:</label>
                    <textarea id="cvNotes" class="form-control" rows="6" placeholder="Add your notes here...">${existingNotes}</textarea>
                </div>
                <div class="flex gap-2 justify-end">
                    <button class="btn btn-secondary" onclick="this.closest('.popup-overlay').remove()">Cancel</button>
                    <button class="btn btn-primary" onclick="saveNotes()">
                        <i class="fas fa-save mr-1"></i> Save Notes
                    </button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

function saveNotes() {
    const notes = document.getElementById('cvNotes').value;
    
    fetch(`/api/cv/${currentCVId}/notes/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ notes: notes })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Notes saved successfully!', 'success');
            document.querySelector('.popup-overlay').remove();
        } else {
            showNotification('Failed to save notes: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        showNotification('Failed to save notes: ' + error.message, 'error');
    });
}

function archiveCV() {
    if (!currentCVId) {
        showNotification('No CV selected for archiving', 'error');
        return;
    }
    
    if (confirm('Are you sure you want to archive this CV? Archived CVs can be restored later.')) {
        showNotification('Archiving CV...', 'info');
        
        fetch(`/api/cv/${currentCVId}/archive/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('CV archived successfully!', 'success');
                closePopup('cvDetailPopup');
                // Refresh the main data view
                refreshData();
            } else {
                showNotification('Archive failed: ' + (data.error || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            showNotification('Archive failed: ' + error.message, 'error');
        });
    }
}

// Update the viewCV function to store the current CV ID
function viewCVWithId(cvId) {
    currentCVId = cvId;
    viewCV(cvId);
}
</script>
{% endblock %}