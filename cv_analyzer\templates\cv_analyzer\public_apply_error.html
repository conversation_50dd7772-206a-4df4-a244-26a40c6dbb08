{% load form_tags %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Error | CV Analyzer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <a href="/" class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-robot text-white"></i>
                    </div>
                    <span class="text-xl font-bold text-gray-900">CV Analyzer</span>
                </a>
                <div class="flex items-center space-x-4">
                    <a href="/public/vacancies/" class="text-blue-600 hover:text-blue-700 font-medium">
                        <i class="fas fa-briefcase mr-2"></i>All Jobs
                    </a>
                    <a href="/" class="text-blue-600 hover:text-blue-700 font-medium">
                        <i class="fas fa-home mr-2"></i>Home
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-2xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <!-- Error Content -->
        <div class="bg-white rounded-lg shadow-lg border p-8 text-center">
            <!-- Error Icon -->
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
                <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
            </div>
            
            <!-- Error Title -->
            <h1 class="text-2xl font-bold text-gray-900 mb-4">
                Application Error
            </h1>
            
            <!-- Error Message -->
            <div class="mb-8">
                {% if error %}
                    <p class="text-lg text-gray-600 mb-4">{{ error }}</p>
                {% else %}
                    <p class="text-lg text-gray-600 mb-4">
                        We encountered an issue processing your application.
                    </p>
                {% endif %}
                
                {% if vacancy_id %}
                    <p class="text-sm text-gray-500">
                        <i class="fas fa-info-circle mr-1"></i>
                        Vacancy ID: {{ vacancy_id }}
                    </p>
                {% endif %}
            </div>
            
            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                {% if vacancy_id %}
                    <a href="/public/vacancy/{{ vacancy_id }}/" 
                       class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors">
                        <i class="fas fa-eye mr-2"></i>
                        View Job Details
                    </a>
                    <a href="/public/apply/{{ vacancy_id }}/" 
                       class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 rounded-lg transition-colors">
                        <i class="fas fa-redo mr-2"></i>
                        Try Again
                    </a>
                {% endif %}
                
                <a href="/public/vacancies/" 
                   class="inline-flex items-center justify-center bg-gray-600 hover:bg-gray-700 text-white font-medium px-6 py-3 rounded-lg transition-colors">
                    <i class="fas fa-briefcase mr-2"></i>
                    Browse All Jobs
                </a>
                
                <a href="/" 
                   class="inline-flex items-center justify-center border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 font-medium px-6 py-3 rounded-lg transition-colors">
                    <i class="fas fa-home mr-2"></i>
                    Back to Home
                </a>
            </div>
        </div>
        
        <!-- Help Section -->
        <div class="mt-8 bg-blue-50 rounded-lg border border-blue-200 p-6">
            <h2 class="text-lg font-semibold text-blue-900 mb-3 flex items-center">
                <i class="fas fa-question-circle mr-2"></i>
                Need Help?
            </h2>
            <div class="text-blue-800 text-sm space-y-2">
                <p><strong>Common issues and solutions:</strong></p>
                <ul class="list-disc list-inside space-y-1 ml-4">
                    <li>Make sure your CV file is in PDF, DOC, or DOCX format</li>
                    <li>Check that your file size is under 10MB</li>
                    <li>Verify that the job posting is still active</li>
                    <li>Try refreshing the page and applying again</li>
                </ul>
            </div>
        </div>
        
        <!-- Contact Support -->
        <div class="mt-6 text-center">
            <p class="text-gray-600 text-sm">
                Still having trouble? 
                <a href="#" class="text-blue-600 hover:text-blue-700 font-medium">Contact our support team</a>
                for assistance.
            </p>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t">
        <div class="max-w-2xl mx-auto px-4 py-8">
            <div class="text-center text-gray-600">
                <p>&copy; 2024 CV Analyzer. All rights reserved.</p>
                <p class="mt-2">
                    <a href="/" class="text-blue-600 hover:text-blue-700">Home</a> |
                    <a href="/public/vacancies/" class="text-blue-600 hover:text-blue-700">Jobs</a> |
                    <a href="#" class="text-blue-600 hover:text-blue-700">Support</a>
                </p>
            </div>
        </div>
    </footer>
</body>
</html> 