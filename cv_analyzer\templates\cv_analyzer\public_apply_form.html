<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apply for {{ vacancy.title }} - CV Analyzer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #d1d5db;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #3b82f6;
            background-color: #f9fafb;
        }
        .upload-area.dragover {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <a href="/" class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-robot text-white"></i>
                    </div>
                    <span class="text-xl font-bold text-gray-900">CV Analyzer</span>
                </a>
                <a href="/public/vacancies/" class="text-blue-600 hover:text-blue-700 font-medium">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Jobs
                </a>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- Job Information -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
            <div class="flex items-start justify-between mb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ vacancy.title }}</h1>
                    <div class="flex items-center text-gray-600 mb-2">
                        <i class="fas fa-building mr-2"></i>
                        <span class="font-medium">{{ company.name }}</span>
                    </div>
                    <div class="flex items-center text-gray-600">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        <span>Remote</span>
                    </div>
                </div>
                <span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">
                    Active Position
                </span>
            </div>
            
            <div class="border-t pt-4">
                <h3 class="font-semibold text-gray-900 mb-2">Job Description</h3>
                <p class="text-gray-700 mb-4">{{ vacancy.description }}</p>
                
                <h3 class="font-semibold text-gray-900 mb-2">Requirements</h3>
                <p class="text-gray-700">{{ vacancy.requirements }}</p>
            </div>
        </div>

        <!-- Application Form -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-6">
                <i class="fas fa-file-upload mr-2 text-blue-600"></i>
                Submit Your Application
            </h2>

            <form id="applicationForm" enctype="multipart/form-data" class="space-y-6">
                {% csrf_token %}
                
                <!-- Personal Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="applicant_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Full Name *
                        </label>
                        <input type="text" id="applicant_name" name="applicant_name" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="applicant_email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address *
                        </label>
                        <input type="email" id="applicant_email" name="applicant_email" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <div>
                    <label for="applicant_phone" class="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                    </label>
                    <input type="tel" id="applicant_phone" name="applicant_phone"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- CV Upload -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Upload Your CV *
                    </label>
                    <div class="upload-area rounded-lg p-8 text-center cursor-pointer" onclick="document.getElementById('cv_file').click()">
                        <div id="upload-content">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                            <p class="text-lg font-medium text-gray-700 mb-2">Click to upload your CV</p>
                            <p class="text-sm text-gray-500">or drag and drop your file here</p>
                            <p class="text-xs text-gray-400 mt-2">Supported formats: PDF, DOC, DOCX (Max 10MB)</p>
                        </div>
                        <div id="file-selected" class="hidden">
                            <i class="fas fa-file-check text-4xl text-green-500 mb-4"></i>
                            <p class="text-lg font-medium text-green-700 mb-2">File Selected</p>
                            <p id="file-name" class="text-sm text-gray-600"></p>
                            <button type="button" onclick="clearFile()" class="text-red-600 hover:text-red-700 text-sm mt-2">
                                <i class="fas fa-times mr-1"></i>Remove file
                            </button>
                        </div>
                    </div>
                    <input type="file" id="cv_file" name="cv_file" accept=".pdf,.doc,.docx" class="hidden" required>
                </div>

                <!-- Submit Button -->
                <div class="flex items-center justify-between pt-6 border-t">
                    <p class="text-sm text-gray-500">
                        <i class="fas fa-info-circle mr-1"></i>
                        Your information will be processed according to our privacy policy.
                    </p>
                    
                    <button type="submit" id="submitBtn" 
                            class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition-colors flex items-center">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Submit Application
                    </button>
                </div>
            </form>
        </div>

        <!-- Success Message -->
        <div id="successMessage" class="hidden bg-green-50 border border-green-200 rounded-lg p-6 mt-6">
            <div class="flex items-center">
                <i class="fas fa-check-circle text-green-500 text-xl mr-3"></i>
                <div>
                    <h3 class="text-lg font-semibold text-green-800">Application Submitted Successfully!</h3>
                    <p class="text-green-700 mt-1">Thank you for your interest. We'll review your application and get back to you soon.</p>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <div id="errorMessage" class="hidden bg-red-50 border border-red-200 rounded-lg p-6 mt-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle text-red-500 text-xl mr-3"></i>
                <div>
                    <h3 class="text-lg font-semibold text-red-800">Application Failed</h3>
                    <p id="errorText" class="text-red-700 mt-1"></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // File upload handling
        const fileInput = document.getElementById('cv_file');
        const uploadArea = document.querySelector('.upload-area');
        const uploadContent = document.getElementById('upload-content');
        const fileSelected = document.getElementById('file-selected');
        const fileName = document.getElementById('file-name');

        // File input change
        fileInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                const file = e.target.files[0];
                showFileSelected(file.name);
            }
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                showFileSelected(files[0].name);
            }
        });

        function showFileSelected(name) {
            uploadContent.classList.add('hidden');
            fileSelected.classList.remove('hidden');
            fileName.textContent = name;
        }

        function clearFile() {
            fileInput.value = '';
            uploadContent.classList.remove('hidden');
            fileSelected.classList.add('hidden');
        }

        // Form submission
        document.getElementById('applicationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Submitting...';
            submitBtn.disabled = true;
            
            // Create FormData
            const formData = new FormData(this);
            
            // Submit form
            fetch(`/public/apply/{{ vacancy.id }}/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('successMessage').classList.remove('hidden');
                    document.getElementById('errorMessage').classList.add('hidden');
                    document.getElementById('applicationForm').style.display = 'none';
                } else {
                    document.getElementById('errorMessage').classList.remove('hidden');
                    document.getElementById('errorText').textContent = data.error || 'An error occurred';
                    document.getElementById('successMessage').classList.add('hidden');
                }
            })
            .catch(error => {
                document.getElementById('errorMessage').classList.remove('hidden');
                document.getElementById('errorText').textContent = 'Network error. Please try again.';
                document.getElementById('successMessage').classList.add('hidden');
            })
            .finally(() => {
                // Restore button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html> 