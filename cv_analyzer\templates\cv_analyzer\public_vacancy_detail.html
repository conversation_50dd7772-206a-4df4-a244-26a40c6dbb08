{% load form_tags %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ vacancy.title }} - {{ vacancy.company.name }} | CV Analyzer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <a href="/" class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-robot text-white"></i>
                    </div>
                    <span class="text-xl font-bold text-gray-900">CV Analyzer</span>
                </a>
                <div class="flex items-center space-x-4">
                    <a href="/public/vacancies/" class="text-blue-600 hover:text-blue-700 font-medium">
                        <i class="fas fa-briefcase mr-2"></i>All Jobs
                    </a>
                    <a href="/" class="text-blue-600 hover:text-blue-700 font-medium">
                        <i class="fas fa-home mr-2"></i>Home
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- Breadcrumb -->
        <nav class="mb-6">
            <ol class="flex items-center space-x-2 text-sm text-gray-500">
                <li><a href="/" class="hover:text-blue-600">Home</a></li>
                <li><i class="fas fa-chevron-right"></i></li>
                <li><a href="/public/vacancies/" class="hover:text-blue-600">Jobs</a></li>
                <li><i class="fas fa-chevron-right"></i></li>
                <li class="text-gray-900">{{ vacancy.title }}</li>
            </ol>
        </nav>

        <!-- Job Header -->
        <div class="bg-white rounded-lg shadow-sm border p-8 mb-6">
            <div class="flex items-start justify-between mb-6">
                <div class="flex-1">
                    <h1 class="text-3xl font-bold text-gray-900 mb-4">
                        {{ vacancy.title }}
                    </h1>
                    <div class="flex items-center text-lg text-gray-600 mb-2">
                        <i class="fas fa-building mr-3 text-blue-600"></i>
                        {{ vacancy.company.name }}
                    </div>
                    <div class="flex items-center text-gray-600 mb-2">
                        <i class="fas fa-map-marker-alt mr-3 text-green-600"></i>
                        Remote
                    </div>
                    {% if vacancy.category %}
                    <div class="flex items-center text-gray-600 mb-4">
                        <i class="fas fa-tag mr-3 text-purple-600"></i>
                        {{ vacancy.category }}
                    </div>
                    {% endif %}
                </div>
                <div class="text-right">
                    <span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">
                        <i class="fas fa-circle text-xs mr-1"></i>Active
                    </span>
                    <div class="text-sm text-gray-500 mt-2">
                        <i class="fas fa-clock mr-1"></i>
                        Posted {{ vacancy.created_at|timesince }} ago
                    </div>
                </div>
            </div>

            <!-- Apply Button (Prominent) -->
            <div class="border-t pt-6">
                <a href="/public/apply/{{ vacancy.id }}/" 
                   class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition-colors shadow-lg hover:shadow-xl">
                    <i class="fas fa-upload mr-3"></i>
                    Apply for this Position
                </a>
                <p class="text-sm text-gray-500 mt-2">
                    <i class="fas fa-info-circle mr-1"></i>
                    Upload your CV and we'll match you with this position
                </p>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Job Description -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-align-left mr-3 text-blue-600"></i>
                        Job Description
                    </h2>
                    <div class="prose prose-sm max-w-none text-gray-700">
                        {% if vacancy.description %}
                            {{ vacancy.description|linebreaks }}
                        {% else %}
                            <p>Full job description will be provided during the application process.</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Requirements -->
                {% if vacancy.requirements %}
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-clipboard-list mr-3 text-red-600"></i>
                        Requirements
                    </h2>
                    <div class="prose prose-sm max-w-none text-gray-700">
                        {{ vacancy.requirements|linebreaks }}
                    </div>
                </div>
                {% endif %}

                <!-- Skills -->
                {% if vacancy_analysis.required_skills %}
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-tools mr-3 text-yellow-600"></i>
                        Required Skills
                    </h2>
                    <div class="flex flex-wrap gap-2">
                        {% for skill in vacancy_analysis.required_skills|split %}
                        <span class="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                            {{ skill }}
                        </span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Company Info -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-building mr-3 text-green-600"></i>
                        About {{ vacancy.company.name }}
                    </h3>
                    {% if vacancy.company.description %}
                        <p class="text-gray-600 text-sm leading-relaxed">
                            {{ vacancy.company.description }}
                        </p>
                    {% endif %}
                    {% if vacancy.company.industry %}
                        <div class="mt-4">
                            <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded">
                                {{ vacancy.company.industry }}
                            </span>
                        </div>
                    {% endif %}
                </div>

                <!-- Additional Info -->
                {% if vacancy_analysis %}
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-info-circle mr-3 text-purple-600"></i>
                        Position Details
                    </h3>
                    <div class="space-y-3 text-sm">
                        {% if vacancy_analysis.salary_range %}
                        <div class="flex items-center">
                            <i class="fas fa-dollar-sign w-4 mr-2 text-gray-500"></i>
                            <span class="text-gray-600">Salary:</span>
                            <span class="ml-auto font-medium">{{ vacancy_analysis.salary_range }}</span>
                        </div>
                        {% endif %}
                        {% if vacancy_analysis.required_experience %}
                        <div class="flex items-center">
                            <i class="fas fa-user-tie w-4 mr-2 text-gray-500"></i>
                            <span class="text-gray-600">Experience:</span>
                            <span class="ml-auto font-medium">{{ vacancy_analysis.required_experience }} years</span>
                        </div>
                        {% endif %}
                        {% if vacancy_analysis.remote_work_policy %}
                        <div class="flex items-center">
                            <i class="fas fa-laptop-house w-4 mr-2 text-gray-500"></i>
                            <span class="text-gray-600">Work Style:</span>
                            <span class="ml-auto font-medium">{{ vacancy_analysis.remote_work_policy }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Apply Again (Sidebar) -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">
                        Ready to Apply?
                    </h3>
                    <p class="text-gray-600 text-sm mb-4">
                        Submit your CV and let our AI analyze your compatibility with this position.
                    </p>
                    <a href="/public/apply/{{ vacancy.id }}/" 
                       class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-center block">
                        <i class="fas fa-upload mr-2"></i>
                        Apply Now
                    </a>
                </div>

                <!-- Share -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        Share this Job
                    </h3>
                    <div class="flex space-x-2">
                        <button onclick="shareJob('twitter')" class="flex-1 bg-blue-400 hover:bg-blue-500 text-white py-2 px-3 rounded text-sm">
                            <i class="fab fa-twitter"></i>
                        </button>
                        <button onclick="shareJob('linkedin')" class="flex-1 bg-blue-700 hover:bg-blue-800 text-white py-2 px-3 rounded text-sm">
                            <i class="fab fa-linkedin"></i>
                        </button>
                        <button onclick="shareJob('copy')" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-3 rounded text-sm">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="mt-8 text-center">
            <a href="/public/vacancies/" class="text-blue-600 hover:text-blue-700 font-medium">
                <i class="fas fa-arrow-left mr-2"></i>Back to All Jobs
            </a>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t mt-16">
        <div class="max-w-4xl mx-auto px-4 py-8">
            <div class="text-center text-gray-600">
                <p>&copy; 2024 CV Analyzer. All rights reserved.</p>
                <p class="mt-2">
                    <a href="/" class="text-blue-600 hover:text-blue-700">Home</a> |
                    <a href="/public/vacancies/" class="text-blue-600 hover:text-blue-700">Jobs</a> |
                    <a href="#" class="text-blue-600 hover:text-blue-700">Privacy Policy</a>
                </p>
            </div>
        </div>
    </footer>

    <script>
        function shareJob(platform) {
            const url = window.location.href;
            const title = "{{ vacancy.title }} at {{ vacancy.company.name }}";
            
            switch(platform) {
                case 'twitter':
                    window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`, '_blank');
                    break;
                case 'linkedin':
                    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank');
                    break;
                case 'copy':
                    navigator.clipboard.writeText(url).then(() => {
                        alert('Job link copied to clipboard!');
                    });
                    break;
            }
        }
    </script>
</body>
</html> 