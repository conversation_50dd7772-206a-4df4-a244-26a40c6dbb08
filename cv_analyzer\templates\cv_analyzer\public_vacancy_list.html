{% load form_tags %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Opportunities - CV Analyzer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <a href="/" class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-robot text-white"></i>
                    </div>
                    <span class="text-xl font-bold text-gray-900">CV Analyzer</span>
                </a>
                <a href="/" class="text-blue-600 hover:text-blue-700 font-medium">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
            </div>
        </div>
    </nav>

    <div class="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">
                <i class="fas fa-briefcase mr-3 text-blue-600"></i>
                Current Job Opportunities
            </h1>
            <p class="text-lg text-gray-600">
                Discover your next career opportunity. Apply directly by uploading your CV.
            </p>
        </div>

        <!-- Search Bar -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
            <form method="GET" class="flex gap-4">
                <div class="flex-1">
                    <input type="text" name="search" value="{{ search_query }}" 
                           placeholder="Search jobs, companies, or keywords..."
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-search mr-2"></i>Search
                </button>
            </form>
        </div>

        <!-- Results Stats -->
        <div class="mb-6">
            <p class="text-gray-600">
                <i class="fas fa-info-circle mr-1"></i>
                {{ total_count }} job{{ total_count|pluralize }} found
                {% if search_query %}
                    for "{{ search_query }}"
                {% endif %}
            </p>
        </div>

        <!-- Job Listings -->
        {% if vacancies %}
            <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {% for vacancy in vacancies %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-300">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                {{ vacancy.title }}
                            </h3>
                            <div class="flex items-center text-sm text-gray-600 mb-2">
                                <i class="fas fa-building mr-2"></i>
                                {{ vacancy.company.name }}
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-map-marker-alt mr-2"></i>
                                Remote
                            </div>
                        </div>
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                            Active
                        </span>
                    </div>
                    
                    <p class="text-sm text-gray-600 mb-4 line-clamp-3">
                        {{ vacancy.description|truncatewords:25 }}
                    </p>
                    
                    <div class="mb-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Key Skills:</h4>
                        <div class="flex flex-wrap gap-1">
                            {% with skills="Python,Django,React,SQL,JavaScript,HTML,CSS,Node.js"|split:"," %}
                            {% for skill in skills|slice:":4" %}
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded">
                                {{ skill }}
                            </span>
                            {% endfor %}
                            {% endwith %}
                        </div>
                    </div>
                    
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <i class="fas fa-clock mr-1"></i>
                        Posted {{ vacancy.created_at|timesince }} ago
                    </div>
                    
                    <div class="flex gap-3">
                        <a href="/public/apply/{{ vacancy.id }}/" 
                           class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors text-center">
                            <i class="fas fa-upload mr-2"></i>Apply Now
                        </a>
                        <a href="/public/vacancy/{{ vacancy.id }}/" 
                           class="bg-gray-200 hover:bg-gray-300 text-gray-700 text-sm font-medium py-2 px-3 rounded-lg transition-colors">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- No Jobs Found -->
            <div class="bg-white rounded-lg shadow-sm border p-12 text-center">
                <div class="text-gray-400 text-6xl mb-6">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">No Jobs Found</h3>
                <p class="text-gray-600 mb-6">
                    {% if search_query %}
                        We couldn't find any jobs matching "{{ search_query }}". Try different keywords or check back later.
                    {% else %}
                        There are no active job postings at the moment. Check back later for new opportunities.
                    {% endif %}
                </p>
                <div class="flex justify-center space-x-4">
                    {% if search_query %}
                        <a href="/public/vacancies/" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                            <i class="fas fa-list mr-2"></i>View All Jobs
                        </a>
                    {% endif %}
                    <a href="/" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg transition-colors">
                        <i class="fas fa-home mr-2"></i>Back to Home
                    </a>
                </div>
            </div>
        {% endif %}

        <!-- Back to Top -->
        <div class="text-center mt-12">
            <a href="#top" class="text-blue-600 hover:text-blue-700 font-medium">
                <i class="fas fa-arrow-up mr-2"></i>Back to Top
            </a>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t mt-16">
        <div class="max-w-6xl mx-auto px-4 py-8">
            <div class="text-center text-gray-600">
                <p>&copy; 2024 CV Analyzer. All rights reserved.</p>
                <p class="mt-2">
                    <a href="/" class="text-blue-600 hover:text-blue-700">Home</a> |
                    <a href="#" class="text-blue-600 hover:text-blue-700">Privacy Policy</a> |
                    <a href="#" class="text-blue-600 hover:text-blue-700">Terms of Service</a>
                </p>
            </div>
        </div>
    </footer>

    <style>
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</body>
</html> 