<!DOCTYPE html>
<html>
<head>
    <title>{{ job_title }} - {{ job_description }}</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="{% static 'js/background3d.js' %}"></script>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between mt-5 mb-3">
            <h1>{{ job_title }}</h1>
            <div>
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="theme-switch">
                    <label class="custom-control-label" for="theme-switch">Dark mode</label>
                </div>
            </div>
        </div>
        <p class="mb-5">{{ job_description }} and {{ conditions }}</p>

        <ul class="nav nav-tabs mt-5" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="summary-tab" data-toggle="tab" href="#summary" role="tab">Summary</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="details-tab" data-toggle="tab" href="#details" role="tab">Details</a>
            </li>
        </ul>

        <div class="tab-content">
            <div class="tab-pane fade show active" id="summary" role="tabpanel">
                <div class="mt-5">
                    <h3>Consolidated Summary</h3>
                    <canvas id="consolidated"></canvas>
                    <script>
                        new Chart(document.getElementById('consolidated'), {
                            type: 'pie',
                            data: {
                              labels: ["Experience", "Education", "Skills"],
                              datasets: [{
                                label: "Average Compatibility",
                                backgroundColor: ["#3e95cd", "#8e5ea2","#3cba9f"],
                                data: [{{ consolidated.experience_value }}, {{ consolidated.education_value }}, {{ consolidated.skills_value }}]
                              }]
                            },
                            options: {
                              title: {
                                display: true,
                                text: 'Average Compatibility'
                              }
                            }
                        });
                    </script>
                </div>
                {% for result in results %}
                <div class="mt-5">
                    <h3>{{ result.name }}</h3>
                    <p>Name: {{ result.name }}</p>
                    <p>Location: {{ result.location }}</p>
                    <p>Contact: {{ result.contact }}</p>
                    <canvas id="{{ result.filename }}"></canvas>
                    <script>
                        new Chart(document.getElementById('{{ result.filename }}'), {
                            type: 'pie',
                            data: {
                              labels: ["Experience", "Education", "Skills"],
                              datasets: [{
                                label: "Compatibility",
                                backgroundColor: ["{{ '#4caf50' if result.compatibility >= 80 else '#ffeb3b' if result.compatibility >= 50 else '#f44336' }}", "#8e5ea2","#3cba9f"],
                                data: [{{ result.experience_value }}, {{ result.education_value }}, {{ result.skills_value }}]
                              }]
                            },
                            options: {
                              title: {
                                display: true,
                                text: 'Compatibility: {{ result.compatibility }}%'
                              }
                            }
                        });
                    </script>
                </div>
                {% endfor %}
            </div>

            <div class="tab-pane fade" id="details" role="tabpanel">
                {% for result in results %}
                <div class="mt-5" style="border-left: 10px solid {{ '#4caf50' if result.compatibility >= 80 else '#ffeb3b' if result.compatibility >= 50 else '#f44336' }};">
                    <h3>{{ result.name }}</h3>
                    <div class="card">
                        <div class="card-header" id="heading-{{ loop.index }}">
                            <h5 class="mb-0">
                                <button class="btn btn-link" data-toggle="collapse" data-target="#collapse-{{ loop.index }}" aria-expanded="true" aria-controls="collapse-{{ loop.index }}">
                                    Experience, Education, Skills and Certifications
                                </button>
                            </h5>
                        </div>
                        <div id="collapse-{{ loop.index }}" class="collapse show" aria-labelledby="heading-{{ loop.index }}" data-parent="#details">
                            <div class="card-body">
                                <p>Experience: {{ result.experience }}</p>
                                <p>Education: {{ result.education }}</p>
                                <p>Skills: {{ result.skills }}</p>
                                <p>Past Roles: {{ result.past_roles }}</p>
                                <p>Certifications: {{ result.certifications }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <a href="{{ url_for('download', filename=excel_filename) }}" class="btn btn-primary mt-3">Download Excel</a>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js"></script>
</body>
</html>