{% extends "cv_analyzer/base.html" %}

{% block title %}CV Analysis Results{% endblock %}

{% block content %}
    <h2>CV Analysis Results</h2>
    <div class="overall-score">
        <h3>Average Scores</h3>
        <div class="score-circle">{{ consolidated.avg_overall_score|floatformat:1 }}/100</div>
    </div>

    <div class="category-scores">
        <div class="category">
            <h4>Content</h4>
            <div class="progress-bar" style="width: {{ consolidated.avg_content_score }}%;">{{ consolidated.avg_content_score|floatformat:1 }}%</div>
        </div>
        <div class="category">
            <h4>Format</h4>
            <div class="progress-bar" style="width: {{ consolidated.avg_format_score }}%;">{{ consolidated.avg_format_score|floatformat:1 }}%</div>
        </div>
        <div class="category">
            <h4>Sections</h4>
            <div class="progress-bar" style="width: {{ consolidated.avg_sections_score }}%;">{{ consolidated.avg_sections_score|floatformat:1 }}%</div>
        </div>
        <div class="category">
            <h4>Skills</h4>
            <div class="progress-bar" style="width: {{ consolidated.avg_skills_score }}%;">{{ consolidated.avg_skills_score|floatformat:1 }}%</div>
        </div>
        <div class="category">
            <h4>Style</h4>
            <div class="progress-bar" style="width: {{ consolidated.avg_style_score }}%;">{{ consolidated.avg_style_score|floatformat:1 }}%</div>
        </div>
    </div>

    {% for result in results %}
    <div class="individual-analysis mt-4">
        <h3>{{ result.name }}</h3>
        <div class="score-details">
            <div class="overall-score">
                <h4>Overall Score</h4>
                <div class="score-circle">{{ result.overall_score }}/100</div>
            </div>
            <div class="category-scores">
                <div class="category">
                    <h5>Content</h5>
                    <div class="progress-bar" style="width: {{ result.content_score }}%;">{{ result.content_score }}%</div>
                </div>
                <div class="category">
                    <h5>Format</h5>
                    <div class="progress-bar" style="width: {{ result.format_score }}%;">{{ result.format_score }}%</div>
                </div>
                <div class="category">
                    <h5>Sections</h5>
                    <div class="progress-bar" style="width: {{ result.sections_score }}%;">{{ result.sections_score }}%</div>
                </div>
                <div class="category">
                    <h5>Skills</h5>
                    <div class="progress-bar" style="width: {{ result.skills_score }}%;">{{ result.skills_score }}%</div>
                </div>
                <div class="category">
                    <h5>Style</h5>
                    <div class="progress-bar" style="width: {{ result.style_score }}%;">{{ result.style_score }}%</div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Create consolidated chart
    new Chart(document.getElementById('consolidated-chart'), {
        type: 'radar',
        data: {
            labels: ['Content', 'Format', 'Sections', 'Skills', 'Style'],
            datasets: [{
                label: 'Average Scores',
                data: [
                    {{ consolidated.avg_content_score|floatformat:1 }},
                    {{ consolidated.avg_format_score|floatformat:1 }},
                    {{ consolidated.avg_sections_score|floatformat:1 }},
                    {{ consolidated.avg_skills_score|floatformat:1 }},
                    {{ consolidated.avg_style_score|floatformat:1 }}
                ],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(54, 162, 235, 1)'
            }]
        },
        options: {
            responsive: true,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Create individual charts for each result
    {% for result in results %}
    new Chart(document.getElementById('result-{{ result.id }}-chart'), {
        type: 'radar',
        data: {
            labels: ['Content', 'Format', 'Sections', 'Skills', 'Style'],
            datasets: [{
                label: 'Scores',
                data: [
                    {{ result.content_score }},
                    {{ result.format_score }},
                    {{ result.sections_score }},
                    {{ result.skills_score }},
                    {{ result.style_score }}
                ],
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(255, 99, 132, 1)'
            }]
        },
        options: {
            responsive: true,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
    {% endfor %}
</script>
{% endblock %}