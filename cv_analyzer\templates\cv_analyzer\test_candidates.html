<!DOCTYPE html>
<html>
<head>
    <title>Test Candidates Page</title>
</head>
<body>
    <h1>Test Candidates Functionality</h1>
    
    <p>Click the links below to test the candidates page:</p>
    
    <ul>
        <li><a href="/vacancy/1/candidates/">Test Vacancy ID 1</a></li>
        <li><a href="/vacancy/2/candidates/">Test Vacancy ID 2</a></li>
        <li><a href="/vacancy/3/candidates/">Test Vacancy ID 3</a></li>
    </ul>
    
    <script>
    function testCandidates(id) {
        window.location.href = `/vacancy/${id}/candidates/`;
    }
    </script>
    
    <p>Or test with JavaScript:</p>
    <button onclick="testCandidates(1)">Test ID 1 with JS</button>
    <button onclick="testCandidates(2)">Test ID 2 with JS</button>
</body>
</html> 