{% extends "cv_analyzer/base.html" %}
{% load static %}

{% block title %}CV Analyzer - Unified Dashboard{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    /* Global Styles */
    .dashboard-container {
        max-width: 100%;
        margin: 0 auto;
        padding: 1rem;
    }

    /* Header Styles */
    .unified-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .unified-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    /* Quick Actions Bar */
    .quick-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 2rem;
        padding: 1rem;
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .action-btn {
        flex: 1;
        min-width: 200px;
        padding: 1rem;
        background: linear-gradient(135deg, var(--btn-start), var(--btn-end));
        color: white;
        border: none;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-weight: 600;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    }

    .action-btn.upload { --btn-start: #4f46e5; --btn-end: #7c3aed; }
    .action-btn.analyze { --btn-start: #059669; --btn-end: #0d9488; }
    .action-btn.manage { --btn-start: #dc2626; --btn-end: #ea580c; }
    .action-btn.reports { --btn-start: #7c2d12; --btn-end: #a16207; }

    /* Main Content Grid */
    .dashboard-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    @media (max-width: 1024px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
    }

    /* Metric Cards */
    .metric-section {
        background: white;
        padding: 1.5rem;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .metric-card {
        background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
        color: white;
        padding: 1.5rem;
        border-radius: 0.75rem;
        text-align: center;
        transition: transform 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .metric-card:hover {
        transform: scale(1.05);
    }

    .metric-card.blue { --gradient-start: #3b82f6; --gradient-end: #1e40af; }
    .metric-card.green { --gradient-start: #10b981; --gradient-end: #059669; }
    .metric-card.purple { --gradient-start: #8b5cf6; --gradient-end: #7c3aed; }
    .metric-card.orange { --gradient-start: #f59e0b; --gradient-end: #d97706; }

    /* Tabbed Content */
    .tab-container {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .tab-navigation {
        display: flex;
        background: #f8fafc;
        border-bottom: 1px solid #e2e8f0;
    }

    .tab-btn {
        flex: 1;
        padding: 1rem;
        background: transparent;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        color: #64748b;
    }

    .tab-btn.active {
        background: white;
        color: #3b82f6;
        border-bottom: 3px solid #3b82f6;
    }

    .tab-content {
        padding: 1.5rem;
        min-height: 400px;
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
    }

    /* Data Tables */
    .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;
    }

    .data-table th,
    .data-table td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid #e2e8f0;
    }

    .data-table th {
        background: #f8fafc;
        font-weight: 600;
        color: #374151;
    }

    .data-table tr:hover {
        background: #f8fafc;
    }

    /* Popup/Modal Styles */
    .popup-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        backdrop-filter: blur(4px);
    }

    .popup-content {
        position: relative;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 2rem;
        border-radius: 1rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        width: 95%;
        max-width: 1400px;
        max-height: 90vh;
        overflow-y: auto;
        animation: popupSlideIn 0.3s ease;
    }

    /* For very large screens, use even more space */
    @media (min-width: 1600px) {
        .popup-content {
            width: 90% !important;
            max-width: 1600px !important;
        }
    }

    /* Responsive adjustments */
    @media (max-width: 1200px) {
        .popup-content {
            width: 98vw !important;
            max-width: none !important;
            margin: 1vh auto;
        }
    }

    @keyframes popupSlideIn {
        from { opacity: 0; transform: translate(-50%, -60%); }
        to { opacity: 1; transform: translate(-50%, -50%); }
    }

    .popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e2e8f0;
    }

    .popup-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #64748b;
    }

    /* Form Styles */
    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .form-control {
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 1rem;
    }

    .form-control:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 0.5rem;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #1e40af);
        color: white;
    }

    .btn-secondary {
        background: #f1f5f9;
        color: #475569;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-container {
            padding: 0.5rem;
        }
        
        .quick-actions {
            flex-direction: column;
        }
        
        .action-btn {
            min-width: auto;
        }
        
        .tab-navigation {
            flex-direction: column;
        }
    }

    /* Dark Mode Support */
    .dark .unified-header {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    }

    .dark .metric-section,
    .dark .tab-container,
    .dark .popup-content {
        background: #1f2937;
        color: white;
    }

    .dark .data-table th {
        background: #374151;
        color: white;
    }

    .dark .data-table tr:hover {
        background: #374151;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Unified Header -->
    <div class="unified-header">
        <h1 class="text-4xl font-bold mb-2">
            <i class="fas fa-tachometer-alt mr-3"></i>
            CV Analyzer Hub
        </h1>
        <p class="text-xl opacity-90">Complete CV management and analysis platform</p>
    </div>

    <!-- Quick Actions Bar -->
    <div class="quick-actions">
        <button class="action-btn upload" onclick="openPopup('uploadPopup')">
            <i class="fas fa-upload"></i>
            Upload CVs
        </button>
        <button class="action-btn analyze" onclick="openPopup('analyzePopup')">
            <i class="fas fa-brain"></i>
            Analyze CVs
        </button>
        <button class="action-btn manage" onclick="openPopup('managePopup')">
            <i class="fas fa-cogs"></i>
            Manage Data
        </button>
        <button class="action-btn reports" onclick="openPopup('reportsPopup')">
            <i class="fas fa-chart-bar"></i>
            Reports
        </button>
    </div>

    <!-- Dashboard Grid -->
    <div class="dashboard-grid">
        <!-- Metrics Overview -->
        <div class="metric-section">
            <h3 class="text-xl font-bold mb-4">
                <i class="fas fa-chart-line mr-2"></i>
                Overview Metrics
            </h3>
            <div class="metrics-grid">
                <div class="metric-card blue">
                    <div class="text-3xl font-bold">{{ total_cvs }}</div>
                    <div class="text-sm opacity-90">Total CVs</div>
                </div>
                <div class="metric-card green">
                    <div class="text-3xl font-bold">{{ active_vacancies_count }}</div>
                    <div class="text-sm opacity-90">Active Jobs</div>
                </div>
                <div class="metric-card purple">
                    <div class="text-3xl font-bold">{{ total_companies }}</div>
                    <div class="text-sm opacity-90">Companies</div>
                </div>
                <div class="metric-card orange">
                    <div class="text-3xl font-bold">{{ avg_score|floatformat:1 }}%</div>
                    <div class="text-sm opacity-90">Avg Score</div>
                </div>
            </div>
        </div>

        <!-- Quick Chart -->
        <div class="metric-section">
            <h3 class="text-xl font-bold mb-4">
                <i class="fas fa-chart-area mr-2"></i>
                Performance Trend
            </h3>
            <div class="chart-container" style="position: relative; height: 200px; width: 100%;">
                <canvas id="trendChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Tabbed Content Section -->
    <div class="tab-container">
        <div class="tab-navigation">
            <button class="tab-btn active" onclick="switchTab('cvs')">
                <i class="fas fa-file-alt mr-2"></i>
                CVs Management
            </button>
            <button class="tab-btn" onclick="switchTab('vacancies')">
                <i class="fas fa-briefcase mr-2"></i>
                Vacancies
            </button>
            <button class="tab-btn" onclick="switchTab('companies')">
                <i class="fas fa-building mr-2"></i>
                Companies
            </button>
            <button class="tab-btn" onclick="switchTab('analytics')">
                <i class="fas fa-analytics mr-2"></i>
                Analytics
            </button>
        </div>

        <div class="tab-content">
            <!-- CVs Tab -->
            <div id="cvs-tab" class="tab-pane active">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-semibold">Recent CVs</h4>
                    <button class="btn btn-primary" onclick="openPopup('uploadPopup')">
                        <i class="fas fa-plus"></i>
                        Add CV
                    </button>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Score</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for cv in recent_cvs %}
                        <tr>
                            <td>{{ cv.candidate_name|default:"Unknown" }}</td>
                            <td>
                                <span class="px-2 py-1 rounded-full text-xs 
                                    {% if cv.status == 'analyzed' %}bg-green-100 text-green-800
                                    {% elif cv.status == 'processing' %}bg-yellow-100 text-yellow-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ cv.get_status_display }}
                                </span>
                            </td>
                            <td>{{ cv.latest_analysis.overall_score|default:"--" }}%</td>
                            <td>{{ cv.uploaded_at|date:"M d, Y" }}</td>
                            <td>
                                <button class="btn btn-secondary btn-sm" onclick="viewCV({{ cv.id }})">
                                    <i class="fas fa-eye"></i>
                                    View
                                </button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center text-gray-500">No CVs found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Vacancies Tab -->
            <div id="vacancies-tab" class="tab-pane">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-semibold">Active Vacancies</h4>
                    <button class="btn btn-primary" onclick="openPopup('vacancyPopup')">
                        <i class="fas fa-plus"></i>
                        Add Vacancy
                    </button>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Company</th>
                            <th>Status</th>
                            <th>Applications</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for vacancy in recent_vacancies %}
                        <tr>
                            <td>{{ vacancy.title }}</td>
                            <td>{{ vacancy.company.name }}</td>
                            <td>
                                <span class="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                                    {{ vacancy.get_status_display }}
                                </span>
                            </td>
                            <td>{{ vacancy.applications_count|default:"0" }}</td>
                            <td>
                                <button class="btn btn-secondary btn-sm" onclick="viewVacancy({{ vacancy.id }})">
                                    <i class="fas fa-eye"></i>
                                    View
                                </button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center text-gray-500">No vacancies found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Companies Tab -->
            <div id="companies-tab" class="tab-pane">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-semibold">Companies</h4>
                    <button class="btn btn-primary" onclick="openPopup('companyPopup')">
                        <i class="fas fa-plus"></i>
                        Add Company
                    </button>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Industry</th>
                            <th>Vacancies</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for company in companies %}
                        <tr>
                            <td>{{ company.name }}</td>
                            <td>{{ company.industry }}</td>
                            <td>{{ company.vacancy_count|default:"0" }}</td>
                            <td>
                                <button class="btn btn-secondary btn-sm" onclick="viewCompany({{ company.id }})">
                                    <i class="fas fa-eye"></i>
                                    View
                                </button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4" class="text-center text-gray-500">No companies found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Analytics Tab -->
            <div id="analytics-tab" class="tab-pane">
                <h4 class="text-lg font-semibold mb-4">Analytics Dashboard</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="chart-container" style="position: relative; height: 200px;">
                        <canvas id="skillsChart"></canvas>
                    </div>
                    <div class="chart-container" style="position: relative; height: 200px;">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Popup Modals -->
{% include 'cv_analyzer/components/upload_popup.html' %}
{% include 'cv_analyzer/components/manage_popup.html' %}
{% include 'cv_analyzer/components/analyze_popup.html' %}
{% include 'cv_analyzer/components/reports_popup.html' %}
{% include 'cv_analyzer/components/cv_detail_popup.html' %}
{% include 'cv_analyzer/components/vacancy_popup.html' %}
{% include 'cv_analyzer/components/company_popup.html' %}

{% endblock %}

{% block extra_js %}
<script src="{% static 'cv_analyzer/js/unified_dashboard.js' %}"></script>
<script>
// Tab switching functionality
function switchTab(tabName) {
    // Hide all tab panes
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected tab pane
    document.getElementById(tabName + '-tab').classList.add('active');
    
    // Add active class to clicked button
    event.target.classList.add('active');
}

// Popup functionality
function openPopup(popupId) {
    document.getElementById(popupId).style.display = 'block';
}

function closePopup(popupId) {
    document.getElementById(popupId).style.display = 'none';
}

// Close popup when clicking outside
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('popup-overlay')) {
        event.target.style.display = 'none';
    }
});

// Chart initialization
document.addEventListener('DOMContentLoaded', function() {
    // Trend Chart
    const trendCtx = document.getElementById('trendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: {{ chart_labels|safe }},
            datasets: [{
                label: 'Average Score',
                data: {{ chart_data|safe }},
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true,
                pointRadius: 3,
                pointHoverRadius: 5,
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: '#3b82f6',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#6b7280',
                        font: {
                            size: 11
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    max: 100,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)',
                        drawBorder: false
                    },
                    ticks: {
                        color: '#6b7280',
                        font: {
                            size: 11
                        },
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            animation: {
                duration: 750,
                easing: 'easeInOutQuart'
            }
        }
    });

    // Skills Chart (Doughnut chart)
    const skillsCtx = document.getElementById('skillsChart').getContext('2d');
    new Chart(skillsCtx, {
        type: 'doughnut',
        data: {
            labels: ['JavaScript', 'Python', 'Java', 'React', 'Node.js'],
            datasets: [{
                data: [30, 25, 20, 15, 10],
                backgroundColor: [
                    '#3b82f6',
                    '#10b981',
                    '#f59e0b',
                    '#ef4444',
                    '#8b5cf6'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true,
                        font: {
                            size: 11
                        }
                    }
                }
            }
        }
    });

    // Status Chart (Bar chart)
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'bar',
        data: {
            labels: ['Uploaded', 'Processing', 'Analyzed', 'Matched'],
            datasets: [{
                label: 'CV Status',
                data: [{{ unanalyzed_cvs.count|default:0 }}, 5, {{ analyzed_cvs.count|default:0 }}, {{ matched_cvs_count|default:0 }}],
                backgroundColor: [
                    '#6b7280',
                    '#f59e0b',
                    '#10b981',
                    '#3b82f6'
                ],
                borderRadius: 4,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#6b7280',
                        font: {
                            size: 11
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)',
                        drawBorder: false
                    },
                    ticks: {
                        color: '#6b7280',
                        font: {
                            size: 11
                        }
                    }
                }
            }
        }
    });
});

// View functions
function viewCV(cvId) {
    // Load CV details in popup
    fetch(`/cv/${cvId}/detail/`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('cvDetailContent').innerHTML = html;
            openPopup('cvDetailPopup');
        });
}

function viewVacancy(vacancyId) {
    // Similar implementation for vacancy details
    openPopup('vacancyPopup');
}

function viewCompany(companyId) {
    // Similar implementation for company details
    openPopup('companyPopup');
}
</script>
{% endblock %} 