{% extends 'cv_analyzer/base.html' %}
{% load static %}

{% block title %}Upload CV{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto page-transition">
    <!-- Page Header -->
    <div class="mb-8 text-center">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-3">Upload CV for Analysis</h1>
        <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">Upload your CV to analyze its content and match it with suitable vacancies using our advanced AI-powered analysis.</p>
    </div>

    <!-- Upload Methods Tabs -->
    <div class="mb-8">
        <div class="mb-4 border-b border-gray-200 dark:border-gray-700">
            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="uploadTabs" data-tabs-toggle="#uploadTabContent" role="tablist">
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 rounded-t-lg hover:text-blue-600 hover:border-blue-600 dark:hover:text-blue-500 dark:hover:border-blue-500 transition-all duration-200" id="local-tab" data-tabs-target="#local" type="button" role="tab" aria-controls="local" aria-selected="true">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                            </svg>
                            Local Upload
                        </div>
                    </button>
                </li>
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-blue-600 hover:border-blue-600 dark:hover:text-blue-500 dark:hover:border-blue-500 transition-all duration-200" id="batch-tab" data-tabs-target="#batch" type="button" role="tab" aria-controls="batch" aria-selected="false">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Batch Upload
                        </div>
                    </button>
                </li>
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-blue-600 hover:border-blue-600 dark:hover:text-blue-500 dark:hover:border-blue-500 transition-all duration-200" id="cloud-tab" data-tabs-target="#cloud" type="button" role="tab" aria-controls="cloud" aria-selected="false">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"></path>
                            </svg>
                            Cloud Storage
                        </div>
                    </button>
                </li>
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-blue-600 hover:border-blue-600 dark:hover:text-blue-500 dark:hover:border-blue-500 transition-all duration-200" id="whatsapp-tab" data-tabs-target="#whatsapp" type="button" role="tab" aria-controls="whatsapp" aria-selected="false">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            WhatsApp
                        </div>
                    </button>
                </li>
                <li role="presentation">
                    <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-blue-600 hover:border-blue-600 dark:hover:text-blue-500 dark:hover:border-blue-500 transition-all duration-200" id="email-tab" data-tabs-target="#email" type="button" role="tab" aria-controls="email" aria-selected="false">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Email
                        </div>
                    </button>
                </li>
            </ul>
        </div>

        <div id="uploadTabContent">
            <!-- Local Upload -->
            <div class="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 scale-in" id="local" role="tabpanel" aria-labelledby="local-tab">
                <form action="{% url 'upload_local' %}" method="post" enctype="multipart/form-data" id="uploadForm" class="space-y-6">
                    {% csrf_token %}
                    
                    <!-- Drag and Drop Zone -->
                    <div class="flex items-center justify-center w-full">
                        <label for="dropzone-file" class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 transition-all duration-200 group">
                            <div class="flex flex-col items-center justify-center pt-5 pb-6" id="dropzone-content">
                                <div class="mb-4 text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-500 transition-colors duration-200">
                                    <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                </div>
                                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-500 transition-colors duration-200">
                                    <span class="font-semibold">Click to upload</span> or drag and drop
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">PDF, DOC, or DOCX (MAX. 10MB)</p>
                            </div>
                            <div id="file-preview" class="hidden flex-col items-center justify-center w-full">
                                <div class="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <span id="file-name" class="text-sm"></span>
                                </div>
                                <button type="button" class="mt-4 text-sm text-red-500 hover:text-red-700 transition-colors duration-200 flex items-center space-x-1" id="remove-file">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    <span>Remove file</span>
                                </button>
                            </div>
                            <input id="dropzone-file" type="file" class="hidden" name="cv_file" accept=".pdf,.doc,.docx" />
                        </label>
                    </div>

                    <!-- Progress Bar -->
                    <div class="hidden w-full" id="progress-bar-container">
                        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mb-2 overflow-hidden">
                            <div class="bg-blue-600 h-2.5 rounded-full transition-all duration-500 ease-out" id="progress-bar" style="width: 0%"></div>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400" id="progress-text">Uploading... 0%</p>
                    </div>

                    <!-- Vacancy Selection -->
                    <div class="relative">
                        <label for="vacancy" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Select Vacancy (Optional)</label>
                        <select id="vacancy" name="vacancy" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 transition-colors duration-200">
                            <option value="">General CV Upload (No specific vacancy)</option>
                            {% for vacancy in vacancies %}
                            <option value="{{ vacancy.id }}">{{ vacancy.title }} - {{ vacancy.company.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none mt-8">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                        <small class="text-gray-500 dark:text-gray-400 mt-1 block">Upload your CV without selecting a specific vacancy for general analysis.</small>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="w-full text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:ring-blue-700 transition-all duration-200 hover:-translate-y-0.5 active:translate-y-0 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:translate-y-0" id="submit-button" disabled>
                        <span class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                            </svg>
                            Upload and Analyze
                        </span>
                    </button>
                </form>
            </div>

            <!-- Batch Upload -->
            <div class="hidden p-4 rounded-lg bg-gray-50 dark:bg-gray-800 scale-in" id="batch" role="tabpanel" aria-labelledby="batch-tab">
                <form action="{% url 'upload_batch' %}" method="post" enctype="multipart/form-data" id="batchUploadForm" class="space-y-6">
                    {% csrf_token %}
                    
                    <!-- Batch Upload Zone -->
                    <div class="flex items-center justify-center w-full">
                        <label for="batch-dropzone" class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 transition-all duration-200 group">
                            <div class="flex flex-col items-center justify-center pt-5 pb-6" id="batch-dropzone-content">
                                <div class="mb-4 text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-500 transition-colors duration-200">
                                    <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-500 transition-colors duration-200">
                                    <span class="font-semibold">Click to upload multiple CVs</span> or drag and drop
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">PDF, DOC, or DOCX (MAX. 50MB total)</p>
                            </div>
                            <div id="batch-file-preview" class="hidden w-full px-6">
                                <div class="max-h-40 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
                                    <ul class="space-y-2" id="batch-file-list"></ul>
                                </div>
                                <button type="button" class="mt-4 text-sm text-red-500 hover:text-red-700 transition-colors duration-200 flex items-center space-x-1 mx-auto" id="clear-batch">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    <span>Clear All Files</span>
                                </button>
                            </div>
                            <input id="batch-dropzone" type="file" class="hidden" name="cv_files" accept=".pdf,.doc,.docx" multiple />
                        </label>
                    </div>

                    <!-- Batch Progress -->
                    <div class="hidden space-y-4" id="batch-progress">
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Overall Progress</span>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300" id="batch-progress-text">0%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 overflow-hidden">
                            <div class="bg-blue-600 h-2.5 rounded-full transition-all duration-500 ease-out" id="batch-progress-bar" style="width: 0%"></div>
                        </div>
                        <div id="file-progress-list" class="space-y-3"></div>
                    </div>

                    <!-- Vacancy Selection -->
                    <div class="relative">
                        <label for="batch-vacancy" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Select Vacancy (Optional)</label>
                        <select id="batch-vacancy" name="vacancy" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 transition-colors duration-200">
                            <option value="">General CV Upload (No specific vacancy)</option>
                            {% for vacancy in vacancies %}
                            <option value="{{ vacancy.id }}">{{ vacancy.title }} - {{ vacancy.company.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none mt-8">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                        <small class="text-gray-500 dark:text-gray-400 mt-1 block">Upload multiple CVs without selecting a specific vacancy for general analysis.</small>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="w-full text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:ring-blue-700 transition-all duration-200 hover:-translate-y-0.5 active:translate-y-0 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:translate-y-0" id="batch-submit-button" disabled>
                        <span class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Upload and Analyze Batch
                        </span>
                    </button>
                </form>
            </div>

            <!-- Cloud Storage -->
            <div class="hidden p-4 rounded-lg bg-gray-50 dark:bg-gray-800 scale-in" id="cloud" role="tabpanel" aria-labelledby="cloud-tab">
                <div class="grid md:grid-cols-2 gap-4">
                    <!-- OneDrive -->
                    <div class="p-6 bg-white rounded-lg border border-gray-200 dark:bg-gray-700 dark:border-gray-600">
                        <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">OneDrive</h3>
                        <p class="mb-4 text-sm text-gray-600 dark:text-gray-400">Connect your OneDrive account to upload CVs directly.</p>
                        <button type="button" class="text-white bg-[#0078D4] hover:bg-[#0078D4]/90 focus:ring-4 focus:outline-none focus:ring-[#0078D4]/50 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:focus:ring-[#0078D4]/55 mr-2 mb-2">
                            <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" fill="currentColor">
                                <path d="M41.52 22.27c.41-1.12.64-2.32.64-3.57 0-5.52-4.48-10-10-10-3.96 0-7.37 2.27-9 5.57-1.84-.64-3.83-1-5.91-1C9.48 13.27 3 19.75 3 27.52c0 7.77 6.48 14.25 14.25 14.25h24.5C45.68 41.77 49 38.45 49 34.52c0-3.93-3.32-7.25-7.25-7.25h-.23z"/>
                            </svg>
                            Connect OneDrive
                        </button>
                    </div>

                    <!-- Google Drive -->
                    <div class="p-6 bg-white rounded-lg border border-gray-200 dark:bg-gray-700 dark:border-gray-600">
                        <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Google Drive</h3>
                        <p class="mb-4 text-sm text-gray-600 dark:text-gray-400">Connect your Google Drive account to upload CVs directly.</p>
                        <button type="button" class="text-white bg-[#4285F4] hover:bg-[#4285F4]/90 focus:ring-4 focus:outline-none focus:ring-[#4285F4]/50 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:focus:ring-[#4285F4]/55 mr-2 mb-2">
                            <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" fill="currentColor">
                                <path d="M47.4 24.7l-7.5 6.2-2.4 2-9.2 7.6L15 32.3l9.2-7.6 8.4-6.9 2.3-1.9z"/>
                                <path d="M28.3 40.5L15 32.3l-9.2 7.6L19 47.7z"/>
                                <path d="M0.6 24.7l7.5-6.2L15 24.7l-7.5 6.2z"/>
                            </svg>
                            Connect Google Drive
                        </button>
                    </div>
                </div>
            </div>

            <!-- WhatsApp -->
            <div class="hidden p-4 rounded-lg bg-gray-50 dark:bg-gray-800 scale-in" id="whatsapp" role="tabpanel" aria-labelledby="whatsapp-tab">
                <div class="text-center p-6 bg-white rounded-lg border border-gray-200 dark:bg-gray-700 dark:border-gray-600">
                    <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">WhatsApp Integration</h3>
                    <p class="mb-4 text-sm text-gray-600 dark:text-gray-400">Send your CV directly through WhatsApp for analysis.</p>
                    <div class="flex justify-center mb-4">
                        <img src="{% static 'images/whatsapp-qr.png' %}" alt="WhatsApp QR Code" class="w-48 h-48">
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Scan this QR code or send your CV to:</p>
                    <p class="mt-2 text-lg font-semibold text-gray-900 dark:text-white">+1 234 567 8900</p>
                </div>
            </div>

            <!-- Email -->
            <div class="hidden p-4 rounded-lg bg-gray-50 dark:bg-gray-800 scale-in" id="email" role="tabpanel" aria-labelledby="email-tab">
                <div class="text-center p-6 bg-white rounded-lg border border-gray-200 dark:bg-gray-700 dark:border-gray-600">
                    <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Email Submission</h3>
                    <p class="mb-4 text-sm text-gray-600 dark:text-gray-400">Send your CV via email for automatic analysis.</p>
                    <div class="mb-4">
                        <p class="text-sm text-gray-600 dark:text-gray-400">Send your CV to:</p>
                        <p class="mt-2 text-lg font-semibold text-gray-900 dark:text-white"><EMAIL></p>
                    </div>
                    <div class="mt-6 text-left">
                        <h4 class="mb-2 text-sm font-medium text-gray-900 dark:text-white">Instructions:</h4>
                        <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400 list-disc list-inside">
                            <li>Attach your CV in PDF, DOC, or DOCX format</li>
                            <li>Use the subject line: "CV Analysis - [Your Name]"</li>
                            <li>Include the vacancy ID in the email body (if applicable)</li>
                            <li>You'll receive the analysis results via email</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tips Section -->
    <div class="mt-12">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 text-center">Tips for Better Analysis</h2>
        <div class="grid md:grid-cols-3 gap-6">
            <div class="p-6 bg-white rounded-lg border border-gray-200 dark:bg-gray-700 dark:border-gray-600 transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Clear Formatting</h3>
                <p class="text-gray-600 dark:text-gray-400">Ensure your CV has clear sections and consistent formatting for better analysis.</p>
            </div>
            <div class="p-6 bg-white rounded-lg border border-gray-200 dark:bg-gray-700 dark:border-gray-600 transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Keywords</h3>
                <p class="text-gray-600 dark:text-gray-400">Include relevant keywords from the job description to improve matching.</p>
            </div>
            <div class="p-6 bg-white rounded-lg border border-gray-200 dark:bg-gray-700 dark:border-gray-600 transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">File Format</h3>
                <p class="text-gray-600 dark:text-gray-400">Use PDF format for best results and to maintain formatting consistency.</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const dropzone = document.getElementById('dropzone-file');
    const dropzoneLabel = dropzone.parentElement;
    const dropzoneContent = document.getElementById('dropzone-content');
    const filePreview = document.getElementById('file-preview');
    const fileName = document.getElementById('file-name');
    const removeFile = document.getElementById('remove-file');
    const submitButton = document.getElementById('submit-button');
    const progressBarContainer = document.getElementById('progress-bar-container');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const form = document.getElementById('uploadForm');

    // Drag and drop functionality
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropzoneLabel.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        dropzoneLabel.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropzoneLabel.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        dropzoneLabel.classList.add('border-blue-500');
    }

    function unhighlight(e) {
        dropzoneLabel.classList.remove('border-blue-500');
    }

    dropzoneLabel.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        handleFiles(files);
    }

    dropzone.addEventListener('change', function(e) {
        handleFiles(this.files);
    });

    function handleFiles(files) {
        if (files.length > 0) {
            const file = files[0];
            if (validateFile(file)) {
                showFilePreview(file);
                submitButton.disabled = false;
            }
        }
    }

    function validateFile(file) {
        const validTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (!validTypes.includes(file.type)) {
            alert('Please upload a PDF, DOC, or DOCX file.');
            return false;
        }

        if (file.size > maxSize) {
            alert('File size must be less than 10MB.');
            return false;
        }

        return true;
    }

    function showFilePreview(file) {
        dropzoneContent.classList.add('hidden');
        filePreview.classList.remove('hidden');
        fileName.textContent = file.name;
    }

    removeFile.addEventListener('click', function() {
        dropzone.value = '';
        dropzoneContent.classList.remove('hidden');
        filePreview.classList.add('hidden');
        fileName.textContent = '';
        submitButton.disabled = true;
    });

    // Form submission with progress bar
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const xhr = new XMLHttpRequest();

        progressBarContainer.classList.remove('hidden');
        submitButton.disabled = true;

        xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                progressBar.style.width = percentComplete + '%';
                progressText.textContent = `Uploading... ${Math.round(percentComplete)}%`;
            }
        });

        xhr.addEventListener('load', function() {
            if (xhr.status === 200) {
                const response = JSON.parse(xhr.responseText);
                window.location.href = response.redirect_url;
            } else {
                alert('Upload failed. Please try again.');
                progressBarContainer.classList.add('hidden');
                submitButton.disabled = false;
            }
        });

        xhr.addEventListener('error', function() {
            alert('Upload failed. Please try again.');
            progressBarContainer.classList.add('hidden');
            submitButton.disabled = false;
        });

        xhr.open('POST', form.action, true);
        xhr.send(formData);
    });

    // Batch upload functionality
    const batchDropzone = document.getElementById('batch-dropzone');
    const batchDropzoneContent = document.getElementById('batch-dropzone-content');
    const batchFilePreview = document.getElementById('batch-file-preview');
    const batchFileList = document.getElementById('batch-file-list');
    const clearBatch = document.getElementById('clear-batch');
    const batchSubmitButton = document.getElementById('batch-submit-button');
    const batchProgress = document.getElementById('batch-progress');
    const batchProgressBar = document.getElementById('batch-progress-bar');
    const batchProgressText = document.getElementById('batch-progress-text');
    const fileProgressList = document.getElementById('file-progress-list');
    const batchForm = document.getElementById('batchUploadForm');

    let selectedFiles = [];

    function updateBatchFileList() {
        batchFileList.innerHTML = '';
        selectedFiles.forEach((file, index) => {
            const li = document.createElement('li');
            li.className = 'flex items-center justify-between p-2 bg-white dark:bg-gray-700 rounded-lg shadow-sm group hover:shadow-md transition-all duration-200';
            li.innerHTML = `
                <div class="flex items-center space-x-3">
                    <div class="text-gray-400 dark:text-gray-500">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <span class="text-sm text-gray-600 dark:text-gray-300 truncate max-w-[200px]">${file.name}</span>
                </div>
                <button type="button" class="text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 opacity-0 group-hover:opacity-100 transition-all duration-200" onclick="removeBatchFile(${index})">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            `;
            batchFileList.appendChild(li);
        });

        if (selectedFiles.length > 0) {
            batchDropzoneContent.classList.add('hidden');
            batchFilePreview.classList.remove('hidden');
            batchSubmitButton.disabled = false;
        } else {
            batchDropzoneContent.classList.remove('hidden');
            batchFilePreview.classList.add('hidden');
            batchSubmitButton.disabled = true;
        }
    }

    window.removeBatchFile = function(index) {
        selectedFiles.splice(index, 1);
        updateBatchFileList();
    }

    clearBatch.addEventListener('click', function() {
        selectedFiles = [];
        batchDropzone.value = '';
        updateBatchFileList();
    });

    batchDropzone.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        const validFiles = files.filter(file => {
            const isValid = validateFile(file);
            if (!isValid) {
                alert(`Invalid file: ${file.name}`);
            }
            return isValid;
        });
        selectedFiles = [...selectedFiles, ...validFiles];
        updateBatchFileList();
    });

    batchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData();
        selectedFiles.forEach(file => {
            formData.append('cv_files', file);
        });
        formData.append('vacancy', document.getElementById('batch-vacancy').value);

        const xhr = new XMLHttpRequest();
        batchProgress.classList.remove('hidden');
        batchSubmitButton.disabled = true;

        // Initialize progress bars for each file
        fileProgressList.innerHTML = selectedFiles.map(file => `
            <div class="file-progress bg-white dark:bg-gray-700 rounded-lg p-3 shadow-sm">
                <div class="flex justify-between mb-2">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="text-sm text-gray-600 dark:text-gray-300 truncate max-w-[200px]">${file.name}</span>
                    </div>
                    <span class="text-xs text-gray-500 dark:text-gray-400">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-600 overflow-hidden">
                    <div class="bg-blue-600 h-1.5 rounded-full transition-all duration-500 ease-out" style="width: 0%"></div>
                </div>
            </div>
        `).join('');

        xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                batchProgressBar.style.width = percentComplete + '%';
                batchProgressText.textContent = `${Math.round(percentComplete)}%`;
                
                // Update individual file progress (approximate)
                const fileProgress = Math.round(percentComplete);
                document.querySelectorAll('.file-progress').forEach(div => {
                    div.querySelector('.bg-blue-600').style.width = `${fileProgress}%`;
                    div.querySelector('span:last-child').textContent = `${fileProgress}%`;
                });
            }
        });

        xhr.addEventListener('load', function() {
            if (xhr.status === 200) {
                const response = JSON.parse(xhr.responseText);
                window.location.href = response.redirect_url;
            } else {
                alert('Batch upload failed. Please try again.');
                batchProgress.classList.add('hidden');
                batchSubmitButton.disabled = false;
            }
        });

        xhr.addEventListener('error', function() {
            alert('Batch upload failed. Please try again.');
            batchProgress.classList.add('hidden');
            batchSubmitButton.disabled = false;
        });

        xhr.open('POST', batchForm.action, true);
        xhr.send(formData);
    });
});
</script>
{% endblock %}