{% extends "cv_analyzer/base.html" %}
{% load static %}
{% load form_tags %}

{% block title %}{{ vacancy.title }} - Candidates - C<PERSON> Analyzer{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .candidate-card {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .candidate-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .dark .candidate-card {
        background: #1f2937;
        border-color: #374151;
    }
    
    .score-badge {
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-weight: 600;
        font-size: 0.875rem;
    }
    
    .score-excellent {
        background: #d1fae5;
        color: #065f46;
    }
    
    .score-good {
        background: #dbeafe;
        color: #1e40af;
    }
    
    .score-fair {
        background: #fef3c7;
        color: #92400e;
    }
    
    .score-poor {
        background: #fee2e2;
        color: #991b1b;
    }
    
    .dark .score-excellent {
        background: #047857;
        color: #d1fae5;
    }
    
    .dark .score-good {
        background: #1e40af;
        color: #dbeafe;
    }
    
    .dark .score-fair {
        background: #d97706;
        color: #fef3c7;
    }
    
    .dark .score-poor {
        background: #dc2626;
        color: #fee2e2;
    }
    
    .metric-card {
        background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
        border: none;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        border-radius: 0.75rem;
        padding: 1.5rem;
        color: white;
    }
    
    .metric-card.blue {
        --gradient-start: #3b82f6;
        --gradient-end: #1d4ed8;
    }
    
    .metric-card.green {
        --gradient-start: #10b981;
        --gradient-end: #047857;
    }
    
    .metric-card.yellow {
        --gradient-start: #f59e0b;
        --gradient-end: #d97706;
    }
    
    .metric-card.red {
        --gradient-start: #ef4444;
        --gradient-end: #dc2626;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        border: 1px solid;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        border-color: transparent;
    }
    
    .btn-secondary {
        background: white;
        color: #6b7280;
        border-color: #d1d5db;
    }
    
    .dark .btn-secondary {
        background: #374151;
        color: #d1d5db;
        border-color: #4b5563;
    }
</style>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{% url 'vacancy_management' %}" class="text-blue-600 hover:text-blue-700">
                            <i class="fas fa-briefcase mr-2"></i>Vacancy Management
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                            <span class="text-gray-500 dark:text-gray-400">{{ vacancy.title }}</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                            <span class="text-gray-900 dark:text-white font-medium">Candidates</span>
                        </div>
                    </li>
                </ol>
            </nav>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                Candidates for {{ vacancy.title }}
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">
                {{ vacancy.company.name }} • {{ total_applications }} application{{ total_applications|pluralize }}
            </p>
        </div>
        <div class="flex gap-3">
            <button class="btn btn-secondary" onclick="exportCandidates()">
                <i class="fas fa-download"></i>Export List
            </button>
            <button id="reAnalyzeBtn" class="btn" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white; border-color: transparent;" onclick="reAnalyzeCandidates()">
                <i class="fas fa-sync-alt"></i>Re-analyze Candidates
            </button>
            <button id="extractDataBtn" class="btn" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white; border-color: transparent;" onclick="extractMissingData()">
                <i class="fas fa-search-plus"></i>Extract Missing Data
            </button>
            <a href="{% url 'vacancy_management' %}" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i>Back to Vacancies
            </a>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div class="metric-card blue">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold opacity-90 mb-1">Total CVs</h3>
                    <div class="text-3xl font-bold">{{ total_cvs }}</div>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-file-alt"></i>
                </div>
            </div>
        </div>

        <div class="metric-card" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold opacity-90 mb-1">Analyzed</h3>
                    <div class="text-3xl font-bold">{{ analyzed_count }}</div>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>
        
        <div class="metric-card green">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold opacity-90 mb-1">Excellent Matches</h3>
                    <div class="text-3xl font-bold">{{ excellent_matches }}</div>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-star"></i>
                </div>
            </div>
        </div>
        
        <div class="metric-card yellow">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold opacity-90 mb-1">Good Matches</h3>
                    <div class="text-3xl font-bold">{{ good_matches }}</div>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-thumbs-up"></i>
                </div>
            </div>
        </div>
        
        <div class="metric-card red">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold opacity-90 mb-1">Needs Review</h3>
                    <div class="text-3xl font-bold">{{ fair_matches|add:poor_matches }}</div>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex flex-col lg:flex-row gap-4">
            <div class="flex-1">
                <div class="relative">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    <input type="text" id="searchInput" placeholder="Search candidates..." value="{{ search_query|default:'' }}" class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
            </div>
            
            <div class="flex gap-4">
                <select id="statusFilter" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="">All Matches</option>
                    <option value="excellent" {% if status_filter == "excellent" %}selected{% endif %}>Excellent (80%+)</option>
                    <option value="good" {% if status_filter == "good" %}selected{% endif %}>Good (70-79%)</option>
                    <option value="fair" {% if status_filter == "fair" %}selected{% endif %}>Fair (60-69%)</option>
                    <option value="poor" {% if status_filter == "poor" %}selected{% endif %}>Poor (<60%)</option>
                </select>
                
                <button class="btn btn-secondary" onclick="clearFilters()">
                    <i class="fas fa-times"></i>Clear Filters
                </button>
            </div>
        </div>
    </div>
    
    <!-- Unified CV Candidates Section -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-users mr-2 text-blue-600"></i>All CV Candidates (Sorted by Compatibility)
            </h2>
            <div class="flex items-center gap-4">
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ total_cvs }} total CV{{ total_cvs|pluralize }}</span>
                <div class="flex gap-2">
                    <span class="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full text-xs">
                        {{ analyzed_count }} analyzed
                    </span>
                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 rounded-full text-xs">
                        {{ unanalyzed_count }} unanalyzed
                    </span>
                </div>
            </div>
        </div>
        
        <div class="space-y-4">
            {% for cv in all_cvs %}
            <div class="candidate-card {% if cv.compatibility_score %}border-l-4 {% if cv.compatibility_score >= 80 %}border-green-500{% elif cv.compatibility_score >= 70 %}border-blue-500{% elif cv.compatibility_score >= 60 %}border-yellow-500{% else %}border-red-500{% endif %}{% endif %}" data-candidate-id="{{ cv.cv_id }}">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 {% if cv.compatibility_score >= 80 %}bg-green-500{% elif cv.compatibility_score >= 70 %}bg-blue-500{% elif cv.compatibility_score >= 60 %}bg-yellow-500{% elif cv.compatibility_score %}bg-red-500{% else %}bg-gray-500{% endif %} rounded-full flex items-center justify-center text-white font-bold text-lg">
                            {{ cv.candidate_name|first|upper }}
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                {{ cv.candidate_name }}
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400">
                                {% if cv.candidate_email and cv.candidate_email != 'not specified' %}
                                    {{ cv.candidate_email }}
                                {% else %}
                                    <span class="text-orange-500 font-medium">📧 Email missing - extraction needed</span>
                                {% endif %}
                            </p>
                            <div class="flex items-center gap-4 mt-2 text-sm text-gray-500">
                                {% if cv.phone_number and cv.phone_number != 'not specified' %}
                                    <span><i class="fas fa-phone mr-1"></i>{{ cv.phone_number }}</span>
                                {% else %}
                                    <span class="text-orange-500">📱 Phone missing</span>
                                {% endif %}
                                {% if cv.location and cv.location != 'not specified' %}
                                    <span><i class="fas fa-map-marker-alt mr-1"></i>{{ cv.location }}</span>
                                {% endif %}
                                <span><i class="fas fa-briefcase mr-1"></i>{{ cv.years_of_experience }} years exp</span>
                                <span><i class="fas fa-calendar-alt mr-1"></i>{{ cv.days_since_upload }} days ago</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        {% if cv.compatibility_score %}
                            <div class="text-right">
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ cv.compatibility_score }}%
                                </div>
                                <div class="score-badge {% if cv.compatibility_score >= 80 %}score-excellent{% elif cv.compatibility_score >= 70 %}score-good{% elif cv.compatibility_score >= 60 %}score-fair{% else %}score-poor{% endif %}">
                                    {% if cv.compatibility_score >= 80 %}Excellent{% elif cv.compatibility_score >= 70 %}Good{% elif cv.compatibility_score >= 60 %}Fair{% else %}Poor{% endif %}
                                </div>
                            </div>
                        {% else %}
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-500 dark:text-gray-400">
                                    —
                                </div>
                                <div class="score-badge bg-gray-100 text-gray-600">
                                    Not Analyzed
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                {% if cv.analysis_text %}
                <div class="mb-4">
                    <p class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                        {{ cv.analysis_text }}
                    </p>
                </div>
                {% endif %}
                
                <div class="mb-4">
                    <div class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Profile Details:</div>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600 dark:text-gray-400">
                        <div>
                            <strong>Education:</strong> 
                            {% if cv.education_level and cv.education_level != 'not specified' %}
                                {{ cv.education_level }}
                            {% else %}
                                <span class="text-orange-500">Missing</span>
                            {% endif %}
                        </div>
                        <div>
                            <strong>Skills:</strong> 
                            {% if cv.skills and cv.skills != 'not specified' %}
                                {{ cv.skills|truncatechars:50 }}
                            {% else %}
                                <span class="text-orange-500">Missing</span>
                            {% endif %}
                        </div>
                        <div>
                            <strong>Source:</strong> {{ cv.source }}
                        </div>
                        <div>
                            <strong>Status:</strong> 
                            {% if cv.is_analyzed %}
                                <span class="text-green-600">✓ Analyzed</span>
                            {% else %}
                                <span class="text-yellow-600">⏳ Pending</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-600">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        <i class="fas fa-file-pdf mr-2"></i>{{ cv.file_name|default:"CV File" }}
                    </div>
                    
                    <div class="flex gap-2">
                        <button class="btn btn-secondary" onclick="viewCVDetails({{ cv.cv_id }})">
                            <i class="fas fa-eye"></i>View CV
                        </button>
                        {% if not cv.is_analyzed %}
                            <button class="btn" style="background: linear-gradient(135deg, #10b981, #047857); color: white; border-color: transparent;" onclick="analyzeCV({{ cv.cv_id }}, {{ vacancy.id }})">
                                <i class="fas fa-chart-line"></i>Analyze
                            </button>
                        {% else %}
                            <button class="btn btn-primary" onclick="contactCandidate('{{ cv.candidate_email }}', '{{ cv.candidate_name }}')">
                                <i class="fas fa-envelope"></i>Contact
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="text-center py-12">
                <div class="text-gray-500 dark:text-gray-400">
                    <i class="fas fa-file-upload text-6xl mb-4 opacity-30"></i>
                    <h3 class="text-lg font-medium mb-2">No CVs found</h3>
                    <p class="text-sm mb-4">No CVs have been uploaded to the system yet.</p>
                    <a href="{% url 'upload_cv' %}" class="btn btn-primary">
                        <i class="fas fa-upload mr-2"></i>Upload First CV
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Universal Modal for CV Details and other content -->
<div id="universalModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden m-4">
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-600">
            <h2 id="modalTitle" class="text-xl font-semibold text-gray-900 dark:text-white"></h2>
            <button onclick="hideModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="modalContent" class="p-6">
            <!-- Content will be dynamically inserted here -->
        </div>
    </div>
</div>

<!-- Re-analysis Progress Modal -->
<div id="progressModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl w-full mx-4">
        <div class="text-center">
            <div class="mb-4">
                <i id="progressIcon" class="fas fa-sync-alt fa-spin text-4xl text-purple-500 mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Re-analyzing Candidates</h3>
                <p class="text-gray-600 dark:text-gray-400 mt-2">AI is re-evaluating all candidates against the vacancy requirements...</p>
            </div>
            
            <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                <div id="progressBar" class="bg-purple-600 h-2.5 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
            
            <div id="progressText" class="text-sm text-gray-600 dark:text-gray-400">
                Initializing analysis...
            </div>
            
            <!-- Progress Log -->
            <div class="mt-6">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Real-time Progress:</h4>
                <div id="progressLog" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 h-48 overflow-y-auto text-left text-sm">
                    <!-- Progress messages will be added here -->
                </div>
            </div>
            
            <div id="progressStats" class="mt-4 grid grid-cols-3 gap-4 text-xs">
                <div class="text-center">
                    <div id="processedCount" class="font-bold text-blue-600">0</div>
                    <div class="text-gray-500">Processed</div>
                </div>
                <div class="text-center">
                    <div id="successCount" class="font-bold text-green-600">0</div>
                    <div class="text-gray-500">Updated</div>
                </div>
                <div class="text-center">
                    <div id="errorCount" class="font-bold text-red-600">0</div>
                    <div class="text-gray-500">Errors</div>
                </div>
            </div>
            
            <!-- Cancel Button -->
            <div class="mt-6">
                <button id="cancelAnalysisBtn" onclick="cancelAnalysis()" class="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors duration-200">
                    <i class="fas fa-stop mr-2"></i>Cancel Analysis
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search and filter functionality
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    
    function applyFilters() {
        const params = new URLSearchParams();
        
        if (searchInput.value) params.append('search', searchInput.value);
        if (statusFilter.value) params.append('status_filter', statusFilter.value);
        
        window.location.href = '{% url "vacancy_candidates" vacancy.id %}' + (params.toString() ? '?' + params.toString() : '');
    }
    
    // Debounced search
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(applyFilters, 500);
    });
    
    statusFilter.addEventListener('change', applyFilters);
    
    // Animate candidate cards
    const candidateCards = document.querySelectorAll('.candidate-card');
    candidateCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

function clearFilters() {
    window.location.href = '{% url "vacancy_candidates" vacancy.id %}';
}

function viewCVDetails(cvId) {
    showCVDetailsPopup(cvId);
}

function viewAnalysis(analysisId) {
    showAnalysisPopup(analysisId);
}

// Enhanced CV Details Popup
async function showCVDetailsPopup(cvId) {
    try {
        // Show loading modal first
        showModal('Loading CV details...', '<div class="text-center"><i class="fas fa-spinner fa-spin text-4xl text-blue-500 mb-4"></i><p>Fetching CV information...</p></div>');
        
        // Fetch CV details via JSON API
        const response = await fetch(`/cv/${cvId}/detail/json/`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const cvData = await response.json();
        
        console.log('CV Data received:', cvData); // Debug log
        
        // Validate and sanitize data
        const safeData = {
            candidate_name: cvData.candidate_name || 'Unknown Candidate',
            email: cvData.email || 'No email provided',
            status: cvData.status || 'unknown',
            analysis: cvData.analysis || null,
            compatibilities: Array.isArray(cvData.compatibilities) ? cvData.compatibilities : [],
            file_name: cvData.file_name || 'Unknown',
            source: cvData.source || 'Unknown',
            uploaded_at: cvData.uploaded_at || new Date().toISOString(),
            file_size: cvData.file_size || 'Unknown',
            file_url: cvData.file_url || null
        };
        
        // Create enhanced CV details HTML
                const cvDetailsHTML = `
            <div class="max-w-4xl max-h-[80vh] overflow-y-auto">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">${safeData.candidate_name}</h2>
                        <p class="text-gray-600 dark:text-gray-400">${safeData.email}</p>
                    </div>
                    <div class="flex gap-2">
                        <span class="px-3 py-1 rounded-full text-sm font-medium ${getStatusBadgeClass(safeData.status)}">
                            ${safeData.status.charAt(0).toUpperCase() + safeData.status.slice(1)}
                        </span>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                            <i class="fas fa-chart-line mr-2"></i>Analysis Scores
                        </h3>
                        ${safeData.analysis ? `
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span>Overall Score:</span>
                                    <span class="font-bold text-blue-600">${safeData.analysis.overall_score || 0}%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Content Score:</span>
                                    <span class="font-bold text-green-600">${safeData.analysis.content_score || 0}%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Skills Score:</span>
                                    <span class="font-bold text-purple-600">${safeData.analysis.skills_score || 0}%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Format Score:</span>
                                    <span class="font-bold text-orange-600">${safeData.analysis.format_score || 0}%</span>
                                </div>
                            </div>
                        ` : '<p class="text-gray-500">No analysis available</p>'}
                    </div>
                    
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                            <i class="fas fa-target mr-2"></i>Compatibility
                        </h3>
                        ${safeData.compatibilities.length > 0 ? `
                            <div class="space-y-2">
                                ${safeData.compatibilities.map(comp => `
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm">${comp.vacancy_title || 'Unknown Position'}</span>
                                        <span class="font-bold ${getCompatibilityColor(comp.compatibility || 0)}">${comp.compatibility || 0}%</span>
                                    </div>
                                `).join('')}
                            </div>
                        ` : '<p class="text-gray-500">No compatibility data available</p>'}
                    </div>
                </div>
                
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                        <i class="fas fa-info-circle mr-2"></i>CV Information
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div><strong>File:</strong> ${safeData.file_name}</div>
                        <div><strong>Source:</strong> ${safeData.source}</div>
                        <div><strong>Uploaded:</strong> ${new Date(safeData.uploaded_at).toLocaleDateString()}</div>
                        <div><strong>Size:</strong> ${safeData.file_size}</div>
                    </div>
                </div>
                
                <div class="flex justify-between gap-4">
                    <button onclick="downloadCV(${cvId})" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-download mr-2"></i>Download CV
                  
                    <button onclick="contactCandidate('${safeData.email}', '${safeData.candidate_name}')" class="flex-1 bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-envelope mr-2"></i>Contact
                    </button>
                </div>
            </div>
        `;
        
        // Show the CV details in modal
        showModal('CV Details', cvDetailsHTML);
        
    } catch (error) {
        console.error('Error fetching CV details:', error);
        showModal('Error', `
            <div class="text-center text-red-600 dark:text-red-400">
                <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                <p class="mb-4">Failed to load CV details</p>
                <p class="text-sm">${error.message}</p>
                <button onclick="hideModal()" class="mt-4 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg">
                    Close
                </button>
            </div>
        `);
    }
}

// Enhanced Analysis Popup
async function showAnalysisPopup(analysisId) {
    try {
        // Show loading modal first
        showModal('Loading Analysis...', '<div class="text-center"><i class="fas fa-spinner fa-spin text-4xl text-purple-500 mb-4"></i><p>Fetching analysis details...</p></div>');
        
        // For now, open in new tab (can be enhanced later with AJAX)
        window.open(`/analysis/${analysisId}/`, '_blank');
        hideModal();
        
    } catch (error) {
        console.error('Error fetching analysis details:', error);
        showModal('Error', `
            <div class="text-center text-red-600 dark:text-red-400">
                <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                <p class="mb-4">Failed to load analysis details</p>
                <p class="text-sm">${error.message}</p>
                <button onclick="hideModal()" class="mt-4 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg">
                    Close
                </button>
            </div>
        `);
    }
}

// Helper functions
function getStatusBadgeClass(status) {
    const classes = {
        'uploaded': 'bg-blue-100 text-blue-800',
        'processing': 'bg-yellow-100 text-yellow-800',
        'analyzed': 'bg-green-100 text-green-800',
        'matched': 'bg-purple-100 text-purple-800',
        'rejected': 'bg-red-100 text-red-800',
        'incomplete': 'bg-gray-100 text-gray-800',
        'unknown': 'bg-gray-100 text-gray-800'
    };
    return classes[status || 'unknown'] || 'bg-gray-100 text-gray-800';
}

function getCompatibilityColor(score) {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-blue-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-red-600';
}

function downloadCV(cvId) {
    // For now, open the file directly (can be enhanced with a proper download endpoint)
    fetch(`/cv/${cvId}/detail/json/`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.file_url) {
                const link = document.createElement('a');
                link.href = data.file_url;
                link.download = data.file_name || `cv_${cvId}.pdf`;
                document.body.appendChild(link); // Add to DOM for Firefox compatibility
                link.click();
                document.body.removeChild(link); // Clean up
                showAlert('CV download started', 'success');
            } else {
                showAlert('CV file not available for download', 'warning');
            }
        })
        .catch(error => {
            console.error('Error downloading CV:', error);
            showAlert(`Failed to download CV: ${error.message}`, 'error');
        });
}

function openCVInNewTab(cvId) {
    window.open(`/cv/${cvId}/detail/`, '_blank');
}

// Universal Modal Functions
function showModal(title, content) {
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalContent').innerHTML = content;
    document.getElementById('universalModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
}

function hideModal() {
    document.getElementById('universalModal').classList.add('hidden');
    document.body.style.overflow = 'auto'; // Restore scrolling
}

// Close modal when clicking outside
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('universalModal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideModal();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideModal();
        }
    });
});

function contactCandidate(email, name) {
    const subject = encodeURIComponent('Regarding your application for {{ vacancy.title }}');
    const body = encodeURIComponent('Dear ' + name + ',\n\nThank you for your interest in our position. We would like to discuss your application further.\n\nBest regards');
    
    window.location.href = 'mailto:' + email + '?subject=' + subject + '&body=' + body;
}

function exportCandidates() {
    // Create export data
    const candidates = Array.from(document.querySelectorAll('.candidate-card')).map(card => {
        const name = card.querySelector('h3').textContent.trim();
        const email = card.querySelector('.text-gray-600').textContent.trim();
        const score = card.querySelector('.text-2xl').textContent.trim();
        const status = card.querySelector('.score-badge').textContent.trim();
        
        return { name, email, score, status };
    });
    
    const exportData = {
        vacancy: '{{ vacancy.title }}',
        company: '{{ vacancy.company.name }}',
        export_date: new Date().toISOString(),
        total_candidates: candidates.length,
        candidates: candidates
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'candidates_' + new Date().toISOString().split('T')[0] + '.json';
    a.click();
    URL.revokeObjectURL(url);
    
    showAlert('Candidates list exported successfully!', 'success');
}

// Re-analysis functionality
let analysisController = null; // For cancelling requests
let currentSessionId = null; // Track current analysis session
let cvNamesMap = {}; // Map CV IDs to names

// Create CV names mapping from page data
function createCVNamesMapping() {
    cvNamesMap = {};
    
    // Get names from analyzed candidates
    document.querySelectorAll('.candidate-card').forEach(card => {
        const cvId = card.dataset.candidateId;
        const nameElement = card.querySelector('h3');
        if (cvId && nameElement) {
            cvNamesMap[cvId] = nameElement.textContent.trim();
        }
    });
    
    // Get names from all CVs section
    const allCvCards = document.querySelectorAll('[data-cv-id]');
    allCvCards.forEach(card => {
        const cvId = card.dataset.cvId;
        const nameElement = card.querySelector('h4');
        if (cvId && nameElement) {
            cvNamesMap[cvId] = nameElement.textContent.trim();
        }
    });
    
    // Also extract from CV cards in the all CVs section
    document.querySelectorAll('.bg-white.dark\\:bg-gray-800.rounded-lg.p-4.border').forEach(card => {
        const nameElement = card.querySelector('h4');
        const analyzeButton = card.querySelector('button[onclick*="analyzeCV"]');
        if (nameElement && analyzeButton) {
            const onclickAttr = analyzeButton.getAttribute('onclick');
            const cvIdMatch = onclickAttr.match(/analyzeCV\((\d+),/);
            if (cvIdMatch) {
                const cvId = cvIdMatch[1];
                cvNamesMap[cvId] = nameElement.textContent.trim();
            }
        }
    });
    
    console.log('CV Names mapping created:', cvNamesMap);
}

// Helper function to get CV name or fallback to ID
function getCVDisplayName(cvId) {
    return cvNamesMap[cvId] || `CV ${cvId}`;
}

// Make sure this function is globally accessible
window.reAnalyzeCandidates = async function reAnalyzeCandidates() {
    // Show analysis options dialog
    const analysisOption = await showAnalysisOptionsDialog();
    
    if (!analysisOption) {
        return;
    }
    
    // Create CV names mapping before starting analysis
    createCVNamesMapping();
    
    // Get all CV IDs from the current candidates
    const candidateCards = document.querySelectorAll('.candidate-card');
    const cvIds = Array.from(candidateCards).map(card => parseInt(card.dataset.candidateId));
    
    if (cvIds.length === 0) {
        showAlert('No candidates found to re-analyze.', 'warning');
        return;
    }
    
    // Show progress modal
    showProgress();
    updateProgress(0, `Starting re-analysis of ${cvIds.length} candidates...`);
    
    // Test API connectivity first
    console.log('Testing API connectivity...');
    const connectivityTest = await testApiConnectivity();
    console.log('API connectivity test result:', connectivityTest);
    
    if (!connectivityTest) {
        hideProgress();
        showAlert('API connectivity test failed. Please check your connection.', 'error');
        return;
    }
    
    updateProgress(10, 'API connectivity verified, starting analysis...');
    addProgressMessage('🔧 Starting analysis configuration...');
    
    try {
        // Create AbortController for manual cancellation only (no automatic timeout for streaming)
        analysisController = new AbortController();
        
        // Add user feedback messages
        addProgressMessage(`📋 Preparing to analyze ${cvIds.length} CVs with comprehensive extraction`);
        addProgressMessage(`🎯 Using ${analysisOption === 'ai' ? 'Comprehensive AI Analysis' : 'Fast Analysis'} method`);
        addProgressMessage(`📚 Deep extraction: Contact info, Education, Work history, Skills, Certifications, Languages, Projects + data validation`);
        addProgressMessage('🌐 Connecting to backend server...');
        
        // Add animated connection with dots
        setTimeout(() => addProgressMessage('🌐 Connecting to backend server....'), 500);
        setTimeout(() => addProgressMessage('🌐 Connecting to backend server.....'), 1000);
        setTimeout(() => addProgressMessage('📡 Server connection established!'), 1500);
        
        console.log('Starting streaming fetch request...');
        
        // Use all CVs for analysis with comprehensive extraction
        const analysisPayload = {
            cv_ids: cvIds, // All CVs
            vacancy_ids: [{{ vacancy.id }}],
            analysis_type: analysisOption, // 'fast' or 'ai'
            stream_mode: true,  // Enable streaming mode
            reanalyze: true,  // Force complete re-analysis
            force_cv_reprocessing: true,  // NEW: Force complete CV data extraction
            extract_all_fields: true,
            include_education_timeline: true,
            include_work_timeline: true,
            extract_skills_detailed: true,
            extract_certifications: true,
            extract_languages: true,
            extract_projects: true,
            comprehensive_analysis: true,
            force_reextraction: true,  // Force re-extraction of all data
            force_complete_extraction: true,
            extract_contact_info: true,
            extract_personal_details: true,
            extract_all_text_content: true,
            deep_content_analysis: true,
            extract_hidden_fields: true,
            validate_extracted_data: true,
            fill_missing_fields: true,
            enhance_existing_data: true,
            fix_incomplete_data: true,
            normalize_field_formats: true
        };
        console.log('Analysis payload:', analysisPayload);
        console.log('Payload size:', JSON.stringify(analysisPayload).length, 'bytes');
        
        console.log('Making streaming API request to start-ai-analysis endpoint...');
        
        const response = await fetch('/api/start-ai-analysis/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken(),
                'Accept': 'text/event-stream'
            },
            body: JSON.stringify(analysisPayload),
            signal: analysisController.signal
        });
        
        console.log('Streaming fetch request started');
        
        console.log('Response status:', response.status);
        console.log('Response headers:', [...response.headers.entries()]);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        // Verify we got a streaming response
        const contentType = response.headers.get('Content-Type');
        console.log('Response Content-Type:', contentType);
        
        if (!contentType || !contentType.includes('text/event-stream')) {
            console.error('❌ Expected streaming response but got:', contentType);
            console.error('❌ This usually means authentication failed or wrong endpoint');
            throw new Error('Server did not return streaming response. Please refresh the page and try again.');
        }
        
        console.log('✅ Confirmed streaming response received');
        addProgressMessage('✅ Streaming connection confirmed');
        
        // Handle streaming response
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
            let processed = 0;
            let successful = 0;
            let errors = 0;
        let totalCVs = cvIds.length;
        
        console.log('Starting to read streaming response...');
        addProgressMessage('📡 Connected to analysis stream...');
        
        while (true) {
            const { done, value } = await reader.read();
            
            if (done) {
                console.log('Stream reading completed');
                break;
            }
            
            // Decode the chunk and add to buffer
            buffer += decoder.decode(value, { stream: true });
            
            // Process complete lines
            const lines = buffer.split('\n');
            buffer = lines.pop(); // Keep incomplete line in buffer
            
            for (const line of lines) {
                if (line.trim() === '') continue;
                
                if (line.startsWith('data: ')) {
                    try {
                        const eventData = JSON.parse(line.slice(6));
                        console.log('📡 Streaming event received:', eventData);
                        
                        // Handle different event types
                        switch (eventData.type) {
                            case 'start':
                                totalCVs = eventData.cv_ids.length;
                                currentSessionId = eventData.session_id; // Store session ID
                                console.log('📡 Session ID stored:', currentSessionId);
                                addProgressMessage(`📋 Starting analysis of ${totalCVs} CVs: [${eventData.cv_ids.join(', ')}]`);
                                addProgressMessage(`🎯 Analysis type: ${eventData.analysis_type.toUpperCase()}`);
                                updateProgress(5, 'Analysis pipeline initialized...');
                                break;
                                
                            case 'cv_start':
                                const cvDisplayName = getCVDisplayName(eventData.cv_id);
                                addProgressMessage(`🔍 Starting analysis of ${cvDisplayName}... (${eventData.progress}/${eventData.total})`);
                                const startProgressPercent = Math.round((eventData.progress / eventData.total) * 80) + 10;
                                updateProgress(startProgressPercent, `Processing ${cvDisplayName}...`);
                                break;
                                
                            case 'cv_analyzing':
                                const analyzingCvName = getCVDisplayName(eventData.cv_id);
                                addProgressMessage(`${eventData.method === 'AI' ? '🤖' : '⚡'} Deep analysis of ${analyzingCvName} - extracting ALL available data...`);
                                // Add detailed extraction messages with specific fields
                                setTimeout(() => {
                                    addProgressMessage(`📧 Extracting contact info: Email, phone, location for ${analyzingCvName}...`);
                                }, 800);
                                setTimeout(() => {
                                    addProgressMessage(`📚 Extracting complete education: Degrees, institutions, dates, GPA for ${analyzingCvName}...`);
                                }, 1600);
                                setTimeout(() => {
                                    addProgressMessage(`💼 Extracting work timeline: Companies, roles, dates, achievements for ${analyzingCvName}...`);
                                }, 2400);
                                setTimeout(() => {
                                    addProgressMessage(`🎯 Analyzing skills: Technical, soft skills, certifications, languages for ${analyzingCvName}...`);
                                }, 3200);
                                setTimeout(() => {
                                    addProgressMessage(`🔍 Validating extracted data and filling gaps for ${analyzingCvName}...`);
                                }, 4000);
                                break;
                                
                            case 'cv_complete':
                                const result = eventData.result;
                                const emoji = result.score >= 85 ? '🎯' : result.score >= 75 ? '✅' : result.score >= 65 ? '⚠️' : '❌';
                                
                                processed++;
                                if (result.status === 'success') {
                                    successful++;
                                }
                                
                                const completeCvName = getCVDisplayName(eventData.cv_id);
                                
                                // Show what was extracted/fixed
                                if (result.extracted_fields) {
                                    const extractedInfo = [];
                                    if (result.extracted_fields.email) extractedInfo.push('📧 Email');
                                    if (result.extracted_fields.phone) extractedInfo.push('📱 Phone');
                                    if (result.extracted_fields.education) extractedInfo.push('🎓 Education');
                                    if (result.extracted_fields.skills) extractedInfo.push('🎯 Skills');
                                    
                                    if (extractedInfo.length > 0) {
                                        addProgressMessage(`🔧 Fixed missing data for ${completeCvName}: ${extractedInfo.join(', ')}`);
                                    }
                                }
                                
                                addProgressMessage(`${emoji} ${completeCvName}: ${result.score}% - ${result.recommendation} (${eventData.progress}/${eventData.total})`);
                                
                                const completeProgressPercent = Math.round((eventData.progress / eventData.total) * 80) + 10;
                                updateProgress(completeProgressPercent, `✅ ${completeCvName}: ${result.score}% compatibility`);
                                updateStats(processed, successful, errors);
                                break;
                                
                            case 'cv_error':
                                processed++;
                                errors++;
                                const errorCvName = getCVDisplayName(eventData.cv_id);
                                addProgressMessage(`❌ Error analyzing ${errorCvName}: ${eventData.error} (${eventData.progress}/${eventData.total})`);
                                updateStats(processed, successful, errors);
                                break;
                                
                            case 'cancelled':
                                addProgressMessage(`🛑 Analysis cancelled: ${eventData.message}`);
                                updateProgress(0, 'Analysis cancelled by user');
                                hideProgress();
                                showAlert('Analysis has been cancelled successfully.', 'warning');
                                return; // Exit early
                                
                            case 'complete':
                                addProgressMessage(`🎉 Analysis complete! ${eventData.successful}/${eventData.total_processed} successful`);
                                updateProgress(100, 'Analysis completed successfully!');
                                
                                // Calculate summary
                                const successfulResults = eventData.results.filter(r => r.status === 'success');
                                const avgScore = successfulResults.length > 0 ? 
                                    Math.round(successfulResults.reduce((sum, r) => sum + (r.score || 0), 0) / successfulResults.length) : 0;
                                const highScores = successfulResults.filter(r => (r.score || 0) >= 80).length;
                                const goodScores = successfulResults.filter(r => (r.score || 0) >= 70 && (r.score || 0) < 80).length;
                                
                                addProgressMessage(`📊 Summary: Avg ${avgScore}%, High matches: ${highScores}, Good matches: ${goodScores}`);
                                addProgressMessage('🔄 Refreshing page to show updated results...');
            
            setTimeout(() => {
                hideProgress();
                                    
                                    const summaryMessage = `Re-analysis completed! ${eventData.successful} candidates analyzed successfully.
                                        Average compatibility: ${avgScore}%
                                        High matches (80%+): ${highScores}
                                        Good matches (70-79%): ${goodScores}`;
                                    
                                    showAlert(summaryMessage, 'success');
                
                // Reload the page to show updated results
                setTimeout(() => {
                    window.location.reload();
                                    }, 2500);
            }, 1000);
            
                                return; // Exit the function
                        }
                    } catch (e) {
                        console.error('Error parsing streaming event data:', e, 'Line:', line);
                    }
                }
            }
        }
        
        // If we reach here, the stream ended without completion event
        console.log('Stream ended without completion event');
        addProgressMessage('⚠️ Stream ended unexpectedly');
        hideProgress();
        showAlert('Analysis stream ended unexpectedly. Please refresh the page and try again.', 'warning');
        
    } catch (error) {
        console.error('Re-analysis error:', error);
        console.error('Error stack:', error.stack);
        
        addProgressMessage(`❌ Error occurred: ${error.message}`);
        
        hideProgress();
        
        if (error.name === 'AbortError') {
            addProgressMessage('🛑 Analysis cancelled by user');
            showAlert('Analysis cancelled by user', 'warning');
        } else {
            addProgressMessage(`🔴 Network error: ${error.message}`);
        showAlert('Network error during re-analysis: ' + error.message, 'error');
        }
    } finally {
        analysisController = null;
    }
}

function showProgress() {
    console.log('showProgress() called');
    document.getElementById('progressModal').classList.remove('hidden');
    document.getElementById('reAnalyzeBtn').disabled = true;
    document.getElementById('reAnalyzeBtn').innerHTML = '<i class="fas fa-sync-alt fa-spin"></i>Analyzing...';
    
    // Add initial progress message
    addProgressMessage('🚀 Analysis started - preparing to process candidates...');
}

function hideProgress() {
    document.getElementById('progressModal').classList.add('hidden');
    document.getElementById('reAnalyzeBtn').disabled = false;
    document.getElementById('reAnalyzeBtn').innerHTML = '<i class="fas fa-sync-alt"></i>Re-analyze Candidates';
    updateProgress(0, 'Initializing analysis...');
    updateStats(0, 0, 0);
    
    // Clear progress log
    const progressLog = document.getElementById('progressLog');
    if (progressLog) {
        progressLog.innerHTML = '';
    }
}

function updateProgress(percentage, text) {
    document.getElementById('progressBar').style.width = percentage + '%';
    document.getElementById('progressText').textContent = text;
}

function updateStats(processed, successful, errors) {
    document.getElementById('processedCount').textContent = processed;
    document.getElementById('successCount').textContent = successful;
    document.getElementById('errorCount').textContent = errors;
}

function addProgressMessage(message) {
    const progressLog = document.getElementById('progressLog');
    if (progressLog) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'text-sm text-gray-700 dark:text-gray-300 py-2 px-2 border-b border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors';
        messageDiv.innerHTML = `<span class="text-xs text-gray-500 dark:text-gray-400">[${new Date().toLocaleTimeString()}]</span> <span class="ml-2">${message}</span>`;
        progressLog.appendChild(messageDiv);
        progressLog.scrollTop = progressLog.scrollHeight; // Auto-scroll to bottom
        
        // Keep only last 15 messages (increased due to larger modal)
        while (progressLog.children.length > 15) {
            progressLog.removeChild(progressLog.firstChild);
        }
    }
    console.log('📡 Progress:', message);
}

function getCSRFToken() {
    // First try to get from meta tag
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    if (metaTag) {
        return metaTag.getAttribute('content');
    }
    
    // Then try to get from cookies
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
            return value;
        }
    }
    
    // Finally try template variable
    return '{{ csrf_token }}';
}

function showAlert(message, type = 'info') {
    const alert = document.createElement('div');
    alert.className = 'fixed top-4 right-4 z-50 p-4 rounded-lg text-white max-w-md ' + 
        (type === 'success' ? 'bg-green-500' : 
         type === 'error' ? 'bg-red-500' : 
         type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500');
    
    // Handle multi-line messages
    if (message.includes('\n')) {
        const lines = message.split('\n');
        const titleLine = lines[0];
        const detailLines = lines.slice(1).filter(line => line.trim());
        
        alert.innerHTML = `
            <div class="font-semibold mb-2">${titleLine}</div>
            ${detailLines.map(line => `<div class="text-sm opacity-90">${line.trim()}</div>`).join('')}
        `;
    } else {
    alert.textContent = message;
    }
    
    document.body.appendChild(alert);
    
    // Remove after longer time for success messages with details
    const timeout = (type === 'success' && message.includes('\n')) ? 5000 : 3000;
    setTimeout(() => {
        alert.remove();
    }, timeout);
}

// Analysis options dialog
function showAnalysisOptionsDialog() {
    return new Promise((resolve) => {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-lg w-full mx-4">
                <div class="text-center">
                    <div class="mb-6">
                        <i class="fas fa-robot text-4xl text-purple-500 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Re-analyze All Candidates</h3>
                        <p class="text-gray-600 dark:text-gray-400">Choose your analysis method:</p>
                    </div>
                    
                    <div class="space-y-4 mb-6">
                    
                        
                        <!-- AI Analysis Option -->
                        <div class="border rounded-lg p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" onclick="selectAnalysisOption('ai')">
                            <div class="flex items-start">
                                <input type="radio" name="analysisType" value="ai" class="mt-1 mr-3">
                                <div class="text-left">
                                    <h4 class="font-semibold text-gray-900 dark:text-white flex items-center">
                                        <i class="fas fa-brain text-blue-500 mr-2"></i>
                                        Comprehensive AI Analysis
                                        <span class="ml-2 px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded">~2-5 minutes</span>
                                    </h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                        <strong>Deep AI extraction</strong> of ALL available CV data including missing contact info, complete education timeline, detailed work experience, skills, certifications, languages, and projects. <strong>Fixes incomplete data</strong> and provides comprehensive compatibility assessment.
                                    </p>
                                    <div class="mt-2 flex flex-wrap gap-1">
                                        <span class="px-2 py-1 bg-red-50 text-red-600 text-xs rounded">📧 Contact Info</span>
                                        <span class="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded">📚 Education Timeline</span>
                                        <span class="px-2 py-1 bg-green-50 text-green-600 text-xs rounded">💼 Work Experience</span>
                                        <span class="px-2 py-1 bg-purple-50 text-purple-600 text-xs rounded">🎯 Skills & Certifications</span>
                                        <span class="px-2 py-1 bg-orange-50 text-orange-600 text-xs rounded">🌐 Languages & Projects</span>
                                        <span class="px-2 py-1 bg-pink-50 text-pink-600 text-xs rounded">🔍 Data Validation</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex gap-3 justify-center">
                        <button id="startAnalysisBtn" class="px-6 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors duration-200">
                            <i class="fas fa-play mr-2"></i>Start Analysis
                        </button>
                        <button id="cancelAnalysisBtn" class="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200">
                            <i class="fas fa-times mr-2"></i>Cancel
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        let selectedOption = 'fast'; // Default selection
        
        // Handle option selection
        window.selectAnalysisOption = (option) => {
            selectedOption = option;
            // Update radio buttons
            modal.querySelectorAll('input[name="analysisType"]').forEach(radio => {
                radio.checked = radio.value === option;
            });
        };
        
        modal.querySelector('#startAnalysisBtn').onclick = () => {
            document.body.removeChild(modal);
            delete window.selectAnalysisOption; // Cleanup
            resolve(selectedOption);
        };
        
        modal.querySelector('#cancelAnalysisBtn').onclick = () => {
            document.body.removeChild(modal);
            delete window.selectAnalysisOption; // Cleanup
            resolve(null);
        };
        
        // Close on backdrop click
        modal.onclick = (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
                delete window.selectAnalysisOption; // Cleanup
                resolve(null);
            }
        };
    });
}

// Custom confirmation dialog
function showConfirmationDialog(title, message, confirmText, cancelText) {
    return new Promise((resolve) => {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-robot text-4xl text-purple-500 mb-4"></i>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">${title}</h3>
                        <p class="text-gray-600 dark:text-gray-400 mt-2">${message}</p>
                    </div>
                    <div class="flex gap-3 justify-center">
                        <button id="confirmBtn" class="px-6 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors duration-200">
                            <i class="fas fa-play mr-2"></i>${confirmText}
                        </button>
                        <button id="cancelBtn" class="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200">
                            <i class="fas fa-times mr-2"></i>${cancelText}
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        modal.querySelector('#confirmBtn').onclick = () => {
            document.body.removeChild(modal);
            resolve(true);
        };
        
        modal.querySelector('#cancelBtn').onclick = () => {
            document.body.removeChild(modal);
            resolve(false);
        };
        
        // Close on backdrop click
        modal.onclick = (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
                resolve(false);
            }
        };
    });
}

// Cancel analysis function
async function cancelAnalysis() {
    if (!currentSessionId && !analysisController) {
        console.log('No active analysis to cancel');
        hideProgress();
        return;
    }

    try {
        // Cancel the backend analysis if we have a session ID
        if (currentSessionId) {
            console.log('🛑 Requesting backend cancellation for session:', currentSessionId);
            const cancelResponse = await fetch('/api/cancel-ai-analysis/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify({
                    session_id: currentSessionId
                })
            });

            const cancelResult = await cancelResponse.json();
            console.log('Cancel response:', cancelResult);
            
            if (cancelResult.success) {
                addProgressMessage('🛑 Backend cancellation requested successfully');
            } else {
                console.warn('Backend cancellation failed:', cancelResult.message);
            }
        }
        
        // Cancel the frontend streaming
        if (analysisController) {
            analysisController.abort();
            console.log('🛑 Frontend stream cancelled by user');
        }
        
        // Clear session tracking
        currentSessionId = null;
        
    } catch (error) {
        console.error('Error cancelling analysis:', error);
        addProgressMessage('❌ Error occurred while cancelling analysis');
    } finally {
        hideProgress();
    }
}

// Test function to check basic API connectivity
async function testApiConnectivity() {
    console.log('Testing basic API connectivity...');
    try {
        const response = await fetch('/api/test-endpoint/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({test: 'data'})
        });
        
        console.log('Test response status:', response.status);
        const result = await response.json();
        console.log('Test response data:', result);
        return result.success;
    } catch (error) {
        console.error('Test API error:', error);
        return false;
    }
}

// Analyze a single CV for the current vacancy
async function analyzeCV(cvId, vacancyId) {
    try {
        // Create CV names mapping for single analysis
        createCVNamesMapping();
        const cvDisplayName = getCVDisplayName(cvId);
        
        // Show confirmation dialog
        const confirmed = await showConfirmationDialog(
            'Comprehensive CV Analysis',
            `This will perform a comprehensive AI analysis of ${cvDisplayName} against vacancy "${document.querySelector('h1').textContent.replace('Candidates for ', '')}". The AI will extract complete education timeline, detailed work experience, skills, certifications, languages, and projects. The process may take a few minutes.`,
            'Start Comprehensive Analysis',
            'Cancel'
        );
        
        if (!confirmed) return;
        
        // Show progress
        showProgress();
        updateProgress(10, 'Starting comprehensive CV analysis...');
        addProgressMessage(`🚀 Starting comprehensive analysis of ${cvDisplayName} for vacancy ${vacancyId}`);
        addProgressMessage(`📋 Deep extraction will capture: Contact info, Education, Work history, Skills, Certifications, Languages, Projects + fix missing data`);
        
        // Create analysis payload for single CV with comprehensive extraction
        const analysisPayload = {
            cvs: [cvId],
            vacancy_id: vacancyId,
            analysis_type: 'ai',
            reanalyze: true,  // Force re-analysis to extract missing data
            force_cv_reprocessing: true,  // NEW: Force complete CV data extraction
            extract_all_fields: true,
            include_education_timeline: true,
            include_work_timeline: true,
            extract_skills_detailed: true,
            extract_certifications: true,
            extract_languages: true,
            extract_projects: true,
            comprehensive_analysis: true,
            force_complete_extraction: true,
            extract_contact_info: true,
            extract_personal_details: true,
            extract_all_text_content: true,
            deep_content_analysis: true,
            extract_hidden_fields: true,
            validate_extracted_data: true,
            fill_missing_fields: true,
            enhance_existing_data: true
        };
        
        console.log('Single CV analysis payload:', analysisPayload);
        
        const response = await fetch('/api/start-ai-analysis/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken(),
                'Accept': 'text/event-stream'
            },
            body: JSON.stringify(analysisPayload)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        // Handle streaming response
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        
        while (true) {
            const { done, value } = await reader.read();
            
            if (done) break;
            
            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop();
            
            for (const line of lines) {
                if (line.trim() === '' || !line.startsWith('data: ')) continue;
                
                try {
                    const eventData = JSON.parse(line.slice(6));
                    
                    switch (eventData.type) {
                        case 'start':
                            updateProgress(20, 'Analysis pipeline initialized...');
                            addProgressMessage('📋 Analysis pipeline ready');
                            break;
                            
                        case 'cv_start':
                            updateProgress(40, 'Processing CV content...');
                            addProgressMessage(`🔍 Starting analysis of ${cvDisplayName}`);
                            break;
                            
                        case 'cv_analyzing':
                            updateProgress(60, 'AI performing deep data extraction...');
                            addProgressMessage(`🤖 AI performing deep extraction of ALL data from ${cvDisplayName}...`);
                            setTimeout(() => {
                                addProgressMessage(`📧 Extracting contact details: Email, phone, address, LinkedIn...`);
                            }, 800);
                            setTimeout(() => {
                                addProgressMessage(`📚 Extracting education: Degrees, universities, graduation dates, GPA...`);
                            }, 1600);
                            setTimeout(() => {
                                addProgressMessage(`💼 Extracting work history: Job titles, companies, employment dates, responsibilities...`);
                            }, 2400);
                            setTimeout(() => {
                                addProgressMessage(`🎯 Extracting skills: Technical skills, soft skills, certifications, languages...`);
                            }, 3200);
                            setTimeout(() => {
                                addProgressMessage(`🔍 Validating and enhancing extracted data, filling missing information...`);
                            }, 4000);
                            break;
                            
                        case 'cv_complete':
                            const result = eventData.result;
                            const emoji = result.score >= 85 ? '🎯' : result.score >= 75 ? '✅' : result.score >= 65 ? '⚠️' : '❌';
                            
                            updateProgress(90, `Analysis complete: ${result.score}% compatibility`);
                            addProgressMessage(`${emoji} ${cvDisplayName} analysis complete: ${result.score}% - ${result.recommendation}`);
                            break;
                            
                        case 'cv_error':
                            addProgressMessage(`❌ Error analyzing ${cvDisplayName}: ${eventData.error}`);
                            hideProgress();
                            showAlert(`Error analyzing ${cvDisplayName}: ` + eventData.error, 'error');
                            return;
                            
                        case 'complete':
                            updateProgress(100, 'Analysis completed successfully!');
                            addProgressMessage('🎉 Analysis complete! Refreshing page...');
                            
                            setTimeout(() => {
                                hideProgress();
                                showAlert('CV analysis completed successfully!', 'success');
                                setTimeout(() => window.location.reload(), 2000);
                            }, 1000);
                            return;
                    }
                } catch (e) {
                    console.error('Error parsing event data:', e);
                }
            }
        }
        
    } catch (error) {
        console.error('Analysis error:', error);
        hideProgress();
        showAlert('Error during analysis: ' + error.message, 'error');
    }
}

// Extract Missing Data Function - Direct CV Processing
async function extractMissingData() {
    try {
        const confirmed = await showConfirmationDialog(
            'Extract Missing CV Data',
            'This will re-process all CVs to extract missing contact info, education details, and other data using deep AI analysis. This operation may take several minutes.',
            'Start Extraction',
            'Cancel'
        );
        
        if (!confirmed) return;
        
        // Create CV names mapping for progress messages
        createCVNamesMapping();
        
        // Get all CV IDs from both analyzed and unanalyzed CVs
        const allCvIds = [];
        
        // From analyzed candidates
        document.querySelectorAll('.candidate-card').forEach(card => {
            const cvId = parseInt(card.dataset.candidateId);
            if (cvId && !allCvIds.includes(cvId)) {
                allCvIds.push(cvId);
            }
        });
        
        // From all CVs section
        document.querySelectorAll('.bg-white.dark\\:bg-gray-800.rounded-lg.p-4.border').forEach(card => {
            const analyzeButton = card.querySelector('button[onclick*="analyzeCV"]');
            if (analyzeButton) {
                const onclickAttr = analyzeButton.getAttribute('onclick');
                const cvIdMatch = onclickAttr.match(/analyzeCV\((\d+),/);
                if (cvIdMatch) {
                    const cvId = parseInt(cvIdMatch[1]);
                    if (!allCvIds.includes(cvId)) {
                        allCvIds.push(cvId);
                    }
                }
            }
        });
        
        if (allCvIds.length === 0) {
            showAlert('No CVs found to process.', 'warning');
            return;
        }
        
        // Show progress modal
        showProgress();
        updateProgress(0, `Starting data extraction for ${allCvIds.length} CVs...`);
        addProgressMessage(`🚀 Starting comprehensive data extraction for ${allCvIds.length} CVs`);
        addProgressMessage(`📋 CVs to process: ${allCvIds.map(id => getCVDisplayName(id)).join(', ')}`);
        addProgressMessage(`🔍 Will extract: Email, Phone, Education, Work History, Skills, Certifications`);
        
        // Process CVs in smaller batches to avoid timeout
        const batchSize = 3;
        let processedCount = 0;
        let successCount = 0;
        let errorCount = 0;
        
        for (let i = 0; i < allCvIds.length; i += batchSize) {
            const batch = allCvIds.slice(i, i + batchSize);
            
            addProgressMessage(`📦 Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(allCvIds.length/batchSize)}: ${batch.map(id => getCVDisplayName(id)).join(', ')}`);
            
            for (const cvId of batch) {
                try {
                    const cvDisplayName = getCVDisplayName(cvId);
                    updateProgress(Math.round((processedCount / allCvIds.length) * 90), `Processing ${cvDisplayName}...`);
                    addProgressMessage(`🔍 Extracting data from ${cvDisplayName}...`);
                    
                    // Call backend CV processing endpoint
                    const response = await fetch('/api/process-cv-data/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCSRFToken()
                        },
                        body: JSON.stringify({
                            cv_id: cvId,
                            force_reprocessing: true,
                            extract_all_fields: true
                        })
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        if (result.success) {
                            successCount++;
                            addProgressMessage(`✅ ${cvDisplayName}: Successfully extracted data`);
                            
                            // Show what was extracted
                            if (result.extracted_fields) {
                                const extractedInfo = [];
                                if (result.extracted_fields.email) extractedInfo.push('📧 Email');
                                if (result.extracted_fields.phone) extractedInfo.push('📱 Phone');
                                if (result.extracted_fields.education) extractedInfo.push('🎓 Education');
                                if (result.extracted_fields.skills) extractedInfo.push('🎯 Skills');
                                
                                if (extractedInfo.length > 0) {
                                    addProgressMessage(`   📋 Found: ${extractedInfo.join(', ')}`);
                                }
                            }
                        } else {
                            errorCount++;
                            addProgressMessage(`❌ ${cvDisplayName}: ${result.message || 'Processing failed'}`);
                        }
                    } else {
                        errorCount++;
                        addProgressMessage(`❌ ${cvDisplayName}: Server error ${response.status}`);
                    }
                    
                    processedCount++;
                    updateStats(processedCount, successCount, errorCount);
                    
                    // Small delay between processing
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                } catch (error) {
                    errorCount++;
                    processedCount++;
                    const cvDisplayName = getCVDisplayName(cvId);
                    addProgressMessage(`❌ ${cvDisplayName}: ${error.message}`);
                    updateStats(processedCount, successCount, errorCount);
                }
            }
        }
        
        updateProgress(100, 'Data extraction completed!');
        addProgressMessage(`🎉 Extraction complete! ${successCount}/${allCvIds.length} CVs processed successfully`);
        addProgressMessage(`📊 Results: ${successCount} successful, ${errorCount} errors`);
        addProgressMessage('🔄 Refreshing page to show updated data...');
        
        setTimeout(() => {
            hideProgress();
            
            const summaryMessage = `Data extraction completed! ${successCount} CVs processed successfully.
                Successful extractions: ${successCount}
                Errors: ${errorCount}
                The CV data has been updated with missing information.`;
            
            showAlert(summaryMessage, 'success');
            
            // Reload the page to show updated results
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        }, 2000);
        
    } catch (error) {
        console.error('Data extraction error:', error);
        hideProgress();
        showAlert('Error during data extraction: ' + error.message, 'error');
    }
}

// Make sure all onclick functions are globally accessible
window.clearFilters = clearFilters;
window.viewCVDetails = viewCVDetails;
window.viewAnalysis = viewAnalysis;
window.showCVDetailsPopup = showCVDetailsPopup;
window.showAnalysisPopup = showAnalysisPopup;
window.showModal = showModal;
window.hideModal = hideModal;
window.downloadCV = downloadCV;
window.openCVInNewTab = openCVInNewTab;
window.contactCandidate = contactCandidate;
window.exportCandidates = exportCandidates;
window.cancelAnalysis = cancelAnalysis;
window.analyzeCV = analyzeCV;
window.extractMissingData = extractMissingData;
</script>
{% endblock %}