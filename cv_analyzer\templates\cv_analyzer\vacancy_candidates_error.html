{% extends "cv_analyzer/base.html" %}
{% load static %}

{% block title %}Vacancy Not Found - CV Analyzer{% endblock %}

{% block content %}
<div class="flex items-center justify-center min-h-96">
    <div class="text-center">
        <div class="mb-8">
            <i class="fas fa-exclamation-triangle text-6xl text-yellow-500 mb-4"></i>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                Oops! Something went wrong
            </h1>
            <p class="text-lg text-gray-600 dark:text-gray-400 mb-6">
                {{ error_message }}
            </p>
        </div>
        
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6 max-w-md mx-auto">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                What happened?
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {% if "not found" in error_message %}
                    The vacancy you're trying to view (ID: {{ vacancy_id }}) either doesn't exist or has been removed from the system.
                {% else %}
                    There was an unexpected error while trying to load the candidates for this vacancy.
                {% endif %}
            </p>
            
            <div class="text-left text-sm text-gray-500 dark:text-gray-400">
                <strong>Possible causes:</strong>
                <ul class="list-disc list-inside mt-2 space-y-1">
                    <li>The vacancy was recently deleted</li>
                    <li>You don't have permission to view this vacancy</li>
                    <li>The vacancy ID in the URL is incorrect</li>
                    <li>Temporary system issue</li>
                </ul>
            </div>
        </div>
        
        <div class="space-y-3">
            <div class="flex flex-col sm:flex-row gap-3 justify-center">
                <a href="{% url 'vacancy_management' %}" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Vacancy Management
                </a>
                
                <button onclick="window.location.reload()" class="inline-flex items-center px-6 py-3 bg-gray-500 text-white font-medium rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="fas fa-redo mr-2"></i>
                    Try Again
                </button>
            </div>
            
            <div class="text-center">
                <a href="{% url 'dashboard' %}" class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                    <i class="fas fa-home mr-1"></i>
                    Go to Dashboard
                </a>
            </div>
        </div>
        
        {% if vacancy_id %}
        <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg max-w-md mx-auto">
            <h4 class="text-sm font-semibold text-blue-900 dark:text-blue-300 mb-2">
                <i class="fas fa-info-circle mr-1"></i>
                Debug Information
            </h4>
            <div class="text-xs text-blue-700 dark:text-blue-400 space-y-1">
                <div>Vacancy ID: {{ vacancy_id }}</div>
                <div>Timestamp: {{ "now"|date:"Y-m-d H:i:s" }}</div>
                <div>User: {{ user.username|default:"Anonymous" }}</div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<style>
    .fade-in {
        animation: fadeIn 0.5s ease-in;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add fade-in animation
    document.querySelector('.text-center').classList.add('fade-in');
    
    // Auto-redirect after 10 seconds if user doesn't interact
    let redirectTimer = setTimeout(function() {
        if (confirm('Would you like to return to Vacancy Management?')) {
            window.location.href = '{% url "vacancy_management" %}';
        }
    }, 10000);
    
    // Cancel auto-redirect if user interacts with the page
    document.addEventListener('click', function() {
        clearTimeout(redirectTimer);
    });
    
    document.addEventListener('keydown', function() {
        clearTimeout(redirectTimer);
    });
});
</script>
{% endblock %} 