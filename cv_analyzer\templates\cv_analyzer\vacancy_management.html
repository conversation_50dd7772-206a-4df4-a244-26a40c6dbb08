{% extends "cv_analyzer/base.html" %}
{% load static %}
{% load widget_tweaks %}

{% block content %}
<div class="container">
    <h1 class="mb-4">Vacancy Management</h1>
    
    <div class="row">
        <div class="col-md-8">
            <h2>Vacancy List</h2>
            <div class="mb-3 d-flex justify-content-between align-items-center">
                <form method="get" class="form-inline flex-grow-1 mr-2">
                    <select name="category" class="form-control mr-2">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                        <option value="{{ category }}">{{ category }}</option>
                        {% endfor %}
                    </select>
                    <input type="text" name="search" class="form-control mr-2" placeholder="Search by vacancy title">
                    <button type="submit" class="btn btn-secondary">Filter</button>
                </form>
                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addVacancyModal">
                    Add New Vacancy
                </button>
            </div>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Title</th>
                        <th>Company</th>
                        <th>Category</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for vacancy in vacancies %}
                    <tr>
                        <td>{{ vacancy.id }}</td>
                        <td>{{ vacancy.title }}</td>
                        <td>{{ vacancy.company.name }}</td>
                        <td>{{ vacancy.category }}</td>
                        <td>
                            <a href="#" class="btn btn-sm btn-info">View</a>
                            <a href="#" class="btn btn-sm btn-warning">Edit</a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5">No vacancies found.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            <!-- Modal -->
            <div class="modal fade" id="addVacancyModal" tabindex="-1" role="dialog" aria-labelledby="addVacancyModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addVacancyModalLabel">Add New Vacancy</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form method="post" action="{% url 'add_vacancy' %}" id="addVacancyForm">
                                {% csrf_token %}
                                <div class="form-row">
                                    <div class="form-group col-md-6">
                                        {{ form.title.label_tag }}
                                        {{ form.title|add_class:"form-control" }}
                                    </div>
                                    <div class="form-group col-md-6">
                                        {{ form.category.label_tag }}
                                        {{ form.category|add_class:"form-control" }}
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group col-md-6">
                                        {{ form.description.label_tag }}
                                        {{ form.description|add_class:"form-control" }}
                                    </div>
                                    <div class="form-group col-md-6">
                                        {{ form.requirements.label_tag }}
                                        {{ form.requirements|add_class:"form-control" }}
                                    </div>
                                </div>
                                <div class="text-right">
                                    <button type="submit" class="btn btn-primary">Add Vacancy</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <h2>Vacancy Statistics</h2>
            <canvas id="vacancyStatsChart"></canvas>
            <div class="mt-4">
                <p><strong>Total Vacancies:</strong> {{ total_vacancies }}</p>
                <p><strong>Open Positions:</strong> {{ open_vacancies }}</p>
                <p><strong>Filled Positions:</strong> {{ filled_vacancies }}</p>
                <p><strong>Expired Positions:</strong> {{ expired_vacancies }}</p>
                <p><strong>Cancelled Positions:</strong> {{ cancelled_vacancies }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    const ctx = document.getElementById('vacancyStatsChart').getContext('2d');
    const vacancyStats = {{ vacancy_stats|safe }};
    
    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: vacancyStats.map(stat => stat.category),
            datasets: [{
                data: vacancyStats.map(stat => stat.count),
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 206, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(153, 102, 255, 0.8)',
                ],
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: 'Vacancies by Category'
                }
            }
        }
    });
</script>

<script>
$(document).ready(function() {
    $('#addCompanyForm, #addVacancyForm').on('submit', function(e) {
        e.preventDefault();
        var form = $(this);
        $.ajax({
            url: form.attr('action'),
            type: form.attr('method'),
            data: form.serialize(),
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error: ' + response.error);
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
            }
        });
    });
});
</script>
{% endblock %}