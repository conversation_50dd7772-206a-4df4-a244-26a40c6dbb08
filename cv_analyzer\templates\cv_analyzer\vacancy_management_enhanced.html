{% extends "cv_analyzer/base.html" %}
{% load static %}
{% load form_tags %}

{% block title %}Vacancy Management - CV Analyzer{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .page-header {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        padding: 3rem 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .process-flow {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1rem;
        margin: 2rem 0;
    }
    
    .process-step {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid #e5e7eb;
        position: relative;
        transition: all 0.3s ease;
    }
    
    .process-step:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
    
    .dark .process-step {
        background: #1f2937;
        border-color: #374151;
    }
    
    .process-step::after {
        content: '→';
        position: absolute;
        right: -15px;
        top: 50%;
        transform: translateY(-50%);
        color: #3b82f6;
        font-size: 1.5rem;
        font-weight: bold;
    }
    
    .process-step:last-child::after {
        display: none;
    }
    
    @media (max-width: 768px) {
        .process-step::after {
            content: '↓';
            right: 50%;
            top: auto;
            bottom: -15px;
            transform: translateX(50%);
        }
    }

    .metric-card {
        background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
        border: none;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        border-radius: 0.75rem;
        padding: 1.5rem;
        color: white;
    }
    
    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .metric-card:hover::before {
        opacity: 1;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }
    
    .metric-card.blue {
        --gradient-start: #3b82f6;
        --gradient-end: #1d4ed8;
    }
    
    .metric-card.green {
        --gradient-start: #10b981;
        --gradient-end: #047857;
    }
    
    .metric-card.purple {
        --gradient-start: #8b5cf6;
        --gradient-end: #6d28d9;
    }
    
    .metric-card.orange {
        --gradient-start: #f59e0b;
        --gradient-end: #d97706;
    }
    
    .metric-card.red {
        --gradient-start: #ef4444;
        --gradient-end: #dc2626;
    }

    .content-grid {
        display: grid;
        grid-template-columns: 1fr 350px;
        gap: 2rem;
        align-items: start;
    }
    
    @media (max-width: 1280px) {
        .content-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
    }

    .main-content {
        min-height: 0;
    }

    .sidebar-content {
        position: sticky;
        top: 2rem;
        max-height: calc(100vh - 4rem);
        overflow-y: auto;
        padding-right: 0.5rem;
    }
    
    .sidebar-content::-webkit-scrollbar {
        width: 4px;
    }
    
    .sidebar-content::-webkit-scrollbar-track {
        background: transparent;
    }
    
    .sidebar-content::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 2px;
    }
    
    .dark .sidebar-content::-webkit-scrollbar-thumb {
        background: #4b5563;
    }

    .vacancy-card {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .vacancy-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }
    
    .vacancy-card:hover::before {
        transform: scaleX(1);
    }
    
    .vacancy-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }
    
    .dark .vacancy-card {
        background: #1f2937;
        border-color: #374151;
    }

    .company-logo {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .status-badge.active {
        background: #d1fae5;
        color: #065f46;
    }
    
    .status-badge.draft {
        background: #fef3c7;
        color: #92400e;
    }
    
    .status-badge.closed {
        background: #fee2e2;
        color: #991b1b;
    }
    
    .dark .status-badge.active {
        background: #047857;
        color: #d1fae5;
    }
    
    .dark .status-badge.draft {
        background: #d97706;
        color: #fef3c7;
    }
    
    .dark .status-badge.closed {
        background: #dc2626;
        color: #fee2e2;
    }

    .priority-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }
    
    .priority-badge.high {
        background: #fee2e2;
        color: #991b1b;
    }
    
    .priority-badge.medium {
        background: #fef3c7;
        color: #92400e;
    }
    
    .priority-badge.low {
        background: #d1fae5;
        color: #065f46;
    }
    
    .dark .priority-badge.high {
        background: #dc2626;
        color: #fee2e2;
    }
    
    .dark .priority-badge.medium {
        background: #d97706;
        color: #fef3c7;
    }
    
    .dark .priority-badge.low {
        background: #047857;
        color: #d1fae5;
    }

    .search-filter-bar {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid #e5e7eb;
        margin-bottom: 2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
    
    .dark .search-filter-bar {
        background: #1f2937;
        border-color: #374151;
    }

    .filter-group {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }

    .btn {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        border: 1px solid;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        border-color: transparent;
    }
    
    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
    
    .btn-secondary {
        background: white;
        color: #6b7280;
        border-color: #d1d5db;
    }
    
    .btn-secondary:hover {
        background: #f9fafb;
        border-color: #9ca3af;
    }
    
    .dark .btn-secondary {
        background: #374151;
        color: #d1d5db;
        border-color: #4b5563;
    }
    
    .dark .btn-secondary:hover {
        background: #4b5563;
    }

    .section-header {
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }
    
    .dark .section-header {
        border-color: #374151;
    }

    .sidebar-section {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .dark .sidebar-section {
        background: #1f2937;
        border-color: #374151;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .vacancies-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
        gap: 1.5rem;
    }

    .add-vacancy-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 50;
    }
    
    .modal-content {
        background: white;
        border-radius: 0.75rem;
        padding: 2rem;
        width: 90%;
        max-width: 600px;
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .dark .modal-content {
        background: #1f2937;
    }

    .quick-actions {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
        flex-wrap: wrap;
    }

    .skill-tag {
        background: #eff6ff;
        color: #1d4ed8;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-block;
        margin: 0.125rem;
    }
    
    .dark .skill-tag {
        background: #1e3a8a;
        color: #dbeafe;
    }
</style>
{% endblock %}

{% block content %}
<div class="space-y-6">
   

    <!-- Content Grid -->
    <div class="content-grid">
        <!-- Main Content Area -->
        <div class="main-content space-y-6">
            <!-- KPI Metrics -->
            <div class="stats-grid">
                <div class="metric-card blue">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold opacity-90 mb-1">Total Vacancies</h3>
                            <div class="text-3xl font-bold">{{ total_vacancies|default:0 }}</div>
                            <div class="text-sm opacity-75 mt-2 flex items-center">
                                <i class="fas fa-arrow-up mr-1"></i>
                                +{{ new_vacancies_this_week|default:5 }} this week
                            </div>
                        </div>
                        <div class="text-4xl opacity-80">
                            <i class="fas fa-briefcase"></i>
                        </div>
                    </div>
                </div>

                <div class="metric-card green">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold opacity-90 mb-1">Active Positions</h3>
                            <div class="text-3xl font-bold">{{ active_vacancies|default:0 }}</div>
                            <div class="text-sm opacity-75 mt-2 flex items-center">
                                <i class="fas fa-clock mr-1"></i>
                                {{ urgent_positions|default:3 }} urgent
                            </div>
                        </div>
                        <div class="text-4xl opacity-80">
                            <i class="fas fa-play-circle"></i>
                        </div>
                    </div>
                </div>

                <div class="metric-card purple">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold opacity-90 mb-1">Applications</h3>
                            <div class="text-3xl font-bold">{{ total_applications|default:0 }}</div>
                            <div class="text-sm opacity-75 mt-2 flex items-center">
                                <i class="fas fa-percentage mr-1"></i>
                                {{ quality_score|default:85 }}% quality
                            </div>
                        </div>
                        <div class="text-4xl opacity-80">
                            <i class="fas fa-user-tie"></i>
                        </div>
                    </div>
                </div>

                <div class="metric-card orange">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold opacity-90 mb-1">Avg Fill Time</h3>
                            <div class="text-3xl font-bold">{{ avg_fill_time|default:18 }}<span class="text-lg">d</span></div>
                            <div class="text-sm opacity-75 mt-2 flex items-center">
                                <i class="fas fa-chart-line mr-1"></i>
                                Industry benchmark
                            </div>
                        </div>
                        <div class="text-4xl opacity-80">
                            <i class="fas fa-stopwatch"></i>
                        </div>
                    </div>
                </div>

                <div class="metric-card red">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold opacity-90 mb-1">Success Rate</h3>
                            <div class="text-3xl font-bold">{{ success_rate|default:78 }}%</div>
                            <div class="text-sm opacity-75 mt-2 flex items-center">
                                <i class="fas fa-trophy mr-1"></i>
                                Placement success
                            </div>
                        </div>
                        <div class="text-4xl opacity-80">
                            <i class="fas fa-award"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter Bar -->
            <div class="search-filter-bar">
                <div class="flex flex-col lg:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" id="searchInput" placeholder="Search vacancies..." value="{{ search_query|default:'' }}" class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        </div>
                    </div>
                    
                    <div class="filter-group">
                        <select id="companyFilter" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="">All Companies</option>
                            {% for company in companies %}
                            <option value="{{ company.id }}" {% if selected_company == company.id|stringformat:"s" %}selected{% endif %}>
                                {{ company.name }}
                            </option>
                            {% endfor %}
                        </select>
                        
                        <select id="statusFilter" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="">All Status</option>
                            {% for status_value, status_label in status_choices %}
                            <option value="{{ status_value }}" {% if selected_status == status_value %}selected{% endif %}>
                                {{ status_label }}
                            </option>
                            {% endfor %}
                        </select>
                        
                        <select id="priorityFilter" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="">All Priorities</option>
                            <option value="high" {% if selected_priority == "high" %}selected{% endif %}>High</option>
                            <option value="medium" {% if selected_priority == "medium" %}selected{% endif %}>Medium</option>
                            <option value="low" {% if selected_priority == "low" %}selected{% endif %}>Low</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <button class="btn btn-primary" onclick="showAddVacancyModal()">
                    <i class="fas fa-plus"></i>Create New Vacancy
                </button>
                <button class="btn btn-secondary" onclick="duplicateSelectedVacancy()">
                    <i class="fas fa-copy"></i>Duplicate Position
                </button>
                <button class="btn btn-secondary" onclick="exportVacancyData()">
                    <i class="fas fa-download"></i>Export Data
                </button>
                <button class="btn btn-secondary" onclick="showAnalyticsReport()">
                    <i class="fas fa-chart-bar"></i>Analytics Report
                </button>
            </div>

            <!-- Vacancies Section -->
            <div class="section-header">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-briefcase mr-3 text-blue-500"></i>Open Positions
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Manage and track your active job postings</p>
            </div>

            <div class="vacancies-grid">
                {% for vacancy in vacancies %}
                <div class="vacancy-card">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-start">
                            <div class="company-logo">
                                {{ vacancy.company.name|first|upper|default:"T" }}
                            </div>
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                                    {{ vacancy.title|default:"Software Engineer" }}
                                </h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                    {{ vacancy.company.name|default:"TechCorp Inc" }} • {{ vacancy.location|default:"Remote" }}
                                </p>
                            </div>
                        </div>
                        <div class="flex flex-col gap-2 items-end">
                            <span class="status-badge {{ vacancy.status }}">{{ vacancy.get_status_display }}</span>
                            <span class="priority-badge {{ vacancy.priority }}">
                                <i class="fas fa-{% if vacancy.priority == 'high' %}exclamation-circle{% elif vacancy.priority == 'medium' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>{{ vacancy.priority|title }}
                            </span>
                        </div>
                    </div>
                    
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                        {{ vacancy.description|default:"We are looking for a talented software engineer to join our dynamic team and work on cutting-edge projects using modern technologies." }}
                    </p>
                    
                    <div class="mb-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Required Skills:</h4>
                        <div class="flex flex-wrap gap-1">
                            {% for skill in vacancy.required_skills|default:"Python,React,Node.js,SQL"|split:"," %}
                            <span class="skill-tag">{{ skill|default:"Python" }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-4 mb-4 text-center py-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div>
                            <div class="text-lg font-semibold text-blue-600 dark:text-blue-400">
                                {{ vacancy.applications_count|default:0 }}
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Applications</div>
                        </div>
                        <div>
                            <div class="text-lg font-semibold text-green-600 dark:text-green-400">
                                {{ vacancy.matches_count|default:0 }}
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Matches</div>
                        </div>
                        <div>
                            <div class="text-lg font-semibold text-purple-600 dark:text-purple-400">
                                {{ vacancy.days_open|default:0 }}d
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Open</div>
                        </div>
                    </div>
                    
                    <div class="flex gap-2">
                        <button class="flex-1 btn btn-primary" onclick="viewVacancyDetails({{ vacancy.id }})">
                            <i class="fas fa-eye"></i>View Details
                        </button>
                        <button class="btn btn-secondary" onclick="editVacancy({{ vacancy.id }})">
                            <i class="fas fa-edit"></i>Edit
                        </button>
                        <button class="btn btn-secondary" onclick="viewCandidates({{ vacancy.id }})">
                            <i class="fas fa-users"></i>Candidates
                        </button>
                    </div>
                </div>
                {% empty %}
                <div class="col-span-full text-center py-12">
                    <div class="text-gray-500 dark:text-gray-400">
                        <i class="fas fa-briefcase text-6xl mb-4 opacity-30"></i>
                        <h3 class="text-lg font-medium mb-2">No vacancies found</h3>
                        <p class="text-sm mb-4">Create your first job posting to start recruiting</p>
                        <button class="btn btn-primary" onclick="showAddVacancyModal()">
                            <i class="fas fa-plus mr-2"></i>Create First Vacancy
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Sidebar Content -->
        <div class="sidebar-content space-y-6">
            <!-- Vacancy Analytics Chart -->
            <div class="sidebar-section">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-chart-bar mr-2 text-green-500"></i>Vacancy Analytics
                </h3>
                <div class="relative mb-4">
                    <canvas id="vacancyChart" width="280" height="280"></canvas>
                </div>
                <div class="space-y-2 text-sm">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">Active</span>
                        </div>
                        <span class="font-semibold text-gray-900 dark:text-white">65%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-yellow-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">Draft</span>
                        </div>
                        <span class="font-semibold text-gray-900 dark:text-white">20%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-red-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">Closed</span>
                        </div>
                        <span class="font-semibold text-gray-900 dark:text-white">15%</span>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="sidebar-section">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-clock mr-2 text-purple-500"></i>Recent Activity
                </h3>
                <div class="space-y-3">
                    <div class="flex items-start">
                        <div class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <div class="flex-1">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">New application received</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Software Engineer • 15 min ago</div>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <div class="flex-1">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">Vacancy published</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Data Scientist • 2 hours ago</div>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <div class="flex-1">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">Interview scheduled</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Frontend Developer • 4 hours ago</div>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <div class="flex-1">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">Position filled</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Product Manager • 1 day ago</div>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View all activity <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="sidebar-section">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-tachometer-alt mr-2 text-red-500"></i>Performance Metrics
                </h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-users text-blue-500 mr-2"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Applications/Day</span>
                        </div>
                        <span class="text-sm font-semibold text-gray-900 dark:text-white">12.5</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-percentage text-green-500 mr-2"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Interview Rate</span>
                        </div>
                        <span class="text-sm font-semibold text-green-600 dark:text-green-400">35%</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-clock text-yellow-500 mr-2"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Time to Fill</span>
                        </div>
                        <span class="text-sm font-semibold text-gray-900 dark:text-white">18 days</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-star text-purple-500 mr-2"></i>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Quality Score</span>
                        </div>
                        <span class="text-sm font-semibold text-purple-600 dark:text-purple-400">4.2/5</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Vacancy Modal -->
<div class="add-vacancy-modal" id="addVacancyModal">
    <div class="modal-content">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Create New Vacancy</h2>
            <button onclick="hideAddVacancyModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form id="vacancyForm" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-briefcase text-blue-500 mr-2"></i>
                        Job Title
                    </label>
                    <input type="text" id="modalJobTitle" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="e.g. Senior Software Engineer" required>
                </div>
                
                <div>
                    <label class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-building text-green-500 mr-2"></i>
                        Company
                    </label>
                    <select id="modalCompanySelect" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option value="">Select company</option>
                        {% for company in companies %}
                        <option value="{{ company.id }}">{{ company.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-tag text-purple-500 mr-2"></i>
                        Category
                    </label>
                    <input type="text" id="modalCategory" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="e.g. IT, Marketing, Sales">
                </div>
                
                <div>
                    <label class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-signal text-orange-500 mr-2"></i>
                        Status
                    </label>
                    <select id="modalStatus" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option value="draft">Draft</option>
                        <option value="active">Active</option>
                        <option value="closed">Closed</option>
                        <option value="archived">Archived</option>
                    </select>
                </div>
                
                <div>
                    <label class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-eye text-indigo-500 mr-2"></i>
                        Published
                    </label>
                    <div class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700">
                        <input type="checkbox" id="modalPublished" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="modalPublished" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Visible to public</label>
                    </div>
                </div>
            </div>
            
            <div>
                <label class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-align-left text-blue-500 mr-2"></i>
                    Job Description
                </label>
                <textarea id="modalDescription" rows="4" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="Describe the role, responsibilities, and requirements..."></textarea>
            </div>
            
            <div>
                <label class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-clipboard-list text-red-500 mr-2"></i>
                    Requirements
                </label>
                <textarea id="modalRequirements" rows="3" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="List the specific requirements, qualifications, and experience needed..."></textarea>
            </div>
            
            <div>
                <label class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i class="fas fa-tools text-yellow-500 mr-2"></i>
                    Required Skills (comma-separated)
                </label>
                <input type="text" id="modalRequiredSkills" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="e.g. Python, React, SQL, Machine Learning">
            </div>
            
            <div class="flex gap-3 pt-4">
                <button type="submit" class="flex-1 btn btn-primary">
                    <i class="fas fa-save mr-2"></i>
                    <span id="modalSubmitText">Create Vacancy</span>
                </button>
                <button type="button" onclick="hideAddVacancyModal()" class="flex-1 btn btn-secondary">
                    <i class="fas fa-times mr-2"></i>
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Vacancy Analytics Chart with dynamic data
    const ctx = document.getElementById('vacancyChart').getContext('2d');
    const chartData = {
        {% for stat in vacancy_stats_by_status %}
            '{{ stat.status }}': {{ stat.count }},
        {% endfor %}
    };
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(chartData),
            datasets: [{
                data: Object.values(chartData),
                backgroundColor: [
                    '#10b981',  // Active
                    '#f59e0b',  // Draft
                    '#ef4444',  // Closed
                    '#8b5cf6'   // Archived
                ],
                borderWidth: 0,
                cutout: '70%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Search and filter functionality
    const searchInput = document.getElementById('searchInput');
    const companyFilter = document.getElementById('companyFilter');
    const statusFilter = document.getElementById('statusFilter');
    const priorityFilter = document.getElementById('priorityFilter');
    
    function applyFilters() {
        const params = new URLSearchParams();
        
        if (searchInput.value) params.append('search', searchInput.value);
        if (companyFilter.value) params.append('company', companyFilter.value);
        if (statusFilter.value) params.append('status', statusFilter.value);
        if (priorityFilter.value) params.append('priority', priorityFilter.value);
        
        window.location.href = '{% url "vacancy_management" %}' + (params.toString() ? '?' + params.toString() : '');
    }
    
    // Debounced search
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(applyFilters, 500);
    });
    
    companyFilter.addEventListener('change', applyFilters);
    statusFilter.addEventListener('change', applyFilters);
    priorityFilter.addEventListener('change', applyFilters);
    
    // Animate metric cards
    const metricCards = document.querySelectorAll('.metric-card');
    metricCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });
    
    // Animate vacancy cards
    const vacancyCards = document.querySelectorAll('.vacancy-card');
    vacancyCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, (index * 100) + 600);
    });
});

// Global variables for modal state
let currentVacancyId = null;
let isEditMode = false;

// Modal functions
function showAddVacancyModal() {
    currentVacancyId = null;
    isEditMode = false;
    document.getElementById('addVacancyModal').querySelector('h2').textContent = 'Create New Vacancy';
    document.getElementById('modalSubmitText').textContent = 'Create Vacancy';
    clearForm();
    document.getElementById('addVacancyModal').style.display = 'flex';
}

function hideAddVacancyModal() {
    document.getElementById('addVacancyModal').style.display = 'none';
    clearForm();
}

function clearForm() {
    document.getElementById('vacancyForm').reset();
}

// Vacancy CRUD operations
async function createVacancy(formData) {
    try {
        const response = await fetch('{% url "add_vacancy" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('Vacancy created successfully!', 'success');
            hideAddVacancyModal();
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(result.message || 'Failed to create vacancy', 'error');
        }
    } catch (error) {
        showAlert('Error creating vacancy: ' + error.message, 'error');
    }
}

async function editVacancy(vacancyId) {
    try {
        currentVacancyId = vacancyId;
        isEditMode = true;
        
        const response = await fetch(`/vacancy/${vacancyId}/edit/`);
        const vacancy = await response.json();
        
        // Populate form with vacancy data
        document.getElementById('modalJobTitle').value = vacancy.title || '';
        document.getElementById('modalCompanySelect').value = vacancy.company_id || '';
        document.getElementById('modalCategory').value = vacancy.category || '';
        document.getElementById('modalStatus').value = vacancy.status || 'draft';
        document.getElementById('modalPublished').checked = vacancy.published || false;
        document.getElementById('modalDescription').value = vacancy.description || '';
        document.getElementById('modalRequirements').value = vacancy.requirements || '';
        document.getElementById('modalRequiredSkills').value = vacancy.required_skills || '';
        
        // Update modal title and submit button
        document.getElementById('addVacancyModal').querySelector('h2').textContent = 'Edit Vacancy';
        document.getElementById('modalSubmitText').textContent = 'Update Vacancy';
        document.getElementById('addVacancyModal').style.display = 'flex';
    } catch (error) {
        showAlert('Error loading vacancy: ' + error.message, 'error');
    }
}

async function updateVacancy(vacancyId, formData) {
    try {
        const response = await fetch(`/vacancy/${vacancyId}/edit/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('Vacancy updated successfully!', 'success');
            hideAddVacancyModal();
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(result.message || 'Failed to update vacancy', 'error');
        }
    } catch (error) {
        showAlert('Error updating vacancy: ' + error.message, 'error');
    }
}

async function deleteVacancy(vacancyId) {
    if (!confirm('Are you sure you want to delete this vacancy? This action cannot be undone.')) {
        return;
    }
    
    try {
        const response = await fetch(`/vacancy/${vacancyId}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('Vacancy deleted successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(result.message || 'Failed to delete vacancy', 'error');
        }
    } catch (error) {
        showAlert('Error deleting vacancy: ' + error.message, 'error');
    }
}

async function viewVacancyDetails(vacancyId) {
    try {
        const response = await fetch(`/vacancy/${vacancyId}/detail/`);
        const vacancy = await response.json();
        
        // Create and show detailed view modal
        showVacancyDetailsModal(vacancy);
    } catch (error) {
        showAlert('Error loading vacancy details: ' + error.message, 'error');
    }
}

function viewCandidates(vacancyId) {
    // Debug information
    console.log('Attempting to view candidates for vacancy ID:', vacancyId);
    
    // Check if vacancyId is valid
    if (!vacancyId) {
        showAlert('Invalid vacancy ID', 'error');
        return;
    }
    
    // Redirect to candidates view for this vacancy
    const url = `/vacancy/${vacancyId}/candidates/`;
    console.log('Redirecting to:', url);
    window.location.href = url;
}

async function duplicateVacancy(vacancyId) {
    try {
        const response = await fetch(`/vacancy/${vacancyId}/duplicate/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('Vacancy duplicated successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(result.message || 'Failed to duplicate vacancy', 'error');
        }
    } catch (error) {
        showAlert('Error duplicating vacancy: ' + error.message, 'error');
    }
}

// Quick action functions
function duplicateSelectedVacancy() {
    showAlert('Please select a vacancy card and use the duplicate button on that card.', 'info');
}

function exportVacancyData() {
    // Create export functionality
    const exportData = {
        vacancies: Array.from(document.querySelectorAll('.vacancy-card')).map(card => ({
            title: card.querySelector('h3').textContent,
            company: card.querySelector('p').textContent,
            status: card.querySelector('.status-badge').textContent
        }))
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `vacancies_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    showAlert('Vacancy data exported successfully!', 'success');
}

function showAnalyticsReport() {
    showAlert('Analytics report feature coming soon!', 'info');
}

// Form submission handler
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('vacancyForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                title: document.getElementById('modalJobTitle').value,
                company_id: document.getElementById('modalCompanySelect').value,
                category: document.getElementById('modalCategory').value || '',
                status: document.getElementById('modalStatus').value || 'draft',
                published: document.getElementById('modalPublished').checked,
                description: document.getElementById('modalDescription').value || '',
                requirements: document.getElementById('modalRequirements').value || '',
                required_skills: document.getElementById('modalRequiredSkills').value || ''
            };
            
            if (isEditMode && currentVacancyId) {
                updateVacancy(currentVacancyId, formData);
            } else {
                createVacancy(formData);
            }
        });
    }
});

// Utility functions
function showAlert(message, type = 'info') {
    // Create alert element
    const alert = document.createElement('div');
    alert.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
    }`;
    alert.textContent = message;
    
    document.body.appendChild(alert);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        alert.remove();
    }, 3000);
}

function showVacancyDetailsModal(vacancy) {
    // Implementation for detailed vacancy view modal
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold">${vacancy.title}</h2>
                <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="space-y-4">
                <p><strong>Company:</strong> ${vacancy.company.name}</p>
                <p><strong>Status:</strong> ${vacancy.status}</p>
                <p><strong>Applications:</strong> ${vacancy.applications_count}</p>
                <p><strong>Matches:</strong> ${vacancy.matches_count}</p>
                <p><strong>Days Open:</strong> ${vacancy.days_open}</p>
                <div><strong>Description:</strong><br>${vacancy.description}</div>
            </div>
            <div class="mt-6 flex gap-3">
                <button onclick="editVacancy(${vacancy.id}); this.closest('.fixed').remove();" class="btn btn-primary">Edit</button>
                <button onclick="deleteVacancy(${vacancy.id}); this.closest('.fixed').remove();" class="btn btn-secondary">Delete</button>
                <button onclick="this.closest('.fixed').remove()" class="btn btn-secondary">Close</button>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.id === 'addVacancyModal') {
        hideAddVacancyModal();
    }
});

// Add CSRF token to all requests
document.addEventListener('DOMContentLoaded', function() {
    if (!document.querySelector('[name=csrfmiddlewaretoken]')) {
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrfmiddlewaretoken';
        csrf.value = '{{ csrf_token }}';
        document.body.appendChild(csrf);
    }
});
</script>
{% endblock %}