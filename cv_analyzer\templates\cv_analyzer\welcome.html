{% extends "cv_analyzer/base.html" %}
{% load form_tags %}

{% block extra_css %}
<style>
/* Base Styles */
body > nav:first-of-type { display: none; }
body > div.p-4, body > div.p-4 > div.p-4 {
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.animate-float { animation: float 3s ease-in-out infinite; }

/* 3D Background */
#particles-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.bg-gradient-overlay {
    @apply absolute inset-0 bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-900/90 dark:to-gray-900/70 -z-10;
}
</style>
{% endblock %}

{% block content %}
<!-- Navbar -->
<nav class="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm fixed w-full z-50">
    <div class="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4">
        <a href="{% url 'welcome' %}" class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-robot text-xl text-white"></i>
            </div>
            <span class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">CV Analyzer</span>
        </a>
        <div class="flex items-center gap-4">
            <!-- Public Jobs Link (always visible) -->
            <a href="{% url 'public_vacancy_list' %}" class="text-gray-800 dark:text-white hover:bg-gray-50 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-4 py-2 dark:hover:bg-gray-700 focus:outline-none dark:focus:ring-gray-800">
                <i class="fas fa-briefcase mr-2"></i>Jobs
            </a>
            
            {% if user.is_authenticated %}
                <a href="{% url 'dashboard' %}" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Dashboard</a>
            {% else %}
                <a href="{% url 'login' %}" class="text-gray-800 dark:text-white hover:bg-gray-50 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-4 py-2 dark:hover:bg-gray-700 focus:outline-none dark:focus:ring-gray-800">Sign In</a>
                <a href="{% url 'login' %}" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Get Started</a>
            {% endif %}
        </div>
    </div>
</nav>

<!-- Hero Section -->
<section class="pt-24 pb-12">
    <div class="grid max-w-screen-xl px-4 py-8 mx-auto lg:gap-8 xl:gap-0 lg:py-16 lg:grid-cols-12">
        <div class="mr-auto place-self-center lg:col-span-7">
            <h1 class="max-w-2xl mb-4 text-4xl font-extrabold tracking-tight leading-none md:text-5xl xl:text-6xl dark:text-white">
                Streamline Your<br>
                <span class="text-blue-600 dark:text-blue-500">Recruitment</span><br>
                Process with AI
            </h1>
            <p class="max-w-2xl mb-6 font-light text-gray-500 lg:mb-8 md:text-lg lg:text-xl dark:text-gray-400">
                Welcome to CV Analyzer! Our advanced AI-powered tool helps you analyze and evaluate CVs quickly and efficiently.
            </p>
            <div class="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
                <a href="{% url 'login' %}" class="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-900">
                    Get started
                    <i class="fas fa-arrow-right w-5 h-5 ml-2 -mr-1"></i>
                </a>
                <button onclick="playDemo()" class="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-center text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 dark:text-white dark:border-gray-700 dark:hover:bg-gray-700 dark:focus:ring-gray-800">
                    <i class="fas fa-play mr-2"></i>
                    Watch demo
                </button>
            </div>
        </div>
        <div class="hidden lg:mt-0 lg:col-span-5 lg:flex">
            <!-- Feature Cards Grid -->
            <div class="grid grid-cols-2 gap-4">
                <!-- Upload CV -->
                <div class="feature-card p-6 rounded-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                    <div class="icon-wrapper w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-upload text-xl text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Upload CV</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Start by uploading the CV you want to analyze.</p>
                </div>

                <!-- AI Analysis -->
                <div class="feature-card p-6 rounded-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                    <div class="icon-wrapper w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-robot text-xl text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">AI Analysis</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Our AI engine processes and evaluates the CV.</p>
                </div>

                <!-- View Results -->
                <div class="feature-card p-6 rounded-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                    <div class="icon-wrapper w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-chart-bar text-xl text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">View Results</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Get detailed analysis and compatibility scores.</p>
                </div>

                <!-- Make Decisions -->
                <div class="feature-card p-6 rounded-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                    <div class="icon-wrapper w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-check-circle text-xl text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Make Decisions</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Make informed hiring decisions quickly.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="bg-white dark:bg-gray-900">
    <div class="py-8 px-4 mx-auto max-w-screen-xl sm:py-16 lg:px-6">
        <div class="max-w-screen-md mb-8 lg:mb-16">
            <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">
                Designed for business teams
            </h2>
            <p class="text-gray-500 sm:text-xl dark:text-gray-400">
                Here at CV Analyzer we focus on markets where technology, innovation, and capital can unlock long-term value.
            </p>
        </div>
        <div class="space-y-8 md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-12 md:space-y-0">
            <!-- Feature cards here -->
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="bg-white dark:bg-gray-900">
    <div class="max-w-screen-xl px-4 py-8 mx-auto text-center lg:py-16 lg:px-6">
        <dl class="grid max-w-screen-md gap-8 mx-auto text-gray-900 sm:grid-cols-4 dark:text-white">
            <div class="flex flex-col items-center justify-center">
                <dt class="mb-2 text-3xl font-extrabold stat-number" data-value="5000">0</dt>
                <dd class="text-gray-500 dark:text-gray-400">CVs Analyzed</dd>
            </div>
            <div class="flex flex-col items-center justify-center">
                <dt class="mb-2 text-3xl font-extrabold stat-number" data-value="98">0</dt>
                <dd class="text-gray-500 dark:text-gray-400">Accuracy Rate</dd>
            </div>
            <div class="flex flex-col items-center justify-center">
                <dt class="mb-2 text-3xl font-extrabold stat-number" data-value="200">0</dt>
                <dd class="text-gray-500 dark:text-gray-400">Companies</dd>
            </div>
            <div class="flex flex-col items-center justify-center">
                <dt class="mb-2 text-3xl font-extrabold">24/7</dt>
                <dd class="text-gray-500 dark:text-gray-400">Support</dd>
            </div>
        </dl>
    </div>
</section>

<!-- Open Vacancies Section -->
{% if open_vacancies %}
<section class="bg-gray-50 dark:bg-gray-800">
    <div class="py-8 px-4 mx-auto max-w-screen-xl sm:py-16 lg:px-6">
        <div class="max-w-screen-md mb-8 lg:mb-16 text-center mx-auto">
            <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">
                <i class="fas fa-briefcase mr-3 text-blue-600"></i>Current Job Openings
            </h2>
            <p class="text-gray-500 sm:text-xl dark:text-gray-400">
                Discover exciting career opportunities and apply by uploading your CV. Our AI-powered system will match your profile with the perfect position.
            </p>
        </div>
        
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {% for vacancy in open_vacancies %}
            <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow duration-300">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            {{ vacancy.title }}
                        </h3>
                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
                            <i class="fas fa-building mr-2"></i>
                            {{ vacancy.company.name }}
                        </div>
                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            Remote
                        </div>
                    </div>
                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
                        Active
                    </span>
                </div>
                
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                    {{ vacancy.description|truncatewords:20 }}
                </p>
                
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Key Skills:</h4>
                    <div class="flex flex-wrap gap-1">
                        {% with skills="Python,Django,React,SQL"|split:"," %}
                        {% for skill in skills|slice:":4" %}
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded dark:bg-blue-900 dark:text-blue-300">
                            {{ skill }}
                        </span>
                        {% endfor %}
                        {% endwith %}
                    </div>
                </div>
                
                <div class="flex gap-3 mt-4">
                    <button onclick="applyToJob({{ vacancy.id }})" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                        <i class="fas fa-upload mr-2"></i>Apply Now
                    </button>
                    <button onclick="viewJobDetails({{ vacancy.id }})" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 text-sm font-medium py-2 px-3 rounded-lg transition-colors">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-12">
            <a href="{% url 'public_vacancy_list' %}" class="inline-flex items-center justify-center px-6 py-3 text-base font-medium text-center text-blue-600 bg-transparent border border-blue-600 rounded-lg hover:bg-blue-600 hover:text-white transition-colors">
                View All Positions
                <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>
    </div>
</section>
{% endif %}

<!-- Testimonials -->
<section class="bg-white dark:bg-gray-900">
    <div class="max-w-screen-xl px-4 py-8 mx-auto text-center lg:py-16 lg:px-6">
        <figure class="max-w-screen-md mx-auto">
            <svg class="h-12 mx-auto mb-3 text-gray-400 dark:text-gray-600" viewBox="0 0 24 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14.017 18L14.017 10.609C14.017 4.905 17.748 1.039 23 0L23.995 2.151C21.563 3.068 20 5.789 20 8H24V18H14.017ZM0 18V10.609C0 4.905 3.748 1.038 9 0L9.996 2.151C7.563 3.068 6 5.789 6 8H9.983L9.983 18L0 18Z" fill="currentColor"/>
            </svg>
            <blockquote>
                <p class="text-xl font-medium text-gray-900 dark:text-white">"CV Analyzer has revolutionized our hiring process. The AI-powered analysis saves us countless hours and helps us make better hiring decisions."</p>
            </blockquote>
            <figcaption class="flex items-center justify-center mt-6 space-x-3">
                <img class="w-6 h-6 rounded-full" src="https://api.dicebear.com/6.x/avataaars/svg?seed=1" alt="profile picture">
                <div class="flex items-center divide-x-2 divide-gray-500 dark:divide-gray-700">
                    <div class="pr-3 font-medium text-gray-900 dark:text-white">John Doe</div>
                    <div class="pl-3 text-sm font-light text-gray-500 dark:text-gray-400">HR Director at TechCorp</div>
                </div>
            </figcaption>
        </figure>
    </div>
</section>

<!-- Footer -->
<footer class="bg-white dark:bg-gray-900">
    <div class="mx-auto w-full max-w-screen-xl p-4 py-6 lg:py-8">
        <div class="md:flex md:justify-between">
            <div class="mb-6 md:mb-0">
                <a href="{% url 'welcome' %}" class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-robot text-lg text-white"></i>
                    </div>
                    <span class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">CV Analyzer</span>
                </a>
            </div>
            <div class="grid grid-cols-2 gap-8 sm:gap-6 sm:grid-cols-3">
                <div>
                    <h2 class="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">Resources</h2>
                    <ul class="text-gray-500 dark:text-gray-400 font-medium">
                        <li class="mb-4">
                            <a href="#" class="hover:underline">Documentation</a>
                        </li>
                        <li>
                            <a href="#" class="hover:underline">API</a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h2 class="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">Follow us</h2>
                    <ul class="text-gray-500 dark:text-gray-400 font-medium">
                        <li class="mb-4">
                            <a href="#" class="hover:underline">Github</a>
                        </li>
                        <li>
                            <a href="#" class="hover:underline">Discord</a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h2 class="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">Legal</h2>
                    <ul class="text-gray-500 dark:text-gray-400 font-medium">
                        <li class="mb-4">
                            <a href="#" class="hover:underline">Privacy Policy</a>
                        </li>
                        <li>
                            <a href="#" class="hover:underline">Terms &amp; Conditions</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <hr class="my-6 border-gray-200 sm:mx-auto dark:border-gray-700 lg:my-8" />
        <div class="sm:flex sm:items-center sm:justify-between">
            <span class="text-sm text-gray-500 sm:text-center dark:text-gray-400">© 2024 <a href="{% url 'welcome' %}" class="hover:underline">CV Analyzer™</a>. All Rights Reserved.</span>
            <div class="flex mt-4 sm:justify-center sm:mt-0">
                <a href="#" class="text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5">
                    <i class="fab fa-facebook-f"></i>
                    <span class="sr-only">Facebook page</span>
                </a>
                <a href="#" class="text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5">
                    <i class="fab fa-twitter"></i>
                    <span class="sr-only">Twitter page</span>
                </a>
                <a href="#" class="text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5">
                    <i class="fab fa-github"></i>
                    <span class="sr-only">GitHub account</span>
                </a>
            </div>
        </div>
    </div>
</footer>

<!-- Background Elements -->
<canvas id="particles-bg"></canvas>
<div class="bg-gradient-overlay"></div>

<!-- Scripts -->
<script>
function playDemo() {
    alert('Demo video coming soon!');
}

// Job application functions
function applyToJob(vacancyId) {
    // Create a simple modal or redirect to upload page with vacancy ID
    if (confirm('Ready to apply for this position? You will be redirected to upload your CV.')) {
        window.location.href = `/public/apply/${vacancyId}/`;
    }
}

function viewJobDetails(vacancyId) {
    // Open job details in a new tab or modal
    window.open(`/public/vacancy/${vacancyId}/`, '_blank');
}

// Stats Counter Animation
function animateValue(element, start, end, duration) {
    let startTimestamp = null;
    const step = (timestamp) => {
        if (!startTimestamp) startTimestamp = timestamp;
        const progress = Math.min((timestamp - startTimestamp) / duration, 1);
        const value = Math.floor(progress * (end - start) + start);
        element.textContent = value + (element.dataset.suffix || '');
        if (progress < 1) {
            window.requestAnimationFrame(step);
        }
    };
    window.requestAnimationFrame(step);
}

// Intersection Observer for Stats
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('visible');
            const value = parseInt(entry.target.dataset.value);
            animateValue(entry.target, 0, value, 2000);
            observer.unobserve(entry.target);
        }
    });
}, {
    threshold: 0.5
});

document.querySelectorAll('.stat-number').forEach(stat => {
    observer.observe(stat);
});

// Ripple effect for buttons
document.querySelectorAll('.btn-primary').forEach(button => {
    button.addEventListener('click', function(e) {
        const rect = button.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const ripple = document.createElement('span');
        ripple.style.left = `${x}px`;
        ripple.style.top = `${y}px`;
        ripple.className = 'ripple';
        
        button.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
    });
});

// Feature card hover effect
document.querySelectorAll('.feature-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.querySelector('.icon-wrapper').style.transform = 'scale(1.1) rotate(5deg)';
    });
    
    card.addEventListener('mouseleave', function() {
        this.querySelector('.icon-wrapper').style.transform = 'scale(1) rotate(0)';
    });
});

// Three.js Background
function initThreeBackground() {
    const canvas = document.getElementById('bg-canvas');
    const renderer = new THREE.WebGLRenderer({ canvas, alpha: true });
    renderer.setSize(window.innerWidth, window.innerHeight);

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.z = 5;

    // Create particles
    const particlesGeometry = new THREE.BufferGeometry();
    const particlesCount = 1000;
    const posArray = new Float32Array(particlesCount * 3);

    for(let i = 0; i < particlesCount * 3; i++) {
        posArray[i] = (Math.random() - 0.5) * 10;
    }

    particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));

    const particlesMaterial = new THREE.PointsMaterial({
        size: 0.005,
        color: isDarkMode() ? 0x4A90E2 : 0x000000,
        transparent: true,
        opacity: 0.5
    });

    const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
    scene.add(particlesMesh);

    // Animation
    function animate() {
        requestAnimationFrame(animate);
        particlesMesh.rotation.x += 0.0001;
        particlesMesh.rotation.y += 0.0001;
        renderer.render(scene, camera);
    }

    animate();

    // Handle resize
    window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    });
}

// Check dark mode
function isDarkMode() {
    return document.documentElement.getAttribute('data-theme') === 'dark';
}

// Initialize background
initThreeBackground();
</script>
{% endblock %}