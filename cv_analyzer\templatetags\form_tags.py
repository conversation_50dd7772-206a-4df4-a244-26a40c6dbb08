from django import template

register = template.Library()

@register.filter(name='add_class')
def add_class(field, css_class):
    return field.as_widget(attrs={"class": css_class})

@register.filter(name='mul')
def mul(value, arg):
    """Multiply the value by the argument."""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter(name='split')
def split(value, arg=','):
    """Split the value by the argument (default comma)."""
    if value:
        return [item.strip() for item in value.split(arg) if item.strip()]
    return []

@register.filter(name='trim')
def trim(value):
    """Remove leading and trailing whitespace."""
    if value:
        return value.strip()
    return value 