"""
Unit tests for CV Analyzer models
Comprehensive testing of all model functionality
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.utils import timezone
from datetime import timedelta
import tempfile
import os

from cv_analyzer.models import (
    Company, Vacancy, CV, CVAnalysis, ApplicantProfile,
    AIConfig, AIAPIConfig, AIPromptConfig, UnifiedAnalysis,
    CompanyAnalysis, VacancyAnalysis, AnalysisProcess,
    WelcomeContent, ComparisonAnalysis, AIUsageLog,
    SecurityAuditLog, SecurityAlert, FileUploadLog,
    TwoFactorAuth, UserSession, DataProcessingConsent,
    DataSubjectRequest, EncryptedData, DataRetentionPolicy,
    DataProcessingActivity, BusinessAuditLog, APIUsageLog,
    WorkflowInstance, TaskMetricsLog, AIProviderMetrics,
    BusinessRuleConfig, ProgressSession, PWAInstallation,
    PushSubscription, NotificationLog, AccessibilityAudit,
    UserInteractionLog, OfflineData, FrontendPerformanceMetric,
    MonitoringMetric, SecurityEvent, VulnerabilityReport,
    DeploymentLog, HealthCheckResult, AuditLog, AlertConfiguration
)


class CompanyModelTest(TestCase):
    """Test Company model"""
    
    def setUp(self):
        self.company_data = {
            'name': 'Test Company',
            'description': 'A test company description',
            'website': 'https://testcompany.com',
            'industry': 'Technology',
            'size': 'Medium'
        }

    def test_company_creation(self):
        """Test creating a company"""
        company = Company.objects.create(**self.company_data)
        self.assertEqual(company.name, 'Test Company')
        self.assertEqual(company.industry, 'Technology')
        self.assertTrue(company.created_at)
        self.assertTrue(company.updated_at)

    def test_company_str_method(self):
        """Test company string representation"""
        company = Company.objects.create(**self.company_data)
        self.assertEqual(str(company), 'Test Company')

    def test_company_fields_validation(self):
        """Test company field validation"""
        # Test with missing required fields
        with self.assertRaises(IntegrityError):
            Company.objects.create(name='', industry='Technology', size='Medium')

    def test_company_meta_options(self):
        """Test company meta options"""
        company = Company.objects.create(**self.company_data)
        self.assertEqual(company._meta.verbose_name_plural, 'companies')


class VacancyModelTest(TestCase):
    """Test Vacancy model"""
    
    def setUp(self):
        self.company = Company.objects.create(
            name='Test Company',
            industry='Technology',
            size='Medium'
        )
        self.vacancy_data = {
            'company': self.company,
            'title': 'Software Engineer',
            'description': 'A software engineering position',
            'requirements': 'Python, Django experience required',
            'category': 'Engineering',
            'status': 'active'
        }

    def test_vacancy_creation(self):
        """Test creating a vacancy"""
        vacancy = Vacancy.objects.create(**self.vacancy_data)
        self.assertEqual(vacancy.title, 'Software Engineer')
        self.assertEqual(vacancy.status, 'active')
        self.assertEqual(vacancy.company, self.company)

    def test_vacancy_str_method(self):
        """Test vacancy string representation"""
        vacancy = Vacancy.objects.create(**self.vacancy_data)
        expected = f"Software Engineer at {self.company.name}"
        self.assertEqual(str(vacancy), expected)

    def test_vacancy_status_choices(self):
        """Test vacancy status choices"""
        valid_statuses = ['draft', 'active', 'closed', 'archived']
        for status in valid_statuses:
            vacancy_data = self.vacancy_data.copy()
            vacancy_data['status'] = status
            vacancy = Vacancy.objects.create(**vacancy_data)
            self.assertEqual(vacancy.status, status)

    def test_vacancy_company_relationship(self):
        """Test vacancy-company relationship"""
        vacancy = Vacancy.objects.create(**self.vacancy_data)
        self.assertEqual(vacancy.company.name, 'Test Company')
        
        # Test cascade delete
        company_id = self.company.id
        self.company.delete()
        self.assertFalse(Vacancy.objects.filter(company_id=company_id).exists())


class CVModelTest(TestCase):
    """Test CV model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )
        self.applicant_profile = ApplicantProfile.objects.create(user=self.user)
        
        # Create a temporary file for testing
        self.temp_file = tempfile.NamedTemporaryFile(
            suffix='.pdf', 
            delete=False
        )
        self.temp_file.write(b'%PDF-1.4 test content')
        self.temp_file.close()

    def tearDown(self):
        # Clean up the temporary file
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

    def test_cv_creation(self):
        """Test creating a CV"""
        cv = CV.objects.create(
            file=self.temp_file.name,
            status='incomplete',
            source='local',
            applicant_profile=self.applicant_profile
        )
        self.assertEqual(cv.status, 'incomplete')
        self.assertEqual(cv.source, 'local')
        self.assertEqual(cv.applicant_profile, self.applicant_profile)

    def test_cv_status_choices(self):
        """Test CV status choices"""
        valid_statuses = ['incomplete', 'ready', 'rejected']
        for status in valid_statuses:
            cv = CV.objects.create(
                file=self.temp_file.name,
                status=status,
                source='local',
                applicant_profile=self.applicant_profile
            )
            self.assertEqual(cv.status, status)

    def test_cv_source_choices(self):
        """Test CV source choices"""
        valid_sources = ['local', 'shared', 'onedrive', 'googledrive', 'email', 'whatsapp', 'telegram']
        for source in valid_sources:
            cv = CV.objects.create(
                file=self.temp_file.name,
                status='incomplete',
                source=source,
                applicant_profile=self.applicant_profile
            )
            self.assertEqual(cv.source, source)

    def test_cv_str_method(self):
        """Test CV string representation"""
        cv = CV.objects.create(
            file=self.temp_file.name,
            status='incomplete',
            source='local',
            applicant_profile=self.applicant_profile
        )
        expected = f"CV {cv.id} - local"
        self.assertEqual(str(cv), expected)


class CVAnalysisModelTest(TestCase):
    """Test CVAnalysis model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )
        self.applicant_profile = ApplicantProfile.objects.create(user=self.user)
        
        # Create a temporary file for testing
        self.temp_file = tempfile.NamedTemporaryFile(
            suffix='.pdf', 
            delete=False
        )
        self.temp_file.write(b'%PDF-1.4 test content')
        self.temp_file.close()
        
        self.cv = CV.objects.create(
            file=self.temp_file.name,
            status='incomplete',
            source='local',
            applicant_profile=self.applicant_profile
        )

    def tearDown(self):
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

    def test_cv_analysis_creation(self):
        """Test creating CV analysis"""
        analysis = CVAnalysis.objects.create(
            cv=self.cv,
            overall_score=85,
            content_score=80,
            format_score=90,
            sections_score=85,
            skills_score=88,
            style_score=82,
            name='John Doe',
            email='<EMAIL>',
            years_of_experience=5
        )
        self.assertEqual(analysis.overall_score, 85)
        self.assertEqual(analysis.name, 'John Doe')
        self.assertEqual(analysis.cv, self.cv)

    def test_cv_analysis_one_to_one_relationship(self):
        """Test one-to-one relationship with CV"""
        analysis = CVAnalysis.objects.create(
            cv=self.cv,
            overall_score=85
        )
        
        # Should not be able to create another analysis for the same CV
        with self.assertRaises(IntegrityError):
            CVAnalysis.objects.create(
                cv=self.cv,
                overall_score=75
            )

    def test_cv_analysis_json_fields(self):
        """Test JSON fields in CV analysis"""
        analysis = CVAnalysis.objects.create(
            cv=self.cv,
            overall_score=85,
            content_details={'structure': 'good', 'clarity': 'excellent'},
            format_details={'layout': 'professional', 'fonts': 'appropriate'}
        )
        self.assertEqual(analysis.content_details['structure'], 'good')
        self.assertEqual(analysis.format_details['layout'], 'professional')


class AIConfigModelTest(TestCase):
    """Test AI configuration models"""
    
    def test_ai_config_creation(self):
        """Test creating AI config"""
        config = AIConfig.objects.create(
            cache_ttl=3600,
            batch_size=5,
            max_retries=2,
            retry_delay=500
        )
        self.assertEqual(config.cache_ttl, 3600)
        self.assertEqual(config.batch_size, 5)

    def test_ai_api_config_creation(self):
        """Test creating AI API config"""
        config = AIAPIConfig.objects.create(
            provider='openai',
            api_key='test-key',
            model_name='gpt-3.5-turbo',
            priority=1,
            max_tokens=1000,
            temperature=0.7
        )
        self.assertEqual(config.provider, 'openai')
        self.assertEqual(config.priority, 1)

    def test_ai_api_config_ordering(self):
        """Test AI API config ordering by priority"""
        config1 = AIAPIConfig.objects.create(
            provider='openai',
            api_key='key1',
            model_name='gpt-3.5-turbo',
            priority=2
        )
        config2 = AIAPIConfig.objects.create(
            provider='groq',
            api_key='key2', 
            model_name='mixtral-8x7b',
            priority=1
        )
        
        configs = list(AIAPIConfig.objects.all())
        self.assertEqual(configs[0].priority, 1)
        self.assertEqual(configs[1].priority, 2)


class SecurityModelTest(TestCase):
    """Test security-related models"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )

    def test_security_audit_log_creation(self):
        """Test creating security audit log"""
        log = SecurityAuditLog.objects.create(
            event_type='login_success',
            username='testuser',
            user=self.user,
            ip_address='***********',
            request_path='/login/',
            request_method='POST',
            success=True
        )
        self.assertEqual(log.event_type, 'login_success')
        self.assertEqual(log.user, self.user)
        self.assertTrue(log.success)

    def test_security_alert_creation(self):
        """Test creating security alert"""
        alert = SecurityAlert.objects.create(
            alert_type='brute_force',
            severity='high',
            ip_address='***********00',
            request_path='/login/',
            description='Multiple failed login attempts'
        )
        self.assertEqual(alert.alert_type, 'brute_force')
        self.assertEqual(alert.severity, 'high')
        self.assertEqual(alert.status, 'open')  # Default status

    def test_file_upload_log_creation(self):
        """Test creating file upload log"""
        log = FileUploadLog.objects.create(
            filename='test.pdf',
            original_filename='original_test.pdf',
            file_size=1024,
            file_type='application/pdf',
            mime_type='application/pdf',
            md5_hash='abcd1234',
            sha256_hash='efgh5678',
            upload_path='/uploads/test.pdf',
            status='uploaded',
            user=self.user,
            ip_address='***********'
        )
        self.assertEqual(log.filename, 'test.pdf')
        self.assertEqual(log.user, self.user)
        self.assertEqual(log.status, 'uploaded')


class MonitoringModelTest(TestCase):
    """Test monitoring and DevOps models"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )

    def test_monitoring_metric_creation(self):
        """Test creating monitoring metric"""
        metric = MonitoringMetric.objects.create(
            metric_type='cpu',
            metric_name='cpu_usage_percent',
            value=75.5,
            unit='percent',
            source='system'
        )
        self.assertEqual(metric.metric_type, 'cpu')
        self.assertEqual(metric.value, 75.5)

    def test_security_event_creation(self):
        """Test creating security event"""
        event = SecurityEvent.objects.create(
            event_type='sql_injection_attempt',
            severity='high',
            ip_address='***********00',
            user=self.user,
            request_path='/search/',
            threat_type='sql_injection',
            risk_score=85
        )
        self.assertEqual(event.event_type, 'sql_injection_attempt')
        self.assertEqual(event.risk_score, 85)

    def test_vulnerability_report_creation(self):
        """Test creating vulnerability report"""
        report = VulnerabilityReport.objects.create(
            scan_id='SCAN001',
            scan_type='dependency_scan',
            vulnerability_title='Outdated Django Version',
            description='Django version has known vulnerabilities',
            risk_level='medium',
            affected_component='django==3.2.0',
            recommendation='Upgrade to Django 4.2+'
        )
        self.assertEqual(report.scan_id, 'SCAN001')
        self.assertEqual(report.risk_level, 'medium')

    def test_deployment_log_creation(self):
        """Test creating deployment log"""
        deployment = DeploymentLog.objects.create(
            deployment_id='DEPLOY001',
            environment='staging',
            version='1.0.0',
            commit_hash='abcd1234',
            status='success',
            deployed_by=self.user,
            pipeline_type='github_actions'
        )
        self.assertEqual(deployment.deployment_id, 'DEPLOY001')
        self.assertEqual(deployment.status, 'success')


class ProgressSessionModelTest(TestCase):
    """Test progress session model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )

    def test_progress_session_creation(self):
        """Test creating progress session"""
        session = ProgressSession.objects.create(
            session_id='test_session_123',
            operation_type='cv_analysis',
            total_steps=5,
            current_step=2,
            status='in_progress',
            progress_percentage=40.0,
            user=self.user
        )
        self.assertEqual(session.session_id, 'test_session_123')
        self.assertEqual(session.current_step, 2)
        self.assertEqual(session.progress_percentage, 40.0)

    def test_progress_session_status_choices(self):
        """Test progress session status choices"""
        valid_statuses = ['initialized', 'in_progress', 'completed', 'failed']
        for status in valid_statuses:
            session = ProgressSession.objects.create(
                session_id=f'test_session_{status}',
                operation_type='cv_analysis',
                total_steps=5,
                status=status,
                user=self.user
            )
            self.assertEqual(session.status, status)


class AccessibilityAuditModelTest(TestCase):
    """Test accessibility audit model"""
    
    def test_accessibility_audit_creation(self):
        """Test creating accessibility audit"""
        audit = AccessibilityAudit.objects.create(
            page_url='/dashboard/',
            template_name='dashboard.html',
            wcag_level='AA',
            score=85.5,
            issues_count=3,
            warnings_count=1,
            audit_data={
                'color_contrast': 'pass',
                'keyboard_navigation': 'pass',
                'alt_text': 'warning'
            }
        )
        self.assertEqual(audit.wcag_level, 'AA')
        self.assertEqual(audit.score, 85.5)
        self.assertEqual(audit.audit_data['color_contrast'], 'pass')

    def test_accessibility_wcag_level_choices(self):
        """Test WCAG level choices"""
        valid_levels = ['A', 'AA', 'AAA', 'NC']
        for level in valid_levels:
            audit = AccessibilityAudit.objects.create(
                page_url=f'/page_{level}/',
                template_name=f'page_{level}.html',
                wcag_level=level,
                score=80.0,
                audit_data={}
            )
            self.assertEqual(audit.wcag_level, level)


class UserInteractionLogModelTest(TestCase):
    """Test user interaction log model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )

    def test_user_interaction_log_creation(self):
        """Test creating user interaction log"""
        interaction = UserInteractionLog.objects.create(
            user=self.user,
            session_id='session123',
            interaction_type='click',
            element_id='submit_button',
            element_class='btn btn-primary',
            page_url='/upload/',
            metadata={'x': 150, 'y': 200}
        )
        self.assertEqual(interaction.interaction_type, 'click')
        self.assertEqual(interaction.element_id, 'submit_button')
        self.assertEqual(interaction.metadata['x'], 150)


class FrontendPerformanceMetricModelTest(TestCase):
    """Test frontend performance metric model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )

    def test_frontend_performance_metric_creation(self):
        """Test creating frontend performance metric"""
        metric = FrontendPerformanceMetric.objects.create(
            page_url='/dashboard/',
            user=self.user,
            load_time=2.5,
            dom_ready_time=1.8,
            first_paint_time=1.2,
            largest_contentful_paint=2.1,
            cumulative_layout_shift=0.05,
            javascript_errors=0,
            device_type='desktop',
            browser='Chrome'
        )
        self.assertEqual(metric.load_time, 2.5)
        self.assertEqual(metric.device_type, 'desktop')
        self.assertEqual(metric.javascript_errors, 0)


class AlertConfigurationModelTest(TestCase):
    """Test alert configuration model"""
    
    def test_alert_configuration_creation(self):
        """Test creating alert configuration"""
        config = AlertConfiguration.objects.create(
            name='High CPU Usage Alert',
            alert_type='metric_threshold',
            enabled=True,
            metric_name='cpu_usage_percent',
            threshold_value=80.0,
            comparison_operator='>',
            time_window_minutes=5,
            notification_channels=['email', 'slack']
        )
        self.assertEqual(config.name, 'High CPU Usage Alert')
        self.assertEqual(config.threshold_value, 80.0)
        self.assertTrue(config.enabled)
        self.assertIn('email', config.notification_channels)

    def test_alert_configuration_unique_name(self):
        """Test alert configuration name uniqueness"""
        AlertConfiguration.objects.create(
            name='Unique Alert',
            alert_type='metric_threshold'
        )
        
        with self.assertRaises(IntegrityError):
            AlertConfiguration.objects.create(
                name='Unique Alert',
                alert_type='health_check'
            )


class PWAModelTest(TestCase):
    """Test PWA-related models"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )

    def test_pwa_installation_creation(self):
        """Test creating PWA installation record"""
        installation = PWAInstallation.objects.create(
            user=self.user,
            device_type='mobile',
            platform='Android',
            browser='Chrome'
        )
        self.assertEqual(installation.device_type, 'mobile')
        self.assertEqual(installation.platform, 'Android')
        self.assertTrue(installation.is_active)

    def test_push_subscription_creation(self):
        """Test creating push subscription"""
        subscription = PushSubscription.objects.create(
            user=self.user,
            endpoint='https://fcm.googleapis.com/fcm/send/abc123',
            p256dh_key='test_p256dh_key',
            auth_key='test_auth_key'
        )
        self.assertEqual(subscription.user, self.user)
        self.assertTrue(subscription.is_active)

    def test_notification_log_creation(self):
        """Test creating notification log"""
        notification = NotificationLog.objects.create(
            user=self.user,
            notification_type='cv_analysis_complete',
            title='CV Analysis Complete',
            message='Your CV analysis has been completed.',
            delivery_status='delivered'
        )
        self.assertEqual(notification.notification_type, 'cv_analysis_complete')
        self.assertEqual(notification.delivery_status, 'delivered')


class ModelValidationTest(TestCase):
    """Test model validation and constraints"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )

    def test_email_field_validation(self):
        """Test email field validation"""
        from cv_analyzer.models import CVAnalysis, CV, ApplicantProfile
        
        # Create valid CV and profile
        profile = ApplicantProfile.objects.create(user=self.user)
        
        temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
        temp_file.write(b'%PDF-1.4 test')
        temp_file.close()
        
        cv = CV.objects.create(
            file=temp_file.name,
            applicant_profile=profile
        )
        
        # Test valid email
        analysis = CVAnalysis.objects.create(
            cv=cv,
            email='<EMAIL>'
        )
        self.assertEqual(analysis.email, '<EMAIL>')
        
        # Clean up
        os.unlink(temp_file.name)

    def test_positive_integer_validation(self):
        """Test positive integer field validation"""
        from cv_analyzer.models import CVAnalysis, CV, ApplicantProfile
        
        profile = ApplicantProfile.objects.create(user=self.user)
        
        temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
        temp_file.write(b'%PDF-1.4 test')
        temp_file.close()
        
        cv = CV.objects.create(
            file=temp_file.name,
            applicant_profile=profile
        )
        
        # Test valid positive integer
        analysis = CVAnalysis.objects.create(
            cv=cv,
            years_of_experience=5
        )
        self.assertEqual(analysis.years_of_experience, 5)
        
        # Clean up
        os.unlink(temp_file.name)

    def test_json_field_default_values(self):
        """Test JSON field default values"""
        from cv_analyzer.models import CVAnalysis, CV, ApplicantProfile
        
        profile = ApplicantProfile.objects.create(user=self.user)
        
        temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
        temp_file.write(b'%PDF-1.4 test')
        temp_file.close()
        
        cv = CV.objects.create(
            file=temp_file.name,
            applicant_profile=profile
        )
        
        analysis = CVAnalysis.objects.create(cv=cv)
        
        # JSON fields should have dict default values
        self.assertIsInstance(analysis.content_details, dict)
        self.assertIsInstance(analysis.format_details, dict)
        self.assertIsInstance(analysis.additional_info, dict)
        
        # Clean up
        os.unlink(temp_file.name) 