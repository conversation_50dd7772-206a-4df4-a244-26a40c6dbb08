from typing import Dict, Any, Optional, List
import pdfplumber
import docx
import re
import logging
from pathlib import Path
import json
from dataclasses import dataclass, asdict
import spacy
from spacy.language import Language
from spacy.tokens import Doc
import fitz  # PyMuPDF
from PIL import Image
import pytesseract
import io

logger = logging.getLogger(__name__)

@dataclass
class Section:
    """Represents a section in the CV"""
    name: str
    content: str
    start_line: int
    end_line: int
    confidence: float

@dataclass
class CVMetadata:
    """Represents metadata extracted from the CV"""
    filename: str
    file_type: str
    page_count: int
    word_count: int
    has_images: bool
    language: str
    encoding: str
    sections: List[Section]

class CVTextExtractor:
    """Enhanced CV text extraction with section detection and metadata"""
    
    SECTION_PATTERNS = {
        'summary': r'(?i)(professional\s+summary|profile|objective)',
        'experience': r'(?i)(work\s+experience|employment|work\s+history)',
        'education': r'(?i)(education|academic|qualifications)',
        'skills': r'(?i)(skills|technical\s+skills|competencies)',
        'certifications': r'(?i)(certifications|certificates|accreditations)',
        'languages': r'(?i)(languages|language\s+skills)',
        'projects': r'(?i)(projects|portfolio)',
        'awards': r'(?i)(awards|achievements|honors)',
        'references': r'(?i)(references|recommendations)'
    }
    
    def __init__(self):
        self.nlp = self._load_spacy_model()
    
    @staticmethod
    def _load_spacy_model() -> Language:
        """Load spaCy model for text analysis"""
        try:
            return spacy.load('en_core_web_sm')
        except OSError:
            # Download if not available
            spacy.cli.download('en_core_web_sm')
            return spacy.load('en_core_web_sm')
    
    def extract_from_file(self, file_path: str) -> tuple[str, CVMetadata]:
        """Extract text and metadata from a CV file"""
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext == '.pdf':
            return self._extract_from_pdf(file_path)
        elif file_ext in ['.doc', '.docx']:
            return self._extract_from_docx(file_path)
        else:
            raise ValueError(f"Unsupported file type: {file_ext}")
    
    def _extract_from_pdf(self, file_path: str) -> tuple[str, CVMetadata]:
        """Extract text and metadata from PDF"""
        try:
            text = ""
            metadata = CVMetadata(
                filename=Path(file_path).name,
                file_type='pdf',
                page_count=0,
                word_count=0,
                has_images=False,
                language='',
                encoding='utf-8',
                sections=[]
            )
            
            # Use PyMuPDF for initial scan
            doc = fitz.open(file_path)
            metadata.page_count = len(doc)
            
            # Check for images
            for page in doc:
                if page.get_images():
                    metadata.has_images = True
                    # Extract text from images using OCR
                    for img in page.get_images():
                        try:
                            xref = img[0]
                            base_image = doc.extract_image(xref)
                            image_data = base_image["image"]
                            image = Image.open(io.BytesIO(image_data))
                            text += pytesseract.image_to_string(image) + "\n"
                        except Exception as e:
                            logger.warning(f"Failed to extract text from image: {e}")
            
            # Use pdfplumber for text extraction
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text() or ""
                    text += page_text + "\n"
            
            # Process extracted text
            doc = self.nlp(text)
            
            # Detect language
            metadata.language = doc.lang_
            
            # Count words
            metadata.word_count = len([token for token in doc if not token.is_space])
            
            # Detect sections
            metadata.sections = self._detect_sections(text)
            
            return text.strip(), metadata
            
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")
            raise
    
    def _extract_from_docx(self, file_path: str) -> tuple[str, CVMetadata]:
        """Extract text and metadata from DOCX"""
        try:
            doc = docx.Document(file_path)
            text = ""
            
            metadata = CVMetadata(
                filename=Path(file_path).name,
                file_type='docx',
                page_count=1,  # Approximate
                word_count=0,
                has_images=False,
                language='',
                encoding='utf-8',
                sections=[]
            )
            
            # Extract text and check for images
            for element in doc.element.body.iter():
                if element.tag.endswith('drawing'):
                    metadata.has_images = True
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            # Process extracted text
            nlp_doc = self.nlp(text)
            
            # Detect language
            metadata.language = nlp_doc.lang_
            
            # Count words
            metadata.word_count = len([token for token in nlp_doc if not token.is_space])
            
            # Detect sections
            metadata.sections = self._detect_sections(text)
            
            return text.strip(), metadata
            
        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {e}")
            raise
    
    def _detect_sections(self, text: str) -> List[Section]:
        """Detect sections in the CV text"""
        sections = []
        lines = text.split('\n')
        current_section = None
        section_start = 0
        
        for i, line in enumerate(lines):
            # Check each section pattern
            for section_name, pattern in self.SECTION_PATTERNS.items():
                if re.search(pattern, line, re.IGNORECASE):
                    # If we found a new section, save the previous one
                    if current_section:
                        sections.append(Section(
                            name=current_section,
                            content='\n'.join(lines[section_start:i]).strip(),
                            start_line=section_start,
                            end_line=i,
                            confidence=0.9
                        ))
                    
                    current_section = section_name
                    section_start = i
                    break
        
        # Add the last section
        if current_section:
            sections.append(Section(
                name=current_section,
                content='\n'.join(lines[section_start:]).strip(),
                start_line=section_start,
                end_line=len(lines),
                confidence=0.9
            ))
        
        return sections
    
    def extract_metadata(self, text: str) -> Dict[str, Any]:
        """Extract metadata from CV text"""
        doc = self.nlp(text)
        
        metadata = {
            'entities': {},
            'key_phrases': [],
            'language': doc.lang_,
            'word_count': len([token for token in doc if not token.is_space])
        }
        
        # Extract named entities
        for ent in doc.ents:
            if ent.label_ not in metadata['entities']:
                metadata['entities'][ent.label_] = []
            metadata['entities'][ent.label_].append(ent.text)
        
        # Extract key phrases (noun chunks)
        metadata['key_phrases'] = [chunk.text for chunk in doc.noun_chunks]
        
        return metadata
    
    def get_section_content(self, text: str, section_name: str) -> Optional[str]:
        """Get content of a specific section"""
        sections = self._detect_sections(text)
        for section in sections:
            if section.name.lower() == section_name.lower():
                return section.content
        return None 