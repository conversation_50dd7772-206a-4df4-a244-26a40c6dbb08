from django.urls import path, include
from django.contrib.auth import views as auth_views
from django.shortcuts import render
from . import views
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    # Specific API endpoints (must come before generic api/ include)
    path('api/test-endpoint/', views.test_api_endpoint, name='test_api_endpoint'),
    path('api/start-ai-analysis/', views.start_ai_analysis, name='start_ai_analysis'),
    path('api/cancel-ai-analysis/', views.cancel_ai_analysis, name='cancel_ai_analysis'),
    path('api/process-cv-data/', views.process_cv_data_api, name='process_cv_data_api'),
    
    # Generic API endpoints
    path('api/', include('cv_analyzer.api.urls')),
    
    # Web application endpoints
    path('', views.welcome, name='welcome'),
    path('accounts/login/', auth_views.LoginView.as_view(), name='login'),
    path('accounts/logout/', auth_views.LogoutView.as_view(), name='logout'),
    path('analysis-process/', views.analysis_process, name='analysis_process'),
    path('update-analysis-status/<int:process_id>/', views.update_analysis_status, name='update_analysis_status'),
    path('upload/', views.upload_local, name='upload'),
    path('results/<int:vacancy_id>/', views.results, name='results'),
    path('dashboard/', views.dashboard, name='dashboard'),
    path('create-company-profile/', views.create_company_profile, name='create_company_profile'),
    path('create-vacancy/', views.create_vacancy, name='create_vacancy'),
    path('applicant-profile/', views.applicant_profile, name='applicant_profile'),
    path('upload-cv/', views.upload_cv, name='upload_cv'),
    path('vacancy-list/', views.vacancy_list, name='vacancy_list'),
    path('apply-to-vacancy/<int:vacancy_id>/', views.apply_to_vacancy, name='apply_to_vacancy'),
    path('application-status/<int:analysis_id>/', views.application_status, name='application_status'),
    path('company-management/', views.company_management, name='company_management'),
    path('vacancy-management/', views.vacancy_management, name='vacancy_management'),
    path('cv-management/', views.cv_management, name='cv_management'),
    path('add-company/', views.add_company, name='add_company'),
    path('add-vacancy/', views.add_vacancy, name='add_vacancy'),
    
    # Vacancy CRUD operations
    path('vacancy/<int:vacancy_id>/edit/', views.edit_vacancy, name='edit_vacancy'),
    path('vacancy/<int:vacancy_id>/delete/', views.delete_vacancy, name='delete_vacancy'),
    path('vacancy/<int:vacancy_id>/detail/', views.vacancy_detail, name='vacancy_detail'),
    path('vacancy/<int:vacancy_id>/duplicate/', views.duplicate_vacancy, name='duplicate_vacancy'),
    path('vacancy/<int:vacancy_id>/candidates/', views.vacancy_candidates, name='vacancy_candidates'),
    path('test-candidates/', lambda request: render(request, 'cv_analyzer/test_candidates.html'), name='test_candidates'),
    
    # AI Analysis Hub
    path('ai-analysis-hub/', views.ai_analysis_hub, name='ai_analysis_hub'),
    path('upload/local/', views.upload_local, name='upload_local'),
    path('upload/shared/', views.upload_shared, name='upload_shared'),
    path('upload/onedrive/', views.upload_onedrive, name='upload_onedrive'),
    path('upload/googledrive/', views.upload_googledrive, name='upload_googledrive'),
    path('process/email/', views.process_email_cv_view, name='process_email_cv'),
    path('process/whatsapp/', views.process_whatsapp_cv_view, name='process_whatsapp_cv'),
    path('process/telegram/', views.process_telegram_cv_view, name='process_telegram_cv'),
    path('download-excel/<str:filename>/', views.download_excel, name='download_excel'),
    path('fetch-models/<int:config_id>/', views.fetch_available_models, name='fetch_available_models'),
    path('admin/fetch-models/', views.fetch_models, name='fetch_models'),
    path('admin/fetch-api-config/', views.fetch_api_config, name='fetch_api_config'),
    path('upload/batch/', views.upload_batch, name='upload_batch'),
    path('batch-results/<int:vacancy_id>/', views.batch_results, name='batch_results'),
    path('export-batch-results/<int:vacancy_id>/', views.export_batch_results, name='export_batch_results'),
    path('api/retry-analysis/<int:analysis_id>/', views.retry_analysis, name='retry_analysis'),
    
    # Enhanced CV Management URLs
    path('cv/<int:cv_id>/analyze/', views.analyze_cv_ajax, name='analyze_cv_ajax'),
    path('cv/<int:cv_id>/match/', views.match_cv_to_vacancies, name='match_cv_to_vacancies'),
    path('cv/<int:cv_id>/detail/', views.cv_detail_view, name='cv_detail_view'),
    path('cv/<int:cv_id>/detail/json/', views.cv_detail_json, name='cv_detail_json'),
    path('cv/export/', views.export_cvs, name='export_cvs'),
    path('cv/bulk-action/', views.bulk_cv_action, name='bulk_cv_action'),
    path('analysis/<int:analysis_id>/', views.analysis_detail, name='analysis_detail'),
    path('toggle-theme/', views.toggle_theme, name='toggle_theme'),
    
    # New unified interface URLs
    path('unified-dashboard/', views.dashboard, name='unified_dashboard'),
    path('operations-hub/', views.operations_hub, name='operations_hub'),
    path('api/operations/<str:entity_type>/', views.operations_api, name='operations_api'),
    
    # Duplication Check API URLs
    path('api/check-duplicates/', views.check_duplicates_api, name='check_duplicates_api'),
    path('api/merge-cvs/', views.merge_cvs_api, name='merge_cvs_api'),
    path('api/delete-cvs/', views.delete_cvs_api, name='delete_cvs_api'),
    
    # Operations Hub API URLs
    path('api/match-cvs/', views.match_cvs_api, name='match_cvs_api'),
    path('api/compare-cvs/', views.compare_cvs_api, name='compare_cvs_api'),
    
    # New AI-powered Analysis Features
    path('cv-matching-analysis/', views.cv_matching_analysis, name='cv_matching_analysis'),
    path('cv-duplication-check/', views.cv_duplication_check, name='cv_duplication_check'),
    
    # Public application endpoints (no login required)
    path('public/apply/<int:vacancy_id>/', views.public_apply_to_vacancy, name='public_apply_to_vacancy'),
    path('public/vacancy/<int:vacancy_id>/', views.public_vacancy_detail, name='public_vacancy_detail'),
    path('public/vacancies/', views.public_vacancy_list, name='public_vacancy_list'),
    
    # Debug endpoints
    path('debug/ai-config/', views.debug_ai_config, name='debug_ai_config'),
    path('debug/cv-database/', views.debug_cv_database, name='debug_cv_database'),
    
    # AI Configuration API endpoints
    path('api/ai-config/providers/', views.ai_config_providers_api, name='ai_config_providers_api'),
    path('api/ai-config/providers/<int:provider_id>/', views.ai_config_provider_detail_api, name='ai_config_provider_detail_api'),
    path('api/ai-config/providers/<int:provider_id>/test/', views.ai_config_test_connection_api, name='ai_config_test_connection_api'),
    path('api/ai-config/fetch-models/', views.ai_config_fetch_models_api, name='ai_config_fetch_models_api'),
    path('api/ai-config/general/', views.ai_config_general_api, name='ai_config_general_api'),
    path('api/ai-config/prompts/', views.ai_config_prompts_api, name='ai_config_prompts_api'),
    path('api/ai-config/prompts/<int:prompt_id>/', views.ai_config_prompt_detail_api, name='ai_config_prompt_detail_api'),

    # Diagnostic page (temporary)
    path('diagnostic/', lambda request: render(request, 'cv_analyzer/diagnostic.html'), name='diagnostic'),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)