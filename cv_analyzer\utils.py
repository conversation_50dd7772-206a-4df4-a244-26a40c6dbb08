import openai
import pdfplumber
import re
from django.conf import settings
from .models import CV, CVAnalysis, Company, Vacancy, AIPromptConfig
from .ai_utils import get_ai_response

def extract_info(text, prompt_name):
    prompt_config = AIPromptConfig.objects.get(name=prompt_name)
    return get_ai_response(text, prompt_config.prompt)

def process_cv_logic(cv, vacancy):
    
    with pdfplumber.open(cv.file.path) as pdf:
        text = "".join(page.extract_text() for page in pdf.pages)
    
    openai.api_key = settings.OPENAI_API_KEY
    
    name = extract_info(text, "extract_name")
    location = extract_info(text, "extract_location")
    contact = extract_info(text, "extract_contact")
    experience = extract_info(text, "extract_experience")
    education = extract_info(text, "extract_education")
    skills = extract_info(text, "extract_skills")
    past_roles = extract_info(text, "extract_past_roles")
    certifications = extract_info(text, "extract_certifications")
    
    compatibility_prompt = f"This CV ('{text}') is for the vacancy title '{vacancy.title}' with the description '{vacancy.description}'. The following requirements apply: '{vacancy.requirements}'. Provide a compatibility percentage and brief explanation."
    compatibility_response = extract_info(text, compatibility_prompt)
    compatibility_match = re.search(r'(\d+)%', compatibility_response)
    compatibility = int(compatibility_match.group(1)) if compatibility_match else 0
    
    analysis_text = compatibility_response
    
    experience_prompt = f"On a scale of 0-100, rate the relevance of this person's experience for the vacancy '{vacancy.title}': '{experience}'. Respond with only a number."
    education_prompt = f"On a scale of 0-100, rate the relevance of this person's education for the vacancy '{vacancy.title}': '{education}'. Respond with only a number."
    skills_prompt = f"On a scale of 0-100, rate the relevance of this person's skills for the vacancy '{vacancy.title}': '{skills}'. Respond with only a number."

    def safe_int_conversion(value, default=0):
        try:
            return int(value)
        except ValueError:
            return default

    experience_value = safe_int_conversion(extract_info(text, experience_prompt))
    education_value = safe_int_conversion(extract_info(text, education_prompt))
    skills_value = safe_int_conversion(extract_info(text, skills_prompt))

    analysis = CVAnalysis(
        cv=cv,
        name=name,
        location=location,
        contact=contact,
        experience=experience,
        education=education,
        skills=skills,
        past_roles=past_roles,
        certifications=certifications,
        compatibility=compatibility,
        analysis_text=analysis_text,
        experience_value=experience_value,
        education_value=education_value,
        skills_value=skills_value
    )
    analysis.save()

def analyze_company_and_vacancy(company, vacancy):
    prompt = f"Analyze this company: {company.description} and vacancy: {vacancy.description}"
    response = openai.Completion.create(engine="text-davinci-002", prompt=prompt, max_tokens=100)
    return response.choices[0].text.strip()

def check_cv_completeness(cv_text):
    prompt = f"Check if this CV is complete and list any missing information: {cv_text}"
    response = openai.Completion.create(engine="text-davinci-002", prompt=prompt, max_tokens=200)
    result = response.choices[0].text.strip()
    is_complete = "complete" in result.lower() and "missing" not in result.lower()
    return {"is_complete": is_complete, "missing_info": result if not is_complete else ""}

def generate_missing_info_questions(missing_info):
    prompt = f"Generate questions to ask for this missing information: {missing_info}"
    response = openai.Completion.create(engine="text-davinci-002", prompt=prompt, max_tokens=200)
    return response.choices[0].text.strip().split('\n')

def match_vacancy_with_profiles(vacancy, profiles):
    # Implement matching logic using OpenAI
    pass

def process_email_cv(email_content):
    # Extract CV from email and create ApplicantProfile
    pass

def process_whatsapp_cv(message_content):
    # Extract CV from WhatsApp message and create ApplicantProfile
    pass

def process_telegram_cv(message_content):
    # Extract CV from Telegram message and create ApplicantProfile
    pass

def monitor_shared_folder(folder_path):
    while True:
        for filename in os.listdir(folder_path):
            if filename.endswith('.pdf'):
                file_path = os.path.join(folder_path, filename)
                with open(file_path, 'rb') as f:
                    cv = CV(file=File(f, name=filename))
                    cv.save()
                os.remove(file_path)
        time.sleep(60)  # Check every minute

def process_cv_logic(analysis):
    cv = analysis.cv
    vacancy = analysis.vacancy
    
    with open(cv.file.path, 'rb') as file:
        cv_text = file.read().decode('utf-8', errors='ignore')
    
    prompt = f"Analyze this CV: '{cv_text}' for the job title '{vacancy.title}' with the description '{vacancy.description}'. Provide a comprehensive analysis and compatibility percentage."
    
    response = openai.Completion.create(engine="text-davinci-002", prompt=prompt, max_tokens=500)
    result = response.choices[0].text.strip()
    
    # Extract information from the result
    name = extract_info(result, "Name:")
    location = extract_info(result, "Location:")
    contact = extract_info(result, "Contact:")
    experience = extract_info(result, "Experience:")
    education = extract_info(result, "Education:")
    skills = extract_info(result, "Skills:")
    past_roles = extract_info(result, "Past Roles:")
    certifications = extract_info(result, "Certifications:")
    compatibility = extract_compatibility(result)
    
    # Update the analysis object
    analysis.name = name
    analysis.location = location
    analysis.contact = contact
    analysis.experience = experience
    analysis.education = education
    analysis.skills = skills
    analysis.past_roles = past_roles
    analysis.certifications = certifications
    analysis.compatibility = compatibility
    analysis.analysis_text = result
    analysis.experience_value = random.randint(0, 100)
    analysis.education_value = random.randint(0, 100)
    analysis.skills_value = random.randint(0, 100)
    analysis.save()

def extract_info(text, key):
    start = text.find(key)
    if start == -1:
        return ""
    end = text.find("\n", start)
    return text[start + len(key):end].strip() if end != -1 else text[start + len(key):].strip()

def extract_compatibility(text):
    match = re.search(r'(\d+)%', text)
    return int(match.group(1)) if match else 0

import openai
import requests

def get_ai_response(ai_config, prompt):
    if ai_config.provider == 'openai':
        openai.api_key = ai_config.api_key
        response = openai.Completion.create(
            engine=ai_config.model_name,
            prompt=prompt,
            max_tokens=500
        )
        return response.choices[0].text.strip()
    elif ai_config.provider == 'groq':
        # Implement Groq API call
        pass
    elif ai_config.provider == 'ollama':
        response = requests.post(
            ai_config.api_url,
            json={
                "model": ai_config.model_name,
                "prompt": prompt
            }
        )
        return response.json()['response']
    else:
        raise ValueError(f"Unsupported AI provider: {ai_config.provider}")