"""
Custom validators for CV Analyzer application.
Implements password complexity validation and other security validators.
"""

import re
from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _

class ComplexPasswordValidator:
    """
    Validate that the password contains at least one uppercase letter,
    one lowercase letter, one digit, and one special character.
    """
    
    def validate(self, password, user=None):
        """Validate password complexity."""
        errors = []
        
        # Check for uppercase letter
        if not re.search(r'[A-Z]', password):
            errors.append(_('Password must contain at least one uppercase letter.'))
        
        # Check for lowercase letter
        if not re.search(r'[a-z]', password):
            errors.append(_('Password must contain at least one lowercase letter.'))
        
        # Check for digit
        if not re.search(r'\d', password):
            errors.append(_('Password must contain at least one digit.'))
        
        # Check for special character
        if not re.search(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\?]', password):
            errors.append(_('Password must contain at least one special character (!@#$%^&*()_+-=[]{};\':"\\|,.<>?).'))
        
        # Check for common patterns
        if self._contains_common_patterns(password):
            errors.append(_('Password contains common patterns that are not allowed.'))
        
        # Check for sequential characters
        if self._contains_sequential_chars(password):
            errors.append(_('Password must not contain sequential characters (e.g., 123, abc).'))
        
        if errors:
            raise ValidationError(errors)
    
    def get_help_text(self):
        """Return help text for password requirements."""
        return _(
            "Your password must contain at least one uppercase letter, "
            "one lowercase letter, one digit, and one special character. "
            "It must not contain common patterns or sequential characters."
        )
    
    def _contains_common_patterns(self, password):
        """Check for common password patterns."""
        common_patterns = [
            r'(.)\1{2,}',  # Three or more consecutive identical characters
            r'password',
            r'admin',
            r'user',
            r'login',
            r'qwerty',
            r'123456',
            r'letmein',
            r'welcome',
            r'monkey',
            r'dragon',
        ]
        
        password_lower = password.lower()
        for pattern in common_patterns:
            if re.search(pattern, password_lower):
                return True
        return False
    
    def _contains_sequential_chars(self, password):
        """Check for sequential characters."""
        password_lower = password.lower()
        
        # Check for ascending sequences
        for i in range(len(password_lower) - 2):
            if (ord(password_lower[i]) + 1 == ord(password_lower[i + 1]) and
                ord(password_lower[i + 1]) + 1 == ord(password_lower[i + 2])):
                return True
        
        # Check for descending sequences
        for i in range(len(password_lower) - 2):
            if (ord(password_lower[i]) - 1 == ord(password_lower[i + 1]) and
                ord(password_lower[i + 1]) - 1 == ord(password_lower[i + 2])):
                return True
        
        return False

class UsernameValidator:
    """Validate username format and security."""
    
    def __call__(self, value):
        """Validate username."""
        if not re.match(r'^[a-zA-Z0-9_.-]+$', value):
            raise ValidationError(
                _('Username can only contain letters, numbers, dots, hyphens, and underscores.')
            )
        
        if len(value) < 3:
            raise ValidationError(_('Username must be at least 3 characters long.'))
        
        if len(value) > 30:
            raise ValidationError(_('Username must be no more than 30 characters long.'))
        
        # Check for reserved usernames
        reserved_usernames = [
            'admin', 'administrator', 'root', 'superuser', 'user',
            'api', 'www', 'mail', 'email', 'support', 'help',
            'info', 'contact', 'sales', 'marketing', 'security',
            'system', 'test', 'demo', 'guest', 'null', 'undefined'
        ]
        
        if value.lower() in reserved_usernames:
            raise ValidationError(_('This username is reserved and cannot be used.'))

class EmailDomainValidator:
    """Validate email domain against allowed/blocked lists."""
    
    def __init__(self, allowed_domains=None, blocked_domains=None):
        """Initialize with domain lists."""
        self.allowed_domains = allowed_domains or []
        self.blocked_domains = blocked_domains or [
            'tempmail.org', '10minutemail.com', 'guerrillamail.com',
            'mailinator.com', 'temp-mail.org', 'throwaway.email'
        ]
    
    def __call__(self, value):
        """Validate email domain."""
        if '@' not in value:
            raise ValidationError(_('Invalid email format.'))
        
        domain = value.split('@')[1].lower()
        
        # Check blocked domains
        if domain in self.blocked_domains:
            raise ValidationError(_('Email domain is not allowed.'))
        
        # Check allowed domains (if specified)
        if self.allowed_domains and domain not in self.allowed_domains:
            raise ValidationError(_('Email domain is not in the allowed list.'))

class FileExtensionValidator:
    """Enhanced file extension validator."""
    
    def __init__(self, allowed_extensions, message=None):
        """Initialize with allowed extensions."""
        self.allowed_extensions = [ext.lower() for ext in allowed_extensions]
        self.message = message or _(
            'File extension "%(extension)s" is not allowed. '
            'Allowed extensions are: %(allowed_extensions)s.'
        )
    
    def __call__(self, value):
        """Validate file extension."""
        if hasattr(value, 'name'):
            filename = value.name
        else:
            filename = str(value)
        
        if '.' not in filename:
            raise ValidationError(_('File must have an extension.'))
        
        extension = filename.split('.')[-1].lower()
        
        if extension not in self.allowed_extensions:
            raise ValidationError(
                self.message,
                params={
                    'extension': extension,
                    'allowed_extensions': ', '.join(self.allowed_extensions),
                }
            )

class FileSizeValidator:
    """Validate file size."""
    
    def __init__(self, max_size, message=None):
        """Initialize with maximum size in bytes."""
        self.max_size = max_size
        self.message = message or _(
            'File too large. Size should not exceed %(max_size)s bytes.'
        )
    
    def __call__(self, value):
        """Validate file size."""
        if hasattr(value, 'size'):
            file_size = value.size
        else:
            file_size = len(value)
        
        if file_size > self.max_size:
            raise ValidationError(
                self.message,
                params={'max_size': self.max_size}
            )

class IPAddressValidator:
    """Validate IP address format and restrictions."""
    
    def __init__(self, allowed_ranges=None, blocked_ranges=None):
        """Initialize with IP ranges."""
        import ipaddress
        self.allowed_ranges = []
        self.blocked_ranges = []
        
        if allowed_ranges:
            for range_str in allowed_ranges:
                try:
                    self.allowed_ranges.append(ipaddress.ip_network(range_str, strict=False))
                except ValueError:
                    pass
        
        if blocked_ranges:
            for range_str in blocked_ranges:
                try:
                    self.blocked_ranges.append(ipaddress.ip_network(range_str, strict=False))
                except ValueError:
                    pass
    
    def __call__(self, value):
        """Validate IP address."""
        import ipaddress
        
        try:
            ip = ipaddress.ip_address(value)
        except ValueError:
            raise ValidationError(_('Invalid IP address format.'))
        
        # Check blocked ranges
        for blocked_range in self.blocked_ranges:
            if ip in blocked_range:
                raise ValidationError(_('IP address is blocked.'))
        
        # Check allowed ranges (if specified)
        if self.allowed_ranges:
            allowed = False
            for allowed_range in self.allowed_ranges:
                if ip in allowed_range:
                    allowed = True
                    break
            
            if not allowed:
                raise ValidationError(_('IP address is not in allowed range.'))

class PhoneNumberValidator:
    """Validate phone number format."""
    
    def __call__(self, value):
        """Validate phone number."""
        # Remove all non-digit characters except + and ()
        cleaned = re.sub(r'[^\d+()]', '', value)
        
        # Basic validation
        if len(cleaned) < 7 or len(cleaned) > 20:
            raise ValidationError(_('Phone number must be between 7 and 20 digits.'))
        
        # Check for valid format patterns
        patterns = [
            r'^\+?1?[2-9]\d{2}[2-9]\d{2}\d{4}$',  # US format
            r'^\+?[1-9]\d{1,14}$',  # International format
        ]
        
        valid = False
        for pattern in patterns:
            if re.match(pattern, cleaned):
                valid = True
                break
        
        if not valid:
            raise ValidationError(_('Invalid phone number format.'))

class URLValidator:
    """Enhanced URL validator with security checks."""
    
    def __init__(self, allowed_schemes=None, blocked_domains=None):
        """Initialize with allowed schemes and blocked domains."""
        self.allowed_schemes = allowed_schemes or ['http', 'https']
        self.blocked_domains = blocked_domains or []
    
    def __call__(self, value):
        """Validate URL."""
        from urllib.parse import urlparse
        
        try:
            parsed = urlparse(value)
        except Exception:
            raise ValidationError(_('Invalid URL format.'))
        
        # Check scheme
        if parsed.scheme not in self.allowed_schemes:
            raise ValidationError(
                _('URL scheme "%(scheme)s" is not allowed.'),
                params={'scheme': parsed.scheme}
            )
        
        # Check blocked domains
        if parsed.netloc.lower() in self.blocked_domains:
            raise ValidationError(_('URL domain is blocked.'))
        
        # Check for suspicious patterns
        suspicious_patterns = [
            'javascript:', 'data:', 'vbscript:', 'file:', 'ftp:'
        ]
        
        value_lower = value.lower()
        for pattern in suspicious_patterns:
            if pattern in value_lower:
                raise ValidationError(_('URL contains suspicious content.'))

# Company and job-specific validators
class CompanyNameValidator:
    """Validate company name format."""
    
    def __call__(self, value):
        """Validate company name."""
        if len(value.strip()) < 2:
            raise ValidationError(_('Company name must be at least 2 characters long.'))
        
        if len(value) > 255:
            raise ValidationError(_('Company name must be no more than 255 characters long.'))
        
        # Check for suspicious patterns
        suspicious_patterns = ['<script', 'javascript:', 'eval(', 'alert(']
        value_lower = value.lower()
        
        for pattern in suspicious_patterns:
            if pattern in value_lower:
                raise ValidationError(_('Company name contains invalid content.'))

class JobTitleValidator:
    """Validate job title format."""
    
    def __call__(self, value):
        """Validate job title."""
        if len(value.strip()) < 2:
            raise ValidationError(_('Job title must be at least 2 characters long.'))
        
        if len(value) > 200:
            raise ValidationError(_('Job title must be no more than 200 characters long.'))
        
        # Check for valid characters (letters, numbers, spaces, common punctuation)
        if not re.match(r'^[a-zA-Z0-9\s\-_.,()&/]+$', value):
            raise ValidationError(_('Job title contains invalid characters.')) 