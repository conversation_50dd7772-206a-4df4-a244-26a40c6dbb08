#!/usr/bin/env python3
"""
Debug script to test the streaming request and see what's happening
"""

import requests
import json

def debug_stream_request():
    """Debug the streaming request"""
    
    print("🐛 Debug Streaming Request")
    print("=" * 40)
    
    # Get CSRF token first
    session = requests.Session()
    csrf_response = session.get("http://127.0.0.1:8000/vacancy/9/candidates/")
    
    if csrf_response.status_code != 200:
        print(f"❌ Failed to get CSRF token: {csrf_response.status_code}")
        return
    
    csrf_token = session.cookies.get('csrftoken')
    print(f"✅ CSRF token: {csrf_token}")
    
    # Test 1: Regular request (without streaming)
    print("\n📊 Test 1: Regular Request (stream_mode=False)")
    payload = {
        "cv_ids": [10],
        "vacancy_ids": [9],
        "analysis_type": "fast",
        "stream_mode": False
    }
    
    headers = {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrf_token,
        'Accept': 'application/json'
    }
    
    try:
        response = session.post('http://127.0.0.1:8000/api/start-ai-analysis/', 
                              json=payload, headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type')}")
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"Success: {result.get('success')}")
                print(f"Results: {len(result.get('results', []))}")
            except:
                print(f"Response: {response.text[:200]}...")
        else:
            print(f"Error: {response.text[:200]}...")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Streaming request
    print("\n📡 Test 2: Streaming Request (stream_mode=True)")
    payload['stream_mode'] = True
    headers['Accept'] = 'text/event-stream'
    
    try:
        response = session.post('http://127.0.0.1:8000/api/start-ai-analysis/', 
                              json=payload, headers=headers, stream=True)
        print(f"Status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type')}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("📺 Reading first few chunks...")
            
            chunk_count = 0
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    chunk_count += 1
                    print(f"Chunk {chunk_count}: {repr(chunk[:100])}...")
                    
                    if chunk_count >= 3:  # Just read first few chunks
                        break
        else:
            print(f"Error: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_stream_request() 