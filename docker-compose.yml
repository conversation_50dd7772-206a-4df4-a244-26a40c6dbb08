version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/app
      - media_volume:/app/media
      - static_volume:/app/static
    ports:
      - "8000:8000"
    env_file:
      - .env
    depends_on:
      - db
      - redis
      - celery
    networks:
      - cv_analyzer_network

  db:
    image: postgres:14-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=cv_analyzer
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    networks:
      - cv_analyzer_network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cv_analyzer_network

  celery:
    build:
      context: .
      dockerfile: Dockerfile
    command: celery -A cv_analyzer worker -l info
    volumes:
      - .:/app
      - media_volume:/app/media
    env_file:
      - .env
    depends_on:
      - redis
      - db
    networks:
      - cv_analyzer_network

  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile
    command: celery -A cv_analyzer beat -l info
    volumes:
      - .:/app
    env_file:
      - .env
    depends_on:
      - redis
      - db
    networks:
      - cv_analyzer_network

  flower:
    build:
      context: .
      dockerfile: Dockerfile
    command: celery -A cv_analyzer flower --port=5555
    ports:
      - "5555:5555"
    env_file:
      - .env
    depends_on:
      - redis
      - celery
    networks:
      - cv_analyzer_network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - static_volume:/app/static
      - media_volume:/app/media
    depends_on:
      - web
    networks:
      - cv_analyzer_network

volumes:
  postgres_data:
  redis_data:
  media_volume:
  static_volume:

networks:
  cv_analyzer_network:
    driver: bridge 