#!/bin/bash

set -e

# Function to check if PostgreSQL is ready
postgres_ready() {
    python << END
import sys
import psycopg2
try:
    psycopg2.connect(
        dbname="${POSTGRES_DB}",
        user="${POSTGRES_USER}",
        password="${POSTGRES_PASSWORD}",
        host="${POSTGRES_HOST}",
        port="${POSTGRES_PORT}",
    )
except psycopg2.OperationalError:
    sys.exit(-1)
sys.exit(0)
END
}

# Function to check if Redis is ready
redis_ready() {
    python << END
import sys
import redis
try:
    redis.Redis.from_url("${REDIS_URL}").ping()
except redis.exceptions.ConnectionError:
    sys.exit(-1)
sys.exit(0)
END
}

# Wait for PostgreSQL
until postgres_ready; do
  >&2 echo "PostgreSQL is unavailable - sleeping"
  sleep 1
done

>&2 echo "PostgreSQL is up - continuing..."

# Wait for Redis
until redis_ready; do
  >&2 echo "Redis is unavailable - sleeping"
  sleep 1
done

>&2 echo "Redis is up - continuing..."

# Apply database migrations
echo "Applying database migrations..."
python manage.py migrate

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput

# Create cache tables
echo "Creating cache tables..."
python manage.py createcachetable

# Start Celery worker if command contains celery worker
if [[ "$*" == *"celery worker"* ]]; then
    echo "Starting Celery worker..."
    exec "$@"
fi

# Start Celery beat if command contains celery beat
if [[ "$*" == *"celery beat"* ]]; then
    echo "Starting Celery beat..."
    rm -f './celerybeat.pid'
    exec "$@"
fi

# Start Flower if command contains flower
if [[ "$*" == *"flower"* ]]; then
    echo "Starting Flower..."
    exec "$@"
fi

# Start development server
if [[ "$*" == *"runserver"* ]]; then
    echo "Starting development server..."
    exec "$@"
fi

# Execute command passed to entrypoint
exec "$@" 