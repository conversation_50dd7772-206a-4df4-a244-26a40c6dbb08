#!/usr/bin/env python3
"""
<PERSON>ript to download additional CV samples for testing the CV analyzer system
"""

import os
import requests
import time
from pathlib import Path

# Create media/cvs directory if it doesn't exist
CV_DIR = Path("media/cvs")
CV_DIR.mkdir(parents=True, exist_ok=True)

# List of publicly available CV samples (these should be more reliable)
CV_SAMPLES = [
    {
        'url': 'https://cdn.zety.com/pages/cv_examples_software_engineer_1.pdf',
        'filename': 'software_engineer_zety.pdf'
    },
    {
        'url': 'https://www.careercup.com/resume',
        'filename': 'careercup_sample.html'
    },
    {
        'url': 'https://resumegenius.com/wp-content/uploads/nurse-resume-sample.pdf',
        'filename': 'nurse_resume_sample.pdf'
    },
    {
        'url': 'https://novoresume.com/resume-templates/download?template=vibes&format=pdf',
        'filename': 'novoresume_vibes.pdf'
    },
]

# Alternative: Create realistic CV content locally
SAMPLE_CV_CONTENTS = [
    {
        'filename': 'jane_smith_marketing.txt',
        'content': '''
JANE SMITH
Digital Marketing Manager
Email: <EMAIL> | Phone: (*************
LinkedIn: linkedin.com/in/janesmith | Location: New York, NY

PROFESSIONAL SUMMARY
Results-driven Digital Marketing Manager with 6+ years of experience in developing and executing integrated marketing campaigns. Proven track record of increasing brand awareness by 150% and generating $2M+ in qualified leads. Expert in SEO, SEM, social media marketing, and marketing automation.

WORK EXPERIENCE

Senior Digital Marketing Manager | TechFlow Solutions (2021-2023)
• Led digital marketing strategy for B2B SaaS platform, increasing MQLs by 200%
• Managed $500K annual marketing budget across multiple channels
• Implemented marketing automation workflows resulting in 45% increase in conversion rates
• Collaborated with sales team to achieve 120% of quarterly revenue targets

Digital Marketing Specialist | GrowthCorp (2019-2021)
• Developed and executed SEO strategy improving organic traffic by 180%
• Created and managed PPC campaigns with average ROAS of 350%
• Built email marketing campaigns with 25% open rate and 8% CTR
• Managed social media presence across LinkedIn, Twitter, and Facebook

Marketing Coordinator | StartupXYZ (2018-2019)
• Assisted in planning and executing marketing campaigns
• Created content for blog, social media, and email newsletters
• Conducted market research and competitor analysis
• Supported event planning and trade show coordination

EDUCATION
Bachelor of Science in Marketing
University of Business, New York (2014-2018)
GPA: 3.7/4.0

SKILLS
• Digital Marketing: SEO, SEM, PPC, Display Advertising
• Analytics: Google Analytics, HubSpot, Salesforce, Tableau
• Content Creation: Copywriting, Graphic Design, Video Editing
• Social Media: LinkedIn, Facebook, Twitter, Instagram
• Tools: WordPress, Mailchimp, Hootsuite, Canva

CERTIFICATIONS
• Google Ads Certified
• HubSpot Content Marketing Certification
• Facebook Blueprint Certification
• Google Analytics Individual Qualification (IQ)

ACHIEVEMENTS
• Increased organic search traffic by 300% in 18 months
• Generated $2.5M in pipeline revenue through digital campaigns
• Named "Marketing Professional of the Year" 2022
• Speaker at 3 industry conferences on digital marketing trends
'''
    },
    {
        'filename': 'mike_johnson_developer.txt', 
        'content': '''
MIKE JOHNSON
Full Stack Developer
Email: <EMAIL> | Phone: (*************
GitHub: github.com/mikejohnson | Location: San Francisco, CA

TECHNICAL SUMMARY
Full Stack Developer with 8+ years of experience building scalable web applications using modern technologies. Proficient in React, Node.js, Python, and cloud infrastructure. Strong background in agile development and DevOps practices.

TECHNICAL SKILLS
• Frontend: React, Vue.js, TypeScript, HTML5, CSS3, Tailwind CSS
• Backend: Node.js, Python, Django, Flask, Express.js
• Databases: PostgreSQL, MongoDB, Redis, MySQL
• Cloud: AWS (EC2, S3, Lambda, RDS), Docker, Kubernetes
• Tools: Git, Jenkins, Webpack, npm, yarn

PROFESSIONAL EXPERIENCE

Senior Full Stack Developer | FinanceFirst Advisory (2020-2023)
• Built responsive web applications serving 100k+ daily active users
• Implemented microservices architecture reducing system downtime by 60%
• Led team of 4 developers using agile methodologies
• Optimized database queries improving application performance by 40%

Full Stack Developer | RetailPlus Systems (2018-2020)
• Developed e-commerce platform handling $50M+ in annual transactions
• Integrated third-party APIs including payment gateways and shipping providers
• Implemented automated testing reducing bugs in production by 50%
• Collaborated with UX team to improve user experience metrics

Software Developer | EduTech Innovations (2016-2018)
• Built educational web applications using React and Django
• Developed RESTful APIs serving mobile and web clients
• Participated in code reviews and maintained coding standards
• Worked closely with product managers to implement new features

EDUCATION
Bachelor of Science in Computer Science
California Institute of Technology (2012-2016)
Relevant Coursework: Data Structures, Algorithms, Database Systems, Software Engineering

PROJECTS
• Personal Finance Tracker: Full-stack application with React frontend and Django backend
• Task Management API: RESTful API built with Node.js and PostgreSQL
• Weather Dashboard: Real-time weather application using React and external APIs

CERTIFICATIONS
• AWS Certified Solutions Architect - Associate
• Docker Certified Associate
• Certified Scrum Master (CSM)
'''
    },
    {
        'filename': 'sarah_wilson_analyst.txt',
        'content': '''
SARAH WILSON
Data Analyst
Email: <EMAIL> | Phone: (*************
LinkedIn: linkedin.com/in/sarahwilson | Location: Chicago, IL

PROFESSIONAL SUMMARY
Detail-oriented Data Analyst with 5+ years of experience transforming complex datasets into actionable business insights. Proficient in SQL, Python, R, and various visualization tools. Proven ability to improve business processes and drive data-driven decision making.

CORE COMPETENCIES
• Data Analysis: Statistical Analysis, Predictive Modeling, A/B Testing
• Programming: Python, R, SQL, VBA, MATLAB
• Visualization: Tableau, Power BI, matplotlib, ggplot2
• Databases: MySQL, PostgreSQL, Oracle, MongoDB
• Tools: Excel, SPSS, SAS, Jupyter, Git

PROFESSIONAL EXPERIENCE

Senior Data Analyst | HealthCare Innovations (2021-2023)
• Analyzed patient data to identify trends and improve treatment outcomes
• Built predictive models to forecast patient readmission rates with 85% accuracy
• Created automated dashboards reducing report generation time by 70%
• Collaborated with medical staff to interpret data and recommend improvements

Data Analyst | GreenEnergy Corp (2019-2021)
• Performed statistical analysis on energy consumption patterns
• Developed KPI dashboards for executive leadership team
• Conducted market research analysis supporting $10M investment decisions
• Optimized data collection processes improving data quality by 40%

Junior Data Analyst | Creative Design Studio (2018-2019)
• Analyzed customer behavior data to improve marketing campaigns
• Created reports on campaign performance and ROI
• Assisted in database design and data warehouse maintenance
• Supported ad-hoc analysis requests from various departments

EDUCATION
Master of Science in Data Science
Northwestern University, Chicago (2016-2018)

Bachelor of Science in Statistics
University of Illinois, Chicago (2012-2016)
Magna Cum Laude, GPA: 3.8/4.0

PROJECTS
• Customer Churn Prediction: Built machine learning model with 92% accuracy
• Sales Forecasting Dashboard: Created interactive Tableau dashboard for sales team
• Market Basket Analysis: Analyzed customer purchasing patterns for retail client

CERTIFICATIONS
• Tableau Desktop Specialist
• Microsoft Power BI Data Analyst Associate
• SAS Certified Base Programmer
• Google Analytics Individual Qualification
'''
    },
    {
        'filename': 'david_brown_manager.txt',
        'content': '''
DAVID BROWN
Project Manager
Email: <EMAIL> | Phone: (*************
LinkedIn: linkedin.com/in/davidbrown | Location: Austin, TX

PROFESSIONAL SUMMARY
Experienced Project Manager with 10+ years leading cross-functional teams and delivering complex projects on time and within budget. Certified PMP with expertise in Agile methodologies, risk management, and stakeholder communication. Proven track record of improving project delivery efficiency by 35%.

CORE COMPETENCIES
• Project Management: Agile, Scrum, Waterfall, Kanban
• Tools: Jira, Asana, Microsoft Project, Trello, Confluence
• Leadership: Team Building, Conflict Resolution, Performance Management
• Process Improvement: Lean Six Sigma, Change Management
• Communication: Stakeholder Management, Presentation Skills

PROFESSIONAL EXPERIENCE

Senior Project Manager | TechFlow Solutions (2020-2023)
• Managed portfolio of 8 concurrent projects worth $15M total value
• Led agile transformation initiative improving delivery speed by 40%
• Coordinated teams of 20+ developers, designers, and analysts
• Reduced project scope creep by implementing robust change control processes

Project Manager | Digital Marketing Pro (2017-2020)
• Delivered 25+ marketing technology implementations with 98% success rate
• Managed client relationships for enterprise accounts ($1M+ annual value)
• Implemented PMO standards and processes across organization
• Mentored junior project managers and conducted training sessions

Associate Project Manager | FinanceFirst Advisory (2015-2017)
• Supported senior PMs on financial services technology projects
• Coordinated project schedules, resources, and deliverables
• Facilitated daily standups and sprint planning meetings
• Maintained project documentation and status reports

Program Coordinator | EduTech Innovations (2013-2015)
• Assisted in planning and execution of educational software projects
• Tracked project milestones and managed project communications
• Supported budget tracking and vendor management activities
• Coordinated user acceptance testing and training sessions

EDUCATION
Master of Business Administration (MBA)
University of Texas, Austin (2011-2013)
Concentration: Operations Management

Bachelor of Science in Industrial Engineering
Texas A&M University (2007-2011)

CERTIFICATIONS
• Project Management Professional (PMP)
• Certified Scrum Master (CSM)
• Lean Six Sigma Green Belt
• ITIL Foundation Certification

ACHIEVEMENTS
• Successfully delivered $50M+ in project value over career
• Improved team productivity metrics by average of 30%
• Maintained 95%+ client satisfaction rating across all projects
• Led organizational change management initiatives affecting 200+ employees
'''
    }
]

def download_cv_files():
    """Download CV files from URLs"""
    print("Downloading CV samples...")
    download_count = 0
    
    for cv_data in CV_SAMPLES:
        try:
            print(f"Downloading {cv_data['filename']}...")
            response = requests.get(cv_data['url'], timeout=30)
            response.raise_for_status()
            
            file_path = CV_DIR / cv_data['filename']
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✓ Successfully downloaded {cv_data['filename']}")
            download_count += 1
            time.sleep(1)  # Be respectful to servers
            
        except Exception as e:
            print(f"✗ Failed to download {cv_data['filename']}: {str(e)}")
    
    return download_count

def create_sample_cvs():
    """Create sample CV files locally"""
    print("Creating sample CV files...")
    created_count = 0
    
    for cv_data in SAMPLE_CV_CONTENTS:
        try:
            file_path = CV_DIR / cv_data['filename']
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(cv_data['content'])
            
            print(f"✓ Created {cv_data['filename']}")
            created_count += 1
            
        except Exception as e:
            print(f"✗ Failed to create {cv_data['filename']}: {str(e)}")
    
    return created_count

def main():
    print("🚀 Downloading additional CV samples for testing...")
    print(f"Target directory: {CV_DIR.absolute()}")
    
    # Download from URLs
    downloaded = download_cv_files()
    
    # Create local samples
    created = create_sample_cvs()
    
    total = downloaded + created
    
    print(f"\n✅ Process completed!")
    print(f"📁 Total CV files added: {total}")
    print(f"   - Downloaded: {downloaded}")
    print(f"   - Created locally: {created}")
    print(f"\n📍 Files saved to: {CV_DIR.absolute()}")
    print(f"\n🌐 You can now test the full system at:")
    print(f"   - Dashboard: http://127.0.0.1:8000/dashboard/")
    print(f"   - Operations Hub: http://127.0.0.1:8000/operations-hub/")

if __name__ == "__main__":
    main() 