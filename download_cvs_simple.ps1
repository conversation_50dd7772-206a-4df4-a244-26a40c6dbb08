# Simple PowerShell Script to Download Sample CVs
# Downloads CV datasets from GitHub repositories

param(
    [string]$OutputPath = ".\test_cvs"
)

Write-Host "Starting CV Dataset Download Process..." -ForegroundColor Green
Write-Host "Output Directory: $OutputPath" -ForegroundColor Yellow

# Create output directories
$Directories = @(
    "$OutputPath\pdf_cvs",
    "$OutputPath\docx_cvs", 
    "$OutputPath\json_datasets",
    "$OutputPath\temp"
)

foreach ($dir in $Directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Cyan
    }
}

# Download function
function Download-Repo {
    param(
        [string]$RepoName,
        [string]$ZipUrl,
        [string]$TempPath
    )
    
    Write-Host "Downloading repository: $RepoName..." -ForegroundColor Yellow
    
    $zipPath = Join-Path $TempPath "$RepoName.zip"
    
    try {
        Invoke-WebRequest -Uri $ZipUrl -OutFile $zipPath -UseBasicParsing
        
        $extractPath = Join-Path $TempPath $RepoName
        if (Test-Path $extractPath) {
            Remove-Item $extractPath -Recurse -Force
        }
        
        Expand-Archive -Path $zipPath -DestinationPath $TempPath -Force
        
        # Find extracted folder
        $extractedFolder = Get-ChildItem $TempPath -Directory | Where-Object { $_.Name -like "*$RepoName*" -or $_.Name -like "*master*" } | Select-Object -First 1
        
        if ($extractedFolder) {
            Rename-Item $extractedFolder.FullName $extractPath
            Remove-Item $zipPath -Force
            return $extractPath
        }
        else {
            Write-Host "Could not find extracted folder for $RepoName" -ForegroundColor Red
            return $null
        }
    }
    catch {
        Write-Host "Download failed for $RepoName" -ForegroundColor Red
        return $null
    }
}

$totalFiles = 0

# Download curriculum_vitae_data repository
Write-Host "`nProcessing: curriculum_vitae_data" -ForegroundColor Green
$repo1Path = Download-Repo -RepoName "curriculum_vitae_data" -ZipUrl "https://github.com/arefinnomi/curriculum_vitae_data/archive/refs/heads/master.zip" -TempPath "$OutputPath\temp"

if ($repo1Path -and (Test-Path $repo1Path)) {
    Write-Host "Repository downloaded successfully" -ForegroundColor Green
    
    # Copy PDF files
    $pdfSource = Join-Path $repo1Path "pdf"
    if (Test-Path $pdfSource) {
        $pdfFiles = Get-ChildItem $pdfSource -Filter "*.pdf" -Recurse
        foreach ($file in $pdfFiles) {
            try {
                $destPath = Join-Path "$OutputPath\pdf_cvs" $file.Name
                Copy-Item $file.FullName $destPath -Force
                $totalFiles++
            }
            catch {
                Write-Host "Error copying PDF file: $($file.Name)" -ForegroundColor Red
            }
        }
        Write-Host "Copied $($pdfFiles.Count) PDF files" -ForegroundColor Cyan
    }
    
    # Copy DOCX files
    $docxSource = Join-Path $repo1Path "word"
    if (Test-Path $docxSource) {
        $docxFiles = Get-ChildItem $docxSource -Filter "*.docx" -Recurse
        foreach ($file in $docxFiles) {
            try {
                $destPath = Join-Path "$OutputPath\docx_cvs" $file.Name
                Copy-Item $file.FullName $destPath -Force
                $totalFiles++
            }
            catch {
                Write-Host "Error copying DOCX file: $($file.Name)" -ForegroundColor Red
            }
        }
        Write-Host "Copied $($docxFiles.Count) DOCX files" -ForegroundColor Cyan
    }
}

# Download resume-dataset repository
Write-Host "`nProcessing: resume-dataset" -ForegroundColor Green
$repo2Path = Download-Repo -RepoName "resume-dataset" -ZipUrl "https://github.com/juanfpinzon/resume-dataset/archive/refs/heads/master.zip" -TempPath "$OutputPath\temp"

if ($repo2Path -and (Test-Path $repo2Path)) {
    Write-Host "Repository downloaded successfully" -ForegroundColor Green
    
    $jsonFiles = Get-ChildItem $repo2Path -Filter "*.json" -Recurse
    foreach ($file in $jsonFiles) {
        try {
            $destPath = Join-Path "$OutputPath\json_datasets" $file.Name
            Copy-Item $file.FullName $destPath -Force
            $totalFiles++
        }
        catch {
            Write-Host "Error copying JSON file: $($file.Name)" -ForegroundColor Red
        }
    }
    Write-Host "Copied $($jsonFiles.Count) JSON dataset files" -ForegroundColor Cyan
}

# Clean up
Write-Host "`nCleaning up temporary files..." -ForegroundColor Yellow
$tempPath = "$OutputPath\temp"
if (Test-Path $tempPath) {
    Remove-Item $tempPath -Recurse -Force
}

# Summary
Write-Host "`nDOWNLOAD SUMMARY" -ForegroundColor Green
Write-Host "=================" -ForegroundColor Green
Write-Host "Output Directory: $OutputPath" -ForegroundColor Yellow
Write-Host "Total Files: $totalFiles" -ForegroundColor Green

# Create summary file
$summaryContent = @"
CV Dataset Download Summary
==========================
Date: $(Get-Date)
Output Directory: $OutputPath
Total Files Downloaded: $totalFiles

Directory Structure:
- pdf_cvs/      - PDF format CV files
- docx_cvs/     - Microsoft Word format CV files  
- json_datasets/- JSON datasets with NER annotations

Sources:
1. arefinnomi/curriculum_vitae_data - CV collection
2. juanfpinzon/resume-dataset - 545 CVs with annotations

Note: These files are for testing purposes only.
"@

$summaryPath = Join-Path $OutputPath "download_summary.txt"
$summaryContent | Out-File -FilePath $summaryPath -Encoding UTF8

Write-Host "`nDownload complete! Summary saved to: $summaryPath" -ForegroundColor Green

# Display contents
Write-Host "`nDirectory Contents:" -ForegroundColor Green
Get-ChildItem $OutputPath -Directory | ForEach-Object {
    $fileCount = (Get-ChildItem $_.FullName -File).Count
    Write-Host "  $($_.Name): $fileCount files" -ForegroundColor Cyan
} 