# PowerShell Script to Download Sample CVs for Testing
# This script downloads CV datasets from GitHub repositories

param(
    [string]$OutputPath = ".\test_cvs",
    [switch]$SkipExisting = $true
)

Write-Host "🔄 Starting CV Dataset Download Process..." -ForegroundColor Green
Write-Host "📁 Output Directory: $OutputPath" -ForegroundColor Yellow

# Create output directory structure
$Directories = @(
    "$OutputPath\pdf_cvs",
    "$OutputPath\docx_cvs", 
    "$OutputPath\json_datasets",
    "$OutputPath\temp"
)

foreach ($dir in $Directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "📂 Created directory: $dir" -ForegroundColor Cyan
    }
}

# Function to check if git is available
function Test-GitAvailable {
    try {
        git --version | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to download files from GitHub repository
function Download-GitHubRepo {
    param(
        [string]$RepoUrl,
        [string]$TempPath,
        [string]$RepoName
    )
    
    Write-Host "🔽 Downloading repository: $RepoName..." -ForegroundColor Yellow
    
    if (Test-GitAvailable) {
        # Use git clone if available
        $clonePath = Join-Path $TempPath $RepoName
        if (Test-Path $clonePath) {
            Remove-Item $clonePath -Recurse -Force
        }
        
        try {
            git clone $RepoUrl $clonePath --quiet
            return $clonePath
        }
        catch {
            Write-Host "❌ Git clone failed for $RepoName" -ForegroundColor Red
            return $null
        }
    }
    else {
        # Download as ZIP if git is not available
        Write-Host "⚠️  Git not found, downloading as ZIP..." -ForegroundColor Yellow
        $zipUrl = $RepoUrl -replace "\.git$", "/archive/refs/heads/master.zip"
        $zipPath = Join-Path $TempPath "$RepoName.zip"
        
        try {
            Invoke-WebRequest -Uri $zipUrl -OutFile $zipPath -UseBasicParsing
            
            # Extract ZIP
            $extractPath = Join-Path $TempPath $RepoName
            if (Test-Path $extractPath) {
                Remove-Item $extractPath -Recurse -Force
            }
            
            Expand-Archive -Path $zipPath -DestinationPath $TempPath -Force
            
            # Find the extracted folder (it might have a different name)
            $extractedFolder = Get-ChildItem $TempPath -Directory | Where-Object { $_.Name -like "*$RepoName*" -or $_.Name -like "*master*" } | Select-Object -First 1
            
            if ($extractedFolder) {
                Rename-Item $extractedFolder.FullName $extractPath
                Remove-Item $zipPath -Force
                return $extractPath
            }
            else {
                Write-Host "❌ Could not find extracted folder for $RepoName" -ForegroundColor Red
                return $null
            }
        }
        catch {
            Write-Host "❌ Download failed for ${RepoName}: $_" -ForegroundColor Red
            return $null
        }
    }
}

# Repository configurations
$Repositories = @(
    @{
        Name = "curriculum_vitae_data"
        Url = "https://github.com/arefinnomi/curriculum_vitae_data.git"
        Description = "Collection of CV files in PDF and DOCX formats"
    },
    @{
        Name = "resume-dataset"  
        Url = "https://github.com/juanfpinzon/resume-dataset.git"
        Description = "545 CVs with NER annotations in JSON format"
    }
)

$totalFiles = 0
$downloadStats = @{
    PDF = 0
    DOCX = 0
    JSON = 0
    Errors = 0
}

# Download each repository
foreach ($repo in $Repositories) {
    Write-Host "`n🔽 Processing: $($repo.Name)" -ForegroundColor Green
    Write-Host "📝 Description: $($repo.Description)" -ForegroundColor Gray
    
    $repoPath = Download-GitHubRepo -RepoUrl $repo.Url -TempPath "$OutputPath\temp" -RepoName $repo.Name
    
    if ($repoPath -and (Test-Path $repoPath)) {
        Write-Host "✅ Repository downloaded successfully" -ForegroundColor Green
        
        # Process curriculum_vitae_data repository
        if ($repo.Name -eq "curriculum_vitae_data") {
            # Copy PDF files
            $pdfSource = Join-Path $repoPath "pdf"
            if (Test-Path $pdfSource) {
                $pdfFiles = Get-ChildItem $pdfSource -Filter "*.pdf" -Recurse
                foreach ($file in $pdfFiles) {
                    try {
                        $destPath = Join-Path "$OutputPath\pdf_cvs" $file.Name
                        if (-not $SkipExisting -or -not (Test-Path $destPath)) {
                            Copy-Item $file.FullName $destPath -Force
                            $downloadStats.PDF++
                            $totalFiles++
                        }
                    }
                    catch {
                        Write-Host "❌ Error copying PDF file: $($file.Name)" -ForegroundColor Red
                        $downloadStats.Errors++
                    }
                }
                Write-Host "📄 Copied $($downloadStats.PDF) PDF files" -ForegroundColor Cyan
            }
            
            # Copy DOCX files
            $docxSource = Join-Path $repoPath "word"
            if (Test-Path $docxSource) {
                $docxFiles = Get-ChildItem $docxSource -Filter "*.docx" -Recurse
                foreach ($file in $docxFiles) {
                    try {
                        $destPath = Join-Path "$OutputPath\docx_cvs" $file.Name
                        if (-not $SkipExisting -or -not (Test-Path $destPath)) {
                            Copy-Item $file.FullName $destPath -Force
                            $downloadStats.DOCX++
                            $totalFiles++
                        }
                    }
                    catch {
                        Write-Host "❌ Error copying DOCX file: $($file.Name)" -ForegroundColor Red
                        $downloadStats.Errors++
                    }
                }
                Write-Host "📝 Copied $($downloadStats.DOCX) DOCX files" -ForegroundColor Cyan
            }
        }
        
        # Process resume-dataset repository  
        if ($repo.Name -eq "resume-dataset") {
            $jsonFiles = Get-ChildItem $repoPath -Filter "*.json" -Recurse
            foreach ($file in $jsonFiles) {
                try {
                    $destPath = Join-Path "$OutputPath\json_datasets" $file.Name
                    if (-not $SkipExisting -or -not (Test-Path $destPath)) {
                        Copy-Item $file.FullName $destPath -Force
                        $downloadStats.JSON++
                        $totalFiles++
                    }
                }
                catch {
                    Write-Host "❌ Error copying JSON file: $($file.Name)" -ForegroundColor Red
                    $downloadStats.Errors++
                }
            }
            Write-Host "📊 Copied $($downloadStats.JSON) JSON dataset files" -ForegroundColor Cyan
        }
    }
    else {
        Write-Host "❌ Failed to download repository: $($repo.Name)" -ForegroundColor Red
        $downloadStats.Errors++
    }
}

# Clean up temporary files
Write-Host "`n🧹 Cleaning up temporary files..." -ForegroundColor Yellow
$tempPath = "$OutputPath\temp"
if (Test-Path $tempPath) {
    Remove-Item $tempPath -Recurse -Force
}

# Generate summary report
Write-Host "`n📊 DOWNLOAD SUMMARY" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green
Write-Host "📁 Output Directory: $OutputPath" -ForegroundColor Yellow
Write-Host "📄 PDF Files: $($downloadStats.PDF)" -ForegroundColor Cyan
Write-Host "📝 DOCX Files: $($downloadStats.DOCX)" -ForegroundColor Cyan  
Write-Host "📊 JSON Datasets: $($downloadStats.JSON)" -ForegroundColor Cyan
Write-Host "📁 Total Files: $totalFiles" -ForegroundColor Green
if ($downloadStats.Errors -gt 0) {
    Write-Host "❌ Errors: $($downloadStats.Errors)" -ForegroundColor Red
}

# Create a summary file
$summaryContent = @"
CV Dataset Download Summary
==========================
Date: $(Get-Date)
Output Directory: $OutputPath

Files Downloaded:
- PDF Files: $($downloadStats.PDF)
- DOCX Files: $($downloadStats.DOCX)  
- JSON Datasets: $($downloadStats.JSON)
- Total Files: $totalFiles
- Errors: $($downloadStats.Errors)

Directory Structure:
- pdf_cvs/     - PDF format CV files
- docx_cvs/    - Microsoft Word format CV files
- json_datasets/ - JSON datasets with NER annotations

Sources:
1. arefinnomi/curriculum_vitae_data - CV collection in PDF/DOCX
2. juanfpinzon/resume-dataset - 545 CVs with NER annotations

Note: These files are for testing purposes only.
"@

$summaryPath = Join-Path $OutputPath "download_summary.txt"
$summaryContent | Out-File -FilePath $summaryPath -Encoding UTF8

Write-Host "`n✅ Download complete! Summary saved to: $summaryPath" -ForegroundColor Green
Write-Host "🎯 You can now use these CV files to test your analyzer!" -ForegroundColor Yellow

# Display folder contents
Write-Host "`n📂 Directory Contents:" -ForegroundColor Green
Get-ChildItem $OutputPath -Directory | ForEach-Object {
    $fileCount = (Get-ChildItem $_.FullName -File).Count
    Write-Host "  $($_.Name): $fileCount files" -ForegroundColor Cyan
} 