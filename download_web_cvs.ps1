# PowerShell Script to Download Web-Based CV/Resume Samples
# Downloads CV samples from various educational and professional websites

param(
    [string]$OutputPath = ".\web_cvs"
)

Write-Host "Starting Web CV Sample Download..." -ForegroundColor Green
Write-Host "Output Directory: $OutputPath" -ForegroundColor Yellow

# Create output directory
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "Created directory: $OutputPath" -ForegroundColor Cyan
}

$downloadCount = 0
$errorCount = 0

# Web sources for CV/Resume samples
$webSources = @(
    @{
        Name = "Bellevue University Resume Samples"
        Url = "https://msnlabs.com/img/resume-sample.pdf"
        FileName = "bellevue_resume_samples.pdf"
        Description = "Multiple resume format examples and templates"
    }
)

# Function to download with error handling
function Download-WebCV {
    param(
        [string]$Url,
        [string]$FileName,
        [string]$Description
    )
    
    $filePath = Join-Path $OutputPath $FileName
    
    try {
        Write-Host "Downloading: $FileName" -ForegroundColor Yellow
        Write-Host "  Source: $Url" -ForegroundColor Gray
        Write-Host "  Description: $Description" -ForegroundColor Gray
        
        Invoke-WebRequest -Uri $Url -OutFile $filePath -UseBasicParsing
        
        if (Test-Path $filePath) {
            $fileSize = [math]::Round((Get-Item $filePath).Length / 1KB, 1)
            Write-Host "  Downloaded: $fileSize KB" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "  Failed: File not created" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  Error: $_" -ForegroundColor Red
        return $false
    }
}

# Download from web sources
foreach ($source in $webSources) {
    Write-Host "`nProcessing: $($source.Name)" -ForegroundColor Green
    
    if (Download-WebCV -Url $source.Url -FileName $source.FileName -Description $source.Description) {
        $downloadCount++
    }
    else {
        $errorCount++
    }
}

# Try to download additional CV samples from other sources
Write-Host "`nTrying additional sources..." -ForegroundColor Green

# Sample CV URLs (some universities and career centers provide direct PDF access)
$additionalSources = @(
    @{
        Name = "Harvard Career Services CV Sample"
        Url = "https://hwpi.harvard.edu/files/ocs/files/hes-resume-cover-letter-guide.pdf"
        FileName = "harvard_cv_guide.pdf"
    },
    @{
        Name = "MIT Career Services Resume Guide"
        Url = "https://gecd.mit.edu/sites/default/files/about/files/resume-cover-letter-guide.pdf"  
        FileName = "mit_resume_guide.pdf"
    },
    @{
        Name = "Stanford Career Education Resume Guide"
        Url = "https://beam.stanford.edu/sites/g/files/sbiybj16681/files/media/file/resume_guide_2019.pdf"
        FileName = "stanford_resume_guide.pdf"
    }
)

foreach ($source in $additionalSources) {
    Write-Host "`nTrying: $($source.Name)" -ForegroundColor Cyan
    
    if (Download-WebCV -Url $source.Url -FileName $source.FileName -Description "Career guidance with CV samples") {
        $downloadCount++
    }
    else {
        $errorCount++
        Write-Host "  Skipping - may not be publicly accessible" -ForegroundColor Yellow
    }
}

# Try to download sample CVs from template sites
Write-Host "`nTrying template and example sites..." -ForegroundColor Green

$templateSources = @(
    @{
        Name = "Resume Template Sample 1"
        Url = "https://www.reed.co.uk/career-advice/wp-content/uploads/2018/08/Graduate-CV-template.pdf"
        FileName = "reed_graduate_cv_template.pdf"
    },
    @{
        Name = "Academic CV Sample"
        Url = "https://www.jobs.ac.uk/media/pdf/careers/resources/example-academic-cv.pdf"
        FileName = "academic_cv_sample.pdf"
    }
)

foreach ($source in $templateSources) {
    Write-Host "`nTrying: $($source.Name)" -ForegroundColor Cyan
    
    if (Download-WebCV -Url $source.Url -FileName $source.FileName -Description "Professional CV template") {
        $downloadCount++
    }
    else {
        $errorCount++
    }
}

# Create sample CV text files with common formats if downloads failed
if ($downloadCount -eq 0) {
    Write-Host "`nCreating sample CV text files as backup..." -ForegroundColor Yellow
    
    $sampleCV1 = @"
JOHN DOE
123 Main Street, City, State 12345
Phone: (************* | Email: <EMAIL>

PROFESSIONAL SUMMARY
Experienced software developer with 5+ years in web development and database management.
Strong background in Python, JavaScript, and SQL with proven track record of delivering
high-quality applications on time and within budget.

EDUCATION
Bachelor of Science in Computer Science
University of Technology, City, State
Graduated: May 2018
GPA: 3.7/4.0

PROFESSIONAL EXPERIENCE
Senior Software Developer | Tech Solutions Inc. | June 2020 - Present
• Developed and maintained web applications using Python and Django
• Collaborated with cross-functional teams to deliver software solutions
• Improved application performance by 30% through code optimization

Software Developer | Digital Innovations | June 2018 - May 2020  
• Built responsive web interfaces using HTML, CSS, and JavaScript
• Implemented RESTful APIs for mobile and web applications
• Participated in agile development processes and code reviews

TECHNICAL SKILLS
• Programming Languages: Python, JavaScript, Java, SQL
• Frameworks: Django, React, Node.js
• Databases: PostgreSQL, MySQL, MongoDB
• Tools: Git, Docker, AWS, Jenkins

PROJECTS
E-Commerce Platform (2021)
• Developed full-stack e-commerce solution serving 10,000+ users
• Implemented secure payment processing and inventory management

Task Management App (2020)
• Created mobile-first web application with real-time collaboration features
• Used React for frontend and Node.js for backend services
"@

    $sampleCV2 = @"
JANE SMITH
456 Oak Avenue, City, State 67890
Phone: (************* | Email: <EMAIL>
LinkedIn: linkedin.com/in/janesmith

OBJECTIVE
Marketing professional seeking to leverage 7+ years of digital marketing experience
to drive brand growth and customer engagement in a senior marketing role.

EDUCATION
Master of Business Administration (MBA)
Business University, City, State
Graduated: December 2016
Concentration: Marketing and Strategy

Bachelor of Arts in Communications
State College, City, State  
Graduated: May 2014
Magna Cum Laude, GPA: 3.8/4.0

PROFESSIONAL EXPERIENCE
Digital Marketing Manager | Marketing Solutions Co. | January 2019 - Present
• Manage digital marketing campaigns across multiple channels (social media, email, PPC)
• Increased online engagement by 150% and lead generation by 85%
• Supervise team of 4 marketing specialists and coordinate with external agencies
• Develop and execute comprehensive marketing strategies for B2B and B2C clients

Marketing Specialist | Growth Agency | June 2016 - December 2018
• Created and implemented social media marketing strategies for 15+ clients
• Conducted market research and competitive analysis to inform campaign strategies
• Managed advertising budgets totaling $500K+ annually with 25% ROI improvement
• Collaborated with creative teams to develop compelling marketing content

Marketing Coordinator | Startup Ventures | June 2014 - May 2016
• Assisted in developing go-to-market strategies for new product launches
• Coordinated trade shows and promotional events, increasing brand awareness by 40%
• Created marketing materials including brochures, presentations, and web content

SKILLS
• Digital Marketing: SEO/SEM, Social Media Marketing, Email Marketing, PPC
• Analytics: Google Analytics, Adobe Analytics, Facebook Insights, HubSpot
• Design Tools: Adobe Creative Suite, Canva, Figma
• Project Management: Asana, Trello, Monday.com
• Languages: English (Native), Spanish (Conversational)

CERTIFICATIONS
• Google Ads Certified Professional (2021)
• HubSpot Content Marketing Certification (2020)
• Facebook Blueprint Certification (2019)

ACHIEVEMENTS
• "Marketing Professional of the Year" - Marketing Association (2021)
• Increased client retention rate by 60% through improved campaign performance
• Led rebranding initiative that resulted in 200% increase in brand recognition
"@

    # Save sample CVs as text files
    $sampleCV1 | Out-File -FilePath "$OutputPath\sample_cv_tech_professional.txt" -Encoding UTF8
    $sampleCV2 | Out-File -FilePath "$OutputPath\sample_cv_marketing_professional.txt" -Encoding UTF8
    
    Write-Host "Created 2 sample CV text files" -ForegroundColor Green
    $downloadCount += 2
}

# Generate summary
Write-Host "`n=== DOWNLOAD SUMMARY ===" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host "Output Directory: $OutputPath" -ForegroundColor Yellow
Write-Host "Files Downloaded: $downloadCount" -ForegroundColor Cyan
Write-Host "Errors: $errorCount" -ForegroundColor $(if ($errorCount -gt 0) { "Red" } else { "Green" })

# List downloaded files
Write-Host "`nDownloaded Files:" -ForegroundColor Green
$files = Get-ChildItem $OutputPath -File
if ($files.Count -gt 0) {
    foreach ($file in $files) {
        $size = [math]::Round($file.Length / 1KB, 1)
        Write-Host "  $($file.Name) ($size KB)" -ForegroundColor Cyan
    }
}
else {
    Write-Host "  No files downloaded" -ForegroundColor Red
}

# Create summary file
$summaryContent = @"
Web CV Download Summary
======================
Date: $(Get-Date)
Output Directory: $OutputPath
Files Downloaded: $downloadCount
Errors: $errorCount

Sources Attempted:
1. Bellevue University Resume Samples (msnlabs.com)
2. Harvard Career Services CV Guide
3. MIT Career Services Resume Guide  
4. Stanford Career Education Resume Guide
5. Reed Graduate CV Template
6. Academic CV Sample (jobs.ac.uk)

File Types:
- PDF resume/CV guides and samples
- Text format sample CVs (if PDFs unavailable)

Usage:
These files provide examples of professional CV/resume formats
that can be used for testing your CV analyzer application.
"@

$summaryPath = Join-Path $OutputPath "download_summary.txt"
$summaryContent | Out-File -FilePath $summaryPath -Encoding UTF8

Write-Host "`nSummary saved to: download_summary.txt" -ForegroundColor Green
Write-Host "Ready for CV analyzer testing!" -ForegroundColor Yellow 