Metadata-Version: 2.4
Name: fonttools
Version: 4.58.5
Summary: Tools to manipulate font files
Home-page: http://github.com/fonttools/fonttools
Author: <PERSON> <PERSON>
Author-email: <EMAIL>
Maintainer: <PERSON><PERSON><PERSON> Esfahbod
Maintainer-email: beh<PERSON>@behdad.org
License: MIT
Platform: Any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: Other Environment
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: End Users/Desktop
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Text Processing :: Fonts
Classifier: Topic :: Multimedia :: Graphics
Classifier: Topic :: Multimedia :: Graphics :: Graphics Conversion
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
License-File: LICENSE.external
Provides-Extra: ufo
Requires-Dist: fs<3,>=2.2.0; extra == "ufo"
Provides-Extra: lxml
Requires-Dist: lxml>=4.0; extra == "lxml"
Provides-Extra: woff
Requires-Dist: brotli>=1.0.1; platform_python_implementation == "CPython" and extra == "woff"
Requires-Dist: brotlicffi>=0.8.0; platform_python_implementation != "CPython" and extra == "woff"
Requires-Dist: zopfli>=0.1.4; extra == "woff"
Provides-Extra: unicode
Requires-Dist: unicodedata2>=15.1.0; python_version <= "3.12" and extra == "unicode"
Provides-Extra: graphite
Requires-Dist: lz4>=*******; extra == "graphite"
Provides-Extra: interpolatable
Requires-Dist: scipy; platform_python_implementation != "PyPy" and extra == "interpolatable"
Requires-Dist: munkres; platform_python_implementation == "PyPy" and extra == "interpolatable"
Requires-Dist: pycairo; extra == "interpolatable"
Provides-Extra: plot
Requires-Dist: matplotlib; extra == "plot"
Provides-Extra: symfont
Requires-Dist: sympy; extra == "symfont"
Provides-Extra: type1
Requires-Dist: xattr; sys_platform == "darwin" and extra == "type1"
Provides-Extra: pathops
Requires-Dist: skia-pathops>=0.5.0; extra == "pathops"
Provides-Extra: repacker
Requires-Dist: uharfbuzz>=0.23.0; extra == "repacker"
Provides-Extra: all
Requires-Dist: fs<3,>=2.2.0; extra == "all"
Requires-Dist: lxml>=4.0; extra == "all"
Requires-Dist: brotli>=1.0.1; platform_python_implementation == "CPython" and extra == "all"
Requires-Dist: brotlicffi>=0.8.0; platform_python_implementation != "CPython" and extra == "all"
Requires-Dist: zopfli>=0.1.4; extra == "all"
Requires-Dist: unicodedata2>=15.1.0; python_version <= "3.12" and extra == "all"
Requires-Dist: lz4>=*******; extra == "all"
Requires-Dist: scipy; platform_python_implementation != "PyPy" and extra == "all"
Requires-Dist: munkres; platform_python_implementation == "PyPy" and extra == "all"
Requires-Dist: pycairo; extra == "all"
Requires-Dist: matplotlib; extra == "all"
Requires-Dist: sympy; extra == "all"
Requires-Dist: xattr; sys_platform == "darwin" and extra == "all"
Requires-Dist: skia-pathops>=0.5.0; extra == "all"
Requires-Dist: uharfbuzz>=0.23.0; extra == "all"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: maintainer
Dynamic: maintainer-email
Dynamic: platform
Dynamic: provides-extra
Dynamic: requires-python
Dynamic: summary

|CI Build Status| |Coverage Status| |PyPI| |Gitter Chat|

What is this?
~~~~~~~~~~~~~

| fontTools is a library for manipulating fonts, written in Python. The
  project includes the TTX tool, that can convert TrueType and OpenType
  fonts to and from an XML text format, which is also called TTX. It
  supports TrueType, OpenType, AFM and to an extent Type 1 and some
  Mac-specific formats. The project has an `MIT open-source
  license <LICENSE>`__.
| Among other things this means you can use it free of charge.

`User documentation <https://fonttools.readthedocs.io/en/latest/>`_ and
`developer documentation <https://fonttools.readthedocs.io/en/latest/developer.html>`_
are available at `Read the Docs <https://fonttools.readthedocs.io/>`_.

Installation
~~~~~~~~~~~~

FontTools requires `Python <http://www.python.org/download/>`__ 3.9
or later. We try to follow the same schedule of minimum Python version support as
NumPy (see `NEP 29 <https://numpy.org/neps/nep-0029-deprecation_policy.html>`__).

The package is listed in the Python Package Index (PyPI), so you can
install it with `pip <https://pip.pypa.io>`__:

.. code:: sh

    pip install fonttools

If you would like to contribute to its development, you can clone the
repository from GitHub, install the package in 'editable' mode and
modify the source code in place. We recommend creating a virtual
environment, using `virtualenv <https://virtualenv.pypa.io>`__ or
Python 3 `venv <https://docs.python.org/3/library/venv.html>`__ module.

.. code:: sh

    # download the source code to 'fonttools' folder
    git clone https://github.com/fonttools/fonttools.git
    cd fonttools

    # create new virtual environment called e.g. 'fonttools-venv', or anything you like
    python -m virtualenv fonttools-venv

    # source the `activate` shell script to enter the environment (Unix-like); to exit, just type `deactivate`
    . fonttools-venv/bin/activate

    # to activate the virtual environment in Windows `cmd.exe`, do
    fonttools-venv\Scripts\activate.bat

    # install in 'editable' mode
    pip install -e .

Optional Requirements
---------------------

The ``fontTools`` package currently has no (required) external dependencies
besides the modules included in the Python Standard Library.
However, a few extra dependencies are required by some of its modules, which
are needed to unlock optional features.
The ``fonttools`` PyPI distribution also supports so-called "extras", i.e. a
set of keywords that describe a group of additional dependencies, which can be
used when installing via pip, or when specifying a requirement.
For example:

.. code:: sh

    pip install fonttools[ufo,lxml,woff,unicode]

This command will install fonttools, as well as the optional dependencies that
are required to unlock the extra features named "ufo", etc.

- ``Lib/fontTools/misc/etree.py``

  The module exports a ElementTree-like API for reading/writing XML files, and
  allows to use as the backend either the built-in ``xml.etree`` module or
  `lxml <https://lxml.de>`__. The latter is preferred whenever present,
  as it is generally faster and more secure.

  *Extra:* ``lxml``

- ``Lib/fontTools/ufoLib``

  Package for reading and writing UFO source files; it requires:

  * `fs <https://pypi.org/pypi/fs>`__: (aka ``pyfilesystem2``) filesystem
    abstraction layer.

  *Extra:* ``ufo``

- ``Lib/fontTools/ttLib/woff2.py``

  Module to compress/decompress WOFF 2.0 web fonts; it requires:

  * `brotli <https://pypi.python.org/pypi/Brotli>`__: Python bindings of
    the Brotli compression library.

  *Extra:* ``woff``

- ``Lib/fontTools/ttLib/sfnt.py``

  To better compress WOFF 1.0 web fonts, the following module can be used
  instead of the built-in ``zlib`` library:

  * `zopfli <https://pypi.python.org/pypi/zopfli>`__: Python bindings of
    the Zopfli compression library.

  *Extra:* ``woff``

- ``Lib/fontTools/unicode.py``

  To display the Unicode character names when dumping the ``cmap`` table
  with ``ttx`` we use the ``unicodedata`` module in the Standard Library.
  The version included in there varies between different Python versions.
  To use the latest available data, you can install:

  * `unicodedata2 <https://pypi.python.org/pypi/unicodedata2>`__:
    ``unicodedata`` backport for Python 3.x updated to the latest Unicode
    version 15.0.

  *Extra:* ``unicode``

- ``Lib/fontTools/varLib/interpolatable.py``

  Module for finding wrong contour/component order between different masters.
  It requires one of the following packages in order to solve the so-called
  "minimum weight perfect matching problem in bipartite graphs", or
  the Assignment problem:

  * `scipy <https://pypi.python.org/pypi/scipy>`__: the Scientific Library
    for Python, which internally uses `NumPy <https://pypi.python.org/pypi/numpy>`__
    arrays and hence is very fast;
  * `munkres <https://pypi.python.org/pypi/munkres>`__: a pure-Python
    module that implements the Hungarian or Kuhn-Munkres algorithm.

  To plot the results to a PDF or HTML format, you also need to install:

  * `pycairo <https://pypi.org/project/pycairo/>`__: Python bindings for the
    Cairo graphics library. Note that wheels are currently only available for
    Windows, for other platforms see pycairo's `installation instructions
    <https://pycairo.readthedocs.io/en/latest/getting_started.html>`__.

  *Extra:* ``interpolatable``

- ``Lib/fontTools/varLib/plot.py``

  Module for visualizing DesignSpaceDocument and resulting VariationModel.

  * `matplotlib <https://pypi.org/pypi/matplotlib>`__: 2D plotting library.

  *Extra:* ``plot``

- ``Lib/fontTools/misc/symfont.py``

  Advanced module for symbolic font statistics analysis; it requires:

  * `sympy <https://pypi.python.org/pypi/sympy>`__: the Python library for
    symbolic mathematics.

  *Extra:* ``symfont``

- ``Lib/fontTools/t1Lib.py``

  To get the file creator and type of Macintosh PostScript Type 1 fonts
  on Python 3 you need to install the following module, as the old ``MacOS``
  module is no longer included in Mac Python:

  * `xattr <https://pypi.python.org/pypi/xattr>`__: Python wrapper for
    extended filesystem attributes (macOS platform only).

  *Extra:* ``type1``

- ``Lib/fontTools/ttLib/removeOverlaps.py``

  Simplify TrueType glyphs by merging overlapping contours and components.

  * `skia-pathops <https://pypi.python.org/pypy/skia-pathops>`__: Python
    bindings for the Skia library's PathOps module, performing boolean
    operations on paths (union, intersection, etc.).

  *Extra:* ``pathops``

- ``Lib/fontTools/pens/cocoaPen.py`` and ``Lib/fontTools/pens/quartzPen.py``

  Pens for drawing glyphs with Cocoa ``NSBezierPath`` or ``CGPath`` require:

  * `PyObjC <https://pypi.python.org/pypi/pyobjc>`__: the bridge between
    Python and the Objective-C runtime (macOS platform only).

- ``Lib/fontTools/pens/qtPen.py``

  Pen for drawing glyphs with Qt's ``QPainterPath``, requires:

  * `PyQt5 <https://pypi.python.org/pypi/PyQt5>`__: Python bindings for
    the Qt cross platform UI and application toolkit.

- ``Lib/fontTools/pens/reportLabPen.py``

  Pen to drawing glyphs as PNG images, requires:

  * `reportlab <https://pypi.python.org/pypi/reportlab>`__: Python toolkit
    for generating PDFs and graphics.

- ``Lib/fontTools/pens/freetypePen.py``

  Pen to drawing glyphs with FreeType as raster images, requires:

  * `freetype-py <https://pypi.python.org/pypi/freetype-py>`__: Python binding
    for the FreeType library.
    
- ``Lib/fontTools/ttLib/tables/otBase.py``

  Use the Harfbuzz library to serialize GPOS/GSUB using ``hb_repack`` method, requires:
  
  * `uharfbuzz <https://pypi.python.org/pypi/uharfbuzz>`__: Streamlined Cython
    bindings for the harfbuzz shaping engine
    
  *Extra:* ``repacker``

How to make a new release
~~~~~~~~~~~~~~~~~~~~~~~~~

1) Update ``NEWS.rst`` with all the changes since the last release. Write a
   changelog entry for each PR, with one or two short sentences summarizing it,
   as well as links to the PR and relevant issues addressed by the PR. Do not
   put a new title, the next command will do it for you.
2) Use semantic versioning to decide whether the new release will be a 'major',
   'minor' or 'patch' release. It's usually one of the latter two, depending on
   whether new backward compatible APIs were added, or simply some bugs were fixed.
3) From inside a venv, first do ``pip install -r dev-requirements.txt``, then run
   the ``python setup.py release`` command from the tip of the ``main`` branch.
   By default this bumps the third or 'patch' digit only, unless you pass ``--major``
   or ``--minor`` to bump respectively the first or second digit.
   This bumps the package version string, extracts the changes since the latest
   version from ``NEWS.rst``, and uses that text to create an annotated git tag
   (or a signed git tag if you pass the ``--sign`` option and your git and Github
   account are configured for `signing commits <https://docs.github.com/en/github/authenticating-to-github/managing-commit-signature-verification/signing-commits>`__
   using a GPG key).
   It also commits an additional version bump which opens the main branch for
   the subsequent developmental cycle
4) Push both the tag and commit to the upstream repository, by running the command
   ``git push --follow-tags``. Note: it may push other local tags as well, be
   careful.
5) Let the CI build the wheel and source distribution packages and verify both
   get uploaded to the Python Package Index (PyPI).
6) [Optional] Go to fonttools `Github Releases <https://github.com/fonttools/fonttools/releases>`__
   page and create a new release, copy-pasting the content of the git tag
   message. This way, the release notes are nicely formatted as markdown, and
   users watching the repo will get an email notification. One day we shall
   automate that too.


Acknowledgments
~~~~~~~~~~~~~~~~

In alphabetical order:

aschmitz, Olivier Berten, Samyak Bhuta, Erik van Blokland, Petr van Blokland,
Jelle Bosma, Sascha Brawer, Tom Byrer, Antonio Cavedoni, Frédéric Coiffier,
Vincent Connare, David Corbett, Simon Cozens, Dave Crossland, Simon Daniels,
Peter Dekkers, Behdad Esfahbod, Behnam Esfahbod, Hannes Famira, Sam Fishman,
Matt Fontaine, Takaaki Fuji, Rob Hagemans, Yannis Haralambous, Greg Hitchcock,
Jeremie Hornus, Khaled Hosny, John Hudson, Denis Moyogo Jacquerye, Jack Jansen,
Tom Kacvinsky, Jens Kutilek, Antoine Leca, Werner Lemberg, Tal Leming, Liang Hai, Peter
Lofting, Cosimo Lupo, Olli Meier, Masaya Nakamura, Dave Opstad, Laurence Penney,
Roozbeh Pournader, Garret Rieger, Read Roberts, Colin Rofls, Guido van Rossum,
Just van Rossum, Andreas Seidel, Georg Seifert, Chris Simpkins, Miguel Sousa,
Adam Twardoch, Adrien Tétar, Vitaly Volkov, Paul Wise.

Copyrights
~~~~~~~~~~

| Copyright (c) 1999-2004 Just van Rossum, LettError
  (<EMAIL>)
| See `LICENSE <LICENSE>`__ for the full license.

Copyright (c) 2000 BeOpen.com. All Rights Reserved.

Copyright (c) 1995-2001 Corporation for National Research Initiatives.
All Rights Reserved.

Copyright (c) 1991-1995 Stichting Mathematisch Centrum, Amsterdam. All
Rights Reserved.

Have fun!

.. |CI Build Status| image:: https://github.com/fonttools/fonttools/workflows/Test/badge.svg
   :target: https://github.com/fonttools/fonttools/actions?query=workflow%3ATest
.. |Coverage Status| image:: https://codecov.io/gh/fonttools/fonttools/branch/main/graph/badge.svg
   :target: https://codecov.io/gh/fonttools/fonttools
.. |PyPI| image:: https://img.shields.io/pypi/v/fonttools.svg
   :target: https://pypi.org/project/FontTools
.. |Gitter Chat| image:: https://badges.gitter.im/fonttools-dev/Lobby.svg
   :alt: Join the chat at https://gitter.im/fonttools-dev/Lobby
   :target: https://gitter.im/fonttools-dev/Lobby?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge

Changelog
~~~~~~~~~

4.58.5 (released 2025-07-03)
----------------------------

- [feaLib] Don't try to combine ligature & multisub rules (#3874).
- [feaLib/ast] Use weakref proxies to avoid cycles in visitor (#3873).
- [varLib.instancer] Fixed instancing CFF2 fonts where VarData contains more than 64k items (#3858).

4.58.4 (released 2025-06-13)
----------------------------

- [feaLib] Allow for empty MarkFilter & MarkAttach sets (#3856).

4.58.3 (released 2025-06-13)
----------------------------

- [feaLib] Fixed iterable check for Python 3.13.4 and newer (#3854, #3855).

4.58.2 (released 2025-06-06)
----------------------------

- [ttLib.reorderGlyphs] Handle CFF2 when reordering glyphs (#3852)
- [subset] Copy name IDs in use before scrapping or scrambling them for webfonts (#3853)

4.58.1 (released 2025-05-28)
----------------------------

- [varLib] Make sure that fvar named instances only reuse name ID 2 or 17 if they are at the default location across all axes, to match OT spec requirement (#3831).
- [feaLib] Improve single substitution promotion to multiple/ligature substitutions, fixing a few bugs as well (#3849).
- [loggingTools] Make ``Timer._time`` a static method that doesn't take self, makes it easier to override (#3836).
- [featureVars] Use ``None`` for empty ConditionSet, which translates to a null offset in the compiled table (#3850).
- [feaLib] Raise an error on conflicting ligature substitution rules instead of silently taking the last one (#3835).
- Add typing annotations to T2CharStringPen (#3837).
- [feaLib] Add single substitutions that were promoted to multiple or ligature substitutions to ``aalt`` feature (#3847).
- [featureVars] Create a default ``LangSys`` in a ``ScriptRecord`` if missing when adding feature variations to existing GSUB later in the build (#3838).
- [symfont] Added a ``main()``.
- [cffLib.specializer] Fix rmoveto merging when blends used (#3839, #3840).
- [pyftmerge] Add support for cmap format 14 in the merge tool (#3830).
- [varLib.instancer/cff2] Fix vsindex of Private dicts when instantiating (#3828, #3232).
- Update text file read to use UTF-8 with optional BOM so it works with e.g. Windows Notepad.exe (#3824).
- [varLib] Ensure that instances only reuse name ID 2 or 17 if they are at the default location across all axes (#3831).
- [varLib] Create a dflt LangSys in a ScriptRecord when adding variations later, to fix an avoidable crash in an edge case (#3838).

4.58.0 (released 2025-05-10)
----------------------------

- Drop Python 3.8, require 3.9+ (#3819)
- [HVAR, VVAR] Prune unused regions when using a direct mapping (#3797)
- [Docs] Improvements to ufoLib documentation (#3721)
- [Docs] Improvements to varLib documentation (#3727)
- [Docs] Improvements to Pens and pen-module documentation (#3724)
- [Docs] Miscellany updates to docs (misc modules and smaller modules) (#3730)
- [subset] Close codepoints over BiDi mirror variants. (#3801)
- [feaLib] Fix serializing ChainContextPosStatement and
  ChainContextSubstStatement in some rare cases (#3788)
- [designspaceLib] Clarify user expectations for getStatNames (#2892)
- [GVAR] Add support for new `GVAR` table (#3728)
- [TSI0, TSI5] Derive number of entries to decompile from data length (#2477)
- [ttLib] Fix `AttributeError` when reporting table overflow (#3808)
- [ttLib] Apply rounding more often in getCoordinates (#3798)
- [ttLib] Ignore component bounds if empty (#3799)
- [ttLib] Change the separator for duplicate glyph names from "#" to "." (#3809)
- [feaLib] Support subtable breaks in CursivePos, MarkBasePos, MarkToLigPos and
  MarkToMarkPos lookups (#3800, #3807)
- [feaLib] If the same lookup has single substitutions and ligature
  substitutions, upgrade single substitutions to ligature substitutions with
  one input glyph (#3805)
- [feaLib] Correctly handle <NULL> in single pos lookups (#3803)
- [feaLib] Remove duplicates from class pair pos classes instead of raising an
  error (#3804)
- [feaLib] Support creating extension lookups using useExtenion lookup flag
  instead of silently ignoring it (#3811)
- [STAT] Add typing for the simpler STAT arguments (#3812)
- [otlLib.builder] Add future import for annotations (#3814)
- [cffLib] Fix reading supplement encoding (#3813)
- [voltLib] Add some missing functionality and fixes to voltLib and VoltToFea,
  making the conversion to feature files more robust. Add also `fonttools
  voltLib` command line tool to compile VOLT sources directly (doing an
  intermediate fea conversion internally) (#3818)
- [pens] Add some PointPen annotations (#3820)

4.57.0 (released 2025-04-03)
----------------------------

- [ttLib.__main__] Add `--no-recalc-timestamp` flag (#3771)
- [ttLib.__main__] Add `-b` (recalcBBoxes=False) flag (#3772)
- [cmap] Speed up glyphOrder loading from cmap (#3774)
- [ttLib.__main__] Improvements around the `-t` flag (#3776)
- [Debg] Fix parsing from XML; add roundtrip tests (#3781)
- [fealib] Support \*Base.MinMax tables (#3783, #3786)
- [config] Add OPTIMIZE_FONT_SPEED (#3784)
- [varLib.hvar] New module to add HVAR table to the font (#3780)
- [otlLib.optimize] Fix crash when the provided TTF does not contain a `GPOS` (#3794)

4.56.0 (released 2025-02-07)
----------------------------

- [varStore] Sort the input todo list with the same sorting key used for the opimizer's output (#3767).
- [otData] Fix DeviceTable's ``DeltaValue`` repeat value which caused a crash after importing from XML and then compiling a GPOS containing Device tables (#3758).
- [feaLib] Make ``FeatureLibError`` pickleable, so client can e.g. use feaLib to can compile features in parallel with multiprocessing (#3762).
- [varLib/gvar] Removed workaround for old, long-fixed macOS bug about composite glyphs with all zero deltas (#1381, #1788).
- [Docs] Updated ttLib documentation, beefed up TTFont and TTGlyphSet explanations (#3720).

4.55.8 (released 2025-01-29)
----------------------------

- [MetaTools] Fixed bug in buildUCD.py script whereby the first non-header line of some UCD text file was being skipped. This affected in particular the U+00B7 (MIDDLE DOT) entry of ScriptExtensions.txt (#3756).

4.55.7 (released 2025-01-28)
----------------------------

- Shorten the changelog included in PyPI package description to accommodate maximum length limit imposed by Azure DevOps. No actual code changes since v4.55.6 (#3754).

4.55.6 (released 2025-01-24)
----------------------------

- [glyf] Fixed regression introduced in 4.55.5 when computing bounds of nested composite glyphs  with transformed components (#3752).

4.55.5 (released 2025-01-23)
----------------------------

- [glyf] Fixed recalcBounds of transformed components with unrounded coordinates (#3750).
- [feaLib] Allow duplicate script/language statements (#3749).

4.55.4 (released 2025-01-21)
----------------------------

- [bezierTools] Fixed ``splitCubicAtT`` sometimes not returning identical start/end points as result of numerical precision (#3742, #3743).
- [feaLib/ast] Fixed docstring of ``AlternateSubstStatement`` (#3735).
- [transform] Typing fixes (#3734).

4.55.3 (released 2024-12-10)
----------------------------

- [Docs] fill out ttLib table section [#3716]
- [feaLib] More efficient inline format 4 lookups [#3726]

4.55.2 (released 2024-12-05)
----------------------------

- [Docs] update Sphinx config (#3712)
- [designspaceLib] Allow axisOrdering to be set to zero (#3715)
- [feaLib] Don’t modify variable anchors in place (#3717)

4.55.1 (released 2024-12-02)
----------------------------

- [ttGlyphSet] Support VARC CFF2 fonts (#3683)
- [DecomposedTransform] Document and implement always skewY == 0 (#3697)
- [varLib] "Fix" cython iup issue? (#3704)
- Cython minor refactor (#3705)


4.55.0 (released 2024-11-14)
----------------------------

- [cffLib.specializer] Adjust stack use calculation (#3689)
- [varLib] Lets not add mac names if the rest of name doesn't have them (#3688)
- [ttLib.reorderGlyphs] Update CFF table charstrings and charset (#3682)
- [cffLib.specializer] Add cmdline to specialize a CFF2 font (#3675, #3679)
- [CFF2] Lift uint16 VariationStore.length limitation (#3674)
- [subset] consider variation selectors subsetting cmap14 (#3672)
- [varLib.interpolatable] Support CFF2 fonts (#3670)
- Set isfinal to true in XML parser for proper resource cleanup (#3669)
- [removeOverlaps] Fix CFF CharString width (#3659)
- [glyf] Add optimizeSize option (#3657)
- Python 3.13 support (#3656)
- [TupleVariation] Optimize for loading speed, not size (#3650, #3653)


4.54.1 (released 2024-09-24)
----------------------------

- [unicodedata] Update to Unicode 16
- [subset] Escape ``\\`` in doc string

4.54.0 (released 2024-09-23)
----------------------------

- [Docs] Small docs cleanups by @n8willis (#3611)
- [Docs] cleanup code blocks by @n8willis (#3627)
- [Docs] fix Sphinx builds by @n8willis (#3625)
- [merge] Minor fixes to documentation for merge by @drj11 (#3588)
- [subset] Small tweaks to pyftsubset documentation by @RoelN (#3633)
- [Tests] Do not require fonttools command to be available by @behdad (#3612)
- [Tests] subset_test: add failing test to reproduce issue #3616 by @anthrotype (#3622)
- [ttLib] NameRecordVisitor: include whole sequence of character variants' UI labels, not just the first by @anthrotype (#3617)
- [varLib.avar] Reconstruct mappings from binary by @behdad (#3598)
- [varLib.instancer] Fix visual artefacts with partial L2 instancing by @Hoolean (#3635)
- [varLib.interpolatable] Support discrete axes in .designspace by @behdad (#3599)
- [varLib.models] By default, assume OpenType-like normalized space by @behdad (#3601)

4.53.1 (released 2024-07-05)
----------------------------

- [feaLib] Improve the sharing of inline chained lookups (#3559)
- [otlLib] Correct the calculation of OS/2.usMaxContext with reversed chaining contextual single substitutions (#3569)
- [misc.visitor] Visitors search the inheritance chain of objects they are visiting (#3581)

4.53.0 (released 2024-05-31)
----------------------------

- [ttLib.removeOverlaps] Support CFF table to aid in downconverting CFF2 fonts (#3528)
- [avar] Fix crash when accessing not-yet-existing attribute (#3550)
- [docs] Add buildMathTable to otlLib.builder documentation (#3540)
- [feaLib] Allow UTF-8 with BOM when reading features (#3495)
- [SVGPathPen] Revert rounding coordinates to two decimal places by default (#3543)
- [varLib.instancer] Refix output filename decision-making  (#3545, #3544, #3548)

4.52.4 (released 2024-05-27)
----------------------------

- [varLib.cff] Restore and deprecate convertCFFtoCFF2 that was removed in 4.52.0
  release as it is used by downstream projects (#3535).

4.52.3 (released 2024-05-27)
----------------------------

- Fixed a small syntax error in the reStructuredText-formatted NEWS.rst file
  which caused the upload to PyPI to fail for 4.52.2. No other code changes.

4.52.2 (released 2024-05-27)
----------------------------

- [varLib.interpolatable] Ensure that scipy/numpy output is JSON-serializable
  (#3522, #3526).
- [housekeeping] Regenerate table lists, to fix pyinstaller packaging of the new
  ``VARC`` table (#3531, #3529).
- [cffLib] Make CFFToCFF2 and CFF2ToCFF more robust (#3521, #3525).

4.52.1 (released 2024-05-24)
----------------------------

- Fixed a small syntax error in the reStructuredText-formatted NEWS.rst file
  which caused the upload to PyPI to fail for 4.52.0. No other code changes.

4.52.0 (released 2024-05-24)
----------------------------

- Added support for the new ``VARC`` (Variable Composite) table that is being
  proposed to OpenType spec (#3395). For more info:
  https://github.com/harfbuzz/boring-expansion-spec/blob/main/VARC.md
- [ttLib.__main__] Fixed decompiling all tables (90fed08).
- [feaLib] Don't reference the same lookup index multiple times within the same
  feature record, it is only applied once anyway (#3520).
- [cffLib] Moved methods to desubroutinize, remove hints and unused subroutines
  from subset module to cffLib (#3517).
- [varLib.instancer] Added support for partial-instancing CFF2 tables! Also, added
  method to down-convert from CFF2 to CFF 1.0, and CLI entry points to convert
  CFF<->CFF2 (#3506).
- [subset] Prune unused user name IDs even with --name-IDs='*' (#3410).
- [ttx] use GNU-style getopt to intermix options and positional arguments (#3509).
- [feaLib.variableScalar] Fixed ``value_at_location()`` method (#3491)
- [psCharStrings] Shorten output of ``encodeFloat`` (#3492).
- [bezierTools] Fix infinite-recursion in ``calcCubicArcLength`` (#3502).
- [avar2] Implement ``avar2`` support in ``TTFont.getGlyphSet()`` (#3473).

4.51.0 (released 2024-04-05)
----------------------------

- [ttLib] Optimization on loading aux fields (#3464).
- [ttFont] Add reorderGlyphs (#3468).

4.50.0 (released 2024-03-15)
----------------------------

- [pens] Added decomposing filter pens that draw components as regular contours (#3460).
- [instancer] Drop explicit no-op axes from TupleVariations (#3457).
- [cu2qu/ufo] Return set of modified glyph names from fonts_to_quadratic (#3456).

4.49.0 (released 2024-02-15)
----------------------------

- [otlLib] Add API for building ``MATH`` table (#3446)

4.48.1 (released 2024-02-06)
----------------------------

- Fixed uploading wheels to PyPI, no code changes since v4.48.0.

4.48.0 (released 2024-02-06)
----------------------------

- [varLib] Do not log when there are no OTL tables to be merged.
- [setup.py] Do not restrict lxml<5 any more, tests pass just fine with lxml>=5.
- [feaLib] Remove glyph and class names length restrictions in FEA (#3424).
- [roundingPens] Added ``transformRoundFunc`` parameter to the rounding pens to allow
  for custom rounding of the components' transforms (#3426).
- [feaLib] Keep declaration order of ligature components within a ligature set, instead
  of sorting by glyph name (#3429).
- [feaLib] Fixed ordering of alternates in ``aalt`` lookups, following the declaration
  order of feature references within the ``aalt`` feature block (#3430).
- [varLib.instancer] Fixed a bug in the instancer's IUP optimization (#3432).
- [sbix] Support sbix glyphs with new graphicType "flip" (#3433).
- [svgPathPen] Added ``--glyphs`` option to dump the SVG paths for the named glyphs
  in the font (0572f78).
- [designspaceLib] Added "description" attribute to ``<mappings>`` and ``<mapping>``
  elements, and allow multiple ``<mappings>`` elements to group ``<mapping>`` elements
  that are logically related (#3435, #3437).
- [otlLib] Correctly choose the most compact GSUB contextual lookup format (#3439).

4.47.2 (released 2024-01-11)
----------------------------

Minor release to fix uploading wheels to PyPI.

4.47.1 (released 2024-01-11)
----------------------------

- [merge] Improve help message and add standard command line options (#3408)
- [otlLib] Pass ``ttFont`` to ``name.addName`` in ``buildStatTable`` (#3406)
- [featureVars] Re-use ``FeatureVariationRecord``'s when possible (#3413)

4.47.0 (released 2023-12-18)
----------------------------

- [varLib.models] New API for VariationModel: ``getMasterScalars`` and
  ``interpolateFromValuesAndScalars``.
- [varLib.interpolatable] Various bugfixes and rendering improvements. In particular,
  add a Summary page in the front, and an Index and Table-of-Contents in the back.
  Change the page size to Letter.
- [Docs/designspaceLib] Defined a new ``public.fontInfo`` lib key, not used anywhere yet (#3358).

4.46.0 (released 2023-12-02)
----------------------------

- [featureVars] Allow to register the same set of substitution rules to multiple features.
  The ``addFeatureVariations`` function can now take a list of featureTags; similarly, the
  lib key 'com.github.fonttools.varLib.featureVarsFeatureTag' can now take a
  comma-separateed string of feature tags (e.g. "salt,ss01") instead of a single tag (#3360).
- [featureVars] Don't overwrite GSUB FeatureVariations, but append new records to it
  for features which are not already there. But raise ``VarLibError`` if the feature tag
  already has feature variations associated with it (#3363).
- [varLib] Added ``addGSUBFeatureVariations`` function to add GSUB Feature Variations
  to an existing variable font from rules defined in a DesignSpace document (#3362).
- [varLib.interpolatable] Various bugfixes and rendering improvements. In particular,
  a new test for "underweight" glyphs. The new test reports quite a few false-positives
  though. Please send feedback.

4.45.1 (released 2023-11-23)
----------------------------

- [varLib.interpolatable] Various bugfixes and improvements, better reporting, reduced
  false positives.
- [ttGlyphSet] Added option to not recalculate glyf bounds (#3348).

4.45.0 (released 2023-11-20)
----------------------------

- [varLib.interpolatable] Vastly improved algorithms. Also available now is ``--pdf``
  and ``--html`` options to generate a PDF or HTML report of the interpolation issues.
  The PDF/HTML report showcases the problematic masters, the interpolated broken
  glyph, as well as the proposed fixed version.

4.44.3 (released 2023-11-15)
----------------------------

- [subset] Only prune codepage ranges for OS/2.version >= 1, ignore otherwise (#3334).
- [instancer] Ensure hhea vertical metrics stay in sync with OS/2 ones after instancing
  MVAR table containing 'hasc', 'hdsc' or 'hlgp' tags (#3297).

4.44.2 (released 2023-11-14)
----------------------------

- [glyf] Have ``Glyph.recalcBounds`` skip empty components (base glyph with no contours)
  when computing the bounding box of composite glyphs. This simply restores the existing
  behavior before some changes were introduced in fonttools 4.44.0 (#3333).

4.44.1 (released 2023-11-14)
----------------------------

- [feaLib] Ensure variable mark anchors are deep-copied while building since they
  get modified in-place and later reused (#3330).
- [OS/2|subset] Added method to ``recalcCodePageRanges`` to OS/2 table class; added
  ``--prune-codepage-ranges`` to `fonttools subset` command (#3328, #2607).

4.44.0 (released 2023-11-03)
----------------------------

- [instancer] Recalc OS/2 AvgCharWidth after instancing if default changes (#3317).
- [otlLib] Make ClassDefBuilder class order match varLib.merger's, i.e. large
  classes first, then glyph lexicographic order (#3321, #3324).
- [instancer] Allow not specifying any of min:default:max values and let be filled
  up with fvar's values (#3322, #3323).
- [instancer] When running --update-name-table ignore axes that have no STAT axis
  values (#3318, #3319).
- [Debg] When dumping to ttx, write the embedded JSON as multi-line string with
  indentation (92cbfee0d).
- [varStore] Handle > 65535 items per encoding by splitting VarData subtable (#3310).
- [subset] Handle null-offsets in MarkLigPos subtables.
- [subset] Keep East Asian spacing fatures vhal, halt, chws, vchw by default (#3305).
- [instancer.solver] Fixed case where axisDef < lower and upper < axisMax (#3304).
- [glyf] Speed up compilation, mostly around ``recalcBounds`` (#3301).
- [varLib.interpolatable] Speed it up when working on variable fonts, plus various
  micro-optimizations (#3300).
- Require unicodedata2 >= 15.1.0 when installed with 'unicode' extra, contains UCD 15.1.

4.43.1 (released 2023-10-06)
----------------------------

- [EBDT] Fixed TypeError exception in `_reverseBytes` method triggered when dumping
  some bitmap fonts with `ttx -z bitwise` option (#3162).
- [v/hhea] Fixed UnboundLocalError exception in ``recalc`` method when no vmtx or hmtx
  tables are present (#3290).
- [bezierTools] Fixed incorrectly typed cython local variable leading to TypeError when
  calling ``calcQuadraticArcLength`` (#3288).
- [feaLib/otlLib] Better error message when building Coverage table with missing glyph (#3286).

4.43.0 (released 2023-09-29)
----------------------------

- [subset] Set up lxml ``XMLParser(resolve_entities=False)`` when parsing OT-SVG documents
  to prevent XML External Entity (XXE) attacks (9f61271dc):
  https://codeql.github.com/codeql-query-help/python/py-xxe/
- [varLib.iup] Added workaround for a Cython bug in ``iup_delta_optimize`` that was
  leading to IUP tolerance being incorrectly initialised, resulting in sub-optimal deltas
  (60126435d, cython/cython#5732).
- [varLib] Added new command-line entry point ``fonttools varLib.avar`` to add an
  ``avar`` table to an existing VF from axes mappings in a .designspace file (0a3360e52).
- [instancer] Fixed bug whereby no longer used variation regions were not correctly pruned
  after VarData optimization (#3268).
- Added support for Python 3.12 (#3283).

4.42.1 (released 2023-08-20)
----------------------------

- [t1Lib] Fixed several Type 1 issues (#3238, #3240).
- [otBase/packer] Allow sharing tables reached by different offset sizes (#3241, #3236).
- [varLib/merger] Fix Cursive attachment merging error when all anchors are NULL (#3248, #3247).
- [ttLib] Fixed warning when calling ``addMultilingualName`` and ``ttFont`` parameter was not
  passed on to ``findMultilingualName`` (#3253).

4.42.0 (released 2023-08-02)
----------------------------

- [varLib] Use sentinel value 0xFFFF to mark a glyph advance in hmtx/vmtx as non
  participating, allowing sparse masters to contain glyphs for variation purposes other
  than {H,V}VAR (#3235).
- [varLib/cff] Treat empty glyphs in non-default masters as missing, thus not participating
  in CFF2 delta computation, similarly to how varLib already treats them for gvar (#3234).
- Added varLib.avarPlanner script to deduce 'correct' avar v1 axis mappings based on
  glyph average weights (#3223).

4.41.1 (released 2023-07-21)
----------------------------

- [subset] Fixed perf regression in v4.41.0 by making ``NameRecordVisitor`` only visit
  tables that do contain nameID references (#3213, #3214).
- [varLib.instancer] Support instancing fonts containing null ConditionSet offsets in
  FeatureVariationRecords (#3211, #3212).
- [statisticsPen] Report font glyph-average weight/width and font-wide slant.
- [fontBuilder] Fixed head.created date incorrectly set to 0 instead of the current
  timestamp, regression introduced in v4.40.0 (#3210).
- [varLib.merger] Support sparse ``CursivePos`` masters (#3209).

4.41.0 (released 2023-07-12)
----------------------------

- [fontBuilder] Fixed bug in setupOS2 with default panose attribute incorrectly being
  set to a dict instead of a Panose object (#3201).
- [name] Added method to ``removeUnusedNameRecords`` in the user range (#3185).
- [varLib.instancer] Fixed issue with L4 instancing (moving default) (#3179).
- [cffLib] Use latin1 so we can roundtrip non-ASCII in {Full,Font,Family}Name (#3202).
- [designspaceLib] Mark <source name="..."> as optional in docs (as it is in the code).
- [glyf-1] Fixed drawPoints() bug whereby last cubic segment becomes quadratic (#3189, #3190).
- [fontBuilder] Propagate the 'hidden' flag to the fvar Axis instance (#3184).
- [fontBuilder] Update setupAvar() to also support avar 2, fixing ``_add_avar()`` call
  site (#3183).
- Added new ``voltLib.voltToFea`` submodule (originally Tiro Typeworks' "Volto") for
  converting VOLT OpenType Layout sources to FEA format (#3164).

4.40.0 (released 2023-06-12)
----------------------------

- Published native binary wheels to PyPI for all the python minor versions and platform
  and architectures currently supported that would benefit from this. They will include
  precompiled Cython-accelerated modules (e.g. cu2qu) without requiring to compile them
  from source. The pure-python wheel and source distribution will continue to be
  published as always (pip will automatically chose them when no binary wheel is
  available for the given platform, e.g. pypy). Use ``pip install --no-binary=fonttools fonttools``
  to expliclity request pip to install from the pure-python source.
- [designspaceLib|varLib] Add initial support for specifying axis mappings and build
  ``avar2`` table from those (#3123).
- [feaLib] Support variable ligature caret position (#3130).
- [varLib|glyf] Added option to --drop-implied-oncurves; test for impliable oncurve
  points either before or after rounding (#3146, #3147, #3155, #3156).
- [TTGlyphPointPen] Don't error with empty contours, simply ignore them (#3145).
- [sfnt] Fixed str vs bytes remnant of py3 transition in code dealing with de/compiling
  WOFF metadata (#3129).
- [instancer-solver] Fixed bug when moving default instance with sparse masters (#3139, #3140).
- [feaLib] Simplify variable scalars that don’t vary (#3132).
- [pens] Added filter pen that explicitly emits closing line when lastPt != movePt (#3100).
- [varStore] Improve optimize algorithm and better document the algorithm (#3124, #3127).
  Added ``quantization`` option (#3126).
- Added CI workflow config file for building native binary wheels (#3121).
- [fontBuilder] Added glyphDataFormat=0 option; raise error when glyphs contain cubic
  outlines but glyphDataFormat was not explicitly set to 1 (#3113, #3119).
- [subset] Prune emptied GDEF.MarkGlyphSetsDef and remap indices; ensure GDEF is
  subsetted before GSUB and GPOS (#3114, #3118).
- [xmlReader] Fixed issue whereby DSIG table data was incorrectly parsed (#3115, #2614).
- [varLib/merger] Fixed merging of SinglePos with pos=0 (#3111, #3112).
- [feaLib] Demote "Feature has not been defined" error to a warning when building aalt
  and referenced feature is empty (#3110).
- [feaLib] Dedupe multiple substitutions with classes (#3105).

4.39.4 (released 2023-05-10)
----------------------------

- [varLib.interpolatable] Allow for sparse masters (#3075)
- [merge] Handle differing default/nominalWidthX in CFF (#3070)
- [ttLib] Add missing main.py file to ttLib package (#3088)
- [ttx] Fix missing composite instructions in XML (#3092)
- [ttx] Fix split tables option to work on filenames containing '%' (#3096)
- [featureVars] Process lookups for features other than rvrn last (#3099)
- [feaLib] support multiple substitution with classes (#3103)

4.39.3 (released 2023-03-28)
----------------------------

- [sbix] Fixed TypeError when compiling empty glyphs whose imageData is None, regression
  was introduced in v4.39 (#3059).
- [ttFont] Fixed AttributeError on python <= 3.10 when opening a TTFont from a tempfile
  SpooledTemporaryFile, seekable method only added on python 3.11 (#3052).

4.39.2 (released 2023-03-16)
----------------------------

- [varLib] Fixed regression introduced in 4.39.1 whereby an incomplete 'STAT' table
  would be built even though a DesignSpace v5 did contain 'STAT' definitions (#3045, #3046).

4.39.1 (released 2023-03-16)
----------------------------

- [avar2] Added experimental support for reading/writing avar version 2 as specified in
  this draft proposal: https://github.com/harfbuzz/boring-expansion-spec/blob/main/avar2.md
- [glifLib] Wrap underlying XML library exceptions with GlifLibError when parsing GLIFs,
  and also print the name and path of the glyph that fails to be parsed (#3042).
- [feaLib] Consult avar for normalizing user-space values in ConditionSets and in
  VariableScalars (#3042, #3043).
- [ttProgram] Handle string input to Program.fromAssembly() (#3038).
- [otlLib] Added a config option to emit GPOS 7 lookups, currently disabled by default
  because of a macOS bug (#3034).
- [COLRv1] Added method to automatically compute ClipBoxes (#3027).
- [ttFont] Fixed getGlyphID to raise KeyError on missing glyphs instead of returning
  None. The regression was introduced in v4.27.0 (#3032).
- [sbix] Fixed UnboundLocalError: cannot access local variable 'rawdata' (#3031).
- [varLib] When building VF, do not overwrite a pre-existing ``STAT`` table that was built
  with feaLib from FEA feature file. Also, added support for building multiple VFs
  defined in Designspace v5 from ``fonttools varLib`` script (#3024).
- [mtiLib] Only add ``Debg`` table with lookup names when ``FONTTOOLS_LOOKUP_DEBUGGING``
  env variable is set (#3023).

4.39.0 (released 2023-03-06)
----------------------------

- [mtiLib] Optionally add `Debg` debug info for MTI feature builds (#3018).
- [ttx] Support reading input file from standard input using special `-` character,
  similar to existing `-o -` option to write output to standard output (#3020).
- [cython] Prevent ``cython.compiled`` raise AttributeError if cython not installed
  properly (#3017).
- [OS/2] Guard against ZeroDivisionError when calculating xAvgCharWidth in the unlikely
  scenario no glyph has non-zero advance (#3015).
- [subset] Recompute xAvgCharWidth independently of --no-prune-unicode-ranges,
  previously the two options were involuntarily bundled together (#3012).
- [fontBuilder] Add ``debug`` parameter to addOpenTypeFeatures method to add source
  debugging information to the font in the ``Debg`` private table (#3008).
- [name] Make NameRecord `__lt__` comparison not fail on Unicode encoding errors (#3006).
- [featureVars] Fixed bug in ``overlayBox`` (#3003, #3005).
- [glyf] Added experimental support for cubic bezier curves in TrueType glyf table, as
  outlined in glyf v1 proposal (#2988):
  https://github.com/harfbuzz/boring-expansion-spec/blob/main/glyf1-cubicOutlines.md
- Added new qu2cu module and related qu2cuPen, the reverse of cu2qu for converting
  TrueType quadratic splines to cubic bezier curves (#2993).
- [glyf] Added experimental support for reading and writing Variable Composites/Components
  as defined in glyf v1 spec proposal (#2958):
  https://github.com/harfbuzz/boring-expansion-spec/blob/main/glyf1-varComposites.md.
- [pens]: Added `addVarComponent` method to pen protocols' base classes, which pens can implement
  to handle varcomponents (by default they get decomposed) (#2958).
- [misc.transform] Added DecomposedTransform class which implements an affine transformation
  with separate translate, rotation, scale, skew, and transformation-center components (#2598)
- [sbix] Ensure Glyph.referenceGlyphName is set; fixes error after dumping and
  re-compiling sbix table with 'dupe' glyphs (#2984).
- [feaLib] Be cleverer when merging chained single substitutions into same lookup
  when they are specified using the inline notation (#2150, #2974).
- [instancer] Clamp user-inputted axis ranges to those of fvar (#2959).
- [otBase/subset] Define ``__getstate__`` for BaseTable so that a copied/pickled 'lazy'
  object gets its own OTTableReader to read from; incidentally fixes a bug while
  subsetting COLRv1 table containing ClipBoxes on python 3.11 (#2965, #2968).
- [sbix] Handle glyphs with "dupe" graphic type on compile correctly (#2963).
- [glyf] ``endPointsOfContours`` field should be unsigned! Kudos to behdad for
  spotting one of the oldest bugs in FT. Probably nobody has ever dared to make
  glyphs with more than 32767 points... (#2957).
- [feaLib] Fixed handling of ``ignore`` statements with unmarked glyphs to match
  makeotf behavior, which assumes the first glyph is marked (#2950).
- Reformatted code with ``black`` and enforce new code style via CI check (#2925).
- [feaLib] Sort name table entries following OT spec prescribed order in the builder (#2927).
- [cu2quPen] Add Cu2QuMultiPen that converts multiple outlines at a time in
  interpolation compatible way; its methods take a list of tuples arguments
  that would normally be passed to individual segment pens, and at the end it
  dispatches the converted outlines to each pen (#2912).
- [reverseContourPen/ttGlyphPen] Add outputImpliedClosingLine option (#2913, #2914,
  #2921, #2922, #2995).
- [gvar] Avoid expanding all glyphs unnecessarily upon compile (#2918).
- [scaleUpem] Fixed bug whereby CFF2 vsindex was scaled; it should not (#2893, #2894).
- [designspaceLib] Add DS.getAxisByTag and refactor getAxis (#2891).
- [unicodedata] map Zmth<->math in ot_tag_{to,from}_script (#1737, #2889).
- [woff2] Support encoding/decoding OVERLAP_SIMPLE glyf flags (#2576, #2884).
- [instancer] Update OS/2 class and post.italicAngle when default moved (L4)
- Dropped support for Python 3.7 which reached EOL, fontTools requires 3.8+.
- [instancer] Fixed instantiateFeatureVariations logic when a rule range becomes
  default-applicable (#2737, #2880).
- [ttLib] Add main to ttFont and ttCollection that just decompile and re-compile the
  input font (#2869).
- [featureVars] Insert 'rvrn' lookup at the beginning of LookupList, to work around bug
  in Apple implementation of 'rvrn' feature which the spec says it should be processed
  early whereas on macOS 10.15 it follows lookup order (#2140, #2867).
- [instancer/mutator] Remove 'DSIG' table if present.
- [svgPathPen] Don't close path in endPath(), assume open unless closePath() (#2089, #2865).

4.38.0 (released 2022-10-21)
----------------------------

- [varLib.instancer] Added support for L4 instancing, i.e. moving the default value of
  an axis while keeping it variable. Thanks Behdad! (#2728, #2861).
  It's now also possible to restrict an axis min/max values beyond the current default
  value, e.g. a font wght has min=100, def=400, max=900 and you want a partial VF that
  only varies between 500 and 700, you can now do that.
  You can either specify two min/max values (wght=500:700), and the new default will be
  set to either the minimum or maximum, depending on which one is closer to the current
  default (e.g. 500 in this case). Or you can specify three values (e.g. wght=500:600:700)
  to specify the new default value explicitly.
- [otlLib/featureVars] Set a few Count values so one doesn't need to compile the font
  to update them (#2860).
- [varLib.models] Make extrapolation work for 2-master models as well where one master
  is at the default location (#2843, #2846).
  Add optional extrapolate=False to normalizeLocation() (#2847, #2849).
- [varLib.cff] Fixed sub-optimal packing of CFF2 deltas by no longer rounding them to
  integer (#2838).
- [scaleUpem] Calculate numShorts in VarData after scale; handle CFF hintmasks (#2840).

4.37.4 (released 2022-09-30)
----------------------------

- [subset] Keep nameIDs used by CPAL palette entry labels (#2837).
- [varLib] Avoid negative hmtx values when creating font from variable CFF2 font (#2827).
- [instancer] Don't prune stat.ElidedFallbackNameID (#2828).
- [unicodedata] Update Scripts/Blocks to Unicode 15.0 (#2833).

4.37.3 (released 2022-09-20)
----------------------------

- Fix arguments in calls to (glyf) glyph.draw() and drawPoints(), whereby offset wasn't
  correctly passed down; this fix also exposed a second bug, where lsb and tsb were not
  set (#2824, #2825, adobe-type-tools/afdko#1560).

4.37.2 (released 2022-09-15)
----------------------------

- [subset] Keep CPAL table and don't attempt to prune unused color indices if OT-SVG
  table is present even if COLR table was subsetted away; OT-SVG may be referencing the
  CPAL table; for now we assume that's the case (#2814, #2815).
- [varLib.instancer] Downgrade GPOS/GSUB version if there are no more FeatureVariations
  after instancing (#2812).
- [subset] Added ``--no-lazy`` to optionally load fonts eagerly (mostly to ease
  debugging of table lazy loading, no practical effects) (#2807).
- [varLib] Avoid building empty COLR.DeltaSetIndexMap with only identity mappings (#2803).
- [feaLib] Allow multiple value record types (by promoting to the most general format)
  within the same PairPos subtable; e.g. this allows variable and non variable kerning
  rules to share the same subtable. This also fixes a bug whereby some kerning pairs
  would become unreachable while shapiong because of premature subtable splitting (#2772, #2776).
- [feaLib] Speed up ``VarScalar`` by caching models for recurring master locations (#2798).
- [feaLib] Optionally cythonize ``feaLib.lexer``, speeds up parsing FEA a bit (#2799).
- [designspaceLib] Avoid crash when handling unbounded rule conditions (#2797).
- [post] Don't crash if ``post`` legacy format 1 is malformed/improperly used (#2786)
- [gvar] Don't be "lazy" (load all glyph variations up front) when TTFont.lazy=False (#2771).
- [TTFont] Added ``normalizeLocation`` method to normalize a location dict from the
  font's defined axes space (also known as "user space") into the normalized (-1..+1)
  space. It applies ``avar`` mapping if the font contains an ``avar`` table (#2789).
- [TTVarGlyphSet] Support drawing glyph instances from CFF2 variable glyph set (#2784).
- [fontBuilder] Do not error when building cmap if there are zero code points (#2785).
- [varLib.plot] Added ability to plot a variation model and set of accompaning master
  values corresponding to the model's master locations into a pyplot figure (#2767).
- [Snippets] Added ``statShape.py`` script to draw statistical shape of a glyph as an
  ellips (requires pycairo) (baecd88).
- [TTVarGlyphSet] implement drawPoints natively, avoiding going through
  SegmentToPointPen (#2778).
- [TTVarGlyphSet] Fixed bug whereby drawing a composite glyph multiple times, its
  components would shif; needed an extra copy (#2774).

4.37.1 (released 2022-08-24)
----------------------------

- [subset] Fixed regression introduced with v4.37.0 while subsetting the VarStore of
  ``HVAR`` and ``VVAR`` tables, whereby an ``AttributeError: subset_varidxes`` was
  thrown because an apparently unused import statement (with the side-effect of
  dynamically binding that ``subset_varidxes`` method to the VarStore class) had been
  accidentally deleted in an unrelated PR (#2679, #2773).
- [pens] Added ``cairoPen`` (#2678).
- [gvar] Read ``gvar`` more lazily by not parsing all of the ``glyf`` table (#2771).
- [ttGlyphSet] Make ``drawPoints(pointPen)`` method work for CFF fonts as well via
  adapter pen (#2770).

4.37.0 (released 2022-08-23)
----------------------------

- [varLib.models] Reverted PR #2717 which added support for "narrow tents" in v4.36.0,
  as it introduced a regression (#2764, #2765). It will be restored in upcoming release
  once we found a solution to the bug.
- [cff.specializer] Fixed issue in charstring generalizer with the ``blend`` operator
  (#2750, #1975).
- [varLib.models] Added support for extrapolation (#2757).
- [ttGlyphSet] Ensure the newly added ``_TTVarGlyphSet`` inherits from ``_TTGlyphSet``
  to keep backward compatibility with existing API (#2762).
- [kern] Allow compiling legacy kern tables with more than 64k entries (d21cfdede).
- [visitor] Added new visitor API to traverse tree of objects and dispatch based
  on the attribute type: cf. ``fontTools.misc.visitor`` and ``fontTools.ttLib.ttVisitor``. Added ``fontTools.ttLib.scaleUpem`` module that uses the latter to
  change a font's units-per-em and scale all the related fields accordingly (#2718,
  #2755).

4.36.0 (released 2022-08-17)
----------------------------

- [varLib.models] Use a simpler model that generates narrower "tents" (regions, master
  supports) whenever possible: specifically when any two axes that actively "cooperate"
  (have masters at non-zero positions for both axes) have a complete set of intermediates.
  The simpler algorithm produces fewer overlapping regions and behaves better with
  respect to rounding at the peak positions than the generic solver, always matching
  intermediate masters exactly, instead of maximally 0.5 units off. This may be useful
  when 100% metrics compatibility is desired (#2218, #2717).
- [feaLib] Remove warning when about ``GDEF`` not being built when explicitly not
  requested; don't build one unconditonally even when not requested (#2744, also works
  around #2747).
- [ttFont] ``TTFont.getGlyphSet`` method now supports selecting a location that
  represents an instance of a variable font (supports both user-scale and normalized
  axes coordinates via the ``normalized=False`` parameter). Currently this only works
  for TrueType-flavored variable fonts (#2738).

4.35.0 (released 2022-08-15)
----------------------------

- [otData/otConverters] Added support for 'biased' PaintSweepGradient start/end angles
  to match latest COLRv1 spec (#2743).
- [varLib.instancer] Fixed bug in ``_instantiateFeatureVariations`` when at the same
  time pinning one axis and restricting the range of a subsequent axis; the wrong axis
  tag was being used in the latter step (as the records' axisIdx was updated in the
  preceding step but looked up using the old axes order in the following step) (#2733,
  #2734).
- [mtiLib] Pad script tags with space when less than 4 char long (#1727).
- [merge] Use ``'.'`` instead of ``'#'`` in duplicate glyph names (#2742).
- [gvar] Added support for lazily loading glyph variations (#2741).
- [varLib] In ``build_many``, we forgot to pass on ``colr_layer_reuse`` parameter to
  the ``build`` method (#2730).
- [svgPathPen] Add a main that prints SVG for input text (6df779fd).
- [cffLib.width] Fixed off-by-one in optimized values; previous code didn't match the
  code block above it (2963fa50).
- [varLib.interpolatable] Support reading .designspace and .glyphs files (via optional
  ``glyphsLib``).
- Compile some modules with Cython when available and building/installing fonttools
  from source: ``varLib.iup`` (35% faster), ``pens.momentsPen`` (makes
  ``varLib.interpolatable`` 3x faster).
- [feaLib] Allow features to be built for VF without also building a GDEF table (e.g.
  only build GSUB); warn when GDEF would be needed but isn't requested (#2705, 2694).
- [otBase] Fixed ``AttributeError`` when uharfbuzz < 0.23.0 and 'repack' method is
  missing (32aa8eaf). Use new ``uharfbuzz.repack_with_tag`` when available (since
  uharfbuzz>=0.30.0), enables table-specific optimizations to be performed during
  repacking (#2724).
- [statisticsPen] By default report all glyphs (4139d891). Avoid division-by-zero
  (52b28f90).
- [feaLib] Added missing required argument to FeatureLibError exception (#2693)
- [varLib.merge] Fixed error during error reporting (#2689). Fixed undefined
  ``NotANone`` variable (#2714).

4.34.4 (released 2022-07-07)
----------------------------

- Fixed typo in varLib/merger.py that causes NameError merging COLR glyphs
  containing more than 255 layers (#2685).

4.34.3 (released 2022-07-07)
----------------------------

- [designspaceLib] Don't make up bad PS names when no STAT data (#2684)

4.34.2 (released 2022-07-06)
----------------------------

- [varStore/subset] fixed KeyError exception to do with NO_VARIATION_INDEX while
  subsetting varidxes in GPOS/GDEF (a08140d).

4.34.1 (released 2022-07-06)
----------------------------

- [instancer] When optimizing HVAR/VVAR VarStore, use_NO_VARIATION_INDEX=False to avoid
  including NO_VARIATION_INDEX in AdvWidthMap, RsbMap, LsbMap mappings, which would
  push the VarIdx width to maximum (4bytes), which is not desirable. This also fixes
  a hard crash when attempting to subset a varfont after it had been partially instanced
  with use_NO_VARIATION_INDEX=True.

4.34.0 (released 2022-07-06)
----------------------------

- [instancer] Set RIBBI bits in head and OS/2 table when cutting instances and the
  subfamily nameID=2 contains strings like 'Italic' or 'Bold' (#2673).
- [otTraverse] Addded module containing methods for traversing trees of otData tables
  (#2660).
- [otTables] Made DeltaSetIndexMap TTX dump less verbose by omitting no-op entries
  (#2660).
- [colorLib.builder] Added option to disable PaintColrLayers's reuse of layers from
  LayerList (#2660).
- [varLib] Added support for merging multiple master COLRv1 tables into a variable
  COLR table (#2660, #2328). Base color glyphs of same name in different masters must have
  identical paint graph structure (incl. number of layers, palette indices, number
  of color line stops, corresponding paint formats at each level of the graph),
  but can differ in the variable fields (e.g. PaintSolid.Alpha). PaintVar* tables
  are produced when this happens and a VarStore/DeltaSetIndexMap is added to the
  variable COLR table. It is possible for non-default masters to be 'sparse', i.e.
  omit some of the color glyphs present in the default master.
- [feaLib] Let the Parser set nameIDs 1 through 6 that were previously reserved (#2675).
- [varLib.varStore] Support NO_VARIATION_INDEX in optimizer and instancer.
- [feaLib] Show all missing glyphs at once at end of parsing (#2665).
- [varLib.iup] Rewrite force-set conditions and limit DP loopback length (#2651).
  For Noto Sans, IUP time drops from 23s down to 9s, with only a slight size increase
  in the final font. This basically turns the algorithm from O(n^3) into O(n).
- [featureVars] Report about missing glyphs in substitution rules (#2654).
- [mutator/instancer] Added CLI flag to --no-recalc-timestamp (#2649).
- [SVG] Allow individual SVG documents in SVG OT table to be compressed on uncompressed,
  and remember that when roundtripping to/from ttx. The SVG.docList is now a list
  of SVGDocument namedtuple-like dataclass containing an extra ``compressed`` field,
  and no longer a bare 3-tuple (#2645).
- [designspaceLib] Check for descriptor types with hasattr() to allow custom classes
  that don't inherit the default descriptors (#2634).
- [subset] Enable sharing across subtables of extension lookups for harfbuzz packing
  (#2626). Updated how table packing falls back to fontTools from harfbuzz (#2668).
- [subset] Updated default feature tags following current Harfbuzz (#2637).
- [svgLib] Fixed regex for real number to support e.g. 1e-4 in addition to 1.0e-4.
  Support parsing negative rx, ry on arc commands (#2596, #2611).
- [subset] Fixed subsetting SinglePosFormat2 when ValueFormat=0 (#2603).

4.33.3 (released 2022-04-26)
----------------------------

- [designspaceLib] Fixed typo in ``deepcopyExceptFonts`` method, preventing font
  references to be transferred (#2600). Fixed another typo in the name of ``Range``
  dataclass's ``__post_init__`` magic method (#2597).

4.33.2 (released 2022-04-22)
----------------------------

- [otBase] Make logging less verbose when harfbuzz fails to serialize. Do not exit
  at the first failure but continue attempting to fix offset overflow error using
  the pure-python serializer even when the ``USE_HARFBUZZ_REPACKER`` option was
  explicitly set to ``True``. This is normal with fonts with relatively large
  tables, at least until hb.repack implements proper table splitting.

4.33.1 (released 2022-04-22)
----------------------------

- [otlLib] Put back the ``FONTTOOLS_GPOS_COMPACT_MODE`` environment variable to fix
  regression in ufo2ft (and thus fontmake) introduced with v4.33.0 (#2592, #2593).
  This is deprecated and will be removed one ufo2ft gets updated to use the new
  config setup.

4.33.0 (released 2022-04-21)
----------------------------

- [OS/2 / merge] Automatically recalculate ``OS/2.xAvgCharWidth`` after merging
  fonts with ``fontTools.merge`` (#2591, #2538).
- [misc/config] Added ``fontTools.misc.configTools`` module, a generic configuration
  system (#2416, #2439).
  Added ``fontTools.config`` module, a fontTools-specific configuration
  system using ``configTools`` above.
  Attached a ``Config`` object to ``TTFont``.
- [otlLib] Replaced environment variable for GPOS compression level with an
  equivalent option using the new config system.
- [designspaceLib] Incremented format version to 5.0 (#2436).
  Added discrete axes, variable fonts, STAT information, either design- or
  user-space location on instances.
  Added ``fontTools.designspaceLib.split`` module to split a designspace
  into sub-spaces that interpolate and that represent the variable fonts
  listed in the document.
  Made instance names optional and allow computing them from STAT data instead.
  Added ``fontTools.designspaceLib.statNames`` module.
  Allow instances to have the same location as a previously defined STAT label.
  Deprecated some attributes:
  ``SourceDescriptor``: ``copyLib``, ``copyInfo``, ``copyGroups``, ``copyFeatures``.
  ``InstanceDescriptor``: ``kerning``, ``info``; ``glyphs``: use rules or sparse
  sources.
  For both, ``location``: use the more explicit designLocation.
  Note: all are soft deprecations and existing code should keep working.
  Updated documentation for Python methods and the XML format.
- [varLib] Added ``build_many`` to build several variable fonts from a single
  designspace document (#2436).
  Added ``fontTools.varLib.stat`` module to build STAT tables from a designspace
  document.
- [otBase] Try to use the Harfbuzz Repacker for packing GSUB/GPOS tables when
  ``uharfbuzz`` python bindings are available (#2552). Disable it by setting the
  "fontTools.ttLib.tables.otBase:USE_HARFBUZZ_REPACKER" config option to ``False``.
  If the option is set explicitly to ``True`` but ``uharfbuzz`` can't be imported
  or fails to serialize for any reasons, an error will be raised (ImportError or
  uharfbuzz errors).
- [CFF/T2] Ensure that ``pen.closePath()`` gets called for CFF2 charstrings (#2577).
  Handle implicit CFF2 closePath within ``T2OutlineExtractor`` (#2580).

4.32.0 (released 2022-04-08)
----------------------------

- [otlLib] Disable GPOS7 optimization to work around bug in Apple CoreText.
  Always force Chaining GPOS8 for now (#2540).
- [glifLib] Added ``outputImpliedClosingLine=False`` parameter to ``Glyph.draw()``,
  to control behaviour of ``PointToSegmentPen`` (6b4e2e7).
- [varLib.interpolatable] Check for wrong contour starting point (#2571).
- [cffLib] Remove leftover ``GlobalState`` class and fix calls to ``TopDictIndex()``
  (#2569, #2570).
- [instancer] Clear ``AxisValueArray`` if it is empty after instantiating (#2563).

4.31.2 (released 2022-03-22)
----------------------------

- [varLib] fix instantiation of GPOS SinglePos values (#2555).

4.31.1 (released 2022-03-18)
----------------------------

- [subset] fix subsetting OT-SVG when glyph id attribute is on the root ``<svg>``
  element (#2553).

4.31.0 (released 2022-03-18)
----------------------------

- [ttCollection] Fixed 'ResourceWarning: unclosed file' warning (#2549).
- [varLib.merger] Handle merging SinglePos with valueformat=0 (#2550).
- [ttFont] Update glyf's glyphOrder when calling TTFont.setGlyphOrder() (#2544).
- [ttFont] Added ``ensureDecompiled`` method to load all tables irrespective
  of the ``lazy`` attribute (#2551).
- [otBase] Added ``iterSubTable`` method to iterate over BaseTable's children of
  type BaseTable; useful for traversing a tree of otTables (#2551).

4.30.0 (released 2022-03-10)
----------------------------

- [varLib] Added debug logger showing the glyph name for which ``gvar`` is built (#2542).
- [varLib.errors] Fixed undefined names in ``FoundANone`` and ``UnsupportedFormat``
  exceptions (ac4d5611).
- [otlLib.builder] Added ``windowsNames`` and ``macNames`` (bool) parameters to the
  ``buildStatTabe`` function, so that one can select whether to only add one or both
  of the two sets (#2528).
- [t1Lib] Added the ability to recreate PostScript stream (#2504).
- [name] Added ``getFirstDebugName``, ``getBest{Family,SubFamily,Full}Name`` methods (#2526).

4.29.1 (released 2022-02-01)
----------------------------

- [colorLib] Fixed rounding issue with radial gradient's start/end circles inside
  one another (#2521).
- [freetypePen] Handle rotate/skew transform when auto-computing width/height of the
  buffer; raise PenError wen missing moveTo (#2517)

4.29.0 (released 2022-01-24)
----------------------------

- [ufoLib] Fixed illegal characters and expanded reserved filenames (#2506).
- [COLRv1] Don't emit useless PaintColrLayers of lenght=1 in LayerListBuilder (#2513).
- [ttx] Removed legacy ``waitForKeyPress`` method on Windows (#2509).
- [pens] Added FreeTypePen that uses ``freetype-py`` and the pen protocol for
  rasterizating outline paths (#2494).
- [unicodedata] Updated the script direction list to Unicode 14.0 (#2484).
  Bumped unicodedata2 dependency to 14.0 (#2499).
- [psLib] Fixed type of ``fontName`` in ``suckfont`` (#2496).

4.28.5 (released 2021-12-19)
----------------------------

- [svgPathPen] Continuation of #2471: make sure all occurrences of ``str()`` are now
  replaced with user-defined ``ntos`` callable.
- [merge] Refactored code into submodules, plus several bugfixes and improvements:
  fixed duplicate-glyph-resolution GSUB-lookup generation code; use tolerance in glyph
  comparison for empty glyph's width; ignore space of default ignorable glyphs;
  downgrade duplicates-resolution missing-GSUB from assert to warn; added --drop-tables
  option (#2473, #2475, #2476).

4.28.4 (released 2021-12-15)
----------------------------

- [merge] Merge GDEF marksets in Lookups properly (#2474).
- [feaLib] Have ``fontTools feaLib`` script exit with error code when build fails (#2459)
- [svgPathPen] Added ``ntos`` option to customize number formatting (e.g. rounding) (#2471).
- [subset] Speed up subsetting of large CFF fonts (#2467).
- [otTables] Speculatively promote lookups to extension to speed up compilation. If the
  offset to lookup N is too big to fit in a ushort, the offset to lookup N+1 is going to
  be too big as well, so we promote to extension all lookups from lookup N onwards (#2465).

4.28.3 (released 2021-12-03)
----------------------------

- [subset] Fixed bug while subsetting ``COLR`` table, whereby incomplete layer records
  pointing to missing glyphs were being retained leading to ``struct.error`` upon
  compiling. Make it so that ``glyf`` glyph closure, which follows the ``COLR`` glyph
  closure, does not influence the ``COLR`` table subsetting (#2461, #2462).
- [docs] Fully document the ``cmap`` and ``glyf`` tables (#2454, #2457).
- [colorLib.unbuilder] Fixed CLI by deleting no longer existing parameter (180bb1867).

4.28.2 (released 2021-11-22)
----------------------------

- [otlLib] Remove duplicates when building coverage (#2433).
- [docs] Add interrogate configuration (#2443).
- [docs] Remove comment about missing “start” optional argument to ``calcChecksum`` (#2448).
- [cu2qu/cli] Adapt to the latest ufoLib2.
- [subset] Support subsetting SVG table and remove it from the list of  drop by default tables (#534).
- [subset] add ``--pretty-svg`` option to pretty print SVG table contents (#2452).
- [merge] Support merging ``CFF`` tables (CID-keyed ``CFF`` is still not supported) (#2447).
- [merge] Support ``--output-file`` (#2447).
- [docs] Split table docs into individual pages (#2444).
- [feaLib] Forbid empty classes (#2446).
- [docs] Improve documentation for ``fontTools.ttLib.ttFont`` (#2442).

4.28.1 (released 2021-11-08)
----------------------------

- [subset] Fixed AttributeError while traversing a color glyph's Paint graph when there is no
  LayerList, which is optional (#2441).

4.28.0 (released 2021-11-05)
----------------------------

- Dropped support for EOL Python 3.6, require Python 3.7 (#2417).
- [ufoLib/glifLib] Make filename-clash checks faster by using a set instead of a list (#2422).
- [subset] Don't crash if optional ClipList and LayerList are ``None`` (empty) (#2424, 2439).
- [OT-SVG] Removed support for old deprecated version 1 and embedded color palettes,
  which were never officially part of the OpenType SVG spec. Upon compile, reuse offsets
  to SVG documents that are identical (#2430).
- [feaLib] Added support for Variable Feature File syntax. This is experimental and subject
  to change until it is finalized in the Adobe FEA spec (#2432).
- [unicodedata] Update Scripts/ScriptExtensions/Blocks to UnicodeData 14.0 (#2437).

4.27.1 (released 2021-09-23)
----------------------------

- [otlLib] Fixed error when chained contextual lookup builder overflows (#2404, #2411).
- [bezierTools] Fixed two floating-point bugs: one when computing `t` for a point
  lying on an almost horizontal/vertical line; another when computing the intersection
  point between a curve and a line (#2413).

4.27.0 (released 2021-09-14)
----------------------------

- [ttLib/otTables] Cleaned up virtual GID handling: allow virtual GIDs in ``Coverage``
  and ``ClassDef`` readers; removed unused ``allowVID`` argument from ``TTFont``
  constructor, and ``requireReal`` argument in ``TTFont.getGlyphID`` method.
  Make ``TTFont.setGlyphOrder`` clear reverse glyphOrder map, and assume ``glyphOrder``
  internal attribute is never modified outside setGlyphOrder; added ``TTFont.getGlyphNameMany``
  and ``getGlyphIDMany`` (#1536, #1654, #2334, #2398).
- [py23] Dropped internal use of ``fontTools.py23`` module to fix deprecation warnings
  in client code that imports from fontTools (#2234, #2399, #2400).
- [subset] Fix subsetting COLRv1 clip boxes when font is loaded lazily (#2408).

4.26.2 (released 2021-08-09)
----------------------------

- [otTables] Added missing ``CompositeMode.PLUS`` operator (#2390).

4.26.1 (released 2021-08-03)
----------------------------

- [transform] Added ``transformVector`` and ``transformVectors`` methods to the
  ``Transform`` class. Similar to ``transformPoint`` but ignore the translation
  part (#2386).

4.26.0 (released 2021-08-03)
----------------------------

- [xmlWriter] Default to ``"\n"`` for ``newlinestr`` instead of platform-specific
  ``os.linesep`` (#2384).
- [otData] Define COLRv1 ClipList and ClipBox (#2379).
- [removeOverlaps/instancer] Added --ignore-overlap-errors option to work around
  Skia PathOps.Simplify bug (#2382, #2363, google/fonts#3365).
- NOTE: This will be the last version to support Python 3.6. FontTools will require
  Python 3.7 or above from the next release (#2350)

4.25.2 (released 2021-07-26)
----------------------------

- [COLRv1] Various changes to sync with the latest CORLv1 draft spec. In particular:
  define COLR.VarIndexMap, remove/inline ColorIndex struct, add VarIndexBase to ``PaintVar*`` tables (#2372);
  add reduced-precicion specialized transform Paints;
  define Angle as fraction of half circle encoded as F2Dot14;
  use FWORD (int16) for all Paint center coordinates;
  change PaintTransform to have an offset to Affine2x3;
- [ttLib] when importing XML, only set sfntVersion if the font has no reader and is empty (#2376)

4.25.1 (released 2021-07-16)
----------------------------

- [ttGlyphPen] Fixed bug in ``TTGlyphPointPen``, whereby open contours (i.e. starting
  with segmentType "move") would throw ``NotImplementedError``. They are now treated
  as if they are closed, like with the ``TTGlyphPen`` (#2364, #2366).

4.25.0 (released 2021-07-05)
----------------------------

- [tfmLib] Added new library for parsing TeX Font Metric (TFM) files (#2354).
- [TupleVariation] Make shared tuples order deterministic on python < 3.7 where
  Counter (subclass of dict) doesn't remember insertion order (#2351, #2353).
- [otData] Renamed COLRv1 structs to remove 'v1' suffix and match the updated draft
  spec: 'LayerV1List' -> 'LayerList', 'BaseGlyphV1List' -> 'BaseGlyphList',
  'BaseGlyphV1Record' -> 'BaseGlyphPaintRecord' (#2346).
  Added 8 new ``PaintScale*`` tables: with/without centers, uniform vs non-uniform.
  Added ``*AroundCenter`` variants to ``PaintRotate`` and ``PaintSkew``: the default
  versions no longer have centerX/Y, but default to origin.
  ``PaintRotate``, ``PaintSkew`` and ``PaintComposite`` formats were re-numbered.
  NOTE: these are breaking changes; clients using the experimental COLRv1 API will
  have to be updated (#2348).
- [pointPens] Allow ``GuessSmoothPointPen`` to accept a tolerance. Fixed call to
  ``math.atan2`` with x/y parameters inverted. Sync the code with fontPens (#2344).
- [post] Fixed parsing ``post`` table format 2.0 when it contains extra garbage
  at the end of the stringData array (#2314).
- [subset] drop empty features unless 'size' with FeatureParams table (#2324).
- [otlLib] Added ``otlLib.optimize`` module; added GPOS compaction algorithm.
  The compaction can be run on existing fonts with ``fonttools otlLib.optimize``
  or using the snippet ``compact_gpos.py``. There's experimental support for
  compacting fonts at compilation time using an environment variable, but that
  might be removed later (#2326).

4.24.4 (released 2021-05-25)
----------------------------

- [subset/instancer] Fixed ``AttributeError`` when instantiating a VF that
  contains GPOS ValueRecords with ``Device`` tables but without the respective
  non-Device values (e.g. ``XAdvDevice`` without ``XAdvance``). When not
  explicitly set, the latter are assumed to be 0 (#2323).

4.24.3 (released 2021-05-20)
----------------------------

- [otTables] Fixed ``AttributeError`` in methods that split LigatureSubst,
  MultipleSubst and AlternateSubst subtables when an offset overflow occurs.
  The ``Format`` attribute was removed in v4.22.0 (#2319).

4.24.2 (released 2021-05-20)
----------------------------

- [ttGlyphPen] Fixed typing annotation of TTGlyphPen glyphSet parameter (#2315).
- Fixed two instances of DeprecationWarning: invalid escape sequence (#2311).

4.24.1 (released 2021-05-20)
----------------------------

- [subset] Fixed AttributeError when SinglePos subtable has None Value (ValueFormat 0)
  (#2312, #2313).

4.24.0 (released 2021-05-17)
----------------------------

- [pens] Add ``ttGlyphPen.TTGlyphPointPen`` similar to ``TTGlyphPen`` (#2205).

4.23.1 (released 2021-05-14)
----------------------------

- [subset] Fix ``KeyError`` after subsetting ``COLR`` table that initially contains
  both v0 and v1 color glyphs when the subset only requested v1 glyphs; we were
  not pruning the v0 portion of the table (#2308).
- [colorLib] Set ``LayerV1List`` attribute to ``None`` when empty, it's optional
  in CORLv1 (#2308).

4.23.0 (released 2021-05-13)
----------------------------

- [designspaceLib] Allow to use ``\\UNC`` absolute paths on Windows (#2299, #2306).
- [varLib.merger] Fixed bug where ``VarLibMergeError`` was raised with incorrect
  parameters (#2300).
- [feaLib] Allow substituting a glyph class with ``NULL`` to delete multiple glyphs
  (#2303).
- [glyf] Fixed ``NameError`` exception in ``getPhantomPoints`` (#2295, #2305).
- [removeOverlaps] Retry pathops.simplify after rounding path coordinates to integers
  if it fails the first time using floats, to work around a rare and hard to debug
  Skia bug (#2288).
- [varLib] Added support for building, reading, writing and optimizing 32-bit
  ``ItemVariationStore`` as used in COLRv1 table (#2285).
- [otBase/otConverters] Add array readers/writers for int types (#2285).
- [feaLib] Allow more than one lookahead glyph/class in contextual positioning with
  "value at end" (#2293, #2294).
- [COLRv1] Default varIdx should be 0xFFFFFFFF (#2297, #2298).
- [pens] Make RecordingPointPen actually pass on identifiers; replace asserts with
  explicit ``PenError`` exception (#2284).
- [mutator] Round lsb for CF2 fonts as well (#2286).

4.22.1 (released 2021-04-26)
----------------------------

- [feaLib] Skip references to named lookups if the lookup block definition
  is empty, similarly to makeotf. This also fixes an ``AttributeError`` while
  generating ``aalt`` feature (#2276, #2277).
- [subset] Fixed bug with ``--no-hinting`` implementation for Device tables (#2272,
  #2275). The previous code was alwyas dropping Device tables if no-hinting was
  requested, but some Device tables (DeltaFormat=0x8000) are also used to encode
  variation indices and need to be retained.
- [otBase] Fixed bug in getting the ValueRecordSize when decompiling ``MVAR``
  table with ``lazy=True`` (#2273, #2274).
- [varLib/glyf/gvar] Optimized and simplified ``GlyphCoordinates`` and
  ``TupleVariation`` classes, use ``bytearray`` where possible, refactored
  phantom-points calculations. We measured about 30% speedup in total time
  of loading master ttfs, building gvar, and saving (#2261, #2266).
- [subset] Fixed ``AssertionError`` while pruning unused CPAL palettes when
  ``0xFFFF`` is present (#2257, #2259).

4.22.0 (released 2021-04-01)
----------------------------

- [ttLib] Remove .Format from Coverage, ClassDef, SingleSubst, LigatureSubst,
  AlternateSubst, MultipleSubst (#2238).
  ATTENTION: This will change your TTX dumps!
- [misc.arrayTools] move Vector to its own submodule, and rewrite as a tuple
  subclass (#2201).
- [docs] Added a terminology section for varLib (#2209).
- [varLib] Move rounding to VariationModel, to avoid error accumulation from
  multiple deltas (#2214)
- [varLib] Explain merge errors in more human-friendly terms (#2223, #2226)
- [otlLib] Correct some documentation (#2225)
- [varLib/otlLib] Allow merging into VariationFont without first saving GPOS
  PairPos2 (#2229)
- [subset] Improve PairPosFormat2 subsetting (#2221)
- [ttLib] TTFont.save: create file on disk as late as possible (#2253)
- [cffLib] Add missing CFF2 dict operators LanguageGroup and ExpansionFactor
  (#2249)
  ATTENTION: This will change your TTX dumps!

4.21.1 (released 2021-02-26)
----------------------------

- [pens] Reverted breaking change that turned ``AbstractPen`` and ``AbstractPointPen``
  into abstract base classes (#2164, #2198).

4.21.0 (released 2021-02-26)
----------------------------

- [feaLib] Indent anchor statements in ``asFea()`` to make them more legible and
  diff-able (#2193).
- [pens] Turn ``AbstractPen`` and ``AbstractPointPen`` into abstract base classes
  (#2164).
- [feaLib] Added support for parsing and building ``STAT`` table from AFDKO feature
  files (#2039).
- [instancer] Added option to update name table of generated instance using ``STAT``
  table's axis values (#2189).
- [bezierTools] Added functions to compute bezier point-at-time, as well as line-line,
  curve-line and curve-curve intersections (#2192).

4.20.0 (released 2021-02-15)
----------------------------

- [COLRv1] Added ``unbuildColrV1`` to deconstruct COLRv1 otTables to raw json-able
  data structure; it does the reverse of ``buildColrV1`` (#2171).
- [feaLib] Allow ``sub X by NULL`` sequence to delete a glyph (#2170).
- [arrayTools] Fixed ``Vector`` division (#2173).
- [COLRv1] Define new ``PaintSweepGradient`` (#2172).
- [otTables] Moved ``Paint.Format`` enum class outside of ``Paint`` class definition,
  now named ``PaintFormat``. It was clashing with paint instance ``Format`` attribute
  and thus was breaking lazy load of COLR table which relies on magic ``__getattr__``
  (#2175).
- [COLRv1] Replace hand-coded builder functions with otData-driven dynamic
  implementation (#2181).
- [COLRv1] Define additional static (non-variable) Paint formats (#2181).
- [subset] Added support for subsetting COLR v1 and CPAL tables (#2174, #2177).
- [fontBuilder] Allow ``setupFvar`` to optionally take ``designspaceLib.AxisDescriptor``
  objects. Added new ``setupAvar`` method. Support localised names for axes and
  named instances (#2185).

4.19.1 (released 2021-01-28)
----------------------------

- [woff2] An initial off-curve point with an overlap flag now stays an off-curve
  point after compression.

4.19.0 (released 2021-01-25)
----------------------------

- [codecs] Handle ``errors`` parameter different from 'strict' for the custom
  extended mac encodings (#2137, #2132).
- [featureVars] Raise better error message when a script is missing the required
  default language system (#2154).
- [COLRv1] Avoid abrupt change caused by rounding ``PaintRadialGradient.c0`` when
  the start circle almost touches the end circle's perimeter (#2148).
- [COLRv1] Support building unlimited lists of paints as 255-ary trees of
  ``PaintColrLayers`` tables (#2153).
- [subset] Prune redundant format-12 cmap subtables when all non-BMP characters
  are dropped (#2146).
- [basePen] Raise ``MissingComponentError`` instead of bare ``KeyError`` when a
  referenced component is missing (#2145).

4.18.2 (released 2020-12-16)
----------------------------

- [COLRv1] Implemented ``PaintTranslate`` paint format (#2129).
- [varLib.cff] Fixed unbound local variable error (#1787).
- [otlLib] Don't crash when creating OpenType class definitions if some glyphs
  occur more than once (#2125).

4.18.1 (released 2020-12-09)
----------------------------

- [colorLib] Speed optimization for ``LayerV1ListBuilder`` (#2119).
- [mutator] Fixed missing tab in ``interpolate_cff2_metrics`` (0957dc7a).

4.18.0 (released 2020-12-04)
----------------------------

- [COLRv1] Update to latest draft: added ``PaintRotate`` and ``PaintSkew`` (#2118).
- [woff2] Support new ``brotlicffi`` bindings for PyPy (#2117).
- [glifLib] Added ``expectContentsFile`` parameter to ``GlyphSet``, for use when
  reading existing UFOs, to comply with the specification stating that a
  ``contents.plist`` file must exist in a glyph set (#2114).
- [subset] Allow ``LangSys`` tags in ``--layout-scripts`` option (#2112). For example:
  ``--layout-scripts=arab.dflt,arab.URD,latn``; this will keep ``DefaultLangSys``
  and ``URD`` language for ``arab`` script, and all languages for ``latn`` script.
- [varLib.interpolatable] Allow UFOs to be checked; report open paths, non existant
  glyphs; add a ``--json`` option to produce a machine-readable list of
  incompatibilities
- [pens] Added ``QuartzPen`` to create ``CGPath`` from glyph outlines on macOS.
  Requires pyobjc (#2107).
- [feaLib] You can export ``FONTTOOLS_LOOKUP_DEBUGGING=1`` to enable feature file
  debugging info stored in ``Debg`` table (#2106).
- [otlLib] Build more efficient format 1 and format 2 contextual lookups whenever
  possible (#2101).

4.17.1 (released 2020-11-16)
----------------------------

- [colorLib] Fixed regression in 4.17.0 when building COLR v0 table; when color
  layers are stored in UFO lib plist, we can't distinguish tuples from lists so
  we need to accept either types (e5439eb9, googlefonts/ufo2ft/issues#426).

4.17.0 (released 2020-11-12)
----------------------------

- [colorLib/otData] Updated to latest draft ``COLR`` v1 spec (#2092).
- [svgLib] Fixed parsing error when arc commands' boolean flags are not separated
  by space or comma (#2094).
- [varLib] Interpret empty non-default glyphs as 'missing', if the default glyph is
  not empty (#2082).
- [feaLib.builder] Only stash lookup location for ``Debg`` if ``Builder.buildLookups_``
  has cooperated (#2065, #2067).
- [varLib] Fixed bug in VarStore optimizer (#2073, #2083).
- [varLib] Add designspace lib key for custom feavar feature tag (#2080).
- Add HashPointPen adapted from psautohint. With this pen, a hash value of a glyph
  can be computed, which can later be used to detect glyph changes (#2005).

4.16.1 (released 2020-10-05)
----------------------------

- [varLib.instancer] Fixed ``TypeError`` exception when instantiating a VF with
  a GSUB table 1.1 in which ``FeatureVariations`` attribute is present but set to
  ``None`` -- indicating that optional ``FeatureVariations`` is missing (#2077).
- [glifLib] Make ``x`` and ``y`` attributes of the ``point`` element required
  even when validation is turned off, and raise a meaningful ``GlifLibError``
  message when that happens (#2075).

4.16.0 (released 2020-09-30)
----------------------------

- [removeOverlaps] Added new module and ``removeOverlaps`` function that merges
  overlapping contours and components in TrueType glyphs. It requires the
  `skia-pathops <https://github.com/fonttools/skia-pathops>`__ module.
  Note that removing overlaps invalidates the TrueType hinting (#2068).
- [varLib.instancer] Added ``--remove-overlaps`` command-line option.
  The ``overlap`` option in ``instantiateVariableFont`` now takes an ``OverlapMode``
  enum: 0: KEEP_AND_DONT_SET_FLAGS, 1: KEEP_AND_SET_FLAGS (default), and 2: REMOVE.
  The latter is equivalent to calling ``removeOverlaps`` on the generated static
  instance. The option continues to accept ``bool`` value for backward compatibility.


4.15.0 (released 2020-09-21)
----------------------------

- [plistlib] Added typing annotations to plistlib module. Set up mypy static
  typechecker to run automatically on CI (#2061).
- [ttLib] Implement private ``Debg`` table, a reverse-DNS namespaced JSON dict.
- [feaLib] Optionally add an entry into the ``Debg`` table with the original
  lookup name (if any), feature name / script / language combination (if any),
  and original source filename and line location. Annotate the ttx output for
  a lookup with the information from the Debg table (#2052).
- [sfnt] Disabled checksum checking by default in ``SFNTReader`` (#2058).
- [Docs] Document ``mtiLib`` module (#2027).
- [varLib.interpolatable] Added checks for contour node count and operation type
  of each node (#2054).
- [ttLib] Added API to register custom table packer/unpacker classes (#2055).

4.14.0 (released 2020-08-19)
----------------------------

- [feaLib] Allow anonymous classes in LookupFlags definitions (#2037).
- [Docs] Better document DesignSpace rules processing order (#2041).
- [ttLib] Fixed 21-year old bug in ``maxp.maxComponentDepth`` calculation (#2044,
  #2045).
- [varLib.models] Fixed misspelled argument name in CLI entry point (81d0042a).
- [subset] When subsetting GSUB v1.1, fixed TypeError by checking whether the
  optional FeatureVariations table is present (e63ecc5b).
- [Snippets] Added snippet to show how to decompose glyphs in a TTF (#2030).
- [otlLib] Generate GSUB type 5 and GPOS type 7 contextual lookups where appropriate
  (#2016).

4.13.0 (released 2020-07-10)
----------------------------

- [feaLib/otlLib] Moved lookup subtable builders from feaLib to otlLib; refactored
  some common code (#2004, #2007).
- [docs] Document otlLib module (#2009).
- [glifLib] Fixed bug with some UFO .glif filenames clashing on case-insensitive
  filesystems (#2001, #2002).
- [colorLib] Updated COLRv1 implementation following changes in the draft spec:
  (#2008, googlefonts/colr-gradients-spec#24).

4.12.1 (released 2020-06-16)
----------------------------

- [_n_a_m_e] Fixed error in ``addMultilingualName`` with one-character names.
  Only attempt to recovered malformed UTF-16 data from a ``bytes`` string,
  not from unicode ``str`` (#1997, #1998).

4.12.0 (released 2020-06-09)
----------------------------

- [otlLib/varLib] Ensure that the ``AxisNameID`` in the ``STAT`` and ``fvar``
  tables is grater than 255 as per OpenType spec (#1985, #1986).
- [docs] Document more modules in ``fontTools.misc`` package: ``filenames``,
  ``fixedTools``, ``intTools``, ``loggingTools``, ``macCreatorType``, ``macRes``,
  ``plistlib`` (#1981).
- [OS/2] Don't calculate whole sets of unicode codepoints, use faster and more memory
  efficient ranges and bisect lookups (#1984).
- [voltLib] Support writing back abstract syntax tree as VOLT data (#1983).
- [voltLib] Accept DO_NOT_TOUCH_CMAP keyword (#1987).
- [subset/merge] Fixed a namespace clash involving a private helper class (#1955).

4.11.0 (released 2020-05-28)
----------------------------

- [feaLib] Introduced ``includeDir`` parameter on Parser and IncludingLexer to
  explicitly specify the directory to search when ``include()`` statements are
  encountered (#1973).
- [ufoLib] Silently delete duplicate glyphs within the same kerning group when reading
  groups (#1970).
- [ttLib] Set version of COLR table when decompiling COLRv1 (commit 9d8a7e2).

4.10.2 (released 2020-05-20)
----------------------------

- [sfnt] Fixed ``NameError: SimpleNamespace`` while reading TTC header. The regression
  was introduced with 4.10.1 after removing ``py23`` star import.

4.10.1 (released 2020-05-19)
----------------------------

- [sfnt] Make ``SFNTReader`` pickleable even when TTFont is loaded with lazy=True
  option and thus keeps a reference to an external file (#1962, #1967).
- [feaLib.ast] Restore backward compatibility (broken in 4.10 with #1905) for
  ``ChainContextPosStatement`` and ``ChainContextSubstStatement`` classes.
  Make them accept either list of lookups or list of lists of lookups (#1961).
- [docs] Document some modules in ``fontTools.misc`` package: ``arrayTools``,
  ``bezierTools`` ``cliTools`` and ``eexec`` (#1956).
- [ttLib._n_a_m_e] Fixed ``findMultilingualName()`` when name record's ``string`` is
  encoded as bytes sequence (#1963).

4.10.0 (released 2020-05-15)
----------------------------

- [varLib] Allow feature variations to be active across the entire space (#1957).
- [ufoLib] Added support for ``formatVersionMinor`` in UFO's ``fontinfo.plist`` and for
  ``formatMinor`` attribute in GLIF file as discussed in unified-font-object/ufo-spec#78.
  No changes in reading or writing UFOs until an upcoming (non-0) minor update of the
  UFO specification is published (#1786).
- [merge] Fixed merging fonts with different versions of ``OS/2`` table (#1865, #1952).
- [subset] Fixed ``AttributeError`` while subsetting ``ContextSubst`` and ``ContextPos``
  Format 3 subtable (#1879, #1944).
- [ttLib.table._m_e_t_a] if data happens to be ascii, emit comment in TTX (#1938).
- [feaLib] Support multiple lookups per glyph position (#1905).
- [psCharStrings] Use inheritance to avoid repeated code in initializer (#1932).
- [Doc] Improved documentation for the following modules: ``afmLib`` (#1933), ``agl``
  (#1934), ``cffLib`` (#1935), ``cu2qu`` (#1937), ``encodings`` (#1940), ``feaLib``
  (#1941), ``merge`` (#1949).
- [Doc] Split off developer-centric info to new page, making front page of docs more
  user-focused. List all utilities and sub-modules with brief descriptions.
  Make README more concise and focused (#1914).
- [otlLib] Add function to build STAT table from high-level description (#1926).
- [ttLib._n_a_m_e] Add ``findMultilingualName()`` method (#1921).
- [unicodedata] Update ``RTL_SCRIPTS`` for Unicode 13.0 (#1925).
- [gvar] Sort ``gvar`` XML output by glyph name, not glyph order (#1907, #1908).
- [Doc] Added help options to ``fonttools`` command line tool (#1913, #1920).
  Ensure all fonttools CLI tools have help documentation (#1948).
- [ufoLib] Only write fontinfo.plist when there actually is content (#1911).

4.9.0 (released 2020-04-29)
---------------------------

- [subset] Fixed subsetting of FeatureVariations table. The subsetter no longer drops
  FeatureVariationRecords that have empty substitutions as that will keep the search
  going and thus change the logic. It will only drop empty records that occur at the
  end of the FeatureVariationRecords array (#1881).
- [subset] Remove FeatureVariations table and downgrade GSUB/GPOS to version 0x10000
  when FeatureVariations contain no FeatureVariationRecords after subsetting (#1903).
- [agl] Add support for legacy Adobe Glyph List of glyph names in ``fontTools.agl``
  (#1895).
- [feaLib] Ignore superfluous script statements (#1883).
- [feaLib] Hide traceback by default on ``fonttools feaLib`` command line.
  Use ``--traceback`` option to show (#1898).
- [feaLib] Check lookup index in chaining sub/pos lookups and print better error
  message (#1896, #1897).
- [feaLib] Fix building chained alt substitutions (#1902).
- [Doc] Included all fontTools modules in the sphinx-generated documentation, and
  published it to ReadTheDocs for continuous documentation of the fontTools project
  (#1333). Check it out at https://fonttools.readthedocs.io/. Thanks to Chris Simpkins!
- [transform] The ``Transform`` class is now subclass of ``typing.NamedTuple``. No
  change in functionality (#1904).


4.8.1 (released 2020-04-17)
---------------------------

- [feaLib] Fixed ``AttributeError: 'NoneType' has no attribute 'getAlternateGlyphs'``
  when ``aalt`` feature references a chain contextual substitution lookup
  (googlefonts/fontmake#648, #1878).

4.8.0 (released 2020-04-16)
---------------------------

- [feaLib] If Parser is initialized without a ``glyphNames`` parameter, it cannot
  distinguish between a glyph name containing an hyphen, or a range of glyph names;
  instead of raising an error, it now interprets them as literal glyph names, while
  also outputting a logging warning to alert user about the ambiguity (#1768, #1870).
- [feaLib] When serializing AST to string, emit spaces around hyphens that denote
  ranges. Also, fixed an issue with CID ranges when round-tripping AST->string->AST
  (#1872).
- [Snippets/otf2ttf] In otf2ttf.py script update LSB in hmtx to match xMin (#1873).
- [colorLib] Added experimental support for building ``COLR`` v1 tables as per
  the `colr-gradients-spec <https://github.com/googlefonts/colr-gradients-spec/blob/main/colr-gradients-spec.md>`__
  draft proposal. **NOTE**: both the API and the XML dump of ``COLR`` v1 are
  susceptible to change while the proposal is being discussed and formalized (#1822).

4.7.0 (released 2020-04-03)
---------------------------

- [cu2qu] Added ``fontTools.cu2qu`` package, imported from the original
  `cu2qu <https://github.com/googlefonts/cu2qu>`__ project. The ``cu2qu.pens`` module
  was moved to ``fontTools.pens.cu2quPen``. The optional cu2qu extension module
  can be compiled by installing `Cython <https://cython.org/>`__ before installing
  fonttools from source (i.e. git repo or sdist tarball). The wheel package that
  is published on PyPI (i.e. the one ``pip`` downloads, unless ``--no-binary``
  option is used), will continue to be pure-Python for now (#1868).

4.6.0 (released 2020-03-24)
---------------------------

- [varLib] Added support for building variable ``BASE`` table version 1.1 (#1858).
- [CPAL] Added ``fromRGBA`` method to ``Color`` class (#1861).


4.5.0 (released 2020-03-20)
---------------------------

- [designspaceLib] Added ``add{Axis,Source,Instance,Rule}Descriptor`` methods to
  ``DesignSpaceDocument`` class, to initialize new descriptor objects using keyword
  arguments, and at the same time append them to the current document (#1860).
- [unicodedata] Update to Unicode 13.0 (#1859).

4.4.3 (released 2020-03-13)
---------------------------

- [varLib] Always build ``gvar`` table for TrueType-flavored Variable Fonts,
  even if it contains no variation data. The table is required according to
  the OpenType spec (#1855, #1857).

4.4.2 (released 2020-03-12)
---------------------------

- [ttx] Annotate ``LookupFlag`` in XML dump with comment explaining what bits
  are set and what they mean (#1850).
- [feaLib] Added more descriptive message to ``IncludedFeaNotFound`` error (#1842).

4.4.1 (released 2020-02-26)
---------------------------

- [woff2] Skip normalizing ``glyf`` and ``loca`` tables if these are missing from
  a font (e.g. in NotoColorEmoji using ``CBDT/CBLC`` tables).
- [timeTools] Use non-localized date parsing in ``timestampFromString``, to fix
  error when non-English ``LC_TIME`` locale is set (#1838, #1839).
- [fontBuilder] Make sure the CFF table generated by fontBuilder can be used by varLib
  without having to compile and decompile the table first. This was breaking in
  converting the CFF table to CFF2 due to some unset attributes (#1836).

4.4.0 (released 2020-02-18)
---------------------------

- [colorLib] Added ``fontTools.colorLib.builder`` module, initially with ``buildCOLR``
  and ``buildCPAL`` public functions. More color font formats will follow (#1827).
- [fontBuilder] Added ``setupCOLR`` and ``setupCPAL`` methods (#1826).
- [ttGlyphPen] Quantize ``GlyphComponent.transform`` floats to ``F2Dot14`` to fix
  round-trip issue when computing bounding boxes of transformed components (#1830).
- [glyf] If a component uses reference points (``firstPt`` and ``secondPt``) for
  alignment (instead of X and Y offsets), compute the effective translation offset
  *after* having applied any transform (#1831).
- [glyf] When all glyphs have zero contours, compile ``glyf`` table data as a single
  null byte in order to pass validation by OTS and Windows (#1829).
- [feaLib] Parsing feature code now ensures that referenced glyph names are part of
  the known glyph set, unless a glyph set was not provided.
- [varLib] When filling in the default axis value for a missing location of a source or
  instance, correctly map the value forward.
- [varLib] The avar table can now contain mapping output values that are greater than
  OR EQUAL to the preceeding value, as the avar specification allows this.
- [varLib] The errors of the module are now ordered hierarchically below VarLibError.
  See #1821.

4.3.0 (released 2020-02-03)
---------------------------

- [EBLC/CBLC] Fixed incorrect padding length calculation for Format 3 IndexSubTable
  (#1817, #1818).
- [varLib] Fixed error when merging OTL tables and TTFonts were loaded as ``lazy=True``
  (#1808, #1809).
- [varLib] Allow to use master fonts containing ``CFF2`` table when building VF (#1816).
- [ttLib] Make ``recalcBBoxes`` option work also with ``CFF2`` table (#1816).
- [feaLib] Don't reset ``lookupflag`` in lookups defined inside feature blocks.
  They will now inherit the current ``lookupflag`` of the feature. This is what
  Adobe ``makeotf`` also does in this case (#1815).
- [feaLib] Fixed bug with mixed single/multiple substitutions. If a single substitution
  involved a glyph class, we were incorrectly using only the first glyph in the class
  (#1814).

4.2.5 (released 2020-01-29)
---------------------------

- [feaLib] Do not fail on duplicate multiple substitutions, only warn (#1811).
- [subset] Optimize SinglePos subtables to Format 1 if all ValueRecords are the same
  (#1802).

4.2.4 (released 2020-01-09)
---------------------------

- [unicodedata] Update RTL_SCRIPTS for Unicode 11 and 12.

4.2.3 (released 2020-01-07)
---------------------------

- [otTables] Fixed bug when splitting `MarkBasePos` subtables as offsets overflow.
  The mark class values in the split subtable were not being updated, leading to
  invalid mark-base attachments (#1797, googlefonts/noto-source#145).
- [feaLib] Only log a warning instead of error when features contain duplicate
  substitutions (#1767).
- [glifLib] Strip XML comments when parsing with lxml (#1784, #1785).

4.2.2 (released 2019-12-12)
---------------------------

- [subset] Fixed issue with subsetting FeatureVariations table when the index
  of features changes as features get dropped. The feature index need to be
  remapped to point to index of the remaining features (#1777, #1782).
- [fontBuilder] Added `addFeatureVariations` method to `FontBuilder` class. This
  is a shorthand for calling `featureVars.addFeatureVariations` on the builder's
  TTFont object (#1781).
- [glyf] Fixed the flags bug in glyph.drawPoints() like we did for glyph.draw()
  (#1771, #1774).

4.2.1 (released 2019-12-06)
---------------------------

- [glyf] Use the ``flagOnCurve`` bit mask in ``glyph.draw()``, so that we ignore
  the ``overlap`` flag that may be set when instantiating variable fonts (#1771).

4.2.0 (released 2019-11-28)
---------------------------

- [pens] Added the following pens:

  * ``roundingPen.RoundingPen``: filter pen that rounds coordinates and components'
    offsets to integer;
  * ``roundingPen.RoundingPointPen``: like the above, but using PointPen protocol.
  * ``filterPen.FilterPointPen``: base class for filter point pens;
  * ``transformPen.TransformPointPen``: filter point pen to apply affine transform;
  * ``recordingPen.RecordingPointPen``: records and replays point-pen commands.

- [ttGlyphPen] Always round float coordinates and component offsets to integers
  (#1763).
- [ufoLib] When converting kerning groups from UFO2 to UFO3, avoid confusing
  groups with the same name as one of the glyphs (#1761, #1762,
  unified-font-object/ufo-spec#98).

4.1.0 (released 2019-11-18)
---------------------------

- [instancer] Implemented restricting axis ranges (level 3 partial instancing).
  You can now pass ``{axis_tag: (min, max)}`` tuples as input to the
  ``instantiateVariableFont`` function. Note that changing the default axis
  position is not supported yet. The command-line script also accepts axis ranges
  in the form of colon-separated float values, e.g. ``wght=400:700`` (#1753, #1537).
- [instancer] Never drop STAT ``DesignAxis`` records, but only prune out-of-range
  ``AxisValue`` records.
- [otBase/otTables] Enforce that VarStore.RegionAxisCount == fvar.axisCount, even
  when regions list is empty to appease OTS < v8.0 (#1752).
- [designspaceLib] Defined new ``processing`` attribute for ``<rules>`` element,
  with values "first" or "last", plus other editorial changes to DesignSpace
  specification. Bumped format version to 4.1 (#1750).
- [varLib] Improved error message when masters' glyph orders do not match (#1758,
  #1759).
- [featureVars] Allow to specify custom feature tag in ``addFeatureVariations``;
  allow said feature to already exist, in which case we append new lookup indices
  to existing features. Implemented ``<rules>`` attribute ``processing`` according to
  DesignSpace specification update in #1750. Depending on this flag, we generate
  either an 'rvrn' (always processed first) or a 'rclt' feature (follows lookup order,
  therefore last) (#1747, #1625, #1371).
- [ttCollection] Added support for context manager auto-closing via ``with`` statement
  like with ``TTFont`` (#1751).
- [unicodedata] Require unicodedata2 >= 12.1.0.
- [py2.py3] Removed yet more PY2 vestiges (#1743).
- [_n_a_m_e] Fixed issue when comparing NameRecords with different string types (#1742).
- [fixedTools] Changed ``fixedToFloat`` to not do any rounding but simply return
  ``value / (1 << precisionBits)``. Added ``floatToFixedToStr`` and
  ``strToFixedToFloat`` functions to be used when loading from or dumping to XML.
  Fixed values (e.g. fvar axes and instance coordinates, avar mappings, etc.) are
  are now stored as un-rounded decimal floats upon decompiling (#1740, #737).
- [feaLib] Fixed handling of multiple ``LigatureCaret`` statements for the same glyph.
  Only the first rule per glyph is used, additional ones are ignored (#1733).

4.0.2 (released 2019-09-26)
---------------------------

- [voltLib] Added support for ``ALL`` and ``NONE`` in ``PROCESS_MARKS`` (#1732).
- [Silf] Fixed issue in ``Silf`` table compilation and decompilation regarding str vs
  bytes in python3 (#1728).
- [merge] Handle duplicate glyph names better: instead of appending font index to
  all glyph names, use similar code like we use in ``post`` and ``CFF`` tables (#1729).

4.0.1 (released 2019-09-11)
---------------------------

- [otTables] Support fixing offset overflows in ``MultipleSubst`` lookup subtables
  (#1706).
- [subset] Prune empty strikes in ``EBDT`` and ``CBDT`` table data (#1698, #1633).
- [pens] Fixed issue in ``PointToSegmentPen`` when last point of closed contour has
  same coordinates as the starting point and was incorrectly dropped (#1720).
- [Graphite] Fixed ``Sill`` table output to pass OTS (#1705).
- [name] Added ``removeNames`` method to ``table__n_a_m_e`` class (#1719).
- [ttLib] Added aliases for renamed entries ``ascender`` and ``descender`` in
  ``hhea`` table (#1715).

4.0.0 (released 2019-08-22)
---------------------------

- NOTE: The v4.x version series only supports Python 3.6 or greater. You can keep
  using fonttools 3.x if you need support for Python 2.
- [py23] Removed all the python2-only code since it is no longer reachable, thus
  unused; only the Python3 symbols were kept, but these are no-op. The module is now
  DEPRECATED and will removed in the future.
- [ttLib] Fixed UnboundLocalError for empty loca/glyph tables (#1680). Also, allow
  the glyf table to be incomplete when dumping to XML (#1681).
- [varLib.models] Fixed KeyError while sorting masters and there are no on-axis for
  a given axis (38a8eb0e).
- [cffLib] Make sure glyph names are unique (#1699).
- [feaLib] Fix feature parser to correctly handle octal numbers (#1700).

\... see `here <https://github.com/fonttools/fonttools/blob/main/NEWS.rst>`__ for earlier changes
