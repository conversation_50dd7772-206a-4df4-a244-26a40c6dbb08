# Extract and organize downloaded CV files

$OutputPath = ".\test_cvs"
$tempPath = "$OutputPath\temp"

Write-Host "Extracting downloaded CV files..." -ForegroundColor Green

# Extract the curriculum_vitae_data.zip
$zipPath = "$tempPath\curriculum_vitae_data.zip"
if (Test-Path $zipPath) {
    Write-Host "Found curriculum_vitae_data.zip, extracting..." -ForegroundColor Yellow
    
    try {
        Expand-Archive -Path $zipPath -DestinationPath $tempPath -Force
        
        # Find the extracted folder
        $extractedFolder = Get-ChildItem $tempPath -Directory | Where-Object { $_.Name -like "*curriculum*" } | Select-Object -First 1
        
        if ($extractedFolder) {
            Write-Host "Found extracted folder: $($extractedFolder.Name)" -ForegroundColor Cyan
            
            # Copy PDF files
            $pdfSource = Join-Path $extractedFolder.FullName "pdf"
            if (Test-Path $pdfSource) {
                $pdfFiles = Get-ChildItem $pdfSource -Filter "*.pdf"
                Write-Host "Found $($pdfFiles.Count) PDF files" -ForegroundColor Cyan
                
                foreach ($file in $pdfFiles) {
                    $destPath = Join-Path "$OutputPath\pdf_cvs" $file.Name
                    Copy-Item $file.FullName $destPath -Force
                }
                Write-Host "Copied PDF files to pdf_cvs folder" -ForegroundColor Green
            }
            
            # Copy DOCX files
            $docxSource = Join-Path $extractedFolder.FullName "word"
            if (Test-Path $docxSource) {
                $docxFiles = Get-ChildItem $docxSource -Filter "*.docx"
                Write-Host "Found $($docxFiles.Count) DOCX files" -ForegroundColor Cyan
                
                foreach ($file in $docxFiles) {
                    $destPath = Join-Path "$OutputPath\docx_cvs" $file.Name
                    Copy-Item $file.FullName $destPath -Force
                }
                Write-Host "Copied DOCX files to docx_cvs folder" -ForegroundColor Green
            }
        }
    }
    catch {
        Write-Host "Error extracting ZIP: $_" -ForegroundColor Red
    }
}

# Now download the second repository
Write-Host "`nDownloading resume-dataset..." -ForegroundColor Yellow
try {
    $repo2Url = "https://github.com/juanfpinzon/resume-dataset/archive/refs/heads/master.zip"
    $repo2Zip = "$tempPath\resume-dataset.zip"
    
    Invoke-WebRequest -Uri $repo2Url -OutFile $repo2Zip -UseBasicParsing
    
    if (Test-Path $repo2Zip) {
        Write-Host "Downloaded resume-dataset.zip" -ForegroundColor Cyan
        
        Expand-Archive -Path $repo2Zip -DestinationPath $tempPath -Force
        
        $extractedFolder2 = Get-ChildItem $tempPath -Directory | Where-Object { $_.Name -like "*resume*" } | Select-Object -First 1
        
        if ($extractedFolder2) {
            $jsonFiles = Get-ChildItem $extractedFolder2.FullName -Filter "*.json"
            Write-Host "Found $($jsonFiles.Count) JSON files" -ForegroundColor Cyan
            
            foreach ($file in $jsonFiles) {
                $destPath = Join-Path "$OutputPath\json_datasets" $file.Name
                Copy-Item $file.FullName $destPath -Force
            }
            Write-Host "Copied JSON files to json_datasets folder" -ForegroundColor Green
        }
    }
}
catch {
    Write-Host "Error downloading resume-dataset: $_" -ForegroundColor Red
}

# Show results
Write-Host "`nFinal Results:" -ForegroundColor Green
$pdfCount = (Get-ChildItem "$OutputPath\pdf_cvs" -File).Count
$docxCount = (Get-ChildItem "$OutputPath\docx_cvs" -File).Count
$jsonCount = (Get-ChildItem "$OutputPath\json_datasets" -File).Count

Write-Host "PDF files: $pdfCount" -ForegroundColor Cyan
Write-Host "DOCX files: $docxCount" -ForegroundColor Cyan
Write-Host "JSON files: $jsonCount" -ForegroundColor Cyan
Write-Host "Total: $($pdfCount + $docxCount + $jsonCount) files" -ForegroundColor Green

Write-Host "`nDone! You can now test your CV analyzer with these files." -ForegroundColor Green 