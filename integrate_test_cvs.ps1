# PowerShell Script to Integrate Downloaded CVs into CV Analyzer Project
# This script copies test CVs to the project's media folder

param(
    [string]$SourcePath = ".\test_cvs",
    [string]$ProjectMediaPath = ".\media\cvs",
    [int]$MaxFilesToCopy = 20,
    [switch]$CreateBackup = $true
)

Write-Host "🔄 Integrating Test CVs into CV Analyzer Project..." -ForegroundColor Green
Write-Host "📁 Source: $SourcePath" -ForegroundColor Yellow
Write-Host "📁 Destination: $ProjectMediaPath" -ForegroundColor Yellow

# Check if source directory exists
if (-not (Test-Path $SourcePath)) {
    Write-Host "❌ Source directory not found: $SourcePath" -ForegroundColor Red
    Write-Host "💡 Please run the download script first!" -ForegroundColor Yellow
    exit 1
}

# Create project media directory if it doesn't exist
if (-not (Test-Path $ProjectMediaPath)) {
    New-Item -ItemType Directory -Path $ProjectMediaPath -Force | Out-Null
    Write-Host "📂 Created project media directory: $ProjectMediaPath" -ForegroundColor Cyan
}

# Create backup if requested
if ($CreateBackup -and (Get-ChildItem $ProjectMediaPath -File).Count -gt 0) {
    $backupPath = "$ProjectMediaPath\backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    New-Item -ItemType Directory -Path $backupPath -Force | Out-Null
    
    Get-ChildItem $ProjectMediaPath -File | ForEach-Object {
        Copy-Item $_.FullName $backupPath -Force
    }
    Write-Host "💾 Created backup at: $backupPath" -ForegroundColor Cyan
}

$copiedFiles = 0
$totalAvailable = 0

# Function to copy files with limit
function Copy-CVFiles {
    param(
        [string]$SourceDir,
        [string]$FilePattern,
        [string]$FileType
    )
    
    if (-not (Test-Path $SourceDir)) {
        return 0
    }
    
    $files = Get-ChildItem $SourceDir -Filter $FilePattern | Select-Object -First ($MaxFilesToCopy - $script:copiedFiles)
    $script:totalAvailable += (Get-ChildItem $SourceDir -Filter $FilePattern).Count
    
    $filesCopied = 0
    foreach ($file in $files) {
        if ($script:copiedFiles -ge $MaxFilesToCopy) {
            break
        }
        
        try {
            $destPath = Join-Path $ProjectMediaPath $file.Name
            Copy-Item $file.FullName $destPath -Force
            $script:copiedFiles++
            $filesCopied++
        }
        catch {
            Write-Host "❌ Error copying $($file.Name): $_" -ForegroundColor Red
        }
    }
    
    if ($filesCopied -gt 0) {
        Write-Host "📄 Copied $filesCopied $FileType files" -ForegroundColor Cyan
    }
    
    return $filesCopied
}

# Copy PDF files
Write-Host "`n📄 Processing PDF files..." -ForegroundColor Green
$pdfPath = Join-Path $SourcePath "pdf_cvs"
Copy-CVFiles -SourceDir $pdfPath -FilePattern "*.pdf" -FileType "PDF"

# Copy DOCX files (only if we haven't reached the limit)
if ($copiedFiles -lt $MaxFilesToCopy) {
    Write-Host "`n📝 Processing DOCX files..." -ForegroundColor Green
    $docxPath = Join-Path $SourcePath "docx_cvs"
    Copy-CVFiles -SourceDir $docxPath -FilePattern "*.docx" -FileType "DOCX"
}

# Create a test manifest file
$manifestContent = @"
Test CV Integration Manifest
============================
Date: $(Get-Date)
Source: $SourcePath
Destination: $ProjectMediaPath
Max Files Limit: $MaxFilesToCopy

Files Copied: $copiedFiles
Total Available: $totalAvailable

File Types:
- PDF: $(if (Test-Path (Join-Path $SourcePath "pdf_cvs")) { (Get-ChildItem (Join-Path $SourcePath "pdf_cvs") -Filter "*.pdf").Count } else { 0 }) available
- DOCX: $(if (Test-Path (Join-Path $SourcePath "docx_cvs")) { (Get-ChildItem (Join-Path $SourcePath "docx_cvs") -Filter "*.docx").Count } else { 0 }) available

Integration Notes:
- Files copied to project media folder for immediate testing
- Original test dataset preserved in: $SourcePath
- $(if ($CreateBackup) { "Backup created for existing files" } else { "No backup created" })

Next Steps:
1. Start your Django development server
2. Navigate to the CV upload page
3. Test the analyzer with these sample CVs
4. Monitor the analysis results and performance
"@

$manifestPath = Join-Path $ProjectMediaPath "test_integration_manifest.txt"
$manifestContent | Out-File -FilePath $manifestPath -Encoding UTF8

# Display results
Write-Host "`n📊 INTEGRATION SUMMARY" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host "📁 Project Media Path: $ProjectMediaPath" -ForegroundColor Yellow
Write-Host "📄 Files Copied: $copiedFiles" -ForegroundColor Cyan
Write-Host "📄 Total Available: $totalAvailable" -ForegroundColor Cyan
Write-Host "📝 Manifest: $manifestPath" -ForegroundColor Gray

if ($copiedFiles -lt $totalAvailable) {
    Write-Host "`n💡 Note: Only $copiedFiles of $totalAvailable files were copied (limit: $MaxFilesToCopy)" -ForegroundColor Yellow
    Write-Host "   To copy more files, increase the -MaxFilesToCopy parameter" -ForegroundColor Gray
}

# List files in the project media directory
Write-Host "`n📂 Project Media Directory Contents:" -ForegroundColor Green
$mediaFiles = Get-ChildItem $ProjectMediaPath -File
if ($mediaFiles.Count -gt 0) {
    $mediaFiles | ForEach-Object {
        $size = [math]::Round($_.Length / 1KB, 1)
        Write-Host "  $($_.Name) ($size KB)" -ForegroundColor Cyan
    }
}
else {
    Write-Host "  No files found" -ForegroundColor Gray
}

Write-Host "`n✅ Integration complete!" -ForegroundColor Green
Write-Host "🎯 You can now test your CV analyzer with these sample files!" -ForegroundColor Yellow
Write-Host "🚀 Start your Django server and navigate to the upload page to begin testing." -ForegroundColor Yellow 