upstream cv_analyzer {
    server web:8000;
}

server {
    listen 80;
    server_name localhost;
    client_max_body_size 10M;

    # Security headers
    add_header X-Frame-Options "DENY" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self' https: data: 'unsafe-inline' 'unsafe-eval';" always;

    # Gzip compression
    gzip on;
    gzip_proxied any;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_vary on;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;

    # Static files
    location /static/ {
        alias /app/static/;
        expires 30d;
        access_log off;
        add_header Cache-Control "public, no-transform";
    }

    # Media files
    location /media/ {
        alias /app/media/;
        expires 30d;
        access_log off;
        add_header Cache-Control "public, no-transform";
    }

    # Proxy configuration
    location / {
        proxy_pass http://cv_analyzer;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_redirect off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_read_timeout 90;
        proxy_connect_timeout 90;
        proxy_send_timeout 90;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket configuration for real-time updates
    location /ws/ {
        proxy_pass http://cv_analyzer;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }

    # Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Deny access to backup files
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Basic logging configuration
    access_log /var/log/nginx/cv_analyzer_access.log combined buffer=512k flush=1m;
    error_log /var/log/nginx/cv_analyzer_error.log warn;
} 