#!/usr/bin/env python3
"""
Quick test simulating complete browser flow with CSRF cookies
"""

import os
import django
import requests
import json
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cv_analyzer_project.settings')
django.setup()

def quick_test():
    print("=== Quick API Test (Full Browser Simulation) ===")
    
    # Create a session to maintain cookies like a browser
    session = requests.Session()
    
    # Step 1: Get the page first to establish session and get CSRF cookie
    print("📄 Getting page to establish session...")
    page_response = session.get("http://127.0.0.1:8000/vacancy/9/candidates/")
    
    if page_response.status_code != 200:
        print(f"❌ Failed to get page: {page_response.status_code}")
        return False
    
    print(f"✅ Page loaded successfully")
    
    # Step 2: Extract CSRF token from cookies
    csrf_token = None
    for cookie in session.cookies:
        if cookie.name == 'csrftoken':
            csrf_token = cookie.value
            break
    
    if not csrf_token:
        print("❌ No CSRF token found in cookies")
        return False
    
    print(f"🔑 CSRF token extracted: {csrf_token[:20]}...")
    
    # Step 3: Make the API request with proper headers
    url = "http://127.0.0.1:8000/api/start-ai-analysis/"
    
    payload = {
        "cv_ids": [15, 14],  # Just 2 CVs for speed test
        "vacancy_ids": [9],
        "analysis_type": "ai"  # AI analysis to force processing
    }
    
    headers = {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrf_token,
        'Referer': 'http://127.0.0.1:8000/vacancy/9/candidates/',
        'Accept': 'application/json',
    }
    
    print(f"📤 Testing FAST analysis with 2 CVs...")
    start_time = time.time()
    
    try:
        response = session.post(url, json=payload, headers=headers, timeout=180)  # 3 minutes
        end_time = time.time()
        
        print(f"⏱️ Analysis completed in {end_time - start_time:.1f}s")
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: {result.get('successful', 0)}/{result.get('total_processed', 0)} processed")
            
            # Show some results
            results = result.get('results', [])
            for res in results:
                cv_id = res.get('cv_id')
                status = res.get('status')
                score = res.get('score', 'N/A')
                method = res.get('method', 'N/A')
                print(f"   - CV {cv_id}: {status} ({method} - {score}%)")
                
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == "__main__":
    quick_test() 