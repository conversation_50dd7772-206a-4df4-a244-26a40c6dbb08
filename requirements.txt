# CV Analyzer - Enterprise Production Requirements

# Core Django and Web Framework
Django>=4.2.0,<5.0
djangorestframework>=3.14.0
django-cors-headers>=4.0.0
django-widget-tweaks>=1.5.0
djangorestframework-simplejwt>=5.2.0

# Database and ORM
psycopg2-binary>=2.9.0  # PostgreSQL adapter
dj-database-url>=2.0.0

# Caching and Queue Management
redis>=4.5.0
celery>=5.3.0
django-redis>=5.2.0

# File Processing and Analysis
pdfplumber>=0.11.0
openpyxl>=3.1.0
python-docx>=0.8.11  # DOCX file reading
PyPDF2>=3.0.1  # Alternative PDF reader
Pillow>=9.5.0
python-magic-bin>=0.4.14  # File type detection

# AI and Machine Learning
openai>=1.3.0
pocketgroq>=0.5.0
scikit-learn>=1.3.0
pandas>=2.0.0
numpy>=1.24.0

# Security and Authentication
cryptography>=41.0.0
PyJWT>=2.8.0
argon2-cffi>=23.1.0  # Password hashing
python-decouple>=3.8  # Environment variables

# Monitoring and Logging
sentry-sdk[django]>=1.32.0
django-debug-toolbar>=4.2.0
structlog>=23.1.0

# API Documentation
drf-yasg>=1.21.0  # Swagger/OpenAPI

# Development and Testing
pytest>=7.4.0
pytest-django>=4.5.0
pytest-cov>=4.1.0
pytest-asyncio>=0.21.0
factory-boy>=3.3.0  # Test data generation
coverage>=7.3.0

# Production Server
gunicorn>=21.2.0
whitenoise>=6.5.0  # Static files serving

# AWS and Cloud Services
boto3>=1.28.0  # AWS SDK
django-storages>=1.14.0  # Cloud storage backends

# Data Processing and Analytics
scipy>=1.11.0
joblib>=1.3.0
matplotlib>=3.7.0  # For chart generation
seaborn>=0.12.0  # Statistical visualization

# Utilities
requests>=2.31.0
python-dateutil>=2.8.0
pytz>=2023.3
chardet>=5.2.0  # Character encoding detection
validators>=0.22.0  # Data validation

# Development Tools (Optional)
black>=23.7.0  # Code formatting
flake8>=6.1.0  # Code linting
isort>=5.12.0  # Import sorting
pre-commit>=3.4.0  # Git hooks

# Documentation
Sphinx>=7.1.0  # Documentation generation
sphinx-rtd-theme>=1.3.0

# Web Scraping and HTML Processing (if needed)
beautifulsoup4>=4.12.0
html2text>=2020.1.16

# WebSockets (for real-time features)
channels>=4.0.0
channels-redis>=4.1.0

# Performance Monitoring
django-silk>=5.0.0  # Performance profiling

# Email
django-email-extras>=0.3.4

# Internationalization
django-rosetta>=0.9.9  # Translation management

# Task Scheduling
django-crontab>=0.7.1

# File Upload Progress
# django-uploadify>=1.1.0  # Package not available on PyPI

# Environment and Configuration
python-dotenv>=1.0.0

# Background Tasks
django-rq>=2.8.0  # Alternative to Celery
rq>=1.15.0

# Compression
django-compressor>=4.4.0

# Rate Limiting
django-ratelimit>=4.1.0

# CORS and Security Headers
django-security>=0.12.0

# Model Utilities
django-model-utils>=4.3.0
django-extensions>=3.2.0

# Timezone Support
django-timezone-field>=6.0.0

# Health Checks
django-health-check>=3.17.0

# Admin Enhancements
django-admin-sortable2>=2.1.0
django-grappelli>=3.0.0  # Admin interface

# Testing and Quality
bandit>=1.7.0  # Security testing
safety>=2.3.0  # Dependency vulnerability scanning