@echo off
echo.
echo ============================================
echo  CV Dataset Download Script
echo ============================================
echo.
echo This script will download sample CVs for testing your CV analyzer
echo.

REM Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PowerShell is not available or not in PATH
    echo Please ensure PowerShell is installed and accessible
    pause
    exit /b 1
)

echo Starting PowerShell script...
echo.

REM Run the PowerShell script
powershell -ExecutionPolicy Bypass -File "download_sample_cvs.ps1" -OutputPath ".\test_cvs"

echo.
echo ============================================
echo  Download process completed!
echo ============================================
echo.
echo Check the 'test_cvs' folder for downloaded files
echo.
pause 