function testConnection(configId) {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', `/admin/cv_analyzer/aiapiconfig/test-connection/${configId}/`, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            const response = JSON.parse(xhr.responseText);
            if (xhr.status === 200) {
                if (response.success) {
                    alert('✅ ' + response.message);
                } else {
                    alert('❌ Connection failed: ' + response.error);
                }
            } else {
                alert('❌ Error: ' + (response.error || 'Unknown error'));
            }
        }
    };
    
    xhr.send();
}

function fetchModels(configId) {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', `/admin/cv_analyzer/aiapiconfig/fetch-models/${configId}/`, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            const response = JSON.parse(xhr.responseText);
            if (xhr.status === 200 && response.success) {
                const modelField = document.getElementById('id_model_name');
                if (modelField) {
                    // Clear existing options
                    modelField.innerHTML = '<option value="">Select a model...</option>';
                    
                    // Add new options
                    response.models.forEach(function(model) {
                        const option = document.createElement('option');
                        option.value = model;
                        option.text = model;
                        modelField.appendChild(option);
                    });
                    
                    alert(`✅ Found ${response.models.length} models. Please select one from the dropdown.`);
                } else {
                    alert('✅ Models fetched: ' + response.models.join(', '));
                }
            } else {
                alert('❌ Failed to fetch models: ' + (response.error || 'Unknown error'));
            }
        }
    };
    
    xhr.send();
}

// Add event listeners when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add fetch models button for existing configurations
    const modelNameField = document.getElementById('id_model_name');
    if (modelNameField && window.location.pathname.includes('/change/')) {
        const configId = window.location.pathname.split('/').slice(-3, -2)[0];
        if (configId && !isNaN(configId)) {
            const fetchButton = document.createElement('button');
            fetchButton.type = 'button';
            fetchButton.className = 'button';
            fetchButton.innerHTML = '🔄 Fetch Available Models';
            fetchButton.onclick = function() { fetchModels(configId); };
            
            modelNameField.parentNode.appendChild(fetchButton);
        }
    }
    
    // Add guidance text based on provider selection
    const providerField = document.getElementById('id_provider');
    if (providerField) {
        const guidanceDiv = document.createElement('div');
        guidanceDiv.id = 'provider-guidance';
        guidanceDiv.style.marginTop = '10px';
        guidanceDiv.style.padding = '10px';
        guidanceDiv.style.backgroundColor = '#f0f8ff';
        guidanceDiv.style.border = '1px solid #ccc';
        guidanceDiv.style.borderRadius = '4px';
        
        function updateGuidance() {
            const provider = providerField.value;
            let guidance = '';
            
            switch(provider) {
                case 'openai':
                    guidance = '<strong>OpenAI Configuration:</strong><br>• API Key: Enter your OpenAI API key<br>• Model: e.g., gpt-3.5-turbo, gpt-4';
                    break;
                case 'groq':
                    guidance = '<strong>Groq Configuration:</strong><br>• API Key: Enter your Groq API key<br>• Model: e.g., llama-3.1-70b-versatile, mixtral-8x7b-32768';
                    break;
                case 'ollama':
                    guidance = '<strong>Ollama Configuration:</strong><br>• API Key: Enter server URL (e.g., http://*************:11434) or just IP (*************), or leave blank for default server<br>• Model: Use "Fetch Available Models" button to see available models<br>• Recommended: qwen-qwq-32b for CV analysis<br>• Note: Make sure Ollama server is running';
                    break;
                default:
                    guidance = 'Select a provider to see configuration guidance.';
            }
            
            guidanceDiv.innerHTML = guidance;
        }
        
        providerField.addEventListener('change', updateGuidance);
        providerField.parentNode.appendChild(guidanceDiv);
        updateGuidance(); // Initial call
    }
}); 