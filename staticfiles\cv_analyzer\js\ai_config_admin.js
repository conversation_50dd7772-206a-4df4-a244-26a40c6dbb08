(function($) {
    $(document).ready(function() {
        var $apiConfigField = $('#id_api_config');
        var $inlineFormset = $('.inline-group');

        $apiConfigField.change(function() {
            var selectedConfigId = $(this).val();
            if (selectedConfigId) {
                $.ajax({
                    url: '/admin/fetch-api-config/',
                    method: 'POST',
                    data: {
                        config_id: selectedConfigId,
                        csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val()
                    },
                    success: function(data) {
                        updateInlineForm(data);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching API config:', error);
                    }
                });
            } else {
                clearInlineForm();
            }
        });

        function updateInlineForm(data) {
            var $form = $inlineFormset.find('.inline-related:not(.empty-form)');
            $form.find('[name$="-provider"]').val(data.provider);
            $form.find('[name$="-api_key"]').val(data.api_key);
            $form.find('[name$="-api_url"]').val(data.api_url);
            $form.find('[name$="-model_name"]').val(data.model_name);
            $form.find('[name$="-is_active"]').prop('checked', data.is_active);
            $form.find('[name$="-priority"]').val(data.priority);
        }

        function clearInlineForm() {
            var $form = $inlineFormset.find('.inline-related:not(.empty-form)');
            $form.find('input, select').val('');
            $form.find('input[type="checkbox"]').prop('checked', false);
        }
    });
})(django.jQuery);