// Unified Dashboard JavaScript Functions

// Global variables
let currentAnalysisType = '';
let uploadProgressInterval = null;
let analysisProgressInterval = null;

// Tab switching functionality
function switchTab(tabName) {
    // Hide all tab panes
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected tab pane
    const targetTab = document.getElementById(tabName + '-tab');
    if (targetTab) {
        targetTab.classList.add('active');
    }
    
    // Add active class to clicked button
    if (event && event.target) {
        event.target.classList.add('active');
    }
}

// Detail tab switching
function switchDetailTab(tabName) {
    // Hide all detail tab panes
    document.querySelectorAll('.detail-tab-pane').forEach(pane => {
        pane.style.display = 'none';
    });
    
    // Remove active class from all detail tab buttons
    document.querySelectorAll('.analysis-tabs .tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected detail tab pane
    const targetTab = document.getElementById(tabName + '-detail-tab');
    if (targetTab) {
        targetTab.style.display = 'block';
    }
    
    // Add active class to clicked button
    if (event && event.target) {
        event.target.classList.add('active');
    }
}

// Popup functionality
function openPopup(popupId) {
    const popup = document.getElementById(popupId);
    if (popup) {
        popup.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function closePopup(popupId) {
    const popup = document.getElementById(popupId);
    if (popup) {
        popup.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// Close popup when clicking outside
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('popup-overlay')) {
        event.target.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
});

// Escape key to close popups
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const visiblePopups = document.querySelectorAll('.popup-overlay[style*="block"]');
        visiblePopups.forEach(popup => {
            popup.style.display = 'none';
        });
        document.body.style.overflow = 'auto';
    }
});

// Upload functionality
function switchUploadMethod(method) {
    // Hide all upload methods
    document.querySelectorAll('.upload-method').forEach(el => {
        el.style.display = 'none';
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.upload-methods .tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected method
    const targetMethod = document.getElementById(method + '-upload');
    if (targetMethod) {
        targetMethod.style.display = 'block';
    }
    
    // Add active class to clicked tab
    if (event && event.target) {
        event.target.classList.add('active');
    }
}

// Analysis functionality
function selectAnalysisType(type) {
    currentAnalysisType = type;
    
    // Hide all analysis types
    document.querySelectorAll('.analysis-type').forEach(el => {
        el.style.display = 'none';
    });
    
    // Remove selected class from all option cards
    document.querySelectorAll('.option-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Show selected analysis type
    const targetType = document.getElementById(type + '-analysis');
    if (targetType) {
        targetType.style.display = 'block';
    }
    
    // Add selected class to clicked card
    if (event && event.target) {
        const card = event.target.closest('.option-card');
        if (card) {
            card.classList.add('selected');
        }
    }
}

// Management functionality
function switchManageTab(tabName) {
    // Hide all manage sections
    document.querySelectorAll('.manage-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // Remove active class from all manage tab buttons
    document.querySelectorAll('.manage-tabs .tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected manage section
    const targetSection = document.getElementById(tabName + '-manage');
    if (targetSection) {
        targetSection.style.display = 'block';
    }
    
    // Add active class to clicked button
    if (event && event.target) {
        event.target.classList.add('active');
    }
}

// Form management
function showAddCompanyForm() {
    const form = document.getElementById('addCompanyForm');
    if (form) {
        form.classList.remove('hidden');
    }
}

function hideAddCompanyForm() {
    const form = document.getElementById('addCompanyForm');
    if (form) {
        form.classList.add('hidden');
        form.querySelector('form').reset();
    }
}

function showAddVacancyForm() {
    const form = document.getElementById('addVacancyForm');
    if (form) {
        form.classList.remove('hidden');
    }
}

function hideAddVacancyForm() {
    const form = document.getElementById('addVacancyForm');
    if (form) {
        form.classList.add('hidden');
        form.querySelector('form').reset();
    }
}

// CV Detail functions
function viewCV(cvId) {
    // Show loading state
    const popup = document.getElementById('cvDetailPopup');
    const content = document.getElementById('cvDetailContent');
    
    content.innerHTML = `
        <div class="loading-state text-center py-8">
            <i class="fas fa-spinner fa-spin text-4xl text-blue-600 mb-4"></i>
            <p>Loading CV details...</p>
        </div>
    `;
    
    openPopup('cvDetailPopup');
    
    // Fetch CV details as JSON
    fetch(`/cv/${cvId}/detail/json/`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            populateCVDetailPopup(data);
        })
        .catch(error => {
            content.innerHTML = `
                <div class="error-state text-center py-8">
                    <i class="fas fa-exclamation-triangle text-4xl text-red-600 mb-4"></i>
                    <p>Failed to load CV details: ${error.message}</p>
                    <button class="btn btn-primary mt-4" onclick="viewCV(${cvId})">Retry</button>
                </div>
            `;
        });
}

function populateCVDetailPopup(data) {
    // Get the template and clone it
    const template = document.getElementById('cvDetailTemplate');
    const content = document.getElementById('cvDetailContent');
    
    if (!template) {
        content.innerHTML = '<div class="error-state text-center py-8"><p>Template not found</p></div>';
        return;
    }
    
    // Clone the template content
    const templateContent = template.content.cloneNode(true);
    
    // Helper function to set field value
    function setField(selector, value, isHtml = false) {
        const element = templateContent.querySelector(`[data-field="${selector}"]`);
        if (element) {
            if (isHtml) {
                element.innerHTML = value || '--';
            } else {
                element.textContent = value || '--';
            }
        }
    }
    
    // Helper function to set status badge
    function setStatusBadge(value) {
        const element = templateContent.querySelector('[data-field="status"]');
        if (element) {
            element.textContent = value || '--';
            element.className = 'status-badge';
            
            // Add appropriate styling based on status
            if (value && value.toLowerCase().includes('analyzed')) {
                element.classList.add('analyzed');
            } else if (value && value.toLowerCase().includes('processing')) {
                element.classList.add('processing');
            } else {
                element.classList.add('uploaded');
            }
        }
    }
    
    // Helper function to populate skills/languages lists
    function populateList(selector, items, className) {
        const container = templateContent.querySelector(`[data-field="${selector}"]`);
        if (container && Array.isArray(items)) {
            if (items.length > 0) {
                container.innerHTML = items.map(item => 
                    `<span class="${className}">${item}</span>`
                ).join('');
            } else {
                container.innerHTML = '<span class="text-gray-500">None specified</span>';
            }
        }
    }
    
    // Helper function to format analysis text
    function formatAnalysisText(text) {
        if (!text || text === 'CV has not been analyzed yet. Please run analysis to see detailed results.') {
            return '<div class="text-center text-gray-500 py-4"><i class="fas fa-info-circle mr-2"></i>CV has not been analyzed yet. Please run analysis to see detailed results.</div>';
        }
        
        // Convert markdown-style formatting to HTML
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>');
    }
    
    // Helper function to format additional info
    function formatAdditionalInfo(text) {
        if (!text || text === 'No additional information available.') {
            return '<div class="text-center text-gray-500 py-4"><i class="fas fa-info-circle mr-2"></i>No additional information available.</div>';
        }
        
        return '<div class="space-y-2">' + text
            .replace(/\*\*(.*?)\*\*/g, '<div class="font-medium text-gray-900 dark:text-white">$1</div>')
            .replace(/\n\n/g, '</div><div class="space-y-2">')
            .replace(/\n/g, '<br>') + '</div>';
    }
    
    // Helper function to populate AI response data
    function populateAIResponseData(templateContent, aiData) {
        // Helper function to set AI field value
        function setAIField(selector, value, isHtml = false) {
            const element = templateContent.querySelector(`[data-field="${selector}"]`);
            if (element && value !== undefined) {
                if (isHtml) {
                    element.innerHTML = value || '--';
                } else {
                    element.textContent = value || '--';
                }
            }
        }
        
        // Populate AI metadata
        setAIField('ai_provider', aiData.ai_provider);
        setAIField('model_used', aiData.model_used);
        setAIField('cv_text_length', aiData.cv_text_length);
        setAIField('parsing_method', aiData.parsing_method);
        
        // Format processing time
        if (aiData.processed_at) {
            const processedDate = new Date(aiData.processed_at);
            setAIField('processed_at', processedDate.toLocaleString());
        }
        
        // Populate raw AI response (with character count)
        const rawResponse = aiData.raw_ai_response || 'No raw response available';
        setAIField('raw_ai_response', rawResponse);
        
        // Add character count indicator
        const rawResponseElement = templateContent.querySelector('[data-field="raw_ai_response"]');
        if (rawResponseElement && rawResponse !== 'No raw response available') {
            const charCount = document.createElement('div');
            charCount.className = 'text-xs text-gray-500 mt-2';
            charCount.textContent = `Response length: ${rawResponse.length} characters`;
            rawResponseElement.parentNode.appendChild(charCount);
        }
        
        // Populate prompt used
        setAIField('prompt_used', aiData.prompt_used || 'No prompt information available');
        
        // Populate response lines
        const responseLinesContainer = templateContent.querySelector('[data-field="response_lines"]');
        if (responseLinesContainer && aiData.response_lines && Array.isArray(aiData.response_lines)) {
            responseLinesContainer.innerHTML = '';
            
            aiData.response_lines.forEach((line, index) => {
                const lineDiv = document.createElement('div');
                lineDiv.className = 'response-line';
                
                const lineNumber = document.createElement('span');
                lineNumber.className = 'response-line-number';
                lineNumber.textContent = `${index + 1}:`;
                
                const lineContent = document.createElement('span');
                lineContent.className = 'response-line-content';
                lineContent.textContent = line || '(empty line)';
                
                lineDiv.appendChild(lineNumber);
                lineDiv.appendChild(lineContent);
                responseLinesContainer.appendChild(lineDiv);
            });
        } else if (responseLinesContainer) {
            responseLinesContainer.innerHTML = '<div class="text-gray-500">No response lines available</div>';
        }
    }
    
    // Populate basic information
    setField('candidate_name', data.candidate_name);
    setField('email', data.email);
    setField('phone', data.phone);
    setField('location', data.location);
    setField('years_of_experience', data.years_of_experience);
    setField('education_level', data.education_level);
    setField('uploaded_at', data.uploaded_at);
    setField('analyzed_at', data.analyzed_at);
    setField('source', data.source);
    
    // Set status badge
    setStatusBadge(data.status);
    
    // Populate scores
    setField('overall_score', data.overall_score);
    setField('content_score', data.content_score);
    setField('format_score', data.format_score);
    setField('sections_score', data.sections_score);
    setField('skills_score', data.skills_score);
    setField('style_score', data.style_score);
    
    // Populate preferences
    setField('preferred_job_type', data.preferred_job_type);
    setField('preferred_work_location', data.preferred_work_location);
    setField('salary_expectation', data.salary_expectation);
    setField('availability', data.availability);
    
    // Populate skills and languages
    populateList('skills_list', data.skills_list, 'skill-tag');
    populateList('languages_list', data.languages_list, 'language-tag');
    
    // Populate detailed analysis
    setField('content_analysis', formatAnalysisText(data.content_analysis), true);
    setField('format_analysis', formatAnalysisText(data.format_analysis), true);
    setField('sections_analysis', formatAnalysisText(data.sections_analysis), true);
    setField('skills_analysis', formatAnalysisText(data.skills_analysis), true);
    setField('style_analysis', formatAnalysisText(data.style_analysis), true);
    
    // Populate additional information
    setField('additional_info', formatAdditionalInfo(data.additional_info), true);
    
    // Populate AI response data if available
    if (data.ai_response_data) {
        console.log('AI Response Data found:', data.ai_response_data);
        populateAIResponseData(templateContent, data.ai_response_data);
        
        // Make sure AI Analysis section is visible
        const aiSection = templateContent.querySelector('.ai-analysis-section');
        if (aiSection) {
            aiSection.style.display = 'block';
        }
    } else {
        console.log('No AI Response Data available');
    }
    
    // Store CV ID for action buttons
    templateContent.querySelectorAll('.action-btn').forEach(btn => {
        btn.setAttribute('data-cv-id', data.cv_id);
    });
    
    // Replace content
    content.innerHTML = '';
    content.appendChild(templateContent);
}

// Action button handlers
function reanalyzeCV() {
    const button = event.target.closest('.action-btn');
    const cvId = button ? button.getAttribute('data-cv-id') : null;
    
    if (!cvId) {
        alert('CV ID not found');
        return;
    }
    
    if (confirm('Are you sure you want to re-analyze this CV?')) {
        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Analyzing...';
        button.disabled = true;
        
        fetch(`/cv/${cvId}/analyze/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Analysis completed successfully!');
                closePopup('cvDetailPopup');
                location.reload();
            } else {
                alert('Analysis failed: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            alert('Analysis failed: ' + error.message);
        })
        .finally(() => {
            button.innerHTML = '<i class="fas fa-redo mr-2"></i>Re-analyze CV';
            button.disabled = false;
        });
    }
}

function matchToJobs() {
    const button = event.target.closest('.action-btn');
    const cvId = button ? button.getAttribute('data-cv-id') : null;
    
    if (!cvId) {
        alert('CV ID not found');
        return;
    }
    
    window.location.href = `/cv/${cvId}/match/`;
}

function downloadCV() {
    const button = event.target.closest('.action-btn');
    const cvId = button ? button.getAttribute('data-cv-id') : null;
    
    if (!cvId) {
        alert('CV ID not found');
        return;
    }
    
    // This would need to be implemented to get the actual file URL
    alert('Download functionality will be implemented soon');
}

function shareCV() {
    const button = event.target.closest('.action-btn');
    const cvId = button ? button.getAttribute('data-cv-id') : null;
    
    if (!cvId) {
        alert('CV ID not found');
        return;
    }
    
    const shareUrl = `${window.location.origin}/cv/${cvId}/detail/`;
    
    if (navigator.share) {
        navigator.share({
            title: 'CV Details',
            text: 'Check out this CV analysis',
            url: shareUrl
        });
    } else {
        navigator.clipboard.writeText(shareUrl).then(() => {
            alert('Link copied to clipboard!');
        }).catch(() => {
            alert('Failed to copy link');
        });
    }
}

function addNotes() {
    const button = event.target.closest('.action-btn');
    const cvId = button ? button.getAttribute('data-cv-id') : null;
    
    if (!cvId) {
        alert('CV ID not found');
        return;
    }
    
    const notes = prompt('Add notes for this CV:');
    if (notes !== null && notes.trim() !== '') {
        // This would need to be implemented to save notes
        alert('Notes functionality will be implemented soon');
    }
}

function archiveCV() {
    const button = event.target.closest('.action-btn');
    const cvId = button ? button.getAttribute('data-cv-id') : null;
    
    if (!cvId) {
        alert('CV ID not found');
        return;
    }
    
    if (confirm('Are you sure you want to archive this CV?')) {
        // This would need to be implemented
        alert('Archive functionality will be implemented soon');
    }
}

function viewVacancy(vacancyId) {
    // Similar implementation for vacancy details
    openPopup('vacancyPopup');
    console.log('Loading vacancy details for ID:', vacancyId);
}

function viewCompany(companyId) {
    // Similar implementation for company details
    openPopup('companyPopup');
    console.log('Loading company details for ID:', companyId);
}

// Analysis functions
function startSingleAnalysis() {
    const cvSelect = document.getElementById('singleCVSelect');
    const cvId = cvSelect.value;
    
    if (!cvId) {
        showNotification('Please select a CV to analyze', 'warning');
        return;
    }
    
    // Show progress
    showAnalysisProgress();
    
    // Start analysis
    fetch(`/cv/${cvId}/analyze/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        // Ensure progress reaches 100% before showing result
        setTimeout(() => {
            if (data.success) {
                showNotification('Analysis completed successfully!', 'success');
                setTimeout(() => {
                    closePopup('analyzePopup');
                    location.reload();
                }, 1500);
            } else {
                showNotification('Analysis failed: ' + (data.error || 'Unknown error'), 'error');
                hideAnalysisProgress();
            }
        }, 1000);
    })
    .catch(error => {
        setTimeout(() => {
            showNotification('Analysis failed: ' + error.message, 'error');
            hideAnalysisProgress();
        }, 1000);
    });
}

function startBatchAnalysis() {
    const selectedCVs = Array.from(document.querySelectorAll('input[name="batch_cvs"]:checked')).map(cb => cb.value);
    
    if (selectedCVs.length === 0) {
        showNotification('Please select at least one CV to analyze', 'warning');
        return;
    }
    
    // Show progress
    showAnalysisProgress();
    
    // Update progress subtitle for batch analysis
    const progressSubtitle = document.getElementById('progressSubtitle');
    if (progressSubtitle) {
        progressSubtitle.textContent = `Analyzing ${selectedCVs.length} CVs...`;
    }
    
    // Start batch analysis
    fetch('/api/batch-analyze/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            cv_ids: selectedCVs
        })
    })
    .then(response => response.json())
    .then(data => {
        setTimeout(() => {
            if (data.success) {
                showNotification(`Batch analysis started for ${selectedCVs.length} CVs!`, 'success');
                setTimeout(() => {
                    closePopup('analyzePopup');
                    location.reload();
                }, 1500);
            } else {
                showNotification('Batch analysis failed: ' + (data.error || 'Unknown error'), 'error');
                hideAnalysisProgress();
            }
        }, 1000);
    })
    .catch(error => {
        setTimeout(() => {
            showNotification('Batch analysis failed: ' + error.message, 'error');
            hideAnalysisProgress();
        }, 1000);
    });
}

function showAnalysisProgress() {
    // Hide all analysis types
    document.querySelectorAll('.analysis-type').forEach(el => {
        el.style.display = 'none';
    });
    
    // Show progress
    const progress = document.getElementById('analysisProgress');
    if (progress) {
        progress.style.display = 'block';
        // Start the progress simulation
        startProgressSimulation();
    }
}

function hideAnalysisProgress() {
    const progress = document.getElementById('analysisProgress');
    if (progress) {
        progress.style.display = 'none';
        // Reset progress state
        resetProgressState();
    }
}

// Progress simulation with realistic steps
function startProgressSimulation() {
    const steps = [
        { percentage: 0, status: "Starting...", subtitle: "Initializing AI analysis...", step: 1 },
        { percentage: 8, status: "Preparing...", subtitle: "Setting up AI models...", step: 1 },
        { percentage: 15, status: "Processing...", subtitle: "Uploading CV data...", step: 1 },
        { percentage: 22, status: "Validating...", subtitle: "Validating document format...", step: 1 },
        { percentage: 30, status: "Extracting...", subtitle: "Extracting text from CV...", step: 2 },
        { percentage: 38, status: "Parsing...", subtitle: "Parsing document structure...", step: 2 },
        { percentage: 45, status: "Identifying...", subtitle: "Identifying key sections...", step: 2 },
        { percentage: 52, status: "Processing...", subtitle: "Processing structured data...", step: 2 },
        { percentage: 60, status: "Analyzing...", subtitle: "Running AI analysis...", step: 3 },
        { percentage: 68, status: "Learning...", subtitle: "Applying machine learning models...", step: 3 },
        { percentage: 75, status: "Evaluating...", subtitle: "Evaluating skills and experience...", step: 3 },
        { percentage: 82, status: "Calculating...", subtitle: "Computing compatibility metrics...", step: 4 },
        { percentage: 85, status: "Scoring...", subtitle: "Calculating compatibility scores...", step: 4 },
        { percentage: 92, status: "Optimizing...", subtitle: "Optimizing recommendation results...", step: 4 },
        { percentage: 95, status: "Finalizing...", subtitle: "Preparing comprehensive report...", step: 4 },
        { percentage: 98, status: "Formatting...", subtitle: "Formatting analysis results...", step: 5 },
        { percentage: 100, status: "Complete!", subtitle: "Analysis completed successfully!", step: 5 }
    ];
    
    let currentIndex = 0;
    
    const progressInterval = setInterval(() => {
        if (currentIndex < steps.length) {
            const currentStep = steps[currentIndex];
            updateProgress(currentStep.percentage, currentStep.status, currentStep.subtitle, currentStep.step);
            currentIndex++;
        } else {
            clearInterval(progressInterval);
            // Keep showing the complete state for a moment
            setTimeout(() => {
                // The actual API response will handle hiding this
                // or you can add additional logic here
            }, 2000); // Show complete state for 2 seconds
        }
    }, 1200); // Update every 1200ms (1.2 seconds) for longer, more visible timing
    
    // Store interval ID for potential cancellation
    window.analysisProgressInterval = progressInterval;
}

function updateProgress(percentage, status, subtitle, activeStep) {
    // Update progress bar
    const progressFill = document.getElementById('progressFill');
    if (progressFill) {
        progressFill.style.width = percentage + '%';
    }
    
    // Update percentage text
    const progressPercentage = document.getElementById('progressPercentage');
    if (progressPercentage) {
        progressPercentage.textContent = percentage + '%';
    }
    
    // Update status text
    const progressStatus = document.getElementById('progressStatus');
    if (progressStatus) {
        progressStatus.textContent = status;
    }
    
    // Update subtitle
    const progressSubtitle = document.getElementById('progressSubtitle');
    if (progressSubtitle) {
        progressSubtitle.textContent = subtitle;
    }
    
    // Update step indicators
    updateStepIndicators(activeStep, percentage);
}

function updateStepIndicators(activeStep, percentage) {
    // Reset all steps
    for (let i = 1; i <= 5; i++) {
        const step = document.getElementById(`step${i}`);
        if (step) {
            step.classList.remove('active', 'completed');
            
            if (i < activeStep || (i === activeStep && percentage === 100 && activeStep === 5)) {
                step.classList.add('completed');
            } else if (i === activeStep) {
                step.classList.add('active');
            }
        }
    }
}

function resetProgressState() {
    // Clear any running intervals
    if (window.analysisProgressInterval) {
        clearInterval(window.analysisProgressInterval);
        window.analysisProgressInterval = null;
    }
    
    // Reset progress elements
    const progressFill = document.getElementById('progressFill');
    const progressPercentage = document.getElementById('progressPercentage');
    const progressStatus = document.getElementById('progressStatus');
    const progressSubtitle = document.getElementById('progressSubtitle');
    
    if (progressFill) progressFill.style.width = '0%';
    if (progressPercentage) progressPercentage.textContent = '0%';
    if (progressStatus) progressStatus.textContent = 'Starting...';
    if (progressSubtitle) progressSubtitle.textContent = 'Initializing AI analysis...';
    
    // Reset all step indicators
    for (let i = 1; i <= 5; i++) {
        const step = document.getElementById(`step${i}`);
        if (step) {
            step.classList.remove('active', 'completed');
            if (i === 1) {
                step.classList.add('active');
            }
        }
    }
}

function cancelAnalysis() {
    if (confirm('Are you sure you want to cancel the analysis?')) {
        hideAnalysisProgress();
        // Show the analysis options again
        document.querySelectorAll('.analysis-type').forEach(el => {
            el.style.display = 'none';
        });
        // Show the main analysis options
        const analysisOptions = document.querySelector('.analysis-options');
        if (analysisOptions) {
            analysisOptions.style.display = 'block';
        }
    }
}

// Utility functions
function selectAllCVs() {
    document.querySelectorAll('input[name="batch_cvs"]').forEach(cb => {
        cb.checked = true;
    });
}

function clearAllCVs() {
    document.querySelectorAll('input[name="batch_cvs"]').forEach(cb => {
        cb.checked = false;
    });
}

function generateReport(reportType) {
    console.log('Generating report:', reportType);
    
    // Show configuration section
    const config = document.getElementById('reportConfig');
    if (config) {
        config.style.display = 'block';
    }
    
    // Highlight selected report card
    document.querySelectorAll('.report-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    if (event && event.target) {
        const card = event.target.closest('.report-card');
        if (card) {
            card.classList.add('selected');
        }
    }
}

// Integration functions
function connectGoogleDrive() {
    alert('Google Drive integration not yet implemented');
}

function connectOneDrive() {
    alert('OneDrive integration not yet implemented');
}

function setupEmailIntegration() {
    alert('Email integration setup not yet implemented');
}

function setupFolderMonitoring() {
    alert('Folder monitoring setup not yet implemented');
}

// Bulk operations
function bulkAnalyze() {
    alert('Bulk analyze not yet implemented');
}

function bulkMatch() {
    alert('Bulk match not yet implemented');
}

function exportData() {
    alert('Export data not yet implemented');
}

function cleanup() {
    if (confirm('Are you sure you want to start cleanup? This will remove old and inactive records.')) {
        alert('Cleanup not yet implemented');
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('Unified Dashboard initialized');
    
    // Initialize any charts or dynamic content here
    initializeCharts();
    
    // Create notification container if it doesn't exist
    createNotificationContainer();
});

function initializeCharts() {
    // Chart initialization will be handled by the template's Chart.js code
    console.log('Charts initialization delegated to template');
}

// Notification system
function createNotificationContainer() {
    if (!document.getElementById('notificationContainer')) {
        const container = document.createElement('div');
        container.id = 'notificationContainer';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        `;
        document.body.appendChild(container);
    }
}

function showNotification(message, type = 'info', duration = 4000) {
    const container = document.getElementById('notificationContainer');
    if (!container) return;
    
    const notification = document.createElement('div');
    notification.style.cssText = `
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        margin-bottom: 10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        pointer-events: auto;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    
    const icon = type === 'success' ? '✓' : type === 'error' ? '✗' : type === 'warning' ? '⚠' : 'ℹ';
    notification.innerHTML = `<strong>${icon}</strong> ${message}`;
    
    container.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, duration);
}

// AI Section toggle function for CV detail popup
function toggleAISection(sectionId) {
    const content = document.getElementById(sectionId + '-content');
    const icon = document.getElementById(sectionId + '-icon');
    
    if (content && icon) {
        if (content.classList.contains('hidden')) {
            content.classList.remove('hidden');
            content.classList.add('show');
            icon.classList.add('rotated');
        } else {
            content.classList.add('hidden');
            content.classList.remove('show');
            icon.classList.remove('rotated');
        }
    }
}

// Quick Actions functions for CV detail popup
function reanalyzeCV() {
    const cvId = document.querySelector('.action-btn')?.getAttribute('data-cv-id');
    if (!cvId) {
        showNotification('CV ID not found', 'error');
        return;
    }
    
    fetch(`/cv/${cvId}/analyze/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('CV re-analysis started successfully', 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showNotification(data.message || 'Analysis failed', 'error');
        }
    })
    .catch(error => {
        showNotification('Error starting analysis', 'error');
        console.error('Analysis error:', error);
    });
}

function matchToJobs() {
    const cvId = document.querySelector('.action-btn')?.getAttribute('data-cv-id');
    if (!cvId) {
        showNotification('CV ID not found', 'error');
        return;
    }
    window.location.href = `/cv/${cvId}/match/`;
}

function addNotes() {
    showNotification('Add notes functionality coming soon', 'info');
}

function archiveCV() {
    if (confirm('Are you sure you want to archive this CV?')) {
        showNotification('Archive functionality coming soon', 'info');
    }
}

function downloadCV() {
    const cvId = document.querySelector('.action-btn')?.getAttribute('data-cv-id');
    if (!cvId) {
        showNotification('CV ID not found', 'error');
        return;
    }
    // TODO: Implement download functionality
    showNotification('Download functionality coming soon', 'info');
}

function shareCV() {
    const cvId = document.querySelector('.action-btn')?.getAttribute('data-cv-id');
    if (!cvId) {
        showNotification('CV ID not found', 'error');
        return;
    }
    
    if (navigator.share) {
        navigator.share({
            title: 'CV Analysis',
            text: 'Check out this CV analysis',
            url: window.location.href
        });
    } else {
        // Fallback: copy URL to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            showNotification('Link copied to clipboard', 'success');
        });
    }
}