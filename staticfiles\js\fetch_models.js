(function($) {
    $(document).ready(function() {
        var $modelNameField = $('#id_model_name');
        var $providerField = $('#id_provider');
        var $apiKeyField = $('#id_api_key');
        var $apiUrlField = $('#id_api_url');

        $('<button type="button" class="button" id="fetch-models">Fetch Models</button>').insertAfter($modelNameField);

        $('#fetch-models').click(function(e) {
            e.preventDefault();
            fetchModels();
        });

        function fetchModels() {
            var provider = $providerField.val();
            var apiKey = $apiKeyField.val();
            var apiUrl = $apiUrlField.val();

            $.ajax({
                url: '/admin/fetch-models/',
                method: 'POST',
                data: {
                    provider: provider,
                    api_key: apiKey,
                    api_url: apiUrl,
                    csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val()
                },
                success: function(data) {
                    $modelNameField.empty();
                    $.each(data.models, function(index, model) {
                        $modelNameField.append($('<option></option>').attr('value', model).text(model));
                    });
                    $modelNameField.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    var errorMessage = xhr.responseJSON && xhr.responseJSON.error ? xhr.responseJSON.error : 'Unknown error occurred';
                    alert('Error fetching models: ' + errorMessage);
                }
            });
        }
    });
})(django.jQuery);