#!/usr/bin/env python

import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cv_analyzer_project.settings')
django.setup()

from cv_analyzer.models import CV, Vacancy, AIAPIConfig
from cv_analyzer.views import calculate_detailed_compatibility

def test_ai_analysis():
    print("=== AI Analysis Test ===")
    
    # Check AI configuration
    print("\n1. Checking AI Configuration...")
    ai_configs = AIAPIConfig.objects.all()
    active_configs = AIAPIConfig.objects.filter(is_active=True)
    
    print(f"Total AI configs: {ai_configs.count()}")
    print(f"Active AI configs: {active_configs.count()}")
    
    if active_configs.exists():
        config = active_configs.first()
        print(f"Active config: {config.provider} - {config.model_name}")
        print(f"API key configured: {'Yes' if config.api_key else 'No'}")
    else:
        print("❌ No active AI configuration found!")
        return
    
    # Get test CV and vacancy
    print("\n2. Getting test CV and vacancy...")
    cv = CV.objects.first()
    vacancy = Vacancy.objects.get(id=9)  # The vacancy we're testing with
    
    if not cv:
        print("❌ No CV found!")
        return
        
    print(f"Testing CV: {cv.id}")
    print(f"Testing Vacancy: {vacancy.id} - {vacancy.title}")
    
    # Test AI analysis
    print("\n3. Testing AI analysis...")
    try:
        import time
        start_time = time.time()
        
        result = calculate_detailed_compatibility(cv, vacancy)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ AI Analysis successful!")
        print(f"Duration: {duration:.1f} seconds")
        print(f"Compatibility score: {result['compatibility_score']}%")
        print(f"Recommendation: {result['recommendation_level']}")
        print(f"Analysis text: {result['analysis_text'][:100]}...")
        
    except Exception as e:
        print(f"❌ AI Analysis failed: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        
        # Try to get more details
        import traceback
        print("\nFull error traceback:")
        traceback.print_exc()

if __name__ == "__main__":
    test_ai_analysis() 