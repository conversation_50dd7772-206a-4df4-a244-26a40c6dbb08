#!/usr/bin/env python3
"""
Test AI analysis with console logging
"""

import os
import django
import requests
import json
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cv_analyzer_project.settings')
django.setup()

def test_ai_analysis():
    """Test AI analysis with console output"""
    print("=== Testing AI Analysis with Console Logging ===\n")
    
    session = requests.Session()
    
    # Get page to establish session
    print("📄 Getting vacancy page...")
    page_response = session.get("http://127.0.0.1:8000/vacancy/9/candidates/")
    
    if page_response.status_code != 200:
        print(f"❌ Failed to get page: {page_response.status_code}")
        return False
    
    # Extract CSRF token
    csrf_token = session.cookies.get('csrftoken')
    if not csrf_token:
        print("❌ No CSRF token found")
        return False
    
    print(f"🔐 CSRF token: {csrf_token[:20]}...")
    
    # Test AI analysis (will show detailed console output)
    print("\n🤖 Testing AI Analysis...")
    print("💡 Watch the console for detailed emoji-based progress logging!")
    
    payload = {
        "cv_ids": [15],  # Just 1 CV for demo
        "vacancy_ids": [9],
        "analysis_type": "ai"  # This should trigger detailed AI analysis
    }
    
    headers = {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrf_token,
        'Referer': 'http://127.0.0.1:8000/vacancy/9/candidates/'
    }
    
    print(f"📤 Sending AI analysis request...")
    print(f"🎯 Expected to see detailed console logs with emojis...")
    start_time = time.time()
    
    response = session.post(
        "http://127.0.0.1:8000/api/start-ai-analysis/",
        json=payload,
        headers=headers,
        timeout=120  # 2 minutes for AI analysis
    )
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n📥 Response received in {duration:.1f}s")
    print(f"📊 Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Success: {result.get('success', False)}")
        print(f"📈 Results: {result.get('total_processed', 0)} processed, {result.get('successful', 0)} successful")
        
        if result.get('results'):
            for res in result['results']:
                cv_id = res.get('cv_id')
                score = res.get('score', 0)
                method = res.get('method', 'Unknown')
                print(f"   🎯 CV {cv_id}: {score}% compatibility ({method} analysis)")
        
        return True
    else:
        print(f"❌ Request failed: {response.status_code}")
        return False

if __name__ == "__main__":
    print("🚀 Starting AI analysis test...")
    print("👀 Make sure to watch the Django server console for detailed logging!")
    print("📺 You should see emoji progress like: 🤖🚀📄✅ etc.\n")
    
    success = test_ai_analysis()
    if success:
        print(f"\n🎉 AI analysis test completed!")
        print(f"📊 Check the server console above for detailed progress logs")
    else:
        print(f"\n❌ AI analysis test failed!") 