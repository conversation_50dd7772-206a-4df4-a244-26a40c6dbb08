#!/usr/bin/env python3
"""
Basic test for Arabic processing functionality.
"""

import sys
sys.path.append('.')

def test_arabic_processing():
    """Test basic Arabic processing functionality."""
    try:
        from cv_analyzer.arabic_processing import ArabicTextProcessor
        print('✓ ArabicTextProcessor imported successfully')
        
        processor = ArabicTextProcessor()
        
        # Test Arabic text
        arabic_text = 'مرحبا، اسمي أحمد محمد وأنا مهندس برمجيات'
        result = processor.detect_language(arabic_text)
        print(f'✓ Arabic detection: is_arabic={result["is_arabic"]}, confidence={result["confidence"]:.2f}')
        
        # Test normalization
        normalized = processor.normalize_arabic_text(arabic_text)
        print(f'✓ Normalization works: {len(normalized)} chars')
        
        # Test entity extraction
        entities = processor.extract_arabic_entities('اسمي أحمد، إيميلي <EMAIL>، هاتف 123456789')
        print(f'✓ Entity extraction: emails={len(entities["emails"])}, phones={len(entities["phones"])}')
        
        # Test English text
        english_text = 'Hello, my name is <PERSON>'
        result_en = processor.detect_language(english_text)
        print(f'✓ English detection: is_arabic={result_en["is_arabic"]}, language={result_en["language"]}')
        
        print('✓ All basic tests passed!')
        return True
        
    except Exception as e:
        print(f'✗ Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_arabic_processing()
    sys.exit(0 if success else 1)
