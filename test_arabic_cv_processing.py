#!/usr/bin/env python3
"""
Test Arabic CV processing functionality.
"""

import sys
sys.path.append('.')

def test_arabic_cv_processing():
    """Test Arabic CV processing functionality."""
    try:
        from cv_analyzer.arabic_cv_utils_simple import ArabicCVProcessor
        print('✓ ArabicCVProcessor imported successfully')
        
        processor = ArabicCVProcessor()
        
        # Test with sample Arabic CV text
        arabic_cv_text = """
        السيرة الذاتية
        الاسم: أحمد محمد علي
        البريد الإلكتروني: <EMAIL>
        الهاتف: +966501234567
        
        الخبرة المهنية:
        - مهندس برمجيات في شركة التقنية المتقدمة (2020-2023)
        - مطور تطبيقات في شركة الحلول الذكية (2018-2020)
        
        المهارات:
        - البرمجة: Python, JavaScript, Java
        - قواعد البيانات: MySQL, PostgreSQL
        - الأطر: <PERSON><PERSON><PERSON>, React, Spring Boot
        
        التعليم:
        - بكالوريوس علوم الحاسوب - جامعة الملك سعود (2014-2018)
        """
        
        # Test CV processing
        result = processor.process_arabic_cv(arabic_cv_text)
        print(f'✓ CV processing: success={result["success"]}, language_detected={result.get("language_detected", "unknown")}')
        
        if result['success']:
            extracted_data = result.get('extracted_data', {})
            print(f'✓ Extracted data: name_found={bool(extracted_data.get("name"))}, email_found={bool(extracted_data.get("email"))}, phone_found={bool(extracted_data.get("phone"))}')
            
            # Test skills extraction
            skills = extracted_data.get('skills', [])
            print(f'✓ Skills extracted: {len(skills)} skills found')
            
            # Test experience extraction
            experience = extracted_data.get('experience', [])
            print(f'✓ Experience extracted: {len(experience)} positions found')
        
        print('✓ All Arabic CV processing tests passed!')
        return True
        
    except Exception as e:
        print(f'✗ Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_arabic_cv_processing()
    sys.exit(0 if success else 1)
