#!/usr/bin/env python3
"""
Test Arabic CV job matching functionality.
"""

import sys
sys.path.append('.')

def test_arabic_job_matching():
    """Test Arabic CV job matching functionality."""
    try:
        from cv_analyzer.arabic_cv_utils_simple import ArabicCVProcessor
        print('✓ ArabicCVProcessor imported successfully')
        
        processor = ArabicCVProcessor()
        
        # Test with sample Arabic CV text
        arabic_cv_text = """
        السيرة الذاتية
        الاسم: أحمد محمد علي
        البريد الإلكتروني: <EMAIL>
        الهاتف: +966501234567
        
        الخبرة المهنية:
        - مهندس برمجيات في شركة التقنية المتقدمة (2020-2023)
        - مطور تطبيقات في شركة الحلول الذكية (2018-2020)
        
        المهارات:
        - البرمجة: <PERSON>, Java<PERSON>, Java
        - قواعد البيانات: MySQL, PostgreSQL
        - الأطر: <PERSON><PERSON><PERSON>, React, Spring Boot
        
        التعليم:
        - بكالوريوس علوم الحاسوب - جامعة الملك سعود (2014-2018)
        """
        
        # Test job description
        job_description = """
        مطلوب مطور برمجيات
        المهارات المطلوبة:
        - Python
        - Django
        - JavaScript
        - React
        - MySQL
        - Git
        - Docker
        
        الخبرة: 3-5 سنوات في تطوير تطبيقات الويب
        """
        
        # Test job matching
        result = processor.analyze_arabic_cv_for_job(arabic_cv_text, job_description)
        print(f'✓ Job matching: success={result["success"]}')
        
        if result['success']:
            compatibility = result.get('compatibility', {})
            score = compatibility.get('score', 0)
            matching_skills = compatibility.get('matching_skills', [])
            missing_skills = compatibility.get('missing_skills', [])
            
            print(f'✓ Compatibility score: {score}%')
            print(f'✓ Matching skills: {len(matching_skills)} found - {matching_skills}')
            print(f'✓ Missing skills: {len(missing_skills)} found - {missing_skills}')
        
        # Test batch processing
        cv_texts = [arabic_cv_text, arabic_cv_text.replace('أحمد', 'فاطمة')]
        batch_results = processor.batch_process_arabic_cvs(cv_texts)
        print(f'✓ Batch processing: {len(batch_results)} CVs processed')
        
        successful_results = [r for r in batch_results if r.get('success', False)]
        print(f'✓ Successful processing: {len(successful_results)}/{len(batch_results)} CVs')
        
        print('✓ All Arabic job matching tests passed!')
        return True
        
    except Exception as e:
        print(f'✗ Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_arabic_job_matching()
    sys.exit(0 if success else 1)
