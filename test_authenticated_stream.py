#!/usr/bin/env python3
"""
Test streaming with proper authentication
"""

import requests
import json
import time

def test_authenticated_stream():
    """Test streaming with authentication"""
    
    print("🔐 Testing Authenticated Streaming")
    print("=" * 40)
    
    session = requests.Session()
    
    # Step 1: Get login page and CSRF token
    login_page = session.get("http://127.0.0.1:8000/accounts/login/")
    if login_page.status_code != 200:
        print(f"❌ Failed to get login page: {login_page.status_code}")
        return False
    
    # Extract CSRF token from login page
    csrf_token = None
    for line in login_page.text.split('\n'):
        if 'name="csrfmiddlewaretoken"' in line:
            csrf_token = line.split('value="')[1].split('"')[0]
            break
    
    if not csrf_token:
        print("❌ Could not find CSRF token in login page")
        return False
    
    print(f"✅ CSRF token from login page: {csrf_token[:20]}...")
    
    # Step 2: Login (you'll need valid credentials)
    login_data = {
        'username': 'admin',  # You may need to change this
        'password': 'admin123',  # You may need to change this  
        'csrfmiddlewaretoken': csrf_token
    }
    
    login_response = session.post("http://127.0.0.1:8000/accounts/login/", 
                                 data=login_data,
                                 headers={'Referer': 'http://127.0.0.1:8000/accounts/login/'})
    
    print(f"📝 Login response status: {login_response.status_code}")
    print(f"📝 Login response URL: {login_response.url}")
    
    # Check if login was successful (should redirect away from login page)
    if 'login' in login_response.url:
        print("❌ Login failed - still on login page")
        print("💡 You may need to create a superuser: python manage.py createsuperuser")
        return False
    
    print("✅ Login successful!")
    
    # Step 3: Now test the streaming API
    # Get fresh CSRF token for API calls
    csrf_token = session.cookies.get('csrftoken')
    
    payload = {
        "cv_ids": [10, 9, 15],
        "vacancy_ids": [9],
        "analysis_type": "fast",
        "stream_mode": True
    }
    
    headers = {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrf_token,
        'Accept': 'text/event-stream',
        'Referer': 'http://127.0.0.1:8000/vacancy/9/candidates/'
    }
    
    print(f"\n📡 Making authenticated streaming request...")
    print(f"📋 Payload: {payload}")
    
    try:
        response = session.post('http://127.0.0.1:8000/api/start-ai-analysis/', 
                              json=payload, headers=headers, stream=True)
        
        print(f"📡 Response status: {response.status_code}")
        print(f"📡 Content-Type: {response.headers.get('Content-Type')}")
        print(f"📡 URL: {response.url}")
        
        if response.status_code == 200 and 'text/event-stream' in response.headers.get('Content-Type', ''):
            print("✅ Streaming response received!")
            print("📺 Reading events...\n")
            
            buffer = ""
            event_count = 0
            
            # Read streaming events with timeout
            start_time = time.time()
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    buffer += chunk
                    
                    lines = buffer.split('\n')
                    buffer = lines.pop()
                    
                    for line in lines:
                        if line.strip() == '':
                            continue
                        
                        if line.startswith('data: '):
                            try:
                                event_data = json.loads(line[6:])
                                event_count += 1
                                print(f"📡 Event #{event_count}: {event_data['type']}")
                                
                                if event_data['type'] == 'complete':
                                    print("🎉 Stream completed successfully!")
                                    return True
                                    
                            except json.JSONDecodeError as e:
                                print(f"❌ JSON decode error: {e}")
                
                # Timeout after 60 seconds
                if time.time() - start_time > 60:
                    print("⏰ Test timeout after 60 seconds")
                    break
            
            print(f"📊 Total events received: {event_count}")
            return event_count > 0
            
        else:
            print(f"❌ Non-streaming response:")
            print(f"   Status: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('Content-Type')}")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_authenticated_stream()
    if success:
        print("\n✅ Authenticated streaming test PASSED!")
    else:
        print("\n❌ Authenticated streaming test FAILED!")
        print("💡 Try creating a superuser if needed:")
        print("   python manage.py createsuperuser") 