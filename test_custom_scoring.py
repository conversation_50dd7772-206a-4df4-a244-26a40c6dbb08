#!/usr/bin/env python
"""
Test script for Custom Scoring System
Tests the implementation of the weighted scoring functionality
"""

import os
import sys
import django
from django.conf import settings

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cv_analyzer_project.settings')
django.setup()

from cv_analyzer.models import (
    CV, CVAnalysis, Vacancy, Company, 
    VacancyScoringRules, ScoringTemplate, CustomScoringResult
)
from cv_analyzer.custom_scoring import CustomScoringEngine
from django.contrib.auth.models import User
import json


def test_custom_scoring():
    """Test the custom scoring system"""
    print("🚀 Testing Custom Scoring System\n")
    
    # Test 1: Create a scoring template
    print("1. Creating scoring template...")
    template, created = ScoringTemplate.objects.get_or_create(
        name="Software Developer Template",
        defaults={
            'category': 'Technology',
            'description': 'Scoring template for software development positions',
            'education_weight': 20.0,
            'experience_weight': 40.0,
            'skills_weight': 35.0,
            'responsibilities_weight': 5.0,
            'education_requirements': {
                'preferred_levels': ['bachelor', 'master'],
                'required_fields': ['computer science', 'software engineering', 'information technology']
            },
            'experience_requirements': {
                'minimum_years': 2,
                'preferred_years': 5,
                'relevant_industries': ['technology', 'software', 'fintech']
            },
            'skills_requirements': {
                'required_skills': ['python', 'javascript', 'sql'],
                'preferred_skills': ['react', 'django', 'aws', 'docker'],
                'minimum_match_percentage': 60
            },
            'responsibilities_requirements': {
                'key_responsibilities': ['development', 'testing', 'debugging', 'code review']
            }
        }
    )
    print(f"   ✓ Template created: {template.name}")
    
    # Test 2: Get or create a test vacancy
    print("\n2. Setting up test vacancy...")
    company, _ = Company.objects.get_or_create(
        name="Tech Solutions Inc.",
        defaults={'email': '<EMAIL>'}
    )
    
    vacancy, _ = Vacancy.objects.get_or_create(
        title="Senior Python Developer",
        defaults={
            'company': company,
            'description': 'Looking for an experienced Python developer with Django experience',
            'requirements': 'Bachelor degree in Computer Science, 3+ years Python experience',
            'location': 'Remote',
            'salary_range': '80000-120000',
            'status': 'active'
        }
    )
    print(f"   ✓ Vacancy created: {vacancy.title}")
    
    # Test 3: Apply template to vacancy
    print("\n3. Applying template to vacancy...")
    scoring_rules, _ = VacancyScoringRules.objects.get_or_create(
        vacancy=vacancy,
        defaults={
            'education_weight': template.education_weight,
            'experience_weight': template.experience_weight,
            'skills_weight': template.skills_weight,
            'responsibilities_weight': template.responsibilities_weight,
            'education_requirements': template.education_requirements,
            'experience_requirements': template.experience_requirements,
            'skills_requirements': template.skills_requirements,
            'responsibilities_requirements': template.responsibilities_requirements,
        }
    )
    print(f"   ✓ Scoring rules applied to vacancy")
    
    # Test 4: Find an existing CV analysis or create a mock one
    print("\n4. Finding CV analysis for testing...")
    cv_analysis = CVAnalysis.objects.first()
    
    if not cv_analysis:
        print("   ⚠️  No CV analysis found. Creating mock analysis...")
        # Create a mock CV analysis for testing
        from django.core.files.base import ContentFile
        
        user, _ = User.objects.get_or_create(username='testuser')
        
        # Create a simple CV file
        cv_content = b"John Doe\nPython Developer\nSkills: Python, JavaScript, SQL, Django, React\nExperience: 4 years\nEducation: Bachelor in Computer Science"
        cv_file = ContentFile(cv_content, name='test_cv.txt')
        
        cv = CV.objects.create(
            file=cv_file,
            uploaded_by=user,
            source='local'
        )
        
        cv_analysis = CVAnalysis.objects.create(
            cv=cv,
            full_name="John Doe",
            education_level="Bachelor in Computer Science",
            skills="Python, JavaScript, SQL, Django, React",
            years_of_experience=4,
            overall_score=75.0,
            content_score=80.0,
            format_score=70.0,
            skills_score=85.0,
            style_score=65.0
        )
    
    print(f"   ✓ Using CV analysis: {cv_analysis.full_name or 'ID ' + str(cv_analysis.id)}")
    
    # Test 5: Calculate custom score
    print("\n5. Calculating custom score...")
    engine = CustomScoringEngine()
    
    try:
        result = engine.calculate_custom_score(cv_analysis, vacancy)
        print(f"   ✓ Custom score calculated successfully!")
        print(f"   📊 Results:")
        print(f"      • Final Score: {result.final_custom_score:.1f}%")
        print(f"      • Education: {result.education_score:.1f}%")
        print(f"      • Experience: {result.experience_score:.1f}%")
        print(f"      • Skills: {result.skills_score:.1f}%")
        print(f"      • Responsibilities: {result.responsibilities_score:.1f}%")
        print(f"      • Recommendation: {result.custom_recommendation}")
        print(f"      • Meets Requirements: {result.meets_minimum_requirements}")
        
        if hasattr(cv_analysis, 'overall_score') and cv_analysis.overall_score:
            print(f"      • AI vs Custom Difference: {result.ai_vs_custom_difference:.1f}%")
        
        # Test 6: Display score breakdown
        print("\n6. Score breakdown:")
        breakdown = result.score_breakdown
        if breakdown:
            for component, details in breakdown.items():
                if isinstance(details, dict):
                    print(f"   • {component.title()}:")
                    for key, value in details.items():
                        print(f"     - {key}: {value}")
        
        # Test 7: Test scoring template functionality
        print("\n7. Testing scoring template functionality...")
        templates = ScoringTemplate.objects.filter(is_active=True)
        print(f"   ✓ Found {templates.count()} active templates")
        
        for template in templates[:3]:  # Show first 3
            print(f"   • {template.name} ({template.category})")
            total_weight = (template.education_weight + template.experience_weight + 
                          template.skills_weight + template.responsibilities_weight)
            print(f"     Weights: {total_weight}% (Education: {template.education_weight}%, "
                  f"Experience: {template.experience_weight}%, Skills: {template.skills_weight}%, "
                  f"Responsibilities: {template.responsibilities_weight}%)")
        
        # Test 8: Test batch scoring
        print("\n8. Testing batch scoring...")
        all_cv_analyses = CVAnalysis.objects.all()[:5]  # Test with first 5
        
        for i, cv_analysis in enumerate(all_cv_analyses):
            try:
                result = engine.calculate_custom_score(cv_analysis, vacancy)
                print(f"   ✓ CV {i+1}: {result.final_custom_score:.1f}% - {result.custom_recommendation}")
            except Exception as e:
                print(f"   ✗ CV {i+1}: Error - {str(e)}")
        
        print("\n✅ All tests completed successfully!")
        print("\n📋 System Status:")
        print(f"   • Scoring Templates: {ScoringTemplate.objects.count()}")
        print(f"   • Vacancy Scoring Rules: {VacancyScoringRules.objects.count()}")
        print(f"   • Custom Scoring Results: {CustomScoringResult.objects.count()}")
        
    except Exception as e:
        print(f"   ❌ Error calculating custom score: {str(e)}")
        import traceback
        traceback.print_exc()


def test_scoring_validation():
    """Test scoring validation rules"""
    print("\n🔍 Testing Scoring Validation\n")
    
    # Test weight validation
    print("1. Testing weight validation...")
    try:
        invalid_template = ScoringTemplate(
            name="Invalid Template",
            category="Test",
            education_weight=30.0,
            experience_weight=30.0,
            skills_weight=30.0,
            responsibilities_weight=30.0  # Total = 120%, should be 100%
        )
        # This should be caught by validation
        total_weight = (invalid_template.education_weight + invalid_template.experience_weight + 
                       invalid_template.skills_weight + invalid_template.responsibilities_weight)
        
        if total_weight != 100.0:
            print(f"   ⚠️  Weight validation needed: Total = {total_weight}%")
        else:
            print("   ✓ Weights are valid")
            
    except Exception as e:
        print(f"   ❌ Validation error: {str(e)}")


def create_default_templates():
    """Create some default scoring templates"""
    print("\n🎯 Creating Default Scoring Templates\n")
    
    default_templates = [
        {
            'name': 'Entry Level Developer',
            'category': 'Technology',
            'description': 'For junior software development positions',
            'education_weight': 30.0,
            'experience_weight': 20.0,
            'skills_weight': 45.0,
            'responsibilities_weight': 5.0,
            'education_requirements': {
                'preferred_levels': ['bachelor', 'associate'],
                'required_fields': ['computer science', 'information technology']
            },
            'experience_requirements': {
                'minimum_years': 0,
                'preferred_years': 2
            },
            'skills_requirements': {
                'required_skills': ['programming', 'problem solving'],
                'preferred_skills': ['python', 'java', 'javascript']
            }
        },
        {
            'name': 'Senior Developer',
            'category': 'Technology',
            'description': 'For senior software development positions',
            'education_weight': 15.0,
            'experience_weight': 45.0,
            'skills_weight': 35.0,
            'responsibilities_weight': 5.0,
            'education_requirements': {
                'preferred_levels': ['bachelor', 'master'],
                'required_fields': ['computer science', 'software engineering']
            },
            'experience_requirements': {
                'minimum_years': 5,
                'preferred_years': 8
            },
            'skills_requirements': {
                'required_skills': ['leadership', 'architecture', 'mentoring'],
                'preferred_skills': ['python', 'java', 'aws', 'docker']
            }
        },
        {
            'name': 'Marketing Professional',
            'category': 'Marketing',
            'description': 'For marketing and communications roles',
            'education_weight': 25.0,
            'experience_weight': 35.0,
            'skills_weight': 30.0,
            'responsibilities_weight': 10.0,
            'education_requirements': {
                'preferred_levels': ['bachelor', 'master'],
                'required_fields': ['marketing', 'communications', 'business']
            },
            'experience_requirements': {
                'minimum_years': 2,
                'preferred_years': 5
            },
            'skills_requirements': {
                'required_skills': ['marketing', 'communication', 'analytics'],
                'preferred_skills': ['digital marketing', 'seo', 'social media']
            }
        }
    ]
    
    for template_data in default_templates:
        template, created = ScoringTemplate.objects.get_or_create(
            name=template_data['name'],
            defaults=template_data
        )
        status = "Created" if created else "Updated"
        print(f"   ✓ {status}: {template.name}")


if __name__ == "__main__":
    print("=" * 60)
    print("🎯 CUSTOM SCORING SYSTEM TEST")
    print("=" * 60)
    
    try:
        create_default_templates()
        test_custom_scoring()
        test_scoring_validation()
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS COMPLETED")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc() 