#!/usr/bin/env python
"""
Comprehensive test script for the custom scoring system.
This script tests the complete custom scoring functionality to verify the system works.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cv_analyzer_project.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
django.setup()

from django.db import transaction
from cv_analyzer.models import (
    CVAnalysis, Vacancy, VacancyScoringRules, 
    CustomScoringResult, ScoringTemplate, Company
)

# Import the custom scoring functionality from the temp file
sys.path.append('.')
import custom_scoring_temp as custom_scoring


def test_custom_scoring_engine():
    """Test the CustomScoringEngine class."""
    print("=" * 60)
    print("TESTING CUSTOM SCORING ENGINE")
    print("=" * 60)
    
    # Initialize the scoring engine
    engine = custom_scoring.CustomScoringEngine()
    print(f"✓ CustomScoringEngine initialized successfully")
    
    # Get test data
    cv_analyses = CVAnalysis.objects.all()[:3]  # Test with first 3 CVs
    vacancies = Vacancy.objects.all()[:2]  # Test with first 2 vacancies
    
    print(f"✓ Found {len(cv_analyses)} CV analyses and {len(vacancies)} vacancies for testing")
    
    if not cv_analyses or not vacancies:
        print("❌ No test data available. Please ensure CVs and vacancies exist.")
        return False
    
    results = []
    
    # Test scoring for each CV-vacancy combination
    for cv_analysis in cv_analyses:
        for vacancy in vacancies:
            try:
                print(f"\n--- Testing CV {cv_analysis.id} vs Vacancy {vacancy.id} ---")
                print(f"CV: {cv_analysis.name}")
                print(f"Vacancy: {vacancy.title}")
                
                # Calculate custom score
                result = engine.calculate_custom_score(cv_analysis, vacancy)
                results.append(result)
                
                print(f"✓ Custom Score: {result.final_custom_score:.1f}%")
                print(f"  - Education: {result.education_score:.1f}% (weight: {result.weighted_education_score:.1f})")
                print(f"  - Experience: {result.experience_score:.1f}% (weight: {result.weighted_experience_score:.1f})")
                print(f"  - Skills: {result.skills_score:.1f}% (weight: {result.weighted_skills_score:.1f})")
                print(f"  - Responsibilities: {result.responsibilities_score:.1f}% (weight: {result.weighted_responsibilities_score:.1f})")
                print(f"  - Recommendation: {result.custom_recommendation}")
                print(f"  - Meets Requirements: {result.meets_minimum_requirements}")
                
                if hasattr(cv_analysis, 'overall_score') and cv_analysis.overall_score:
                    print(f"  - AI Score: {cv_analysis.overall_score}%")
                    print(f"  - Difference: {result.ai_vs_custom_difference:+.1f}%")
                
            except Exception as e:
                print(f"❌ Error calculating score: {e}")
                return False
    
    print(f"\n✓ Successfully calculated {len(results)} custom scores")
    return True


def test_scoring_rules_management():
    """Test scoring rules creation and management."""
    print("\n" + "=" * 60)
    print("TESTING SCORING RULES MANAGEMENT")
    print("=" * 60)
    
    try:
        # Get a test vacancy
        vacancy = Vacancy.objects.first()
        if not vacancy:
            print("❌ No vacancy available for testing")
            return False
        
        print(f"Testing with vacancy: {vacancy.title}")
        
        # Create custom scoring rules
        scoring_rules, created = VacancyScoringRules.objects.get_or_create(
            vacancy=vacancy,
            defaults={
                'education_weight': 30.0,
                'experience_weight': 40.0,
                'skills_weight': 25.0,
                'responsibilities_weight': 5.0,
                'minimum_education_score': 60.0,
                'minimum_experience_score': 50.0,
                'minimum_skills_score': 70.0,
                'minimum_overall_score': 60.0,
                'education_requirements': {
                    'bachelor': {'score': 80, 'keywords': ['bachelor', 'ba', 'bs']},
                    'master': {'score': 95, 'keywords': ['master', 'ma', 'ms', 'mba']},
                    'default_score': 40
                },
                'experience_requirements': {
                    'ranges': [
                        {'min': 0, 'max': 2, 'score': 30},
                        {'min': 2, 'max': 5, 'score': 70},
                        {'min': 5, 'max': 999, 'score': 100}
                    ],
                    'default_score': 20
                },
                'skills_requirements': {
                    'required_skills': ['python', 'django', 'sql'],
                    'preferred_skills': ['react', 'aws', 'docker'],
                    'skill_matching_threshold': 0.7,
                    'default_score': 50
                },
                'responsibilities_requirements': {
                    'key_responsibilities': [
                        'software development',
                        'team leadership',
                        'project management'
                    ],
                    'responsibility_matching_threshold': 0.6,
                    'default_score': 50
                }
            }
        )
        
        action = "Created" if created else "Retrieved existing"
        print(f"✓ {action} scoring rules for vacancy {vacancy.id}")
        print(f"  - Education weight: {scoring_rules.education_weight}%")
        print(f"  - Experience weight: {scoring_rules.experience_weight}%")
        print(f"  - Skills weight: {scoring_rules.skills_weight}%")
        print(f"  - Responsibilities weight: {scoring_rules.responsibilities_weight}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Error managing scoring rules: {e}")
        return False


def test_bulk_scoring_functions():
    """Test bulk scoring functions."""
    print("\n" + "=" * 60)
    print("TESTING BULK SCORING FUNCTIONS")
    print("=" * 60)
    
    try:
        # Get test data
        cv_analyses = CVAnalysis.objects.all()[:2]
        vacancies = Vacancy.objects.all()[:2]
        
        if not cv_analyses or not vacancies:
            print("❌ Insufficient test data for bulk operations")
            return False
        
        print(f"Testing bulk scoring with {len(cv_analyses)} CVs and {len(vacancies)} vacancies")
        
        # Test bulk calculate function
        results = custom_scoring.bulk_calculate_custom_scores(
            list(cv_analyses), 
            list(vacancies)
        )
        
        print(f"✓ Bulk calculation completed: {len(results)} results")
        
        # Test calculate for specific vacancy
        vacancy = vacancies[0]
        vacancy_results = custom_scoring.calculate_custom_scores_for_vacancy(vacancy)
        
        print(f"✓ Vacancy-specific calculation completed: {len(vacancy_results)} results")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in bulk scoring functions: {e}")
        return False


def test_score_breakdown_analysis():
    """Test detailed score breakdown functionality."""
    print("\n" + "=" * 60)
    print("TESTING SCORE BREAKDOWN ANALYSIS")
    print("=" * 60)
    
    try:
        # Get a custom scoring result
        result = CustomScoringResult.objects.first()
        if not result:
            print("❌ No custom scoring results available")
            return False
        
        print(f"Analyzing score breakdown for CV {result.cv_analysis.id} vs Vacancy {result.vacancy.id}")
        
        breakdown = result.score_breakdown
        if isinstance(breakdown, dict):
            # Component scores
            components = breakdown.get('component_scores', {})
            print("\n--- Component Scores ---")
            for component, data in components.items():
                print(f"{component.title()}:")
                print(f"  Raw Score: {data.get('raw_score', 0):.1f}%")
                print(f"  Weight: {data.get('weight', 0):.1f}%")
                print(f"  Weighted Score: {data.get('weighted_score', 0):.2f}")
            
            # Minimum requirements
            min_reqs = breakdown.get('minimum_requirements', {})
            print("\n--- Minimum Requirements ---")
            for requirement, threshold in min_reqs.items():
                print(f"{requirement.title()}: {threshold}%")
            
            # Candidate data
            candidate_data = breakdown.get('candidate_data', {})
            print("\n--- Candidate Profile ---")
            for key, value in candidate_data.items():
                print(f"{key.replace('_', ' ').title()}: {value}")
            
            print("✓ Score breakdown analysis completed successfully")
            return True
        else:
            print("❌ Score breakdown data not in expected format")
            return False
            
    except Exception as e:
        print(f"❌ Error analyzing score breakdown: {e}")
        return False


def test_scoring_template_system():
    """Test scoring template functionality."""
    print("\n" + "=" * 60)
    print("TESTING SCORING TEMPLATE SYSTEM")
    print("=" * 60)
    
    try:
        # Create a test scoring template
        template, created = ScoringTemplate.objects.get_or_create(
            name="Software Developer Template",
            defaults={
                'description': "Template for software developer positions",
                'template_data': {
                    'education_weight': 25.0,
                    'experience_weight': 35.0,
                    'skills_weight': 30.0,
                    'responsibilities_weight': 10.0,
                    'minimum_scores': {
                        'education': 50.0,
                        'experience': 60.0,
                        'skills': 70.0,
                        'overall': 65.0
                    },
                    'skill_requirements': [
                        'Python', 'JavaScript', 'SQL', 'Git'
                    ],
                    'experience_requirements': {
                        'minimum_years': 2,
                        'preferred_years': 5
                    }
                }
            }
        )
        
        action = "Created" if created else "Retrieved existing"
        print(f"✓ {action} scoring template: {template.name}")
        print(f"  Description: {template.description}")
        print(f"  Template ID: {template.id}")
        
        # List all templates
        templates = ScoringTemplate.objects.all()
        print(f"✓ Total scoring templates in system: {templates.count()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error with scoring template system: {e}")
        return False


def display_system_summary():
    """Display a summary of the custom scoring system."""
    print("\n" + "=" * 60)
    print("CUSTOM SCORING SYSTEM SUMMARY")
    print("=" * 60)
    
    try:
        # Count existing data
        cv_count = CVAnalysis.objects.count()
        vacancy_count = Vacancy.objects.count()
        scoring_rules_count = VacancyScoringRules.objects.count()
        custom_results_count = CustomScoringResult.objects.count()
        templates_count = ScoringTemplate.objects.count()
        
        print(f"📊 System Statistics:")
        print(f"   CV Analyses: {cv_count}")
        print(f"   Vacancies: {vacancy_count}")
        print(f"   Scoring Rules: {scoring_rules_count}")
        print(f"   Custom Results: {custom_results_count}")
        print(f"   Scoring Templates: {templates_count}")
        
        if custom_results_count > 0:
            # Show average scores
            results = CustomScoringResult.objects.all()
            avg_custom_score = sum(r.final_custom_score for r in results) / len(results)
            avg_ai_difference = sum(abs(r.ai_vs_custom_difference) for r in results) / len(results)
            
            print(f"\n📈 Score Analytics:")
            print(f"   Average Custom Score: {avg_custom_score:.1f}%")
            print(f"   Average AI vs Custom Difference: {avg_ai_difference:.1f}%")
            
            # Show recommendation distribution
            recommendations = {}
            for result in results:
                rec = result.custom_recommendation
                recommendations[rec] = recommendations.get(rec, 0) + 1
            
            print(f"\n🎯 Recommendation Distribution:")
            for rec, count in recommendations.items():
                percentage = (count / len(results)) * 100
                print(f"   {rec.replace('_', ' ').title()}: {count} ({percentage:.1f}%)")
        
        print(f"\n✅ Custom Scoring System Status: FULLY OPERATIONAL")
        
    except Exception as e:
        print(f"❌ Error generating system summary: {e}")


def main():
    """Run all tests for the custom scoring system."""
    print("🚀 CUSTOM SCORING SYSTEM COMPREHENSIVE TEST")
    print("=" * 60)
    
    tests = [
        ("Custom Scoring Engine", test_custom_scoring_engine),
        ("Scoring Rules Management", test_scoring_rules_management),
        ("Bulk Scoring Functions", test_bulk_scoring_functions),
        ("Score Breakdown Analysis", test_score_breakdown_analysis),
        ("Scoring Template System", test_scoring_template_system),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        try:
            if test_function():
                passed_tests += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
        except Exception as e:
            print(f"\n❌ {test_name}: ERROR - {e}")
    
    # Display system summary
    display_system_summary()
    
    # Final results
    print("\n" + "=" * 60)
    print("FINAL TEST RESULTS")
    print("=" * 60)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED - CUSTOM SCORING SYSTEM IS FULLY FUNCTIONAL!")
    else:
        print("⚠️  Some tests failed - Check the output above for details")
    
    return passed_tests == total_tests


if __name__ == "__main__":
    main() 