#!/usr/bin/env python3
"""
Test script to verify CV analysis is working with Ollama configuration.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cv_analyzer_project.settings')
django.setup()

from cv_analyzer.models import AIAPIConfig
from cv_analyzer.views import get_ai_response

def test_cv_analysis():
    """Test CV analysis with current AI configuration."""
    
    print("🧪 Testing CV Analysis with Ollama...")
    print("=" * 50)
    
    # Get active AI configuration
    config = AIAPIConfig.objects.filter(is_active=True).first()
    
    if not config:
        print("❌ No active AI configuration found!")
        return False
    
    print(f"✅ Found active configuration:")
    print(f"   Provider: {config.provider}")
    print(f"   Model: {config.model_name}")
    print(f"   Server: {config.api_key}")
    
    # Test prompt (similar to what CV analysis uses)
    test_prompt = """Analyze this CV and extract:

<PERSON>
Email: <EMAIL>
Phone: ******-0123
Location: New York, NY

Experience:
- Software Engineer at Tech Corp (2020-2023)
- Junior Developer at StartupXYZ (2018-2020)

Education:
- Bachelor of Computer Science, MIT (2018)

Skills: Python, JavaScript, React, Django, SQL

Extract:
1. Name: [candidate name]
2. Email: [email address]
3. Phone: [phone number]
4. Location: [location]
5. Years of experience: [number]
6. Education level: [level]
7. Skills: [comma-separated skills]
8. Languages: [languages]
9. Preferred job type: [full_time/part_time/contract/freelance]
10. Preferred work location: [on_site/remote/hybrid]
11. Overall score: [0-100]
12. Content score: [0-100]
13. Format score: [0-100]
14. Sections score: [0-100]
15. Skills score: [0-100]
16. Style score: [0-100]
17. Content analysis: [brief analysis]
18. Format analysis: [brief analysis]
19. Sections analysis: [brief analysis]
20. Skills analysis: [brief analysis]
21. Style analysis: [brief analysis]

Use exact format above."""
    
    try:
        print(f"\n🔄 Testing AI response...")
        response = get_ai_response(config, test_prompt)
        
        if response:
            print(f"✅ AI Response received ({len(response)} characters)")
            print(f"\n📋 Sample response (first 200 chars):")
            print(f"   {response[:200]}...")
            
            # Check if response contains expected format
            if "Name:" in response and "Email:" in response:
                print(f"✅ Response format looks correct!")
                return True
            else:
                print(f"⚠️  Response format may need adjustment")
                return True
        else:
            print(f"❌ Empty response received")
            return False
            
    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")
        return False

def main():
    """Main function."""
    print("🚀 CV Analysis Test for Ollama Integration")
    print("=" * 50)
    
    success = test_cv_analysis()
    
    if success:
        print(f"\n🎉 Success! CV analysis is working with Ollama!")
        print(f"Your CV Analyzer is ready to process CVs.")
    else:
        print(f"\n❌ CV analysis test failed.")
        print(f"Please check the configuration and try again.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main()) 