#!/usr/bin/env python
"""
Test script for CV Detail Popup functionality
"""
import os
import sys
import django

# Add the project directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cv_analyzer_project.settings')
django.setup()

from cv_analyzer.models import CV, CVAnalysis
from django.contrib.auth.models import User
from django.test import Client
from django.urls import reverse
import json

def test_cv_detail_json():
    """Test the CV detail JSON endpoint"""
    print("Testing CV Detail JSON endpoint...")
    
    # Get a CV from the database
    cvs = CV.objects.all()
    if not cvs.exists():
        print("❌ No CVs found in database. Please upload a CV first.")
        return False
    
    cv = cvs.first()
    print(f"✅ Found CV: {cv.id}")
    
    # Create a test client
    client = Client()
    
    # Create a test user and login
    try:
        user = User.objects.get(username='admin')
    except User.DoesNotExist:
        try:
            user = User.objects.get(username='testuser')
        except User.DoesNotExist:
            user = User.objects.create_user(username='testuser', password='testpass123')
    
    client.force_login(user)
    
    # Test the JSON endpoint
    url = reverse('cv_detail_json', kwargs={'cv_id': cv.id})
    print(f"🔗 Testing URL: {url}")
    
    response = client.get(url)
    
    if response.status_code == 200:
        print("✅ Response status: 200 OK")
        
        try:
            data = response.json()
            print("✅ Response is valid JSON")
            
            # Check required fields
            required_fields = [
                'cv_id', 'candidate_name', 'email', 'phone', 'location',
                'status', 'uploaded_at', 'source', 'overall_score',
                'content_score', 'format_score', 'sections_score',
                'skills_score', 'style_score', 'skills_list', 'languages_list'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ Missing fields: {missing_fields}")
                return False
            else:
                print("✅ All required fields present")
            
            # Print sample data
            print("\n📊 Sample response data:")
            print(f"  - Candidate: {data['candidate_name']}")
            print(f"  - Email: {data['email']}")
            print(f"  - Overall Score: {data['overall_score']}%")
            print(f"  - Skills Count: {len(data['skills_list'])}")
            print(f"  - Languages Count: {len(data['languages_list'])}")
            
            return True
            
        except json.JSONDecodeError:
            print("❌ Response is not valid JSON")
            print(f"Response content: {response.content[:200]}...")
            return False
    else:
        print(f"❌ Response status: {response.status_code}")
        print(f"Response content: {response.content[:200]}...")
        return False

def test_template_structure():
    """Test that the popup template exists and has required elements"""
    print("\n🔍 Testing template structure...")
    
    template_path = "cv_analyzer/templates/cv_analyzer/components/cv_detail_popup.html"
    
    if not os.path.exists(template_path):
        print(f"❌ Template not found: {template_path}")
        return False
    
    print(f"✅ Template found: {template_path}")
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for required elements
    required_elements = [
        'id="cvDetailPopup"',
        'id="cvDetailTemplate"',
        'data-field="candidate_name"',
        'data-field="overall_score"',
        'data-field="skills_list"',
        'action-btn'
    ]
    
    missing_elements = []
    for element in required_elements:
        if element not in content:
            missing_elements.append(element)
    
    if missing_elements:
        print(f"❌ Missing template elements: {missing_elements}")
        return False
    else:
        print("✅ All required template elements present")
        return True

if __name__ == "__main__":
    print("🚀 Starting CV Detail Popup Tests...\n")
    
    success = True
    
    # Test JSON endpoint
    if not test_cv_detail_json():
        success = False
    
    # Test template structure
    if not test_template_structure():
        success = False
    
    print(f"\n{'✅ All tests passed!' if success else '❌ Some tests failed!'}")
    
    if success:
        print("\n🎉 CV Detail Popup is ready to use!")
        print("   - All extracted CV data will be displayed")
        print("   - Back arrow has been removed")
        print("   - Comprehensive analysis results are shown")
    else:
        print("\n🔧 Please fix the issues above before using the popup.") 