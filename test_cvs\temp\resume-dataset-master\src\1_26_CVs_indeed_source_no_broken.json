{"content": "<PERSON><PERSON><PERSON>\nSenior System Engineer - Infosys Limited\n\nSalem, Tamil Nadu - Email me on Indeed: indeed.com/r/Kavitha-K/8977ce8ce48bc800\n\nSeeking to work with a software firm, to constantly upgrade my knowledge and utilize my\nexisting skills to benefit the concerned organization\n\nWORK EXPERIENCE\n\nSenior System Engineer\n\nInfosys Limited -\n\nDecember 2014 to Present\n\nUnix, CA7 scheduler\n\nInfosys Limited -\n\nDecember 2015 to February 2018\n\nQlikview Level 1 • Basic knowledge of creating simple dashboards with different\nProduction support style using Qlikview components like List Box, Slider, Buttons,\ncharts and Bookmarks.\n• Created different types of sheet objects like List boxes, Buttons,\nMulti box.\n• Good knowledge of monitoring Qlikview Dashboards\n• Monitoring Critical dashboards and communicating delay to clients\n• Involved in Qlikview initial error analysis and the concerned\nteam to trouble shoot the issue\n• Monitoring Qlikview dashboards from end to end and manually\nrefreshing the tasks if needed\n• Handling service request for manual refresh of dashboards\n• Monitoring Qlikview dependent ETL jobs in CA7 job scheduler\nLevel 1 (BI process) • Involved in monitoring batch jobs in CA7 job scheduler\n• Managing the daily workload based on priorities and maintain\nSLA's to provide quality services to end users\n• Responsible for sending daily and weekly reports to the clients\n• Initial analysis of log files and fixing of environment related\nissue in ETL Process\n• Coordinating with concerned team in troubleshooting of major\nbusiness related issues and sending notification to the clients on timely manner\n• Responsible for all Process related activities like incident\nmanagement and change management\n• Involved in documenting the process, procedures and flow of ETL Process for critical\napplications\n• Respond to user service requests and resolving them with in stipulated time\n• Participated in Incident Management and Problem\nManagement processes for root cause analysis, resolution and reporting\n\nhttps://www.indeed.com/r/Kavitha-K/8977ce8ce48bc800?isid=rex-download&ikw=download-top&co=IN\n\n\nEDUCATION\n\nNetworking, Infosys Limited\n\nFoundation Training Program in Networking\n\n2016\n\nBachelor of Engineering in Information Technology\n\nREiume Institute of road -  Erode, Tamil Nadu\n\n2014","annotation":[{"label":["Graduation Year"],"points":[{"start":2292,"end":2295,"text":"2014"}]},{"label":["College Name"],"points":[{"start":2245,"end":2268,"text":"REiume Institute of road"}]},{"label":["Degree"],"points":[{"start":2194,"end":2242,"text":"Bachelor of Engineering in Information Technology"}]},{"label":["Degree"],"points":[{"start":2145,"end":2186,"text":"Foundation Training Program in Networking\n"}]},{"label":["Companies worked at"],"points":[{"start":2128,"end":2143,"text":"Infosys Limited\n"}]},{"label":["College Name"],"points":[{"start":2116,"end":2142,"text":"Networking, Infosys Limited"}]},{"label":["Email Address"],"points":[{"start":2022,"end":2060,"text":"indeed.com/r/Kavitha-K/8977ce8ce48bc800"}]},{"label":["Graduation Year"],"points":[{"start":345,"end":348,"text":"2014"}]},{"label":["Email Address"],"points":[{"start":92,"end":130,"text":"indeed.com/r/Kavitha-K/8977ce8ce48bc800"}]},{"label":["Location"],"points":[{"start":52,"end":56,"text":"Salem"}]},{"label":["Companies worked at"],"points":[{"start":35,"end":50,"text":"Infosys Limited\n"}]},{"label":["Designation"],"points":[{"start":10,"end":32,"text":"Senior System Engineer "}]},{"label":["Name"],"points":[{"start":0,"end":8,"text":"Kavitha K"}]}]}
{"content": "Kavya U.\nNetwork Ops Associate - Accenture\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Kavya-U/049577580b3814e6\n\nSeeking for opportunities to learn and grow in electronics domain.\n\nWORK EXPERIENCE\n\nNetwork Ops Associate\n\nAccenture -  Bengaluru, Karnataka -\n\nDecember 2017 to Present\n\nExposure:\n• Provisioning of different types of network speeds for multiple clients.\n• Use of Mux designing for Logical cross connect and Physical patching. We co- ordinate with\nvarious ops and field engineers to do the connections at the physical\nlevel.\n\n2) Organisation: QuadGen Wireless Engineering Services Pvt. Ltd., Bangalore.\nPosition: Network Engineer\nExperience: 1 year\nExposure:\n• RAN (Radio Access Network) Engineer.\n• New Site Build (NSB)\n\n2) Organisation: Manipal Dot Net Pvt. Ltd., Manipal.\nPosition: Intern\nExperience: 1 year\nExposure:\n• Module coding and verification using Verilog HDL\n• Worked on Linux O.S.\n• Understanding of SPI, I2C protocols\n• Compilation using Altera Quartus\n• Simulation using ModelSim\n• Report preparation and documentation\n\nEDUCATION\n\nLittle Rock Indian School\n\n2007\n\nMaster of Science in Technology in Technology\n\nSchool Of Information Sciences\n\nVLSI Design\n\nhttps://www.indeed.com/r/Kavya-U/049577580b3814e6?isid=rex-download&ikw=download-top&co=IN\n\n\nManipal Academy of Higher Education\n\nBachelor of Engineering in Engineering\n\nSrinivas Institute of Technology -  Mangalore, Karnataka\n\nElectronics and Communication\n\nVisvesvaraya Technological University\n\nVidyodaya P.U. College -  Udipi, Karnataka\n\nSKILLS\n\ncoding (Less than 1 year), HDL (Less than 1 year), Microsoft office (Less than 1 year), MS\nOFFICE (Less than 1 year), UART (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTechnical Skills\n• Verilog HDL\n• Knowledge of RTL coding, FSM based designs.\n• Understanding of UART, AMBA protocol\n• Platforms: Microsoft office, Libreoffice","annotation":[{"label":["Skills"],"points":[{"start":1844,"end":1872,"text":"Microsoft office, Libreoffice"}]},{"label":["Skills"],"points":[{"start":1844,"end":1859,"text":"Microsoft office"}]},{"label":["Skills"],"points":[{"start":1811,"end":1814,"text":"UART"}]},{"label":["Skills"],"points":[{"start":1794,"end":1829,"text":"Understanding of UART, AMBA protocol"}]},{"label":["Skills"],"points":[{"start":1765,"end":1770,"text":"coding"}]},{"label":["Skills"],"points":[{"start":1748,"end":1789,"text":"Knowledge of RTL coding, FSM based designs"}]},{"label":["Skills"],"points":[{"start":1742,"end":1744,"text":"HDL"}]},{"label":["Skills"],"points":[{"start":1734,"end":1744,"text":"Verilog HDL"}]},{"label":["Skills"],"points":[{"start":1666,"end":1669,"text":"UART"}]},{"label":["Skills"],"points":[{"start":1636,"end":1644,"text":"MS\nOFFICE"}]},{"label":["Skills"],"points":[{"start":1599,"end":1614,"text":"Microsoft office"}]},{"label":["Skills"],"points":[{"start":1575,"end":1577,"text":"HDL"}]},{"label":["Skills"],"points":[{"start":1548,"end":1553,"text":"coding"}]},{"label":["College Name"],"points":[{"start":1496,"end":1517,"text":"Vidyodaya P.U. College"}]},{"label":["College Name"],"points":[{"start":1457,"end":1494,"text":"Visvesvaraya Technological University\n"}]},{"label":["College Name"],"points":[{"start":1368,"end":1399,"text":"Srinivas Institute of Technology"}]},{"label":["Degree"],"points":[{"start":1328,"end":1365,"text":"Bachelor of Engineering in Engineering"}]},{"label":["College Name"],"points":[{"start":1291,"end":1325,"text":"Manipal Academy of Higher Education"}]},{"label":["Email Address"],"points":[{"start":1210,"end":1246,"text":"indeed.com/r/Kavya-U/049577580b3814e6"}]},{"label":["College Name"],"points":[{"start":1153,"end":1183,"text":"School Of Information Sciences\n"}]},{"label":["Degree"],"points":[{"start":1106,"end":1182,"text":"Master of Science in Technology in Technology\n\nSchool Of Information Sciences"}]},{"label":["Graduation Year"],"points":[{"start":1100,"end":1103,"text":"2007"}]},{"label":["College Name"],"points":[{"start":1073,"end":1098,"text":"Little Rock Indian School\n"}]},{"label":["Skills"],"points":[{"start":894,"end":896,"text":"HDL"}]},{"label":["Skills"],"points":[{"start":886,"end":896,"text":"Verilog HDL"}]},{"label":["Skills"],"points":[{"start":856,"end":861,"text":"coding"}]},{"label":["Location"],"points":[{"start":247,"end":255,"text":"Bengaluru"}]},{"label":["Companies worked at"],"points":[{"start":234,"end":243,"text":"Accenture "}]},{"label":["Designation"],"points":[{"start":211,"end":232,"text":"Network Ops Associate\n"}]},{"label":["Email Address"],"points":[{"start":87,"end":123,"text":"indeed.com/r/Kavya-U/049577580b3814e6"}]},{"label":["Location"],"points":[{"start":44,"end":52,"text":"Bengaluru"}]},{"label":["Name"],"points":[{"start":0,"end":7,"text":"Kavya U."}]}]}
{"content": "Khushboo Choudhary\nDeveloper\n\nNoida, Uttar Pradesh - Email me on Indeed: indeed.com/r/Khushboo-Choudhary/\nb10649068fcdfa42\n\nTo pursue a challenging career and be part of a progressive organization that gives scope to\nenhance my\nknowledge, skills and to reach the pinnacle in the computing and research field with sheer\ndetermination,\ndedication and hard work.\n\nWORK EXPERIENCE\n\nDeveloper\n\nSAP ABAP -  Noida, Uttar Pradesh -\n\nJanuary 2018 to May 2018\n\nSAP ABAB 7.4 Noida, Uttar\n(5 months) Pradesh\n\nTechnical Proficiency\n1. SAP ABAP 7.4 2. OOPS\n3. DBMS 4. Core Java\n5. C/C++ 6. Data Structures\n\nRoles and Responsibilities\n• Creating report generating modules.\n• Creating interactive modules for trainers to train.\n\nOfficial Projects\n1. Uploading file from non-sap system to sap system using BAPI.\n2. Uploading excel data using BDC.\n3. Generating Adobe forms.\n4. Creating smart forms for order purchasing.\n5. Automatic email sending module using workflow.\n6. Creating classical reports.\n7. Creating function module.\n\nEDUCATION\n\nB.Tech in CSE\n\nMM University\n\n2013 to 2017\n\nCBSE\n\nhttps://www.indeed.com/r/Khushboo-Choudhary/b10649068fcdfa42?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Khushboo-Choudhary/b10649068fcdfa42?isid=rex-download&ikw=download-top&co=IN\n\n\nParatap Public School -  Karnal, Haryana\n\nDecember 2011\n\nCBSE in Technology Used\n\nSilver Bells Public School -  Muzaffarnagar, Uttar Pradesh\n\nOctober 2009\n\nEngineering College\n\nSKILLS\n\nANDROID (Less than 1 year), CISCO (Less than 1 year), NETWORKING (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nDevelopment Skills.\n\nMay 2016 1.5 Learned about \"Basic Networking\" using Cisco MMEC, Mullana\nPacket Tracer.\n\nJune, 2015 2 Built an application which have functionality of Solitaire Infosys inc.\nconverting a text into speech using text to speech class Mohali, India\nnamed \"kid'z speak\" using Android.","annotation":[{"label":["Skills"],"points":[{"start":1466,"end":1548,"text":"ANDROID (Less than 1 year), CISCO (Less than 1 year), NETWORKING (Less than 1 year)"}]},{"label":["College Name"],"points":[{"start":1363,"end":1420,"text":"Silver Bells Public School -  Muzaffarnagar, Uttar Pradesh"}]},{"label":["College Name"],"points":[{"start":1281,"end":1320,"text":"Paratap Public School -  Karnal, Haryana"}]},{"label":["Graduation Year"],"points":[{"start":1063,"end":1066,"text":"2017"}]},{"label":["College Name"],"points":[{"start":1040,"end":1052,"text":"MM University"}]},{"label":["Degree"],"points":[{"start":1025,"end":1037,"text":"B.Tech in CSE"}]},{"label":["Companies worked at"],"points":[{"start":522,"end":530,"text":"SAP ABAP "}]},{"label":["Location"],"points":[{"start":464,"end":468,"text":"Noida"}]},{"label":["Location"],"points":[{"start":401,"end":405,"text":"Noida"}]},{"label":["Companies worked at"],"points":[{"start":389,"end":397,"text":"SAP ABAP "}]},{"label":["Designation"],"points":[{"start":378,"end":386,"text":"Developer"}]},{"label":["Email Address"],"points":[{"start":73,"end":122,"text":"indeed.com/r/Khushboo-Choudhary/\nb10649068fcdfa42\n"}]},{"label":["Location"],"points":[{"start":30,"end":34,"text":"Noida"}]},{"label":["Designation"],"points":[{"start":19,"end":27,"text":"Developer"}]},{"label":["Name"],"points":[{"start":0,"end":17,"text":"Khushboo Choudhary"}]}]}
{"content": "kimaya sonawane\nThane, Maharashtra - Email me on Indeed: indeed.com/r/kimaya-\nsonawane/1f27a18d2e4b1948\n\nQuality education blended with sense of responsibility to utilize my professional as well as\ninterpersonal skills that enables me to achieve the goals.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nTechnical Support Engineer\n\nSAP -  Thane, Maharashtra -\n\nNovember 2016 to Present\n\nEDUCATION\n\nBE in computer science\n\nSSVPS’s Late B. S. Deore College of Engineering ,Dhule -  Dhule, Maharashtra\n\n2011 to 2016\n\nSKILLS\n\nnetwork engineers, Networking, CCNA, knowledge of Active Directory, DHCP, DNS ,\nTroubleshooting and fix Network related issues (2 years)\n\nCERTIFICATIONS/LICENSES\n\nCCNA(Cisco Certified Network Associate- Routing & Switching) , MCSA\n(Microsoft Certified Solution Associate)\n\nJuly 2016 to Present\n\nADDITIONAL INFORMATION\n\nPROFESSIONAL INTRESTS:\n• Leading and managing teams\n• Interacting with People\nCO CURRICULAR ACTIVITES:\n• Participated in \"Mech-Tricks\" in IMPULSE 2014 National Level Event.\n• Participated in \"Mech-Tricks\" in IMPULSE 2013 National Level Event.\n• Participated in \"Tech-Quiz\" in IMPULSE 2013 National Level Event.\n• Participated in \"Management Games\" Organised in Ganesh Utsav 2012.\n• Winner in \"Rangoli Competition\" Organised in Ganesh Utsav 2013.\n\nhttps://www.indeed.com/r/kimaya-sonawane/1f27a18d2e4b1948?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/kimaya-sonawane/1f27a18d2e4b1948?isid=rex-download&ikw=download-top&co=IN\n\n\nPERSONAL TRAITS:\n\n• Self Motivated\n• Adaptable\n• Confident\n• Team facilitator\n• Hard Worker","annotation":[{"label":["Graduation Year"],"points":[{"start":802,"end":805,"text":"2016"}]},{"label":["Skills"],"points":[{"start":524,"end":660,"text":"network engineers, Networking, CCNA, knowledge of Active Directory, DHCP, DNS ,\nTroubleshooting and fix Network related issues (2 years)\n"}]},{"label":["Graduation Year"],"points":[{"start":510,"end":513,"text":"2016"}]},{"label":["College Name"],"points":[{"start":424,"end":470,"text":"SSVPS’s Late B. S. Deore College of Engineering"}]},{"label":["Degree"],"points":[{"start":400,"end":421,"text":"BE in computer science"}]},{"label":["Graduation Year"],"points":[{"start":372,"end":375,"text":"2016"}]},{"label":["Location"],"points":[{"start":341,"end":345,"text":"Thane"}]},{"label":["Companies worked at"],"points":[{"start":334,"end":338,"text":"SAP -"}]},{"label":["Designation"],"points":[{"start":306,"end":331,"text":"Technical Support Engineer"}]},{"label":["Email Address"],"points":[{"start":57,"end":102,"text":"indeed.com/r/kimaya-\nsonawane/1f27a18d2e4b1948"}]},{"label":["Location"],"points":[{"start":16,"end":20,"text":"Thane"}]},{"label":["Name"],"points":[{"start":0,"end":14,"text":"kimaya sonawane"}]}]}
{"content": "Koushik Katta\nDevops\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Koushik-Katta/a6b19244854199ec\n\nDevOps Administrator with an experience of 3.4 years working in a challenging agile\nenvironment, looking forward for a position where I can use my knowledge pursuing my domain\ninterests. I'm more aligned to work for companies where knowledge and intellectual ability takes\nthe lead which can utilize a performance driven individual efficiently.\n\nWORK EXPERIENCE\n\nDevops Engineer\n\nInfosys limited -  Hyderabad, Telangana -\n\nDecember 2014 to Present\n\nHyderabad, since December 2014 to till date.\n\nSkill and Abilities:\nAtlassian Tools: Jira, Confluence\nConfiguration Management: Ansible /Chef\nCI Tools: Jenkins\nMonitoring Tools: Nagios\nCloud: AWS\nContainerization: Docker\nBuild Tools: Bamboo\\Maven\nLog Tools: Splunk\nDatabases: RDBMS, MYSQL, Oracle Database\nProgramming Languages: Python and Java\nScripting: Power Shell\nOperating Systems: Windows, Linux family, Redhat Linux\nMiddleware: Websphere, Tomcat, Websphere MQ\n\nResponsibilities:\nDEVOPS ADMINISTRATOR\nINFOSYS LTD.\n\nAtlassian tools Release Management according\n\nInfosys limited -\n\nDecember 2014 to Present\n\nto project needs.\n✓ Review and upgrade of Plugins to meet project requirements and to achieve better\nperformance.\n✓ Configuring Automated Mail handlers, Webhooks as POC to test the new demands raised by\nclient.\n\nhttps://www.indeed.com/r/Koushik-Katta/a6b19244854199ec?isid=rex-download&ikw=download-top&co=IN\n\n\n✓ JIRA Project Management/Administration.\n✓ Confluence Space Management/Administration.\n✓ Bitbucket Project/Repository Management/Administration (Enterprise/DataCenter)\n✓ Integration of Webhooks in Bitbucket.\n✓ Streamlining tools access management with Crowd.\n2. Administration and Maintenance of Jenkins\n✓ Configure and maintain Jenkins slaves as per the requirement.\n✓ Jenkins release management.\n✓ Work closely with Development teams to configure CI/CD Pipelines to automate their build &\ndeployment process.\n✓ Review, Installation/Upgrade and configuration of Jenkins Plugins.\n✓ Configuring proxy on the environments to enable security\n✓ Debug build issues\n3. Administration and Maintenance of Docker registry\n4. Working with Open-Source Nagios plugins on demand basis to setup ICINGA monitoring for our\non-premise Servers/Applications.\n5. Alerting Setup Splunk.\n6. Monitoring Dashboards setup using kibana.\n7. Working with product support teams to resolve the product bugs.\n8. Involve in client meetings and tool performance reviews to ensure stakeholder satisfaction.\n9. Work closely with Infrastructure Teams to setup/maintain/improve the above mentioned\napplication on large scale.\n\nEDUCATION\n\nBachelor Of Engineering in Mechanical Engineering\n\nLovely Professional University\n\n2010 to 2014\n\nSecondary School Certificate in education\n\nBoard of Intermediate education -  Hyderabad, Telangana\n\n2008 to 2010\n\nSister Nivedita School -  Karimnagar, Telangana\n\n2008\n\nSKILLS\n\nJira, Ansible, Jenkins, Splunk, Nagios, Docker, Python, AWS, Bamboo, Linux, Git, Chef, Windows,\nPowershell Scripting\n\nADDITIONAL INFORMATION\n\n• Ability to learn new technologies and processes rapidly and implement them in the project.\n• Highly motivated with very good problem solving and analytical skills Well organized, with\nexcellent in multitasking and prioritizing the work.\n\n\n\n• Effective communicator with an ability to convey ideas in speaking and writing.\n• Excellent analytical and decision making skills.\n• Ability to work in pressurized situation.\n• Hard worker and goal oriented.\n• Always ready to learn new skills","annotation":[{"label":["Skills"],"points":[{"start":2957,"end":3073,"text":"Jira, Ansible, Jenkins, Splunk, Nagios, Docker, Python, AWS, Bamboo, Linux, Git, Chef, Windows,\nPowershell Scripting\n"}]},{"label":["Graduation Year"],"points":[{"start":2943,"end":2947,"text":"2008\n"}]},{"label":["College Name"],"points":[{"start":2894,"end":2916,"text":"Sister Nivedita School "}]},{"label":["Graduation Year"],"points":[{"start":2888,"end":2891,"text":"2010"}]},{"label":["Location"],"points":[{"start":2858,"end":2866,"text":"Hyderabad"}]},{"label":["Degree"],"points":[{"start":2823,"end":2853,"text":"Board of Intermediate education"}]},{"label":["Degree"],"points":[{"start":2780,"end":2821,"text":"Secondary School Certificate in education\n"}]},{"label":["Graduation Year"],"points":[{"start":2774,"end":2777,"text":"2014"}]},{"label":["Graduation Year"],"points":[{"start":2766,"end":2769,"text":"2010"}]},{"label":["College Name"],"points":[{"start":2734,"end":2763,"text":"Lovely Professional University"}]},{"label":["Degree"],"points":[{"start":2683,"end":2731,"text":"Bachelor Of Engineering in Mechanical Engineering"}]},{"label":["Email Address"],"points":[{"start":1394,"end":1436,"text":"indeed.com/r/Koushik-Katta/a6b19244854199ec"}]},{"label":["Graduation Year"],"points":[{"start":1153,"end":1156,"text":"2014"}]},{"label":["Companies worked at"],"points":[{"start":1125,"end":1139,"text":"Infosys limited"}]},{"label":["Designation"],"points":[{"start":1044,"end":1063,"text":"DEVOPS ADMINISTRATOR"}]},{"label":["Skills"],"points":[{"start":626,"end":1023,"text":"Atlassian Tools: Jira, Confluence\nConfiguration Management: Ansible /Chef\nCI Tools: Jenkins\nMonitoring Tools: Nagios\nCloud: AWS\nContainerization: Docker\nBuild Tools: Bamboo\\Maven\nLog Tools: Splunk\nDatabases: RDBMS, MYSQL, Oracle Database\nProgramming Languages: Python and Java\nScripting: Power Shell\nOperating Systems: Windows, Linux family, Redhat Linux\nMiddleware: Websphere, Tomcat, Websphere MQ"}]},{"label":["Graduation Year"],"points":[{"start":585,"end":588,"text":"2014"}]},{"label":["Location"],"points":[{"start":559,"end":567,"text":"Hyderabad"}]},{"label":["Graduation Year"],"points":[{"start":542,"end":545,"text":"2014"}]},{"label":["Location"],"points":[{"start":509,"end":517,"text":"Hyderabad"}]},{"label":["Companies worked at"],"points":[{"start":490,"end":504,"text":"Infosys limited"}]},{"label":["Email Address"],"points":[{"start":65,"end":107,"text":"indeed.com/r/Koushik-Katta/a6b19244854199ec"}]},{"label":["Location"],"points":[{"start":22,"end":30,"text":"Hyderabad"}]},{"label":["Name"],"points":[{"start":0,"end":12,"text":"Koushik Katta"}]}]}
{"content": "Kowsick Somasundaram\nCertified Network Associate Training Program\n\nErode, Tamil Nadu - Email me on Indeed: indeed.com/r/Kowsick-\nSomasundaram/3bd9e5de546cc3c8\n\nBachelor of computer science graduate seeking opportunities in the field of ITIS to contribute\nto corporate goals and objectives. Easily adapt to changes, with eagerness toward learning and\nexpanding capabilities.\n\nEXPERIENCE:-\n\nWORK EXPERIENCE\n\nCertified Network Associate Training Program\n\nCisco -\n\nJuly 2013 to October 2013\n\n• Workshop on computer Hardware& Software.\n\n• Workshop on Web development.\n\nEDUCATION\n\nBachelor of computer science in computer science\n\ninDR N.G.P ARTS AND SCIENCE COLLEGE -  Coimbatore, Tamil Nadu\n\nSKILLS\n\nDHCP (Less than 1 year), DNS (Less than 1 year), EXCHANGE (Less than 1 year), exchange\n(Less than 1 year), LAN (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS:-\n\n• Messaging: MS exchange, Lotus client and MS outlook issue coordination to user.\n\n• Users / Share folders creation and permission assigning.\n\n• Networking: TCP/IP, DNS, DHCP, and LAN/WAN.\n\n• Monthly patching update activity and server owner approval / RFC follow-ups.\n\nhttps://www.indeed.com/r/Kowsick-Somasundaram/3bd9e5de546cc3c8?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Kowsick-Somasundaram/3bd9e5de546cc3c8?isid=rex-download&ikw=download-top&co=IN","annotation":[{"label":["Skills"],"points":[{"start":696,"end":1128,"text":"DHCP (Less than 1 year), DNS (Less than 1 year), EXCHANGE (Less than 1 year), exchange\n(Less than 1 year), LAN (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS:-\n\n• Messaging: MS exchange, Lotus client and MS outlook issue coordination to user.\n\n• Users / Share folders creation and permission assigning.\n\n• Networking: TCP/IP, DNS, DHCP, and LAN/WAN.\n\n• Monthly patching update activity and server owner approval / RFC follow-ups."}]},{"label":["College Name"],"points":[{"start":625,"end":660,"text":"inDR N.G.P ARTS AND SCIENCE COLLEGE "}]},{"label":["Degree"],"points":[{"start":575,"end":623,"text":"Bachelor of computer science in computer science\n"}]},{"label":["Companies worked at"],"points":[{"start":451,"end":456,"text":"\nCisco"}]},{"label":["Designation"],"points":[{"start":406,"end":449,"text":"Certified Network Associate Training Program"}]},{"label":["Email Address"],"points":[{"start":107,"end":157,"text":"indeed.com/r/Kowsick-\nSomasundaram/3bd9e5de546cc3c8"}]},{"label":["Location"],"points":[{"start":67,"end":71,"text":"Erode"}]},{"label":["Designation"],"points":[{"start":21,"end":64,"text":"Certified Network Associate Training Program"}]},{"label":["Name"],"points":[{"start":0,"end":19,"text":"Kowsick Somasundaram"}]}]}
{"content": "Lakshika Neelakshi\nSenior Systems Engineer - Infosys Limited\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Lakshika-\nNeelakshi/27b31f359c52ef76\n\nAn organized and independent individual looking for role to be able to effectively coordinate\ntasks in\nproject and accomplish the result adhering with timeliness and creativity.\n\nWORK EXPERIENCE\n\nSenior Systems Engineer\n\nInfosys Limited -  Bengaluru, Karnataka -\n\nJanuary 2018 to Present\n\nEnvironment SAPUI5 version 1.4\n\nDescription:\nAirbus SE is a European multinational corporation that designs, manufactures, and sells civil and\nmilitary aeronautical products worldwide.\n\nProject Contribution:\n• Working on creating a custom Annotation Tool (AnnoQ) using a third party js library to annotate\nany 2D picture within a SAPUI5 application specifically designed to work on laptop and Desktop.\n• The custom tool can be called from any SAP or Non- SAP system to be used as a plug-in to\nannotate pictures across the Airbus SE application to be used across browsers like\nChrome/Edge and IE and should be compatible with Windows 7, 8, 10.\n• Parts of a picture can be highlighted and marked and using this tool and can be saved for\nreference or future use.\n• Worked extensively on Fabric js and created various customized objects like Call out Box,\nMeasurement Arrow, Datum, Cross Datum, Forward arrow, etc on mouse and object events.\n• Implemented various functionality like color change, crop, zoom, text size selection, width\nselection, saving the annotation in JSON format in the backend so as to retain the original\npicture as it is.\n• Also contributed in designing the layout and overall appearance of the tool.\n• Contributed in integrating the application with MNC Mobile Application.\n\nSenior Systems Engineer\n\nInfosys Limited -  Bengaluru, Karnataka -\n\nOctober 2015 to Present\n\n• Having 2.5 years of experience in SAPUI5/Fiori, Netweaver Gateway Odata Services and SAP\nABAP development of large scale ERP packages.\n• Working with SAP/R3, ECC environments also having experience in HTML, JavaScript, JSON,\nXML, CSS.\n\nhttps://www.indeed.com/r/Lakshika-Neelakshi/27b31f359c52ef76?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Lakshika-Neelakshi/27b31f359c52ef76?isid=rex-download&ikw=download-top&co=IN\n\n\n• Currently working as Senior Systems Engineer with Infosys Limited, Bangalore since October\n2015.\n\nSAP Expertise\nSAPUI5/Fiori\n\n• Expertise in developing SAP UI5 applications using ADT (Eclipse) /WebIDE, jQuery, JavaScript,\nHTML5 and CSS3. Also consuming the data by using Net weaver Gateway services.\n• Well equipped in extending and customizing various UI5 controls specially charts, vizframes\nand Smart Table, etc.\n• Experience in integrating Gateway Odata/ JSON services with UI% application.\n• Worked extensively with application having multiple Views and controllers and hence\nexperienced in navigating through it using Routing using both XML and JS views.\n• Exposed to be working in SAPUI5 Custom Applications and Standard Fiori Applications.\n• Experienced with UI5 application development in local server and debug it in Chrome/Firefox\ndebuggers.\n• Experienced in Github.\n\nAdvance Business Application Programming\n• ALV (ABAP List Viewer) - Grid and List Display.\n• SAP smart forms and Scripts.\n• Worked in BDC (Batch Data Communication) - BI and CTU method\n• RFC Function Modules.\n• Exposure in creating Data Dictionary objects (Domain, Data Elements, Structures and Views)\n• Conceptual knowledge Dialog programs using Menu Painter/Screen Painter\n• Worked on Object Oriental Programming concepts (OOPS)\n\nSystems Engineer\n\nInfosys Limited -  Bengaluru, Karnataka -\n\nAugust 2017 to December 2017\n\nEnvironment SAPUI5 version 1.28\n\nDescription:\nJabil Inc. is a US based global manufacturing services company headquartered in St. Petersburg,\nFlorida. Jabil is involved in design engineering services. The company has industrial design\nservices\nthat concentrate on designing the look and feel of plastic and metal enclosures that house printed\ncircuit board assemblies and systems.\n\nProject Contribution:\n• Created custom Fiori apps for the client including screens having functionality of create\ndocument, Inbox to store the transaction mail and Dashboards to view day to day transactions.\n• Prepared multiple screens for the dashboard with view reusability features.\n• Worked on OData binding and hence display of data in relevant format on to the screen.\n• Implemented various other functionalities based on OData consumption, Routing and\nNavigation and JSON models.\n\n\n\n• Worked on complex functionality like excel data transfer directly to UI5 tables on screen while\nkeeping the table data editable and also excel data upload functions to the table.\n• Have clear and distinct knowledge of various UI5 controls.\n\nSystems Engineer\n\nInfosys Limited -  Hyderabad, Telangana -\n\n2016 to July 2017\n\nEnvironment SAPUI5 version 1.5\n\nDescription:\nAmerican Water is an American public utility company operating in the United States and Canada.\nIt\nwas founded in 1886 as the American Water Works & Guarantee Company.\n\nProject Contribution:\n• Worked as SAPUI5 developer in KPI project for American Waters account.\n• Developed KPI Dashboards using controls like Vizframe, Tables etc.\n• Worked with different types Micro charts control available like Column, Comparison, Radial etc.\n• Also extended some of them as per project requirements.\n• Experience of working with OData to get the data to be displayed on the dashboards.\n• Additionally, implemented some special features in the Dashboard development like Export to\nexcel link, Download Image of the UI charts & documentation link in Fiori tiles.\n• Also worked on multiple levels of drill downs using multiple controllers and views.\n• Individually worked and delivered an extension for one of the UI5 controls which got much\nappreciated from clients and offshore team as well.\n• Implementation of the apps on Fiori Launchpad.\n• Implemented multiple filters on the data pulled from the service for desired results as per\nproject requirements.\n\nSAP UI5:\nUI5 Controls:\n• Created dashboards by various UI5 controls such as Tables, vizFrame, Tab Filters to name a few.\n• Exposure in extending various UI5 standard controls to get the desired result.\n• Used SAP best practices while using all these controls.\n\nSystems Engineer\n\nInfosys Limited -  Hyderabad, Telangana -\n\nJuly 2016 to September 2016\n\nEnvironment SAP 5.0\n\nDescription:\nHarley Davidson is an American motorcycle manufacturer, founded in Milwaukee, Wisconsin in\n1903.\n\nProject Contribution:\n\n\n\n• Innovation track dashboard preparation.\n• Creation of POC application to filter the list based upon multiple filters with the help of Dialog\nProgramming.\n• The application also had functionalities like add new POCs, update and delete the existing\nones and modify others.\n• Data Upload to the SAP system from Excel sheet using BDC.\n• Additionally, the task required knowledge of Data Dictionary, Report Programming- Classical &\nALV, Module Pool Programming, Batch Data Communication- Call Transaction & Session\nMethod.\n\nEDUCATION\n\nBachelor of Engineering in Instrumentation Technology in\nInstrumentation Technology\n\nDayananda Sagar College of Engineering -  Sagar, Karnataka\n\nSKILLS\n\nSAPUI5 (2 years), CSS. (2 years), EMPLOYEE RESOURCE GROUP (2 years), ENTERPRISE\nRESOURCE PLANNING (2 years), SAP ABAP (2 years)\n\nADDITIONAL INFORMATION\n\nTechnical skills\nLanguages SAPUI5 (Primary Skill),\nABAP/4, C, C++, HTML, CSS, JavaScript, PHP,\njQuery, Ajax.\nERP SAP R/3 in 4.7, 5.0 (E)\nOperating Systems Windows\nDatabase MySQL, Oracle","annotation":[{"label":["Skills"],"points":[{"start":7260,"end":7598,"text":"\nSAPUI5 (2 years), CSS. (2 years), EMPLOYEE RESOURCE GROUP (2 years), ENTERPRISE\nRESOURCE PLANNING (2 years), SAP ABAP (2 years)\n\nADDITIONAL INFORMATION\n\nTechnical skills\nLanguages SAPUI5 (Primary Skill),\nABAP/4, C, C++, HTML, CSS, JavaScript, PHP,\njQuery, Ajax.\nERP SAP R/3 in 4.7, 5.0 (E)\nOperating Systems Windows\nDatabase MySQL, Oracle"}]},{"label":["Location"],"points":[{"start":3628,"end":3636,"text":"Bengaluru"}]},{"label":["Companies worked at"],"points":[{"start":2329,"end":2344,"text":" Infosys Limited"}]},{"label":["Designation"],"points":[{"start":2301,"end":2323,"text":"Senior Systems Engineer"}]},{"label":["Location"],"points":[{"start":1785,"end":1793,"text":"Bengaluru"}]},{"label":["Designation"],"points":[{"start":1741,"end":1763,"text":"Senior Systems Engineer"}]},{"label":["Location"],"points":[{"start":396,"end":404,"text":"Bengaluru"}]},{"label":["Designation"],"points":[{"start":352,"end":374,"text":"Senior Systems Engineer"}]},{"label":["Email Address"],"points":[{"start":105,"end":153,"text":"indeed.com/r/Lakshika-\nNeelakshi/27b31f359c52ef76"}]},{"label":["Location"],"points":[{"start":62,"end":70,"text":"Bengaluru"}]},{"label":["Companies worked at"],"points":[{"start":44,"end":59,"text":" Infosys Limited"}]},{"label":["Designation"],"points":[{"start":19,"end":41,"text":"Senior Systems Engineer"}]},{"label":["Name"],"points":[{"start":0,"end":17,"text":"Lakshika Neelakshi"}]}]}
{"content": "Madas Peddaiah\nAnantapur, Andhra Pradesh - Email me on Indeed: indeed.com/r/Madas-\nPeddaiah/557069069de72b14\n\n• Having 3 moths of experience in Manual Testing.\n• Previously worked with Infosys Limited, Mysore as a Software Test Engineer.\n• Having good experience in Executed Test Cases as per Client Requirements.\n• Having good experience in identifying the test scenarios and designing the test cases.\n• Worked on IE, Firefox and Chrome Driver using Selenium.\n• Good Knowledge in Core Java, SQL.\n• Experience in designing, preparing and executing test cases for client server and web based\napplications STLC Concepts.\n➢ Web Based Application Testing\n• Experience in understanding business requirements, preparing and execution of test cases for\nSystem Customizations/Enhancements and Initiatives.\n• Quick learner with the ability to grasp new technologies.\n• Excellent team player having ability to finish the tight deadlines and work under pressure.\n• Good exposure on Manual Testing & Bug Life Cycle.\n\nWORK EXPERIENCE\n\nInfosys Limited -  Mysore, Karnataka -\n\nSeptember 2014 to December 2014\n\nEducational Technologies:\n\nSoftware Test Engineer\n\nInfosys Limited -\n\nSeptember 2014 to December 2014\n\n-September 2014 to December 2014.\nProject: 1\nClient: Loan Account\nRole: Software Test Engineer\nTeam Size: 4\nTechnologies: J2EE, Oracle 10g, Windows 7\nUsed Tools: Selenium, Core Java, HPQC and Eclipse.\nDuration: September 2014 to December 2014\n\nProject Description:\nIn this project we maintain all details about account transaction details, customer loan account\ndetails, calculate monthly EMI's and there activity like - Account login details, Account transaction,\nLoan account details etc.\nResponsibilities:\n• Participated in identifying the test scenarios and designing the test cases.\n• Prepared and Executed Test Cases as per Client Requirements.\n\nhttps://www.indeed.com/r/Madas-Peddaiah/557069069de72b14?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Madas-Peddaiah/557069069de72b14?isid=rex-download&ikw=download-top&co=IN\n\n\n• Performed Manual Testing on some modules.\n• Feasibility analysis of Manual Test Cases.\n• Performed Functional, Compatibility testing on different browsers like Firefox & Chrome.\n• Defect tracking & Bug Reporting.\nProject: 2\nClient: Hospital Management\nRole: Software Test Engineer\nTeam Size: 4\n\nEDUCATION\n\nB-Tech\n\nKuppam Engineering College -  Kuppam, Andhra Pradesh\n\n2014\n\nEducation, A.P\n\nVani Jr college\n\nOctober 1977 to 2010\n\nEducation, A.P\n\nPadmavani High School\n\n2008\n\nADDITIONAL INFORMATION\n\nTechnical Skills:\nTools: Manual Testing, Selenium (Selenium IDE, Selenium Web Driver), Eclipse IDE.\nLanguages: C, Core Java\nDatabase: SQL\nOperating Systems: Windows XP, 7, 8\nManagement Tool: HP Quality Center\nDefect Tracking Tool: JIRA\n\nProjects Summary:\n\nTechnologies: J2EE, Oracle 10g, Windows 7\nUsed Tools: Selenium, Core Java, HPQC and Eclipse.\nDuration: September 2014 to December 2014.\nProject Description:\nIn this project, we maintain all details about Hospital details like- Hospital address, Consultant\nDoctor, Doctor Details, Permanent Doctor, Medicine, Lab Test, In Patient, Out Patient etc.\nResponsibilities:\n• Prepared and Executed Test Cases as per Client Requirements.\n• Participated in identifying the test scenarios and designing the test cases.\n• Performed Manual Testing on some modules.\n\n\n\n• Feasibility analysis of Manual Test Cases.\n• Performed Functional, Compatibility testing on different browsers like Firefox & Chrome.\n• Defect tracking & Bug Reporting.","annotation":[{"label":["Graduation Year"],"points":[{"start":2934,"end":2937,"text":"2014"}]},{"label":["Graduation Year"],"points":[{"start":2917,"end":2920,"text":"2014"}]},{"label":["Skills"],"points":[{"start":2566,"end":2782,"text":"Tools: Manual Testing, Selenium (Selenium IDE, Selenium Web Driver), Eclipse IDE.\nLanguages: C, Core Java\nDatabase: SQL\nOperating Systems: Windows XP, 7, 8\nManagement Tool: HP Quality Center\nDefect Tracking Tool: JIRA"}]},{"label":["College Name"],"points":[{"start":2495,"end":2516,"text":"Padmavani High School\n"}]},{"label":["Graduation Year"],"points":[{"start":2473,"end":2476,"text":"2010"}]},{"label":["College Name"],"points":[{"start":2440,"end":2454,"text":"Vani Jr college"}]},{"label":["Graduation Year"],"points":[{"start":2418,"end":2421,"text":"2014"}]},{"label":["College Name"],"points":[{"start":2364,"end":2389,"text":"Kuppam Engineering College"}]},{"label":["Degree"],"points":[{"start":2356,"end":2361,"text":"B-Tech"}]},{"label":["Designation"],"points":[{"start":2308,"end":2330,"text":"Software Test Engineer\n"}]},{"label":["Graduation Year"],"points":[{"start":1436,"end":1439,"text":"2014"}]},{"label":["Graduation Year"],"points":[{"start":1419,"end":1422,"text":"2014"}]},{"label":["Designation"],"points":[{"start":1270,"end":1292,"text":"Software Test Engineer\n"}]},{"label":["Graduation Year"],"points":[{"start":1226,"end":1229,"text":"2014"}]},{"label":["Graduation Year"],"points":[{"start":1209,"end":1212,"text":"2014"}]},{"label":["Graduation Year"],"points":[{"start":1192,"end":1195,"text":"2014"}]},{"label":["Graduation Year"],"points":[{"start":1175,"end":1178,"text":"2014"}]},{"label":["Companies worked at"],"points":[{"start":1146,"end":1160,"text":"Infosys Limited"}]},{"label":["Designation"],"points":[{"start":1122,"end":1144,"text":"Software Test Engineer\n"}]},{"label":["Graduation Year"],"points":[{"start":1089,"end":1092,"text":"2014"}]},{"label":["Graduation Year"],"points":[{"start":1072,"end":1075,"text":"2014"}]},{"label":["Companies worked at"],"points":[{"start":1022,"end":1036,"text":"Infosys Limited"}]},{"label":["Companies worked at"],"points":[{"start":185,"end":199,"text":"Infosys Limited"}]},{"label":["Email Address"],"points":[{"start":63,"end":108,"text":"indeed.com/r/Madas-\nPeddaiah/557069069de72b14\n"}]},{"label":["Location"],"points":[{"start":15,"end":23,"text":"Anantapur"}]},{"label":["Name"],"points":[{"start":0,"end":13,"text":"Madas Peddaiah"}]}]}
{"content": "Madhuri Sripathi\nBanglore, Karnataka, Karnataka - Email me on Indeed: indeed.com/r/Madhuri-\nSripathi/04a52a262175111c\n\nAround 4 years of IT experience in analysis, testing and scripting on L2/L3 layer protocols and\ndesiging testcases and automating the same in TCL/TK and Python.\n\n• Expertise in Networking Protocols L2, L3 protocols, Manual, Performance, Platform, Regression\nand Automation Testing.\n• Experience in python scripting and PYATS framework.\n• Coordinating with onsite/offsite teams in resolving the defects found in Testing and working on\nqueries raised by customers..\n• Reviewing the automated scripts.\n• Exposure to Networking Protocols such as DHCP, OSPF, RIP, VLAN, STP/RSTP, LACP, TCP/IP,\nIPv4, Ipv6, Ethernet.\n• Automation in Python.\n• Excellent ability to plan, organize and prioritize my work to meet on time the deadlines of my\nclients and keep customer's satisfaction at the highest level possible.\n• Proven ability in quick understanding and learning of new technologies and their application\nin business solutions\n• Good debugging and problem solving skills with excellent understanding of system\ndevelopment methodologies, techniques and tools.\n• Highly motivated team member with strong communication, analytical and organizational\nskills.\n• Strong communication, interpersonal and analytical skills with proficiency at grasping new\nconcepts quickly and utilizing the same in a productive manner.\n• Willingness and ability to quickly adapt to new environment.\n• Good positive attitude and ability to learn new things independently.\n\n• Worked as Senior project engineer in Wipro Technologies, from Jan2014 to till date.\n\nLanguages: C\nNetwork Analysis Tools: QDDTS, GNS3, IXIA, SPIRENT, PAGENT\nRouting protocols VLAN, ETHECHANNELSTP, RSTP, RIP, EIGRP, OSPF, BGP, MPLS, L2VPN, L3VPN,\nIPSEC and MULTICAST.\nScripting Language Perl, Tcl/TK, Python\nTraffic Generators IXIA, PAGENT, SPIRENT\nManagement Protocols Telnet, SNMP\n\nWilling to relocate to: UAE - Dubai - abu-dabhi\n\nWORK EXPERIENCE\n\nSenior project engineer\n\nCisco -  Bengaluru, Karnataka -\n\nMarch 2014 to Present\n\nhttps://www.indeed.com/r/Madhuri-Sripathi/04a52a262175111c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Madhuri-Sripathi/04a52a262175111c?isid=rex-download&ikw=download-top&co=IN\n\n\nCisco 7600 is a router which supports both layer2 and layer3 protocols. It mainly deploys protocols\nlike MPLS and having specific modules to support the protocols IPSEC. Worked as system testing,\nperformance testing, stress testing and regression testing for all the IOS release on all layer 2\nand layer 3 protocols.\n\nWipro Technologies Limited (Bangalore, Karnataka)\nSenior project engineer (March 2014 till date)\n\nSenior software engineer\n\nWipro -\n\nFebruary 2014 to Present\n\nResponsibilities:\n• Responsible for regression and Manual testing of CISCO IOS -7600\n• Test case execution, test case results tracking, debugging, logging defects in CDETS,\nreproductions and fix verification.\n• Configuration and Testing on Routing protocols OSPF, BGP, OSPF, MPLS, L3VPN, L2VPN, IPSEC,\nQOS, SNMP and MULTICAST features on Cisco 7600 Routers.\n• Filed critical bugs of high severity, through root cause analysis and effective testing methods\nBug verification, Bug tracking, and documentation and review bug fixes.\n• Engaged in regression testing, filing bugs against Cisco IOS images to improve the quality of\nthe images and send weekly test reports.\n• Mentoring of new joiners in the team and conducting technical training sessions.\n• Responsibility for the 7600 platform customer queries (AT&T, Bharati, Vodafone, German IT,\netc )\n• Involved in Sev1, Sev2 and sev3 cases and MW related to L2/L3 Features.\n• Create a Local Repro of the issue which was raised by the customer.\n• Analyzed the customer issues and will provide the solutions to the customers\n• Worked with Developer to verify the DDTs fix for the customer Found Defects\n• System Testing on every New IOS build for the L2/L3 protocols.\n• Configuration and Testing on routing protocols\n• Working on Functionality, Scalability and Performance testing\n• Preparing of Test beds and topologies using Line cards - SIP200, SIP400, SIP600, ES+, ES20, GIG\nand TenGig Lancards, pagent, IXIA Traffic generators etc. to create customer setup in local Labs\n• Knowledge on TCL scripting and automated customer found issues into regression testing and\nalso able to troubleshoot the script issues.\n\nEDUCATION\n\nMaster degree in computer science in computer science\n\nPES college\n\nS.S.C in computer science\n\nRajah college\n\n\n\nSKILLS\n\nLINUX (4 years), UNIX (4 years), ospf (4 years), bgp (4 years), mpls (4 years), ipsec (4 years),\nmulticast (4 years), l2vpn (4 years), l3vpn (4 years), tcl (4 years), python (2 years)\n\nADDITIONAL INFORMATION\n\nOperating Systems: LINUX, UNIX\nOther protocols ARP, RARP, ICMP,ospf,bgp,mpls,l2vpn,l3vpn\nAutomaton tools: tcl,python","annotation":[{"label":["Skills"],"points":[{"start":4563,"end":4746,"text":"LINUX (4 years), UNIX (4 years), ospf (4 years), bgp (4 years), mpls (4 years), ipsec (4 years),\nmulticast (4 years), l2vpn (4 years), l3vpn (4 years), tcl (4 years), python (2 years)\n"}]},{"label":["College Name"],"points":[{"start":4538,"end":4551,"text":"Rajah college\n"}]},{"label":["Degree"],"points":[{"start":4511,"end":4535,"text":"S.S.C in computer science"}]},{"label":["College Name"],"points":[{"start":4498,"end":4508,"text":"PES college"}]},{"label":["Degree"],"points":[{"start":4443,"end":4495,"text":"Master degree in computer science in computer science"}]},{"label":["Companies worked at"],"points":[{"start":3353,"end":3357,"text":"Cisco"}]},{"label":["Companies worked at"],"points":[{"start":3110,"end":3114,"text":"Cisco"}]},{"label":["Companies worked at"],"points":[{"start":2737,"end":2741,"text":"Wipro"}]},{"label":["Designation"],"points":[{"start":2711,"end":2735,"text":"Senior software engineer\n"}]},{"label":["Designation"],"points":[{"start":2663,"end":2685,"text":"Senior project engineer"}]},{"label":["Companies worked at"],"points":[{"start":2613,"end":2617,"text":"Wipro"}]},{"label":["Companies worked at"],"points":[{"start":2295,"end":2299,"text":"Cisco"}]},{"label":["Companies worked at"],"points":[{"start":2037,"end":2041,"text":"Cisco"}]},{"label":["Designation"],"points":[{"start":2012,"end":2034,"text":"Senior project engineer"}]},{"label":["Companies worked at"],"points":[{"start":1600,"end":1604,"text":"Wipro"}]},{"label":["Designation"],"points":[{"start":1573,"end":1595,"text":"Senior project engineer"}]},{"label":["Years of Experience"],"points":[{"start":126,"end":131,"text":"4 year"}]},{"label":["Email Address"],"points":[{"start":62,"end":117,"text":"Indeed: indeed.com/r/Madhuri-\nSripathi/04a52a262175111c\n"}]},{"label":["Location"],"points":[{"start":17,"end":24,"text":"Banglore"}]},{"label":["Name"],"points":[{"start":0,"end":15,"text":"Madhuri Sripathi"}]}]}
{"content": "Mahesh Vijay\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Mahesh-Vijay/a2584aabc9572c30\n\nOver 6.5 years of functional enriched experience in ERP in the Procurement to Pay domain. Was\nassociated with Oracle India Pvt Ltd, Bangalore as Team lead - Supplier Data Management in\ntheir Global Financial Information Centre (Global Shared Service Center) for Oracle's Business\nfrom Sep 2007- Feb 2014.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nTeam lead - supplier data management\n\nOracle India -  Bangalore, Karnataka -\n\nMarch 2014 to December 2016\n\nManaging Partner of family business of Tours & Travels\n\nTeam Lead\n\nOracle India Pvt Ltd -\n\nOctober 2013 to February 2014\n\nSupplier Data Management\n\nLead Analyst -SME -Supplier Data Management\n\nOracle India Pvt Ltd -\n\nSeptember 2012 to October 2013\n\nSenior Analyst -Supplier Data Management\n\nOracle India Pvt Ltd -  Bengaluru, Karnataka -\n\nJanuary 2010 to September 2012\n\nAcademia\n• Bachelors in Commerce (B.Com) from Vivekananda Degree College, Bangalore\nUniversity(2007)\n• Pre University from Vivekananda PU College, Bangalore(2004)\n• Passed 10th STD from Angels High School, Bangalore(2002)\nFunctional & Technical Expertise\nFunctional\n- Ensuring data quality in Purchasing Supplier management (PSM) registry and Trading\nCommunity Architecture of the Oracle e-business suite bundle.\n\nhttps://www.indeed.com/r/Mahesh-Vijay/a2584aabc9572c30?isid=rex-download&ikw=download-top&co=IN\n\n\n- Managing all projects and ensuring the completion of the same within timeframe. Projects\nlike - Oracle Fusion Supplier Self Service, Supplier cleanup, migration of merger and acquisition\nsuppliers, UAT\n- Managing activities like synchronizing creation and updates of supplier records.\n- Oracle Fusion - Related to Procurement modules -Fusion Supplier Portal\n- Sound knowledge in the Oracle Financial applications domain that includes various cycles like\nthe Expense Reporting, Accounts Payables, Accounts Receivables, and Tactical Purchasing.\n- R12 User Acceptance Testing, writing test cases and author test reports which analyze the\nreported defects.\nTechnical\n- Oracle Applications Releases: 12, 11.5, Oracle Applications Modules Purchasing, iProcurement\n- Business Intelligence Reporting Tools: Oracle Discoverer & Accounting Software Tally 7.2\nProjects & Accomplishments\nOracle Master Data Management- Legacy Data Cleanup Project\nRole:\n• Global Country wise clean up initiative focusing on achieving a clean and accurate database\n• Supplier Information retrieval based on information in Purchase orders\nOracle Fusion UAT- Supplier Self Service\nRole:\n• Internal UAT-Part of upgrade team, testing all functionality and interfaces.\n• Monitoring the new add on features in Fusion and old features assigned\n\n11i to R12 Migration- Manual UAT\nRole:\n• Testing for Supplier creations, Merges, Tax set ups, Withholding and TDS code, Bank details etc\n• Monitoring the new add on features in R12 and old features assigned\n\nOracle Supplier Life Cycle Management (SLM) or Supplier Hub Project\nRole:\n• Internal UAT- testing all functionality and interfaces for creating a 360 degree view for each\nand every supplier.\n• Responsible for setting up suppliers' online, assisting requesters and suppliers to register a\nsupplier and iSupplier access. Testing fast and flexible supplier searches that can be made into\ntemplates resulting in quick report generation Create and test blended supplier records from\nmultiple sources\n\nSKILLS\n\nBCP (6 years), Data Governance (6 years), Data Management (6 years), Oracle (6 years),\nReporting Tools (6 years)\n\nADDITIONAL INFORMATION\n\nKey Skills\n• Process Management & Improvement\n• Operations & Team Management\n• Data Governance & Automation\n• Oracle E- Business Systems experience in Supplier Data/Vendor Data management\n• BCP Policies & Procedures\n\n\n\n• Desk Manuals/Business Process & Navigation Documentation\n• Business Ethics\n• Professional Communication\n• Reporting Tools & Microsoft Office Applications","annotation":[{"label":["Skills"],"points":[{"start":3822,"end":3977,"text":"\n• Desk Manuals/Business Process & Navigation Documentation\n• Business Ethics\n• Professional Communication\n• Reporting Tools & Microsoft Office Applications"}]},{"label":["Companies worked at"],"points":[{"start":3714,"end":3719,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":3535,"end":3540,"text":"Oracle"}]},{"label":["Skills"],"points":[{"start":3466,"end":3818,"text":"BCP (6 years), Data Governance (6 years), Data Management (6 years), Oracle (6 years),\nReporting Tools (6 years)\n\nADDITIONAL INFORMATION\n\nKey Skills\n• Process Management & Improvement\n• Operations & Team Management\n• Data Governance & Automation\n• Oracle E- Business Systems experience in Supplier Data/Vendor Data management\n• BCP Policies & Procedures"}]},{"label":["Companies worked at"],"points":[{"start":2962,"end":2967,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":2554,"end":2559,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":2322,"end":2327,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":2245,"end":2250,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":2151,"end":2156,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":2111,"end":2116,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1829,"end":1834,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1733,"end":1738,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1542,"end":1547,"text":"Oracle"}]},{"label":["Email Address"],"points":[{"start":1358,"end":1399,"text":"indeed.com/r/Mahesh-Vijay/a2584aabc9572c30"}]},{"label":["Companies worked at"],"points":[{"start":1313,"end":1318,"text":"Oracle"}]},{"label":["Graduation Year"],"points":[{"start":1089,"end":1092,"text":"2004"}]},{"label":["College Name"],"points":[{"start":1055,"end":1076,"text":"Vivekananda PU College"}]},{"label":["Graduation Year"],"points":[{"start":1027,"end":1030,"text":"2007"}]},{"label":["College Name"],"points":[{"start":978,"end":1025,"text":"Vivekananda Degree College, Bangalore\nUniversity"}]},{"label":["Degree"],"points":[{"start":943,"end":972,"text":"Bachelors in Commerce (B.Com) "}]},{"label":["Location"],"points":[{"start":876,"end":884,"text":"Bengaluru"}]},{"label":["Companies worked at"],"points":[{"start":852,"end":857,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":810,"end":849,"text":"Senior Analyst -Supplier Data Management"}]},{"label":["Companies worked at"],"points":[{"start":754,"end":759,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":709,"end":752,"text":"Lead Analyst -SME -Supplier Data Management\n"}]},{"label":["Companies worked at"],"points":[{"start":628,"end":633,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":492,"end":497,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":454,"end":463,"text":"Team lead "}]},{"label":["Graduation Year"],"points":[{"start":389,"end":392,"text":"2007"}]},{"label":["Companies worked at"],"points":[{"start":362,"end":367,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":245,"end":254,"text":"Team lead "}]},{"label":["Companies worked at"],"points":[{"start":210,"end":215,"text":"Oracle"}]},{"label":["Years of Experience"],"points":[{"start":105,"end":113,"text":"6.5 years"}]},{"label":["Email Address"],"points":[{"start":56,"end":97,"text":"indeed.com/r/Mahesh-Vijay/a2584aabc9572c30"}]},{"label":["Location"],"points":[{"start":13,"end":21,"text":"Bengaluru"}]},{"label":["Name"],"points":[{"start":0,"end":11,"text":"Mahesh Vijay"}]}]}
{"content": "Manisha Bharti\nSoftware Automation Engineer\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Manisha-Bharti/3573e36088ddc073\n\n• 3.5 years of professional IT experience in Banking and Finance domain and currently working\nas Software\nAutomation Engineer in Infosys Limited, Pune.\n• Have experience in accounts and customers domain in banking.\n• Woking on SOA technology.\n• Hands on experience of 2+ years in Oracle 11g\n• 2.9 years of professional experience in Middleware Testing and\nFunctional Testing\n• 4 months of experience with UiPath.\n• Experience on GUI and API testing on HP UFT\n• Working on agile methodology where involved as a senior tester.\n• Involved in various STLC stages including Test Planning, Test analysis, Test Execution, Defect\nmanagement and\nTest reporting.\n• Possess sound knowledge of SQL, STLC, Testing Procedures, HP ALM, HP UFT, HP SV, SOAP\nUI, JIRA, JENKINS, CICD, UiPath.\n• Involved in various client presentation.\n\nTraining & Achievement\n\nTitle: Infosys E&R Training\nDescription: Has undergone E&R training in Infosys Limited (Mysore) in Microsoft. Net Stream.\nThere I had been explored SQL,\nRDBMS, OOPS, Mainframes, Software Testing and Software Engineering. Has been trained in\nAutomation Testing\nTools used- Eclipse, UFT, RPT, SQL Server Studio\nReceived two times FS INSTA award from Infosys for excellence in work in automation and team\nsupport Got Appreciation from Project Manager for root cause analysis of defects\nGot Client Appreciations for successful execution in releases. ( Almost 240 service operations go\nlive in a year.)\n\nWilling to relocate to: Pune, Maharashtra\n\nWORK EXPERIENCE\n\nNOT WORKING\n\nSoftware Automation Engineer\n\nInfosys Limited -\n\nAugust 2014 to July 2017\n\n->Worked as an software automation tester more than 3 years.\n->Working experience in Agile methodology.\n\nhttps://www.indeed.com/r/Manisha-Bharti/3573e36088ddc073?isid=rex-download&ikw=download-top&co=IN\n\n\n->Robotic Process automation certified.(UiPath)\n->Involved in CICD implementation in projects.\n->Having strong knowledge about HP UFT/QTP,HP ALM/QC,JIRA.JENKINS,SQL.\n\nSystem Engineer Trainee\n\nInfosys Limited -\n\nFebruary 2014 to July 2014\n\nCORE COMPETENCIES\nTechnology:\nService Oriented Architecture\n(SOA) Languages: •\n• SQL (Oracle DB) •\n• VB Scripting •\n.NET •\nSTLC:\n• Test Planning •\n• Requirement Analysis •\n• Test Scenario •\n• Test Case Preparation •\n• Test Case Execution •\n• Defect Logging •\nTesting:\n• Functional Testing •\n• Middleware Testing •\nRegression Testing •\nGUI testing & API testing•\nLanguages VB Scripting, JAVA\n\nWeb Technologies ASP.Net, XML, HTML\nDatabases SQL Server 2008/2005, ORACLE 11g\n\nDatabase Connectivity ODBC\nDistributed Computing Web Services, API, Windows Services\nModelling Tools Microsoft Vision\n\nEDUCATION\n\nB.Tech in CSE\n\nMeghnad saha institute of technology\n\n2013\n\nSKILLS\n\nUft/qtp,alm/qc,jira,jenkins,automation testing,cicd,service vitualization,uipath\n\n\n\nADDITIONAL INFORMATION\n\nOperating Systems Windows 10 / 8 / 7 / Vista / XP\n\nDomains Banking and Finance\n\nFrameworks Data driven framework, Keyword driven framework\n\nTools HP-UFT, HP-SV, HP-ALM/QC, SOAP UI, JENKINS, UiPath\n\nMethodologies STLC, Agile and waterfall.\n\nProject Management Tools JIRA\n\nTECHNICAL SKILLS\n\n• 2.5+years of professional experience in SQL.\n• Has hands on experience on Oracle DB (Oracle 11g)\n• Has extensive knowledge of Testing Procedures and various\nphases of Testing Has 2+ years of experience on QC/ALM\n• Has 2+ years working experience in API & GUI testing\nusing HP UFT. Has 4 months of experience with uiPath\n• Has 2+ years working experience in SOAP UI.\n• Has 1.5+ year working experience on service virtualization using HP SV tool.\n• Has 6 months working experience in JIRA during work under agile methodology.\n• Has undergone Infosys Training in .Net Testing.\n• Has knowledge about CICD (Continuous integration and continuous delivery)\n\nPROJECT UNDERTAKEN\n\nDI-Middleware testing (August 14 -\nJuly 17) Domain- Accounts and customer.\nClient- ABN AMRO Bank (Netherland's bank)\nProject Name- ESB (Enterprise service bus)\n• Tools- ALM, SQL Developer, HP UFT, HP SV, SOAP UI, JENKINS, JIRA.\nIn this project, we were validating end to end communication of consumer & provider via ESB.\nWhat consumer actually\nsent to the Provider and how provider responds to the consumer. Testing included System\nIntegration Testing,\nRegression Testing, GUI Testing and Reports.\n\nResponsibilities-\n\nAutomation work\n• Preparing automation scripts using HP UFT tool where focus on Middleware logging as per the\nESB behavior.\n• Integrating all automation scripts with the ALM so that on one click we are able to execute test\ncases and collecting all\ntest results and logged defects in ALM without any manual efforts.\nManual Work -\n\n\n\n• Requirement analysis and Test Planning.\n• Test Scenarios preparation for various functionalities as per the Requirement.\n• Test Cases Creation and their execution for various functionalities of ESB and different provider\nservices\n• Prioritization of test cases as per the business requirement\n• Test Data Preparation as per the requirement using HP ALM.\n• Defect logging in case of any unusual behavior of the solution.\n• Preparing Weekly Progress Reports.\n• Leading the defect call\n• Virtualizing services using HP SV tool and deploy on central server so that in downtime testing\nshould not be impacted.\nCICD-JENKINS:\n• Involve in Continuous integration and continuous deployment strategy, with the help of JENKINS\n& UFT (automation scri\npt integrated with HP ALM) successfully implemented for currently working project.\n\nTDM implantation in CICD pipeline.\n\nPresentation - Direct communication to clients:\n• Present my team and our work to the client directly. (including CICD and TDM job\nimplementation in the same)\n\nuiPath exposure within same project:\n• Convert existing/new projects which are using UFT for automation into uiPath based\nautomation.\n• Do the feasibility analysis for the conversion and come up with a plan to convert maximum\nartifacts with minimum\nefforts\n• Setup basic skeleton for the new project","annotation":[{"label":["Skills"],"points":[{"start":2833,"end":4399,"text":"Uft/qtp,alm/qc,jira,jenkins,automation testing,cicd,service vitualization,uipath\n\n\n\nADDITIONAL INFORMATION\n\nOperating Systems Windows 10 / 8 / 7 / Vista / XP\n\nDomains Banking and Finance\n\nFrameworks Data driven framework, Keyword driven framework\n\nTools HP-UFT, HP-SV, HP-ALM/QC, SOAP UI, JENKINS, UiPath\n\nMethodologies STLC, Agile and waterfall.\n\nProject Management Tools JIRA\n\nTECHNICAL SKILLS\n\n• 2.5+years of professional experience in SQL.\n• Has hands on experience on Oracle DB (Oracle 11g)\n• Has extensive knowledge of Testing Procedures and various\nphases of Testing Has 2+ years of experience on QC/ALM\n• Has 2+ years working experience in API & GUI testing\nusing HP UFT. Has 4 months of experience with uiPath\n• Has 2+ years working experience in SOAP UI.\n• Has 1.5+ year working experience on service virtualization using HP SV tool.\n• Has 6 months working experience in JIRA during work under agile methodology.\n• Has undergone Infosys Training in .Net Testing.\n• Has knowledge about CICD (Continuous integration and continuous delivery)\n\nPROJECT UNDERTAKEN\n\nDI-Middleware testing (August 14 -\nJuly 17) Domain- Accounts and customer.\nClient- ABN AMRO Bank (Netherland's bank)\nProject Name- ESB (Enterprise service bus)\n• Tools- ALM, SQL Developer, HP UFT, HP SV, SOAP UI, JENKINS, JIRA.\nIn this project, we were validating end to end communication of consumer & provider via ESB.\nWhat consumer actually\nsent to the Provider and how provider responds to the consumer. Testing included System\nIntegration Testing,\nRegression Testing, GUI Testing and Reports."}]},{"label":["Graduation Year"],"points":[{"start":2819,"end":2822,"text":"2013"}]},{"label":["College Name"],"points":[{"start":2781,"end":2817,"text":"Meghnad saha institute of technology\n"}]},{"label":["Degree"],"points":[{"start":2766,"end":2778,"text":"B.Tech in CSE"}]},{"label":["Designation"],"points":[{"start":1645,"end":1673,"text":"Software Automation Engineer\n"}]},{"label":["Location"],"points":[{"start":1596,"end":1600,"text":"Pune,"}]},{"label":["Years of Experience"],"points":[{"start":133,"end":141,"text":"3.5 years"}]},{"label":["Email Address"],"points":[{"start":85,"end":129,"text":"indeed.com/r/Manisha-Bharti/3573e36088ddc073\n"}]},{"label":["Location"],"points":[{"start":45,"end":49,"text":"Pune,"}]},{"label":["Designation"],"points":[{"start":15,"end":43,"text":"Software Automation Engineer\n"}]},{"label":["Name"],"points":[{"start":0,"end":13,"text":"Manisha Bharti"}]}]}
{"content": "Manjari Singh\nSenior Software Analyst - Accenture\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Manjari-Singh/fd072d33991401f0\n\n• Test Lead with more than 6.5 years of professional experience in TELECOM domain specifically\nin OSS applications including Order\nmanagement, Inventory Management, Service provisioning, Service Activation and Service\nAssurance\n• An accomplished QA Lead with extensive experience in Functional Testing, Regression Testing,\nUnit Testing, Integration Testing, Test strategy\ndefinition, Test Plan, Test Estimation, Test Cases/Scenarios, Test Procedures, Results and\ndocumentation\n• Proficient in Project management, requirements definition, software testing, SDLC, STLC, Agile,\nV-Model and Waterfall test methodologies\n• Worked as primary liaison with Business, QA, Development teams and Vendors for major, minor\nand emergency releases\n• Proficient in testing Web Based applications, Web Services, SOAP UI, High Speed Broadband\nand IPTV Testing (Modem, STBs, DSLAMs)\n• Expertise in handling and coordinating defect triage meetings, project meeting with various\nstakeholders in onshore-offshore model\n• A certified business analyst from BCS, The Chartered Institute for IT with proficiency in\nrequirement management and project definition\n\nWORK EXPERIENCE\n\nSenior Software Analyst\n\nAccenture -  Bengaluru, Karnataka -\n\nDecember 2017 to Present\n\nClient Leading telecommunication and digital entertainment service provider in US operating in\nfour segments: Business solutions,\nEntertainment Group, Consumer Mobility and International.\n• As an Agile Test Lead responsible for analyzing, estimating and sizing the user stories which\nwill help product owners to prioritize the story cards\n• Participating in project planning and scrum meetings to understand business and technical\nrequirements\nRoles & • Responsible for testing and validation of user stories, developing testing plans, and\nestimating project resources\nResponsibilities\n• Facilitate the resolution of testing roadblocks, ensure execution of QA deliverables and guiding\nteam members on agile\nstandards and best practices\n• Responsible for conducting smoke, functional, regression testing as well as ad-hoc testing of\nclient's B2B web application\n• Creating project specific documents including system understanding documents and\nrequirement traceability metrics\n\nhttps://www.indeed.com/r/Manjari-Singh/fd072d33991401f0?isid=rex-download&ikw=download-top&co=IN\n\n\nProject 1: ASPEN - Unified Communication\n• Role: Senior QA Lead\n• Key Responsibilities:\nProject\no Extensively collaborated with external Vendor to understand the new B2B Portal and\ndocumented the same\no Mentored/Trained new team members with Unified Communication concepts and functionality\nof the portal\no Responsible for leading a team to deliver test results for unified communication platform\no Lead Iteration Retrospective meetings to define problems, prioritize actions items and decide\nnext steps\n\nSenior Software Analyst\n\nAccenture -  Bengaluru, Karnataka -\n\nJune 2011 to November 2017\n\nOne of the leading telecommunication services and products provider in Canada providing wide\nrange of products and services\nClient\nincluding mobile telephony, internet access, voice entertainment, video and satellite television\n• As a Test Lead responsible for testing the End-to-End flow of Business Requirement\n• Develop test suite, test approach, test plan and high level scenarios for the project\n• To provide Project estimates, resource commitment for functional testing\n• Working with the Project Leads to establish time tables and agree on a QA plan for the\nimplementation\nRoles & • Lead defect triage calls, business reviews and training session to help subordinates\nproduce quality deliverables\nResponsibilities • Mentoring a testing team of 14 personnel through all phases of application\ndevelopment, ensuring that systems, products, and services meet or exceed organization/\nindustry quality standards and end user requirements\n• Sending daily status reports to the stakeholders regarding test progress and defect updates\n• Participating in risk and mitigation planning discussing the project planning and related risks\n• Assisting/leading GO-LIVE activities for smooth code delivery in short duration\nProject 1: Service Assurance Enablement\n• Role: QA Lead\n• Key Responsibilities:\no Worked as a QA prime on multi-million CAD$ project for assuring service quality and enabling\nglitch free service delivery\no Lead the QA team of 6 members both at onshore and offshore at different time periods\no As a Subject Matter expert, managed and delivered complex end to end data setup for AT,\nPT, UAT testing\n\nProject 2: HDM Upgrade and firmware testing of Customer Premises Equipment (CPE)\n• Role: QA Lead\n• Key Responsibilities:\no Owned and managed the only live lab environment as Onshore lead\n\n\n\no Performed network testing in established lab on DSLAMs, modems, STBs with various firmware\nversions\no Extensively collaborated with the external vendors and provided support to offshore team in\ntesting phase\nProjects\nProject 3: TV3- THOR Program\n• Role: QA Lead\n• Key Responsibilities:\no Worked as an Onshore QA lead for multiple agile releases during the launch of new next gen\nIPTV app\no Collaborated closely with project team to resolve the outstanding issues within strict timelines\no Performed Web based, Android & IOS testing for the new TV content app while collaborating\nwith the vendor team\n\nProject 4: Multiple HSIA projects like HSIA 250/250, Optik 150, Optik 50\n• Role: QA Lead\n• Key Responsibilities:\no Worked on requirements analysis, estimation, test planning, test approach, and defect\nmanagement for the release\no Collaborated with developers and BSAs to daily triage the defects logged during the testing\no Performed database testing for one of the activation tool and automated the process using\nWorksoft Certify\n\nQA\n\nAccenture -\n\nAugust 2015 to October 2016\n\nLead, worked on quality assurance and service enablement for multiple newly launched digital\napplications in live environment.\n\nAccenture -  Vancouver, BC -\n\n2015 to 2016\n\nEDUCATION\n\nB.TECH. in Information Technology\n\nAmity University -  Lucknow, Uttar Pradesh\n\n2011\n\nClass XII\n\nCanossa Convent Girls Inter College\n\n2007\n\nClass X\n\n\n\nCanossa Convent School\n\n2005\n\nSKILLS\n\nQA (7 years), TESTING (6 years), ESTIMATION (6 years), AMDOCS (Less than 1 year), BILLING\n(Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS AND COMPETENCIES\nDomain and Functional Expertise Tools and Languages:\n• Telecommunications and IPTV Systems • NetCracker, NetProvision, Insight (IISY), HDM (Nokia),\nTrouble Ticketing\n• Test/QA Lead • Click Schedule, FieldLink, AMDOCS-Ordering, Billing - Product Catalog\n• Project Management • IBM - TOCP, Mediaroom, MediaFirst TV3 platform (Ericsson)\n• Software Testing and Defect Management • HP QC 10, JIRA, TDP, Accenture Test Estimation Tool\n• Lead to Order and Order to Cash Management • Caliber RM (Requirement Gathering),\nConfluence, Clear Quest\n• Service Provisioning and Activation • Worksoft Certify, QTP, Java, Selenium, SQL, HTML, XML\n• Business Analysis (Requirement Management) • Splunk, WinSCP, SOAP UI, Kibana, Wireshark\n• Automation • Proficiency in MS Office (Word, Excel, PowerPoint and Visio)","annotation":[{"label":["Companies worked at"],"points":[{"start":6861,"end":6869,"text":"Accenture"}]},{"label":["Skills"],"points":[{"start":6305,"end":7257,"text":"QA (7 years), TESTING (6 years), ESTIMATION (6 years), AMDOCS (Less than 1 year), BILLING\n(Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS AND COMPETENCIES\nDomain and Functional Expertise Tools and Languages:\n• Telecommunications and IPTV Systems • NetCracker, NetProvision, Insight (IISY), HDM (Nokia),\nTrouble Ticketing\n• Test/QA Lead • Click Schedule, FieldLink, AMDOCS-Ordering, Billing - Product Catalog\n• Project Management • IBM - TOCP, Mediaroom, MediaFirst TV3 platform (Ericsson)\n• Software Testing and Defect Management • HP QC 10, JIRA, TDP, Accenture Test Estimation Tool\n• Lead to Order and Order to Cash Management • Caliber RM (Requirement Gathering),\nConfluence, Clear Quest\n• Service Provisioning and Activation • Worksoft Certify, QTP, Java, Selenium, SQL, HTML, XML\n• Business Analysis (Requirement Management) • Splunk, WinSCP, SOAP UI, Kibana, Wireshark\n• Automation • Proficiency in MS Office (Word, Excel, PowerPoint and Visio)"}]},{"label":["College Name"],"points":[{"start":6267,"end":6289,"text":"Canossa Convent School\n"}]},{"label":["Graduation Year"],"points":[{"start":6250,"end":6253,"text":"2007"}]},{"label":["College Name"],"points":[{"start":6213,"end":6248,"text":"Canossa Convent Girls Inter College\n"}]},{"label":["Graduation Year"],"points":[{"start":6196,"end":6199,"text":"2011"}]},{"label":["College Name"],"points":[{"start":6152,"end":6168,"text":"Amity University "}]},{"label":["Degree"],"points":[{"start":6117,"end":6150,"text":"B.TECH. in Information Technology\n"}]},{"label":["Companies worked at"],"points":[{"start":6062,"end":6070,"text":"Accenture"}]},{"label":["Companies worked at"],"points":[{"start":5892,"end":5900,"text":"Accenture"}]},{"label":["Graduation Year"],"points":[{"start":3029,"end":3032,"text":"2011"}]},{"label":["Location"],"points":[{"start":3000,"end":3008,"text":"Bengaluru"}]},{"label":["Companies worked at"],"points":[{"start":2987,"end":2995,"text":"Accenture"}]},{"label":["Designation"],"points":[{"start":2962,"end":2984,"text":"Senior Software Analyst"}]},{"label":["Location"],"points":[{"start":1330,"end":1338,"text":"Bengaluru"}]},{"label":["Companies worked at"],"points":[{"start":1317,"end":1325,"text":"Accenture"}]},{"label":["Designation"],"points":[{"start":1292,"end":1314,"text":"Senior Software Analyst"}]},{"label":["Email Address"],"points":[{"start":94,"end":137,"text":"indeed.com/r/Manjari-Singh/fd072d33991401f0\n"}]},{"label":["Location"],"points":[{"start":51,"end":59,"text":"Bengaluru"}]},{"label":["Companies worked at"],"points":[{"start":40,"end":48,"text":"Accenture"}]},{"label":["Designation"],"points":[{"start":14,"end":36,"text":"Senior Software Analyst"}]},{"label":["Name"],"points":[{"start":0,"end":12,"text":"Manjari Singh"}]}]}
{"content": "Mohamed Ameen\nSystem engineer\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Mohamed-Ameen/\nba052bfa70e4c0b7\n\nI am looking for a job opportunity as a System Engineer that gives me professional growth and\nexcellence and enables me to contribute my efforts in the success of the organization.\n\nWORK EXPERIENCE\n\nIT Operations Analyst\n\nAccenture\n\nI am looking for a job as system engineer that gives me professional growth and excellence and\nenables me to contribute my efforts in the success of the organization.\n\ntechnical support engineer\n\nConvergys for Microsoft -\n\nNovember 2014 to November 2015\n\nCurrently working with Accenture as a Subject Matter Expert for the Remote Technology Support\nteam in IT Operations.\n\nEDUCATION\n\nB.E in Electronics & Communication\n\nVisveswaraiah Technological University -  Bengaluru, Karnataka\n\n2013\n\nElectronics Project\n\nAl-Ameen PU College\n\nRajiv Gandhi Institute of Technology\n\nSKILLS\n\nActive Directory (2 years), Microsoft office, Windows,End user computing (3 years)\n\nCERTIFICATIONS/LICENSES\n\nCCNA\n\nCCNA certified\n\nhttps://www.indeed.com/r/Mohamed-Ameen/ba052bfa70e4c0b7?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Mohamed-Ameen/ba052bfa70e4c0b7?isid=rex-download&ikw=download-top&co=IN\n\n\nADDITIONAL INFORMATION\n\nSkill set:\n\nNetworking:\n\n➢ Knowledge of OSI and TCP/IP architecture.\n➢ Knowledge of Routing Protocols.\n\n➢ Knowledge of Virtual Private Network and IPv6.\n\n➢ Wireless LAN\n\n➢ Installing, configuration & Troubleshooting of Operating System such as Windows client (XP,\nVista, 7, 8x and 10)\n\n➢ Installing, configuration & Troubleshooting Microsoft Office {2013, 2016, 365 pro plus (Outlook\nand Other Office Tools Such as Excel, word, Power Point) }\n\n➢ Configuring and Troubleshooting Microsoft outlook in Blackberry, iPhone & iPad.\n\n➢ Configuring Group Policies in Domain & Work Group Environment.\n\n➢ Create, modify and Manage User Account in AD Server.\n\n➢ Creating Folder groups in File Server and providing Network folder access.\n\n➢ Configuring & Troubleshooting Wireless Network & LAN, WAN Network,\n\n➢ Installing and Configuration of VPN clients.\n\n➢ Workgroup and Domain level security policies.\n\n➢ Migration and Up gradation of Server/Desktops.\n\n➢ Installation and Troubleshooting of Symantec Endpoint Antivirus.\n\n➢ Installation and Troubleshooting of Avecto Defend point client.\n\n➢ Managing & Troubleshooting Printer, Scanner, Fax.\n\n➢ Configuring Managing and Troubleshooting SCCM on End user machines.\n\n➢ Perform 1st Level troubleshooting and/or escalate as appropriate issue to warranty Vendors.\n\n➢ Maintain Inventory of all warranty / AMC assets for the current year.\n\n➢ Maintain an Inventory of parts for emergency repairs.\n\n➢ Coordinate with vendors and with company personnel to facilitate purchases.\n\n\n\n➢ Working on Web Tickets Tools.\n\n➢ Handling Escalation and Severity for Incidents.\n\nOperating System:\n\n➢ Windows 7, Windows 8, Windows 8.1 and Windows 10.\n\nApplication:\n\n➢ MS Office, Service Now, ITSM, LogMeIn Rescue","annotation":[{"label":["Graduation Year"],"points":[{"start":1632,"end":1635,"text":"2013"}]},{"label":["Skills"],"points":[{"start":931,"end":1012,"text":"Active Directory (2 years), Microsoft office, Windows,End user computing (3 years)"}]},{"label":["College Name"],"points":[{"start":885,"end":920,"text":"Rajiv Gandhi Institute of Technology"}]},{"label":["College Name"],"points":[{"start":864,"end":882,"text":"Al-Ameen PU College"}]},{"label":["Graduation Year"],"points":[{"start":837,"end":840,"text":"2013"}]},{"label":["Location"],"points":[{"start":815,"end":823,"text":"Bengaluru"}]},{"label":["College Name"],"points":[{"start":773,"end":810,"text":"Visveswaraiah Technological University"}]},{"label":["Degree"],"points":[{"start":737,"end":771,"text":"B.E in Electronics & Communication\n"}]},{"label":["Companies worked at"],"points":[{"start":631,"end":639,"text":"Accenture"}]},{"label":["Companies worked at"],"points":[{"start":342,"end":350,"text":"Accenture"}]},{"label":["Designation"],"points":[{"start":319,"end":339,"text":"IT Operations Analyst"}]},{"label":["Location"],"points":[{"start":74,"end":118,"text":"indeed.com/r/Mohamed-Ameen/\nba052bfa70e4c0b7\n"}]},{"label":["Location"],"points":[{"start":31,"end":39,"text":"Bengaluru"}]},{"label":["Designation"],"points":[{"start":14,"end":28,"text":"System engineer"}]},{"label":["Name"],"points":[{"start":0,"end":12,"text":"Mohamed Ameen"}]}]}
{"content": "Mohini Gupta\nServer Support Engineer\n\nGurgaon, Haryana - Email me on Indeed: indeed.com/r/Mohini-Gupta/08b5b8e1acd8cf07\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nServer Support Engineer\n\nMicrosoft -\n\nJuly 2015 to November 2017\n\nKey Responsibilities:\n● Worked as Technical Support Engineer for Microsoft Enterprise Platforms Support.\n\n● Worked in U.S., U.K., India & APAC time zones.\n\n● Always available (24X7) for any explanation, support or information required by team, client\nand managers.\n\n● Configuring, deploying and troubleshooting SCCM with remote/local SQL.\n● Perform Software distribution and ensure successful deployment on end assets.\n● Patch management of servers and end user estate along with troubleshooting.\n● Performed on checks through server, to perform server operations, check services, analyzed\nthe logs to check the communication from the new server to the primary server and vice versa,\nalso checked server's communication with its client.\n● Setting up new packages along with new collection.\n● Collection of inventory i.e. hardware inventory and software inventory.\n● Troubleshooting client connectivity and package installation issues by analysis of logs.\n● Working on incidents logged by end users as a daily activity.\n● Fixing operational issues and performing installation or un-installation of applications.\n● Create new groups, add users and grant permissions.\n● Good understanding of SCCM architecture, operations and management.\n● Knowledge of Active Directory and networking required in SCCM environment.\n● Deploying Operating System with SCCM.\n\nServer Support Engg.\n\nConvergys -\n\nJuly 2015 to November 2017\n\nServer Support Engg.\n\nEDUCATION\n\nB.tech\n\nhttps://www.indeed.com/r/Mohini-Gupta/08b5b8e1acd8cf07?isid=rex-download&ikw=download-top&co=IN\n\n\nKIIT college of Engg.\n\nSKILLS\n\nactive directory, iis, sccm, dhcp, sql, wsus, dns\n\nADDITIONAL INFORMATION\n\nComputer Skills\n● MS Office Tools: MS Excel, MS Word, MS Power Point.\n● Hands on experience on all versions of Windows.\n● Sound knowledge of internet and networking.\n● Coding Languages: C, C++, Java.\n\nOther Information\n● Regular Swimmer.\n● Interested in playing Table Tennis, Lawn Tennis.\n● Professional Proficiency: English and Hindi\n\nI hereby declare that all the above particulars are true to the best of my knowledge.\n\nPLACE: Gurgaon (MOHINI GUPTA)","annotation":[{"label":["Location"],"points":[{"start":2326,"end":2332,"text":"Gurgaon"}]},{"label":["Skills"],"points":[{"start":1821,"end":2094,"text":"active directory, iis, sccm, dhcp, sql, wsus, dns\n\nADDITIONAL INFORMATION\n\nComputer Skills\n● MS Office Tools: MS Excel, MS Word, MS Power Point.\n● Hands on experience on all versions of Windows.\n● Sound knowledge of internet and networking.\n● Coding Languages: C, C++, Java."}]},{"label":["College Name"],"points":[{"start":1790,"end":1810,"text":"KIIT college of Engg."}]},{"label":["Email Address"],"points":[{"start":1704,"end":1745,"text":"indeed.com/r/Mohini-Gupta/08b5b8e1acd8cf07"}]},{"label":["Degree"],"points":[{"start":1684,"end":1689,"text":"B.tech"}]},{"label":["Companies worked at"],"points":[{"start":300,"end":308,"text":"Microsoft"}]},{"label":["Companies worked at"],"points":[{"start":194,"end":202,"text":"Microsoft"}]},{"label":["Designation"],"points":[{"start":169,"end":191,"text":"Server Support Engineer"}]},{"label":["Email Address"],"points":[{"start":77,"end":118,"text":"indeed.com/r/Mohini-Gupta/08b5b8e1acd8cf07"}]},{"label":["Location"],"points":[{"start":38,"end":44,"text":"Gurgaon"}]},{"label":["Designation"],"points":[{"start":13,"end":35,"text":"Server Support Engineer"}]},{"label":["Name"],"points":[{"start":0,"end":11,"text":"Mohini Gupta"}]}]}
{"content": "Navas Koya\nTest Engineer\n\nMangalore, Karnataka - Email me on Indeed: indeed.com/r/Navas-Koya/23c1e4e94779b465\n\nWilling to relocate to: Mangalore, Karnataka - Bangalore, Karnataka - Chennai, Tamil Nadu\n\nWORK EXPERIENCE\n\nSystem Engineer\n\nInfosys -\n\nAugust 2014 to Present\n\n.NET application Maintenance and do the code changes if required\n\nTest Engineer\n\nInfosys -\n\nJune 2015 to February 2016\n\nPrProject 2:\n\nTitle: RBS W&G Proving testing.\nTechnology: Manual testing\nRole: Software Test Engineer\n\nDomain: Banking\nDescription:\n\nWrite test cases & descriptions. Review the entries. Upload and map the documents into\nHP QC. Execute the testing operations in TPROD mainframe. Upload the result in QC along with\nthe proof.\nRoles and Responsibilities:\n•Prepared the Test Scenarios\n\n•Prepared and Executed Test Cases\n•Performed functional, Regression testing, Sanity testing.\n\n•Reviewed the Test Reports and Preparing Test Summary Report.\n•Upload Test cases to the QC.\n•Execute in TPROD Mainframe.\n•Defect Track and Report.\n\nTest Executive\n\nInfosys Limited -\n\nAugust 2014 to May 2015\n\nhttps://www.indeed.com/r/Navas-Koya/23c1e4e94779b465?isid=rex-download&ikw=download-top&co=IN\n\n\nProject 1:\nTitle: CAWP (Compliance Automated Work Paper)\n\nTechnology: Manual testing\nRole: Software Test Executive\nDomain: Banking\nDescription:\nThe Admin can create and maintain annual test plan, and users can only view and add\ndetails. Testers will get Business Requirement which explains the flows and Functional\nrequirements which gives the full detail of the project.\nRoles and Responsibilities:\n\n•Prepared the Test Scenarios\n•Prepared and Executed Test Cases\n•Performed functional, Regression testing, Sanity testing.\n•Reviewed the Test Reports and Preparing Test Summary Report.\n•Defect Track and Report.\n\nEDUCATION\n\nBachelor of Computer Applications\n\nMangalore University, Mangalore\n\nJune 2011 to April 2014\n\nSKILLS\n\nC# (Less than 1 year), .NET, SQL Server, Css, Html5\n\nADDITIONAL INFORMATION\n\nBachelor of computer application: with 74% from Milagres College, Kallianpur under\nMangalore University, Karnataka.\n\nNavas Najeer Koya 2\n\nSKILL SET • ASP.NET, C# • QA tools\n\n• Coding and modularization • Excellent communication skills\n\n• VB, VB.net, ASP • Technical specifications creation\n\n• HTML • System backups\n\n• Sql server 2005, Oracle • System upgrades\n\n• Java/C/C++ • Excellent problem-solving abilities\n\nNavas Najeer Koya 3","annotation":[{"label":["Skills"],"points":[{"start":2110,"end":2403,"text":"SKILL SET • ASP.NET, C# • QA tools\n\n• Coding and modularization • Excellent communication skills\n\n• VB, VB.net, ASP • Technical specifications creation\n\n• HTML • System backups\n\n• Sql server 2005, Oracle • System upgrades\n\n• Java/C/C++ • Excellent problem-solving abilities\n\nNavas Najeer Koya 3"}]},{"label":["Location"],"points":[{"start":2055,"end":2063,"text":"Mangalore"}]},{"label":["Skills"],"points":[{"start":1895,"end":1946,"text":"C# (Less than 1 year), .NET, SQL Server, Css, Html5\n"}]},{"label":["Graduation Year"],"points":[{"start":1880,"end":1884,"text":" 2014"}]},{"label":["Location"],"points":[{"start":1851,"end":1859,"text":"Mangalore"}]},{"label":["Location"],"points":[{"start":1829,"end":1837,"text":"Mangalore"}]},{"label":["Degree"],"points":[{"start":1794,"end":1825,"text":"Bachelor of Computer Application"}]},{"label":["Graduation Year"],"points":[{"start":1056,"end":1060,"text":" 2014"}]},{"label":["Companies worked at"],"points":[{"start":1031,"end":1037,"text":"Infosys"}]},{"label":["Designation"],"points":[{"start":479,"end":492,"text":"Test Engineer\n"}]},{"label":["Companies worked at"],"points":[{"start":352,"end":358,"text":"Infosys"}]},{"label":["Designation"],"points":[{"start":337,"end":350,"text":"Test Engineer\n"}]},{"label":["Graduation Year"],"points":[{"start":253,"end":257,"text":" 2014"}]},{"label":["Companies worked at"],"points":[{"start":236,"end":242,"text":"Infosys"}]},{"label":["Designation"],"points":[{"start":219,"end":233,"text":"System Engineer"}]},{"label":["Location"],"points":[{"start":135,"end":143,"text":"Mangalore"}]},{"label":["Location"],"points":[{"start":26,"end":34,"text":"Mangalore"}]},{"label":["Designation"],"points":[{"start":11,"end":24,"text":"Test Engineer\n"}]},{"label":["Name"],"points":[{"start":0,"end":9,"text":"Navas Koya"}]}]}
{"content": "Navjyot Singh Rathore\nUlhasnagar, Maharashtra - Email me on Indeed: indeed.com/r/Navjyot-Singh-Rathore/\nad92079f3f1a4cad\n\nWORK EXPERIENCE\n\nfresher job\n\nAccenture -  Ulhasnagar, Maharashtra\n\nFresher\n\nAny post\n\nEDUCATION\n\nTYBMS in Management Studies\n\nVedanta College of management and information technology -  Mumbai, Maharashtra\n\n2015 to 2018\n\nH.S.C\n\nGuru Nanak English High School and Jr cllg\n\n2013 to 2015\n\nS.S.C\n\nswami Vivekananda school\n\n2013\n\nSKILLS\n\nFresher\n\nADDITIONAL INFORMATION\n\n● Can switch to any environment within a short span\n● Dedication towards Hard work\n● Willingness to learn\n\nSKILLS\n\n● Basic Computers knowledge\n● Good Understanding of Business Ethics, Operational Research.\n● Completed Project Work on working capital with A+ Grade.\n\nhttps://www.indeed.com/r/Navjyot-Singh-Rathore/ad92079f3f1a4cad?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Navjyot-Singh-Rathore/ad92079f3f1a4cad?isid=rex-download&ikw=download-top&co=IN","annotation":[{"label":["Skills"],"points":[{"start":605,"end":752,"text":" Basic Computers knowledge\n● Good Understanding of Business Ethics, Operational Research.\n● Completed Project Work on working capital with A+ Grade."}]},{"label":["Graduation Year"],"points":[{"start":403,"end":406,"text":"2015"}]},{"label":["College Name"],"points":[{"start":351,"end":392,"text":"Guru Nanak English High School and Jr cllg"}]},{"label":["Degree"],"points":[{"start":344,"end":348,"text":"H.S.C"}]},{"label":["Graduation Year"],"points":[{"start":338,"end":341,"text":"2018"}]},{"label":["Graduation Year"],"points":[{"start":330,"end":333,"text":"2015"}]},{"label":["College Name"],"points":[{"start":249,"end":304,"text":"Vedanta College of management and information technology"}]},{"label":["Degree"],"points":[{"start":220,"end":246,"text":"TYBMS in Management Studies"}]},{"label":["Location"],"points":[{"start":165,"end":174,"text":"Ulhasnagar"}]},{"label":["Companies worked at"],"points":[{"start":152,"end":160,"text":"Accenture"}]},{"label":["Designation"],"points":[{"start":139,"end":149,"text":"fresher job"}]},{"label":["Email Address"],"points":[{"start":68,"end":119,"text":"indeed.com/r/Navjyot-Singh-Rathore/\nad92079f3f1a4cad"}]},{"label":["Location"],"points":[{"start":22,"end":31,"text":"Ulhasnagar"}]},{"label":["Name"],"points":[{"start":0,"end":20,"text":"Navjyot Singh Rathore"}]}]}
{"content": "Nazish Alam\nConsultant - SAP ABAP\n\nGhaziabad, Uttar Pradesh - Email me on Indeed: indeed.com/r/Nazish-Alam/\nb06dbac9d6236221\n\nWilling to relocate to: Delhi, Delhi - Noida, Uttar Pradesh - Lucknow, Uttar Pradesh\n\nWORK EXPERIENCE\n\nConsultant\n\nSAP ABAP -  Noida, Uttar Pradesh -\n\nNovember 2016 to Present\n\nCredence Systems, Noida\n\nCredence Systems is IT Infrastructure Management Company, offers end-to-end solutions.\nCombining deep domain expertise with new technologies and a cost effective on-site/ offshore\nmodel. Helping companies integrate key business processes, improving their operational\nefficiencies and extracting, better business value from their investment.\n\nPROJECT UNDERTAKEN\nClient ECC Version Role and Responsibilities\nWelspun Group Plate & Coil Mills Division\nSAP ECC 6.0\n\nConsultant\n\nSAP ABAP -\n\nJanuary 2016 to Present\n\nReports:\n• Designed technical program specifications based on business requirements.\n• Generated basic lists and Interactive Reports for information in the MM/SD including Sales,\nBilling, Purchasing, Goods Received, Inspection Plan, and Batch Determination using ABAP\nprograms, Screen, Report Painter and Menu Painters. Used Parameters, Select-options and Match\nCodes to make the reports more friendly and intuitive to the user.\n• Generated different kind of reports like for PR (Purchase Requisition) analysis using ALV, PO\n(Purchase Order) Pricing details, Pending Export Sales order etc.\n• Developed report for the daily production done.\nSAP Scripts:\n• Generated various client specific Layout sets and form letters using SAP Script.\n• Involved in modification of SAP scripts for Purchase orders (MEDRUCK) and indents, Delivery\nnotes (RVDELNOTE), and Invoices (RVINVOICE) according to customer needs.\n• Modified existing layout sets for Purchase Order and GR using SAP Script.\nData Migration:\n\nhttps://www.indeed.com/r/Nazish-Alam/b06dbac9d6236221?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Nazish-Alam/b06dbac9d6236221?isid=rex-download&ikw=download-top&co=IN\n\n\n• Implemented both Call Transaction and Session Method of BDC accordingly, depending upon\nthe size, type, state and created routines for data upload using data extracts for sequential files\non the application server and UPLOAD/WS_UPLOAD for local files on the presentation server.\n• Wrote ABAP programs for extracting data from SAP tables (Vendor master, Purchase Orders,\nInvoices and remittance) to be transferred to vendors using non-SAP systems for reconciliation\nand their local use.\nObject Oriented:\n• Created local and global classes with SE24 and within programs.\n• Used the Standard ALV classes in OOPs ALV reports.\n• Used ABSTRACT classes and Interfaces.\n• Having knowledge and used the different object oriented concepts technically.\n\nSKILLS\n\nSAP (2 years), ABAP (2 years), ADBC (Less than 1 year), C++ (Less than 1 year), DATA\nMODELING (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nOTHER TECHNICAL SKILLS\n• Trained on SAP S4 HANA.\n• Having knowledge of Code Push down, CDS view and it's consumption in ABAP.\n• Data Modeling, creation of different type of views.\n• AMDP.\n• ADBC connectivity.\n• Familiar with SQL, DDL, DML syntaxes.\n• Work on Windows 7, Windows XP, Windows 8, Windows 10 OS, can work on C, C++\nACADEMEIC CREDENTIALS\n2015 Master of Computer Application\nUPTU. India","annotation":[{"label":["College Name"],"points":[{"start":3303,"end":3313,"text":"UPTU. India"}]},{"label":["Degree"],"points":[{"start":3272,"end":3301,"text":"Master of Computer Application"}]},{"label":["Graduation Year"],"points":[{"start":3267,"end":3270,"text":"2015"}]},{"label":["Skills"],"points":[{"start":2941,"end":3243,"text":"• Trained on SAP S4 HANA.\n• Having knowledge of Code Push down, CDS view and it's consumption in ABAP.\n• Data Modeling, creation of different type of views.\n• AMDP.\n• ADBC connectivity.\n• Familiar with SQL, DDL, DML syntaxes.\n• Work on Windows 7, Windows XP, Windows 8, Windows 10 OS, can work on C, C++"}]},{"label":["Skills"],"points":[{"start":2780,"end":2891,"text":"SAP (2 years), ABAP (2 years), ADBC (Less than 1 year), C++ (Less than 1 year), DATA\nMODELING (Less than 1 year)"}]},{"label":["Companies worked at"],"points":[{"start":801,"end":808,"text":"SAP ABAP"}]},{"label":["Designation"],"points":[{"start":789,"end":798,"text":"Consultant"}]},{"label":["Companies worked at"],"points":[{"start":241,"end":248,"text":"SAP ABAP"}]},{"label":["Designation"],"points":[{"start":229,"end":238,"text":"Consultant"}]},{"label":["Email Address"],"points":[{"start":82,"end":123,"text":"indeed.com/r/Nazish-Alam/\nb06dbac9d6236221"}]},{"label":["Location"],"points":[{"start":35,"end":43,"text":"Ghaziabad"}]},{"label":["Companies worked at"],"points":[{"start":25,"end":32,"text":"SAP ABAP"}]},{"label":["Designation"],"points":[{"start":12,"end":21,"text":"Consultant"}]},{"label":["Name"],"points":[{"start":0,"end":10,"text":"Nazish Alam"}]}]}
{"content": "Nidhi Pandit\nTest Engineer - Infosys Limited\n\n- Email me on Indeed: indeed.com/r/Nidhi-Pandit/b4b383dbe14789c5\n\nOverall around 4 years of work experience - Currently working with Infosys Limited designated\nas Test\nEngineer. Experience in Automation and Manual Testing in telecom and banking domain.\n\nWORK EXPERIENCE\n\nTest Engineer\n\nInfosys Limited -\n\nJune 2016 to Present\n\nProject Description:\nCBIL (Core Banking Integration Layer) is a crucial integration layer that is specifically addressing\nthe architectural complexity due to multiple core banking systems and variants at HSBC. It is a\nstandard service\ninterface across different core banking systems to facilitate easy integration with other global\nsystems.\nCBIL is a strategic initiative to standardize all interfaces with core banking without impacting the\nunderlying\ncore banking system.\nRoles & Responsibilities:\n\n• Understanding the functional requirements of the API.\n• Involvement in Test Planning.\n• Verifying the documents provided by the development team.\n• Creating test data request document to test the service on the certified environment.\n• Create and automate Test Cases.\n• Preparing Stub to virtualize the API.\n• Executing test cases in local and certified environments and validating the responses.\n• Participation in Stand up Calls, Scrum Calls, Sprint Planning, Retrospective Meetings.\n• Defect Management in JIRA.\n• Involvement in Automation Scripting.\n• Presenting completed APIs to the client.\n• Experience in working under client environment, multi-vendor environment.\n\nTest Engineer\n\nInfosys Limited -\n\nFebruary 2014 to Present\n\nTest Engineer\n\nInfosys Limited -\n\nhttps://www.indeed.com/r/Nidhi-Pandit/b4b383dbe14789c5?isid=rex-download&ikw=download-top&co=IN\n\n\nJuly 2014 to January 2016\n\nProject Description:\nOrder Management Fulfillment (OMFUL), which belongs to Telecommunication Domain, is a\nunified\nbusiness process management framework that orchestrates, automates and manages the\nservice\nfulfillment process, aligning people, processes and technology. This product caters the end to end\nfunctionality for telecom services in OSS space. Our role as a team was to ensure any Initiate/\nChange\nRequest towards the product is delivered successfully in time with no compromise in quality.\nRoles & Responsibilities:\n\n• Understanding the client requirement\n• Creating SQL scripts and deploying on the local environment (UNIX)\n• Sanity testing on different environments\n• Performing Manual Testing on OMFUL Application\n• Creating and maintaining test cases as per the requirement\n• Run and validate the test cases in the system which is integrated in a real production like\nenvironment\n• Creating manual stubs to complete the process fulfillment flow\n• Defect Management\n• Creating Show And Tell Related documents\n• Participation in support team at the time of Production Deployment\n\nEDUCATION\n\nState Board\n\n2008\n\nEducation\n\nPassing\n\nBachelor in Electronics\n\nCentral India Institute\n\nEngineering\n\nTechnical University\n\nSKILLS\n\nAPI. (1 year), Scripting. (1 year), SOAP (1 year), UI (1 year), XML (3 years)\n\nADDITIONAL INFORMATION\n\nKey Technical Skills\n\n\n\nTechnical Experience: - Automation Testing (REST API, Service Virtualization), Functional Testing,\nRegression Testing\nManual Testing, Scripting (SQL)\nDomain Experience: - Telecom, Banking\nProgramming & Scripting Languages: - HTML, CSS, XML, SQL, JAVA (Basic), JSON\nSDLC Model: -Waterfall, Agile\nTesting Tools: - CA LISA, APM (Amdocs Process Manager), SOAP UI, TOSCA, HP-ALM (QC)\nTest Management Tools: -JIRA, Quality Center\nOther Tools: -SQL Developer, TOAD.\nDatabase: - DB2, SQL","annotation":[{"label":["Skills"],"points":[{"start":3132,"end":3610,"text":"Technical Experience: - Automation Testing (REST API, Service Virtualization), Functional Testing,\nRegression Testing\nManual Testing, Scripting (SQL)\nDomain Experience: - Telecom, Banking\nProgramming & Scripting Languages: - HTML, CSS, XML, SQL, JAVA (Basic), JSON\nSDLC Model: -Waterfall, Agile\nTesting Tools: - CA LISA, APM (Amdocs Process Manager), SOAP UI, TOSCA, HP-ALM (QC)\nTest Management Tools: -JIRA, Quality Center\nOther Tools: -SQL Developer, TOAD.\nDatabase: - DB2, SQL"}]},{"label":["Skills"],"points":[{"start":3005,"end":3082,"text":"API. (1 year), Scripting. (1 year), SOAP (1 year), UI (1 year), XML (3 years)\n"}]},{"label":["College Name"],"points":[{"start":2937,"end":2960,"text":"Central India Institute\n"}]},{"label":["Degree"],"points":[{"start":2912,"end":2934,"text":"Bachelor in Electronics"}]},{"label":["Graduation Year"],"points":[{"start":2885,"end":2891,"text":"\n2008\n\n"}]},{"label":["Degree"],"points":[{"start":2873,"end":2883,"text":"State Board"}]},{"label":["Email Address"],"points":[{"start":1656,"end":1697,"text":"indeed.com/r/Nidhi-Pandit/b4b383dbe14789c5"}]},{"label":["Designation"],"points":[{"start":1610,"end":1622,"text":"Test Engineer"}]},{"label":["Designation"],"points":[{"start":1550,"end":1562,"text":"Test Engineer"}]},{"label":["Designation"],"points":[{"start":317,"end":329,"text":"Test Engineer"}]},{"label":["Email Address"],"points":[{"start":68,"end":109,"text":"indeed.com/r/Nidhi-Pandit/b4b383dbe14789c5"}]},{"label":["Companies worked at"],"points":[{"start":29,"end":44,"text":"Infosys Limited\n"}]},{"label":["Designation"],"points":[{"start":13,"end":25,"text":"Test Engineer"}]},{"label":["Name"],"points":[{"start":0,"end":11,"text":"Nidhi Pandit"}]}]}
{"content": "Nikhileshkumar Ikhar\nProduct development engineer with 7+ years of experience with\nM.Tech. in IT. Successfully developed & deployed, platform & behaviour\ndesign strategies in well-established corporations as well as emerging\nstartups.\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Nikhileshkumar-Ikhar/\ncb907948c3299ef4\n\nWilling to relocate to: Hyderabad, Telangana - Mumbai, Maharashtra\n\nWORK EXPERIENCE\n\nProduct Development\n\nAggrigator -  Bengaluru, Karnataka -\n\n2015 to Present\n\nAggrigator is Stanford incubated startup. It is an agriculture-based online marketplace in US B2B\nmarket.\nFirst non-co-founding engineer to come on board reported directly to CTO.\nDeveloped Reverse Auction Engine. A farmer would bid for their SKU and engine would pick\nfarmer based on bidding and capacity.\nDeveloped Palletization engine. A pallet can have multiple SKU boxes from multiple buyers. The\nengine generates a packing sequence of boxes in pallets in a truck container according to the\ndelivery route.\nDeveloped prototype to categorize SKU with Deep learning.\nOwned delivery of functionalities development, behavioural nudges, shaping of platform business\nmodel, aligning of product development to business.\nArchitected, designed & deployed websites, database, UI/UX to facilitate farm fresh produce\nprocurement & delivery to end consumers. Owned and developed various features like, \nInventory management plays a big role in optimizing cold storage, warehouse and trucking\nrequirement. It helped in reducing crop wastage.\nInvoicing of sold crops and tracking payments.\nOrder tracking for buyer and seller.\nFetching USDA price list daily via a web crawler.\nGenerating various reports in online, CSV and PDF format.\nWorked with technologies like Python, Django, Celery, MySQL, MongoDB, Ubuntu, Neural\nNetwork.\nFirst, six months employer was Above Solutions.\n\nSoftware Engineer\n\nCisco -  Bengaluru, Karnataka -\n\n2012 to 2015\n\nOwned and developed several products to help network migration, upgradation, tracking bugs,\ntracking\n\nhttps://www.indeed.com/r/Nikhileshkumar-Ikhar/cb907948c3299ef4?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Nikhileshkumar-Ikhar/cb907948c3299ef4?isid=rex-download&ikw=download-top&co=IN\n\n\nvulnerabilities.\n\n● Presented several proofs of concept for new business requirement.\n\n● Worked on various technologies such as Python, Java, Django, Celery, Cisco routers, SDN,\nOnePk & Hadoop.\n\nWorked on network migration during intern\n\nCisco -\n\nJanuary 2012 to June 2012\n\nSystem Engineer\n\nInfosys -  Pune, Maharashtra -\n\n2008 to 2010\n\nWorked as an SAP Basis consultant. Organised various in house events.\n\nEDUCATION\n\nM.Tech. in IT in VIT, Pune\n\nIIIT -  Bengaluru, Karnataka\n\n2010 to 2012\n\nSKILLS\n\nDjango (6 years), Java (3 years), MongoDB (3 years), MySQL (3 years), Python (6 years)\n\nLINKS\n\nhttp://github.com/nik-hil\n\nhttp://linkedin.com/in/nikhar\n\nADDITIONAL INFORMATION\n\nSkills\nPython, Django, Celery, Java, AngularJS, HTML, Bootstrap 3, Shell Script, MySQL, MongoDB,\nUbuntu\n\nBusiness Skills\nPlatform business model, Behaviour design\n\nhttp://github.com/nik-hil\nhttp://linkedin.com/in/nikhar","annotation":[{"label":["Skills"],"points":[{"start":3036,"end":3077,"text":"Platform business model, Behaviour design\n"}]},{"label":["Skills"],"points":[{"start":2922,"end":3017,"text":"Python, Django, Celery, Java, AngularJS, HTML, Bootstrap 3, Shell Script, MySQL, MongoDB,\nUbuntu"}]},{"label":["Skills"],"points":[{"start":2738,"end":2823,"text":"Django (6 years), Java (3 years), MongoDB (3 years), MySQL (3 years), Python (6 years)"}]},{"label":["Graduation Year"],"points":[{"start":2724,"end":2727,"text":"2012"}]},{"label":["Location"],"points":[{"start":2694,"end":2702,"text":"Bengaluru"}]},{"label":["College Name"],"points":[{"start":2686,"end":2689,"text":"IIIT"}]},{"label":["College Name"],"points":[{"start":2675,"end":2677,"text":"VIT"}]},{"label":["Degree"],"points":[{"start":2658,"end":2670,"text":"M.Tech. in IT"}]},{"label":["Companies worked at"],"points":[{"start":2530,"end":2536,"text":"Infosys"}]},{"label":["Designation"],"points":[{"start":2513,"end":2527,"text":"System Engineer"}]},{"label":["Graduation Year"],"points":[{"start":2507,"end":2510,"text":"2012"}]},{"label":["Graduation Year"],"points":[{"start":2494,"end":2497,"text":"2012"}]},{"label":["Companies worked at"],"points":[{"start":2477,"end":2481,"text":"Cisco"}]},{"label":["Companies worked at"],"points":[{"start":2397,"end":2401,"text":"Cisco"}]},{"label":["Graduation Year"],"points":[{"start":1913,"end":1916,"text":"2012"}]},{"label":["Location"],"points":[{"start":1889,"end":1897,"text":"Bengaluru"}]},{"label":["Companies worked at"],"points":[{"start":1880,"end":1884,"text":"Cisco"}]},{"label":["Designation"],"points":[{"start":1861,"end":1877,"text":"Software Engineer"}]},{"label":["Companies worked at"],"points":[{"start":493,"end":502,"text":"Aggrigator"}]},{"label":["Location"],"points":[{"start":452,"end":460,"text":"Bengaluru"}]},{"label":["Companies worked at"],"points":[{"start":438,"end":447,"text":"Aggrigator"}]},{"label":["Designation"],"points":[{"start":417,"end":436,"text":"Product Development\n"}]},{"label":["Email Address"],"points":[{"start":279,"end":330,"text":"indeed.com/r/Nikhileshkumar-Ikhar/\ncb907948c3299ef4\n"}]},{"label":["Location"],"points":[{"start":236,"end":244,"text":"Bengaluru"}]},{"label":["Degree"],"points":[{"start":83,"end":95,"text":"M.Tech. in IT"}]},{"label":["Years of Experience"],"points":[{"start":54,"end":62,"text":" 7+ years"}]},{"label":["Designation"],"points":[{"start":21,"end":48,"text":"Product development engineer"}]},{"label":["Name"],"points":[{"start":0,"end":19,"text":"Nikhileshkumar Ikhar"}]}]}
{"content": "Nitin Tr\nPeopleSoft Consultant\n\nBangalore Urban, Karnataka - Email me on Indeed: indeed.com/r/Nitin-Tr/e7e3a2f5b4c1e24e\n\nAn e-commerce website I built as my college project. The website contains all the basic elements\nof an e-commerce website which are\n\nThe landing page, categorization of items based on filters, basic session level security, product\npage, Cart, share button, empty cart button, paginations etc.\n\nIt consists of a separate seller accounts where sellers can register and later upload their products\nto be sold, which can later be edited or deleted.\n\nIt consists of an admin panel where all the products listed can be viewed and edited by the\nmoderator.\n\n60 days auto delete feature, which deletes the product listing 60 days from the date of upload\nif seller has not modified the listing upon the next login.\n\nUsage of Modals for registration and Login which reduces the number of pages to navigate.\n\nLanguages used: PHP, MySQL, Html, CSS, Bootstrap, JavaScript and jQuery.\n\nWORK EXPERIENCE\n\nPeopleSoft consultant\n\nOracle -\n\nSeptember 2017 to Present\n\nPerforming customisations, enhancements and bug fixes for front end and backend inpeople code\nusing appdesigner.\n\nPeopleSoft Consultant\n\nOracle India Ltd -\n\nSeptember 2017 to April 2018\n\n• Develop customizations to meet business process requirements using application designer.\n\n• Involved in modification enhancement and bug-fixing of the PeopleSoft application both front-\nend and back-end to suit business needs.\n\n• Communicate with the business and get clear requirements if adequate information is not\navailable and also follow-up with them until final resolution is obtained.\n\n• Release Enhancements for UAT and communicate with business to migrate into production.\n\nhttps://www.indeed.com/r/Nitin-Tr/e7e3a2f5b4c1e24e?isid=rex-download&ikw=download-top&co=IN\n\n\n• Also have to work on service requests, Incident Creation, Incident Assignment additionally and\nconstantly involved in querying the database and running reports.\n\nFreelance Development\n\nI am a passionate Web Developer and love to build clean, smooth and responsive websites. I\nhave built many websites for individuals on freelance or per-project basis which are Responsive in\nnature. I am capable of building clean and responsive websites. They include personal portfolios\nand Small business websites.\n• http://www.pramodprakash.com/shop: Responsive and Dynamic e-commerce portal.\n\n• http://www.pramodprakash.com/tickItBusDemoUrl: A bus booking platform, source:\nKathmandu, Destination: Pokhra.\n• http://www.pramodprakash.com/geisle/index.php: A small business website focused on\nanimations (under construction) also check (geisle/index2.php)\n• http://pramodprakash.com/fulllogin: complete login module with email account activation and\npassword reset.\n• http://pramodprakash.com/sec: A small business website, built to showcase color combinations\nand layout.\n• http://pramodprakash.com/r&d: A small business website built in parallax format. Completely\nresponsive.\n• http://pramodprakash.com/web1: A template built according to given specifications and also\nsmall map feature included which also supports location search.\n\nEDUCATION\n\nBtech information science in BCET\n\nVtu -  Bengaluru, Karnataka\n\n2017\n\nVijaya composite p.u. college\n\ne-commerce\n\nVijaya High School\n\nSKILLS\n\nPhp, Html5, Javascript, Css, Bootstrap, Jquery, Sql\n\nLINKS\n\nhttps://www.linkedin.com/in/nitin-tr-*********\n\nADDITIONAL INFORMATION\n\nSoftware Skills\n\nhttps://www.linkedin.com/in/nitin-tr-*********\n\n\n• Programming Language: core java, peoplecode.\n• Scripting languages: PHP, JavaScript.\n• Web Languages: HTML, CSS.\n• Database Language: Sql.\n• Frameworks: Bootstrap, JQuery.\n• IDE's: NetBeans, Eclipse.\n• Tools: Application Designer, Toad.\n\nPersonal Skills\n\n• Excellent Communication Skills both Written and verbal.\n• Honest, trustworthy and highly motivated team player and Strong Negotiator.\n• Supportive and creative.\n• Quick Learner and Flexible.\n• Good Analytical Ability and Logical Reasoning.\n• Good listening skills.","annotation":[{"label":["Skills"],"points":[{"start":3511,"end":3748,"text":"• Programming Language: core java, peoplecode.\n• Scripting languages: PHP, JavaScript.\n• Web Languages: HTML, CSS.\n• Database Language: Sql.\n• Frameworks: Bootstrap, JQuery.\n• IDE's: NetBeans, Eclipse.\n• Tools: Application Designer, Toad."}]},{"label":["Skills"],"points":[{"start":3313,"end":3363,"text":"Php, Html5, Javascript, Css, Bootstrap, Jquery, Sql"}]},{"label":["College Name"],"points":[{"start":3285,"end":3302,"text":"Vijaya High School"}]},{"label":["Degree"],"points":[{"start":3273,"end":3282,"text":"e-commerce"}]},{"label":["College Name"],"points":[{"start":3242,"end":3270,"text":"Vijaya composite p.u. college"}]},{"label":["Graduation Year"],"points":[{"start":3235,"end":3239,"text":"\n2017"}]},{"label":["College Name"],"points":[{"start":3207,"end":3210,"text":"Vtu "}]},{"label":["Degree"],"points":[{"start":3172,"end":3205,"text":"Btech information science in BCET\n"}]},{"label":["Degree"],"points":[{"start":2399,"end":2408,"text":"e-commerce"}]},{"label":["Email Address"],"points":[{"start":1754,"end":1791,"text":"indeed.com/r/Nitin-Tr/e7e3a2f5b4c1e24e"}]},{"label":["Companies worked at"],"points":[{"start":1206,"end":1211,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1032,"end":1037,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":1008,"end":1030,"text":"\nPeopleSoft consultant\n"}]},{"label":["Degree"],"points":[{"start":224,"end":233,"text":"e-commerce"}]},{"label":["Degree"],"points":[{"start":124,"end":133,"text":"e-commerce"}]},{"label":["Email Address"],"points":[{"start":81,"end":118,"text":"indeed.com/r/Nitin-Tr/e7e3a2f5b4c1e24e"}]},{"label":["Location"],"points":[{"start":32,"end":46,"text":"Bangalore Urban"}]},{"label":["Name"],"points":[{"start":0,"end":7,"text":"Nitin Tr"}]}]}
{"content": "Pradeeba V\nLEAD ENGINEER - CISCO\n\n- Email me on Indeed: indeed.com/r/Pradeeba-V/19ff20f4b8552375\n\nWORK EXPERIENCE\n\nLEAD ENGINEER\n\nCISCO -\n\nJune 2014 to Present\n\nPROJECT SPECIFIC SKILLS JAVASCRIPT, OOJS, HTML5, CSS3, REST, DOJO,\nAngular JS\nTOOLS USED SVN, Code Collaborator\nDescription:\n\nCisco Prime Infrastructure simplifies the management of wireless and wired networks. The Prime\nUI\noffers Prime Widget Toolkit (XWT) which provides dojo widgets. The UI supports HTML 5 features.\nThe\nPrime UI offers rich UI experience which includes consistency, better look and feel and scalable\ndesigns to handle large volume of data.\n\nResponsibilities:\n• Creating Widgets in dojo\n• Enhancement of existing widget\n• Handling REST calls\n• Writing Test cases\n• Unit Testing\n\n2. Project Title FINUX\n\nINDUSTRY FINACLE - BANKING\n\nSENIOR SYSTEMS ENGINEER\n\n-\n\nJune 2012 to June 2014\n\nPROJECT SPECIFIC SKILLS JAVASCRIPT, DOJO, CSS, HTML\n\nDescription:\nFinacle is a core banking software package. It is used by multiple banks across several countries,\nit can\nhandle multi-currency transactions.\n\nhttps://www.indeed.com/r/Pradeeba-V/19ff20f4b8552375?isid=rex-download&ikw=download-top&co=IN\n\n\nTo achieve the common look and feel for all the screens where the interaction done with the\nuser in the form of getting input values as well as retrieval of some kind of data and display of\nsuch data.\nI was totally involved in developing the Datagrid UI component in DOJO to display the search\nresults which are obtained as a result of inquiring the transactions.\n\nResponsibilities:\n• Front end enhancements for the Core product.\n• Discussing and finalizing the end UI screens with Functional and Design teams.\n• Writing front end and back end validation routines.\n• Regression testing for menus using Service Testing Framework.\n3. Project Title FINACLE\n\nINDUSTRY BANKING\nCLIENT Universal Banking Product from INFOSYS\n\nSYSTEMS ENGINEER\n\n-\n\nOctober 2011 to May 2012\n\nPROJECT SPECIFIC SKILLS JAVASCRIPT, DOJO, CSS, HTML\n\nDescription:\nFinacle is a core banking software package. It is used by multiple banks across several countries,\nit can\nhandle multi-currency transactions.\nBeing a part of Finacle team, I had involved in making the front end enhancement for Core\nproduct.\nResponsibilities:\n\n• Front end enhancements for the Core product.\n• Bug Fixing\n\nEDUCATION\n\nB.Tech\n\nInstitute of Road and Transport May\n\nOctober 1980\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\nProgramming Languages: Core Java\nScripting languages: JavaScript, OOJS\nDatabases: Oracle\nOperating systems: Windows (7, XP) and UNIX\nTools & Utilities: Eclipse, SSH, WinSCP, Code Collaborator, SVN\n\n\n\nWeb Designing Tools: HTML 4, HTML 5, CSS 3, DOJO, Angular JS\nWeb Service: REST Web services","annotation":[{"label":["Skills"],"points":[{"start":2707,"end":2710,"text":"REST"}]},{"label":["Skills"],"points":[{"start":2683,"end":2692,"text":"Angular JS"}]},{"label":["Skills"],"points":[{"start":2677,"end":2680,"text":"DOJO"}]},{"label":["Skills"],"points":[{"start":2670,"end":2672,"text":"CSS"}]},{"label":["Skills"],"points":[{"start":2662,"end":2665,"text":"HTML"}]},{"label":["Skills"],"points":[{"start":2654,"end":2657,"text":"HTML"}]},{"label":["Skills"],"points":[{"start":2626,"end":2628,"text":"SVN"}]},{"label":["Skills"],"points":[{"start":2607,"end":2623,"text":"Code Collaborator"}]},{"label":["Skills"],"points":[{"start":2599,"end":2604,"text":"WinSCP"}]},{"label":["Skills"],"points":[{"start":2594,"end":2596,"text":"SSH"}]},{"label":["Skills"],"points":[{"start":2585,"end":2591,"text":"Eclipse"}]},{"label":["Skills"],"points":[{"start":2561,"end":2564,"text":"UNIX"}]},{"label":["Skills"],"points":[{"start":2541,"end":2555,"text":"Windows (7, XP)"}]},{"label":["Skills"],"points":[{"start":2515,"end":2520,"text":"Oracle"}]},{"label":["Skills"],"points":[{"start":2499,"end":2502,"text":"OOJS"}]},{"label":["Skills"],"points":[{"start":2487,"end":2496,"text":"JavaScript"}]},{"label":["Skills"],"points":[{"start":2456,"end":2464,"text":"Core Java"}]},{"label":["Graduation Year"],"points":[{"start":2386,"end":2389,"text":"1980"}]},{"label":["College Name"],"points":[{"start":2341,"end":2375,"text":"Institute of Road and Transport May"}]},{"label":["Degree"],"points":[{"start":2333,"end":2339,"text":"B.Tech\n"}]},{"label":["Skills"],"points":[{"start":1982,"end":1985,"text":"HTML"}]},{"label":["Skills"],"points":[{"start":1977,"end":1979,"text":"CSS"}]},{"label":["Skills"],"points":[{"start":1971,"end":1974,"text":"DOJO"}]},{"label":["Skills"],"points":[{"start":1959,"end":1968,"text":"JAVASCRIPT"}]},{"label":["Designation"],"points":[{"start":1888,"end":1903,"text":"SYSTEMS ENGINEER"}]},{"label":["Designation"],"points":[{"start":1824,"end":1839,"text":"INDUSTRY BANKING"}]},{"label":["Skills"],"points":[{"start":1436,"end":1439,"text":"DOJO"}]},{"label":["Email Address"],"points":[{"start":1085,"end":1124,"text":"indeed.com/r/Pradeeba-V/19ff20f4b8552375"}]},{"label":["Skills"],"points":[{"start":911,"end":914,"text":"HTML"}]},{"label":["Skills"],"points":[{"start":906,"end":908,"text":"CSS"}]},{"label":["Skills"],"points":[{"start":900,"end":903,"text":"DOJO"}]},{"label":["Skills"],"points":[{"start":888,"end":897,"text":"JAVASCRIPT"}]},{"label":["Designation"],"points":[{"start":819,"end":834,"text":"SYSTEMS ENGINEER"}]},{"label":["Designation"],"points":[{"start":812,"end":834,"text":"SENIOR SYSTEMS ENGINEER"}]},{"label":["Designation"],"points":[{"start":784,"end":809,"text":"INDUSTRY FINACLE - BANKING"}]},{"label":["Skills"],"points":[{"start":712,"end":715,"text":"REST"}]},{"label":["Skills"],"points":[{"start":464,"end":467,"text":"HTML"}]},{"label":["Skills"],"points":[{"start":255,"end":271,"text":"Code Collaborator"}]},{"label":["Skills"],"points":[{"start":250,"end":252,"text":"SVN"}]},{"label":["Skills"],"points":[{"start":228,"end":237,"text":"Angular JS"}]},{"label":["Skills"],"points":[{"start":222,"end":225,"text":"DOJO"}]},{"label":["Skills"],"points":[{"start":216,"end":219,"text":"REST"}]},{"label":["Skills"],"points":[{"start":210,"end":213,"text":"CSS3"}]},{"label":["Skills"],"points":[{"start":210,"end":212,"text":"CSS"}]},{"label":["Skills"],"points":[{"start":203,"end":207,"text":"HTML5"}]},{"label":["Skills"],"points":[{"start":203,"end":206,"text":"HTML"}]},{"label":["Skills"],"points":[{"start":197,"end":200,"text":"OOJS"}]},{"label":["Skills"],"points":[{"start":185,"end":194,"text":"JAVASCRIPT"}]},{"label":["Companies worked at"],"points":[{"start":130,"end":134,"text":"CISCO"}]},{"label":["Designation"],"points":[{"start":115,"end":127,"text":"LEAD ENGINEER"}]},{"label":["Email Address"],"points":[{"start":56,"end":95,"text":"indeed.com/r/Pradeeba-V/19ff20f4b8552375"}]},{"label":["Companies worked at"],"points":[{"start":27,"end":31,"text":"CISCO"}]},{"label":["Designation"],"points":[{"start":11,"end":23,"text":"LEAD ENGINEER"}]},{"label":["Name"],"points":[{"start":0,"end":9,"text":"Pradeeba V"}]}]}
{"content": "Prakriti Shaurya\nSenior System Engineer - Infosys Limited\n\nMangalore, Karnataka - Email me on Indeed: indeed.com/r/Prakriti-\nShaurya/5339383f9294887e\n\nDetail-oriented individual with three years of experience as an IT Consultant looking\nfor opportunity to develop my professional skills in a vibrant and stable environment,\nand to use those skills for the benefits of the organization in best possible way.\n\nWilling to relocate to: Bengaluru, Karnataka - Bangalore Urban, Karnataka\n\nWORK EXPERIENCE\n\nSenior System Engineer\n\nInfosys Limited -  Mangalore, Karnataka -\n\nJanuary 2017 to Present\n\nWorking as an IT Consultant under application maintenance and support for METLIFE Insurance\ncompany.\n\nSystem Engineer\n\nInfosys Limited -  Mangalore, Karnataka -\n\nDecember 2014 to December 2016\n\nWorked as an IT Consultant under application maintenance and support for METLIFE Insurance\ncompany.\n\nSOFTWARE\n\nEDUCATION\n\nBachelor of Technology in Information Technology\n\nVellore Institute of Technology -  Vellore, Tamil Nadu\n\n2010 to 2014\n\nC.B.S.E.\n\nNotre Dame Academy -  Patna, Bihar\n\n2007 to 2009\n\nSKILLS\n\nJava, Jsp, Html, Sql, C++, Javascript\n\nhttps://www.indeed.com/r/Prakriti-Shaurya/5339383f9294887e?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Prakriti-Shaurya/5339383f9294887e?isid=rex-download&ikw=download-top&co=IN\n\n\nADDITIONAL INFORMATION\n\nSKILLS\n• Good communication - written and oral skills\n• Excellent conceptual and analytical skills\n• Effective interpersonal skills\n\nPERSONALITY\n• Communicative • Punctuality\n• Creativity • Organized","annotation":[{"label":["Skills"],"points":[{"start":1368,"end":1559,"text":"• Good communication - written and oral skills\n• Excellent conceptual and analytical skills\n• Effective interpersonal skills\n\nPERSONALITY\n• Communicative • Punctuality\n• Creativity • Organized"}]},{"label":["Skills"],"points":[{"start":1096,"end":1132,"text":"Java, Jsp, Html, Sql, C++, Javascript"}]},{"label":["College Name"],"points":[{"start":1038,"end":1056,"text":"Notre Dame Academy "}]},{"label":["Degree"],"points":[{"start":1028,"end":1036,"text":"C.B.S.E.\n"}]},{"label":["Graduation Year"],"points":[{"start":1022,"end":1026,"text":"2014\n"}]},{"label":["College Name"],"points":[{"start":958,"end":989,"text":"Vellore Institute of Technology "}]},{"label":["Degree"],"points":[{"start":908,"end":955,"text":"Bachelor of Technology in Information Technology"}]},{"label":["Location"],"points":[{"start":730,"end":738,"text":"Mangalore"}]},{"label":["Companies worked at"],"points":[{"start":711,"end":725,"text":"Infosys Limited"}]},{"label":["Location"],"points":[{"start":543,"end":551,"text":"Mangalore"}]},{"label":["Companies worked at"],"points":[{"start":524,"end":538,"text":"Infosys Limited"}]},{"label":["Designation"],"points":[{"start":500,"end":521,"text":"Senior System Engineer"}]},{"label":["Email Address"],"points":[{"start":102,"end":148,"text":"indeed.com/r/Prakriti-\nShaurya/5339383f9294887e"}]},{"label":["Location"],"points":[{"start":59,"end":67,"text":"Mangalore"}]},{"label":["Companies worked at"],"points":[{"start":42,"end":56,"text":"Infosys Limited"}]},{"label":["Designation"],"points":[{"start":17,"end":38,"text":"Senior System Engineer"}]},{"label":["Name"],"points":[{"start":0,"end":15,"text":"Prakriti Shaurya"}]}]}
{"content": "PRASHANTH BADALA\nDevops Engineer ,Cloud Engineer -Oracle\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/PRASHANTH-BADALA/\nbf4c4b7253a8ece7\n\n• Hands on experience in end-end process of Build Management, Release Management and\nConfiguration Management.\n• Hands on experience on supporting configuration management tools on both physical and cloud\nenvironment\n• Involved in setting up Jenkins in Distributed Environments with Master and Slave\n• Working experience on Subversion (SVN) administration and basic usage\n• Creating Branches, tags and providing SVN user access to all developers in the organization\n• Managing application server instances running on AWS\n• Involved in configuring EC2 instances along with Auto scale up options.\n• Involved in configuration Management using CHEF and automated the complete platform\nsetup.\n• Good knowledge on Cookbooks, Chef Kitchen, Burks file and Metadata.\n• Involved in automating the release using UDeploy and deploying the application to different\nenvironments.\n• Involved in container setup with Docker. Having good understanding of creating and running\nDocker images.\n• Involved in writing Applications, Componenets, Resources and component process flow.\n• Configured Jenkins as contionus integration tool for regular source code builds.\n• Experience in setting up branching strategies, merging and taking regular backups of the source\ncode on SVN server\n• Helping the Developers in SVN related issues\n• Written the integrated build automation scripts using Ant\n• Perform the QA & Stress and UAT builds and deployed the binaries on respective environment\nservers\n• Monitoring the deployment in all the servers\n• Supported setting up of various environments in multi-tier architecture involving load balancers,\nApache webservers, Oracle database,\n• Developed custom scripts to automate the build and release process.\n• Implemented a custom authentication with WebLogic for user authentication, authorization and\npassword policy\n• Monitoring environment using monitoring tools like Wily monitoring tool and custom shell script.\n• Involved in integrating WebLogic with Wily introscope.\n• Involved in resolving tickets with different SLAs like SEV1, SEV2, SEV3 and SEV4.\n• Involved in change management process like opening change records using change\nmanagement tools like INFRA, Managenow.\n• Providing on call, weekend and deployment support.\n• Involved in applying security patches using WebLogic Utility.\n• Deploying applications using different deployment strategies like Stage, No Stage and External\nstage.\nWork Experience:\n\n• Working as Configuration Engineer in Oracle, Hyderabad from July 2015 to till date.\n\nhttps://www.indeed.com/r/PRASHANTH-BADALA/bf4c4b7253a8ece7?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/PRASHANTH-BADALA/bf4c4b7253a8ece7?isid=rex-download&ikw=download-top&co=IN\n\n\nTechnical Skills:\n\nVersion Control Tools \n\nSubversion(SVN),GIT\n\nWeb/Appservers\nContinuous Integration Tools\nWebLogic 11g, Apache HTTP Server 2.4\nJenkins 1.6, Hudson\n\nBuild Tools \n\nMaven 3\n\nScripting Languages \n\nShell Scripting and Python\n\nOperating Systems \n\nWindows servers […] Windows XP/7, Red Hat LINUX 5\n\nDatabase\n\nOracle 11g\n\nRelease Tools\n\nUDeploy,Jenkins\n\nCloud\n\nAWS\n\nConfiguration Tools\n\nCHEF\n\nEducation Qualification:\nB.Tech From Annamacharya Institute of Technology, JNTU Hyderabad - 2015 \n\nProject Details\n\nTitle: E-Banking solutions \n\nClient : Union Bank of Canada, Canada \nRole : Configuration Engineer \nEnvironment : Maven, Jenkins, CHEF, UDeploy, SVN, Linux, Weblogic,Aws \n\n\n\nDuration : Nov 2016 to till date\n\nProject Description:\n\nThis is a banking project and basic objective of this project is to deal with the loans. This\napplication is based on Java technology. For this we have to schedule the tasks and have to collect\ncode from development team and have to build and deploy the code later have to support the\nrelease management team of which executing Java applications build and deployments in Dev,\nQA, performance and production environments.\n. .\nResponsibilities:\n• Involved in automation of Configuration Management using CHEF and automated multiple\nenvironments like Prod and Non Prod.\n• Involved in configuring AWS Environment to deploy applications.\n• Involved in Release Management and automated the overall release process using Urban Code\nDeployments (UDeploy)\n• For on boarding existing application, performing knowledge transition from development team\nto SCM team on build and deployment process.\n• For new applications, work with development to get the requirements of application build and\ndeployment process.\n• Creating War/Ear files using Ant Script\n• Creating Jenkins/Hudson jobs.\n• Monitor and track requests in Subversion.\n• Monitor and fix the continuous integration builds running in Jenkins.\n• Troubleshooting the compilation errors and provide the technical support to the developers\nteam on that issue.\n• Installation, configuring, administration of Web Logic 9.x, 10.x on Linux environment.\n• Supporting Builds for Multiple environments like Dev, QA, Staging, Perf and Production.\n• Performing Manual and Automation Builds using Maven and Jenkins.\n• Troubleshooting the middle ware issues and resolving the P2,P3 tickets with in SLA\n• Provided on call support on 24/7 basis\n• Involved in creation and configuration of Domains, JVM instances in production, QA and UAT\nenvironments.\n• Configured clusters to provide fail over, load balancing and deployed applications on clusters.\n• Installed, configured and administration to Web logic 10.x, JDBC Data source and Connection\npool configuration with Oracle. Web Logic Administration, Monitoring and Troubleshooting using\nAdmin Console.\n• Have excellent experience in Client Interaction\n• Provided on call support for production tickets on 24/7 basis.\n• Deployment of web and enterprise applications and their updates in dev, production, pre-\nproduction using Admin console.\n\n• Creating branches & merging using Subversion.\n• Performing deployments to multiple environments like Dev, QA, Field, Perf, UAT & Production\nenvs\n• Troubleshooting application related issues by log verification.\n• Writing a UNIX Shell Script and schedule in the respective run levels for automate day-to-day\nactivities such as auto start application server.\n• Automate code deployments by using ANT and Jenkins.\n\n\n\nSprint Value Added Services\n\nClient : Sprint, U.S \nRole : Build and Release Engineer\nEnvironment: Maven, Jenkins, SVN, Linux, Weblogic, Apache,AWS,Docker\nDuration : Dec 2015 to Oct 2016\n\nProject Description:\n\nThis is a Value Added Services provided by Sprint. This application is based on Java technology.\nFor this we have to schedule the tasks and have to collect code from development team and\nhave to build and deploy the code later have to support the release management team of which\nexecuting Java applications build and deployments in Dev, QA, performance and production\nenvironments.\n\nResponsibilities:\n\n• For on boarding existing application, performing knowledge transition from development team\nto SCM team on build and deployment process.\n• For new applications, work with development to get the requirements of application build and\ndeployment process.\n• Installing and configuring Subversion (SVN) and Jenkins.\n• Providing support to Subversion (SVN) related issues.\n• Developing and maintaining build files by using Ant script.\n• Integrate Unit Testing in Ant builds\n• Installation, configuring, administration of Web Logic 9.x, 10.x on Linux environment.\n• Supporting Builds for Multiple environments like Dev, QA, Staging, Perf and Production.\n• Performing Manual and Automation Builds using Maven and Jenkins.\n• Troubleshooting the middle ware issues and resolving the P2,P3 tickets with in SLA\n• Provided on call support on 24/7 basis\n• Involved in creation and configuration of Domains, JVM instances in production, QA and UAT\nenvironments.\n• Configured clusters to provide fail over, load balancing and deployed applications on clusters.\n• Installed, configured and administration to Web logic 10.x, JDBC Data source and Connection\npool configuration with Oracle. Web Logic Administration, Monitoring and Troubleshooting using\nAdmin Console.\n• Have excellent experience in Client Interaction\n• Provided on call support for production tickets on 24/7 basis.\n• Deployment of web and enterprise applications and their updates in dev, production, pre-\nproduction using Admin console.\n• Building the source code using Jenkins.\n• Helped developers in resolving SVN issues and concerns.\n• Responsible for weekly and daily work checks and backups.\n• Environment: WebLogic Application Server 10.3, JDK1.6, Oracle, Apache Webserver, Linux,\nJIRA,Infra,SSH,TOAD\n\nKOhls Retail Services\n\n\n\nClient : Kohls, US\nRole : Build and Release Engineer. \nEnvironment: Maven, Jenkins, SVN, Linux, WebLogic, Apache\nDuration : July 2015 to Nov 2015\n\nProject Description:\n\nThis application is based on Java technology. For this we have to schedule the tasks and have to\ncollect code from development team and have to build and deploy the code later have to support\nthe release management team of which executing Java applications build and deployments in\nDev, QA, performance and production environments.\n\nResponsibilities: \n\n• Developing and maintaining build files by using Ant script.\n• Integrate Unit Testing in Ant builds\n• Installation, configuring, administration of Web Logic 9.x, 10.x on Linux environment.\n• Supporting Builds for Multiple environments like Dev, QA, Staging, Perf and Production.\n\n• Supporting Builds for Multiple environments like Dev, QA, Staging, Perf and Production.\n• Performing Manual and Automation Builds using Maven and Jenkins.\n• Deploying WAR, EAR applications on various targeted servers in the clustered environments.\n• Web Logic Administration, Monitoring and Troubleshooting using Admin Console.\n• Analyzing log files and periodic removal of them\n• Creating branches & merging using Subversion.\n• Performing deployments to multiple environments like Dev, QA, Field, Perf, UAT & Production\nenvs\n• Troubleshooting application related issues by log verification.\n• Writing a UNIX Shell Script and schedule in the respective run levels for automate day-to-day\nactivities such as auto start application server.\n• Automate code deployments by using ANT and Jenkins.\n\n• Involved in changing heap parameters like –Xms, -Xmx, -XnoOpt,-XnoHup etc.\n• Perform daily environment health-check\n• Good in taking the thread dumps and finding the root cause analysis.\n• Created and configured web logic server instances, clusters in domain environment.\n• Installed web logic on production boxes in console mode.\n• Strong experience in administrating by using Admin console.\n\nEnvironment: WebLogic Application Server 9.2, Java, Oracle, Apache Webserver, Linux, JIRA,\nManagenow, Putty, TOAD\n\nWilling to relocate to: Hyderabad, Telangana - Bangalore Urban, Karnataka - Chennai, Tamil Nadu\n\nWORK EXPERIENCE\n\nConfiguration Engineer\n\nOracle -  Hyderabad, Telangana -\n\n\n\nJuly 2015 to Present\n\n• Hands on experience in end-end process of Build Management, Release Management and\nConfiguration Management.\n• Hands on experience on supporting configuration management tools on both physical and cloud\nenvironment\n• Involved in setting up Jenkins in Distributed Environments with Master and Slave\n• Working experience on Subversion(SVN) administration and basic usage\n• Creating Branches, tags and providing SVN user access to all developers in the organization\n• Managing application server instances running on AWS\n• Involved in configuring EC2 instances along with Auto scale up options.\n• Involved in configuration Management using CHEF and automated the complete platform\nsetup.\n• Good knowledge on Cookbooks, Chef Kitchen, Burks file and Metadata.\n• Involved in automating the release using UDeploy and deploying the application to different\nenvironments.\n• Involved in container setup with Docker. Having good understanding of creating and running\nDocker images.\n• Involved in writing Applications, Componenets, Resources and component process flow.\n• Configured Jenkins as contionus integration tool for regular source code builds.\n• Experience in setting up branching strategies, merging and taking regular backups of the source\ncode on SVN server\n• Helping the Developers in SVN related issues\n• Written the integrated build automation scripts using Ant\n• Perform the QA & Stress and UAT builds and deployed the binaries on respective environment\nservers\n• Monitoring the deployment in all the servers\n• Supported setting up of various environments in multi-tier architecture involving load balancers,\nApache webservers, Oracle database, \n• Developed custom scripts to automate the build and release process.\n• Implemented a custom authentication with WebLogic for user authentication, authorization and\npassword policy\n• Monitoring environment using monitoring tools like Wily monitoring tool and custom shell script.\n• Involved in integrating WebLogic with Wily introscope.\n• Involved in resolving tickets with different SLAs like SEV1, SEV2, SEV3 and SEV4.\n• Involved in change management process like opening change records using change\nmanagement tools like INFRA, Managenow.\n• Providing on call, weekend and deployment support.\n• Involved in applying security patches using WebLogic Utility.\n• Deploying applications using different deployment strategies like Stage, No Stage and External\nstage.\n\nConfiguration Engineer\n\nOracle\n\nDevops\n\n\n\nEDUCATION\n\nB.TECH/B.E\n\nAnnamacharya Institute of Technology, JNTU, Hyderabad -  Hyderabad, Telangana\n\n2015\n\nSKILLS\n\nAWS (1 year), CHEF (1 year), Linux (2 years), git, svn, maven, devops, jenkins, Docker,\nweblogic\n\nADDITIONAL INFORMATION\n\nOperating Systems Windows servers […] Windows XP/7, Red Hat LINUX 5\nDatabase Oracle 11g\nRelease Tools UDeploy, Jenkins\nCloud AWS\nConfiguration Tools CHEF","annotation":[{"label":["Skills"],"points":[{"start":13907,"end":13910,"text":"CHEF"}]},{"label":["Skills"],"points":[{"start":13883,"end":13886,"text":"AWS\n"}]},{"label":["Skills"],"points":[{"start":13883,"end":13885,"text":"AWS"}]},{"label":["Companies worked at"],"points":[{"start":13835,"end":13840,"text":"Oracle"}]},{"label":["Skills"],"points":[{"start":13724,"end":13731,"text":"weblogic"}]},{"label":["Skills"],"points":[{"start":13716,"end":13721,"text":"Docker"}]},{"label":["Skills"],"points":[{"start":13707,"end":13713,"text":"jenkins"}]},{"label":["Skills"],"points":[{"start":13699,"end":13704,"text":"devops"}]},{"label":["Skills"],"points":[{"start":13691,"end":13696,"text":" maven"}]},{"label":["Skills"],"points":[{"start":13687,"end":13689,"text":"svn"}]},{"label":["Skills"],"points":[{"start":13682,"end":13684,"text":"git"}]},{"label":["Skills"],"points":[{"start":13665,"end":13669,"text":"Linux"}]},{"label":["Skills"],"points":[{"start":13650,"end":13653,"text":"CHEF"}]},{"label":["Skills"],"points":[{"start":13636,"end":13638,"text":"AWS"}]},{"label":["Location"],"points":[{"start":13600,"end":13608,"text":"Hyderabad"}]},{"label":["Location"],"points":[{"start":13587,"end":13595,"text":"Hyderabad"}]},{"label":["College Name"],"points":[{"start":13543,"end":13578,"text":"Annamacharya Institute of Technology"}]},{"label":["Companies worked at"],"points":[{"start":13502,"end":13507,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":13478,"end":13499,"text":"Configuration Engineer"}]},{"label":["Companies worked at"],"points":[{"start":12696,"end":12701,"text":"Oracle"}]},{"label":["Skills"],"points":[{"start":12020,"end":12025,"text":"Docker"}]},{"label":["Skills"],"points":[{"start":11962,"end":11967,"text":"Docker"}]},{"label":["Skills"],"points":[{"start":11701,"end":11704,"text":"CHEF"}]},{"label":["Skills"],"points":[{"start":11578,"end":11581,"text":"AWS\n"}]},{"label":["Skills"],"points":[{"start":11578,"end":11580,"text":"AWS"}]},{"label":["Location"],"points":[{"start":11014,"end":11022,"text":"Hyderabad"}]},{"label":["Companies worked at"],"points":[{"start":11004,"end":11009,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":10980,"end":11001,"text":"Configuration Engineer"}]},{"label":["Location"],"points":[{"start":10890,"end":10898,"text":"Hyderabad"}]},{"label":["Skills"],"points":[{"start":10829,"end":10833,"text":"Linux"}]},{"label":["Companies worked at"],"points":[{"start":10803,"end":10808,"text":"Oracle"}]},{"label":["Skills"],"points":[{"start":9450,"end":9454,"text":"Linux"}]},{"label":["Skills"],"points":[{"start":8846,"end":8850,"text":"Linux"}]},{"label":["Skills"],"points":[{"start":8704,"end":8708,"text":"Linux"}]},{"label":["Companies worked at"],"points":[{"start":8678,"end":8683,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":8137,"end":8142,"text":"Oracle"}]},{"label":["Skills"],"points":[{"start":7512,"end":7516,"text":"Linux"}]},{"label":["Skills"],"points":[{"start":6507,"end":6512,"text":"Docker"}]},{"label":["Skills"],"points":[{"start":6503,"end":6505,"text":"AWS"}]},{"label":["Skills"],"points":[{"start":6479,"end":6483,"text":"Linux"}]},{"label":["Companies worked at"],"points":[{"start":5619,"end":5624,"text":"Oracle"}]},{"label":["Skills"],"points":[{"start":4994,"end":4998,"text":"Linux"}]},{"label":["Skills"],"points":[{"start":4213,"end":4215,"text":"AWS"}]},{"label":["Skills"],"points":[{"start":4122,"end":4125,"text":"CHEF"}]},{"label":["Skills"],"points":[{"start":3540,"end":3544,"text":"Linux"}]},{"label":["Skills"],"points":[{"start":3520,"end":3523,"text":"CHEF"}]},{"label":["Designation"],"points":[{"start":3466,"end":3487,"text":"Configuration Engineer"}]},{"label":["Graduation Year"],"points":[{"start":3367,"end":3370,"text":"2015"}]},{"label":["Location"],"points":[{"start":3355,"end":3363,"text":"Hyderabad"}]},{"label":["College Name"],"points":[{"start":3312,"end":3347,"text":"Annamacharya Institute of Technology"}]},{"label":["Degree"],"points":[{"start":3300,"end":3305,"text":"B.Tech"}]},{"label":["Skills"],"points":[{"start":3269,"end":3272,"text":"CHEF"}]},{"label":["Skills"],"points":[{"start":3248,"end":3267,"text":"Configuration Tools\n"}]},{"label":["Skills"],"points":[{"start":3243,"end":3246,"text":"AWS\n"}]},{"label":["Skills"],"points":[{"start":3243,"end":3245,"text":"AWS"}]},{"label":["Skills"],"points":[{"start":3236,"end":3241,"text":"Cloud\n"}]},{"label":["Skills"],"points":[{"start":3219,"end":3233,"text":"UDeploy,Jenkins"}]},{"label":["Companies worked at"],"points":[{"start":3192,"end":3197,"text":"Oracle"}]},{"label":["Skills"],"points":[{"start":3083,"end":3109,"text":"Shell Scripting and Python\n"}]},{"label":["Skills"],"points":[{"start":3052,"end":3058,"text":"Maven 3"}]},{"label":["Skills"],"points":[{"start":3017,"end":3035,"text":"Jenkins 1.6, Hudson"}]},{"label":["Skills"],"points":[{"start":2980,"end":3015,"text":"WebLogic 11g, Apache HTTP Server 2.4"}]},{"label":["Skills"],"points":[{"start":2951,"end":2978,"text":"Continuous Integration Tools"}]},{"label":["Skills"],"points":[{"start":2936,"end":2949,"text":"Web/Appservers"}]},{"label":["Skills"],"points":[{"start":2915,"end":2934,"text":"Subversion(SVN),GIT\n"}]},{"label":["Skills"],"points":[{"start":2891,"end":2913,"text":"Version Control Tools \n"}]},{"label":["Graduation Year"],"points":[{"start":2650,"end":2653,"text":"2015"}]},{"label":["Location"],"points":[{"start":2630,"end":2638,"text":"Hyderabad"}]},{"label":["Companies worked at"],"points":[{"start":2622,"end":2627,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":2596,"end":2617,"text":"Configuration Engineer"}]},{"label":["Companies worked at"],"points":[{"start":1785,"end":1790,"text":"Oracle"}]},{"label":["Skills"],"points":[{"start":1109,"end":1114,"text":"Docker"}]},{"label":["Skills"],"points":[{"start":1051,"end":1056,"text":"Docker"}]},{"label":["Skills"],"points":[{"start":790,"end":793,"text":"CHEF"}]},{"label":["Skills"],"points":[{"start":667,"end":670,"text":"AWS\n"}]},{"label":["Skills"],"points":[{"start":667,"end":669,"text":"AWS"}]},{"label":["Email Address"],"points":[{"start":101,"end":147,"text":"indeed.com/r/PRASHANTH-BADALA/\nbf4c4b7253a8ece7"}]},{"label":["Location"],"points":[{"start":58,"end":66,"text":"Hyderabad"}]},{"label":["Companies worked at"],"points":[{"start":50,"end":55,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":34,"end":47,"text":"Cloud Engineer"}]},{"label":["Designation"],"points":[{"start":17,"end":31,"text":"Devops Engineer"}]},{"label":["Name"],"points":[{"start":0,"end":15,"text":"PRASHANTH BADALA"}]}]}
{"content": "Pratibha P\nPrincipal Consultant at Oracle\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Pratibha-P/b4c1202741d63c6c\n\nOver 14 years of experience in estimation, design, development, implementation, testing and\nenhancement of various Oracle applications. Currently working as Principal Consultant (Oracle\nApplications) with the consulting group Global Service Delivery (GSD) of Oracle India Pvt Ltd,\nBangalore. Possesses excellent communication, extensive functional and technical experience in\nimplementing Oracle E-business Suite applications.\nWorked on various modules like AR, AP, GL, FA, OLFM, HRMS and PO. Has gained proficiency in\nOracle 9i/10g, SQL, PL/SQL, Discoverer 10g, Oracle reports 6i/10g, BI Publisher reports, OEM (For\ndata masking), Methodologies (Oracle AIM, OUM) & Migration Tool (Kintana)\n\nWilling to relocate to: Bangalore City, Karnataka\n\nWORK EXPERIENCE\n\nPrincipal Consultant\n\nOracle -\n\nJuly 2012 to Present\n\nOver 14 years of experience in estimation, design, development, implementation, testing and\nenhancement of various Oracle applications. Currently working as Principal Consultant (Oracle\nApplications) with the consulting group Global Service Delivery (GSD) of Oracle India Pvt Ltd,\nBangalore. Possesses excellent communication, extensive functional and technical experience in\nimplementing Oracle E-business Suite applications.\nWorked on various modules like AR, AP, GL, FA, OLFM, HRMS and PO. Has gained proficiency in\nOracle 9i/10g, SQL, PL/SQL, Discoverer 10g, Oracle reports 6i/10g, BI Publisher reports, OEM(For\ndata masking),Methodologies(Oracle AIM,OUM)& Migration Tool(Kintana)\n\nSenor Consultant\n\nOracle -\n\n2011 to July 2011\n\nStaff Consultant\n\nCaterpillar -\n\nDecember 2005 to December 2007\n\nDuration/Size: 4 months/10\n\nAssociate Consultant\n\nCaterpillar -\n\nOctober 2004 to December 2005\n\nhttps://www.indeed.com/r/Pratibha-P/b4c1202741d63c6c?isid=rex-download&ikw=download-top&co=IN\n\n\nDuration/Size: 4 months/10\n\nAssociate Consultant\n\nVault Consulting -\n\nOctober 2003 to October 2004\n\nProject Name: AMAT\nClient: Applied Materials\nDuration/Size: 1 months/3\nOrganization: Oracle\nEnvironment: Oracle Applications (11i)\nResponsibilities: AMAT was an implementation project. I was involved in developing complex\nDiscoverer reports which fully meets the objectives for forecasting the history and exception\ndetails of AMAT's buyers/Suppliers with capability to review FGI (Finished Goods Inventory), LLI\n(Linked Level Inventory) details\n\nProject Name: Dollar General\nClient: Dollar General\nDuration/Size: 2 months/3\nOrganization: Oracle\nEnvironment: Data Warehousing\nResponsibilities: Dollar General was an implementation project. I was involved in developing\nDiscoverer reports.\n\nProject Name: Emerson\nClient: Emerson Process Management\nDuration/Size: 2 months/3\nOrganization: Oracle\nEnvironment: Data Warehousing\nResponsibilities: Dollar General was an implementation project. I was involved in developing\nDiscoverer report for each division-country, division-world area and total division (single reports) .\nThe divisions were also compared against each other (comparison reports)\n\nEDUCATION\n\nB.E\n\nVisvesvaraya Technological University\n\n2017\n\nBachelor of Engineering in Engineering\n\nDayanand Sagar College of Engineering Bangalore (Visvesvaraya Technological University) -\nSagar, Karnataka\n\n\n\nSKILLS\n\nExtension, PLSQL, BI Publisher Reports, Oracle Reports, AP, HRMS, GL, Conversion, Oracle Apps\ntechnical, Interface, AR, TOAD, OLFM, SQL, FA\n\nADDITIONAL INFORMATION\n\nSoftware Skills:\n\nERP-Oracle Applications 11i, R12\nAccounts Receivable, Accounts Payable, Fixed Assets, General Ledger, Oracle Lease and Finance\nManagement, Purchasing and HRMS (Employee, Assignment, Contacts, Pay methods, Job and\nLocation)\n\nRDBMS Oracle […]\nLANGUAGES C++\nTOOLS Toad, SQL Developer, Kintana, Report Builder 6i/10g, Discoverer Admin/Desktop/Plus/\nViewer and BI Publisher","annotation":[{"label":["Companies worked at"],"points":[{"start":3758,"end":3763,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":3630,"end":3635,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":3532,"end":3537,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":3427,"end":3432,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":3385,"end":3390,"text":"Oracle"}]},{"label":["Skills"],"points":[{"start":3345,"end":3895,"text":"Extension, PLSQL, BI Publisher Reports, Oracle Reports, AP, HRMS, GL, Conversion, Oracle Apps\ntechnical, Interface, AR, TOAD, OLFM, SQL, FA\n\nADDITIONAL INFORMATION\n\nSoftware Skills:\n\nERP-Oracle Applications 11i, R12\nAccounts Receivable, Accounts Payable, Fixed Assets, General Ledger, Oracle Lease and Finance\nManagement, Purchasing and HRMS (Employee, Assignment, Contacts, Pay methods, Job and\nLocation)\n\nRDBMS Oracle […]\nLANGUAGES C++\nTOOLS Toad, SQL Developer, Kintana, Report Builder 6i/10g, Discoverer Admin/Desktop/Plus/\nViewer and BI Publisher"}]},{"label":["College Name"],"points":[{"start":3276,"end":3312,"text":"Visvesvaraya Technological University"}]},{"label":["College Name"],"points":[{"start":3227,"end":3263,"text":"Dayanand Sagar College of Engineering"}]},{"label":["Degree"],"points":[{"start":3187,"end":3224,"text":"Bachelor of Engineering in Engineering"}]},{"label":["College Name"],"points":[{"start":3142,"end":3178,"text":"Visvesvaraya Technological University"}]},{"label":["Degree"],"points":[{"start":3137,"end":3140,"text":"B.E\n"}]},{"label":["Companies worked at"],"points":[{"start":2819,"end":2824,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":2571,"end":2576,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":2137,"end":2142,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":2117,"end":2122,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1646,"end":1651,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1586,"end":1591,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1505,"end":1510,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1461,"end":1466,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1331,"end":1336,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1201,"end":1206,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1121,"end":1126,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":1099,"end":1118,"text":"Principal Consultant"}]},{"label":["Companies worked at"],"points":[{"start":1057,"end":1062,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":910,"end":915,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":888,"end":907,"text":"Principal Consultant"}]},{"label":["Companies worked at"],"points":[{"start":775,"end":780,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":691,"end":696,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":647,"end":652,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":517,"end":522,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":387,"end":392,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":307,"end":312,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":285,"end":304,"text":"Principal Consultant"}]},{"label":["Companies worked at"],"points":[{"start":243,"end":248,"text":"Oracle"}]},{"label":["Location"],"points":[{"start":43,"end":51,"text":"Bengaluru"}]},{"label":["Companies worked at"],"points":[{"start":35,"end":40,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":11,"end":30,"text":"Principal Consultant"}]},{"label":["Name"],"points":[{"start":0,"end":9,"text":"Pratibha P"}]}]}
{"content": "Prem Koshti\nOfficer-HR & Administration in H.& R. Johnson (India) - SAP - R\n\nDewas, Madhya Pradesh - Email me on Indeed: indeed.com/r/Prem-Koshti/a1fec9e7289496f0\n\n❖ To acquire a key Position in Human Resource Management / SAP field by continuously improving\nknowledge and skills.\n❖ Very strong logical, analytical skills with vast experience in MS-EXCEL.\n❖ Very energetic, hardworking and highly self-motivated team player with strong problem solving\nskills and very good communication and leadership skills. Very flexible.\n\nProjects:-\nProject Name: SAP HR, Employee Administration\nClient: H.& R. Johnson (India) [A Division of Prism Cement Limited], DEWAS (M.P.)\n\nWORK EXPERIENCE\n\nOfficer-HR & Administration in H.& R. Johnson (India)\n\nSAP - R -  Dewas, Madhya Pradesh -\n\nJuly 2002 to Present\n\n- SAP - R/3, 06 years' experience in SAP HR-Functional Module\nCurrent Employer:\nPresently working as Officer-HR & Administration in H.& R. Johnson (India), [A Division of Prism\nCement Limited], DEWAS (M.P.) from 30.07.2002 to till date.\n\nEDUCATION\n\nB.Com. in Dr. Harisingh Gour V.V\n\nPolytechnic College Damoh -  Sagar, Madhya Pradesh\n\n1990\n\nSKILLS\n\nHR (10+ years), SAP (10+ years), APPRAISAL (Less than 1 year), BUYING/PROCUREMENT (Less\nthan 1 year), DATABASE (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTechnical skills: SAP HR Module.\nDescription:-\nMaintaining electronic punching system, Daily Production MIS Report, Monthly Manpower\nreport. I.F. Annual Returns, Form-5 Holiday wages. Employee's gratuity policy updation.\nMaintaining all record's & document related to personal / HR department, Employees data\nbank. Employees leave, attendance, c-off, absenteeism statement. Payroll Preparation, Bonus,\n\nhttps://www.indeed.com/r/Prem-Koshti/a1fec9e7289496f0?isid=rex-download&ikw=download-top&co=IN\n\n\nOvertime, Attendance Incentive, Production Incentive, Arrear Wages, Wage slip, Full & Final\nSettlement, No Dues Certificate, Issue of certificate if any, ID / Punching card, Birth day card etc.\n\nRoles and responsibilities: Personal & HR Functions:\n\n✓ Performance Appraisal / Increments preparation co-ordination.\n\n✓ Computerized Time Office Management, HR Policy.\n\n✓ Handle Payroll on SAP & responsible for smooth functioning of payroll cycle.\n\n✓ Maintain employee data on SAP and updates them as and when required\n\n✓ Reconciling statutory reports i.e. PF, ESIC, and coordinating with Finance Team for timely\npayment.\n\n✓ Production & manpower MIS data in MS-Excel daily, Monthly & Yearly basis.\n\n✓ Performing of Exit Formalities and processing Full and Final Settlement for exit cases.\n\n✓ Maintain Attendance record in Electronic Punching Machine.\n\n✓ Joining Formalities (Pre & Post recruitment activities)\n\nGeneral Administration\n\nOffice stationery, Telephones, Fax, Computers, Reception, Purchasing First Aid, mineral water,\nbiscuits etc\n\nTechnical Expertise\nFront End Tool\nLanguages\nSAP Technologies HR and Administration Functional Module\nDatabase FOXPRO 6.22\nConcepts OOP'S, Networking, DBMS, Operating System.\nOperating System MS-Dos […] & MS OFFICE 2007, 2008 & 2010\n\nTRAININGIG PROGRAMME & CONFERENCE ATTENDED:\n\n❖ First Aid Procedure conducted by St. John Ambulance Association.\n\n❖ Fire Fighting by Usha Fire Safety.\n\n❖ Interpersonal Skills, Communication, Motivational related various Training programmes\norganize.\n\n❖ SAP - HR Module conducted by Covansys, Mumbai.\n\n\n\n❖ Internal Auditors Training Program on Environmental & Occupational Health & Safety\nManagement System","annotation":[{"label":["Companies worked at"],"points":[{"start":3327,"end":3329,"text":"SAP"}]},{"label":["Companies worked at"],"points":[{"start":2886,"end":2888,"text":"SAP"}]},{"label":["Skills"],"points":[{"start":2861,"end":3073,"text":"Front End Tool\nLanguages\nSAP Technologies HR and Administration Functional Module\nDatabase FOXPRO 6.22\nConcepts OOP'S, Networking, DBMS, Operating System.\nOperating System MS-Dos […] & MS OFFICE 2007, 2008 & 2010\n"}]},{"label":["Companies worked at"],"points":[{"start":2273,"end":2275,"text":"SAP"}]},{"label":["Companies worked at"],"points":[{"start":2185,"end":2187,"text":"SAP"}]},{"label":["Companies worked at"],"points":[{"start":1318,"end":1320,"text":"SAP"}]},{"label":["Companies worked at"],"points":[{"start":1161,"end":1163,"text":"SAP"}]},{"label":["Skills"],"points":[{"start":1145,"end":1273,"text":"HR (10+ years), SAP (10+ years), APPRAISAL (Less than 1 year), BUYING/PROCUREMENT (Less\nthan 1 year), DATABASE (Less than 1 year)"}]},{"label":["Designation"],"points":[{"start":897,"end":949,"text":"Officer-HR & Administration in H.& R. Johnson (India)"}]},{"label":["Companies worked at"],"points":[{"start":833,"end":835,"text":"SAP"}]},{"label":["Companies worked at"],"points":[{"start":798,"end":800,"text":"SAP"}]},{"label":["Location"],"points":[{"start":749,"end":753,"text":"Dewas"}]},{"label":["Companies worked at"],"points":[{"start":738,"end":740,"text":"SAP"}]},{"label":["Designation"],"points":[{"start":683,"end":735,"text":"Officer-HR & Administration in H.& R. Johnson (India)"}]},{"label":["Companies worked at"],"points":[{"start":551,"end":553,"text":"SAP"}]},{"label":["Companies worked at"],"points":[{"start":223,"end":225,"text":"SAP"}]},{"label":["Email Address"],"points":[{"start":120,"end":161,"text":" indeed.com/r/Prem-Koshti/a1fec9e7289496f0"}]},{"label":["Location"],"points":[{"start":77,"end":81,"text":"Dewas"}]},{"label":["Companies worked at"],"points":[{"start":68,"end":70,"text":"SAP"}]},{"label":["Designation"],"points":[{"start":12,"end":64,"text":"Officer-HR & Administration in H.& R. Johnson (India)"}]},{"label":["Name"],"points":[{"start":0,"end":10,"text":"Prem Koshti"}]}]}
{"content": "Pulkit Saxena\nNew Delhi, Delhi - Email me on Indeed: indeed.com/r/Pulkit-Saxena/ad3f35bfe88a0410\n\nI have a high degree of technical competence, a strong learning aptitude and an excellent\nwork ethic. I am a technical expert in a number of network areas, in particular connectivity,\nperformance, scalability and security. As an articulate communicator, I have ability to influence\npeople at every level by ensuring that issues are discussed, conflicts are resolved and the best\nsolutions are delivered. In my current role I work with the rest of the team to ensure the successful\ndelivery and operation of all supported services. Right now, I would like to join a growing company\nthat wants to recruit proven and experienced IT people.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nCisco -  Samba, Jammu and Kashmir -\n\n2000 to 2000\n\nAmple knowledge in Windows 98 \\ Me \\ Xp \\ 2000 \\ Server 2003 \\Server 2008 \\Server\n2008r2\\Server 2012\n✓ Active directory management, NTFS security, disk quota management\n✓ Good understanding of OSI Model, TCP/IP protocol suite (IP, ARP, ICMP, TCP, UDP, RARP, FTP,\nTFTP)\n✓ Ample understanding of Bridging and switching concepts and LAN technologies\n✓ IP addressing and subnetting, Routing concepts\n✓ Sound knowledge of routing protocols - RIP V1/V2, OSPF, IGRP & EIGRP\n✓ Switches: Basic Configuration & VLAN setup on Cisco 1900, 2950, 2960 Switches &\nTroubleshooting.\n✓ Router: Basic Configuration & monitoring of Cisco 2500, 2600, 1800\n✓ Vlan: configuration, switching isl, dotlq\n✓ Cisco Firewall 5500 Series: Configuration and policies Setup and Troubleshooting.\n✓ Back-up and restore of all critical resources including router & switches IOS, Outlook, DHCP,\nDNS.\n✓ Functioning knowledge of wan solution, protocol HDLC, PPP\n✓ Working knowledge of, DHCP Server, DNS Server, ADDS, Proxy Server on Linux and Complete\nKnowledge on Windows\n✓ Security administration port security on switch and IP security on Router via Access list\n✓ Familiar with web technology HTML CSS\n✓ Setting up Secure NFS Servers with multiple Clients for File and Disk sharing.\n✓ Setting up SAMBA servers, to enable Windows clients to communicate with Linux without the\nneed of additional software on the Windows side.\n✓ Configuring NIS Servers for Centralized and secure Password and Login Management\n✓ Linux user management creating, changing group, and assign permission on resources\n✓ Recover of root password securing terminals\nHardware\n✓ Computer assembling and maintenance.\n✓ Troubleshooting hardware and software problems.\n✓ Installing and configuring the peripherals, components and drivers.\n\nhttps://www.indeed.com/r/Pulkit-Saxena/ad3f35bfe88a0410?isid=rex-download&ikw=download-top&co=IN\n\n\n✓ Installing software and application to user standards.\n\nEDUCATION\n\nMCL in Computer Application\n\nIGNOU\n\nBACHELOR'S IN SCIENCE in COMPUTER NETWORKING\n\nKarnataka State University\n\nComputer Networking\n\nAptech Institute of Technology\n\nComputing\n\nAptech Institute of Technology\n\nSKILLS\n\nFirewall (1 year), HTML (1 year), MICROSOFT WINDOWS (1 year), Router (1 year), security. (1\nyear)\n\nADDITIONAL INFORMATION\n\nSKILLS\n✓ A computer expert with hardware and software grab.\n✓ Administration and Back-end handling (Technical) on overall basis of the Branch.\n✓ Convincing people tactfully so as to make the company an option for the user.\n\nAREAS OF EXPERTISE\n✓ Server administration\n✓ Technical documentation\n✓ Network security\n✓ Network management\n✓ Data backups\n✓ Disaster recovery\n✓ Cisco Router\n✓ Cisco Switch\n✓ Network management\n✓ Switching\n✓ Routers\n✓ Firewalls\n✓ Firewall principles\n✓ Remote Access\n\nPROFESSIONAL\n✓ ACESE RIM\n\n\n\n✓ MICROSOFT Certified\n\nADDITIONAL SKILLS\n✓ ADVANCED MS OFFICE (WORD, POWERPOINT, EXCEL, ETC.)\n✓ ADOBE PHOTOSHOP, READER X, ACROBAT, COREL DRAW, MICROMEDIA FLASH ETC.\n✓ JAVA NETBEANS, SQL, HTML, CMD PROMPT, ETC.\n✓ FAMILIAR WITH ALL SORTS OF WINDOW SETS AND WEB BROWSERS.\n✓ ALL KINDS OF UTILITY SOFTWARES AND HARDWARE OPTIONS TO INCREASE EFFICIENCY AND\nEFFECTIVENESS IN WORKING.\n✓ COMPLETE KNOWLEDGE OF MICROSOFT EXCHANGE SERVER 2012.\n✓ CAN DEVELOP WEBSITES BASED ON HTML, PHP ETC.\n✓ BASIC KNOWLEDGE OF ANDROID AND MAC APPLICATIONS.\n✓ COMPLETE KNOWLEDGE OF SERVER AND CLIENT BASED ENVIRONMENT\n✓ BASIC KNOWLEDGE OF MICROSOFT WINDOWS FIREWALL","annotation":[{"label":["Skills"],"points":[{"start":3673,"end":4269,"text":"✓ ADVANCED MS OFFICE (WORD, POWERPOINT, EXCEL, ETC.)\n✓ ADOBE PHOTOSHOP, READER X, ACROBAT, COREL DRAW, MICROMEDIA FLASH ETC.\n✓ JAVA NETBEANS, SQL, HTML, CMD PROMPT, ETC.\n✓ FAMILIAR WITH ALL SORTS OF WINDOW SETS AND WEB BROWSERS.\n✓ ALL KINDS OF UTILITY SOFTWARES AND HARDWARE OPTIONS TO INCREASE EFFICIENCY AND\nEFFECTIVENESS IN WORKING.\n✓ COMPLETE KNOWLEDGE OF MICROSOFT EXCHANGE SERVER 2012.\n✓ CAN DEVELOP WEBSITES BASED ON HTML, PHP ETC.\n✓ BASIC KNOWLEDGE OF ANDROID AND MAC APPLICATIONS.\n✓ COMPLETE KNOWLEDGE OF SERVER AND CLIENT BASED ENVIRONMENT\n✓ BASIC KNOWLEDGE OF MICROSOFT WINDOWS FIREWALL"}]},{"label":["Companies worked at"],"points":[{"start":3497,"end":3501,"text":"Cisco"}]},{"label":["Companies worked at"],"points":[{"start":3482,"end":3486,"text":"Cisco"}]},{"label":["Skills"],"points":[{"start":3121,"end":3333,"text":"A computer expert with hardware and software grab.\n✓ Administration and Back-end handling (Technical) on overall basis of the Branch.\n✓ Convincing people tactfully so as to make the company an option for the user."}]},{"label":["Skills"],"points":[{"start":2989,"end":3085,"text":"Firewall (1 year), HTML (1 year), MICROSOFT WINDOWS (1 year), Router (1 year), security. (1\nyear)"}]},{"label":["College Name"],"points":[{"start":2949,"end":2978,"text":"Aptech Institute of Technology"}]},{"label":["College Name"],"points":[{"start":2906,"end":2935,"text":"Aptech Institute of Technology"}]},{"label":["Degree"],"points":[{"start":2885,"end":2903,"text":"Computer Networking"}]},{"label":["College Name"],"points":[{"start":2857,"end":2883,"text":"Karnataka State University\n"}]},{"label":["Degree"],"points":[{"start":2811,"end":2854,"text":"BACHELOR'S IN SCIENCE in COMPUTER NETWORKING"}]},{"label":["College Name"],"points":[{"start":2804,"end":2809,"text":"IGNOU\n"}]},{"label":["Degree"],"points":[{"start":2774,"end":2802,"text":"\nMCL in Computer Application\n"}]},{"label":["Companies worked at"],"points":[{"start":1516,"end":1520,"text":"Cisco"}]},{"label":["Companies worked at"],"points":[{"start":1447,"end":1451,"text":"Cisco"}]},{"label":["Companies worked at"],"points":[{"start":1350,"end":1354,"text":"Cisco"}]},{"label":["Companies worked at"],"points":[{"start":784,"end":788,"text":"Cisco"}]},{"label":["Email Address"],"points":[{"start":53,"end":96,"text":"indeed.com/r/Pulkit-Saxena/ad3f35bfe88a0410\n"}]},{"label":["Location"],"points":[{"start":14,"end":22,"text":"New Delhi"}]},{"label":["Name"],"points":[{"start":0,"end":12,"text":"Pulkit Saxena"}]}]}
