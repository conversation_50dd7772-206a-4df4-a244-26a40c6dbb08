{"id": 0, "text": "<PERSON><PERSON><PERSON>\nSenior System Engineer - Infosys Limited\n\nSalem, Tamil Nadu - Email me on Indeed: indeed.com/r/Kavitha-K/8977ce8ce48bc800\n\nSeeking to work with a software firm, to constantly upgrade my knowledge and utilize my\nexisting skills to benefit the concerned organization\n\nWORK EXPERIENCE\n\nSenior System Engineer\n\nInfosys Limited -\n\nDecember 2014 to Present\n\nUnix, CA7 scheduler\n\nInfosys Limited -\n\nDecember 2015 to February 2018\n\nQlikview Level 1 \u2022 Basic knowledge of creating simple dashboards with different\nProduction support style using Qlikview components like List Box, Slider, Buttons,\ncharts and Bookmarks.\n\u2022 Created different types of sheet objects like List boxes, Buttons,\nMulti box.\n\u2022 Good knowledge of monitoring Qlikview Dashboards\n\u2022 Monitoring Critical dashboards and communicating delay to clients\n\u2022 Involved in Qlikview initial error analysis and the concerned\nteam to trouble shoot the issue\n\u2022 Monitoring Qlikview dashboards from end to end and manually\nrefreshing the tasks if needed\n\u2022 Handling service request for manual refresh of dashboards\n\u2022 Monitoring Qlikview dependent ETL jobs in CA7 job scheduler\nLevel 1 (BI process) \u2022 Involved in monitoring batch jobs in CA7 job scheduler\n\u2022 Managing the daily workload based on priorities and maintain\nSLA's to provide quality services to end users\n\u2022 Responsible for sending daily and weekly reports to the clients\n\u2022 Initial analysis of log files and fixing of environment related\nissue in ETL Process\n\u2022 Coordinating with concerned team in troubleshooting of major\nbusiness related issues and sending notification to the clients on timely manner\n\u2022 Responsible for all Process related activities like incident\nmanagement and change management\n\u2022 Involved in documenting the process, procedures and flow of ETL Process for critical\napplications\n\u2022 Respond to user service requests and resolving them with in stipulated time\n\u2022 Participated in Incident Management and Problem\nManagement processes for root cause analysis, resolution and reporting\n\nhttps://www.indeed.com/r/Kavitha-K/8977ce8ce48bc800?isid=rex-download&ikw=download-top&co=IN\n\n\nEDUCATION\n\nNetworking, Infosys Limited\n\nFoundation Training Program in Networking\n\n2016\n\nBachelor of Engineering in Information Technology\n\nREiume Institute of road -  Erode, Tamil Nadu\n\n2014", "labels": [[2292, 2296, "Graduation Year"], [2245, 2269, "College Name"], [2194, 2243, "Degree"], [2145, 2187, "Degree"], [2128, 2144, "Companies worked at"], [2116, 2143, "College Name"], [2022, 2061, "Email Address"], [345, 349, "Graduation Year"], [92, 131, "Email Address"], [52, 57, "Location"], [35, 51, "Companies worked at"], [10, 33, "Designation"], [0, 9, "Name"]]}
{"id": 1, "text": "Kavya U.\nNetwork Ops Associate - Accenture\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Kavya-U/049577580b3814e6\n\nSeeking for opportunities to learn and grow in electronics domain.\n\nWORK EXPERIENCE\n\nNetwork Ops Associate\n\nAccenture -  Bengaluru, Karnataka -\n\nDecember 2017 to Present\n\nExposure:\n\u2022 Provisioning of different types of network speeds for multiple clients.\n\u2022 Use of Mux designing for Logical cross connect and Physical patching. We co- ordinate with\nvarious ops and field engineers to do the connections at the physical\nlevel.\n\n2) Organisation: QuadGen Wireless Engineering Services Pvt. Ltd., Bangalore.\nPosition: Network Engineer\nExperience: 1 year\nExposure:\n\u2022 RAN (Radio Access Network) Engineer.\n\u2022 New Site Build (NSB)\n\n2) Organisation: Manipal Dot Net Pvt. Ltd., Manipal.\nPosition: Intern\nExperience: 1 year\nExposure:\n\u2022 Module coding and verification using Verilog HDL\n\u2022 Worked on Linux O.S.\n\u2022 Understanding of SPI, I2C protocols\n\u2022 Compilation using Altera Quartus\n\u2022 Simulation using ModelSim\n\u2022 Report preparation and documentation\n\nEDUCATION\n\nLittle Rock Indian School\n\n2007\n\nMaster of Science in Technology in Technology\n\nSchool Of Information Sciences\n\nVLSI Design\n\nhttps://www.indeed.com/r/Kavya-U/049577580b3814e6?isid=rex-download&ikw=download-top&co=IN\n\n\nManipal Academy of Higher Education\n\nBachelor of Engineering in Engineering\n\nSrinivas Institute of Technology -  Mangalore, Karnataka\n\nElectronics and Communication\n\nVisvesvaraya Technological University\n\nVidyodaya P.U. College -  Udipi, Karnataka\n\nSKILLS\n\ncoding (Less than 1 year), HDL (Less than 1 year), Microsoft office (Less than 1 year), MS\nOFFICE (Less than 1 year), UART (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTechnical Skills\n\u2022 Verilog HDL\n\u2022 Knowledge of RTL coding, FSM based designs.\n\u2022 Understanding of UART, AMBA protocol\n\u2022 Platforms: Microsoft office, Libreoffice", "labels": [[1844, 1873, "Skills"], [1844, 1860, "Skills"], [1811, 1815, "Skills"], [1794, 1830, "Skills"], [1765, 1771, "Skills"], [1748, 1790, "Skills"], [1742, 1745, "Skills"], [1734, 1745, "Skills"], [1666, 1670, "Skills"], [1636, 1645, "Skills"], [1599, 1615, "Skills"], [1575, 1578, "Skills"], [1548, 1554, "Skills"], [1496, 1518, "College Name"], [1457, 1495, "College Name"], [1368, 1400, "College Name"], [1328, 1366, "Degree"], [1291, 1326, "College Name"], [1210, 1247, "Email Address"], [1153, 1184, "College Name"], [1106, 1183, "Degree"], [1100, 1104, "Graduation Year"], [1073, 1099, "College Name"], [894, 897, "Skills"], [886, 897, "Skills"], [856, 862, "Skills"], [247, 256, "Location"], [234, 244, "Companies worked at"], [211, 233, "Designation"], [87, 124, "Email Address"], [44, 53, "Location"], [0, 8, "Name"]]}
{"id": 2, "text": "Khushboo Choudhary\nDeveloper\n\nNoida, Uttar Pradesh - Email me on Indeed: indeed.com/r/Khushboo-Choudhary/\nb10649068fcdfa42\n\nTo pursue a challenging career and be part of a progressive organization that gives scope to\nenhance my\nknowledge, skills and to reach the pinnacle in the computing and research field with sheer\ndetermination,\ndedication and hard work.\n\nWORK EXPERIENCE\n\nDeveloper\n\nSAP ABAP -  Noida, Uttar Pradesh -\n\nJanuary 2018 to May 2018\n\nSAP ABAB 7.4 Noida, Uttar\n(5 months) Pradesh\n\nTechnical Proficiency\n1. SAP ABAP 7.4 2. OOPS\n3. DBMS 4. Core Java\n5. C/C++ 6. Data Structures\n\nRoles and Responsibilities\n\u2022 Creating report generating modules.\n\u2022 Creating interactive modules for trainers to train.\n\nOfficial Projects\n1. Uploading file from non-sap system to sap system using BAPI.\n2. Uploading excel data using BDC.\n3. Generating Adobe forms.\n4. Creating smart forms for order purchasing.\n5. Automatic email sending module using workflow.\n6. Creating classical reports.\n7. Creating function module.\n\nEDUCATION\n\nB.Tech in CSE\n\nMM University\n\n2013 to 2017\n\nCBSE\n\nhttps://www.indeed.com/r/Khushboo-Choudhary/b10649068fcdfa42?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Khushboo-Choudhary/b10649068fcdfa42?isid=rex-download&ikw=download-top&co=IN\n\n\nParatap Public School -  Karnal, Haryana\n\nDecember 2011\n\nCBSE in Technology Used\n\nSilver Bells Public School -  Muzaffarnagar, Uttar Pradesh\n\nOctober 2009\n\nEngineering College\n\nSKILLS\n\nANDROID (Less than 1 year), CISCO (Less than 1 year), NETWORKING (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nDevelopment Skills.\n\nMay 2016 1.5 Learned about \"Basic Networking\" using Cisco MMEC, Mullana\nPacket Tracer.\n\nJune, 2015 2 Built an application which have functionality of Solitaire Infosys inc.\nconverting a text into speech using text to speech class Mohali, India\nnamed \"kid'z speak\" using Android.", "labels": [[1466, 1549, "Skills"], [1363, 1421, "College Name"], [1281, 1321, "College Name"], [1063, 1067, "Graduation Year"], [1040, 1053, "College Name"], [1025, 1038, "Degree"], [522, 531, "Companies worked at"], [464, 469, "Location"], [401, 406, "Location"], [389, 398, "Companies worked at"], [378, 387, "Designation"], [73, 123, "Email Address"], [30, 35, "Location"], [19, 28, "Designation"], [0, 18, "Name"]]}
{"id": 3, "text": "kimaya sonawane\nThane, Maharashtra - Email me on Indeed: indeed.com/r/kimaya-\nsonawane/1f27a18d2e4b1948\n\nQuality education blended with sense of responsibility to utilize my professional as well as\ninterpersonal skills that enables me to achieve the goals.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nTechnical Support Engineer\n\nSAP -  Thane, Maharashtra -\n\nNovember 2016 to Present\n\nEDUCATION\n\nBE in computer science\n\nSSVPS\u2019s Late B. S. Deore College of Engineering ,Dhule -  Dhule, Maharashtra\n\n2011 to 2016\n\nSKILLS\n\nnetwork engineers, Networking, CCNA, knowledge of Active Directory, DHCP, DNS ,\nTroubleshooting and fix Network related issues (2 years)\n\nCERTIFICATIONS/LICENSES\n\nCCNA(Cisco Certified Network Associate- Routing & Switching) , MCSA\n(Microsoft Certified Solution Associate)\n\nJuly 2016 to Present\n\nADDITIONAL INFORMATION\n\nPROFESSIONAL INTRESTS:\n\u2022 Leading and managing teams\n\u2022 Interacting with People\nCO CURRICULAR ACTIVITES:\n\u2022 Participated in \"Mech-Tricks\" in IMPULSE 2014 National Level Event.\n\u2022 Participated in \"Mech-Tricks\" in IMPULSE 2013 National Level Event.\n\u2022 Participated in \"Tech-Quiz\" in IMPULSE 2013 National Level Event.\n\u2022 Participated in \"Management Games\" Organised in Ganesh Utsav 2012.\n\u2022 Winner in \"Rangoli Competition\" Organised in Ganesh Utsav 2013.\n\nhttps://www.indeed.com/r/kimaya-sonawane/1f27a18d2e4b1948?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/kimaya-sonawane/1f27a18d2e4b1948?isid=rex-download&ikw=download-top&co=IN\n\n\nPERSONAL TRAITS:\n\n\u2022 Self Motivated\n\u2022 Adaptable\n\u2022 Confident\n\u2022 Team facilitator\n\u2022 Hard Worker", "labels": [[802, 806, "Graduation Year"], [524, 661, "Skills"], [510, 514, "Graduation Year"], [424, 471, "College Name"], [400, 422, "Degree"], [372, 376, "Graduation Year"], [341, 346, "Location"], [334, 339, "Companies worked at"], [306, 332, "Designation"], [57, 103, "Email Address"], [16, 21, "Location"], [0, 15, "Name"]]}
{"id": 4, "text": "Koushik Katta\nDevops\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Koushik-Katta/a6b19244854199ec\n\nDevOps Administrator with an experience of 3.4 years working in a challenging agile\nenvironment, looking forward for a position where I can use my knowledge pursuing my domain\ninterests. I'm more aligned to work for companies where knowledge and intellectual ability takes\nthe lead which can utilize a performance driven individual efficiently.\n\nWORK EXPERIENCE\n\nDevops Engineer\n\nInfosys limited -  Hyderabad, Telangana -\n\nDecember 2014 to Present\n\nHyderabad, since December 2014 to till date.\n\nSkill and Abilities:\nAtlassian Tools: Jira, Confluence\nConfiguration Management: Ansible /Chef\nCI Tools: Jenkins\nMonitoring Tools: Nagios\nCloud: AWS\nContainerization: Docker\nBuild Tools: Bamboo\\Maven\nLog Tools: Splunk\nDatabases: RDBMS, MYSQL, Oracle Database\nProgramming Languages: Python and Java\nScripting: Power Shell\nOperating Systems: Windows, Linux family, Redhat Linux\nMiddleware: Websphere, Tomcat, Websphere MQ\n\nResponsibilities:\nDEVOPS ADMINISTRATOR\nINFOSYS LTD.\n\nAtlassian tools Release Management according\n\nInfosys limited -\n\nDecember 2014 to Present\n\nto project needs.\n\u2713 Review and upgrade of Plugins to meet project requirements and to achieve better\nperformance.\n\u2713 Configuring Automated Mail handlers, Webhooks as POC to test the new demands raised by\nclient.\n\nhttps://www.indeed.com/r/Koushik-Katta/a6b19244854199ec?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2713 JIRA Project Management/Administration.\n\u2713 Confluence Space Management/Administration.\n\u2713 Bitbucket Project/Repository Management/Administration (Enterprise/DataCenter)\n\u2713 Integration of Webhooks in Bitbucket.\n\u2713 Streamlining tools access management with Crowd.\n2. Administration and Maintenance of Jenkins\n\u2713 Configure and maintain Jenkins slaves as per the requirement.\n\u2713 Jenkins release management.\n\u2713 Work closely with Development teams to configure CI/CD Pipelines to automate their build &\ndeployment process.\n\u2713 Review, Installation/Upgrade and configuration of Jenkins Plugins.\n\u2713 Configuring proxy on the environments to enable security\n\u2713 Debug build issues\n3. Administration and Maintenance of Docker registry\n4. Working with Open-Source Nagios plugins on demand basis to setup ICINGA monitoring for our\non-premise Servers/Applications.\n5. Alerting Setup Splunk.\n6. Monitoring Dashboards setup using kibana.\n7. Working with product support teams to resolve the product bugs.\n8. Involve in client meetings and tool performance reviews to ensure stakeholder satisfaction.\n9. Work closely with Infrastructure Teams to setup/maintain/improve the above mentioned\napplication on large scale.\n\nEDUCATION\n\nBachelor Of Engineering in Mechanical Engineering\n\nLovely Professional University\n\n2010 to 2014\n\nSecondary School Certificate in education\n\nBoard of Intermediate education -  Hyderabad, Telangana\n\n2008 to 2010\n\nSister Nivedita School -  Karimnagar, Telangana\n\n2008\n\nSKILLS\n\nJira, Ansible, Jenkins, Splunk, Nagios, Docker, Python, AWS, Bamboo, Linux, Git, Chef, Windows,\nPowershell Scripting\n\nADDITIONAL INFORMATION\n\n\u2022 Ability to learn new technologies and processes rapidly and implement them in the project.\n\u2022 Highly motivated with very good problem solving and analytical skills Well organized, with\nexcellent in multitasking and prioritizing the work.\n\n\n\n\u2022 Effective communicator with an ability to convey ideas in speaking and writing.\n\u2022 Excellent analytical and decision making skills.\n\u2022 Ability to work in pressurized situation.\n\u2022 Hard worker and goal oriented.\n\u2022 Always ready to learn new skills", "labels": [[2957, 3074, "Skills"], [2943, 2948, "Graduation Year"], [2894, 2917, "College Name"], [2888, 2892, "Graduation Year"], [2858, 2867, "Location"], [2823, 2854, "Degree"], [2780, 2822, "Degree"], [2774, 2778, "Graduation Year"], [2766, 2770, "Graduation Year"], [2734, 2764, "College Name"], [2683, 2732, "Degree"], [1394, 1437, "Email Address"], [1153, 1157, "Graduation Year"], [1125, 1140, "Companies worked at"], [1044, 1064, "Designation"], [626, 1024, "Skills"], [585, 589, "Graduation Year"], [559, 568, "Location"], [542, 546, "Graduation Year"], [509, 518, "Location"], [490, 505, "Companies worked at"], [65, 108, "Email Address"], [22, 31, "Location"], [0, 13, "Name"]]}
{"id": 5, "text": "Kowsick Somasundaram\nCertified Network Associate Training Program\n\nErode, Tamil Nadu - Email me on Indeed: indeed.com/r/Kowsick-\nSomasundaram/3bd9e5de546cc3c8\n\nBachelor of computer science graduate seeking opportunities in the field of ITIS to contribute\nto corporate goals and objectives. Easily adapt to changes, with eagerness toward learning and\nexpanding capabilities.\n\nEXPERIENCE:-\n\nWORK EXPERIENCE\n\nCertified Network Associate Training Program\n\nCisco -\n\nJuly 2013 to October 2013\n\n\u2022 Workshop on computer Hardware& Software.\n\n\u2022 Workshop on Web development.\n\nEDUCATION\n\nBachelor of computer science in computer science\n\ninDR N.G.P ARTS AND SCIENCE COLLEGE -  Coimbatore, Tamil Nadu\n\nSKILLS\n\nDHCP (Less than 1 year), DNS (Less than 1 year), EXCHANGE (Less than 1 year), exchange\n(Less than 1 year), LAN (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS:-\n\n\u2022 Messaging: MS exchange, Lotus client and MS outlook issue coordination to user.\n\n\u2022 Users / Share folders creation and permission assigning.\n\n\u2022 Networking: TCP/IP, DNS, DHCP, and LAN/WAN.\n\n\u2022 Monthly patching update activity and server owner approval / RFC follow-ups.\n\nhttps://www.indeed.com/r/Kowsick-Somasundaram/3bd9e5de546cc3c8?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Kowsick-Somasundaram/3bd9e5de546cc3c8?isid=rex-download&ikw=download-top&co=IN", "labels": [[696, 1129, "Skills"], [625, 661, "College Name"], [575, 624, "Degree"], [451, 457, "Companies worked at"], [406, 450, "Designation"], [107, 158, "Email Address"], [67, 72, "Location"], [21, 65, "Designation"], [0, 20, "Name"]]}
{"id": 6, "text": "Lakshika Neelakshi\nSenior Systems Engineer - Infosys Limited\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Lakshika-\nNeelakshi/27b31f359c52ef76\n\nAn organized and independent individual looking for role to be able to effectively coordinate\ntasks in\nproject and accomplish the result adhering with timeliness and creativity.\n\nWORK EXPERIENCE\n\nSenior Systems Engineer\n\nInfosys Limited -  Bengaluru, Karnataka -\n\nJanuary 2018 to Present\n\nEnvironment SAPUI5 version 1.4\n\nDescription:\nAirbus SE is a European multinational corporation that designs, manufactures, and sells civil and\nmilitary aeronautical products worldwide.\n\nProject Contribution:\n\u2022 Working on creating a custom Annotation Tool (AnnoQ) using a third party js library to annotate\nany 2D picture within a SAPUI5 application specifically designed to work on laptop and Desktop.\n\u2022 The custom tool can be called from any SAP or Non- SAP system to be used as a plug-in to\nannotate pictures across the Airbus SE application to be used across browsers like\nChrome/Edge and IE and should be compatible with Windows 7, 8, 10.\n\u2022 Parts of a picture can be highlighted and marked and using this tool and can be saved for\nreference or future use.\n\u2022 Worked extensively on Fabric js and created various customized objects like Call out Box,\nMeasurement Arrow, Datum, Cross Datum, Forward arrow, etc on mouse and object events.\n\u2022 Implemented various functionality like color change, crop, zoom, text size selection, width\nselection, saving the annotation in JSON format in the backend so as to retain the original\npicture as it is.\n\u2022 Also contributed in designing the layout and overall appearance of the tool.\n\u2022 Contributed in integrating the application with MNC Mobile Application.\n\nSenior Systems Engineer\n\nInfosys Limited -  Bengaluru, Karnataka -\n\nOctober 2015 to Present\n\n\u2022 Having 2.5 years of experience in SAPUI5/Fiori, Netweaver Gateway Odata Services and SAP\nABAP development of large scale ERP packages.\n\u2022 Working with SAP/R3, ECC environments also having experience in HTML, JavaScript, JSON,\nXML, CSS.\n\nhttps://www.indeed.com/r/Lakshika-Neelakshi/27b31f359c52ef76?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Lakshika-Neelakshi/27b31f359c52ef76?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Currently working as Senior Systems Engineer with Infosys Limited, Bangalore since October\n2015.\n\nSAP Expertise\nSAPUI5/Fiori\n\n\u2022 Expertise in developing SAP UI5 applications using ADT (Eclipse) /WebIDE, jQuery, JavaScript,\nHTML5 and CSS3. Also consuming the data by using Net weaver Gateway services.\n\u2022 Well equipped in extending and customizing various UI5 controls specially charts, vizframes\nand Smart Table, etc.\n\u2022 Experience in integrating Gateway Odata/ JSON services with UI% application.\n\u2022 Worked extensively with application having multiple Views and controllers and hence\nexperienced in navigating through it using Routing using both XML and JS views.\n\u2022 Exposed to be working in SAPUI5 Custom Applications and Standard Fiori Applications.\n\u2022 Experienced with UI5 application development in local server and debug it in Chrome/Firefox\ndebuggers.\n\u2022 Experienced in Github.\n\nAdvance Business Application Programming\n\u2022 ALV (ABAP List Viewer) - Grid and List Display.\n\u2022 SAP smart forms and Scripts.\n\u2022 Worked in BDC (Batch Data Communication) - BI and CTU method\n\u2022 RFC Function Modules.\n\u2022 Exposure in creating Data Dictionary objects (Domain, Data Elements, Structures and Views)\n\u2022 Conceptual knowledge Dialog programs using Menu Painter/Screen Painter\n\u2022 Worked on Object Oriental Programming concepts (OOPS)\n\nSystems Engineer\n\nInfosys Limited -  Bengaluru, Karnataka -\n\nAugust 2017 to December 2017\n\nEnvironment SAPUI5 version 1.28\n\nDescription:\nJabil Inc. is a US based global manufacturing services company headquartered in St. Petersburg,\nFlorida. Jabil is involved in design engineering services. The company has industrial design\nservices\nthat concentrate on designing the look and feel of plastic and metal enclosures that house printed\ncircuit board assemblies and systems.\n\nProject Contribution:\n\u2022 Created custom Fiori apps for the client including screens having functionality of create\ndocument, Inbox to store the transaction mail and Dashboards to view day to day transactions.\n\u2022 Prepared multiple screens for the dashboard with view reusability features.\n\u2022 Worked on OData binding and hence display of data in relevant format on to the screen.\n\u2022 Implemented various other functionalities based on OData consumption, Routing and\nNavigation and JSON models.\n\n\n\n\u2022 Worked on complex functionality like excel data transfer directly to UI5 tables on screen while\nkeeping the table data editable and also excel data upload functions to the table.\n\u2022 Have clear and distinct knowledge of various UI5 controls.\n\nSystems Engineer\n\nInfosys Limited -  Hyderabad, Telangana -\n\n2016 to July 2017\n\nEnvironment SAPUI5 version 1.5\n\nDescription:\nAmerican Water is an American public utility company operating in the United States and Canada.\nIt\nwas founded in 1886 as the American Water Works & Guarantee Company.\n\nProject Contribution:\n\u2022 Worked as SAPUI5 developer in KPI project for American Waters account.\n\u2022 Developed KPI Dashboards using controls like Vizframe, Tables etc.\n\u2022 Worked with different types Micro charts control available like Column, Comparison, Radial etc.\n\u2022 Also extended some of them as per project requirements.\n\u2022 Experience of working with OData to get the data to be displayed on the dashboards.\n\u2022 Additionally, implemented some special features in the Dashboard development like Export to\nexcel link, Download Image of the UI charts & documentation link in Fiori tiles.\n\u2022 Also worked on multiple levels of drill downs using multiple controllers and views.\n\u2022 Individually worked and delivered an extension for one of the UI5 controls which got much\nappreciated from clients and offshore team as well.\n\u2022 Implementation of the apps on Fiori Launchpad.\n\u2022 Implemented multiple filters on the data pulled from the service for desired results as per\nproject requirements.\n\nSAP UI5:\nUI5 Controls:\n\u2022 Created dashboards by various UI5 controls such as Tables, vizFrame, Tab Filters to name a few.\n\u2022 Exposure in extending various UI5 standard controls to get the desired result.\n\u2022 Used SAP best practices while using all these controls.\n\nSystems Engineer\n\nInfosys Limited -  Hyderabad, Telangana -\n\nJuly 2016 to September 2016\n\nEnvironment SAP 5.0\n\nDescription:\nHarley Davidson is an American motorcycle manufacturer, founded in Milwaukee, Wisconsin in\n1903.\n\nProject Contribution:\n\n\n\n\u2022 Innovation track dashboard preparation.\n\u2022 Creation of POC application to filter the list based upon multiple filters with the help of Dialog\nProgramming.\n\u2022 The application also had functionalities like add new POCs, update and delete the existing\nones and modify others.\n\u2022 Data Upload to the SAP system from Excel sheet using BDC.\n\u2022 Additionally, the task required knowledge of Data Dictionary, Report Programming- Classical &\nALV, Module Pool Programming, Batch Data Communication- Call Transaction & Session\nMethod.\n\nEDUCATION\n\nBachelor of Engineering in Instrumentation Technology in\nInstrumentation Technology\n\nDayananda Sagar College of Engineering -  Sagar, Karnataka\n\nSKILLS\n\nSAPUI5 (2 years), CSS. (2 years), EMPLOYEE RESOURCE GROUP (2 years), ENTERPRISE\nRESOURCE PLANNING (2 years), SAP ABAP (2 years)\n\nADDITIONAL INFORMATION\n\nTechnical skills\nLanguages SAPUI5 (Primary Skill),\nABAP/4, C, C++, HTML, CSS, JavaScript, PHP,\njQuery, Ajax.\nERP SAP R/3 in 4.7, 5.0 (E)\nOperating Systems Windows\nDatabase MySQL, Oracle", "labels": [[7260, 7599, "Skills"], [3628, 3637, "Location"], [2329, 2345, "Companies worked at"], [2301, 2324, "Designation"], [1785, 1794, "Location"], [1741, 1764, "Designation"], [396, 405, "Location"], [352, 375, "Designation"], [105, 154, "Email Address"], [62, 71, "Location"], [44, 60, "Companies worked at"], [19, 42, "Designation"], [0, 18, "Name"]]}
{"id": 7, "text": "Madas Peddaiah\nAnantapur, Andhra Pradesh - Email me on Indeed: indeed.com/r/Madas-\nPeddaiah/557069069de72b14\n\n\u2022 Having 3 moths of experience in Manual Testing.\n\u2022 Previously worked with Infosys Limited, Mysore as a Software Test Engineer.\n\u2022 Having good experience in Executed Test Cases as per Client Requirements.\n\u2022 Having good experience in identifying the test scenarios and designing the test cases.\n\u2022 Worked on IE, Firefox and Chrome Driver using Selenium.\n\u2022 Good Knowledge in Core Java, SQL.\n\u2022 Experience in designing, preparing and executing test cases for client server and web based\napplications STLC Concepts.\n\u27a2 Web Based Application Testing\n\u2022 Experience in understanding business requirements, preparing and execution of test cases for\nSystem Customizations/Enhancements and Initiatives.\n\u2022 Quick learner with the ability to grasp new technologies.\n\u2022 Excellent team player having ability to finish the tight deadlines and work under pressure.\n\u2022 Good exposure on Manual Testing & Bug Life Cycle.\n\nWORK EXPERIENCE\n\nInfosys Limited -  Mysore, Karnataka -\n\nSeptember 2014 to December 2014\n\nEducational Technologies:\n\nSoftware Test Engineer\n\nInfosys Limited -\n\nSeptember 2014 to December 2014\n\n-September 2014 to December 2014.\nProject: 1\nClient: Loan Account\nRole: Software Test Engineer\nTeam Size: 4\nTechnologies: J2EE, Oracle 10g, Windows 7\nUsed Tools: Selenium, Core Java, HPQC and Eclipse.\nDuration: September 2014 to December 2014\n\nProject Description:\nIn this project we maintain all details about account transaction details, customer loan account\ndetails, calculate monthly EMI's and there activity like - Account login details, Account transaction,\nLoan account details etc.\nResponsibilities:\n\u2022 Participated in identifying the test scenarios and designing the test cases.\n\u2022 Prepared and Executed Test Cases as per Client Requirements.\n\nhttps://www.indeed.com/r/Madas-Peddaiah/557069069de72b14?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Madas-Peddaiah/557069069de72b14?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Performed Manual Testing on some modules.\n\u2022 Feasibility analysis of Manual Test Cases.\n\u2022 Performed Functional, Compatibility testing on different browsers like Firefox & Chrome.\n\u2022 Defect tracking & Bug Reporting.\nProject: 2\nClient: Hospital Management\nRole: Software Test Engineer\nTeam Size: 4\n\nEDUCATION\n\nB-Tech\n\nKuppam Engineering College -  Kuppam, Andhra Pradesh\n\n2014\n\nEducation, A.P\n\nVani Jr college\n\nOctober 1977 to 2010\n\nEducation, A.P\n\nPadmavani High School\n\n2008\n\nADDITIONAL INFORMATION\n\nTechnical Skills:\nTools: Manual Testing, Selenium (Selenium IDE, Selenium Web Driver), Eclipse IDE.\nLanguages: C, Core Java\nDatabase: SQL\nOperating Systems: Windows XP, 7, 8\nManagement Tool: HP Quality Center\nDefect Tracking Tool: JIRA\n\nProjects Summary:\n\nTechnologies: J2EE, Oracle 10g, Windows 7\nUsed Tools: Selenium, Core Java, HPQC and Eclipse.\nDuration: September 2014 to December 2014.\nProject Description:\nIn this project, we maintain all details about Hospital details like- Hospital address, Consultant\nDoctor, Doctor Details, Permanent Doctor, Medicine, Lab Test, In Patient, Out Patient etc.\nResponsibilities:\n\u2022 Prepared and Executed Test Cases as per Client Requirements.\n\u2022 Participated in identifying the test scenarios and designing the test cases.\n\u2022 Performed Manual Testing on some modules.\n\n\n\n\u2022 Feasibility analysis of Manual Test Cases.\n\u2022 Performed Functional, Compatibility testing on different browsers like Firefox & Chrome.\n\u2022 Defect tracking & Bug Reporting.", "labels": [[2934, 2938, "Graduation Year"], [2917, 2921, "Graduation Year"], [2566, 2783, "Skills"], [2495, 2517, "College Name"], [2473, 2477, "Graduation Year"], [2440, 2455, "College Name"], [2418, 2422, "Graduation Year"], [2364, 2390, "College Name"], [2356, 2362, "Degree"], [2308, 2331, "Designation"], [1436, 1440, "Graduation Year"], [1419, 1423, "Graduation Year"], [1270, 1293, "Designation"], [1226, 1230, "Graduation Year"], [1209, 1213, "Graduation Year"], [1192, 1196, "Graduation Year"], [1175, 1179, "Graduation Year"], [1146, 1161, "Companies worked at"], [1122, 1145, "Designation"], [1089, 1093, "Graduation Year"], [1072, 1076, "Graduation Year"], [1022, 1037, "Companies worked at"], [185, 200, "Companies worked at"], [63, 109, "Email Address"], [15, 24, "Location"], [0, 14, "Name"]]}
{"id": 8, "text": "Madhuri Sripathi\nBanglore, Karnataka, Karnataka - Email me on Indeed: indeed.com/r/Madhuri-\nSripathi/04a52a262175111c\n\nAround 4 years of IT experience in analysis, testing and scripting on L2/L3 layer protocols and\ndesiging testcases and automating the same in TCL/TK and Python.\n\n\u2022 Expertise in Networking Protocols L2, L3 protocols, Manual, Performance, Platform, Regression\nand Automation Testing.\n\u2022 Experience in python scripting and PYATS framework.\n\u2022 Coordinating with onsite/offsite teams in resolving the defects found in Testing and working on\nqueries raised by customers..\n\u2022 Reviewing the automated scripts.\n\u2022 Exposure to Networking Protocols such as DHCP, OSPF, RIP, VLAN, STP/RSTP, LACP, TCP/IP,\nIPv4, Ipv6, Ethernet.\n\u2022 Automation in Python.\n\u2022 Excellent ability to plan, organize and prioritize my work to meet on time the deadlines of my\nclients and keep customer's satisfaction at the highest level possible.\n\u2022 Proven ability in quick understanding and learning of new technologies and their application\nin business solutions\n\u2022 Good debugging and problem solving skills with excellent understanding of system\ndevelopment methodologies, techniques and tools.\n\u2022 Highly motivated team member with strong communication, analytical and organizational\nskills.\n\u2022 Strong communication, interpersonal and analytical skills with proficiency at grasping new\nconcepts quickly and utilizing the same in a productive manner.\n\u2022 Willingness and ability to quickly adapt to new environment.\n\u2022 Good positive attitude and ability to learn new things independently.\n\n\u2022 Worked as Senior project engineer in Wipro Technologies, from Jan2014 to till date.\n\nLanguages: C\nNetwork Analysis Tools: QDDTS, GNS3, IXIA, SPIRENT, PAGENT\nRouting protocols VLAN, ETHECHANNELSTP, RSTP, RIP, EIGRP, OSPF, BGP, MPLS, L2VPN, L3VPN,\nIPSEC and MULTICAST.\nScripting Language Perl, Tcl/TK, Python\nTraffic Generators IXIA, PAGENT, SPIRENT\nManagement Protocols Telnet, SNMP\n\nWilling to relocate to: UAE - Dubai - abu-dabhi\n\nWORK EXPERIENCE\n\nSenior project engineer\n\nCisco -  Bengaluru, Karnataka -\n\nMarch 2014 to Present\n\nhttps://www.indeed.com/r/Madhuri-Sripathi/04a52a262175111c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Madhuri-Sripathi/04a52a262175111c?isid=rex-download&ikw=download-top&co=IN\n\n\nCisco 7600 is a router which supports both layer2 and layer3 protocols. It mainly deploys protocols\nlike MPLS and having specific modules to support the protocols IPSEC. Worked as system testing,\nperformance testing, stress testing and regression testing for all the IOS release on all layer 2\nand layer 3 protocols.\n\nWipro Technologies Limited (Bangalore, Karnataka)\nSenior project engineer (March 2014 till date)\n\nSenior software engineer\n\nWipro -\n\nFebruary 2014 to Present\n\nResponsibilities:\n\u2022 Responsible for regression and Manual testing of CISCO IOS -7600\n\u2022 Test case execution, test case results tracking, debugging, logging defects in CDETS,\nreproductions and fix verification.\n\u2022 Configuration and Testing on Routing protocols OSPF, BGP, OSPF, MPLS, L3VPN, L2VPN, IPSEC,\nQOS, SNMP and MULTICAST features on Cisco 7600 Routers.\n\u2022 Filed critical bugs of high severity, through root cause analysis and effective testing methods\nBug verification, Bug tracking, and documentation and review bug fixes.\n\u2022 Engaged in regression testing, filing bugs against Cisco IOS images to improve the quality of\nthe images and send weekly test reports.\n\u2022 Mentoring of new joiners in the team and conducting technical training sessions.\n\u2022 Responsibility for the 7600 platform customer queries (AT&T, Bharati, Vodafone, German IT,\netc )\n\u2022 Involved in Sev1, Sev2 and sev3 cases and MW related to L2/L3 Features.\n\u2022 Create a Local Repro of the issue which was raised by the customer.\n\u2022 Analyzed the customer issues and will provide the solutions to the customers\n\u2022 Worked with Developer to verify the DDTs fix for the customer Found Defects\n\u2022 System Testing on every New IOS build for the L2/L3 protocols.\n\u2022 Configuration and Testing on routing protocols\n\u2022 Working on Functionality, Scalability and Performance testing\n\u2022 Preparing of Test beds and topologies using Line cards - SIP200, SIP400, SIP600, ES+, ES20, GIG\nand TenGig Lancards, pagent, IXIA Traffic generators etc. to create customer setup in local Labs\n\u2022 Knowledge on TCL scripting and automated customer found issues into regression testing and\nalso able to troubleshoot the script issues.\n\nEDUCATION\n\nMaster degree in computer science in computer science\n\nPES college\n\nS.S.C in computer science\n\nRajah college\n\n\n\nSKILLS\n\nLINUX (4 years), UNIX (4 years), ospf (4 years), bgp (4 years), mpls (4 years), ipsec (4 years),\nmulticast (4 years), l2vpn (4 years), l3vpn (4 years), tcl (4 years), python (2 years)\n\nADDITIONAL INFORMATION\n\nOperating Systems: LINUX, UNIX\nOther protocols ARP, RARP, ICMP,ospf,bgp,mpls,l2vpn,l3vpn\nAutomaton tools: tcl,python", "labels": [[4563, 4747, "Skills"], [4538, 4552, "College Name"], [4511, 4536, "Degree"], [4498, 4509, "College Name"], [4443, 4496, "Degree"], [3353, 3358, "Companies worked at"], [3110, 3115, "Companies worked at"], [2737, 2742, "Companies worked at"], [2711, 2736, "Designation"], [2663, 2686, "Designation"], [2613, 2618, "Companies worked at"], [2295, 2300, "Companies worked at"], [2037, 2042, "Companies worked at"], [2012, 2035, "Designation"], [1600, 1605, "Companies worked at"], [1573, 1596, "Designation"], [126, 132, "Years of Experience"], [62, 118, "Email Address"], [17, 25, "Location"], [0, 16, "Name"]]}
{"id": 9, "text": "Mahesh Vijay\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Mahesh-Vijay/a2584aabc9572c30\n\nOver 6.5 years of functional enriched experience in ERP in the Procurement to Pay domain. Was\nassociated with Oracle India Pvt Ltd, Bangalore as Team lead - Supplier Data Management in\ntheir Global Financial Information Centre (Global Shared Service Center) for Oracle's Business\nfrom Sep 2007- Feb 2014.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nTeam lead - supplier data management\n\nOracle India -  Bangalore, Karnataka -\n\nMarch 2014 to December 2016\n\nManaging Partner of family business of Tours & Travels\n\nTeam Lead\n\nOracle India Pvt Ltd -\n\nOctober 2013 to February 2014\n\nSupplier Data Management\n\nLead Analyst -SME -Supplier Data Management\n\nOracle India Pvt Ltd -\n\nSeptember 2012 to October 2013\n\nSenior Analyst -Supplier Data Management\n\nOracle India Pvt Ltd -  Bengaluru, Karnataka -\n\nJanuary 2010 to September 2012\n\nAcademia\n\u2022 Bachelors in Commerce (B.Com) from Vivekananda Degree College, Bangalore\nUniversity(2007)\n\u2022 Pre University from Vivekananda PU College, Bangalore(2004)\n\u2022 Passed 10th STD from Angels High School, Bangalore(2002)\nFunctional & Technical Expertise\nFunctional\n- Ensuring data quality in Purchasing Supplier management (PSM) registry and Trading\nCommunity Architecture of the Oracle e-business suite bundle.\n\nhttps://www.indeed.com/r/Mahesh-Vijay/a2584aabc9572c30?isid=rex-download&ikw=download-top&co=IN\n\n\n- Managing all projects and ensuring the completion of the same within timeframe. Projects\nlike - Oracle Fusion Supplier Self Service, Supplier cleanup, migration of merger and acquisition\nsuppliers, UAT\n- Managing activities like synchronizing creation and updates of supplier records.\n- Oracle Fusion - Related to Procurement modules -Fusion Supplier Portal\n- Sound knowledge in the Oracle Financial applications domain that includes various cycles like\nthe Expense Reporting, Accounts Payables, Accounts Receivables, and Tactical Purchasing.\n- R12 User Acceptance Testing, writing test cases and author test reports which analyze the\nreported defects.\nTechnical\n- Oracle Applications Releases: 12, 11.5, Oracle Applications Modules Purchasing, iProcurement\n- Business Intelligence Reporting Tools: Oracle Discoverer & Accounting Software Tally 7.2\nProjects & Accomplishments\nOracle Master Data Management- Legacy Data Cleanup Project\nRole:\n\u2022 Global Country wise clean up initiative focusing on achieving a clean and accurate database\n\u2022 Supplier Information retrieval based on information in Purchase orders\nOracle Fusion UAT- Supplier Self Service\nRole:\n\u2022 Internal UAT-Part of upgrade team, testing all functionality and interfaces.\n\u2022 Monitoring the new add on features in Fusion and old features assigned\n\n11i to R12 Migration- Manual UAT\nRole:\n\u2022 Testing for Supplier creations, Merges, Tax set ups, Withholding and TDS code, Bank details etc\n\u2022 Monitoring the new add on features in R12 and old features assigned\n\nOracle Supplier Life Cycle Management (SLM) or Supplier Hub Project\nRole:\n\u2022 Internal UAT- testing all functionality and interfaces for creating a 360 degree view for each\nand every supplier.\n\u2022 Responsible for setting up suppliers' online, assisting requesters and suppliers to register a\nsupplier and iSupplier access. Testing fast and flexible supplier searches that can be made into\ntemplates resulting in quick report generation Create and test blended supplier records from\nmultiple sources\n\nSKILLS\n\nBCP (6 years), Data Governance (6 years), Data Management (6 years), Oracle (6 years),\nReporting Tools (6 years)\n\nADDITIONAL INFORMATION\n\nKey Skills\n\u2022 Process Management & Improvement\n\u2022 Operations & Team Management\n\u2022 Data Governance & Automation\n\u2022 Oracle E- Business Systems experience in Supplier Data/Vendor Data management\n\u2022 BCP Policies & Procedures\n\n\n\n\u2022 Desk Manuals/Business Process & Navigation Documentation\n\u2022 Business Ethics\n\u2022 Professional Communication\n\u2022 Reporting Tools & Microsoft Office Applications", "labels": [[3822, 3978, "Skills"], [3714, 3720, "Companies worked at"], [3535, 3541, "Companies worked at"], [3466, 3819, "Skills"], [2962, 2968, "Companies worked at"], [2554, 2560, "Companies worked at"], [2322, 2328, "Companies worked at"], [2245, 2251, "Companies worked at"], [2151, 2157, "Companies worked at"], [2111, 2117, "Companies worked at"], [1829, 1835, "Companies worked at"], [1733, 1739, "Companies worked at"], [1542, 1548, "Companies worked at"], [1358, 1400, "Email Address"], [1313, 1319, "Companies worked at"], [1089, 1093, "Graduation Year"], [1055, 1077, "College Name"], [1027, 1031, "Graduation Year"], [978, 1026, "College Name"], [943, 973, "Degree"], [876, 885, "Location"], [852, 858, "Companies worked at"], [810, 850, "Designation"], [754, 760, "Companies worked at"], [709, 753, "Designation"], [628, 634, "Companies worked at"], [492, 498, "Companies worked at"], [454, 464, "Designation"], [389, 393, "Graduation Year"], [362, 368, "Companies worked at"], [245, 255, "Designation"], [210, 216, "Companies worked at"], [105, 114, "Years of Experience"], [56, 98, "Email Address"], [13, 22, "Location"], [0, 12, "Name"]]}
{"id": 10, "text": "Manisha Bharti\nSoftware Automation Engineer\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Manisha-Bharti/3573e36088ddc073\n\n\u2022 3.5 years of professional IT experience in Banking and Finance domain and currently working\nas Software\nAutomation Engineer in Infosys Limited, Pune.\n\u2022 Have experience in accounts and customers domain in banking.\n\u2022 Woking on SOA technology.\n\u2022 Hands on experience of 2+ years in Oracle 11g\n\u2022 2.9 years of professional experience in Middleware Testing and\nFunctional Testing\n\u2022 4 months of experience with UiPath.\n\u2022 Experience on GUI and API testing on HP UFT\n\u2022 Working on agile methodology where involved as a senior tester.\n\u2022 Involved in various STLC stages including Test Planning, Test analysis, Test Execution, Defect\nmanagement and\nTest reporting.\n\u2022 Possess sound knowledge of SQL, STLC, Testing Procedures, HP ALM, HP UFT, HP SV, SOAP\nUI, JIRA, JENKINS, CICD, UiPath.\n\u2022 Involved in various client presentation.\n\nTraining & Achievement\n\nTitle: Infosys E&R Training\nDescription: Has undergone E&R training in Infosys Limited (Mysore) in Microsoft. Net Stream.\nThere I had been explored SQL,\nRDBMS, OOPS, Mainframes, Software Testing and Software Engineering. Has been trained in\nAutomation Testing\nTools used- Eclipse, UFT, RPT, SQL Server Studio\nReceived two times FS INSTA award from Infosys for excellence in work in automation and team\nsupport Got Appreciation from Project Manager for root cause analysis of defects\nGot Client Appreciations for successful execution in releases. ( Almost 240 service operations go\nlive in a year.)\n\nWilling to relocate to: Pune, Maharashtra\n\nWORK EXPERIENCE\n\nNOT WORKING\n\nSoftware Automation Engineer\n\nInfosys Limited -\n\nAugust 2014 to July 2017\n\n->Worked as an software automation tester more than 3 years.\n->Working experience in Agile methodology.\n\nhttps://www.indeed.com/r/Manisha-Bharti/3573e36088ddc073?isid=rex-download&ikw=download-top&co=IN\n\n\n->Robotic Process automation certified.(UiPath)\n->Involved in CICD implementation in projects.\n->Having strong knowledge about HP UFT/QTP,HP ALM/QC,JIRA.JENKINS,SQL.\n\nSystem Engineer Trainee\n\nInfosys Limited -\n\nFebruary 2014 to July 2014\n\nCORE COMPETENCIES\nTechnology:\nService Oriented Architecture\n(SOA) Languages: \u2022\n\u2022 SQL (Oracle DB) \u2022\n\u2022 VB Scripting \u2022\n.NET \u2022\nSTLC:\n\u2022 Test Planning \u2022\n\u2022 Requirement Analysis \u2022\n\u2022 Test Scenario \u2022\n\u2022 Test Case Preparation \u2022\n\u2022 Test Case Execution \u2022\n\u2022 Defect Logging \u2022\nTesting:\n\u2022 Functional Testing \u2022\n\u2022 Middleware Testing \u2022\nRegression Testing \u2022\nGUI testing & API testing\u2022\nLanguages VB Scripting, JAVA\n\nWeb Technologies ASP.Net, XML, HTML\nDatabases SQL Server 2008/2005, ORACLE 11g\n\nDatabase Connectivity ODBC\nDistributed Computing Web Services, API, Windows Services\nModelling Tools Microsoft Vision\n\nEDUCATION\n\nB.Tech in CSE\n\nMeghnad saha institute of technology\n\n2013\n\nSKILLS\n\nUft/qtp,alm/qc,jira,jenkins,automation testing,cicd,service vitualization,uipath\n\n\n\nADDITIONAL INFORMATION\n\nOperating Systems Windows 10 / 8 / 7 / Vista / XP\n\nDomains Banking and Finance\n\nFrameworks Data driven framework, Keyword driven framework\n\nTools HP-UFT, HP-SV, HP-ALM/QC, SOAP UI, JENKINS, UiPath\n\nMethodologies STLC, Agile and waterfall.\n\nProject Management Tools JIRA\n\nTECHNICAL SKILLS\n\n\u2022 2.5+years of professional experience in SQL.\n\u2022 Has hands on experience on Oracle DB (Oracle 11g)\n\u2022 Has extensive knowledge of Testing Procedures and various\nphases of Testing Has 2+ years of experience on QC/ALM\n\u2022 Has 2+ years working experience in API & GUI testing\nusing HP UFT. Has 4 months of experience with uiPath\n\u2022 Has 2+ years working experience in SOAP UI.\n\u2022 Has 1.5+ year working experience on service virtualization using HP SV tool.\n\u2022 Has 6 months working experience in JIRA during work under agile methodology.\n\u2022 Has undergone Infosys Training in .Net Testing.\n\u2022 Has knowledge about CICD (Continuous integration and continuous delivery)\n\nPROJECT UNDERTAKEN\n\nDI-Middleware testing (August 14 -\nJuly 17) Domain- Accounts and customer.\nClient- ABN AMRO Bank (Netherland's bank)\nProject Name- ESB (Enterprise service bus)\n\u2022 Tools- ALM, SQL Developer, HP UFT, HP SV, SOAP UI, JENKINS, JIRA.\nIn this project, we were validating end to end communication of consumer & provider via ESB.\nWhat consumer actually\nsent to the Provider and how provider responds to the consumer. Testing included System\nIntegration Testing,\nRegression Testing, GUI Testing and Reports.\n\nResponsibilities-\n\nAutomation work\n\u2022 Preparing automation scripts using HP UFT tool where focus on Middleware logging as per the\nESB behavior.\n\u2022 Integrating all automation scripts with the ALM so that on one click we are able to execute test\ncases and collecting all\ntest results and logged defects in ALM without any manual efforts.\nManual Work -\n\n\n\n\u2022 Requirement analysis and Test Planning.\n\u2022 Test Scenarios preparation for various functionalities as per the Requirement.\n\u2022 Test Cases Creation and their execution for various functionalities of ESB and different provider\nservices\n\u2022 Prioritization of test cases as per the business requirement\n\u2022 Test Data Preparation as per the requirement using HP ALM.\n\u2022 Defect logging in case of any unusual behavior of the solution.\n\u2022 Preparing Weekly Progress Reports.\n\u2022 Leading the defect call\n\u2022 Virtualizing services using HP SV tool and deploy on central server so that in downtime testing\nshould not be impacted.\nCICD-JENKINS:\n\u2022 Involve in Continuous integration and continuous deployment strategy, with the help of JENKINS\n& UFT (automation scri\npt integrated with HP ALM) successfully implemented for currently working project.\n\nTDM implantation in CICD pipeline.\n\nPresentation - Direct communication to clients:\n\u2022 Present my team and our work to the client directly. (including CICD and TDM job\nimplementation in the same)\n\nuiPath exposure within same project:\n\u2022 Convert existing/new projects which are using UFT for automation into uiPath based\nautomation.\n\u2022 Do the feasibility analysis for the conversion and come up with a plan to convert maximum\nartifacts with minimum\nefforts\n\u2022 Setup basic skeleton for the new project", "labels": [[2833, 4400, "Skills"], [2819, 2823, "Graduation Year"], [2781, 2818, "College Name"], [2766, 2779, "Degree"], [1645, 1674, "Designation"], [1596, 1601, "Location"], [133, 142, "Years of Experience"], [85, 130, "Email Address"], [45, 50, "Location"], [15, 44, "Designation"], [0, 14, "Name"]]}
{"id": 11, "text": "Manjari Singh\nSenior Software Analyst - Accenture\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Manjari-Singh/fd072d33991401f0\n\n\u2022 Test Lead with more than 6.5 years of professional experience in TELECOM domain specifically\nin OSS applications including Order\nmanagement, Inventory Management, Service provisioning, Service Activation and Service\nAssurance\n\u2022 An accomplished QA Lead with extensive experience in Functional Testing, Regression Testing,\nUnit Testing, Integration Testing, Test strategy\ndefinition, Test Plan, Test Estimation, Test Cases/Scenarios, Test Procedures, Results and\ndocumentation\n\u2022 Proficient in Project management, requirements definition, software testing, SDLC, STLC, Agile,\nV-Model and Waterfall test methodologies\n\u2022 Worked as primary liaison with Business, QA, Development teams and Vendors for major, minor\nand emergency releases\n\u2022 Proficient in testing Web Based applications, Web Services, SOAP UI, High Speed Broadband\nand IPTV Testing (Modem, STBs, DSLAMs)\n\u2022 Expertise in handling and coordinating defect triage meetings, project meeting with various\nstakeholders in onshore-offshore model\n\u2022 A certified business analyst from BCS, The Chartered Institute for IT with proficiency in\nrequirement management and project definition\n\nWORK EXPERIENCE\n\nSenior Software Analyst\n\nAccenture -  Bengaluru, Karnataka -\n\nDecember 2017 to Present\n\nClient Leading telecommunication and digital entertainment service provider in US operating in\nfour segments: Business solutions,\nEntertainment Group, Consumer Mobility and International.\n\u2022 As an Agile Test Lead responsible for analyzing, estimating and sizing the user stories which\nwill help product owners to prioritize the story cards\n\u2022 Participating in project planning and scrum meetings to understand business and technical\nrequirements\nRoles & \u2022 Responsible for testing and validation of user stories, developing testing plans, and\nestimating project resources\nResponsibilities\n\u2022 Facilitate the resolution of testing roadblocks, ensure execution of QA deliverables and guiding\nteam members on agile\nstandards and best practices\n\u2022 Responsible for conducting smoke, functional, regression testing as well as ad-hoc testing of\nclient's B2B web application\n\u2022 Creating project specific documents including system understanding documents and\nrequirement traceability metrics\n\nhttps://www.indeed.com/r/Manjari-Singh/fd072d33991401f0?isid=rex-download&ikw=download-top&co=IN\n\n\nProject 1: ASPEN - Unified Communication\n\u2022 Role: Senior QA Lead\n\u2022 Key Responsibilities:\nProject\no Extensively collaborated with external Vendor to understand the new B2B Portal and\ndocumented the same\no Mentored/Trained new team members with Unified Communication concepts and functionality\nof the portal\no Responsible for leading a team to deliver test results for unified communication platform\no Lead Iteration Retrospective meetings to define problems, prioritize actions items and decide\nnext steps\n\nSenior Software Analyst\n\nAccenture -  Bengaluru, Karnataka -\n\nJune 2011 to November 2017\n\nOne of the leading telecommunication services and products provider in Canada providing wide\nrange of products and services\nClient\nincluding mobile telephony, internet access, voice entertainment, video and satellite television\n\u2022 As a Test Lead responsible for testing the End-to-End flow of Business Requirement\n\u2022 Develop test suite, test approach, test plan and high level scenarios for the project\n\u2022 To provide Project estimates, resource commitment for functional testing\n\u2022 Working with the Project Leads to establish time tables and agree on a QA plan for the\nimplementation\nRoles & \u2022 Lead defect triage calls, business reviews and training session to help subordinates\nproduce quality deliverables\nResponsibilities \u2022 Mentoring a testing team of 14 personnel through all phases of application\ndevelopment, ensuring that systems, products, and services meet or exceed organization/\nindustry quality standards and end user requirements\n\u2022 Sending daily status reports to the stakeholders regarding test progress and defect updates\n\u2022 Participating in risk and mitigation planning discussing the project planning and related risks\n\u2022 Assisting/leading GO-LIVE activities for smooth code delivery in short duration\nProject 1: Service Assurance Enablement\n\u2022 Role: QA Lead\n\u2022 Key Responsibilities:\no Worked as a QA prime on multi-million CAD$ project for assuring service quality and enabling\nglitch free service delivery\no Lead the QA team of 6 members both at onshore and offshore at different time periods\no As a Subject Matter expert, managed and delivered complex end to end data setup for AT,\nPT, UAT testing\n\nProject 2: HDM Upgrade and firmware testing of Customer Premises Equipment (CPE)\n\u2022 Role: QA Lead\n\u2022 Key Responsibilities:\no Owned and managed the only live lab environment as Onshore lead\n\n\n\no Performed network testing in established lab on DSLAMs, modems, STBs with various firmware\nversions\no Extensively collaborated with the external vendors and provided support to offshore team in\ntesting phase\nProjects\nProject 3: TV3- THOR Program\n\u2022 Role: QA Lead\n\u2022 Key Responsibilities:\no Worked as an Onshore QA lead for multiple agile releases during the launch of new next gen\nIPTV app\no Collaborated closely with project team to resolve the outstanding issues within strict timelines\no Performed Web based, Android & IOS testing for the new TV content app while collaborating\nwith the vendor team\n\nProject 4: Multiple HSIA projects like HSIA 250/250, Optik 150, Optik 50\n\u2022 Role: QA Lead\n\u2022 Key Responsibilities:\no Worked on requirements analysis, estimation, test planning, test approach, and defect\nmanagement for the release\no Collaborated with developers and BSAs to daily triage the defects logged during the testing\no Performed database testing for one of the activation tool and automated the process using\nWorksoft Certify\n\nQA\n\nAccenture -\n\nAugust 2015 to October 2016\n\nLead, worked on quality assurance and service enablement for multiple newly launched digital\napplications in live environment.\n\nAccenture -  Vancouver, BC -\n\n2015 to 2016\n\nEDUCATION\n\nB.TECH. in Information Technology\n\nAmity University -  Lucknow, Uttar Pradesh\n\n2011\n\nClass XII\n\nCanossa Convent Girls Inter College\n\n2007\n\nClass X\n\n\n\nCanossa Convent School\n\n2005\n\nSKILLS\n\nQA (7 years), TESTING (6 years), ESTIMATION (6 years), AMDOCS (Less than 1 year), BILLING\n(Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS AND COMPETENCIES\nDomain and Functional Expertise Tools and Languages:\n\u2022 Telecommunications and IPTV Systems \u2022 NetCracker, NetProvision, Insight (IISY), HDM (Nokia),\nTrouble Ticketing\n\u2022 Test/QA Lead \u2022 Click Schedule, FieldLink, AMDOCS-Ordering, Billing - Product Catalog\n\u2022 Project Management \u2022 IBM - TOCP, Mediaroom, MediaFirst TV3 platform (Ericsson)\n\u2022 Software Testing and Defect Management \u2022 HP QC 10, JIRA, TDP, Accenture Test Estimation Tool\n\u2022 Lead to Order and Order to Cash Management \u2022 Caliber RM (Requirement Gathering),\nConfluence, Clear Quest\n\u2022 Service Provisioning and Activation \u2022 Worksoft Certify, QTP, Java, Selenium, SQL, HTML, XML\n\u2022 Business Analysis (Requirement Management) \u2022 Splunk, WinSCP, SOAP UI, Kibana, Wireshark\n\u2022 Automation \u2022 Proficiency in MS Office (Word, Excel, PowerPoint and Visio)", "labels": [[6861, 6870, "Companies worked at"], [6305, 7258, "Skills"], [6267, 6290, "College Name"], [6250, 6254, "Graduation Year"], [6213, 6249, "College Name"], [6196, 6200, "Graduation Year"], [6152, 6169, "College Name"], [6117, 6151, "Degree"], [6062, 6071, "Companies worked at"], [5892, 5901, "Companies worked at"], [3029, 3033, "Graduation Year"], [3000, 3009, "Location"], [2987, 2996, "Companies worked at"], [2962, 2985, "Designation"], [1330, 1339, "Location"], [1317, 1326, "Companies worked at"], [1292, 1315, "Designation"], [94, 138, "Email Address"], [51, 60, "Location"], [40, 49, "Companies worked at"], [14, 37, "Designation"], [0, 13, "Name"]]}
{"id": 12, "text": "Mohamed Ameen\nSystem engineer\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Mohamed-Ameen/\nba052bfa70e4c0b7\n\nI am looking for a job opportunity as a System Engineer that gives me professional growth and\nexcellence and enables me to contribute my efforts in the success of the organization.\n\nWORK EXPERIENCE\n\nIT Operations Analyst\n\nAccenture\n\nI am looking for a job as system engineer that gives me professional growth and excellence and\nenables me to contribute my efforts in the success of the organization.\n\ntechnical support engineer\n\nConvergys for Microsoft -\n\nNovember 2014 to November 2015\n\nCurrently working with Accenture as a Subject Matter Expert for the Remote Technology Support\nteam in IT Operations.\n\nEDUCATION\n\nB.E in Electronics & Communication\n\nVisveswaraiah Technological University -  Bengaluru, Karnataka\n\n2013\n\nElectronics Project\n\nAl-Ameen PU College\n\nRajiv Gandhi Institute of Technology\n\nSKILLS\n\nActive Directory (2 years), Microsoft office, Windows,End user computing (3 years)\n\nCERTIFICATIONS/LICENSES\n\nCCNA\n\nCCNA certified\n\nhttps://www.indeed.com/r/Mohamed-Ameen/ba052bfa70e4c0b7?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Mohamed-Ameen/ba052bfa70e4c0b7?isid=rex-download&ikw=download-top&co=IN\n\n\nADDITIONAL INFORMATION\n\nSkill set:\n\nNetworking:\n\n\u27a2 Knowledge of OSI and TCP/IP architecture.\n\u27a2 Knowledge of Routing Protocols.\n\n\u27a2 Knowledge of Virtual Private Network and IPv6.\n\n\u27a2 Wireless LAN\n\n\u27a2 Installing, configuration & Troubleshooting of Operating System such as Windows client (XP,\nVista, 7, 8x and 10)\n\n\u27a2 Installing, configuration & Troubleshooting Microsoft Office {2013, 2016, 365 pro plus (Outlook\nand Other Office Tools Such as Excel, word, Power Point) }\n\n\u27a2 Configuring and Troubleshooting Microsoft outlook in Blackberry, iPhone & iPad.\n\n\u27a2 Configuring Group Policies in Domain & Work Group Environment.\n\n\u27a2 Create, modify and Manage User Account in AD Server.\n\n\u27a2 Creating Folder groups in File Server and providing Network folder access.\n\n\u27a2 Configuring & Troubleshooting Wireless Network & LAN, WAN Network,\n\n\u27a2 Installing and Configuration of VPN clients.\n\n\u27a2 Workgroup and Domain level security policies.\n\n\u27a2 Migration and Up gradation of Server/Desktops.\n\n\u27a2 Installation and Troubleshooting of Symantec Endpoint Antivirus.\n\n\u27a2 Installation and Troubleshooting of Avecto Defend point client.\n\n\u27a2 Managing & Troubleshooting Printer, Scanner, Fax.\n\n\u27a2 Configuring Managing and Troubleshooting SCCM on End user machines.\n\n\u27a2 Perform 1st Level troubleshooting and/or escalate as appropriate issue to warranty Vendors.\n\n\u27a2 Maintain Inventory of all warranty / AMC assets for the current year.\n\n\u27a2 Maintain an Inventory of parts for emergency repairs.\n\n\u27a2 Coordinate with vendors and with company personnel to facilitate purchases.\n\n\n\n\u27a2 Working on Web Tickets Tools.\n\n\u27a2 Handling Escalation and Severity for Incidents.\n\nOperating System:\n\n\u27a2 Windows 7, Windows 8, Windows 8.1 and Windows 10.\n\nApplication:\n\n\u27a2 MS Office, Service Now, ITSM, LogMeIn Rescue", "labels": [[1632, 1636, "Graduation Year"], [931, 1013, "Skills"], [885, 921, "College Name"], [864, 883, "College Name"], [837, 841, "Graduation Year"], [815, 824, "Location"], [773, 811, "College Name"], [737, 772, "Degree"], [631, 640, "Companies worked at"], [342, 351, "Companies worked at"], [319, 340, "Designation"], [74, 119, "Location"], [31, 40, "Location"], [14, 29, "Designation"], [0, 13, "Name"]]}
{"id": 13, "text": "Mohini Gupta\nServer Support Engineer\n\nGurgaon, Haryana - Email me on Indeed: indeed.com/r/Mohini-Gupta/08b5b8e1acd8cf07\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nServer Support Engineer\n\nMicrosoft -\n\nJuly 2015 to November 2017\n\nKey Responsibilities:\n\u25cf Worked as Technical Support Engineer for Microsoft Enterprise Platforms Support.\n\n\u25cf Worked in U.S., U.K., India & APAC time zones.\n\n\u25cf Always available (24X7) for any explanation, support or information required by team, client\nand managers.\n\n\u25cf Configuring, deploying and troubleshooting SCCM with remote/local SQL.\n\u25cf Perform Software distribution and ensure successful deployment on end assets.\n\u25cf Patch management of servers and end user estate along with troubleshooting.\n\u25cf Performed on checks through server, to perform server operations, check services, analyzed\nthe logs to check the communication from the new server to the primary server and vice versa,\nalso checked server's communication with its client.\n\u25cf Setting up new packages along with new collection.\n\u25cf Collection of inventory i.e. hardware inventory and software inventory.\n\u25cf Troubleshooting client connectivity and package installation issues by analysis of logs.\n\u25cf Working on incidents logged by end users as a daily activity.\n\u25cf Fixing operational issues and performing installation or un-installation of applications.\n\u25cf Create new groups, add users and grant permissions.\n\u25cf Good understanding of SCCM architecture, operations and management.\n\u25cf Knowledge of Active Directory and networking required in SCCM environment.\n\u25cf Deploying Operating System with SCCM.\n\nServer Support Engg.\n\nConvergys -\n\nJuly 2015 to November 2017\n\nServer Support Engg.\n\nEDUCATION\n\nB.tech\n\nhttps://www.indeed.com/r/Mohini-Gupta/08b5b8e1acd8cf07?isid=rex-download&ikw=download-top&co=IN\n\n\nKIIT college of Engg.\n\nSKILLS\n\nactive directory, iis, sccm, dhcp, sql, wsus, dns\n\nADDITIONAL INFORMATION\n\nComputer Skills\n\u25cf MS Office Tools: MS Excel, MS Word, MS Power Point.\n\u25cf Hands on experience on all versions of Windows.\n\u25cf Sound knowledge of internet and networking.\n\u25cf Coding Languages: C, C++, Java.\n\nOther Information\n\u25cf Regular Swimmer.\n\u25cf Interested in playing Table Tennis, Lawn Tennis.\n\u25cf Professional Proficiency: English and Hindi\n\nI hereby declare that all the above particulars are true to the best of my knowledge.\n\nPLACE: Gurgaon (MOHINI GUPTA)", "labels": [[2326, 2333, "Location"], [1821, 2095, "Skills"], [1790, 1811, "College Name"], [1704, 1746, "Email Address"], [1684, 1690, "Degree"], [300, 309, "Companies worked at"], [194, 203, "Companies worked at"], [169, 192, "Designation"], [77, 119, "Email Address"], [38, 45, "Location"], [13, 36, "Designation"], [0, 12, "Name"]]}
{"id": 14, "text": "Navas Koya\nTest Engineer\n\nMangalore, Karnataka - Email me on Indeed: indeed.com/r/Navas-Koya/23c1e4e94779b465\n\nWilling to relocate to: Mangalore, Karnataka - Bangalore, Karnataka - Chennai, Tamil Nadu\n\nWORK EXPERIENCE\n\nSystem Engineer\n\nInfosys -\n\nAugust 2014 to Present\n\n.NET application Maintenance and do the code changes if required\n\nTest Engineer\n\nInfosys -\n\nJune 2015 to February 2016\n\nPrProject 2:\n\nTitle: RBS W&G Proving testing.\nTechnology: Manual testing\nRole: Software Test Engineer\n\nDomain: Banking\nDescription:\n\nWrite test cases & descriptions. Review the entries. Upload and map the documents into\nHP QC. Execute the testing operations in TPROD mainframe. Upload the result in QC along with\nthe proof.\nRoles and Responsibilities:\n\u2022Prepared the Test Scenarios\n\n\u2022Prepared and Executed Test Cases\n\u2022Performed functional, Regression testing, Sanity testing.\n\n\u2022Reviewed the Test Reports and Preparing Test Summary Report.\n\u2022Upload Test cases to the QC.\n\u2022Execute in TPROD Mainframe.\n\u2022Defect Track and Report.\n\nTest Executive\n\nInfosys Limited -\n\nAugust 2014 to May 2015\n\nhttps://www.indeed.com/r/Navas-Koya/23c1e4e94779b465?isid=rex-download&ikw=download-top&co=IN\n\n\nProject 1:\nTitle: CAWP (Compliance Automated Work Paper)\n\nTechnology: Manual testing\nRole: Software Test Executive\nDomain: Banking\nDescription:\nThe Admin can create and maintain annual test plan, and users can only view and add\ndetails. Testers will get Business Requirement which explains the flows and Functional\nrequirements which gives the full detail of the project.\nRoles and Responsibilities:\n\n\u2022Prepared the Test Scenarios\n\u2022Prepared and Executed Test Cases\n\u2022Performed functional, Regression testing, Sanity testing.\n\u2022Reviewed the Test Reports and Preparing Test Summary Report.\n\u2022Defect Track and Report.\n\nEDUCATION\n\nBachelor of Computer Applications\n\nMangalore University, Mangalore\n\nJune 2011 to April 2014\n\nSKILLS\n\nC# (Less than 1 year), .NET, SQL Server, Css, Html5\n\nADDITIONAL INFORMATION\n\nBachelor of computer application: with 74% from Milagres College, Kallianpur under\nMangalore University, Karnataka.\n\nNavas Najeer Koya 2\n\nSKILL SET \u2022 ASP.NET, C# \u2022 QA tools\n\n\u2022 Coding and modularization \u2022 Excellent communication skills\n\n\u2022 VB, VB.net, ASP \u2022 Technical specifications creation\n\n\u2022 HTML \u2022 System backups\n\n\u2022 Sql server 2005, Oracle \u2022 System upgrades\n\n\u2022 Java/C/C++ \u2022 Excellent problem-solving abilities\n\nNavas Najeer Koya 3", "labels": [[2110, 2404, "Skills"], [2055, 2064, "Location"], [1895, 1947, "Skills"], [1880, 1885, "Graduation Year"], [1851, 1860, "Location"], [1829, 1838, "Location"], [1794, 1826, "Degree"], [1056, 1061, "Graduation Year"], [1031, 1038, "Companies worked at"], [479, 493, "Designation"], [352, 359, "Companies worked at"], [337, 351, "Designation"], [253, 258, "Graduation Year"], [236, 243, "Companies worked at"], [219, 234, "Designation"], [135, 144, "Location"], [26, 35, "Location"], [11, 25, "Designation"], [0, 10, "Name"]]}
{"id": 15, "text": "Navjyot Singh Rathore\nUlhasnagar, Maharashtra - Email me on Indeed: indeed.com/r/Navjyot-Singh-Rathore/\nad92079f3f1a4cad\n\nWORK EXPERIENCE\n\nfresher job\n\nAccenture -  Ulhasnagar, Maharashtra\n\nFresher\n\nAny post\n\nEDUCATION\n\nTYBMS in Management Studies\n\nVedanta College of management and information technology -  Mumbai, Maharashtra\n\n2015 to 2018\n\nH.S.C\n\nGuru Nanak English High School and Jr cllg\n\n2013 to 2015\n\nS.S.C\n\nswami Vivekananda school\n\n2013\n\nSKILLS\n\nFresher\n\nADDITIONAL INFORMATION\n\n\u25cf Can switch to any environment within a short span\n\u25cf Dedication towards Hard work\n\u25cf Willingness to learn\n\nSKILLS\n\n\u25cf Basic Computers knowledge\n\u25cf Good Understanding of Business Ethics, Operational Research.\n\u25cf Completed Project Work on working capital with A+ Grade.\n\nhttps://www.indeed.com/r/Navjyot-Singh-Rathore/ad92079f3f1a4cad?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Navjyot-Singh-Rathore/ad92079f3f1a4cad?isid=rex-download&ikw=download-top&co=IN", "labels": [[605, 753, "Skills"], [403, 407, "Graduation Year"], [351, 393, "College Name"], [344, 349, "Degree"], [338, 342, "Graduation Year"], [330, 334, "Graduation Year"], [249, 305, "College Name"], [220, 247, "Degree"], [165, 175, "Location"], [152, 161, "Companies worked at"], [139, 150, "Designation"], [68, 120, "Email Address"], [22, 32, "Location"], [0, 21, "Name"]]}
{"id": 16, "text": "Nazish Alam\nConsultant - SAP ABAP\n\nGhaziabad, Uttar Pradesh - Email me on Indeed: indeed.com/r/Nazish-Alam/\nb06dbac9d6236221\n\nWilling to relocate to: Delhi, Delhi - Noida, Uttar Pradesh - Lucknow, Uttar Pradesh\n\nWORK EXPERIENCE\n\nConsultant\n\nSAP ABAP -  Noida, Uttar Pradesh -\n\nNovember 2016 to Present\n\nCredence Systems, Noida\n\nCredence Systems is IT Infrastructure Management Company, offers end-to-end solutions.\nCombining deep domain expertise with new technologies and a cost effective on-site/ offshore\nmodel. Helping companies integrate key business processes, improving their operational\nefficiencies and extracting, better business value from their investment.\n\nPROJECT UNDERTAKEN\nClient ECC Version Role and Responsibilities\nWelspun Group Plate & Coil Mills Division\nSAP ECC 6.0\n\nConsultant\n\nSAP ABAP -\n\nJanuary 2016 to Present\n\nReports:\n\u2022 Designed technical program specifications based on business requirements.\n\u2022 Generated basic lists and Interactive Reports for information in the MM/SD including Sales,\nBilling, Purchasing, Goods Received, Inspection Plan, and Batch Determination using ABAP\nprograms, Screen, Report Painter and Menu Painters. Used Parameters, Select-options and Match\nCodes to make the reports more friendly and intuitive to the user.\n\u2022 Generated different kind of reports like for PR (Purchase Requisition) analysis using ALV, PO\n(Purchase Order) Pricing details, Pending Export Sales order etc.\n\u2022 Developed report for the daily production done.\nSAP Scripts:\n\u2022 Generated various client specific Layout sets and form letters using SAP Script.\n\u2022 Involved in modification of SAP scripts for Purchase orders (MEDRUCK) and indents, Delivery\nnotes (RVDELNOTE), and Invoices (RVINVOICE) according to customer needs.\n\u2022 Modified existing layout sets for Purchase Order and GR using SAP Script.\nData Migration:\n\nhttps://www.indeed.com/r/Nazish-Alam/b06dbac9d6236221?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Nazish-Alam/b06dbac9d6236221?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Implemented both Call Transaction and Session Method of BDC accordingly, depending upon\nthe size, type, state and created routines for data upload using data extracts for sequential files\non the application server and UPLOAD/WS_UPLOAD for local files on the presentation server.\n\u2022 Wrote ABAP programs for extracting data from SAP tables (Vendor master, Purchase Orders,\nInvoices and remittance) to be transferred to vendors using non-SAP systems for reconciliation\nand their local use.\nObject Oriented:\n\u2022 Created local and global classes with SE24 and within programs.\n\u2022 Used the Standard ALV classes in OOPs ALV reports.\n\u2022 Used ABSTRACT classes and Interfaces.\n\u2022 Having knowledge and used the different object oriented concepts technically.\n\nSKILLS\n\nSAP (2 years), ABAP (2 years), ADBC (Less than 1 year), C++ (Less than 1 year), DATA\nMODELING (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nOTHER TECHNICAL SKILLS\n\u2022 Trained on SAP S4 HANA.\n\u2022 Having knowledge of Code Push down, CDS view and it's consumption in ABAP.\n\u2022 Data Modeling, creation of different type of views.\n\u2022 AMDP.\n\u2022 ADBC connectivity.\n\u2022 Familiar with SQL, DDL, DML syntaxes.\n\u2022 Work on Windows 7, Windows XP, Windows 8, Windows 10 OS, can work on C, C++\nACADEMEIC CREDENTIALS\n2015 Master of Computer Application\nUPTU. India", "labels": [[3303, 3314, "College Name"], [3272, 3302, "Degree"], [3267, 3271, "Graduation Year"], [2941, 3244, "Skills"], [2780, 2892, "Skills"], [801, 809, "Companies worked at"], [789, 799, "Designation"], [241, 249, "Companies worked at"], [229, 239, "Designation"], [82, 124, "Email Address"], [35, 44, "Location"], [25, 33, "Companies worked at"], [12, 22, "Designation"], [0, 11, "Name"]]}
{"id": 17, "text": "Nidhi Pandit\nTest Engineer - Infosys Limited\n\n- Email me on Indeed: indeed.com/r/Nidhi-Pandit/b4b383dbe14789c5\n\nOverall around 4 years of work experience - Currently working with Infosys Limited designated\nas Test\nEngineer. Experience in Automation and Manual Testing in telecom and banking domain.\n\nWORK EXPERIENCE\n\nTest Engineer\n\nInfosys Limited -\n\nJune 2016 to Present\n\nProject Description:\nCBIL (Core Banking Integration Layer) is a crucial integration layer that is specifically addressing\nthe architectural complexity due to multiple core banking systems and variants at HSBC. It is a\nstandard service\ninterface across different core banking systems to facilitate easy integration with other global\nsystems.\nCBIL is a strategic initiative to standardize all interfaces with core banking without impacting the\nunderlying\ncore banking system.\nRoles & Responsibilities:\n\n\u2022 Understanding the functional requirements of the API.\n\u2022 Involvement in Test Planning.\n\u2022 Verifying the documents provided by the development team.\n\u2022 Creating test data request document to test the service on the certified environment.\n\u2022 Create and automate Test Cases.\n\u2022 Preparing Stub to virtualize the API.\n\u2022 Executing test cases in local and certified environments and validating the responses.\n\u2022 Participation in Stand up Calls, Scrum Calls, Sprint Planning, Retrospective Meetings.\n\u2022 Defect Management in JIRA.\n\u2022 Involvement in Automation Scripting.\n\u2022 Presenting completed APIs to the client.\n\u2022 Experience in working under client environment, multi-vendor environment.\n\nTest Engineer\n\nInfosys Limited -\n\nFebruary 2014 to Present\n\nTest Engineer\n\nInfosys Limited -\n\nhttps://www.indeed.com/r/Nidhi-Pandit/b4b383dbe14789c5?isid=rex-download&ikw=download-top&co=IN\n\n\nJuly 2014 to January 2016\n\nProject Description:\nOrder Management Fulfillment (OMFUL), which belongs to Telecommunication Domain, is a\nunified\nbusiness process management framework that orchestrates, automates and manages the\nservice\nfulfillment process, aligning people, processes and technology. This product caters the end to end\nfunctionality for telecom services in OSS space. Our role as a team was to ensure any Initiate/\nChange\nRequest towards the product is delivered successfully in time with no compromise in quality.\nRoles & Responsibilities:\n\n\u2022 Understanding the client requirement\n\u2022 Creating SQL scripts and deploying on the local environment (UNIX)\n\u2022 Sanity testing on different environments\n\u2022 Performing Manual Testing on OMFUL Application\n\u2022 Creating and maintaining test cases as per the requirement\n\u2022 Run and validate the test cases in the system which is integrated in a real production like\nenvironment\n\u2022 Creating manual stubs to complete the process fulfillment flow\n\u2022 Defect Management\n\u2022 Creating Show And Tell Related documents\n\u2022 Participation in support team at the time of Production Deployment\n\nEDUCATION\n\nState Board\n\n2008\n\nEducation\n\nPassing\n\nBachelor in Electronics\n\nCentral India Institute\n\nEngineering\n\nTechnical University\n\nSKILLS\n\nAPI. (1 year), Scripting. (1 year), SOAP (1 year), UI (1 year), XML (3 years)\n\nADDITIONAL INFORMATION\n\nKey Technical Skills\n\n\n\nTechnical Experience: - Automation Testing (REST API, Service Virtualization), Functional Testing,\nRegression Testing\nManual Testing, Scripting (SQL)\nDomain Experience: - Telecom, Banking\nProgramming & Scripting Languages: - HTML, CSS, XML, SQL, JAVA (Basic), JSON\nSDLC Model: -Waterfall, Agile\nTesting Tools: - CA LISA, APM (Amdocs Process Manager), SOAP UI, TOSCA, HP-ALM (QC)\nTest Management Tools: -JIRA, Quality Center\nOther Tools: -SQL Developer, TOAD.\nDatabase: - DB2, SQL", "labels": [[3132, 3611, "Skills"], [3005, 3083, "Skills"], [2937, 2961, "College Name"], [2912, 2935, "Degree"], [2885, 2892, "Graduation Year"], [2873, 2884, "Degree"], [1656, 1698, "Email Address"], [1610, 1623, "Designation"], [1550, 1563, "Designation"], [317, 330, "Designation"], [68, 110, "Email Address"], [29, 45, "Companies worked at"], [13, 26, "Designation"], [0, 12, "Name"]]}
{"id": 18, "text": "Nikhileshkumar Ikhar\nProduct development engineer with 7+ years of experience with\nM.Tech. in IT. Successfully developed & deployed, platform & behaviour\ndesign strategies in well-established corporations as well as emerging\nstartups.\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Nikhileshkumar-Ikhar/\ncb907948c3299ef4\n\nWilling to relocate to: Hyderabad, Telangana - Mumbai, Maharashtra\n\nWORK EXPERIENCE\n\nProduct Development\n\nAggrigator -  Bengaluru, Karnataka -\n\n2015 to Present\n\nAggrigator is Stanford incubated startup. It is an agriculture-based online marketplace in US B2B\nmarket.\nFirst non-co-founding engineer to come on board reported directly to CTO.\nDeveloped Reverse Auction Engine. A farmer would bid for their SKU and engine would pick\nfarmer based on bidding and capacity.\nDeveloped Palletization engine. A pallet can have multiple SKU boxes from multiple buyers. The\nengine generates a packing sequence of boxes in pallets in a truck container according to the\ndelivery route.\nDeveloped prototype to categorize SKU with Deep learning.\nOwned delivery of functionalities development, behavioural nudges, shaping of platform business\nmodel, aligning of product development to business.\nArchitected, designed & deployed websites, database, UI/UX to facilitate farm fresh produce\nprocurement & delivery to end consumers. Owned and developed various features like, \nInventory management plays a big role in optimizing cold storage, warehouse and trucking\nrequirement. It helped in reducing crop wastage.\nInvoicing of sold crops and tracking payments.\nOrder tracking for buyer and seller.\nFetching USDA price list daily via a web crawler.\nGenerating various reports in online, CSV and PDF format.\nWorked with technologies like Python, Django, Celery, MySQL, MongoDB, Ubuntu, Neural\nNetwork.\nFirst, six months employer was Above Solutions.\n\nSoftware Engineer\n\nCisco -  Bengaluru, Karnataka -\n\n2012 to 2015\n\nOwned and developed several products to help network migration, upgradation, tracking bugs,\ntracking\n\nhttps://www.indeed.com/r/Nikhileshkumar-Ikhar/cb907948c3299ef4?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Nikhileshkumar-Ikhar/cb907948c3299ef4?isid=rex-download&ikw=download-top&co=IN\n\n\nvulnerabilities.\n\n\u25cf Presented several proofs of concept for new business requirement.\n\n\u25cf Worked on various technologies such as Python, Java, Django, Celery, Cisco routers, SDN,\nOnePk & Hadoop.\n\nWorked on network migration during intern\n\nCisco -\n\nJanuary 2012 to June 2012\n\nSystem Engineer\n\nInfosys -  Pune, Maharashtra -\n\n2008 to 2010\n\nWorked as an SAP Basis consultant. Organised various in house events.\n\nEDUCATION\n\nM.Tech. in IT in VIT, Pune\n\nIIIT -  Bengaluru, Karnataka\n\n2010 to 2012\n\nSKILLS\n\nDjango (6 years), Java (3 years), MongoDB (3 years), MySQL (3 years), Python (6 years)\n\nLINKS\n\nhttp://github.com/nik-hil\n\nhttp://linkedin.com/in/nikhar\n\nADDITIONAL INFORMATION\n\nSkills\nPython, Django, Celery, Java, AngularJS, HTML, Bootstrap 3, Shell Script, MySQL, MongoDB,\nUbuntu\n\nBusiness Skills\nPlatform business model, Behaviour design\n\nhttp://github.com/nik-hil\nhttp://linkedin.com/in/nikhar", "labels": [[3036, 3078, "Skills"], [2922, 3018, "Skills"], [2738, 2824, "Skills"], [2724, 2728, "Graduation Year"], [2694, 2703, "Location"], [2686, 2690, "College Name"], [2675, 2678, "College Name"], [2658, 2671, "Degree"], [2530, 2537, "Companies worked at"], [2513, 2528, "Designation"], [2507, 2511, "Graduation Year"], [2494, 2498, "Graduation Year"], [2477, 2482, "Companies worked at"], [2397, 2402, "Companies worked at"], [1913, 1917, "Graduation Year"], [1889, 1898, "Location"], [1880, 1885, "Companies worked at"], [1861, 1878, "Designation"], [493, 503, "Companies worked at"], [452, 461, "Location"], [438, 448, "Companies worked at"], [417, 437, "Designation"], [279, 331, "Email Address"], [236, 245, "Location"], [83, 96, "Degree"], [54, 63, "Years of Experience"], [21, 49, "Designation"], [0, 20, "Name"]]}
{"id": 19, "text": "Nitin Tr\nPeopleSoft Consultant\n\nBangalore Urban, Karnataka - Email me on Indeed: indeed.com/r/Nitin-Tr/e7e3a2f5b4c1e24e\n\nAn e-commerce website I built as my college project. The website contains all the basic elements\nof an e-commerce website which are\n\nThe landing page, categorization of items based on filters, basic session level security, product\npage, Cart, share button, empty cart button, paginations etc.\n\nIt consists of a separate seller accounts where sellers can register and later upload their products\nto be sold, which can later be edited or deleted.\n\nIt consists of an admin panel where all the products listed can be viewed and edited by the\nmoderator.\n\n60 days auto delete feature, which deletes the product listing 60 days from the date of upload\nif seller has not modified the listing upon the next login.\n\nUsage of Modals for registration and Login which reduces the number of pages to navigate.\n\nLanguages used: PHP, MySQL, Html, CSS, Bootstrap, JavaScript and jQuery.\n\nWORK EXPERIENCE\n\nPeopleSoft consultant\n\nOracle -\n\nSeptember 2017 to Present\n\nPerforming customisations, enhancements and bug fixes for front end and backend inpeople code\nusing appdesigner.\n\nPeopleSoft Consultant\n\nOracle India Ltd -\n\nSeptember 2017 to April 2018\n\n\u2022 Develop customizations to meet business process requirements using application designer.\n\n\u2022 Involved in modification enhancement and bug-fixing of the PeopleSoft application both front-\nend and back-end to suit business needs.\n\n\u2022 Communicate with the business and get clear requirements if adequate information is not\navailable and also follow-up with them until final resolution is obtained.\n\n\u2022 Release Enhancements for UAT and communicate with business to migrate into production.\n\nhttps://www.indeed.com/r/Nitin-Tr/e7e3a2f5b4c1e24e?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Also have to work on service requests, Incident Creation, Incident Assignment additionally and\nconstantly involved in querying the database and running reports.\n\nFreelance Development\n\nI am a passionate Web Developer and love to build clean, smooth and responsive websites. I\nhave built many websites for individuals on freelance or per-project basis which are Responsive in\nnature. I am capable of building clean and responsive websites. They include personal portfolios\nand Small business websites.\n\u2022 http://www.pramodprakash.com/shop: Responsive and Dynamic e-commerce portal.\n\n\u2022 http://www.pramodprakash.com/tickItBusDemoUrl: A bus booking platform, source:\nKathmandu, Destination: Pokhra.\n\u2022 http://www.pramodprakash.com/geisle/index.php: A small business website focused on\nanimations (under construction) also check (geisle/index2.php)\n\u2022 http://pramodprakash.com/fulllogin: complete login module with email account activation and\npassword reset.\n\u2022 http://pramodprakash.com/sec: A small business website, built to showcase color combinations\nand layout.\n\u2022 http://pramodprakash.com/r&d: A small business website built in parallax format. Completely\nresponsive.\n\u2022 http://pramodprakash.com/web1: A template built according to given specifications and also\nsmall map feature included which also supports location search.\n\nEDUCATION\n\nBtech information science in BCET\n\nVtu -  Bengaluru, Karnataka\n\n2017\n\nVijaya composite p.u. college\n\ne-commerce\n\nVijaya High School\n\nSKILLS\n\nPhp, Html5, Javascript, Css, Bootstrap, Jquery, Sql\n\nLINKS\n\nhttps://www.linkedin.com/in/nitin-tr-*********\n\nADDITIONAL INFORMATION\n\nSoftware Skills\n\nhttps://www.linkedin.com/in/nitin-tr-*********\n\n\n\u2022 Programming Language: core java, peoplecode.\n\u2022 Scripting languages: PHP, JavaScript.\n\u2022 Web Languages: HTML, CSS.\n\u2022 Database Language: Sql.\n\u2022 Frameworks: Bootstrap, JQuery.\n\u2022 IDE's: NetBeans, Eclipse.\n\u2022 Tools: Application Designer, Toad.\n\nPersonal Skills\n\n\u2022 Excellent Communication Skills both Written and verbal.\n\u2022 Honest, trustworthy and highly motivated team player and Strong Negotiator.\n\u2022 Supportive and creative.\n\u2022 Quick Learner and Flexible.\n\u2022 Good Analytical Ability and Logical Reasoning.\n\u2022 Good listening skills.", "labels": [[3511, 3749, "Skills"], [3313, 3364, "Skills"], [3285, 3303, "College Name"], [3273, 3283, "Degree"], [3242, 3271, "College Name"], [3235, 3240, "Graduation Year"], [3207, 3211, "College Name"], [3172, 3206, "Degree"], [2399, 2409, "Degree"], [1754, 1792, "Email Address"], [1206, 1212, "Companies worked at"], [1032, 1038, "Companies worked at"], [1008, 1031, "Designation"], [224, 234, "Degree"], [124, 134, "Degree"], [81, 119, "Email Address"], [32, 47, "Location"], [0, 8, "Name"]]}
{"id": 20, "text": "Pradeeba V\nLEAD ENGINEER - CISCO\n\n- Email me on Indeed: indeed.com/r/Pradeeba-V/19ff20f4b8552375\n\nWORK EXPERIENCE\n\nLEAD ENGINEER\n\nCISCO -\n\nJune 2014 to Present\n\nPROJECT SPECIFIC SKILLS JAVASCRIPT, OOJS, HTML5, CSS3, REST, DOJO,\nAngular JS\nTOOLS USED SVN, Code Collaborator\nDescription:\n\nCisco Prime Infrastructure simplifies the management of wireless and wired networks. The Prime\nUI\noffers Prime Widget Toolkit (XWT) which provides dojo widgets. The UI supports HTML 5 features.\nThe\nPrime UI offers rich UI experience which includes consistency, better look and feel and scalable\ndesigns to handle large volume of data.\n\nResponsibilities:\n\u2022 Creating Widgets in dojo\n\u2022 Enhancement of existing widget\n\u2022 Handling REST calls\n\u2022 Writing Test cases\n\u2022 Unit Testing\n\n2. Project Title FINUX\n\nINDUSTRY FINACLE - BANKING\n\nSENIOR SYSTEMS ENGINEER\n\n-\n\nJune 2012 to June 2014\n\nPROJECT SPECIFIC SKILLS JAVASCRIPT, DOJO, CSS, HTML\n\nDescription:\nFinacle is a core banking software package. It is used by multiple banks across several countries,\nit can\nhandle multi-currency transactions.\n\nhttps://www.indeed.com/r/Pradeeba-V/19ff20f4b8552375?isid=rex-download&ikw=download-top&co=IN\n\n\nTo achieve the common look and feel for all the screens where the interaction done with the\nuser in the form of getting input values as well as retrieval of some kind of data and display of\nsuch data.\nI was totally involved in developing the Datagrid UI component in DOJO to display the search\nresults which are obtained as a result of inquiring the transactions.\n\nResponsibilities:\n\u2022 Front end enhancements for the Core product.\n\u2022 Discussing and finalizing the end UI screens with Functional and Design teams.\n\u2022 Writing front end and back end validation routines.\n\u2022 Regression testing for menus using Service Testing Framework.\n3. Project Title FINACLE\n\nINDUSTRY BANKING\nCLIENT Universal Banking Product from INFOSYS\n\nSYSTEMS ENGINEER\n\n-\n\nOctober 2011 to May 2012\n\nPROJECT SPECIFIC SKILLS JAVASCRIPT, DOJO, CSS, HTML\n\nDescription:\nFinacle is a core banking software package. It is used by multiple banks across several countries,\nit can\nhandle multi-currency transactions.\nBeing a part of Finacle team, I had involved in making the front end enhancement for Core\nproduct.\nResponsibilities:\n\n\u2022 Front end enhancements for the Core product.\n\u2022 Bug Fixing\n\nEDUCATION\n\nB.Tech\n\nInstitute of Road and Transport May\n\nOctober 1980\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\nProgramming Languages: Core Java\nScripting languages: JavaScript, OOJS\nDatabases: Oracle\nOperating systems: Windows (7, XP) and UNIX\nTools & Utilities: Eclipse, SSH, WinSCP, Code Collaborator, SVN\n\n\n\nWeb Designing Tools: HTML 4, HTML 5, CSS 3, DOJO, Angular JS\nWeb Service: REST Web services", "labels": [[2707, 2711, "Skills"], [2683, 2693, "Skills"], [2677, 2681, "Skills"], [2670, 2673, "Skills"], [2662, 2666, "Skills"], [2654, 2658, "Skills"], [2626, 2629, "Skills"], [2607, 2624, "Skills"], [2599, 2605, "Skills"], [2594, 2597, "Skills"], [2585, 2592, "Skills"], [2561, 2565, "Skills"], [2541, 2556, "Skills"], [2515, 2521, "Skills"], [2499, 2503, "Skills"], [2487, 2497, "Skills"], [2456, 2465, "Skills"], [2386, 2390, "Graduation Year"], [2341, 2376, "College Name"], [2333, 2340, "Degree"], [1982, 1986, "Skills"], [1977, 1980, "Skills"], [1971, 1975, "Skills"], [1959, 1969, "Skills"], [1888, 1904, "Designation"], [1824, 1840, "Designation"], [1436, 1440, "Skills"], [1085, 1125, "Email Address"], [911, 915, "Skills"], [906, 909, "Skills"], [900, 904, "Skills"], [888, 898, "Skills"], [819, 835, "Designation"], [812, 835, "Designation"], [784, 810, "Designation"], [712, 716, "Skills"], [464, 468, "Skills"], [255, 272, "Skills"], [250, 253, "Skills"], [228, 238, "Skills"], [222, 226, "Skills"], [216, 220, "Skills"], [210, 214, "Skills"], [210, 213, "Skills"], [203, 208, "Skills"], [203, 207, "Skills"], [197, 201, "Skills"], [185, 195, "Skills"], [130, 135, "Companies worked at"], [115, 128, "Designation"], [56, 96, "Email Address"], [27, 32, "Companies worked at"], [11, 24, "Designation"], [0, 10, "Name"]]}
{"id": 21, "text": "Prakriti Shaurya\nSenior System Engineer - Infosys Limited\n\nMangalore, Karnataka - Email me on Indeed: indeed.com/r/Prakriti-\nShaurya/5339383f9294887e\n\nDetail-oriented individual with three years of experience as an IT Consultant looking\nfor opportunity to develop my professional skills in a vibrant and stable environment,\nand to use those skills for the benefits of the organization in best possible way.\n\nWilling to relocate to: Bengaluru, Karnataka - Bangalore Urban, Karnataka\n\nWORK EXPERIENCE\n\nSenior System Engineer\n\nInfosys Limited -  Mangalore, Karnataka -\n\nJanuary 2017 to Present\n\nWorking as an IT Consultant under application maintenance and support for METLIFE Insurance\ncompany.\n\nSystem Engineer\n\nInfosys Limited -  Mangalore, Karnataka -\n\nDecember 2014 to December 2016\n\nWorked as an IT Consultant under application maintenance and support for METLIFE Insurance\ncompany.\n\nSOFTWARE\n\nEDUCATION\n\nBachelor of Technology in Information Technology\n\nVellore Institute of Technology -  Vellore, Tamil Nadu\n\n2010 to 2014\n\nC.B.S.E.\n\nNotre Dame Academy -  Patna, Bihar\n\n2007 to 2009\n\nSKILLS\n\nJava, Jsp, Html, Sql, C++, Javascript\n\nhttps://www.indeed.com/r/Prakriti-Shaurya/5339383f9294887e?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Prakriti-Shaurya/5339383f9294887e?isid=rex-download&ikw=download-top&co=IN\n\n\nADDITIONAL INFORMATION\n\nSKILLS\n\u2022 Good communication - written and oral skills\n\u2022 Excellent conceptual and analytical skills\n\u2022 Effective interpersonal skills\n\nPERSONALITY\n\u2022 Communicative \u2022 Punctuality\n\u2022 Creativity \u2022 Organized", "labels": [[1368, 1560, "Skills"], [1096, 1133, "Skills"], [1038, 1057, "College Name"], [1028, 1037, "Degree"], [1022, 1027, "Graduation Year"], [958, 990, "College Name"], [908, 956, "Degree"], [730, 739, "Location"], [711, 726, "Companies worked at"], [543, 552, "Location"], [524, 539, "Companies worked at"], [500, 522, "Designation"], [102, 149, "Email Address"], [59, 68, "Location"], [42, 57, "Companies worked at"], [17, 39, "Designation"], [0, 16, "Name"]]}
{"id": 22, "text": "PRASHANTH BADALA\nDevops Engineer ,Cloud Engineer -Oracle\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/PRASHANTH-BADALA/\nbf4c4b7253a8ece7\n\n\u2022 Hands on experience in end-end process of Build Management, Release Management and\nConfiguration Management.\n\u2022 Hands on experience on supporting configuration management tools on both physical and cloud\nenvironment\n\u2022 Involved in setting up Jenkins in Distributed Environments with Master and Slave\n\u2022 Working experience on Subversion (SVN) administration and basic usage\n\u2022 Creating Branches, tags and providing SVN user access to all developers in the organization\n\u2022 Managing application server instances running on AWS\n\u2022 Involved in configuring EC2 instances along with Auto scale up options.\n\u2022 Involved in configuration Management using CHEF and automated the complete platform\nsetup.\n\u2022 Good knowledge on Cookbooks, Chef Kitchen, Burks file and Metadata.\n\u2022 Involved in automating the release using UDeploy and deploying the application to different\nenvironments.\n\u2022 Involved in container setup with Docker. Having good understanding of creating and running\nDocker images.\n\u2022 Involved in writing Applications, Componenets, Resources and component process flow.\n\u2022 Configured Jenkins as contionus integration tool for regular source code builds.\n\u2022 Experience in setting up branching strategies, merging and taking regular backups of the source\ncode on SVN server\n\u2022 Helping the Developers in SVN related issues\n\u2022 Written the integrated build automation scripts using Ant\n\u2022 Perform the QA & Stress and UAT builds and deployed the binaries on respective environment\nservers\n\u2022 Monitoring the deployment in all the servers\n\u2022 Supported setting up of various environments in multi-tier architecture involving load balancers,\nApache webservers, Oracle database,\n\u2022 Developed custom scripts to automate the build and release process.\n\u2022 Implemented a custom authentication with WebLogic for user authentication, authorization and\npassword policy\n\u2022 Monitoring environment using monitoring tools like Wily monitoring tool and custom shell script.\n\u2022 Involved in integrating WebLogic with Wily introscope.\n\u2022 Involved in resolving tickets with different SLAs like SEV1, SEV2, SEV3 and SEV4.\n\u2022 Involved in change management process like opening change records using change\nmanagement tools like INFRA, Managenow.\n\u2022 Providing on call, weekend and deployment support.\n\u2022 Involved in applying security patches using WebLogic Utility.\n\u2022 Deploying applications using different deployment strategies like Stage, No Stage and External\nstage.\nWork Experience:\n\n\u2022 Working as Configuration Engineer in Oracle, Hyderabad from July 2015 to till date.\n\nhttps://www.indeed.com/r/PRASHANTH-BADALA/bf4c4b7253a8ece7?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/PRASHANTH-BADALA/bf4c4b7253a8ece7?isid=rex-download&ikw=download-top&co=IN\n\n\nTechnical Skills:\n\nVersion Control Tools \n\nSubversion(SVN),GIT\n\nWeb/Appservers\nContinuous Integration Tools\nWebLogic 11g, Apache HTTP Server 2.4\nJenkins 1.6, Hudson\n\nBuild Tools \n\nMaven 3\n\nScripting Languages \n\nShell Scripting and Python\n\nOperating Systems \n\nWindows servers [\u2026] Windows XP/7, Red Hat LINUX 5\n\nDatabase\n\nOracle 11g\n\nRelease Tools\n\nUDeploy,Jenkins\n\nCloud\n\nAWS\n\nConfiguration Tools\n\nCHEF\n\nEducation Qualification:\nB.Tech From Annamacharya Institute of Technology, JNTU Hyderabad - 2015 \n\nProject Details\n\nTitle: E-Banking solutions \n\nClient : Union Bank of Canada, Canada \nRole : Configuration Engineer \nEnvironment : Maven, Jenkins, CHEF, UDeploy, SVN, Linux, Weblogic,Aws \n\n\n\nDuration : Nov 2016 to till date\n\nProject Description:\n\nThis is a banking project and basic objective of this project is to deal with the loans. This\napplication is based on Java technology. For this we have to schedule the tasks and have to collect\ncode from development team and have to build and deploy the code later have to support the\nrelease management team of which executing Java applications build and deployments in Dev,\nQA, performance and production environments.\n. .\nResponsibilities:\n\u2022 Involved in automation of Configuration Management using CHEF and automated multiple\nenvironments like Prod and Non Prod.\n\u2022 Involved in configuring AWS Environment to deploy applications.\n\u2022 Involved in Release Management and automated the overall release process using Urban Code\nDeployments (UDeploy)\n\u2022 For on boarding existing application, performing knowledge transition from development team\nto SCM team on build and deployment process.\n\u2022 For new applications, work with development to get the requirements of application build and\ndeployment process.\n\u2022 Creating War/Ear files using Ant Script\n\u2022 Creating Jenkins/Hudson jobs.\n\u2022 Monitor and track requests in Subversion.\n\u2022 Monitor and fix the continuous integration builds running in Jenkins.\n\u2022 Troubleshooting the compilation errors and provide the technical support to the developers\nteam on that issue.\n\u2022 Installation, configuring, administration of Web Logic 9.x, 10.x on Linux environment.\n\u2022 Supporting Builds for Multiple environments like Dev, QA, Staging, Perf and Production.\n\u2022 Performing Manual and Automation Builds using Maven and Jenkins.\n\u2022 Troubleshooting the middle ware issues and resolving the P2,P3 tickets with in SLA\n\u2022 Provided on call support on 24/7 basis\n\u2022 Involved in creation and configuration of Domains, JVM instances in production, QA and UAT\nenvironments.\n\u2022 Configured clusters to provide fail over, load balancing and deployed applications on clusters.\n\u2022 Installed, configured and administration to Web logic 10.x, JDBC Data source and Connection\npool configuration with Oracle. Web Logic Administration, Monitoring and Troubleshooting using\nAdmin Console.\n\u2022 Have excellent experience in Client Interaction\n\u2022 Provided on call support for production tickets on 24/7 basis.\n\u2022 Deployment of web and enterprise applications and their updates in dev, production, pre-\nproduction using Admin console.\n\n\u2022 Creating branches & merging using Subversion.\n\u2022 Performing deployments to multiple environments like Dev, QA, Field, Perf, UAT & Production\nenvs\n\u2022 Troubleshooting application related issues by log verification.\n\u2022 Writing a UNIX Shell Script and schedule in the respective run levels for automate day-to-day\nactivities such as auto start application server.\n\u2022 Automate code deployments by using ANT and Jenkins.\n\n\n\nSprint Value Added Services\n\nClient : Sprint, U.S \nRole : Build and Release Engineer\nEnvironment: Maven, Jenkins, SVN, Linux, Weblogic, Apache,AWS,Docker\nDuration : Dec 2015 to Oct 2016\n\nProject Description:\n\nThis is a Value Added Services provided by Sprint. This application is based on Java technology.\nFor this we have to schedule the tasks and have to collect code from development team and\nhave to build and deploy the code later have to support the release management team of which\nexecuting Java applications build and deployments in Dev, QA, performance and production\nenvironments.\n\nResponsibilities:\n\n\u2022 For on boarding existing application, performing knowledge transition from development team\nto SCM team on build and deployment process.\n\u2022 For new applications, work with development to get the requirements of application build and\ndeployment process.\n\u2022 Installing and configuring Subversion (SVN) and Jenkins.\n\u2022 Providing support to Subversion (SVN) related issues.\n\u2022 Developing and maintaining build files by using Ant script.\n\u2022 Integrate Unit Testing in Ant builds\n\u2022 Installation, configuring, administration of Web Logic 9.x, 10.x on Linux environment.\n\u2022 Supporting Builds for Multiple environments like Dev, QA, Staging, Perf and Production.\n\u2022 Performing Manual and Automation Builds using Maven and Jenkins.\n\u2022 Troubleshooting the middle ware issues and resolving the P2,P3 tickets with in SLA\n\u2022 Provided on call support on 24/7 basis\n\u2022 Involved in creation and configuration of Domains, JVM instances in production, QA and UAT\nenvironments.\n\u2022 Configured clusters to provide fail over, load balancing and deployed applications on clusters.\n\u2022 Installed, configured and administration to Web logic 10.x, JDBC Data source and Connection\npool configuration with Oracle. Web Logic Administration, Monitoring and Troubleshooting using\nAdmin Console.\n\u2022 Have excellent experience in Client Interaction\n\u2022 Provided on call support for production tickets on 24/7 basis.\n\u2022 Deployment of web and enterprise applications and their updates in dev, production, pre-\nproduction using Admin console.\n\u2022 Building the source code using Jenkins.\n\u2022 Helped developers in resolving SVN issues and concerns.\n\u2022 Responsible for weekly and daily work checks and backups.\n\u2022 Environment: WebLogic Application Server 10.3, JDK1.6, Oracle, Apache Webserver, Linux,\nJIRA,Infra,SSH,TOAD\n\nKOhls Retail Services\n\n\n\nClient : Kohls, US\nRole : Build and Release Engineer. \nEnvironment: Maven, Jenkins, SVN, Linux, WebLogic, Apache\nDuration : July 2015 to Nov 2015\n\nProject Description:\n\nThis application is based on Java technology. For this we have to schedule the tasks and have to\ncollect code from development team and have to build and deploy the code later have to support\nthe release management team of which executing Java applications build and deployments in\nDev, QA, performance and production environments.\n\nResponsibilities: \n\n\u2022 Developing and maintaining build files by using Ant script.\n\u2022 Integrate Unit Testing in Ant builds\n\u2022 Installation, configuring, administration of Web Logic 9.x, 10.x on Linux environment.\n\u2022 Supporting Builds for Multiple environments like Dev, QA, Staging, Perf and Production.\n\n\u2022 Supporting Builds for Multiple environments like Dev, QA, Staging, Perf and Production.\n\u2022 Performing Manual and Automation Builds using Maven and Jenkins.\n\u2022 Deploying WAR, EAR applications on various targeted servers in the clustered environments.\n\u2022 Web Logic Administration, Monitoring and Troubleshooting using Admin Console.\n\u2022 Analyzing log files and periodic removal of them\n\u2022 Creating branches & merging using Subversion.\n\u2022 Performing deployments to multiple environments like Dev, QA, Field, Perf, UAT & Production\nenvs\n\u2022 Troubleshooting application related issues by log verification.\n\u2022 Writing a UNIX Shell Script and schedule in the respective run levels for automate day-to-day\nactivities such as auto start application server.\n\u2022 Automate code deployments by using ANT and Jenkins.\n\n\u2022 Involved in changing heap parameters like \u2013Xms, -Xmx, -XnoOpt,-XnoHup etc.\n\u2022 Perform daily environment health-check\n\u2022 Good in taking the thread dumps and finding the root cause analysis.\n\u2022 Created and configured web logic server instances, clusters in domain environment.\n\u2022 Installed web logic on production boxes in console mode.\n\u2022 Strong experience in administrating by using Admin console.\n\nEnvironment: WebLogic Application Server 9.2, Java, Oracle, Apache Webserver, Linux, JIRA,\nManagenow, Putty, TOAD\n\nWilling to relocate to: Hyderabad, Telangana - Bangalore Urban, Karnataka - Chennai, Tamil Nadu\n\nWORK EXPERIENCE\n\nConfiguration Engineer\n\nOracle -  Hyderabad, Telangana -\n\n\n\nJuly 2015 to Present\n\n\u2022 Hands on experience in end-end process of Build Management, Release Management and\nConfiguration Management.\n\u2022 Hands on experience on supporting configuration management tools on both physical and cloud\nenvironment\n\u2022 Involved in setting up Jenkins in Distributed Environments with Master and Slave\n\u2022 Working experience on Subversion(SVN) administration and basic usage\n\u2022 Creating Branches, tags and providing SVN user access to all developers in the organization\n\u2022 Managing application server instances running on AWS\n\u2022 Involved in configuring EC2 instances along with Auto scale up options.\n\u2022 Involved in configuration Management using CHEF and automated the complete platform\nsetup.\n\u2022 Good knowledge on Cookbooks, Chef Kitchen, Burks file and Metadata.\n\u2022 Involved in automating the release using UDeploy and deploying the application to different\nenvironments.\n\u2022 Involved in container setup with Docker. Having good understanding of creating and running\nDocker images.\n\u2022 Involved in writing Applications, Componenets, Resources and component process flow.\n\u2022 Configured Jenkins as contionus integration tool for regular source code builds.\n\u2022 Experience in setting up branching strategies, merging and taking regular backups of the source\ncode on SVN server\n\u2022 Helping the Developers in SVN related issues\n\u2022 Written the integrated build automation scripts using Ant\n\u2022 Perform the QA & Stress and UAT builds and deployed the binaries on respective environment\nservers\n\u2022 Monitoring the deployment in all the servers\n\u2022 Supported setting up of various environments in multi-tier architecture involving load balancers,\nApache webservers, Oracle database, \n\u2022 Developed custom scripts to automate the build and release process.\n\u2022 Implemented a custom authentication with WebLogic for user authentication, authorization and\npassword policy\n\u2022 Monitoring environment using monitoring tools like Wily monitoring tool and custom shell script.\n\u2022 Involved in integrating WebLogic with Wily introscope.\n\u2022 Involved in resolving tickets with different SLAs like SEV1, SEV2, SEV3 and SEV4.\n\u2022 Involved in change management process like opening change records using change\nmanagement tools like INFRA, Managenow.\n\u2022 Providing on call, weekend and deployment support.\n\u2022 Involved in applying security patches using WebLogic Utility.\n\u2022 Deploying applications using different deployment strategies like Stage, No Stage and External\nstage.\n\nConfiguration Engineer\n\nOracle\n\nDevops\n\n\n\nEDUCATION\n\nB.TECH/B.E\n\nAnnamacharya Institute of Technology, JNTU, Hyderabad -  Hyderabad, Telangana\n\n2015\n\nSKILLS\n\nAWS (1 year), CHEF (1 year), Linux (2 years), git, svn, maven, devops, jenkins, Docker,\nweblogic\n\nADDITIONAL INFORMATION\n\nOperating Systems Windows servers [\u2026] Windows XP/7, Red Hat LINUX 5\nDatabase Oracle 11g\nRelease Tools UDeploy, Jenkins\nCloud AWS\nConfiguration Tools CHEF", "labels": [[13907, 13911, "Skills"], [13883, 13887, "Skills"], [13883, 13886, "Skills"], [13835, 13841, "Companies worked at"], [13724, 13732, "Skills"], [13716, 13722, "Skills"], [13707, 13714, "Skills"], [13699, 13705, "Skills"], [13691, 13697, "Skills"], [13687, 13690, "Skills"], [13682, 13685, "Skills"], [13665, 13670, "Skills"], [13650, 13654, "Skills"], [13636, 13639, "Skills"], [13600, 13609, "Location"], [13587, 13596, "Location"], [13543, 13579, "College Name"], [13502, 13508, "Companies worked at"], [13478, 13500, "Designation"], [12696, 12702, "Companies worked at"], [12020, 12026, "Skills"], [11962, 11968, "Skills"], [11701, 11705, "Skills"], [11578, 11582, "Skills"], [11578, 11581, "Skills"], [11014, 11023, "Location"], [11004, 11010, "Companies worked at"], [10980, 11002, "Designation"], [10890, 10899, "Location"], [10829, 10834, "Skills"], [10803, 10809, "Companies worked at"], [9450, 9455, "Skills"], [8846, 8851, "Skills"], [8704, 8709, "Skills"], [8678, 8684, "Companies worked at"], [8137, 8143, "Companies worked at"], [7512, 7517, "Skills"], [6507, 6513, "Skills"], [6503, 6506, "Skills"], [6479, 6484, "Skills"], [5619, 5625, "Companies worked at"], [4994, 4999, "Skills"], [4213, 4216, "Skills"], [4122, 4126, "Skills"], [3540, 3545, "Skills"], [3520, 3524, "Skills"], [3466, 3488, "Designation"], [3367, 3371, "Graduation Year"], [3355, 3364, "Location"], [3312, 3348, "College Name"], [3300, 3306, "Degree"], [3269, 3273, "Skills"], [3248, 3268, "Skills"], [3243, 3247, "Skills"], [3243, 3246, "Skills"], [3236, 3242, "Skills"], [3219, 3234, "Skills"], [3192, 3198, "Companies worked at"], [3083, 3110, "Skills"], [3052, 3059, "Skills"], [3017, 3036, "Skills"], [2980, 3016, "Skills"], [2951, 2979, "Skills"], [2936, 2950, "Skills"], [2915, 2935, "Skills"], [2891, 2914, "Skills"], [2650, 2654, "Graduation Year"], [2630, 2639, "Location"], [2622, 2628, "Companies worked at"], [2596, 2618, "Designation"], [1785, 1791, "Companies worked at"], [1109, 1115, "Skills"], [1051, 1057, "Skills"], [790, 794, "Skills"], [667, 671, "Skills"], [667, 670, "Skills"], [101, 148, "Email Address"], [58, 67, "Location"], [50, 56, "Companies worked at"], [34, 48, "Designation"], [17, 32, "Designation"], [0, 16, "Name"]]}
{"id": 23, "text": "Pratibha P\nPrincipal Consultant at Oracle\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Pratibha-P/b4c1202741d63c6c\n\nOver 14 years of experience in estimation, design, development, implementation, testing and\nenhancement of various Oracle applications. Currently working as Principal Consultant (Oracle\nApplications) with the consulting group Global Service Delivery (GSD) of Oracle India Pvt Ltd,\nBangalore. Possesses excellent communication, extensive functional and technical experience in\nimplementing Oracle E-business Suite applications.\nWorked on various modules like AR, AP, GL, FA, OLFM, HRMS and PO. Has gained proficiency in\nOracle 9i/10g, SQL, PL/SQL, Discoverer 10g, Oracle reports 6i/10g, BI Publisher reports, OEM (For\ndata masking), Methodologies (Oracle AIM, OUM) & Migration Tool (Kintana)\n\nWilling to relocate to: Bangalore City, Karnataka\n\nWORK EXPERIENCE\n\nPrincipal Consultant\n\nOracle -\n\nJuly 2012 to Present\n\nOver 14 years of experience in estimation, design, development, implementation, testing and\nenhancement of various Oracle applications. Currently working as Principal Consultant (Oracle\nApplications) with the consulting group Global Service Delivery (GSD) of Oracle India Pvt Ltd,\nBangalore. Possesses excellent communication, extensive functional and technical experience in\nimplementing Oracle E-business Suite applications.\nWorked on various modules like AR, AP, GL, FA, OLFM, HRMS and PO. Has gained proficiency in\nOracle 9i/10g, SQL, PL/SQL, Discoverer 10g, Oracle reports 6i/10g, BI Publisher reports, OEM(For\ndata masking),Methodologies(Oracle AIM,OUM)& Migration Tool(Kintana)\n\nSenor Consultant\n\nOracle -\n\n2011 to July 2011\n\nStaff Consultant\n\nCaterpillar -\n\nDecember 2005 to December 2007\n\nDuration/Size: 4 months/10\n\nAssociate Consultant\n\nCaterpillar -\n\nOctober 2004 to December 2005\n\nhttps://www.indeed.com/r/Pratibha-P/b4c1202741d63c6c?isid=rex-download&ikw=download-top&co=IN\n\n\nDuration/Size: 4 months/10\n\nAssociate Consultant\n\nVault Consulting -\n\nOctober 2003 to October 2004\n\nProject Name: AMAT\nClient: Applied Materials\nDuration/Size: 1 months/3\nOrganization: Oracle\nEnvironment: Oracle Applications (11i)\nResponsibilities: AMAT was an implementation project. I was involved in developing complex\nDiscoverer reports which fully meets the objectives for forecasting the history and exception\ndetails of AMAT's buyers/Suppliers with capability to review FGI (Finished Goods Inventory), LLI\n(Linked Level Inventory) details\n\nProject Name: Dollar General\nClient: Dollar General\nDuration/Size: 2 months/3\nOrganization: Oracle\nEnvironment: Data Warehousing\nResponsibilities: Dollar General was an implementation project. I was involved in developing\nDiscoverer reports.\n\nProject Name: Emerson\nClient: Emerson Process Management\nDuration/Size: 2 months/3\nOrganization: Oracle\nEnvironment: Data Warehousing\nResponsibilities: Dollar General was an implementation project. I was involved in developing\nDiscoverer report for each division-country, division-world area and total division (single reports) .\nThe divisions were also compared against each other (comparison reports)\n\nEDUCATION\n\nB.E\n\nVisvesvaraya Technological University\n\n2017\n\nBachelor of Engineering in Engineering\n\nDayanand Sagar College of Engineering Bangalore (Visvesvaraya Technological University) -\nSagar, Karnataka\n\n\n\nSKILLS\n\nExtension, PLSQL, BI Publisher Reports, Oracle Reports, AP, HRMS, GL, Conversion, Oracle Apps\ntechnical, Interface, AR, TOAD, OLFM, SQL, FA\n\nADDITIONAL INFORMATION\n\nSoftware Skills:\n\nERP-Oracle Applications 11i, R12\nAccounts Receivable, Accounts Payable, Fixed Assets, General Ledger, Oracle Lease and Finance\nManagement, Purchasing and HRMS (Employee, Assignment, Contacts, Pay methods, Job and\nLocation)\n\nRDBMS Oracle [\u2026]\nLANGUAGES C++\nTOOLS Toad, SQL Developer, Kintana, Report Builder 6i/10g, Discoverer Admin/Desktop/Plus/\nViewer and BI Publisher", "labels": [[3758, 3764, "Companies worked at"], [3630, 3636, "Companies worked at"], [3532, 3538, "Companies worked at"], [3427, 3433, "Companies worked at"], [3385, 3391, "Companies worked at"], [3345, 3896, "Skills"], [3276, 3313, "College Name"], [3227, 3264, "College Name"], [3187, 3225, "Degree"], [3142, 3179, "College Name"], [3137, 3141, "Degree"], [2819, 2825, "Companies worked at"], [2571, 2577, "Companies worked at"], [2137, 2143, "Companies worked at"], [2117, 2123, "Companies worked at"], [1646, 1652, "Companies worked at"], [1586, 1592, "Companies worked at"], [1505, 1511, "Companies worked at"], [1461, 1467, "Companies worked at"], [1331, 1337, "Companies worked at"], [1201, 1207, "Companies worked at"], [1121, 1127, "Companies worked at"], [1099, 1119, "Designation"], [1057, 1063, "Companies worked at"], [910, 916, "Companies worked at"], [888, 908, "Designation"], [775, 781, "Companies worked at"], [691, 697, "Companies worked at"], [647, 653, "Companies worked at"], [517, 523, "Companies worked at"], [387, 393, "Companies worked at"], [307, 313, "Companies worked at"], [285, 305, "Designation"], [243, 249, "Companies worked at"], [43, 52, "Location"], [35, 41, "Companies worked at"], [11, 31, "Designation"], [0, 10, "Name"]]}
{"id": 24, "text": "Prem Koshti\nOfficer-HR & Administration in H.& R. Johnson (India) - SAP - R\n\nDewas, Madhya Pradesh - Email me on Indeed: indeed.com/r/Prem-Koshti/a1fec9e7289496f0\n\n\u2756 To acquire a key Position in Human Resource Management / SAP field by continuously improving\nknowledge and skills.\n\u2756 Very strong logical, analytical skills with vast experience in MS-EXCEL.\n\u2756 Very energetic, hardworking and highly self-motivated team player with strong problem solving\nskills and very good communication and leadership skills. Very flexible.\n\nProjects:-\nProject Name: SAP HR, Employee Administration\nClient: H.& R. Johnson (India) [A Division of Prism Cement Limited], DEWAS (M.P.)\n\nWORK EXPERIENCE\n\nOfficer-HR & Administration in H.& R. Johnson (India)\n\nSAP - R -  Dewas, Madhya Pradesh -\n\nJuly 2002 to Present\n\n- SAP - R/3, 06 years' experience in SAP HR-Functional Module\nCurrent Employer:\nPresently working as Officer-HR & Administration in H.& R. Johnson (India), [A Division of Prism\nCement Limited], DEWAS (M.P.) from 30.07.2002 to till date.\n\nEDUCATION\n\nB.Com. in Dr. Harisingh Gour V.V\n\nPolytechnic College Damoh -  Sagar, Madhya Pradesh\n\n1990\n\nSKILLS\n\nHR (10+ years), SAP (10+ years), APPRAISAL (Less than 1 year), BUYING/PROCUREMENT (Less\nthan 1 year), DATABASE (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTechnical skills: SAP HR Module.\nDescription:-\nMaintaining electronic punching system, Daily Production MIS Report, Monthly Manpower\nreport. I.F. Annual Returns, Form-5 Holiday wages. Employee's gratuity policy updation.\nMaintaining all record's & document related to personal / HR department, Employees data\nbank. Employees leave, attendance, c-off, absenteeism statement. Payroll Preparation, Bonus,\n\nhttps://www.indeed.com/r/Prem-Koshti/a1fec9e7289496f0?isid=rex-download&ikw=download-top&co=IN\n\n\nOvertime, Attendance Incentive, Production Incentive, Arrear Wages, Wage slip, Full & Final\nSettlement, No Dues Certificate, Issue of certificate if any, ID / Punching card, Birth day card etc.\n\nRoles and responsibilities: Personal & HR Functions:\n\n\u2713 Performance Appraisal / Increments preparation co-ordination.\n\n\u2713 Computerized Time Office Management, HR Policy.\n\n\u2713 Handle Payroll on SAP & responsible for smooth functioning of payroll cycle.\n\n\u2713 Maintain employee data on SAP and updates them as and when required\n\n\u2713 Reconciling statutory reports i.e. PF, ESIC, and coordinating with Finance Team for timely\npayment.\n\n\u2713 Production & manpower MIS data in MS-Excel daily, Monthly & Yearly basis.\n\n\u2713 Performing of Exit Formalities and processing Full and Final Settlement for exit cases.\n\n\u2713 Maintain Attendance record in Electronic Punching Machine.\n\n\u2713 Joining Formalities (Pre & Post recruitment activities)\n\nGeneral Administration\n\nOffice stationery, Telephones, Fax, Computers, Reception, Purchasing First Aid, mineral water,\nbiscuits etc\n\nTechnical Expertise\nFront End Tool\nLanguages\nSAP Technologies HR and Administration Functional Module\nDatabase FOXPRO 6.22\nConcepts OOP'S, Networking, DBMS, Operating System.\nOperating System MS-Dos [\u2026] & MS OFFICE 2007, 2008 & 2010\n\nTRAININGIG PROGRAMME & CONFERENCE ATTENDED:\n\n\u2756 First Aid Procedure conducted by St. John Ambulance Association.\n\n\u2756 Fire Fighting by Usha Fire Safety.\n\n\u2756 Interpersonal Skills, Communication, Motivational related various Training programmes\norganize.\n\n\u2756 SAP - HR Module conducted by Covansys, Mumbai.\n\n\n\n\u2756 Internal Auditors Training Program on Environmental & Occupational Health & Safety\nManagement System", "labels": [[3327, 3330, "Companies worked at"], [2886, 2889, "Companies worked at"], [2861, 3074, "Skills"], [2273, 2276, "Companies worked at"], [2185, 2188, "Companies worked at"], [1318, 1321, "Companies worked at"], [1161, 1164, "Companies worked at"], [1145, 1274, "Skills"], [897, 950, "Designation"], [833, 836, "Companies worked at"], [798, 801, "Companies worked at"], [749, 754, "Location"], [738, 741, "Companies worked at"], [683, 736, "Designation"], [551, 554, "Companies worked at"], [223, 226, "Companies worked at"], [120, 162, "Email Address"], [77, 82, "Location"], [68, 71, "Companies worked at"], [12, 65, "Designation"], [0, 11, "Name"]]}
{"id": 25, "text": "Pulkit Saxena\nNew Delhi, Delhi - Email me on Indeed: indeed.com/r/Pulkit-Saxena/ad3f35bfe88a0410\n\nI have a high degree of technical competence, a strong learning aptitude and an excellent\nwork ethic. I am a technical expert in a number of network areas, in particular connectivity,\nperformance, scalability and security. As an articulate communicator, I have ability to influence\npeople at every level by ensuring that issues are discussed, conflicts are resolved and the best\nsolutions are delivered. In my current role I work with the rest of the team to ensure the successful\ndelivery and operation of all supported services. Right now, I would like to join a growing company\nthat wants to recruit proven and experienced IT people.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nCisco -  Samba, Jammu and Kashmir -\n\n2000 to 2000\n\nAmple knowledge in Windows 98 \\ Me \\ Xp \\ 2000 \\ Server 2003 \\Server 2008 \\Server\n2008r2\\Server 2012\n\u2713 Active directory management, NTFS security, disk quota management\n\u2713 Good understanding of OSI Model, TCP/IP protocol suite (IP, ARP, ICMP, TCP, UDP, RARP, FTP,\nTFTP)\n\u2713 Ample understanding of Bridging and switching concepts and LAN technologies\n\u2713 IP addressing and subnetting, Routing concepts\n\u2713 Sound knowledge of routing protocols - RIP V1/V2, OSPF, IGRP & EIGRP\n\u2713 Switches: Basic Configuration & VLAN setup on Cisco 1900, 2950, 2960 Switches &\nTroubleshooting.\n\u2713 Router: Basic Configuration & monitoring of Cisco 2500, 2600, 1800\n\u2713 Vlan: configuration, switching isl, dotlq\n\u2713 Cisco Firewall 5500 Series: Configuration and policies Setup and Troubleshooting.\n\u2713 Back-up and restore of all critical resources including router & switches IOS, Outlook, DHCP,\nDNS.\n\u2713 Functioning knowledge of wan solution, protocol HDLC, PPP\n\u2713 Working knowledge of, DHCP Server, DNS Server, ADDS, Proxy Server on Linux and Complete\nKnowledge on Windows\n\u2713 Security administration port security on switch and IP security on Router via Access list\n\u2713 Familiar with web technology HTML CSS\n\u2713 Setting up Secure NFS Servers with multiple Clients for File and Disk sharing.\n\u2713 Setting up SAMBA servers, to enable Windows clients to communicate with Linux without the\nneed of additional software on the Windows side.\n\u2713 Configuring NIS Servers for Centralized and secure Password and Login Management\n\u2713 Linux user management creating, changing group, and assign permission on resources\n\u2713 Recover of root password securing terminals\nHardware\n\u2713 Computer assembling and maintenance.\n\u2713 Troubleshooting hardware and software problems.\n\u2713 Installing and configuring the peripherals, components and drivers.\n\nhttps://www.indeed.com/r/Pulkit-Saxena/ad3f35bfe88a0410?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2713 Installing software and application to user standards.\n\nEDUCATION\n\nMCL in Computer Application\n\nIGNOU\n\nBACHELOR'S IN SCIENCE in COMPUTER NETWORKING\n\nKarnataka State University\n\nComputer Networking\n\nAptech Institute of Technology\n\nComputing\n\nAptech Institute of Technology\n\nSKILLS\n\nFirewall (1 year), HTML (1 year), MICROSOFT WINDOWS (1 year), Router (1 year), security. (1\nyear)\n\nADDITIONAL INFORMATION\n\nSKILLS\n\u2713 A computer expert with hardware and software grab.\n\u2713 Administration and Back-end handling (Technical) on overall basis of the Branch.\n\u2713 Convincing people tactfully so as to make the company an option for the user.\n\nAREAS OF EXPERTISE\n\u2713 Server administration\n\u2713 Technical documentation\n\u2713 Network security\n\u2713 Network management\n\u2713 Data backups\n\u2713 Disaster recovery\n\u2713 Cisco Router\n\u2713 Cisco Switch\n\u2713 Network management\n\u2713 Switching\n\u2713 Routers\n\u2713 Firewalls\n\u2713 Firewall principles\n\u2713 Remote Access\n\nPROFESSIONAL\n\u2713 ACESE RIM\n\n\n\n\u2713 MICROSOFT Certified\n\nADDITIONAL SKILLS\n\u2713 ADVANCED MS OFFICE (WORD, POWERPOINT, EXCEL, ETC.)\n\u2713 ADOBE PHOTOSHOP, READER X, ACROBAT, COREL DRAW, MICROMEDIA FLASH ETC.\n\u2713 JAVA NETBEANS, SQL, HTML, CMD PROMPT, ETC.\n\u2713 FAMILIAR WITH ALL SORTS OF WINDOW SETS AND WEB BROWSERS.\n\u2713 ALL KINDS OF UTILITY SOFTWARES AND HARDWARE OPTIONS TO INCREASE EFFICIENCY AND\nEFFECTIVENESS IN WORKING.\n\u2713 COMPLETE KNOWLEDGE OF MICROSOFT EXCHANGE SERVER 2012.\n\u2713 CAN DEVELOP WEBSITES BASED ON HTML, PHP ETC.\n\u2713 BASIC KNOWLEDGE OF ANDROID AND MAC APPLICATIONS.\n\u2713 COMPLETE KNOWLEDGE OF SERVER AND CLIENT BASED ENVIRONMENT\n\u2713 BASIC KNOWLEDGE OF MICROSOFT WINDOWS FIREWALL", "labels": [[3673, 4270, "Skills"], [3497, 3502, "Companies worked at"], [3482, 3487, "Companies worked at"], [3121, 3334, "Skills"], [2989, 3086, "Skills"], [2949, 2979, "College Name"], [2906, 2936, "College Name"], [2885, 2904, "Degree"], [2857, 2884, "College Name"], [2811, 2855, "Degree"], [2804, 2810, "College Name"], [2774, 2803, "Degree"], [1516, 1521, "Companies worked at"], [1447, 1452, "Companies worked at"], [1350, 1355, "Companies worked at"], [784, 789, "Companies worked at"], [53, 97, "Email Address"], [14, 23, "Location"], [0, 13, "Name"]]}
