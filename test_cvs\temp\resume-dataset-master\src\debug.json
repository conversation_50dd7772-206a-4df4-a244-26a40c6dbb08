{"id": 98, "text": "<PERSON><PERSON><PERSON>\nSenior System Engineer - Infosys Limited\n\nSalem, Tamil Nadu - Email me on Indeed: indeed.com/r/Kavitha-K/8977ce8ce48bc800\n\nSeeking to work with a software firm, to constantly upgrade my knowledge and utilize my\nexisting skills to benefit the concerned organization\n\nWORK EXPERIENCE\n\nSenior System Engineer\n\nInfosys Limited -\n\nDecember 2014 to Present\n\nUnix, CA7 scheduler\n\nInfosys Limited -\n\nDecember 2015 to February 2018\n\nQlikview Level 1 • Basic knowledge of creating simple dashboards with different\nProduction support style using Qlikview components like List Box, Slider, Buttons,\ncharts and Bookmarks.\n• Created different types of sheet objects like List boxes, Buttons,\nMulti box.\n• Good knowledge of monitoring Qlikview Dashboards\n• Monitoring Critical dashboards and communicating delay to clients\n• Involved in Qlikview initial error analysis and the concerned\nteam to trouble shoot the issue\n• Monitoring Qlikview dashboards from end to end and manually\nrefreshing the tasks if needed\n• Handling service request for manual refresh of dashboards\n• Monitoring Qlikview dependent ETL jobs in CA7 job scheduler\nLevel 1 (BI process) • Involved in monitoring batch jobs in CA7 job scheduler\n• Managing the daily workload based on priorities and maintain\nSLA's to provide quality services to end users\n• Responsible for sending daily and weekly reports to the clients\n• Initial analysis of log files and fixing of environment related\nissue in ETL Process\n• Coordinating with concerned team in troubleshooting of major\nbusiness related issues and sending notification to the clients on timely manner\n• Responsible for all Process related activities like incident\nmanagement and change management\n• Involved in documenting the process, procedures and flow of ETL Process for critical\napplications\n• Respond to user service requests and resolving them with in stipulated time\n• Participated in Incident Management and Problem\nManagement processes for root cause analysis, resolution and reporting\n\nhttps://www.indeed.com/r/Kavitha-K/8977ce8ce48bc800?isid=rex-download&ikw=download-top&co=IN\n\n\nEDUCATION\n\nNetworking, Infosys Limited\n\nFoundation Training Program in Networking\n\n2016\n\nBachelor of Engineering in Information Technology\n\nREiume Institute of road -  Erode, Tamil Nadu\n\n2014", "meta": {}, "annotation_approver": "admin", "labels": [[2292, 2296, "Graduation Year"], [2245, 2269, "College Name"], [2194, 2243, "Degree"], [2145, 2187, "Degree"], [92, 131, "Email Address"], [35, 51, "Companies worked at"], [10, 33, "Designation"], [0, 9, "Name"], [52, 69, "Location"], [293, 315, "Designation"], [317, 332, "Companies worked at"], [336, 360, "Years of Experience"], [362, 381, "Designation"], [383, 398, "Companies worked at"], [402, 432, "Years of Experience"], [434, 442, "Skills"], [545, 553, "Skills"], [730, 749, "Skills"], [832, 840, "Skills"], [1099, 1107, "Skills"], [1111, 1128, "Skills"], [1189, 1205, "Skills"], [1458, 1469, "Skills"], [1772, 1783, "Skills"], [2116, 2143, "College Name"], [2188, 2192, "Graduation Year"], [2273, 2290, "Location"]]}
{"id": 99, "text": "Kavya U.\nNetwork Ops Associate - Accenture\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Kavya-U/049577580b3814e6\n\nSeeking for opportunities to learn and grow in electronics domain.\n\nWORK EXPERIENCE\n\nNetwork Ops Associate\n\nAccenture -  Bengaluru, Karnataka -\n\nDecember 2017 to Present\n\nExposure:\n• Provisioning of different types of network speeds for multiple clients.\n• Use of Mux designing for Logical cross connect and Physical patching. We co- ordinate with\nvarious ops and field engineers to do the connections at the physical\nlevel.\n\n2) Organisation: QuadGen Wireless Engineering Services Pvt. Ltd., Bangalore.\nPosition: Network Engineer\nExperience: 1 year\nExposure:\n• RAN (Radio Access Network) Engineer.\n• New Site Build (NSB)\n\n2) Organisation: Manipal Dot Net Pvt. Ltd., Manipal.\nPosition: Intern\nExperience: 1 year\nExposure:\n• Module coding and verification using Verilog HDL\n• Worked on Linux O.S.\n• Understanding of SPI, I2C protocols\n• Compilation using Altera Quartus\n• Simulation using ModelSim\n• Report preparation and documentation\n\nEDUCATION\n\nLittle Rock Indian School\n\n2007\n\nMaster of Science in Technology in Technology\n\nSchool Of Information Sciences\n\nVLSI Design\n\nhttps://www.indeed.com/r/Kavya-U/049577580b3814e6?isid=rex-download&ikw=download-top&co=IN\n\n\nManipal Academy of Higher Education\n\nBachelor of Engineering in Engineering\n\nSrinivas Institute of Technology -  Mangalore, Karnataka\n\nElectronics and Communication\n\nVisvesvaraya Technological University\n\nVidyodaya P.U. College -  Udipi, Karnataka\n\nSKILLS\n\ncoding (Less than 1 year), HDL (Less than 1 year), Microsoft office (Less than 1 year), MS\nOFFICE (Less than 1 year), UART (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTechnical Skills\n• Verilog HDL\n• Knowledge of RTL coding, FSM based designs.\n• Understanding of UART, AMBA protocol\n• Platforms: Microsoft office, Libreoffice", "meta": {}, "annotation_approver": "admin", "labels": [[1844, 1873, "Skills"], [1844, 1860, "Skills"], [1794, 1830, "Skills"], [1748, 1790, "Skills"], [1742, 1745, "Skills"], [1734, 1745, "Skills"], [1666, 1670, "Skills"], [1636, 1645, "Skills"], [1599, 1615, "Skills"], [1575, 1578, "Skills"], [1548, 1554, "Skills"], [1496, 1518, "College Name"], [1457, 1495, "College Name"], [1368, 1400, "College Name"], [1291, 1326, "College Name"], [234, 244, "Companies worked at"], [211, 233, "Designation"], [87, 124, "Email Address"], [0, 8, "Name"], [9, 30, "Designation"], [33, 42, "Companies worked at"], [44, 64, "Location"], [247, 267, "Location"], [271, 295, "Years of Experience"], [569, 616, "Companies worked at"], [618, 627, "Location"], [639, 666, "Designation"], [668, 674, "Years of Experience"], [687, 722, "Designation"], [765, 790, "Companies worked at"], [792, 799, "Location"], [811, 817, "Designation"], [830, 836, "Years of Experience"], [856, 862, "Skills"], [886, 897, "Skills"], [910, 920, "Skills"], [940, 943, "Skills"], [945, 948, "Skills"], [979, 993, "Skills"], [1013, 1021, "Skills"], [1073, 1098, "College Name"], [1100, 1104, "Graduation Year"], [1106, 1137, "Degree"], [1153, 1183, "College Name"], [1185, 1196, "Degree"], [1328, 1351, "Degree"], [1404, 1424, "Location"], [1426, 1455, "Degree"], [1522, 1538, "Location"]]}
{"id": 100, "text": "Khushboo Choudhary\nDeveloper\n\nNoida, Uttar Pradesh - Email me on Indeed: indeed.com/r/Khushboo-Choudhary/\nb10649068fcdfa42\n\nTo pursue a challenging career and be part of a progressive organization that gives scope to\nenhance my\nknowledge, skills and to reach the pinnacle in the computing and research field with sheer\ndetermination,\ndedication and hard work.\n\nWORK EXPERIENCE\n\nDeveloper\n\nSAP ABAP -  Noida, Uttar Pradesh -\n\nJanuary 2018 to May 2018\n\nSAP ABAB 7.4 Noida, Uttar\n(5 months) Pradesh\n\nTechnical Proficiency\n1. SAP ABAP 7.4 2. OOPS\n3. DBMS 4. Core Java\n5. C/C++ 6. Data Structures\n\nRoles and Responsibilities\n• Creating report generating modules.\n• Creating interactive modules for trainers to train.\n\nOfficial Projects\n1. Uploading file from non-sap system to sap system using BAPI.\n2. Uploading excel data using BDC.\n3. Generating Adobe forms.\n4. Creating smart forms for order purchasing.\n5. Automatic email sending module using workflow.\n6. Creating classical reports.\n7. Creating function module.\n\nEDUCATION\n\nB.Tech in CSE\n\nMM University\n\n2013 to 2017\n\nCBSE\n\nhttps://www.indeed.com/r/Khushboo-Choudhary/b10649068fcdfa42?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Khushboo-Choudhary/b10649068fcdfa42?isid=rex-download&ikw=download-top&co=IN\n\n\nParatap Public School -  Karnal, Haryana\n\nDecember 2011\n\nCBSE in Technology Used\n\nSilver Bells Public School -  Muzaffarnagar, Uttar Pradesh\n\nOctober 2009\n\nEngineering College\n\nSKILLS\n\nANDROID (Less than 1 year), CISCO (Less than 1 year), NETWORKING (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nDevelopment Skills.\n\nMay 2016 1.5 Learned about \"Basic Networking\" using Cisco MMEC, Mullana\nPacket Tracer.\n\nJune, 2015 2 Built an application which have functionality of Solitaire Infosys inc.\nconverting a text into speech using text to speech class Mohali, India\nnamed \"kid'z speak\" using Android.", "meta": {}, "annotation_approver": "admin", "labels": [[1466, 1549, "Skills"], [1040, 1053, "College Name"], [1025, 1038, "Degree"], [389, 398, "Companies worked at"], [378, 387, "Designation"], [73, 123, "Email Address"], [30, 35, "Location"], [19, 28, "Designation"], [0, 18, "Name"], [37, 50, "Location"], [401, 421, "Location"], [425, 449, "Years of Experience"], [451, 463, "Skills"], [464, 476, "Location"], [576, 591, "Skills"], [522, 534, "Skills"], [538, 542, "Skills"], [546, 550, "Skills"], [554, 563, "Skills"], [567, 572, "Skills"], [1055, 1067, "Graduation Year"], [1306, 1321, "Location"], [1393, 1421, "Location"], [1281, 1303, "College Name"], [1323, 1336, "Graduation Year"], [1338, 1356, "Degree"]]}
{"id": 101, "text": "kimaya sonawane\nThane, Maharashtra - Email me on Indeed: indeed.com/r/kimaya-\nsonawane/1f27a18d2e4b1948\n\nQuality education blended with sense of responsibility to utilize my professional as well as\ninterpersonal skills that enables me to achieve the goals.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nTechnical Support Engineer\n\nSAP -  Thane, Maharashtra -\n\nNovember 2016 to Present\n\nEDUCATION\n\nBE in computer science\n\nSSVPS’s Late B. S. Deore College of Engineering ,Dhule -  Dhule, Maharashtra\n\n2011 to 2016\n\nSKILLS\n\nnetwork engineers, Networking, CCNA, knowledge of Active Directory, DHCP, DNS ,\nTroubleshooting and fix Network related issues (2 years)\n\nCERTIFICATIONS/LICENSES\n\nCCNA(Cisco Certified Network Associate- Routing & Switching) , MCSA\n(Microsoft Certified Solution Associate)\n\nJuly 2016 to Present\n\nADDITIONAL INFORMATION\n\nPROFESSIONAL INTRESTS:\n• Leading and managing teams\n• Interacting with People\nCO CURRICULAR ACTIVITES:\n• Participated in \"Mech-Tricks\" in IMPULSE 2014 National Level Event.\n• Participated in \"Mech-Tricks\" in IMPULSE 2013 National Level Event.\n• Participated in \"Tech-Quiz\" in IMPULSE 2013 National Level Event.\n• Participated in \"Management Games\" Organised in Ganesh Utsav 2012.\n• Winner in \"Rangoli Competition\" Organised in Ganesh Utsav 2013.\n\nhttps://www.indeed.com/r/kimaya-sonawane/1f27a18d2e4b1948?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/kimaya-sonawane/1f27a18d2e4b1948?isid=rex-download&ikw=download-top&co=IN\n\n\nPERSONAL TRAITS:\n\n• Self Motivated\n• Adaptable\n• Confident\n• Team facilitator\n• Hard Worker", "meta": {}, "annotation_approver": "admin", "labels": [[524, 661, "Skills"], [424, 471, "College Name"], [400, 422, "Degree"], [334, 339, "Companies worked at"], [306, 332, "Designation"], [57, 103, "Email Address"], [0, 15, "Name"], [16, 34, "Location"], [341, 359, "Location"], [361, 387, "Years of Experience"], [473, 500, "Location"], [502, 514, "Graduation Year"], [687, 747, "Degree"], [750, 795, "Degree"], [797, 817, "Graduation Year"], [1510, 1524, "Skills"], [1527, 1536, "Skills"], [1539, 1548, "Skills"], [1551, 1567, "Skills"], [1570, 1581, "Skills"]]}
{"id": 102, "text": "Koushik Katta\nDevops\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Koushik-Katta/a6b19244854199ec\n\nDevOps Administrator with an experience of 3.4 years working in a challenging agile\nenvironment, looking forward for a position where I can use my knowledge pursuing my domain\ninterests. I'm more aligned to work for companies where knowledge and intellectual ability takes\nthe lead which can utilize a performance driven individual efficiently.\n\nWORK EXPERIENCE\n\nDevops Engineer\n\nInfosys limited -  Hyderabad, Telangana -\n\nDecember 2014 to Present\n\nHyderabad, since December 2014 to till date.\n\nSkill and Abilities:\nAtlassian Tools: Jira, Confluence\nConfiguration Management: Ansible /Chef\nCI Tools: Jenkins\nMonitoring Tools: Nagios\nCloud: AWS\nContainerization: Docker\nBuild Tools: Bamboo\\Maven\nLog Tools: Splunk\nDatabases: RDBMS, MYSQL, Oracle Database\nProgramming Languages: Python and Java\nScripting: Power Shell\nOperating Systems: Windows, Linux family, Redhat Linux\nMiddleware: Websphere, Tomcat, Websphere MQ\n\nResponsibilities:\nDEVOPS ADMINISTRATOR\nINFOSYS LTD.\n\nAtlassian tools Release Management according\n\nInfosys limited -\n\nDecember 2014 to Present\n\nto project needs.\n✓ Review and upgrade of Plugins to meet project requirements and to achieve better\nperformance.\n✓ Configuring Automated Mail handlers, Webhooks as POC to test the new demands raised by\nclient.\n\nhttps://www.indeed.com/r/Koushik-Katta/a6b19244854199ec?isid=rex-download&ikw=download-top&co=IN\n\n\n✓ JIRA Project Management/Administration.\n✓ Confluence Space Management/Administration.\n✓ Bitbucket Project/Repository Management/Administration (Enterprise/DataCenter)\n✓ Integration of Webhooks in Bitbucket.\n✓ Streamlining tools access management with Crowd.\n2. Administration and Maintenance of Jenkins\n✓ Configure and maintain Jenkins slaves as per the requirement.\n✓ Jenkins release management.\n✓ Work closely with Development teams to configure CI/CD Pipelines to automate their build &\ndeployment process.\n✓ Review, Installation/Upgrade and configuration of Jenkins Plugins.\n✓ Configuring proxy on the environments to enable security\n✓ Debug build issues\n3. Administration and Maintenance of Docker registry\n4. Working with Open-Source Nagios plugins on demand basis to setup ICINGA monitoring for our\non-premise Servers/Applications.\n5. Alerting Setup Splunk.\n6. Monitoring Dashboards setup using kibana.\n7. Working with product support teams to resolve the product bugs.\n8. Involve in client meetings and tool performance reviews to ensure stakeholder satisfaction.\n9. Work closely with Infrastructure Teams to setup/maintain/improve the above mentioned\napplication on large scale.\n\nEDUCATION\n\nBachelor Of Engineering in Mechanical Engineering\n\nLovely Professional University\n\n2010 to 2014\n\nSecondary School Certificate in education\n\nBoard of Intermediate education -  Hyderabad, Telangana\n\n2008 to 2010\n\nSister Nivedita School -  Karimnagar, Telangana\n\n2008\n\nSKILLS\n\nJira, Ansible, Jenkins, Splunk, Nagios, Docker, Python, AWS, Bamboo, Linux, Git, Chef, Windows,\nPowershell Scripting\n\nADDITIONAL INFORMATION\n\n• Ability to learn new technologies and processes rapidly and implement them in the project.\n• Highly motivated with very good problem solving and analytical skills Well organized, with\nexcellent in multitasking and prioritizing the work.\n\n\n\n• Effective communicator with an ability to convey ideas in speaking and writing.\n• Excellent analytical and decision making skills.\n• Ability to work in pressurized situation.\n• Hard worker and goal oriented.\n• Always ready to learn new skills", "meta": {}, "annotation_approver": "admin", "labels": [[2957, 3074, "Skills"], [2780, 2822, "Degree"], [2734, 2764, "College Name"], [2683, 2732, "Degree"], [1125, 1140, "Companies worked at"], [1044, 1064, "Designation"], [626, 1024, "Skills"], [559, 568, "Location"], [490, 505, "Companies worked at"], [65, 108, "Email Address"], [0, 13, "Name"], [14, 20, "Designation"], [22, 42, "Location"], [110, 130, "Designation"], [153, 162, "Years of Experience"], [473, 488, "Designation"], [507, 529, "Location"], [533, 557, "Years of Experience"], [570, 602, "Years of Experience"], [1144, 1168, "Years of Experience"], [1065, 1077, "Companies worked at"], [1483, 1521, "Skills"], [1525, 1567, "Skills"], [1571, 1649, "Skills"], [1667, 1688, "Skills"], [1692, 1739, "Skills"], [1778, 1785, "Skills"], [1811, 1818, "Skills"], [1852, 1859, "Skills"], [1931, 1946, "Skills"], [2045, 2060, "Skills"], [2123, 2128, "Skills"], [2179, 2194, "Skills"], [2223, 2237, "Skills"], [2263, 2269, "Skills"], [2340, 2346, "Skills"], [2351, 2391, "Skills"], [2766, 2778, "Graduation Year"], [2823, 2854, "College Name"], [2858, 2878, "Location"], [2880, 2892, "Graduation Year"], [2920, 2941, "Location"], [3194, 3210, "Skills"], [3221, 3241, "Skills"], [3246, 3263, "Skills"], [3263, 3278, "Skills"], [3285, 3310, "Skills"], [3425, 3472, "Skills"], [3520, 3531, "Skills"], [3536, 3549, "Skills"], [3560, 3574, "Skills"]]}
{"id": 103, "text": "Kowsick Somasundaram\nCertified Network Associate Training Program\n\nErode, Tamil Nadu - Email me on Indeed: indeed.com/r/Kowsick-\nSomasundaram/3bd9e5de546cc3c8\n\nBachelor of computer science graduate seeking opportunities in the field of ITIS to contribute\nto corporate goals and objectives. Easily adapt to changes, with eagerness toward learning and\nexpanding capabilities.\n\nEXPERIENCE:-\n\nWORK EXPERIENCE\n\nCertified Network Associate Training Program\n\nCisco -\n\nJuly 2013 to October 2013\n\n• Workshop on computer Hardware& Software.\n\n• Workshop on Web development.\n\nEDUCATION\n\nBachelor of computer science in computer science\n\ninDR N.G.P ARTS AND SCIENCE COLLEGE -  Coimbatore, Tamil Nadu\n\nSKILLS\n\nDHCP (Less than 1 year), DNS (Less than 1 year), EXCHANGE (Less than 1 year), exchange\n(Less than 1 year), LAN (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS:-\n\n• Messaging: MS exchange, Lotus client and MS outlook issue coordination to user.\n\n• Users / Share folders creation and permission assigning.\n\n• Networking: TCP/IP, DNS, DHCP, and LAN/WAN.\n\n• Monthly patching update activity and server owner approval / RFC follow-ups.\n\nhttps://www.indeed.com/r/Kowsick-Somasundaram/3bd9e5de546cc3c8?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Kowsick-Somasundaram/3bd9e5de546cc3c8?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": "admin", "labels": [[696, 1129, "Skills"], [625, 661, "College Name"], [575, 624, "Degree"], [451, 457, "Companies worked at"], [406, 450, "Designation"], [107, 158, "Email Address"], [21, 65, "Designation"], [0, 20, "Name"], [67, 84, "Location"], [160, 188, "Degree"], [461, 486, "Years of Experience"], [664, 686, "Location"]]}
{"id": 104, "text": "Lakshika Neelakshi\nSenior Systems Engineer - Infosys Limited\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Lakshika-\nNeelakshi/27b31f359c52ef76\n\nAn organized and independent individual looking for role to be able to effectively coordinate\ntasks in\nproject and accomplish the result adhering with timeliness and creativity.\n\nWORK EXPERIENCE\n\nSenior Systems Engineer\n\nInfosys Limited -  Bengaluru, Karnataka -\n\nJanuary 2018 to Present\n\nEnvironment SAPUI5 version 1.4\n\nDescription:\nAirbus SE is a European multinational corporation that designs, manufactures, and sells civil and\nmilitary aeronautical products worldwide.\n\nProject Contribution:\n• Working on creating a custom Annotation Tool (AnnoQ) using a third party js library to annotate\nany 2D picture within a SAPUI5 application specifically designed to work on laptop and Desktop.\n• The custom tool can be called from any SAP or Non- SAP system to be used as a plug-in to\nannotate pictures across the Airbus SE application to be used across browsers like\nChrome/Edge and IE and should be compatible with Windows 7, 8, 10.\n• Parts of a picture can be highlighted and marked and using this tool and can be saved for\nreference or future use.\n• Worked extensively on Fabric js and created various customized objects like Call out Box,\nMeasurement Arrow, Datum, Cross Datum, Forward arrow, etc on mouse and object events.\n• Implemented various functionality like color change, crop, zoom, text size selection, width\nselection, saving the annotation in JSON format in the backend so as to retain the original\npicture as it is.\n• Also contributed in designing the layout and overall appearance of the tool.\n• Contributed in integrating the application with MNC Mobile Application.\n\nSenior Systems Engineer\n\nInfosys Limited -  Bengaluru, Karnataka -\n\nOctober 2015 to Present\n\n• Having 2.5 years of experience in SAPUI5/Fiori, Netweaver Gateway Odata Services and SAP\nABAP development of large scale ERP packages.\n• Working with SAP/R3, ECC environments also having experience in HTML, JavaScript, JSON,\nXML, CSS.\n\nhttps://www.indeed.com/r/Lakshika-Neelakshi/27b31f359c52ef76?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Lakshika-Neelakshi/27b31f359c52ef76?isid=rex-download&ikw=download-top&co=IN\n\n\n• Currently working as Senior Systems Engineer with Infosys Limited, Bangalore since October\n2015.\n\nSAP Expertise\nSAPUI5/Fiori\n\n• Expertise in developing SAP UI5 applications using ADT (Eclipse) /WebIDE, jQuery, JavaScript,\nHTML5 and CSS3. Also consuming the data by using Net weaver Gateway services.\n• Well equipped in extending and customizing various UI5 controls specially charts, vizframes\nand Smart Table, etc.\n• Experience in integrating Gateway Odata/ JSON services with UI% application.\n• Worked extensively with application having multiple Views and controllers and hence\nexperienced in navigating through it using Routing using both XML and JS views.\n• Exposed to be working in SAPUI5 Custom Applications and Standard Fiori Applications.\n• Experienced with UI5 application development in local server and debug it in Chrome/Firefox\ndebuggers.\n• Experienced in Github.\n\nAdvance Business Application Programming\n• ALV (ABAP List Viewer) - Grid and List Display.\n• SAP smart forms and Scripts.\n• Worked in BDC (Batch Data Communication) - BI and CTU method\n• RFC Function Modules.\n• Exposure in creating Data Dictionary objects (Domain, Data Elements, Structures and Views)\n• Conceptual knowledge Dialog programs using Menu Painter/Screen Painter\n• Worked on Object Oriental Programming concepts (OOPS)\n\nSystems Engineer\n\nInfosys Limited -  Bengaluru, Karnataka -\n\nAugust 2017 to December 2017\n\nEnvironment SAPUI5 version 1.28\n\nDescription:\nJabil Inc. is a US based global manufacturing services company headquartered in St. Petersburg,\nFlorida. Jabil is involved in design engineering services. The company has industrial design\nservices\nthat concentrate on designing the look and feel of plastic and metal enclosures that house printed\ncircuit board assemblies and systems.\n\nProject Contribution:\n• Created custom Fiori apps for the client including screens having functionality of create\ndocument, Inbox to store the transaction mail and Dashboards to view day to day transactions.\n• Prepared multiple screens for the dashboard with view reusability features.\n• Worked on OData binding and hence display of data in relevant format on to the screen.\n• Implemented various other functionalities based on OData consumption, Routing and\nNavigation and JSON models.\n\n\n\n• Worked on complex functionality like excel data transfer directly to UI5 tables on screen while\nkeeping the table data editable and also excel data upload functions to the table.\n• Have clear and distinct knowledge of various UI5 controls.\n\nSystems Engineer\n\nInfosys Limited -  Hyderabad, Telangana -\n\n2016 to July 2017\n\nEnvironment SAPUI5 version 1.5\n\nDescription:\nAmerican Water is an American public utility company operating in the United States and Canada.\nIt\nwas founded in 1886 as the American Water Works & Guarantee Company.\n\nProject Contribution:\n• Worked as SAPUI5 developer in KPI project for American Waters account.\n• Developed KPI Dashboards using controls like Vizframe, Tables etc.\n• Worked with different types Micro charts control available like Column, Comparison, Radial etc.\n• Also extended some of them as per project requirements.\n• Experience of working with OData to get the data to be displayed on the dashboards.\n• Additionally, implemented some special features in the Dashboard development like Export to\nexcel link, Download Image of the UI charts & documentation link in Fiori tiles.\n• Also worked on multiple levels of drill downs using multiple controllers and views.\n• Individually worked and delivered an extension for one of the UI5 controls which got much\nappreciated from clients and offshore team as well.\n• Implementation of the apps on Fiori Launchpad.\n• Implemented multiple filters on the data pulled from the service for desired results as per\nproject requirements.\n\nSAP UI5:\nUI5 Controls:\n• Created dashboards by various UI5 controls such as Tables, vizFrame, Tab Filters to name a few.\n• Exposure in extending various UI5 standard controls to get the desired result.\n• Used SAP best practices while using all these controls.\n\nSystems Engineer\n\nInfosys Limited -  Hyderabad, Telangana -\n\nJuly 2016 to September 2016\n\nEnvironment SAP 5.0\n\nDescription:\nHarley Davidson is an American motorcycle manufacturer, founded in Milwaukee, Wisconsin in\n1903.\n\nProject Contribution:\n\n\n\n• Innovation track dashboard preparation.\n• Creation of POC application to filter the list based upon multiple filters with the help of Dialog\nProgramming.\n• The application also had functionalities like add new POCs, update and delete the existing\nones and modify others.\n• Data Upload to the SAP system from Excel sheet using BDC.\n• Additionally, the task required knowledge of Data Dictionary, Report Programming- Classical &\nALV, Module Pool Programming, Batch Data Communication- Call Transaction & Session\nMethod.\n\nEDUCATION\n\nBachelor of Engineering in Instrumentation Technology in\nInstrumentation Technology\n\nDayananda Sagar College of Engineering -  Sagar, Karnataka\n\nSKILLS\n\nSAPUI5 (2 years), CSS. (2 years), EMPLOYEE RESOURCE GROUP (2 years), ENTERPRISE\nRESOURCE PLANNING (2 years), SAP ABAP (2 years)\n\nADDITIONAL INFORMATION\n\nTechnical skills\nLanguages SAPUI5 (Primary Skill),\nABAP/4, C, C++, HTML, CSS, JavaScript, PHP,\njQuery, Ajax.\nERP SAP R/3 in 4.7, 5.0 (E)\nOperating Systems Windows\nDatabase MySQL, Oracle", "meta": {}, "annotation_approver": "admin", "labels": [[2329, 2345, "Companies worked at"], [2301, 2324, "Designation"], [1741, 1764, "Designation"], [352, 375, "Designation"], [105, 154, "Email Address"], [44, 60, "Companies worked at"], [19, 42, "Designation"], [0, 18, "Name"], [62, 82, "Location"], [377, 392, "Companies worked at"], [396, 416, "Location"], [420, 443, "Years of Experience"], [1766, 1781, "Companies worked at"], [1785, 1805, "Location"], [1809, 1832, "Years of Experience"], [1843, 1852, "Years of Experience"], [1870, 1882, "Skills"], [1921, 1929, "Skills"], [1986, 1992, "Skills"], [1994, 2010, "Skills"], [2037, 2041, "Skills"], [2043, 2053, "Skills"], [2055, 2059, "Skills"], [2061, 2064, "Skills"], [2066, 2069, "Skills"], [2347, 2356, "Location"], [2357, 2375, "Years of Experience"], [2378, 2381, "Skills"], [2392, 2398, "Skills"], [2399, 2404, "Skills"], [2432, 2435, "Skills"], [2436, 2439, "Skills"], [2459, 2462, "Skills"], [2464, 2471, "Skills"], [2474, 2480, "Skills"], [2482, 2488, "Skills"], [2490, 2500, "Skills"], [2502, 2507, "Skills"], [2512, 2516, "Skills"], [2633, 2636, "Skills"], [2664, 2673, "Skills"], [2678, 2689, "Skills"], [2923, 2926, "Skills"], [2931, 2933, "Skills"], [2724, 2743, "Skills"], [2968, 2974, "Skills"], [3107, 3131, "Skills"], [3150, 3156, "Skills"], [3202, 3224, "Skills"], [3252, 3255, "Skills"], [3326, 3343, "Skills"], [3584, 3588, "Skills"], [3591, 3607, "Designation"], [3609, 3624, "Companies worked at"], [3628, 3648, "Location"], [3652, 3680, "Years of Experience"], [3694, 3700, "Skills"], [4362, 4367, "Skills"], [4492, 4497, "Skills"], [4538, 4542, "Skills"], [4625, 4628, "Skills"], [4782, 4785, "Skills"], [4797, 4813, "Designation"], [4815, 4830, "Companies worked at"], [4834, 4854, "Location"], [4858, 4875, "Years of Experience"], [4889, 4895, "Skills"], [5125, 5131, "Skills"], [5198, 5212, "Skills"], [5440, 5445, "Skills"], [5822, 5825, "Skills"], [6068, 6089, "Skills"], [6123, 6126, "Skills"], [6144, 6173, "Skills"], [6221, 6224, "Skills"], [6277, 6280, "Skills"], [6329, 6345, "Designation"], [6347, 6362, "Companies worked at"], [6366, 6386, "Location"], [6390, 6417, "Years of Experience"], [6431, 6438, "Skills"], [6870, 6873, "Skills"], [6886, 6891, "Skills"], [6956, 6971, "Skills"], [6973, 6991, "Skills"], [6993, 7059, "Skills"], [7108, 7161, "Degree"], [7193, 7231, "College Name"], [7235, 7251, "Location"], [7261, 7599, "Skills"]]}
{"id": 105, "text": "Madas Peddaiah\nAnantapur, Andhra Pradesh - Email me on Indeed: indeed.com/r/Madas-\nPeddaiah/557069069de72b14\n\n• Having 3 moths of experience in Manual Testing.\n• Previously worked with Infosys Limited, Mysore as a Software Test Engineer.\n• Having good experience in Executed Test Cases as per Client Requirements.\n• Having good experience in identifying the test scenarios and designing the test cases.\n• Worked on IE, Firefox and Chrome Driver using Selenium.\n• Good Knowledge in Core Java, SQL.\n• Experience in designing, preparing and executing test cases for client server and web based\napplications STLC Concepts.\n➢ Web Based Application Testing\n• Experience in understanding business requirements, preparing and execution of test cases for\nSystem Customizations/Enhancements and Initiatives.\n• Quick learner with the ability to grasp new technologies.\n• Excellent team player having ability to finish the tight deadlines and work under pressure.\n• Good exposure on Manual Testing & Bug Life Cycle.\n\nWORK EXPERIENCE\n\nInfosys Limited -  Mysore, Karnataka -\n\nSeptember 2014 to December 2014\n\nEducational Technologies:\n\nSoftware Test Engineer\n\nInfosys Limited -\n\nSeptember 2014 to December 2014\n\n-September 2014 to December 2014.\nProject: 1\nClient: Loan Account\nRole: Software Test Engineer\nTeam Size: 4\nTechnologies: J2EE, Oracle 10g, Windows 7\nUsed Tools: Selenium, Core Java, HPQC and Eclipse.\nDuration: September 2014 to December 2014\n\nProject Description:\nIn this project we maintain all details about account transaction details, customer loan account\ndetails, calculate monthly EMI's and there activity like - Account login details, Account transaction,\nLoan account details etc.\nResponsibilities:\n• Participated in identifying the test scenarios and designing the test cases.\n• Prepared and Executed Test Cases as per Client Requirements.\n\nhttps://www.indeed.com/r/Madas-Peddaiah/557069069de72b14?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Madas-Peddaiah/557069069de72b14?isid=rex-download&ikw=download-top&co=IN\n\n\n• Performed Manual Testing on some modules.\n• Feasibility analysis of Manual Test Cases.\n• Performed Functional, Compatibility testing on different browsers like Firefox & Chrome.\n• Defect tracking & Bug Reporting.\nProject: 2\nClient: Hospital Management\nRole: Software Test Engineer\nTeam Size: 4\n\nEDUCATION\n\nB-Tech\n\nKuppam Engineering College -  Kuppam, Andhra Pradesh\n\n2014\n\nEducation, A.P\n\nVani Jr college\n\nOctober 1977 to 2010\n\nEducation, A.P\n\nPadmavani High School\n\n2008\n\nADDITIONAL INFORMATION\n\nTechnical Skills:\nTools: Manual Testing, Selenium (Selenium IDE, Selenium Web Driver), Eclipse IDE.\nLanguages: C, Core Java\nDatabase: SQL\nOperating Systems: Windows XP, 7, 8\nManagement Tool: HP Quality Center\nDefect Tracking Tool: JIRA\n\nProjects Summary:\n\nTechnologies: J2EE, Oracle 10g, Windows 7\nUsed Tools: Selenium, Core Java, HPQC and Eclipse.\nDuration: September 2014 to December 2014.\nProject Description:\nIn this project, we maintain all details about Hospital details like- Hospital address, Consultant\nDoctor, Doctor Details, Permanent Doctor, Medicine, Lab Test, In Patient, Out Patient etc.\nResponsibilities:\n• Prepared and Executed Test Cases as per Client Requirements.\n• Participated in identifying the test scenarios and designing the test cases.\n• Performed Manual Testing on some modules.\n\n\n\n• Feasibility analysis of Manual Test Cases.\n• Performed Functional, Compatibility testing on different browsers like Firefox & Chrome.\n• Defect tracking & Bug Reporting.", "meta": {}, "annotation_approver": "admin", "labels": [[2566, 2783, "Skills"], [2418, 2422, "Graduation Year"], [2364, 2390, "College Name"], [2356, 2362, "Degree"], [2308, 2331, "Designation"], [1270, 1293, "Designation"], [1146, 1161, "Companies worked at"], [1122, 1145, "Designation"], [1022, 1037, "Companies worked at"], [185, 200, "Companies worked at"], [63, 109, "Email Address"], [0, 14, "Name"], [15, 40, "Location"], [119, 140, "Years of Experience"], [202, 208, "Location"], [214, 236, "Designation"], [266, 285, "Skills"], [415, 459, "Skills"], [481, 490, "Skills"], [492, 495, "Skills"], [1041, 1058, "Location"], [1062, 1093, "Years of Experience"], [1163, 1196, "Years of Experience"], [1199, 1230, "Years of Experience"], [1409, 1440, "Years of Experience"], [1360, 1368, "Skills"], [1370, 1379, "Skills"], [1381, 1385, "Skills"], [1390, 1397, "Skills"], [2394, 2416, "Location"], [2858, 2866, "Skills"], [2868, 2877, "Skills"], [2879, 2883, "Skills"], [2888, 2895, "Skills"], [2907, 2938, "Years of Experience"]]}
{"id": 106, "text": "Madhuri Sripathi\nBanglore, Karnataka, Karnataka - Email me on Indeed: indeed.com/r/Madhuri-\nSripathi/04a52a262175111c\n\nAround 4 years of IT experience in analysis, testing and scripting on L2/L3 layer protocols and\ndesiging testcases and automating the same in TCL/TK and Python.\n\n• Expertise in Networking Protocols L2, L3 protocols, Manual, Performance, Platform, Regression\nand Automation Testing.\n• Experience in python scripting and PYATS framework.\n• Coordinating with onsite/offsite teams in resolving the defects found in Testing and working on\nqueries raised by customers..\n• Reviewing the automated scripts.\n• Exposure to Networking Protocols such as DHCP, OSPF, RIP, VLAN, STP/RSTP, LACP, TCP/IP,\nIPv4, Ipv6, Ethernet.\n• Automation in Python.\n• Excellent ability to plan, organize and prioritize my work to meet on time the deadlines of my\nclients and keep customer's satisfaction at the highest level possible.\n• Proven ability in quick understanding and learning of new technologies and their application\nin business solutions\n• Good debugging and problem solving skills with excellent understanding of system\ndevelopment methodologies, techniques and tools.\n• Highly motivated team member with strong communication, analytical and organizational\nskills.\n• Strong communication, interpersonal and analytical skills with proficiency at grasping new\nconcepts quickly and utilizing the same in a productive manner.\n• Willingness and ability to quickly adapt to new environment.\n• Good positive attitude and ability to learn new things independently.\n\n• Worked as Senior project engineer in Wipro Technologies, from Jan2014 to till date.\n\nLanguages: C\nNetwork Analysis Tools: QDDTS, GNS3, IXIA, SPIRENT, PAGENT\nRouting protocols VLAN, ETHECHANNELSTP, RSTP, RIP, EIGRP, OSPF, BGP, MPLS, L2VPN, L3VPN,\nIPSEC and MULTICAST.\nScripting Language Perl, Tcl/TK, Python\nTraffic Generators IXIA, PAGENT, SPIRENT\nManagement Protocols Telnet, SNMP\n\nWilling to relocate to: UAE - Dubai - abu-dabhi\n\nWORK EXPERIENCE\n\nSenior project engineer\n\nCisco -  Bengaluru, Karnataka -\n\nMarch 2014 to Present\n\nhttps://www.indeed.com/r/Madhuri-Sripathi/04a52a262175111c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Madhuri-Sripathi/04a52a262175111c?isid=rex-download&ikw=download-top&co=IN\n\n\nCisco 7600 is a router which supports both layer2 and layer3 protocols. It mainly deploys protocols\nlike MPLS and having specific modules to support the protocols IPSEC. Worked as system testing,\nperformance testing, stress testing and regression testing for all the IOS release on all layer 2\nand layer 3 protocols.\n\nWipro Technologies Limited (Bangalore, Karnataka)\nSenior project engineer (March 2014 till date)\n\nSenior software engineer\n\nWipro -\n\nFebruary 2014 to Present\n\nResponsibilities:\n• Responsible for regression and Manual testing of CISCO IOS -7600\n• Test case execution, test case results tracking, debugging, logging defects in CDETS,\nreproductions and fix verification.\n• Configuration and Testing on Routing protocols OSPF, BGP, OSPF, MPLS, L3VPN, L2VPN, IPSEC,\nQOS, SNMP and MULTICAST features on Cisco 7600 Routers.\n• Filed critical bugs of high severity, through root cause analysis and effective testing methods\nBug verification, Bug tracking, and documentation and review bug fixes.\n• Engaged in regression testing, filing bugs against Cisco IOS images to improve the quality of\nthe images and send weekly test reports.\n• Mentoring of new joiners in the team and conducting technical training sessions.\n• Responsibility for the 7600 platform customer queries (AT&T, Bharati, Vodafone, German IT,\netc )\n• Involved in Sev1, Sev2 and sev3 cases and MW related to L2/L3 Features.\n• Create a Local Repro of the issue which was raised by the customer.\n• Analyzed the customer issues and will provide the solutions to the customers\n• Worked with Developer to verify the DDTs fix for the customer Found Defects\n• System Testing on every New IOS build for the L2/L3 protocols.\n• Configuration and Testing on routing protocols\n• Working on Functionality, Scalability and Performance testing\n• Preparing of Test beds and topologies using Line cards - SIP200, SIP400, SIP600, ES+, ES20, GIG\nand TenGig Lancards, pagent, IXIA Traffic generators etc. to create customer setup in local Labs\n• Knowledge on TCL scripting and automated customer found issues into regression testing and\nalso able to troubleshoot the script issues.\n\nEDUCATION\n\nMaster degree in computer science in computer science\n\nPES college\n\nS.S.C in computer science\n\nRajah college\n\n\n\nSKILLS\n\nLINUX (4 years), UNIX (4 years), ospf (4 years), bgp (4 years), mpls (4 years), ipsec (4 years),\nmulticast (4 years), l2vpn (4 years), l3vpn (4 years), tcl (4 years), python (2 years)\n\nADDITIONAL INFORMATION\n\nOperating Systems: LINUX, UNIX\nOther protocols ARP, RARP, ICMP,ospf,bgp,mpls,l2vpn,l3vpn\nAutomaton tools: tcl,python", "meta": {}, "annotation_approver": "admin", "labels": [[4563, 4747, "Skills"], [4538, 4552, "College Name"], [4511, 4536, "Degree"], [4498, 4509, "College Name"], [4443, 4496, "Degree"], [2737, 2742, "Companies worked at"], [2711, 2736, "Designation"], [2663, 2686, "Designation"], [2037, 2042, "Companies worked at"], [2012, 2035, "Designation"], [1573, 1596, "Designation"], [62, 118, "Email Address"], [0, 16, "Name"], [17, 47, "Location"], [126, 133, "Years of Experience"], [261, 267, "Skills"], [272, 278, "Skills"], [296, 333, "Skills"], [335, 399, "Skills"], [438, 453, "Skills"], [417, 433, "Skills"], [732, 752, "Skills"], [943, 995, "Skills"], [1047, 1056, "Skills"], [1061, 1083, "Skills"], [1174, 1190, "Skills"], [1208, 1266, "Skills"], [1270, 1327, "Skills"], [1443, 1486, "Skills"], [1495, 1512, "Skills"], [1600, 1618, "Companies worked at"], [1620, 1645, "Years of Experience"], [1659, 1660, "Skills"], [1685, 1719, "Skills"], [1849, 1869, "Skills"], [1889, 1910, "Skills"], [1932, 1944, "Skills"], [1970, 1993, "Location"], [2046, 2066, "Location"], [2070, 2091, "Years of Experience"], [2613, 2639, "Companies worked at"], [2641, 2661, "Location"], [2688, 2708, "Years of Experience"], [2746, 2770, "Years of Experience"], [4791, 4796, "Skills"], [4798, 4802, "Skills"], [4878, 4881, "Skills"], [4882, 4888, "Skills"], [4819, 4860, "Skills"]]}
{"id": 107, "text": "Mahesh Vijay\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Mahesh-Vijay/a2584aabc9572c30\n\nOver 6.5 years of functional enriched experience in ERP in the Procurement to Pay domain. Was\nassociated with Oracle India Pvt Ltd, Bangalore as Team lead - Supplier Data Management in\ntheir Global Financial Information Centre (Global Shared Service Center) for Oracle's Business\nfrom Sep 2007- Feb 2014.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nTeam lead - supplier data management\n\nOracle India -  Bangalore, Karnataka -\n\nMarch 2014 to December 2016\n\nManaging Partner of family business of Tours & Travels\n\nTeam Lead\n\nOracle India Pvt Ltd -\n\nOctober 2013 to February 2014\n\nSupplier Data Management\n\nLead Analyst -SME -Supplier Data Management\n\nOracle India Pvt Ltd -\n\nSeptember 2012 to October 2013\n\nSenior Analyst -Supplier Data Management\n\nOracle India Pvt Ltd -  Bengaluru, Karnataka -\n\nJanuary 2010 to September 2012\n\nAcademia\n• Bachelors in Commerce (B.Com) from Vivekananda Degree College, Bangalore\nUniversity(2007)\n• Pre University from Vivekananda PU College, Bangalore(2004)\n• Passed 10th STD from Angels High School, Bangalore(2002)\nFunctional & Technical Expertise\nFunctional\n- Ensuring data quality in Purchasing Supplier management (PSM) registry and Trading\nCommunity Architecture of the Oracle e-business suite bundle.\n\nhttps://www.indeed.com/r/Mahesh-Vijay/a2584aabc9572c30?isid=rex-download&ikw=download-top&co=IN\n\n\n- Managing all projects and ensuring the completion of the same within timeframe. Projects\nlike - Oracle Fusion Supplier Self Service, Supplier cleanup, migration of merger and acquisition\nsuppliers, UAT\n- Managing activities like synchronizing creation and updates of supplier records.\n- Oracle Fusion - Related to Procurement modules -Fusion Supplier Portal\n- Sound knowledge in the Oracle Financial applications domain that includes various cycles like\nthe Expense Reporting, Accounts Payables, Accounts Receivables, and Tactical Purchasing.\n- R12 User Acceptance Testing, writing test cases and author test reports which analyze the\nreported defects.\nTechnical\n- Oracle Applications Releases: 12, 11.5, Oracle Applications Modules Purchasing, iProcurement\n- Business Intelligence Reporting Tools: Oracle Discoverer & Accounting Software Tally 7.2\nProjects & Accomplishments\nOracle Master Data Management- Legacy Data Cleanup Project\nRole:\n• Global Country wise clean up initiative focusing on achieving a clean and accurate database\n• Supplier Information retrieval based on information in Purchase orders\nOracle Fusion UAT- Supplier Self Service\nRole:\n• Internal UAT-Part of upgrade team, testing all functionality and interfaces.\n• Monitoring the new add on features in Fusion and old features assigned\n\n11i to R12 Migration- Manual UAT\nRole:\n• Testing for Supplier creations, Merges, Tax set ups, Withholding and TDS code, Bank details etc\n• Monitoring the new add on features in R12 and old features assigned\n\nOracle Supplier Life Cycle Management (SLM) or Supplier Hub Project\nRole:\n• Internal UAT- testing all functionality and interfaces for creating a 360 degree view for each\nand every supplier.\n• Responsible for setting up suppliers' online, assisting requesters and suppliers to register a\nsupplier and iSupplier access. Testing fast and flexible supplier searches that can be made into\ntemplates resulting in quick report generation Create and test blended supplier records from\nmultiple sources\n\nSKILLS\n\nBCP (6 years), Data Governance (6 years), Data Management (6 years), Oracle (6 years),\nReporting Tools (6 years)\n\nADDITIONAL INFORMATION\n\nKey Skills\n• Process Management & Improvement\n• Operations & Team Management\n• Data Governance & Automation\n• Oracle E- Business Systems experience in Supplier Data/Vendor Data management\n• BCP Policies & Procedures\n\n\n\n• Desk Manuals/Business Process & Navigation Documentation\n• Business Ethics\n• Professional Communication\n• Reporting Tools & Microsoft Office Applications", "meta": {}, "annotation_approver": "admin", "labels": [[1089, 1093, "Graduation Year"], [1055, 1077, "College Name"], [1027, 1031, "Graduation Year"], [978, 1026, "College Name"], [943, 973, "Degree"], [810, 850, "Designation"], [709, 753, "Designation"], [492, 498, "Companies worked at"], [105, 114, "Years of Experience"], [56, 98, "Email Address"], [0, 12, "Name"], [13, 33, "Location"], [385, 403, "Years of Experience"], [210, 230, "Companies worked at"], [232, 241, "Location"], [245, 281, "Designation"], [454, 490, "Designation"], [499, 528, "Location"], [532, 559, "Years of Experience"], [617, 626, "Designation"], [628, 648, "Companies worked at"], [652, 681, "Years of Experience"], [754, 774, "Companies worked at"], [778, 808, "Years of Experience"], [852, 872, "Companies worked at"], [876, 896, "Location"], [900, 930, "Years of Experience"], [1079, 1088, "Location"], [3466, 3978, "Skills"]]}
