{"id": 0, "text": "<PERSON><PERSON>\nAssociate Software Engineer\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/<PERSON><PERSON>-<PERSON>/cb1ede9ee6d21bca\n\nWilling to relocate to: Bangalore Urban, Karnataka\n\nWORK EXPERIENCE\n\nAssociate Software Engineer\n\noracle\n\n\u2022 Associate Software Engineer in Oracle India Pvt Ltd. (through Trigent Software Pvt Ltd)\n\u2022 Experience in Oracle Hyperion Products and Automation Scripting. (September 2017 onwards)\nPerform automation through python and batch scripting.\nProvide application maintenance and client support.\n\nEDUCATION\n\nB.E. in ENC\n\nNorth Maharashtra University\n\n2017\n\nCertificate\n\nInstitute/Board\n\nSKILLS\n\nC++ (Less than 1 year), DATABASES (Less than 1 year), ECLIPSE (Less than 1 year), ESSBASE\n(Less than 1 year), HYPERION (Less than 1 year), python, java\n\nLINKS\n\nhttp://www.linkedin.com/in/puneet-singh-277a11151\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\n\n\u2022 Languages: Java, C, C++, Python, Batch\n\u2022 Databases: Oracle, Mysql\n\u2022 Ides: Eclipse, PyCharm\n\u2022 Operating systems: Windows and Linux\n\u2022 Dataware housing Tools: ODI, Hyperion Essbase and Planning, FDMEE\n\nhttps://www.indeed.com/r/Puneet-Singh/cb1ede9ee6d21bca?isid=rex-download&ikw=download-top&co=IN\nhttp://www.linkedin.com/in/puneet-singh-277a11151", "labels": []}
{"id": 1, "text": "Rahul Bollu\nSoftware Engineer - Disney\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Rahul-Bollu/dc40f5ce78045741\n\n\u2022 Over 3.5 years of experience in implementing organization DevOps strategy in various\nenvironments of Linux and windows servers along with adopting cloud strategies based on\nAmazon Web Services.\n\u2022 Experience in Cloud Technologies like Amazon Web Services (AWS) VPC, EC2, S3, ELB, IAM,\nAuto Scaling, Route 53, SQS, SNS, RDS, Cloud Watch, Dynamo DB.\n\u2022 Utilized Cloud Watch to monitor AWS resources to set alarms for notification, and to monitor\nlogs for a better operation of the system.\n\u2022 Experience working with automated build platforms/continuous integration using DevOps\narchitecture.\n\u2022 Implementing DevOps tools like Ansible as configuration management for Continuous\nIntegration and Continuous Deployment with build tools using Maven on Cloud Infrastructure\nusing AWS.\n\u2022 Experience on version control tool GIT- Creating branches, tracking changes, maintaining the\nhistory of code and helping the Developers in GIT related issues.\n\u2022 Worked on Jenkins for continuous integration and for End to End automation for all build and\ndeployments.\n\u2022 Worked with Ansible as a configuration management tool, created playbooks to automate\nrepetitive tasks, quickly deploy applications, and proactively manage change.\n\u2022 Knowledge on Docker.\n\nWORK EXPERIENCE\n\nSoftware Engineer\n\nDisney -\n\nSeptember 2014 to Present\n\nResponsibilities:\n* Coordinate/assist developers with establishing and applying appropriate branching, labeling/\nnaming conventions using GIT source control.\n* Implemented the setup for Master slave architecture to improve the performance of Jenkins.\n* Used Jenkins to implement Continuous Integration and deployment into Tomcat/Web logic\nApplication server.\n* Created Ansible playbooks for automating the Infrastructure, deployment process.\n* Managed clients, roles, tasks, playbooks in Ansible.\n* Deploy and monitor scalable infrastructure on AWS & configuration management.\n* Worked on making application more scalable and highly available in AWS.\n* Created AWS IAM roles & total architecture deployment end to end (creation of EC2 instances\n& its infrastructure)\n\nEnvironment: GIT, Maven, Jenkins, Tomcat, Docker, Jira, AWS, Ansible, LAMP\n\nSoftware Engineer\n\nhttps://www.indeed.com/r/Rahul-Bollu/dc40f5ce78045741?isid=rex-download&ikw=download-top&co=IN\n\n\nHCL Technologies -\n\nSeptember 2014 to Present\n\n\u3013 Coordinate/assist developers with establishing and applying appropriate branching, labeling/\nnaming conventions using GIT source control.\n\u3013 Implemented the setup for Master slave architecture to improve the performance of Jenkins.\n\u3013 Used Jenkins to implement Continuous Integration and deployment into Tomcat/Web logic\nApplication server.\n\u3013 Created Ansible playbooks for automating the Infrastructure, deployment process.\n\u3013 Managed clients, roles, tasks, playbooks in Ansible.\n\u3013 Deploy and monitor scalable infrastructure on AWS & configuration management.\n\u3013 Worked on making application more scalable and highly available in AWS.\n\u3013 Created AWS IAM roles & total architecture deployment end to end (creation of EC2 instances\n& its infrastructure).\n\nProcess Associate\n\nMicrosoft -\n\nJuly 2013 to August 2014\n\nResponsibilities:\n* Collect and document user requirements.\n* Design and develop database architecture for information systems projects.\n* Design, construct, modify, integrate, implement and test data models and database\nmanagement systems.\n* Conduct research and provide advice to other informatics professionals regarding the selection,\napplication and implementation of database management tools.\n* Operate database management systems to analyze data and perform data mining analysis.\n\nEDUCATION\n\nBachelor Of Science\n\nVaughn College of Aeronautics and Technology\n\nSKILLS\n\nAWS (3 years), Tomcat, Ansible, git, LAMP, docker, jenkins, Maven, Jira\n\nADDITIONAL INFORMATION\n\nTechnical Skills:\n\nCloud Technologies: AWS\n\nOperating Systems: Linux, Windows.\nVersion Control Systems: GIT\nAutomated Build Tools: Maven\nContinuous Integration: Jenkins\n\n\n\nScripting Languages: Shell Scripting\nConfiguration Management: Ansible.\nContainer service: Docker", "labels": []}
{"id": 2, "text": "Rajeev Kumar\nSenior Associate Consultant - Infosys/Offshore Lead\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Rajeev-Kumar/3f560fd91275495b\n\n\u2022An erudite professional with 2 years of IT/Presales/Project Management experience\n\u2022Possess knowledge of Project Management, Digital/Social Media Marketing, Analytics, BI\n(Business Intelligence), presales, Business\ndevelopment, strategy and planning, etc.\n\u2022Endowed with a passion for winning as exemplified through excellence in academic,\nextracurricular areas and organizational experience\n\u2022An effective communicator with strong analytical/logical skills to relate to people at any level\nof business and management\n\u2022Exposure in improving business performance by leveraging market insights to influence\nstrategic and tactical plans that delivered differentiated offerings to customers\n\u2022Possess problem solving capability keeping constraints in purview, innovation &amp;\nadaptability\n\u2022A quick learner with the expertise to work under pressure and meet deadlines\n\u2022Pleasing personality with a zest for life, knowledge and sound understanding of technology and\nthe present world\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSenior Associate Consultant\n\nInfosys Limited -  Bangalore Urban, Karnataka -\n\nJuly 2017 to Present\n\n- Project Management Office (PMO)/Offshore Lead for a leading Global Oil &amp; Gas utility giant\n- Handling multiple service areas viz. risks and issues, planning and scheduling and assurance\nprocesses for all projects and engagements across the EMEA geography\n\nAssociate Consultant\n\nInfosys -  Pune, Maharashtra -\n\nMay 2016 to June 2017\n\n- Presales (Domain and Process Consulting) for Enterprise Asset Management (EAM) Supply Chain\nDomain\n- Pega Marketing and BPM (Business Process Management) team as a functional consultant\n- Problem Definition: Conduct secondary research that helps identify problem areas\n- Effort Estimation and preparing Deal Pricing with vendors to provide data points for proposal\ndevelopment\n- Solution Evaluation &amp; Recommendation: Understand solutions recommended and explore\nalternatives based on research (literature survey, information based in public domains etc.)\n-Requirement Analysis &amp; Detailing of Processes: Create requirement specifications from\nbusiness needs. Define detailed functional, process, infrastructure based on requirements\n\nhttps://www.indeed.com/r/Rajeev-Kumar/3f560fd91275495b?isid=rex-download&ikw=download-top&co=IN\n\n\n- Development/Configuration: Configure and build the application, process solution in line with\nthe design document\n- Issue Resolution: Understand the issue, diagnose the root cause and shortlist solution\nalternatives\n- Marketing &amp; Branding: Create marketing material in the form of case studies or solution\ndocumentation\n- Configure and assist in evaluating micro vertical/service/solution requirements on the products,\ndocumenting these in presentations and creating solution brochures\n\nEDUCATION\n\nPGDM in Marketing\n\nGoa Institute of Management -  Goa\n\nJune 2014 to March 2016\n\nB.Tech in Computer Science and Engineering\n\nUttar Pradesh Technical University -  Ghaziabad, Uttar Pradesh\n\nJuly 2010 to April 2014\n\nSKILLS\n\nAdobe Photoshop (Less than 1 year), Ads (Less than 1 year), BI (1 year), Business Intelligence\n(1 year), MS Excel (2 years), presales (1 year), Tableau (1 year), Pega Marketing and BPM (Less\nthan 1 year), MS Word (2 years), MS Powerpoint (2 years), MS Sharepoint (2 years), Spss (Less\nthan 1 year), IBM Maximo (Less than 1 year), marketing strategy (2 years), Business Analysis\n(2 years), communication and soft skills (2 years), Project Management (2 years), Project\nPlanning (2 years), Team Management (1 year)\n\nLINKS\n\nhttp://www.linkedin.com/in/rajeevkumar91\n\nCERTIFICATIONS/LICENSES\n\nGoogle Adwords\n\nDecember 2016 to December 2017\n\nGoogle Analytics\n\nFebruary 2017 to August 2018\n\nHubSpot Inbound Marketing\n\nDigital Vidya Certified Digital Marketer\n\nhttp://www.linkedin.com/in/rajeevkumar91\n\n\nADDITIONAL INFORMATION\n\nSoft Skills\n\nCommunicator, Innovator, Team Player, Analytical Collaborator. Intuitive\n\nTechnical Skills\n\u2022 Google AdWords, Google Analytics, Inbound Marketing, SEO (On-page and Off- page), SEM,\nFacebook Ads and Social\n\u2022Project Management, MS Project Professional\nMedia Campaigns (Content Bucketing)\n\u2022 Tableau, Microsoft Power BI, SPSS\n\u2022 MS Office Suite, Advanced Excel, MS SharePoint, Visio, Adobe Photoshop, Pega Marketing and\nBPM", "labels": []}
{"id": 3, "text": "Ram Edupuganti\nSoftware Development Director - Oracle Inc\n\n- Email me on Indeed: indeed.com/r/Ram-Edupuganti/3ecdecbcba549e21\n\n\u2022 Offering over 22 years of rich national & international experience in developing & deploying\nsoftware appplicatoins development, customized solutions across Oracle Fusion HCM Global\nCore HR, Global Payroll, Global Absences, Compensation and all Talent modules - Performance\nManagement, Goals Management, Career Development, Succession Management & Talent\nReview Meeting\n\u2022 Directed planning, strategy development and implementation & business solution delivery with\nrecent successful implantations at Schneider Electric, Macy's, Pike International, British Telcom.\n\u2022 Accomplished in relational data modeling, data warehouse design and implementation, object\noriented design and development, developing transactional & business analytics in CRM, SCM\nand HCM domains from client server to cloud SaaS.\n\u2022 Led large-scale business application architecture and design efforts; developed and maintained\nsolutions for various business functional areas; assisted in resolving integration and interface\nissues between various systems with focus on optimizing performance and scalability\n\u2022 Extensive experience in all aspects of project management including budgeting and cost\noptimization, risk assessments and control, technical feasibility studies, project scope definition,\nestimations & cost control & so on\n\u2022 Recruit, Develop and led high-performing teams with high motivation and enhanced cross-\nfunctional collaboration\n\nWORK EXPERIENCE\n\nSoftware Development Director\n\nOracle Inc -\n\nApril 2015 to Present\n\nOracle Inc -\n\nJanuary 1998 to Present\n\n98 with Oracle Inc.\nGrowth Path & Deputations:\n\nSenior Development Manager\n\nOracle Inc -\n\nDecember 2011 to March 2015\n\nSenior Development Manager\n\nOracle Inc -\n\nMarch 2010 to November 2011\n\nhttps://www.indeed.com/r/Ram-Edupuganti/3ecdecbcba549e21?isid=rex-download&ikw=download-top&co=IN\n\n\nPrincipal Applications Developer\n\nOracle Inc -\n\nSeptember 2004 to February 2010\n\nProject Lead\n\nOracle Inc -\n\nSeptember 1999 to August 2004\n\nSenior Software Developer\n\nOracle Inc -\n\nJanuary 1998 to August 1999\n\nR-Systems -  Sacramento, CA -\n\nJanuary 1997 to January 1998\n\nKey Result Areas:\n\u2022 Providing overall leadership to the entire project team, mapping clients' requirements,\ntransforming requirements into stipulations and providing them best solutions within the scope\nof project requirements\n\u2022 Creating and managing the estimates, project plan, project schedule, resource allocation and\nexpenses; monitoring and reporting on standards & performance targets\n\u2022 Ensuring the delivery of quality releases on schedules using continuous improvement\ninitiatives; adhering to quality norms throughout the project implementation\n\u2022 Working on service strategy, transition, operations, process improvement, process\nmanagement, team building, training, hiring and client relationship management\n\u2022 Identifying and implementing strategies for building team effectiveness by promoting a spirit\nof cooperation among the team members\n\u2022 Supporting continuous improvement by investigating alternatives and new technologies and\npresenting the same for architectural review\n\u2022 Liaising with stakeholders during the course of problem diagnoses, requirements gathering,\ndetailed level design, development, system test and production implementation to ensure that\noptimal resolutions are achieved\n\nKNOWLEDGE PURVIEW\n\u2022 Machine Learning Algorithms supervised, unsupervised, NLP, chat bot and deep learning with\nPython\n\u2022 Building transactional applications & analytical solutions in SaaS model\n\u2022 Oracle fusion role based data security set up and customization for complex security\nrequirements\n\u2022 Reporting Layer (Subject Areas) for fusion HCM cloud for all HCM modules\n\u2022 Data Warehousing design methodologies, star and snowflake schema designs, aggregations\n\u2022 OBIEE, Data Visualization Desktop, BICS, DVCS, Oracle Analytics Cloud (OAC), Tableau\n\u2022 Design and build dashboards, KPIs using Oracle BI platform and OTBI, Standard Reports using\nBI Publisher\n\u2022 Design and build mappings, knowledge modules, load plans using Oracle Data Integrator (ODI)\n\n\n\n\u2022 Relational data modeling, Object Oriented Modeling & Design, UML, SOAP\n\u2022 Oracle 10g, 11g, 12c\n\u2022 SQL, PL/SQL, HTML, XML, JAVA, JDK, J2EE, Oracle ADF, Oracle JDeveloper\n\u2022 Applications Development for Oracle CRM, Oracle ebusiness SCM,\n\u2022 Applications/database/SQL/Batch program performance tuning\n\u2022 UNIX, Windows, Linux\n\nR-Systems -\n\nApril 1996 to January 1998\n\nGrowth Path & Deputations:\n\nDepartment of Corrections -  Sacramento, CA -\n\nFebruary 1996 to January 1997\n\nSecretary of State\n\nSalem, Tamil Nadu -\n\nDecember 1995 to February 1996\n\nOracle Corporation -  Redwood Shores, CA -\n\nJanuary 1995 to December 1995\n\nEDUCATION\n\nM.S. in Computer Engineering\n\nTexas A&M University -  Kingsville, TX\n\n1994\n\nB.S. in Electronics & Communications Engineering\n\nNagarjuna University\n\n1992\n\nSKILLS\n\nSOFTWARE DEVELOPMENT (3 years), STRUCTURED SOFTWARE (3 years), BUSINESS\nINTELLIGENCE (1 year), ORACLE (1 year), ARCHITECTURE (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nCORE COMPETENCIES\n\nSoftware Development & Consulting\n\nOracle Fusion Applications Architecture and Functionality\n\n\n\nOracle Business Intelligence development and implementations\n\nTechnology Planning\n\nDelivery Management\n\nClient Engagements (Stakeholders/Business)\n\nContinuous Process Enhancement\n\nAgile/Scrum Methodologies\n\nProject Management", "labels": []}
{"id": 4, "text": "Ramesh HP\nCES ASSOCIATE CONSULTANT\n\nBangalore, Karnataka - Email me on Indeed: indeed.com/r/Ramesh-HP/95fc615713630c4e\n\n\u2022 4 years of experience in engineering Technology software sales and strategic sourcing in B2B\nplatform.\n\u2022 Proven track record of generating increased revenue by involving in professional sales\nstrategies.\n\u2022 Responsible for software installation, network configuration, application integration with\nexisting system and Technical support.\n\u2022 Effectively work with cross functional teams to deliver right solutions to client's requirements.\n\u2022 Complete involvement in client meetings with respect to requirement collection, suggesting\nsolutions and financial negotiations.\n\u2022 Good experience in account management, having a track record of generating repeated\nbusiness.\n\u2022 Responsible for report generation with respect qualified leads and expected commitments in\nclosing deals.\n\u2022 Worked on a multiple market sector, responsibility to manage sector wise market analysis and\ndrive business parallely.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nCES ASSOCIATE CONSULTANT\n\nSAP ARIBA -\n\nNovember 2016 to October 2017\n\n\u2022 Responsible for supplier management via Ariba Discovery Which is B2B consulting platform.\n\u2022 Strategic sourcing of supplier corresponding to the buyer's products & service based\ncommodities globally.\n\u2022 Effectively analyse and conduct commodity research on project description from the buyer's\npostings.\n\u2022 Effective handling of multiple projects and converting potential leads into revenue for Ariba.\n\u2022 Having track record of maintaining 100% revenue target by monthly & quarterly.\n\nSALES ENGINEER - CONCEPT TECHNOLOGY SOLUTION\n\n-\n\nJanuary 2014 to November 2016\n\n\u2022 Effective selling of CAD, CAM & Analysis Software's. Which has got multiple market sector.\n\u2022 Generating qualified leads by sourcing market, sector wise & implementing sales action plans.\n\u2022 Giving presentation about the company, products & service offers.\n\u2022 Identifying the client requirements, Plan for proposing a solution by integrating with internal\ntechnical team.\n\u2022 Provide value addition to the prospect by involving my superiors with client management team.\n\nhttps://www.indeed.com/r/Ramesh-HP/95fc615713630c4e?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Responsible to make negotiations and up closing the deals.\n\u2022 Provide technical support after sales and maintain healthy relationship with the prospect.\n\u2022 Responsible to achieve targets monthly, quarterly & annually.\n\nEDUCATION\n\nMCA in COMPUTER APPLICATION\n\nDayananda Sagar College of Engineering -  Bengaluru, Karnataka\n\nBACHELOR OF SCIENCE in Electronics\n\nGovt Science College -  Hassan, Karnataka\n\nSKILLS\n\nLead genearation, Customer Handling, cold calling, Negotiation, upselling, IT sales, outbound\ncalling, Technical Support, sales forcasting, Software sale, product demonstration, cross selling,\nInside Sales, Technical sales, MS office, software integration, Network Management\n\nADDITIONAL INFORMATION\n\n\u2022 Excellent Communication both verbal & written MS-Office\n\u2022 Sales Forecasting SAP Business Objective Tool\n\u2022 Strategic Prospecting Ariba Network Admin Tool\n\u2022 Product Knowledge Ariba B2B Cloud Platform\n\u2022 Social Networking\n\u2022 Negotiation\n\u2022 Customer Relationship Management\n\u2022 Technical Support", "labels": []}
{"id": 5, "text": "Ramya. P\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Ramya-P/00f125c7b9b95a35\n\n( 2 year Experience)\n\u2022 Working currently with Accenture services Pune as a Software Test Engineer with Client\nAccenture on British Telecom Open reach Project, using Agile Methodologies.\n\u2022 Experience in automating provision journey in QTP (UFT)\n\u2022 Good understanding of the project with deep domain knowledge of Telecom with respect to\nProvisioning and Assurance.\n\u2022 Expertise in functional, CST, Integration and E2E testing on CRM application for the BT\nAssurance journey with respect to different products.\n\u2022 Expertise in understanding of the new requirements and developing test cases for new features\nbeing implemented in the code base and executing them.\n\u2022 Good in understanding of the change with involvement in regular solution design discussions,\ntechnical changes discussions with Solution Designers and Stake holders accordingly.\n\u2022 Proficient with the SDLC, STLC and Defect life cycle.\n\u2022 Excellent communication skills with proficiency at grasping new technical concepts quickly and\nutilize the same in a productive manner.\n\u2022 Mentored new joiners on the Overall view of the project, Features and Functionality of Testing.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSoftware Engineer\n\nAccenture -  Hyderabad, Telangana -\n\nDecember 2015 to Present\n\nBT Openreach (British Telecom)\n\nBT Openreach is responsible for looking after the local network (connection from your property\nto local exchange) that makes the running of telecommunication services possible. BT helps to\nprovide lines for telephone connection. It has products like PSTN, Ethernet, NGA, and LLU. BT acts\nas an intermediate between customer and connection provider.\nBT Openreach is a software module which manages BT business right from the placement of the\norder, managing appointments, monitoring status to order completion.\n\nRole and Responsibilities:\n\n\u2022 Performing a Sanity test on the application and working on stabilizing the test environment for\nsmooth testing activities everyday SOP.\n\u2022 Assigning the task and updating the reports to management by collating the data of each\nindividual in the team.\n\u2022 Testing the application with respect to CST, Integration, Functional, E2E and Regression testing.\n\u2022 Writing of Test Cases and execution based on the inputs such as customer requirements/test\nplan/test approach/ test techniques/ test strategies given based on the functionalities using Agile\nMethodologies.\n\nhttps://www.indeed.com/r/Ramya-P/00f125c7b9b95a35?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Execution of test cases and bug reporting in Quality center\n\u2022 Coordinated with developers to get the bugs reproduced and resolved\n\u2022 Reviewing the release and build notes documents.\n\n\u2022 Carry out regression testing every time when changes are made to the code to fix defects.\n\u2022 Regular involvement in RCA calls for the defects raised on daily basis and attending Scrum calls\nfor updating the status of the stack present in a given timelines.\n\nLiberty global international (LGI):\n\nPresently working as a application support Engineer for LGI.\n\nRoles and Responsibilities:\n\nDaily monitoring servers heath check .\nSolving the incident using tools like nagious , splunk,graphana.\n\nAcheviments:\nReceived appreciation excuting high number of test cases with in the given time\nReceived appreciation from Client for finding show stopper defects.\nReceived appreciate for identifying P1 issue within the SLA\n\nEDUCATION\n\nBSc in Computer Science\n\nvasundhara womens\n\n2015\n\nnarayana jr.college\n\n2012\n\nSSC\n\npragathi high school\n\n2010\n\nSKILLS\n\nBlack Box (2 years), Black Box Testing (2 years), Database (Less than 1 year), java (Less than\n1 year), logging (Less than 1 year), SQL (Less than 1 year), Nagious (Less than 1 year), Splunk\n(Less than 1 year), Manual Testing (2 years), SDLC&STLC (2 years)\n\nADDITIONAL INFORMATION\n\nOperating Systems: Windows,\nLanguages: Core java\nTest Management Tool: HP Quality Center, HP ALM\n\n\n\nDatabase mySQL\nDefect Management Tools: AIT ( Accenture Intelligent Testing), QC\n\nProfessional Skills:\n\n\u2022 Proficient in overall Manual testing concepts.\n\u2022 Proficient in Functional E2E Testing, Black Box Testing, Regression Testing.\n\u2022 Good at analyzing the requirements and preparing assessments for the new requirements.\n\u2022 Expertise in Development of test cases for the new features being implemented using different\ntest design techniques. This included Analysis of the Feature Documents and developing a\nstrategy for testing the feature.\n\u2022 Good in defect logging and tracking activities till the closure of a defect using tools QC and AIT.", "labels": []}
{"id": 6, "text": "R Arunravi\nFunctional Consultant / WM Lead - SAP EWM\n\nBangalore, Karnataka - Email me on Indeed: indeed.com/r/R-Arunravi/0da1137537d8b159\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nFunctional Consultant / WM Lead\n\nSAP EWM -\n\nDecember 2015 to Present\n\nCATERPILLAR]\n\nSAP EWM WM Analyst\n\nCATERPILLAR -\n\nOctober 2013 to December 2015\n\nMagna Info Tech]\nProject 1: Implementation & Support of EWM\nProject 2: Johannesburg Distribution Center - WM/SD/MM Support\n\u2022 Involved in Business requirement Gathering for setting up a new warehouse. Understanding\nthe AS-IS Business Process of the current distribution facility by Interacting with the Key business\nusers.\n\u2022 Prepared Blue print of the new warehouse facility by understanding and Mapping the Business\nProcess to the SAP EWM module.\n\u2022 Involved in configuration and maintenance of goods issue processes, Storage type search\nstrategies for Put away and picking based on the Business design for receiving and delivery\nprocess.\n\u2022 Configured the Stock Removal Control Indicators, Storage type sequences, Storage Section\nIndicators, Bin Type Indicators and storage process steps.\n\u2022 Training the users for carrying out their warehouse activities and using the warehouse monitor\nto view outbound and inbound transaction results.\n\u2022 Involved in WM RF screen developments and configurations.\n\u2022 Identify and debug defects in BO report - 'TU Zero Weight' and Emergency Charge Waiving\nissue and Worked on Partial Invoicing change.\n\u2022 Worked on a CR to create unique pack with number for URG Orders, based on Dealer PO.\n\u2022 Set up new storage types that were extended as a part of Rearrangement process in JDC\nwarehouse.\n\u2022 Provide Post-Go Live Incident support and involved in both technical & functional changes.\n\u2022 Delivered 13 CRs for the year 2015 and 6 CRs till June 2016.\n\nWM Functional Consultant / Visual PLM Consultant / Lead Technical\n\nSAP EWM -\n\nhttps://www.indeed.com/r/R-Arunravi/0da1137537d8b159?isid=rex-download&ikw=download-top&co=IN\n\n\nFebruary 2011 to October 2013\n\nClientis S3G - India\n\nProject 1: Implementation of Kair IT EWM / PLM\nProject 2: Bauer Hockey Corp - WM Support\n\u2022 Collaborated with Warehouse internal team members to understand the Business process in\ninbound and outbound logistics in India and Dubai, Gap analysis and design & finalization of\nblueprints.\n\u2022 Involved in configuration of ERP-EWM integration - ALE setting, ERP settings and EWM settings.\n\u2022 Involved in configuration and maintenance of goods issue processes and participated in End\nuser training.\n\u2022 Developed and configured Merchandise Plan, Timeline Management as per client needs.\n\u2022 Integrated VPLM system with SAP R/3 system, mapped fields for use in Tech Pack report, BOM,\nand Production Cost details.\n\u2022 Prepared Functional / Technical specs and involved extensively in Unit Testing and Integration\nTesting.\n\u2022 Involved in Supporting WM module on both functional and technical issues.\n\nConsultant / ABAP Consultant\n\nSAP WM -\n\nOctober 2007 to February 2011\n\nVoletix IT Solutions - India\n\nProject 1: Momentive Project Implementation - ABAP Support\nProject 2: ECCI - WM Implementation\n\u2022 Developed Smart Forms for Transfer Order and Pay slips.\n\u2022 Updated VAT, Service Tax data via customization of Sales Order and Invoice SAP scripts.\n\u2022 Created reports for validation of Bill of Material, and others outlining billing details in ALV format.\n\u2022 Designed Smart Forms layout representing Invoices and Delivery Notes.\n\u2022 Uploaded vendor and customer data through call transaction method.\n\u2022 Configured the Storage Type Indicators, Bin Type Indicators, Bin structure and stock\ndetermination sequence.\n\u2022 Prepared Functional / Technical specifications and involved extensively in Unit Testing and\nIntegration Testing.\n\u2022 Involved in Supporting WM module on both functional and technical issues.\n\nEDUCATION\n\nMaster of Engineering in Computer and Communication\n\nAnna University\n\n2005 to 2007\n\nBachelor of Technology in Information Technology\n\nAnna University\n\n\n\n2001 to 2005\n\nADDITIONAL INFORMATION\n\nProfessional competencies\nBusiness /IT Consulting Configuration/ Support Requirements/ Blue print /Gap Analysis\nCoding/Debugging Change Management Functional/ Technical Spec Design\n\nComputer skills\n\n\u2022 SAP Knowledge: SAP EWM, SAP WM, SAP SD, SAP MM, SAP AFS, SAP PP, SAP XI\n\u2022 Computing languages: SQL, MS-Office, Open office, HTML, PHP", "labels": []}
{"id": 7, "text": "Ravi Shankar\nWorking as Escalation Engineer with Microsoft.\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Ravi-Shankar/befa180dc0449299\n\n\u2022 Working in Microsoft - EPS (SCS-MON) at Convergys India Private Ltd., Pune from 5th October,\n2015 till date.\n\u2022 Currently working as an Escalation Engineer with SCS-MON team.\n\u2022 Done B.E. from Padmshree Dr. D.Y.Patil Institute of Engineering and Technology, Pimpri, Pune\nUniversity.\n\nWilling to relocate to: Pune, Maharashtra - hyderbad, Telangana - Bangalore, Karnataka\n\nWORK EXPERIENCE\n\nEscalation Engineer\n\nMicrosoft\n\nWorking on escalated issues with System Centre Operations Manager, Service Manager and\nOrchestrator. Have trained batches for the same. Supported junior Engineers in resolving tough\nissues.\n\nHave worked as a Support Engineer\n\nMicrosoft -  Pune, Maharashtra -\n\nOctober 2015 to April 2017\n\nsince May 2017 as a Subject Matter Expert to Jan 2018 and as Escalation Engineer since Feb\n2018 at Convergys India Pvt. Ltd. Pune for MICROSOFT Enterprise Support Team (SCS-MON)\n\nRoles and responsibilities: -\n\u2022 Working in Microsoft Commercial Technical Support SCS-MON Team providing advanced\ntechnical support by handling escalated or complex customer issues.\n\u2022 Identify, investigate, research and provide resolution on Customer queries and problems related\nto System Center Products.\n\u2022 Provide subject matter expertise in area of assignment and serve as a resource to other support\npersonnel. Maintain high level of customer satisfaction always with an eye on productivity.\n\u2022 Maintain a high level of technical product knowledge in the specified software/hardware\nproducts and become knowledgeable in new products when deployed.\n\u2022 Have supported SCOM, SCSM and Orchestrator platform for Microsoft customers.\n\u2022 My work includes helping the Engineers with Technical issues on System Center platform\nsupport.\n\nCore Competencies: -\nSystem Center Operations Manager:\n\u2022 Installation, deployment and troubleshooting of System Center Operations Manager\n\u2022 Cross-domain and Cross platform monitoring.\n\u2022 Network devices and Web Application monitoring\n\nhttps://www.indeed.com/r/Ravi-Shankar/befa180dc0449299?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Custom reports / Issue with blank reports\n\u2022 Network Devices monitoring\n\u2022 Integrating and monitoring other System Center Products such as SCCM, SCDPM, SCVMM etc.\nwith SCOM\n\u2022 Notifications\n\nSystem Center Orchestrator:\n\u2022 Installation, deployment and troubleshooting of System Center Orchestrator\n\u2022 Deployment of various Microsoft and Non-Microsoft Integration Packs\n\u2022 Integration of SCSM-Orchestrator\n\u2022 Creating and Deploying Runbooks\n\u2022 Troubleshooting Runbook Server Performance Issues\n\nSystem Center Service Manager:\n\u2022 Installation, deployment and troubleshooting of System Center Service Manager\n\u2022 Configuring Connectors\n\u2022 Notifications\n\u2022 Portal deployment (SharePoint and HTML5)\n\u2022 Troubleshooting workflows\n\u2022 Troubleshooting Data-warehouse Jobs issues\n\nEDUCATION\n\nB.E.\n\nPune University\n\nSKILLS\n\nOPERATIONS MANAGER (1 year), SCOM (1 year), SYSTEM CENTER OPERATIONS MANAGER (1\nyear), Orchestrator SCSM\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\n\nOperating systems\n\u2022 Microsoft Windows Server 2012 (R2) /2008 (R2) /2003 (R2)\n\u2022 Microsoft Windows Win8.1/Win8/Win7/Vista/XP\n\u2022 Linux - Various Flavors and Distributions (Red Hat/Fedora /CentOS/Debian)\n\nServer Applications\n\u2022 Microsoft System Center Operations Manager (SCOM) 2016 /2012 [\u2026] R2\n\u2022 Microsoft System Center Service Manager (SCSM) 2016 /2012 R2/2012\n\u2022 Microsoft System Center Orchestrator 2016 /2012 R2/2012\n\u2022 Microsoft SQL [\u2026] R2\n\nVirtual Appliances\n\u2022 Microsoft Hyper-V\n\n\n\n\u2022 VMware Workstation / VirtualBox\n\nOther Applications\n\u2022 Microsoft Office [\u2026]\n\u2022 Trained and worked on Forefront Identity Manager, Microsoft Identity Manager, Forefront Threat\nManagement Gateway and Unified Access Gateway for Windows security.\n\u2022 Good knowledge of basic Networking concepts\n\u2022 Good knowledge of basics of Microsoft Active Directory\n\u2022 Basic Knowledge of Windows Failover Clusters, Microsoft Exchange and IIS.", "labels": []}
{"id": 8, "text": "Ravi Shivgond\nBidar, Karnataka - Email me on Indeed: indeed.com/r/Ravi-Shivgond/4018c67548312089\n\nTo associate with an organization that promises a creative and challenging career in progressive\nEnvironment to enhance my knowledge, skills to be a part of team that excels in work towards\nthe growth of the organization.\n\nWORK EXPERIENCE\n\nTechnical Support Engineer\n\nSAP -  Bengaluru, Karnataka -\n\nNovember 2017 to February 2018\n\nI completed PLC Automation course that's way I will rejoin that job\n\nEDUCATION\n\nGovt. High school Yernalli\n\nB.E. in Electrical\n\nKarnataka State -  Bidar, Karnataka\n\nSKILLS\n\nAC (Less than 1 year), Allen Bradley (Less than 1 year), Engineer (Less than 1 year), FANUC\n(Less than 1 year), GE FANUC (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTECHNICAL QUALIFICATION:\n\n\u27a2 Obtained Post graduate diploma in Industrial Plant Automation Engineer from Prolific Systems\nand Automation Bangalore. With hands on practical experience in Industrial Automation Tools\nspecializing in PLC, SCADA and VFD.\n\n\u27a2 Attended a 3-day National level seminar on \"Mat Lab and its Applications\" organized by E&EE\ndepartment of GNDEC Bidar.\n\nACADEMIC PROJECT:\n\nSpeed synchronization of multiple DC motors in industries using wireless RF.\n\n\u27a2 Aim: To find Speed synchronization of multiple DC motors in industries using wireless RF.\n\nhttps://www.indeed.com/r/Ravi-Shivgond/4018c67548312089?isid=rex-download&ikw=download-top&co=IN\n\n\n\u27a2 Main Components Used: LCD Display, Diodes, Potentiometer, Rectifier, transformer, DC motor,\nDC motor drive, [\u2026] microcontroller, RF transistor, RF receiver, IR sensor, etc.\n\n\u27a2 TECHNICAL SKILLS: Post Graduate Diploma in Industrial Automation (PGDIA) Course from M/s.\nProlific Systems and Technologies Pvt. Ltd., Bangalore which includes:\n\nAllen Bradley Micro Logix1000, SLC 5 / 03 RS Logix 500\nSiemens S7-300 SIMATIC MANAGER-5.4\nGE FANUC Versamax Micro, Proficy Machine\nDelta WPL Soft 2.42\nMitsubishi FX3U 16M GX-Developer\nOmron Sysmac - CP1E CX-Programmer\nDCS AC-800 F Freelance Industrial IT\nField Basics Of Fi, Interface With Live Plant\nInstrumentation Setup With SLC 5 / 03 Controller Interfaced With In touch SCADA", "labels": []}
{"id": 9, "text": "Rohit Bijlani\nJAVA Developer/Senior Systems Engineer - INFOSYS LIMITED\n\nItarsi, Madhya Pradesh - Email me on Indeed: indeed.com/r/Rohit-Bijlani/06ecf59ddac448c7\n\nA Dynamic, Hardworking, Focused and Result oriented person with 2 years 10 months\nof experience in development using Java Technology and other cutting edge technologies.\nExperience in design and development of enterprise applications using agile methodology. Rich\nexperience in build and deployment process.\n\nWilling to relocate to: Pune, Maharashtra\n\nWORK EXPERIENCE\n\nJAVA Developer/Senior Systems Engineer\n\nINFOSYS LIMITED -  Pune, Maharashtra -\n\nFebruary 2016 to Present\n\nProject Idea: The Aetna Quoting Center (AQC) comes under the Aetna Group in Health Industry\nand its HeadOffice is in Hartford. AQC is a Java enterprise Web based application in Sales Rating\nand Quoting (SRQ) domain. AQC is developed as a single Rating/Quoting engine for Middle and\nNational Market Customers for new business offering both Traditional and Health Maintenance\nOrganization Products.\n\nAQC is used by the Aetna Sales Representatives and Underwriters for creation of New Business\nQuotes.A Quote typically comprised of Customer, Member Information along with the Product/\nBenefits being offered and the corresponding Premium to be paid for the HealthCare Plan.\n\nResponsibilities:\n\u2713 Analysing business and system requirements and creating design patterns for applications.\n\u2713 Presenting the ideas to client for application and implementing it in the application.\n\u2713 Clear understanding of Requirement gathering and designing phase. Created Detailed Level\ndesign (DLD) document of all subsequent releases till date.\n\u2713 Experience in working over deployment process, deploying code over different servers,\nmaintaining deployment documents and handling different task in subsequent releases.\n\u2713 Excellent hands-on in performing request analysis, finding defects and giving code fixes in\nminimal amount of time.\n\u2713 Resolving Client Issue\n\nOther Responsibilities:\n\u2713 Sharing knowledge with the team by sharing KM tips/quizzes/sessions.\n\u2713 Mentoring 2-3 team members and providing technical guidance in AQC project\n\nJAVA Intern\n\nINFOSYS LIMITED -  Mysore, Karnataka -\n\nJune 2015 to November 2015\n\nhttps://www.indeed.com/r/Rohit-Bijlani/06ecf59ddac448c7?isid=rex-download&ikw=download-top&co=IN\n\n\nUndergone 6 months of internship with Infosys, which includes theoretical as well as hands on\nexperience in Core Java, Hibernate and SQL.\n- Successfully completed Infosys's Mysore Internship.\n\nEDUCATION\n\nBachelor of Engineering in Mechanical Eng\n\nITM University Gwalior -  Gwalior, Madhya Pradesh\n\n2015\n\nSKILLS\n\nJ2Ee, Sql server, Core Java, Spring mvc, Hibernate, Spring\n\nADDITIONAL INFORMATION\n\nSKILLS Languages: JAVA.\nTechnologies/Frmwks: Hibernate, Spring, Spring MVC.\nIDE/Tools: Eclipse, Rational Application Development (RAD)\nDatabase: SQL Server 2008\nMiscellaneous: Xml.\nOS: Windows", "labels": []}
{"id": 10, "text": "Roshan Sinha\nApplication Developer - SAP ABAP\n\nKolkata, West Bengal - Email me on Indeed: indeed.com/r/Roshan-Sinha/ab398efcd288724f\n\nWilling to relocate to: Kolkata, West Bengal - Bangalore City, Karnataka - NCR, Delhi\n\nWORK EXPERIENCE\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nMarch 2011 to April 2018\n\nJoined as Associate System Engineer, worked as Application Developer for maintenance and\ndevelopment of SAP systems for clients from Distribution, Chemicals and Petrochemicals and\nIndustrial sectors.\n\nApplication Developer\n\nIBM India Pvt. Ltd. -\n\nApril 2011 to March 2018\n\nApplication Development and Maintainence services on SAP using ABAP as programming\nlanguage.\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nJune 2017 to November 2017\n\nNon-ALE Outbound File Interfaces for Forward and Reverse Logistics Application from SAP to EMM\nsystem Return, Replacement and Dispatched IMEI and SIM Serial Data Interfaces.\n\nApplication Developer\n\nSAP ABAP -\n\nJuly 2016 to November 2017\n\nIMSP Applications built on OOPS-ABAP, BAPI, FM.\nSmartform for Credit/Invoice Note\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nMay 2017 to June 2017\n\nhttps://www.indeed.com/r/Roshan-Sinha/ab398efcd288724f?isid=rex-download&ikw=download-top&co=IN\n\n\nfrom Repacking - Batch creation, Create Inbound Delivery, Reprint Handling Unit, Post Goods\nMovement, Goods Receipt and Goods Issue.\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nJanuary 2017 to May 2017\n\nAudit and Tax applications built on reports and enhancements in ABAP, Purchase TAX Reports,\nC/F Forms.\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nJuly 2013 to July 2016\n\nUpload Purchase Order Header Data.\nDIM Monthly Price deviation table.\nEHS Module: -\nLACK1 report, Where-Used Report and CK2/CK3 Interface\n\nApplication Developer\n\nSAP ABAP -  Bengaluru, Karnataka -\n\nFebruary 2013 to June 2013\n\nDesign and Implement Technical Strategies for the Team Deliverable along with Leads.\n\nUploading of Internal Orders (Cost Objects) using Batch Input Recording.\n\nUploading of Direct Purchase Orders using BAPI.\n\nImplementation of User Exit for overriding standard billing document number ranges.\n\nImplementation of Pricing List Category Quote Order Header in CRM and SD.\n\nEnhancement for Filter products by material type in GTS.\n\nDevelop adding custom fields and field logic in CRM WEBUI\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nJanuary 2013 to February 2013\n\nDeveloped Outbound Interface Report from SAP to CLM, spool generation and Enhancement of\nScreen Elements using Dynamic Search Help.\n\nApplication Developer\n\n\n\nSAP ABAP -  Kolkata, West Bengal -\n\nJanuary 2012 to December 2012\n\nWorked on Remedy Tickets for issues with following applications: -\nOvertime Request on BP People Portal enhancement from Time Attendance domain in HR.\nTAX certificate Administration from Payroll.\nWorked on Custom Infotype for TAX Form.\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nAugust 2011 to December 2011\n\nEnhancement of standard and custom transaction, interfaces and e-forms\n\nEDUCATION\n\nB.Tech in Technology\n\nAnna University Chennai -  Chennai, Tamil Nadu\n\n2010\n\nSKILLS\n\nSap Abap\n\nADDITIONAL INFORMATION\n\nSKILLS\n\nSAP ABAP\nOOPS-ABAP", "labels": []}
{"id": 11, "text": "Sai Dhir\n- Email me on Indeed: indeed.com/r/Sai-Dhir/e6ed06ed081f04cf\n\nWORK EXPERIENCE\n\nSasken Technologies Pvt. Ltd -  Pune, Maharashtra -\n\nJanuary 2017 to Present\n\nORACLE -\n\nJanuary 2011 to Present\n\nSTP is basically a router that realys ss7 messages through various signally points. In project all\nSTPs were replaced by ORACLE STP due to its advanced features, high end support, flexibility.\nThe STP is connected to adjacent SEPs and STPs via signaling links. Based on the address fields\nof the SS7 messages, the STP routes the messages to the appropriate outgoing signaling link.\n\nClient: ORACLE\nTeam Size: 4\nRole: fetching data, analyzing, monitoring, troubleshooting\nTechnologies: filezilla, putty\nMajor Development\n\u27a2 Currently working on External Browser Integration for the Payment Gateway\n\u27a2 Multiple Shipping methods Inside Order Invoice\n\nORACLE -  Gurgaon, Haryana -\n\nOctober 2016 to January 2017\n\nKarizma Order Manager & Karizma Order System\n\nORACLE -\n\nMarch 2011 to October 2011\n\nStamp Duty means a tax payable on certain legal documents specified by statute; the duty may\nbe fixed or ad valorem meaning that the tax paid as a stamp duty may be a fixed amount or\nan amount which varies based on the value of the products, services or property on which it is\nlevied. It is basically a kind of tax paid on any transaction based on exchange of documents or\nexecution of instruments.\n\nClient: Max Life Insurance\nTeam Size: 4\nRole: Business Analyst, Testing and Bug Fixing.\nTechnologies: Group Asia\nMajor Development\n\u27a2 Reading of the new requirements and have a thorough knowledge regarding the functionality\nand generating test case regarding it.\n\nhttps://www.indeed.com/r/Sai-Dhir/e6ed06ed081f04cf?isid=rex-download&ikw=download-top&co=IN\n\n\nPMJJ BY: PMJJBY (Pradhan Mantri Jeevan Jyoti Bima Yojana) is a one year Life insurance scheme,\nauto renewed every year, offering coverage for death due to any reason. The PMJJBY scheme is\navailable to anyone between 18 and 50 years of age and with a CSB Savings bank account. The\nmain administrator within the scheme could be life insurance corporation in addition to hardly\nany other insurance providers who will be able to give similar benefits under the scheme\nTeam Size: 4\nRole: business analyst, testing\nTechnologies: group asia\n\nEDUCATION\n\nClient-Server Architecture\n\nCDAC -  Mohali, Punjab\n\nJanuary 2017 to Present\n\nBachelor of Engineering in Engineering\n\nPunjab Technical University (PTU) Jalhandar\n\n2012 to 2016\n\nB.E\n\nPunjab Technical University\n\n2012 to 2016\n\nAndroid\n\nCDAC Mohali -  Mohali, Punjab\n\nSKILLS\n\nCHANGE MANAGEMENT (Less than 1 year), Configuration Management (Less than 1 year), Git\n(Less than 1 year), Incident Management (Less than 1 year), Linux (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nOperating Systems Windows 7, ubuntu Linux\nConfiguration Management Git, svn\nIncident Management IBM i series 400\n\nDomain insurance\n\nPlatform - Windows\n\nFramework-Group Asia\n\nDomian Telecom\n\n\n\nDomain telecom\n\nPlatform-windows\n\nFramework-filezilla, putty", "labels": []}
{"id": 12, "text": "Sai Patha\nMule ESB Integration Developer - Cisco Systems\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Sai-Patha/981ba615ab108e29\n\n\u2022 6+ years of professional experience in end-to-end designing, developing and implementation\nof\nsoftware solutions in the areas of Middleware Integration and J2EE based applications.\n\u2022 Expertise in the areas of Core Java, Servlet 2.3, JSP, Web Services, MESB, and OSB.\n\u2022 Expertise in PL SQL programming and Oracle Apps (Oracle Order management)\n\u2022 Having 2.5+ years of experience in Mule and expert in Mule ESB development (3.7v & 3.8v),\nMule\nESB administration and Mule API management (API GW 1.x, 2.x, 3.x) CloudHub.\n\u2022 Experience in building Mule ESB & API management platform for organizations\n\u2022 Experience in performance tuning, testing, and benchmarking the platform for the\norganization.\n\u2022 Expert in building middleware systems using Message Routing, Content Enrichment, Cache\nMechanism, Message Filtering, Message Transformation, Message sequencing, Batch message\nprocessing, Error handling and reconciliation mechanisms.\n\u2022 Expertise in designing and implementing multi-tiered application with high performance using\nJ2EE standards.\n\u2022 Good understanding of API management systems - Mulesoft and RAML.\n\u2022 Experience in compiling proof of concepts and presenting to customers.\n\u2022 Deep knowledge of all phases of software engineering involving analysis, design and\nimplementation.\n\u2022 Hands on experience in load testing and performance testing and setting up the environment.\n\u2022 Very strong debugging skills.\n\u2022 Expertise in implementing different J2EE design patterns and Java Multi-Threading.\n\u2022 Hands on experience in creating Splunk dashboard, App for production proactive monitoring\nand\nfor reporting.\n\u2022 Good understanding in Web services security.\n\u2022 Extensively worked on servers Apache Tomcat, JBoss, Web logic and WebSphere.\n\u2022 Familiar with reviewing Functional Requirements and writing Technical Specifications.\n\u2022 Extensive Expertise in using Oracle 11i.\n\u2022 Broad knowledge of version control systems, build scripts and logging mechanisms.\n\u2022 Analysis, design and development of Applications based on J2EE & allied technologies using\nAgile\nmethodology.\n\u2022 Implementing high-end performance REST services for Middleware using Core Java.\n\u2022 Deploying Builds to DEV/TEST/PROD environment using Kintana, Udeploy & URelease tools.\n\u2022 Implementation of modules using Core Java APIs, Java collection, XML technologies and\nintegrating the modules.\n\u2022 Responsible for converting the understood business into Technical Specification document.\n\u2022 Taking KT sessions from client on Application functionality and discuss with Business Analysts to\nintegrate new functionalities with existing systems.\n\u2022 Utilize Oracle as back-end database and TOAD for querying.\n\u2022 Utilize Log4j as logging mechanism and developed wrapper class to configure the logs.\n\u2022 Utilize CVS, SVN, and GIT as Version control tool.\n\nhttps://www.indeed.com/r/Sai-Patha/981ba615ab108e29?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Used Maven build scripts to create and deploy the release builds.\n\u2022 Prepare the Functional and Technical Design documents.\n\u2022 Module implementation and customization based on the Change Requests.\n\u2022 Development and support for different releases before and after implementation of launch.\n\u2022 Review the fellow developers' code as an exercise of internal code review.\n\u2022 Carry out Configuration Management activities for projects.\n\u2022 Carry out Weekly Status reporting activities such as MOM updates and health sheet generation.\n\u2022 Excellent communication and interpersonal skills. Involved in client interactions for scoping,\neffort\nestimates and status reporting.\n\nWORK EXPERIENCE\n\nMule ESB Integration Developer\n\nCisco Systems -  San Jose, CA -\n\nApril 2017 to Present\n\nProject: Extended Enterprise B2B Transformation\n\nResponsibilities\n\u2022 Followed agile methodology and Scrum.\n\u2022 Involved in application design and participated in technical meetings, Effort estimations,\nbacklog\ngrooming, I&A etc.\n\u2022 Involved in analyzing, developing, troubleshooting, debugging, and optimizing ESB application.\n\u2022 Involved in documenting and presenting designed technical solutions.\n\u2022 Extensively used Anypoint studio to develop and design the business process.\n\u2022 Implemented complex transformation Logics using MEL\n\u2022 Building RESTful Web Services with Anypoint Platform for APIs\n\u2022 Involved in data transformation and mapping using data weave\n\u2022 Tested the business process in test mode for debugging\n\u2022 Build and deployed using Anypoint studio, maven.\n\u2022 Participating i n meeting and on-calls\n\u2022 Code reviews and independent unit testing for components\n\u2022 Manage code release deployment into development, SIT, OAT and production\n\u2022 Error handling is properly done in all the business processes\nTechnologies: Mule Server 3.7 EE, Anypoint studio 5.4, cloud hub, Maven, core java, GIT, RAML,\nAPIKit, SOAP 01, Postman, Agile, Jenkins.\n\nMule Soft Team Lead\n\nCisco Systems -  San Jose, CA -\n\nJune 2016 to March 2017\n\nProject:FSMS\n\nResponsibilities:\n\u2022 Followed agile methodology and Scrum.\n\n\n\n\u2022 Involved in application design and participated in technical meetings, Effort estimations,\nbacklog\ngrooming, I&A etc.\n\u2022 Gather requirements and planning on integration of oracle data base with cloud applications\nusing\nMule ESB.\n\u2022 Tightly integrated applications using MULE ESB.\n\u2022 Involved in implementing ESB flows, Proxies, logging and exception handling.\n\u2022 Extensively used Mule ESB components like File Transport, SMTP Transport, FTP/SFTP\nTransport, JDBC Connector, JMS and Transaction Manager.\n\u2022 Used TOAD for internal data storage and retrieval.\n\u2022 Involved in setting up Connection pooling and used JMS for Asynchronous messaging.\n\u2022 Setting up Mule ESB for the development environment.\n\u2022 Developed application using Mule ESB and deployed the services in dev, test and prod\nenvironments. And also done with Unit testing using Test Utility.\n\u2022 Migrated Mule ESB 3.7.0 apps to Mule ESB 3.8.4\n\u2022 Applied OAUTH authentication policy for API proxies\n\u2022 Have integrated web services including SOAP as well as REST using Mule ESB.\n\u2022 QA, UAT & Production issues investigation and supporting business users.\n\nMule Soft Team Lead\n\nCisco Systems -  San Jose, CA -\n\nAugust 2015 to May 2016\n\nProject: Bay Bridge RMA Services\n\n\u2022 Design and implement the Mule ESB platform for Cisco.\n\u2022 Design and implement the RESTful WS using RAML to interact with AI system and storing in\nC3 Database.\n\u2022 Implemented the security for the SOAP and REST Web services using OAUTH and Basic\nAuthentication.\n\u2022 Designed and developed the core modules, which pulls service request details from CSOne\nSystem.\n\u2022 Design and developed common modules like Audit Logging, which can be used as a common\nmodule and shared resources for all the applications.\n\u2022 Designed the Exception handling for all the apps on Mule platform.\n\u2022 Designed the Domain to share the resource like HTTP, HTTPS & DB connector references.\n\u2022 Created flows for basic authentication and caching the token for OAUTH.\n\u2022 Have carried out performance testing for the ESB flows for memory leakage and for fine-tuning.\n\u2022 Worked with Mule team on some of the issue with performance on DB connector.\n\u2022 Interacted with dependent teams (CSONE and PEGA) and came up with the design on the\nimplementation of the flows and architecture and design of services.\n\u2022 Developed required back Java components.\n\u2022 Reported and worked on DB connector Connection pool issues to Mulesoft.\n\u2022 Reported and worked on MMC deployment issues with Mulesoft.\nTechnologies: Java 1.8, Oracle11i Web Services, Mule ESB, Mule API Manager, XML, JSON,\nAnypoint Studio, Maven, GIT, SVN, ESB Servers.\n\nTechnology Analyst\n\n\n\nCisco Systems -  San Jose, CA -\n\nDecember 2013 to July 2015\n\nProject: OMLSS\n\n\u2022 Understanding of the complete architecture of the system including boundary systems.\n\u2022 Understand the client and project requirements by studying the existing documentation and\nseeking clarifications, if any, to participate efficiently in the Development and Testing phases of\nthe project.\n\u2022 Create program specifications, unit test plans for software programs by studying functional\nand non-functional requirements, the application architecture document, and converting the\nassigned\nfunctionalities into pseudo code/algorithms/test cases.\n\u2022 Develop code using knowledge of relevant technology as per design specifications and\ndocument\nartifacts such as unit test scripts, etc. independently and support peers in identifying code defects\nand ensuring that the output is as per the given specifications and SLAs.\n\u2022 Perform testing - self and independent (Functional, Integration, System) - as per defined\nprocesses and guidelines to ensure accurate program output; identify and resolve defects, if any.\n\u2022 Work on 'Go Live' activities as per the Implementation plan and manage any issues related to\nfunctionalities, user interface, performance, etc. that may arise.\n\u2022 Respond to the issues assigned, conduct analysis of the issues assigned, identify and evaluate\ndifferent workarounds/ solution alternatives, implement the most optimal solution, support other\nteam members on issue resolution in areas of expertise as required, manage stakeholder\ncommunication and close the issues assigned in order to ensure support availability as per\nagreed\nSLAs.\n\u2022 Understand application architecture document and seek inputs from the architecture / design\nteam to understand the overall architecture IN ORDER TO provide deliverables that are in line\nwith architectural requirements.\n\nTechnologies: Java 1.5, Web services, Oracle 11i, XML, Eclipse, HP Quality Center, SVN, Jenkins,\nuDeploy and uRelease.\n\nSr.Software Engineer\n\nCisco Systems -  San Jose, CA -\n\nJune 2013 to November 2013\n\nProject: SPED Integration\n\nResponsibilities:\n\u2022 Responsible for development of Oracle Interfaces and Mapping data as per requirements of\nCisco\nBrazil.\n\u2022 Involved in the development of PL/SQL queries to fetch data from the oracle and insertion of\nData into Synchro Open Interface tables. This is a very critical data reporting as the data is to be\nreported to the Government of Brazil by Cisco as per the Legal Procedures. He was involved in the\n\n\n\nDevelopment of Packages, Concurrent Programs and many other Custom functionalities as per\nthe Requirements.\n\u2022 Experience in Cisco Quality Control process and Migration of Code into Different Environments.\n\u2022 Implemented the PL/SQL based on the requirements of AP and RI modules.\n\nTechnologies: PL/SQL, Oracle Applications (Financials), PVCS, Kintana, Toad.\n\nSoftware Engineer\n\nCisco Systems -  San Jose, CA -\n\nApril 2012 to May 2013\n\nProject: IT Creative Solutions (ITCS)\n\nResponsibilities:\n\u2022 Understand the business process and build interactive online dashboards based on client\nrequirement.\n\u2022 Do the data modelling to hold the current data, to sustain future needs and drilldowns in the\ndashboards.\n\u2022 Use dashboard building tools Xcelsius, Tableau to build interactive dashboards.\n\u2022 Develop business layer to perform all the calculations used in dashboard.\n\u2022 Do client interaction and communication to get required inputs.\n\u2022 Participated in Client Demos and meetings.\n\nTechnologies: SQL Server, Tableau, Xcelsius.\n\nSoftware Engineer\n\nArrow Electronics Inc -\n\nAugust 2011 to March 2012\n\nProject: Arrow Unity (Sales Work Bench)\n\nResponsibilities:\n\u2022 Requirements gathering, designing, development and testing.\n\u2022 Ownership of the deliverables.\n\u2022 Involved in Post Production support for SWB application.\n\u2022 Coding and testing for enhancements.\n\u2022 Wrote Oracle PL/SQL Stored procedures, triggers, and views for backend database access.\n\u2022 UAT Testing Support.\n\nTechnologies: Unix, ExtJs, Pl/Sql\n\nEDUCATION\n\nBachelor of Technology in Technology\n\nAmrita School of Engineering\n\n2007 to 2011\n\n\n\nSKILLS\n\nORACLE (3 years), JAVA (3 years), SOAP (2 years), Subversion (2 years), SVN (2 years)\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS:\n\nLanguages Java, Java Script, PL/SQL, SQL SERVER, HTML, XML,\nXSLT\nApplication Servers J Boss, Apache Tomcat, Web logic, IBM web sphere\n\nDatabases Oracle, SQL SERVER\n\nDevelopment Tools MVC, Singleton, Session Facade, DTO, DAO,\n\nService Locator\n\nSOA Restful Web Service, Soap web service, JAX-RS, XML, JSON, WS\nSecurity, Mule ESB\n\nIDE's/TOOLS Eclipse, Mule Anypoint Studio\n\nJava/J2EETechnologies Java, Servlets, JSP, JDBC, EJB, JMS\n\nProtocols HTTP, FTP, TCP/IP\n\nVersion Control tools CVS, SVN, GIT\n\nBuild Tool Ant, Maven\n\nBug Tracking Tools HP Quality Center, Rally\n\nFrameworks Spring, Hibernate, Struts, Spring MVC, Micro Services, EJB, JMS\n\nOperating Systems Windows, UNIX, LINUX", "labels": []}
{"id": 13, "text": "Sai Vivek Venkatraman\nDecisive, Data driven and results-oriented professional offering\n13 years of experience in Infosys Limited handling and managing\nInformation Technology projects in Telecom domain with the last 3+\nyears focused on Project Management.\n\nChennai, Tamil Nadu - Email me on Indeed: indeed.com/r/Sai-Vivek-Venkatraman/\na009f49bfe728ad1\n\nfor excellence in project delivery (2015 - 2016)\n\nOptimistic Project Manager with a\ntotal experience of 13 years, TECHNOLOGY LEAD / ANALYST\naccomplished in prioritizing and INFOSYS LIMITED, INDIA & USA\ndelivering projects with competence. December 2008 - January 2015\nData driven decision maker, creative \u2022 Key responsibilities included: Requirements Gathering\nand elucidation,\nproblem solver and a resilient Estimation, Defect Management & Warranty Support, Team /\nResource\nnegotiator with a remarkable Management, On-time Escalation, Status reporting and Client\nunderstanding of business goals and engagement\noperational methodologies. \u2022 Spear-headed the effort of making an entire suite of Consumer\napplications Web Accessibility complaint based on a WCAG 2.0 and\nWAI-ARIA guidelines for one of the World's largest Telecom service\n\nWilling to relocate to: Pune, Maharashtra - Bengaluru, Karnataka - Chennai, Tamil Nadu\n\nWORK EXPERIENCE\n\nProject Manager / Technology Lead\n\nInfosys Limited -\n\nFebruary 2015 to Present\n\n\u2022 Key responsibilities included: Project Scoping, Estimation, Resource Planning, Scheduling,\nQuality Planning & Management, Risk Planning & Mitigation, Defect Management & Warranty\nSupport, Team / Resource Management, Continuous evaluation and providing feedback through\nappraisals, On-time Escalation, Status reporting and Client engagement.\n\u2022 An ardent believer and practitioner of Data driven decision making; skilled in Business Analytics\nand Intelligence encompassing Statistical\nconcepts, Data Analysis, Data Visualization and Presentation, Predictive Modeling, Artificial\nIntelligence and Machine Learning algorithms and basics of web scraping - Text/Sentiment\nAnalytics.\n\u2022 Adept in effectively contributing and handling all Agile ceremonies, have extensively worked\non Waterfall model based projects as well.\n\u2022 Proposed and Implemented Project and Process level improvement techniques which resulted\nin mitigating the risk and bringing back project\ndelivery on-track.\n\u2022 Independently managed and mentored a dynamic 30 member team.\n\nhttps://www.indeed.com/r/Sai-Vivek-Venkatraman/a009f49bfe728ad1?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Sai-Vivek-Venkatraman/a009f49bfe728ad1?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Winner of the Most Valuable Player Award in large engagement category\n\nProject Manager / Technology Lead\n\nInfosys Limited -\n\nFebruary 2015 to Present\n\n- Key responsibilities included: Project Scoping, Estimation, Resource Planning, Scheduling,\nQuality Planning & Management, Risk Planning & Mitigation, Defect Management & Warranty\nSupport, Team / Resource Management, Continuous evaluation and providing feedback through\nappraisals, On-time Escalation, Status reporting and Client engagement.\n- An ardent believer and practitioner of Data driven decision making; skilled in Business\nAnalytics and Intelligence encompassing Statistical concepts, Data Analysis, Data Visualization\nand Presentation, Predictive Modeling, Artificial Intelligence and Machine Learning algorithms\nand basics of web scraping - Text/Sentiment Analytics.\n- Adept in effectively contributing and handling all Agile ceremonies, have extensively worked\non Waterfall model based projects as well.\n- Proposed and Implemented Project and Process level improvement techniques which resulted\nin mitigating the risk and bringing back project delivery on-track.\n- Independently managed and mentored a dynamic 30 member team.\n- Winner of the Most Valuable Player Award in large engagement category for excellence in project\ndelivery (2015 \u2013 2016).\n\nTechnology Lead / Analyst\n\nInfosys Limited\n\n\u2022 Key responsibilities included: Requirements Gathering and elucidation, Estimation, Defect\nManagement & Warranty Support, Team / Resource Management, On-time Escalation, Status\nreporting and Client engagement\n\u2022 Spear-headed the effort of making an entire suite of Consumer applications Web Accessibility\ncomplaint based on a WCAG 2.0 and WAI-ARIA guidelines for one of the World\u2019s largest Telecom\nservice provider\n\u2022 Working with diverse clients, across different cities in US resulted in enhancing customer\nrelationship.\n\u2022 Exceptional client interfacing and engagement skills resulted in significant project expansion\nwhich included addition of two new applications to the suite of applications that we managed\n\u2022 Winner of the Best Team Award in mid-size project category for excellence in Project execution\n(2014 \u2013 2015)\n\nEDUCATION\n\nPost Graduate Program in Business Analytics & Business Intelligence in\nBUSINESS\n\nGreat Lakes Institute Of Management -  Chennai, Tamil Nadu\n\n2017 to 2018\n\nBACHELOR OF ENGINEERING in ELECTRONICS & COMMUNICATION\nENGINEERING\n\n\n\nAdhiparasakthi Engineering College (Anna University)\n\n2001 to 2005\n\nIBM Jazz\n\nQuality Center (ALM)", "labels": []}
{"id": 14, "text": "Sameer Kujur\nOrrisha - Email me on Indeed: indeed.com/r/Sameer-Kujur/0771f65bfa7aff96\n\nWORK EXPERIENCE\n\nApp develop\n\nMicrosoft -\n\nAugust 2016 to Present\n\nEDUCATION\n\nElectrical engineering\n\nVSSUT,burla\n\nSKILLS\n\nApplication Development, Software Testing\n\nhttps://www.indeed.com/r/Sameer-Kujur/0771f65bfa7aff96?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 15, "text": "Samyuktha Shivakumar\nLooking for strategic opportunities in the field of marketing\n\nBangalore, Karnataka - Email me on Indeed: indeed.com/r/Samyuktha-Shivakumar/\ncabce09fe942cb85\n\nWORK EXPERIENCE\n\nMarketing Operations Specialist & Product Marketer\n\nThoughtWorks -  Bengaluru, Karnataka -\n\nAugust 2012 to June 2016\n\nProduct Marketer - GoCD, ThoughtWorks Studios\n* Responsible for shaping the product marketing strategy of GoCD, an open source continuous\ndelivery product supported by ThoughtWorks\n* Worked cross-functionally with product research and development, design, customer operations\nand PR to deliver integrated go-to-market strategies for new app releases and product launches\n* Led research initiatives analyzing behavioural metrics, driving and monitoring member insights,\nand owning competitive insights by using tools like Google Analytics\n* Project headed the revamp of the GoCD website by working in tandem with the design and\ndevelopment team\n* Established a consistent process to handle the launch of new product versions and ensured it\nworked on a recurring basis\n* Worked with the development and management team to publish relevant content to the open\nsource community on a regular basis\n* Built a strong digital marketing strategy and leveraged the use of several tools to stay on top\nthe product's online presence and reach:\n\u25e6 BuzzSumo, Hootsuite, and Tweet Deck for social media marketing\n\u25e6 Google Adwords for SEO\n\u25e6 Buzzstream, Medium, Wistia and Quora for content marketing\n\nMarketing Operations Specialist - Global Marketing\n* Planned and executed global marketing events to build email campaigns, target audience lists,\ncommunication collateral and follow-up processes\n* Expert user of the marketing automation tool Marketo; worked with regional marketing teams\nglobally to set up their systems from scratch\n* Proactively worked on identifying new customer groups and developed optimal channels to\nreach them\n* Led the creation and management of a marketing performance dashboard that reported on\nmarketing investment, conversion, and overall campaign performance\n* Responsible for providing reliable and consistent metrics on the effectiveness of marketing\ncampaigns to stakeholders, and strategies to improve them\n* Worked in tandem with the design team to develop email and landing page templates, and\ncreated relevant content for them\n* Provided expertise and guidance about marketing automation best practices to global\nmarketing teams by setting internal benchmarks, and ensuring high quality across all regional\ncampaigns\n\nhttps://www.indeed.com/r/Samyuktha-Shivakumar/cabce09fe942cb85?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Samyuktha-Shivakumar/cabce09fe942cb85?isid=rex-download&ikw=download-top&co=IN\n\n\n* Established an effective process for onboarding new users to Marketo, and developed the\nrequired training material\n* Co-ordinated with sales leads to develop an ongoing program that managed all stages of lead\ndevelopment, from cold nurtured inquiries to closed opportunities, ensuring timely follow-up on\nqualified leads\n\nHighlights at ThoughtWorks:\n\u2022 Successfully worked with marketing teams in US, UK, Australia, China, Brazil, South Africa and\nUganda\n\u2022 Underwent training with expert Marketo consultants in San Mateo, USA\n\u2022 Spoke at a number of internal conferences on the impact of marketing automation on business\nand related topics\n\u2022 Consistently recognised by management for working effectively with globally distributed team\nmembers and successfully completing campaigns\n\nMarketing Specialist\n\nOracle -  Bengaluru, Karnataka -\n\nJune 2009 to July 2012\n\nMarketing Specialist \u2013 Hardware Programs Lead \n\u2663 Designed and executed campaigns based on key industry trends and strategic market\nopportunities\n\u2663 Developed innovative programs aligned to business requirements and sales priorities\n\u2663 Worked on campaign target lists, key messages, calls to action and success metrics\n\u2663 Aligned closely with stakeholders like Product Marketing, Field Marketing, Sales Consulting and\nSales Management teams to execute campaigns\n\u2663 Published regular reports on program results, highlighting patterns, growth trends, best\npractices and key learnings\n\u2663 Ensured sales team\u2019s readiness through effective sales training and continually worked to\noptimize processes \n\nBusiness Analyst - Channel Support Specialist \n\u2663 Extended complete sales support to Oracle North America Alliances & Channel Sales team\n\u2663 Assisted Field Channel Managers in indirect deal closures with complete operations support\nfrom order to cash\n\u2663 Primary point of contact to the four exclusive distributors of Oracle and other tier two partners\nin the North America region\n\u2663 Managed the non-standard approval requests through Oracle\u2019s unified approval chain\n\u2663 Supported Group Sales Director in identifying new growth areas & practices in line with sales\nstrategies\n\nHighlights at Oracle:\n\u2022 Was awarded the \u2018Most Valuable Player\u2019, an accolade given to the top performer of the team \n\u2022 Winner of an internal contest for achieving the highest SLA on a consecutive basis\n\u2022 Instrumental in training the new hires and on-boarding them within a short span of time\n\u2022 Recognised by senior management for being a key contributor to the sales team\n\nAnalyst - Operations\n\n\n\nTESCO -  Bengaluru, Karnataka -\n\nJune 2006 to November 2006\n\n\u2663 Worked as an analyst with the Accounts Payable team\n\u2663 Responsible for processing vendor invoices\n\nEDUCATION\n\nPost Graduate Diploma in Business Administration in Marketing and\nHuman Resource\n\nMount Carmel Institute of Management -  Bengaluru, Karnataka\n\nJune 2007 to May 2009\n\nSKILLS\n\nProject Management, Product Marketing, Campaigns Management, Digital Marketing, Social\nMedia Marketing, Marketing Operations, Content Marketing, Channels Operations, Marketo\nAdmin User, Microsoft Office, Cross Functional Marketing, Salesforce, Marketing Programs,\nGlobal Marketing, Marketo, Training\n\nLINKS\n\nhttps://www.linkedin.com/in/samyuktha-shivakumar-********/\n\nADDITIONAL INFORMATION\n\nCurrently on a break to be with toddler child. Looking to get back to work now.\n\nhttps://www.linkedin.com/in/samyuktha-shivakumar-********/", "labels": []}
{"id": 16, "text": "Santosh Ganta\nSenior Systems Engineer - mainframe\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Santosh-Ganta/4270d63f03e71ee8\n\nWilling to relocate to: Bengaluru, Karnataka - hyderbad, Telangana - Chennai, Tamil Nadu\n\nWORK EXPERIENCE\n\nSenior Systems Engineer\n\nInfosys Limited -  Chennai, Tamil Nadu -\n\nFebruary 2014 to Present\n\nDevelopment,Testing,Support\n\nSenior system engineer\n\nInfosys limited\n\nDevelopment,Testing,Support\n\nEDUCATION\n\nB.Tech in Information Technology\n\nGMR Institute of Technology and Management -  Kakinada, Andhra Pradesh\n\n2013\n\nPratibha Junior College\n\n2009\n\nEnglish, Hindi\n\nS.R high School -  Chennai, Tamil Nadu\n\n2006\n\nSKILLS\n\nCA7 (4 years), DB2 (4 years), QMF (4 years), Cobol (4 years), Mainframe (4 years), Cics (4\nyears), Rexx (4 years)\n\nADDITIONAL INFORMATION\n\n\u2022 Adopt to any kind of Environment.\n\nTechnical Summary\n\nhttps://www.indeed.com/r/Santosh-Ganta/4270d63f03e71ee8?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Tools: ISPF, SPUFI, QMF, File-Aid, MainView, Librarian, CA7, Control-M, Xpeditor\n\u2022 Operating System: Windows 7\n\u2022 Database: DB2, SQL Server\n\u2022 Domain: Retail\n\u2022 Packages: MS office\n\n\u2022 Secondary Skills: Java Script, HTML, JSP, Java, Oracle 10g, Unix", "labels": []}
{"id": 17, "text": "Sarfaraz Ahmad\nAssociate network engineer - TATA Communications Ltd\n\nMuzaffarpur, Bihar - Email me on Indeed: indeed.com/r/Sarfaraz-Ahmad/1498048ada755ac3\n\nCisco Certified Internetwork Associate in Routing & Switching with progressive experience in\ndeployment and administration of Network infrastructure. I am looking for opportunities to further\nimprove my abilities & skills in the field of Network and Security technologies.\n\n\u27a2 Professional 2.5 years of experience in network Implementation & Troubleshooting in local and\nremote environments.\n\u27a2 Well accented with the key IT skills in the domain of LAN/WAN/Network Security, installation &\nconfiguration of IT networks, maintenance & troubleshooting.\n\u27a2 Potential of handling multiple tasks easily and capable of meeting deadlines.\n\u27a2 Possess excellent interpersonal communications and organizational skills.\n\nWORK EXPERIENCE\n\nAssociate network engineer\n\nTATA Communications Ltd -  Pune, Maharashtra -\n\nMarch 2017 to Present\n\nDepartment: CMIP (IP Provisioning)\n\nResponsibilities:-\n\u27a2 Working on Alcatel Migration project for Tata Communications Ltd, responsible for provisioning\nand migrating customer services from Cisco (7200/7600) /Juniper (MX104) to Alcatel provider\nedge router (SR 7750)\n\u27a2 To carry out bulk migration and planned events of enterprise customers to enhance the network\ncapabilities and compatibilities for new technologies.\n\u27a2 Responsible for carrying domestic as well as international customer services migration\nactivities in bulk. Involving all other stakeholders responsible for end to end delivery of customer\nservices, to carry the successful migration.\n\u27a2 Parenting existing business switch ring with Alcatel Routers in combination with cisco routers\nwith the help of field Engineers and synchronizing vlan database on each switch.\n\u27a2 Shut the interface and protocol on Cisco Routers and un-shut the interface on Alcatel routers\nduring Migration activity.\n\u27a2 Comparing the results of pre-check and post-checks and revert the links to cisco that are not\ncoming Up on Alcatel or not compatible with cisco i.e. EIGRP.\n\u27a2 Troubleshooting and resolving the customer issues related to either at routing level such as\nEIGRP, OSPF, BGP, MPLS or at access level Post migration activity.\n\u27a2 Troubleshooting and managing LAN-to-LAN VPN on cisco router.\n\u27a2 Troubleshooting and managing GRE Tunnel on services Getaway.\n\u27a2 Configuration on cisco, juniper, Huawei switch creation Ether channel, VLAN, MSTP, QNQ,\nDot1Q etc.\n\u27a2 Configuring and managing SVI interface on a Layer3 switch to provide inter VLAN routing.\n\u27a2 Configuration Line VTY on customer CISCO router.\n\nhttps://www.indeed.com/r/Sarfaraz-Ahmad/1498048ada755ac3?isid=rex-download&ikw=download-top&co=IN\n\n\n\u27a2 Configuration port security on customer hand up switch port.\n\u27a2 Troubleshooting end user network connectivity issue.\n\u27a2 Performing technical escalations in line with company policy.\n\u27a2 Accountable for customer review meetings weekly basis & giving feedback to the subordinate\nteam and Management.\n\u27a2 Responsible for any internal or external escalation (Customer's End)\n\u27a2 Focus on customer requirements and providing them strategic plans for backups, redundancies\nand minimum downtimes under severe outages.\n\nNetwork engineer\n\nCisco -  Mumbai, Maharashtra -\n\nAugust 2015 to February 2017\n\nResponsibilities/Achievement\n\u27a2 Experience in configuring and troubleshooting of routing protocols and Firewalls\n\u27a2 Backing up and Upgrading software on Cisco router, switches using TFTP server\n\u27a2 Troubleshooting end user network connectivity issue\n\u27a2 Experience in installing and configuring DHCP services on Cisco Router and Switch.\n\u27a2 Configuring and troubleshooting Ether channel/Port-channel issue\n\u27a2 Configuring and troubleshooting VLAN, VTP and STP.\n\u27a2 Configuring and managing BPDU guard and Filter on a switch\n\u27a2 Providing port security on switch interface\n\u27a2 Dealing with Dynamic routing protocol Such as EIGRP, OSPF and BGP\n\u27a2 Configuring and managing SVI interface on a Layer3 switch to provide inter VLAN routing\n\u27a2 Worked on networks with WAN protocols such as HDLC and PPP.\n\u27a2 Worked on Gateway Redundancy protocols such as HSRP on Cisco Router.\n\u27a2 Implemented traffic filters using Standard and Extended access-lists, Distribute-Lists, and\nRoute Maps.\n\u27a2 Installation, upgradation, Backup and restoration of checkpoint firewall\n\u27a2 Implemented rules, NAT, URL filtering on checkpoint firewall.\n\u27a2 Configuring and managing Rules and NAT on firewalls.\n\nEDUCATION\n\nBachelor of Commerce in Commerce\n\nR.D.S COLLEGE -  Bihar Sharif, Bihar\n\nJuly 2013\n\nH.S.C\n\nDr. R.M.L.S COLLEGE -  Bihar Sharif, Bihar\n\n2009\n\nS.S.C in G.N.H.S\n\nBihar School Examination Board -  Patna, Bihar\n\n\n\n2007\n\nSKILLS\n\nBGP (2 years), EIGRP. (2 years), OSPF (2 years), security (2 years), vlan (2 years)\n\nADDITIONAL INFORMATION\n\nTechnical Skills\n\nNetwork\n\u27a2 Switching: CST, PVST, PVST+, RSTP and MST, VLAN, Private VLAN, Trucking, Inter-VLAN Routing,\nSPAN/RSPAN, Ether channel or Port Bundling using static and dynamic protocol (PAgP and LACP)\n\u27a2 FHRP: HSRP (V1 & V2), VRRP and GLBP.\n\u27a2 Routing: Configuration and Troubleshooting Layer 3 Routing protocols such as Static routing,\nfloating routing, RIP, EIGRP, OSPF, BGP, VRF-Lite and MPLS.\n\nData Security\n\u27a2 Checkpoint Firewall: FW Monitor, TCP-Dumps, Rules, NAT, IPsec VPN, Backup & Restore, URL\nfiltering, Installation, Migration, Upgradation, ClusterXL etc.\n\u27a2 ASA Firewall: Access Control Lists with object, Address Translation, IPsec VPN, LAN-to-LAN VPN,\nHigh Availability in Active-Active & Active-Standby Mode, Redundant interface, SLA Monitoring,\nSecurity Contexts.\n\u27a2 IOS Firewall: Standard and extended access list and Zone Based Firewall (ZBF), DMVPN, IPsec,\nIPsec over GRE.\n\nServices\n\u27a2 Services: ARP, GARP, RARP, DNS, DHCP, FTP, TFTP, TCP, ICMP, TELNET, SSH, HTTP, HTTPS. IP\nSLA, NTP, SNMP\n\nAreas of Interest\n\n\u27a2 To perform troubleshooting and network performance planning.\n\u27a2 To implement enterprise level network architectures.", "labels": []}
{"id": 18, "text": "Senthil Kumar\nSenior Technical Lead - HCL Cisco\n\n- Email me on Indeed: indeed.com/r/Senthil-Kumar/d9d82865dd38d449\n\n\u2022 Result oriented Networking Professional with 11 years' experience in\ntesting network products diversified skills includes testing ACI SDN fabric,\nUCSD orchestration, Routing and Switching: design, deploy and\ntroubleshoot customer network\n\u2022 Extensive knowledge of Testing and Deploying network test plans,\nprotocols, Strategies and Lab Setup.\n\u2022 In-depth knowledge of ACI fabric, Routing and switching protocols\n\u2022 Proven communication skills and the ability to troubleshoot and solve\nnetwork issues\n\u2022 Skilled in providing support in various networking testing activities to\nprovide effective support and handling of customer escalation and for\nensuring timely and effective support\n\nWORK EXPERIENCE\n\nSenior Technical Lead\n\nHCL Cisco -  Bengaluru, Karnataka -\n\nOctober 2015 to Present\n\nProject Title: Feature testing and Deployment on ACI fabric for Cisco IT and UCSD\n\n\u2022 ACI Fabric deployment in single POD and Multi-POD environment\n\u2022 Deployed Cisco ACI Multi-Site Controller with ACI fabric for Testing stretched\nEPG's between Data centers\n\u2022 Deployed Cisco IT replica Setup in Local Lab\n\u2022 Handling Cisco IT customer network and pre-deployment of image testing before handing to\nCISCO IT Customer\n\u2022 End to end testing of Cisco IT test bed, upgrade and downgrade of Various\nimages\n\u2022 Configured SDN Controller (APIC) Decommissioned and Commissioned in Cluster\n\u2022 Configured UCS manager\n\u2022 APIC upgrade downgrade, Cisco Nexus 9k leaf spine downgrade upgrade testing.\n\u2022 Bring up ACI leaf and spine fabric with UCS fabric interconnect\n\u2022 Implementation of VMware DVS and Cisco AVS in Esxi host and tested the AVS\nfunctionality\n\u2022 Tested redundancy testing on leaf, spine and fabric interconnect\n\u2022 Communicated inconsistencies between System Specifications and Test Results\nto development and/or analyst team\n\u2022 Involved in Defect Review meetings escalating and prioritizing issues effectively\n\u2022 Experience in implementation of VMware Infrastructure 5.5 / 6.0\n\u2022 Managed Virtualization environment using VMware EXI 5.5/6.0, Vcenter 5.5/6.0\n\u2022 Creating and Managing virtual machines and Templates\n\nhttps://www.indeed.com/r/Senthil-Kumar/d9d82865dd38d449?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Migration of VM using Vmotion between Clusters\n\u2022 Configured Windows iSCSI SAN and integrated to VMware\n\u2022 Testing Cisco APIC with UCSD integration with Multi Site Controller\n\nTechnical Lead\n\nAricent Cisco -\n\nJuly 2012 to September 2015\n\nClient Cisco Systems\nProject Title: Feature testing and Deployment on CISCO CARRIER PACKET\nTRANSPORT\n\n\u2022 Worked on EFT with Bharti Telecom and China Telecom Demo for 9.7 rings features\n\u2022 Conducting the Dev-test activities for CTP for Rings 9.7 feature. Bring up new setup,\n\u2022 Written test cases for CPT 50 Ring feature to test REP and also to validate MPLS TP and P2MP\nEVC\n\u2022 Tested MPLS TP, single segment and multi-segment Pseudowire, P2MP EVC, REP\nfeatures.\n\u2022 Tested EVC P2MP type on that carried out Single tag, Double tab of 802.1q and Dot1ad\n\u2022 Based on Cisco Resilient Ethernet Protocol feature CPT 9.7 testing is carried out.\n\u2022 Created open and closed end segment for REP topology and tested Rings CTP 9.7 feature\n\u2022 Tested Mac learning, aging, flushing and limiting features\n\u2022 Tested multiple frame size and JUMBO frame verification with MPLS TP\n\u2022 Tested Link Aggregation verification with MPLS TP\n\u2022 Tested Memory Backup verification with fully loaded CPT configuration\n\u2022 Hardware redundant Plug in and Plug out Circuit Pack test and High availability testing\nfor CTP 600\n\u2022 Network Protection verification testing with MPLS TP working LSP and Protected LSP\n\u2022 Tested DHCP Snooping\n\u2022 Tested EVC P2MP type on that carried out Single tag, Double tab of 802.1q and Dot1ad\n\u2022 Tested Mac learning, aging and limiting features\n\u2022 Supporting customer queries for the product\n\nTechnical Lead\n\nAricent Group -\n\nDecember 2011 to June 2012\n\nClient KeyMile Germany\n\n\u2022 Conducting the Dev-test activities for Eth-Sys series switch. Bring up new setup,\npreparation of FT test plan, derive test estimate for the Release.\n\u2022 Tested the various images submitted by release -ops team\n\u2022 Carried out manual testing on Vlan 802.1Q ( Mac aging, Mac flooding, Tag and untag)\nusing SNMP mib browser and Aricent ISS\n\u2022 Written test cases for RSTP and MSTP features and tested it.\n\u2022 Debug the failures by configuring the test bed manually and filing the defects\n\nTechnical Lead (HCL)\n\n\n\nClient Cisco Systems -\n\nJanuary 2010 to December 2011\n\nCisco 12000 Gigabit\nSwitching Router)\n\n\u2022 Performed the Role of Technical lead for a team member size of 4\n\u2022 Test the various images submitted by release -ops team\n\u2022 Carried out manual testing of Connection Server (HSRP) and OSPF features to verify\nthe functionality\n\u2022 Tested OSPF point to point and Broadcast network\n\u2022 Bring up new setup, prepare test plan, derive test cases and execute the test cases\n\u2022 Debug the failures by configuring the test bed manually and filing the defects\n\u2022 Interact & co-ordinate with Dev-test and Development team to review the CFD's and script\ncoverage.\n\u2022 Find defects through manual testing and verify the defects\n\u2022 Analyze both internal and externally found defects and come up with a plan to reduce the\ndefects missed out in the scripts.\n\nTest Lead (HCL)\n\nClient Cisco Systems -\n\nMay 2007 to December 2011\n\nVirtualization)\n\n\u2022 Performed the Role of lead for a team member size of 5\n\u2022 Image testing on ACE module and ACE 4710 Appliance.\n\u2022 Coordinating with onsite/offsite teams in resolving the defects found when performing\nTesting.\n\u2022 Given white paper & Presentation document in ACE High Availability and Securing\nACE 4710 with ACS server\n\u2022 Test Plan writing, reviewing and planning test strategies.\n\u2022 Peer to Peer Reviews to avoid Post Delivery Defects\n\u2022 Lab Test Bed creation and Infrastructure Maintenance\n\u2022 Configured High Availability of ACE Module, ACE Appliance and ANM Servers\n\u2022 Configured End to End traffic Load balancing of Data center using the ACE 4710, ACE\nmodule and CSM devices\n\u2022 Deployed different topologies NAT, Routed and Bridged for End to End traffic Load\nbalancing of Data center\n\nLab Administrator\n\nHCL Cisco -  Chennai, Tamil Nadu -\n\nMay 2003 to June 2007\n\nKey responsibility is to perform CISCO Network LAB Administration\n\n\n\nEDUCATION\n\nB.Tech in Computer Science in Computer Science\n\nDr MGR University Chennai -  Chennai, Tamil Nadu\n\nSKILLS\n\nCPT (3 years), Data Center (4 years), Ethernet (3 years), testing (10+ years), Virtualization (7\nyears)\n\nLINKS\n\nhttp://www.linkedin.com/in/senthil-kumar-9600101b\n\nADDITIONAL INFORMATION\n\nSkills\n\nVirtualization VMware-VSphere, DVS, Cisco AVS, Cisco\nACE\n\nSDN Cisco Application Centric Infrastructure\n(ACI)\n\nL2/L3 Protocols OSPF, EIGRP, MPLS, VLAN, VXLAN,\nSTP, RSTP, VPC\n\nMetro Ethernet Protocols Carrier Ethernet protocols EVC, MPLS TP,\nPsudoWire, REP\n\nTraffic Generation Tools Traffic tools IXIA Iexplore and Smart Bit\n\nTesting Tools Wireshark and MIB Browser\n\nNetwork Products Cisco SDN ACI Fabric\nVMWare 5.5 and 6.0\nCisco UCSD Orchestration\nCarrier Ethernet devices (CPT [\u2026]\nCisco Metro Ethernet 2600\nCisco routers GSR 12000, 7600\nData center Virtualization using the ACE\nNexus 5k and ACI 9K switches\nCat 6500 and 3550 Switches\n\nhttp://www.linkedin.com/in/senthil-kumar-9600101b", "labels": []}
{"id": 19, "text": "Shabnam Saba\nOffshore SAP CRM Functional Consultant\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Shabnam-Saba/dc70fc366accb67f\n\nTo understand the organization and to identify its needs and correlate them with my goals so as\nto apply myself to responsibility with total dedication and dynamism so as to grow along with\nthe organization.\n\nPast Organization: Tata Consultancy Services as SAP CRM functional consultant (July 2014 - Jan\n2016), SAP Labs, India (from Feb 2011-June 2014), Cognizant Technology Solutions (May 2010-\nOctober 2010)\n\nWORK EXPERIENCE\n\nOffshore SAP CRM Functional Consultant\n\nSAP AG -\n\nJuly 2014 to January 2016\n\nDescription: The project involves SAP IT support with respect to AGS and SAP Cloud Process .SAP\nIT support involves handling incident and service requests from SAP CRM users and customers\nacross the globe.\n\nResponsibilities:\n\u2022 Problem Analysing and Handling Tickets of SAP CRM (AGS and Cloud process)\n\u2022 Handling incident and providing solution with in SLA time frame.\n\u2022 Configuring the system to resolve the issues.\n\u2022 Worked on changes to the functional specifications required as per the clients\nrequirement.\n\u2022 Preparing test cases and taking approval from client before moving new changes to production.\n\u2022 Coordinated with the technical team in solving the tickets.\n\nQuality Engineer\n\nSAP Labs -\n\nJuly 2002 to January 2014\n\nResponsibilities:\n\nWorked in configuring and testing different areas of Framework:\n\n\u2022 Nav. Bar profile\n\u2022 Role Config key\n\u2022 Business Roles and UI Config tool\n\u2022 Creating A Business Role\n\nFunctional Consultant\n\nhttps://www.indeed.com/r/Shabnam-Saba/dc70fc366accb67f?isid=rex-download&ikw=download-top&co=IN\n\n\nSAP Labs -\n\nMarch 2012 to June 2013\n\nDescription:\n\nMobile Client Technology is client technology designed for Microsoft Windows-based, occasionally\nserver-connected CRM field applications. These applications offer a rich function set, such as\nSAP CRM Mobile Sales and SAP CRM Mobile Service. Mobile Sales for SAP CRM allows users to\naccess all their accounts, contacts, leads, opportunities, and activities from a single point. All\nrelationships between these business objects are automatically mapped in the application, which\nallows for fast and easy navigation.\n\nResponsibilities:\nTesting the various business objects:\nOpportunities, Quotation, Sales orders, Activities.\nWorked on system set up (creation of sites, subscriptions), opportunity, quotation, order\nmanagement.\n\n5. CRM Sales ( CRM 7.01, CRM 7.02, CRM 7.03) -Feb '2011-June 2014\n\nClient: SAP Labs India\nRole: Functional Consultant\nDescription:\n\nThis area in SAP Customer Relationship Management (SAP CRM) enables you to manage your\nsales cycle, starting with creating appointments and business opportunities, through to managing\nsales orders, contracts, and invoicing. It also allows you to organize and structure your sales\nterritories according to your business requirements.\n\nResponsibilities:\nWas involved in customizations and testing of:\n\n\u2022 Territory management\n\u2022 Account and contact management\n\u2022 Activity management, visit Planning\n\u2022 opportunity planning, opportunity management\n\u2022 quotation and order management\n\u2022 Pricing\n\u2022 Organizational Management and Billing\n\u2022 Customizations of Surveys\n\nEDUCATION\n\nB.E in CSE\n\nPadmanava College of Engineering\n\n2009\n\nSt. Joseph's convent school\n\n\n\n2003\n\nSKILLS\n\nCRM (10+ years), CUSTOMER RELATIONSHIP MANAGEMENT (10+ years), TESTING (10+ years),\nUI (10+ years), USER INTERFACE (10+ years)\n\nADDITIONAL INFORMATION\n\nOther Skills:\n\nCRM Middleware:\n\n\u2022 Worked on downloading initial and delta download between ECC and CRM,\nCRM and MSA.\n\u2022 Monitoring middleware data between ECC and CRM.\n\u2022 Monitoring Queues and error handling of BDocs.\n\u2022 Worked on subscriptions and Publications, Replication Objects.\n\nSAP ECC Sales and Distribution:\n\n\u2022 Strong Understanding of SAP Customizing and Detailed knowledge of core SD functions such\nas Item Categories, Text Determination, output determination, taxes\n\u2022 Customer Master and Material Master data, item proposal, variant configuration, Product\nhierarchy\n\u2022 Sales document types (Orders, Returns, CMR, DMR)\n\u2022 Billing and Pricing concept, worked on bill plans\n\u2022 Sales Enterprise structure\n\u2022 Copy control, Incompletion log, Material listing and Exclusion\n\u2022 Partner determination, Customization of Account groups\n\u2022 Worked with cross-functional teams during development and configuration activities to ensure\nimpact to other SAP modules and processes is considered.\n\nSAP CRM Skills:\n\u2022 CRM Sales and Service order management (Extensive experience in configuration for Text\nDetermination Procedures, Status Profile, Org. Data Determination and Transaction Types)\n\n\u2022 SAP Fiori (creation of test data and application testing in different landscapes including browser\ntesting)\n\u2022 SAP Mobile Sales (creation of test data and system set up)\n\u2022 Well versed in base customizing and WEBUI configuration along with CRM Tables.\n\u2022 Hands on experience in CRM middleware (creation of sites, subscription, publication, checking\nbdocs, idocs, download objects) and trouble shooting.\n\u2022 In depth knowledge in CRM One Order Framework.\n\u2022 Extensive experience in the configuration of Web UI for multiple Business Roles, Actions,\nNavigation Bar Profile.\n\nCompetencies and Skills:\n\n\n\n\u2022 CR-100 (BP, Product, Org model, Partner/Text determination, Transaction type, Item categories,\nTerritory)\n\u2022 CR-300\n\u2022 CRM Mobile Sales\n\u2022 CRM Middleware Basics\n\u2022 Basic Debugging (ABAP)\n\u2022 Knowledge on Idocs (set-up, filtration, reprocessing)\n\nTesting:\n\u2022 Experience in SAP CRM Module (sales and service) with testing\n\u2022 Good understanding of application testing process.\n\u2022 Coordinated with SAP CRM technical team members to understand testing functionalities\n\u2022 Written Test cases for different CRM modules (Account Management, opportunity, activity\nmanagement, sales order creation) Sales & Service.\n\u2022 Managed issue logs / defects and subsequent closures.\n\nBasic Debugging:\nKnowledge of ABAP Debugging, Basic ABAP (Tables, data elements, Working with Table\nMaintenance Generator, Creating a Transaction Variant, Creating an SAP Area Menus, Find the\nSAP IMG Customizing Activity from the Table Name, basic knowledge of Smartforms)", "labels": []}
{"id": 20, "text": "Shaheen Unissa\nSAP ABAP Consultant\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Shaheen-Unissa/\nc54e7a04da30c354\n\n\u2022 Having 8 years of experience in IT industry as SAP ABAP Developer. She\nwas involved in multiple projects viz. 3 Live Cycle Implementations of SAP and\nworked on Rollout Project (Federal Mogul) . Worked in end to end implementation\nproject MPT (Mormugao Port Trust), GOA at client location and also offshore at pune\nfor MPT.\n\u2022 She has worked on end to end implementation project with client Etisalat on SRM\nserver.\n\u2022 She has worked for GE (General Electric Company) Support Project and Acelor Mittal\nend to end implementation project at offshore Hyderabad.\n\u2022 She has worked for NNIT (Novo Nordisk) and M.R.S oil and gas company at offshore\nHyderabad. She has worked on Windows 98, 2000, windows vista platforms.\n\u2022 Worked for MPT (Mormugao Port Trust) at client location GOA for 7 months which is\nAn end to end implementation so got an good exposure in port operating system.\n\nOrganization Designation Duration\nTech Mahindra Limited, Hyderabad, India. Sr. Software Engineer May/2008 - May/2016\n\nWORK EXPERIENCE\n\nSAP ABAP Consultant\n\nAcelorMittal -\n\nMarch 2015 to April 2016\n\nProject Description:\nArcelorMittal is the world's leading steel and mining company. Guided by a philosophy to produce\nsafe, sustainable steel, it is the leading supplier of quality steel products in all major markets\nincluding automotive, construction, household appliances and packaging. ArcelorMittal is present\nin 60 countries and has an industrial footprint in 19 countries.\n\nContribution:\n* Developed reports both interactive and classical.\n* Developed interactive reports.\n* Developed LSMW to upload material master data.\n* Developed LSMW to upload vendor master data.\n* Developed LSMW to upload Purchase info records.\n* Developed Smartform to print sales order confirmation.\n* Developed Smartform to print purchase order details.\n* Developed Smartform to print Invoice details.\n\nHandled error in workflow.\n\nhttps://www.indeed.com/r/Shaheen-Unissa/c54e7a04da30c354?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Shaheen-Unissa/c54e7a04da30c354?isid=rex-download&ikw=download-top&co=IN\n\n\nSAP Modules/ Tools:\n\u2022 PP, MM and SD.\n\nConsultant\n\nSAP ABAP -  BADI, MADHYA PRADESH, IN -\n\nOctober 2012 to February 2015\n\nProject Description:\nEtisalat is the Middle East's leading telecommunications operator and one of the largest\ncorporations in the six Arab countries of the Gulf Cooperation Council, with a market value of\napproximately Dh81 billion (US$22 billion) and annual revenues of over Dh32.9 billion (US$9\nbillion)\n\nA multinational, blue-chip organization, Etisalat has operations in 15 countries in the Middle East,\nAfrica and Asia. Nearly 42, 000 people are directly employed by the company.\n\nEtisalat's international acquisition program began in earnest in 2004 when it won the second\nmobile license - the first third-generation (3G) mobile license - in Saudi Arabia. Since then, the\ncompany has witnessed rapid expansion that has positioned it as one of the world's fastest\ngrowing operators, with subscribers rocketing around 3, 475 per cent from four million in 2004\nto 141 million in2013.\n\nContribution:\n\u2022 Developed class using methods to increase the RFx number ranges based on opco code and\nadding prefix for portal system in SRM server.\n\u2022 Developed class with methods to get the details of supplier profile and updating the same in\ncustom table and generating a email notification to the vendor manager using web dynpro for\nportal system in SRM server.\n\u2022 Developed a smartform for purchase order in SRM server.\n\u2022 Modified standard smartform for Bid bond invitation form \"BBP_ BID_INVITATION\" adding cover\nletter as first page.\n\u2022 Implemented a BADI \"BBP_OUTPUT_CHANGE_SF\" to trigger the Bid bond smartforms based on\nopco code.\n\u2022 Implemented a BADI \"BBP_DOC_CHANGE_BADI\" to change the purchase organization and\npurchase group based on opco code.\n\n\u2022 Developed a class to download multi excel sheet to transfer the data from SRM to CLM on\npresentation server as well as in CLM server.\n\n\u2022 Developed a class for generating mail for RFX responses rejected.\n\nConsultant\n\nSAP ABAP -\n\nDecember 2011 to March 2012\n\nProject Description:\n\n\n\nM.R.S. Oil and Gas Company Limited distribute and supplies petroleum products. It offers\nkerosene and gas oils. The company was founded in January 1998 and is based in Lagos, Nigeria.\nM.R.S. Oil and Gas Company Limited operate as a subsidiary of MRS Group. The company has\nacquired a total of 426 fuel stations spread across the country and plans were on course toward\nbranding more filling stations.\n\nContribution:\n\u2022 Developed smartforms for Bank Wire Transfers for MRS Oil Nigeria. (a) Payment made in\nNigerian Naira - Local Payment. (b) Payment made in USD to Local Vendors. (c) Payment made\nin Foreign Currency to Foreign Vendor.\n\u2022 Developed a smartform to print the Request for Quotation as per client Requirement.\n\u2022 Developed a smartform for Physical Inventory is used to conduct physical stock counts in\nstorage locations in order to check book inventory to physical inventory (MI01)\n\u2022 Developed a smartform for Operational Work Clearance Document in work clearance\nmanagement.\n\u2022 Developed smartform for work permit.\n\nSAP Modules/ Tools:\n\u2022 PP, MM, PM, SD and IS-Oil.\n\nConsultant\n\nSAP ABAP -\n\nJuly 2011 to November 2011\n\nProject Description:\nArcelorMittal is the worlds leading steel company, with operations in more than 60 countries.\nArcelorMittal is the leader in all major global steel markets, including automotive, construction,\nhousehold appliances and packaging, with leading technology, as well as sizeable captive\nsupplies of raw materials and outstanding distribution networks. In 2010, ArcelorMittal had\nrevenues of $78.0 billion and crude steel production of 90.6 million tonnes, representing\napproximately 8 per cent of world steel output.\n\nContribution:\n\u2022 Developed Quality Claims Report for purchase orders for which whole cycle is done and also\nat sales distribution side.\n\u2022 Developed a Zbapi for portal to display the data in portal from different tables.\n\nSAP Modules/ Tools:\n\u2022 PP, MM, PM and SD.\n\nConsultant\n\nSAP ABAP -  Bangalore, Karnataka -\n\nOctober 2010 to June 2011\n\nGE employees over 13, 000 people in India. It exports over $1 billion in products and services.\n\nContribution:\n\n\n\n\u2022 Handling HPSD tickets as well as GMR tickets.\n\u2022 Modified existing production summary report which gives shop performance for past fiscal\nweeks. It also gives data regarding the current shop situation and expected outcome for the\nupcoming quarter.\n\u2022 Modified induction slot module pool program to display the comments entered in production\nstatus report for a given project.\n\u2022 Modified life limited parts smart form which gives the list of LLP information for each Engine. This\nform compiles the Outgoing Shop Visit Documentation Pack for the customer for each Engine/\nModule shop visit and the audience will be outgoing customer records business users from the\nDocumentation department.\n\u2022 Modified cost accumulation invoicing process to upload file for billing request.\n\nSAP Modules/ Tools:\n\u2022 SAP ECC 6.0\n\u2022 PM\n\nConsultant\n\nSAP ABAP -\n\nMay 2010 to September 2010\n\nProject Description:\nNovo Nordisk is a Healthcare company and a world leader in diabetes care. Since 1923, Novo\nNordisk has been committed to providing the best possible solutions for people with diabetes and\ntheir caregivers. Novo Nordisk products and services help millions of people worldwide better\nmanage their condition with the broadest diabetes product portfolio in the industry, including the\nmost advanced products within the area of insulin delivery systems, other treatment areas like\nhaemophilia and chronic inflammation. Novo Nordisk is a world leader in diabetes care.\n\nContribution:\n\u2022 Reviewing and auditing the enhancements, reports, bi developments, interfaces and forms and\nmigrating them from Lotus notes to Solution manager.\n\u2022 Used an USER EXIT SAPMF02K in program ZXF05U01 for validating vendor tax number range.\n\u2022 Completing the Technical Specifications.\n\u2022 Creation of Handling over documentation\n\nConsultant\n\nSAP ABAP -  Marmagao, Goa -\n\nOctober 2008 to April 2010\n\nProject Description:\nMormugao Port Trust (MPT), GOA is end to end implementation project. MPT is one of the leading\nport in India from 1962.Mormugao Port, GOA is the premier iron ore exporting Port of India\nwith an annual throughput of around 27.33 million tonnes of iron ore traffic. Though ore is the\npredominant cargo, there has been a steady increase in liquid bulk and general cargo traffic ever\nsince it's joining the ranks of the Major Ports of India.\n\nContribution:\n\n\n\n\u2022 Developed Customized screens for Container Maintenance for Port Operating System (POS)\nbased on service order number and vessel number, fetching the Import containers details from\nIW33 for this containers estimating and re-estimating for the new containers added and creating\nthe sales order with reference to the quotation number and extension of sales order bapi has\ndone.\n\u2022 Developed Customized screens for Goods Movement (IN) for Port Operating System (POS) goods\ninward has to be done for MOHP plot and displaying the Stock Details.\n\u2022 Developed customized screens for Barge unloading for MOHP (Mechanical ore hydraulic power)\nas per MPT users as well as Agents of MPT.\n\u2022 Development of ALV Grid report to display the Warranty of the Equipment which is going to\nexpire in the given period or month.\n\u2022 Developed Classical report to display the Vessel Shifted from different Berths during the\nTurnaround period.\n\u2022 Developed a smart form to display the arrival status of vessels for the given period of time.\n\u2022 Implemented User Exit MV45AFZZ for SD module to calculate the Penal rent based on days\nof start of period and billing to be done for every 10 days Routine - Created Routine-920 in SD\nmodule RV61A920 to calculate the % as per number of days\n\u2022 Created a batch input program to upload Plant Maintenance Task List for long text tcode IA05.\n\u2022 Property: Used Standard function module 'BAPI_RE_PR_CREATE' to transfer Property master\ndata from legacy system to R/3 system for Real Estate through 'REBDPR'. -Building: Used\nStandard function module 'BAPI_RE_BU_CREATE' to transfer Building master data from legacy\nsystem to R/3 system for Real Estate through 'REBDBU'. -Contract: Used Standard function\nmodule 'BAPI_RE_CN_CREATE' to transfer Contract master data from legacy system to R/3\nsystem for Real Estate through 'RECN'. -Rental object: Used Standard function module\n'BAPI_RE_RO_CREATE' to transfer Rental Object master data from legacy system to R/3\nsystem for Real Estate through 'REBDRO'. -Business Partner: Used Standard function module\n'BAPI_BUPA_CREATE_FROM_DATA' to Create partners and 'BAPI_BUPA_ROLE_ADD_2' to create\nRolls for the partners from the file on legacy system to R/3 system for Real Estate.\n\nSAP Modules/ Tools:\n\u2022 SAP ECC 6.0\n\u2022 SD, MM, FICO, PM, RE, POS\n\nSAP ABAP Consultant\n\n8 Federal Mogul -\n\nOctober 2007 to September 2008\n\nProject Description:\nFederal-Mogul Corporation is an innovative and diversified $6.3 billion global supplier of quality\nproducts, trusted brands and creative solutions to the automotive, light commercial, heavy-duty\ntruck, off-highway, agricultural, marine, rail and industrial markets. The 45, 000 people of Federal-\nMogul located in 35 countries drive excellence in all they do.\n\nContribution:\n\u2022 Development of ALV Hierarchical report to display the backorders that are cancelled by\ncustomers and updates the database as \"BC\". An ALV report to display the Monthly Earned rebate\naccrued by month for all the active programs in SAP for the Sales organizations selected.\n\u2022 Creation of the BDC for loading Return orders cancelled by customers in Sales order.\n\n\n\n\u2022 Developed smartform for Certificate of origin and export invoice.\n\u2022 Modified standard smartform of PO layout change\n\nSAP Modules/ Tools:\n\u2022 SD, MM, PP, OTC\n\nEDUCATION\n\nB-TECH\n\nJAWAHARLAL NEHRU TECHNOLOGICAL UNIVERSITY\n\n2005\n\nADDITIONAL INFORMATION\n\nSAP Skills\n\n\u2022 Classical and Interactive Reports\n\u2022 BDC's, LSMW, BAPI,\n\u2022 BADI, User Exits\n\u2022 Data Dictionary objects\n\u2022 Dialog Programming\n\u2022 ALE/ IDOCS\n\u2022 SAP Scripts, Smart Forms\n\u2022 Possess hands on skill set on SD & MM modules.\n\u2022 Experience on Unit testing (UT), functional testing (FT), UAT scripts and regression\nTesting\n\u2022 Worked on SAP webdynpro applications.\n\u2022 Worked on SRM server.\n\u2022 Worked on Abap Workflow.", "labels": []}
{"id": 21, "text": "Sharan Adla\n- Email me on Indeed: indeed.com/r/Sharan-Adla/3a382a7b7296a764\n\n\u2022 Having 4yrs. of solid work experience in designing experiences for Digital (Web, Mobile) and\nPrint media\n\u2022 Hands-on experience with tools such as Adobe Photoshop, Illustrator, InDesign, and\nDreamweaver\n\u2022 Experience in creating low/high-detailed annotated wireframes, and user flows for applications\nusing prototyping tools\n\u2022 Having good knowledge on developing use cases, user stories, & personas\n\u2022 Hands-on experience with HTML5 & CSS3, including cross-browser compatibility\n\u2022 Hands-on experience in creating RWD (Responsive Web-Design) layouts\n\u2022 Having good exposure on Marketing and Health Care domains\n\u2022 Good knowledge on marketing automation tool Eloqua\n\u2022 Hands-on experience on version control tool SVN\n\u2022 Quick learner, self-driven, problem-solver, highly motivated team player and ability to quickly\nadapt to new trends and technologies\n\u2022 Excellent written and verbal communication skills to engage clients and the team\ncollaboratively\n\u2022 Having good leadership and team management skills\n\nWORK EXPERIENCE\n\nSpecialist II, Marketing\n\n-\n\nMarch 2017 to Present\n\n2017\n\u2022 Worked as Software Engineer with Prolifics Corporation Limited from Dec' 2015 to Sept' 2016\n\u2022 Worked for National Informatics Centre under multiple payrolls from Apr' 2014 to Oct' 2015\n\u2022 Worked as an Inter Trainee under National Informatics Center as a Web Designer from Sept'\n2013 to Apr' 2014\n\nSkill Set\nSoftware: Adobe Photoshop, Adobe InDesign, Adobe Illustrator,\nAdobe Dreamweaver, MS PowerPoint\n\nMarketing Team\n\n-\n\nNovember 2017 to November 2017\n\nCourses and Certifications\n\u2022 Currently a member and pursuing multiple user experience related certification courses from\n\"The Interaction Design Foundation\"\n\nMarketing Automation Tool\n\nhttps://www.indeed.com/r/Sharan-Adla/3a382a7b7296a764?isid=rex-download&ikw=download-top&co=IN\n\n\nOracle Eloqua -\n\n2010 to 2010\n\nWeb Technologies: HTML (5), CSS (3)\nFront End Framework: Twitter Bootstrap\nAwards and Recognitions\n\u2022 SPARKLE Award - Certificate of Appreciation - Oct 2017\n\u2022 Award of appreciation for outstanding performance and contribution towards development of\nseveral digital assets & microsites for OOW 2017\n\nEDUCATION\n\nBachelors in Computer Science\n\nM. V. G. R College of Engineering -  Vizianagaram, Andhra Pradesh\n\nDecember 2008\n\nMPC subject\n\nGowtham Jr. College -  Vijayawada, Andhra Pradesh\n\nAugust 2006\n\nSSC\n\nVignan Vidyalayam High School -  Visakhapatnam, Andhra Pradesh\n\nLINKS\n\nhttps://behance.net/sharanadla\n\nhttps://www.linkedin.com/in/sharanadla\n\nhttps://behance.net/sharanadla\nhttps://www.linkedin.com/in/sharanadla", "labels": []}
{"id": 22, "text": "Shreyanshu Gupta\nSoftware Development Engineer with 6 months of experience in Java, C,\nVelocity, Web Development at Amazon Development Centre.\n\nBangalore, Karnataka - Email me on Indeed: indeed.com/r/Shreyanshu-\nGupta/6bd08d76c29d63c7\n\nSelf-Motivated and Hardworking Computer Science student seeking to apply my skills and in the\nprocess, develop\nprofessionally.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSoftware Development Engineer\n\nAmazon\n\nInterned at Amazon Development Centre for 6 months 2017\n\nProjects did during Internship\n\n\u2022 Blog Migration from TypePad to Amazon\n- Amazon owns a blog omnivoracious.com which is used to improve the book sales in\namazon.com. It was\nrecently hosted in a 3rd party blogging platform TypePad.\n- The project required to find a platform which satisfies all the requirements and transfer all\nTypePad content\nto the Amazon-owned platform maintaining the functionalities and compatibility.\n- Used Velocity Templating Language, Java, Amazon Tools and Many Blogging Platforms.\n\u2022 Mobile Widget Disparity\n- Due to some recent changes in Amazon configuration, there was a content disparity between\nbook\ndescription of mobile and desktop in millions of products.\n- The project required to find the reason for disparity and then find a solution that doesn\u2019t affect\nother\nfeatures & is also expandable.\n- Used Java, configuration files, and Amazon Tools.\n\u2022 Physical to Amazon Video Conversion\n- To promote Amazon Video Over Physical Video source (DVD, Blu Ray), certain features were\ncreated like\nPopup Window and messages above Add to Cart button.\n- This project required to create UI for each of the features and check whether it satisfies all the\ncriteria.\n- Used Java, JSP, configuration files and bean creation.\n\nhttps://www.indeed.com/r/Shreyanshu-Gupta/6bd08d76c29d63c7?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Shreyanshu-Gupta/6bd08d76c29d63c7?isid=rex-download&ikw=download-top&co=IN\n\n\nEDUCATION\n\nB.Tech in Computer Science\n\nKalinga Institute of Industrial Technology\n\nJuly 2013 to April 2017\n\nSKILLS\n\nANDROID (Less than 1 year), Git (Less than 1 year), HTML (Less than 1 year), PHP (Less than\n1 year), SQL (Less than 1 year), Java (Less than 1 year), C, Velocity (Less than 1 year), C++,\nDatabase Management (Less than 1 year)\n\nLINKS\n\nhttps://www.linkedin.com/in/shreyanshu-gupta-135176103/\n\nCERTIFICATIONS/LICENSES\n\nOnline Content Writer\n\nJuly 2016 to Present\n\nWas Online Content Writer for Greymeter.com.\n\nAndroid Application Development\n\nJune 2015 to Present\n\nLearned to create basic Android Apps using Eclipse and Android Studio.\n\nPUBLICATIONS\n\nBlog Migration from TypePad to Amazon\n\nhttps://www.amazonbookreview.com\n\nJuly 2017\n\nAmazon owns a blog amazonbookreview.com which is used to improve the book sales in\namazon.com. It was recently hosted in a 3rd party blogging platform TypePad.\n- The project required to find a platform which satisfies all the requirements and transfer\nall TypePad content to the Amazon owned platform while maintaining its functionalities and\ncompatibility.\n- Used Velocity Templating Language, Java, Amazon Tools and Many Blogging Platforms.\n\nMobile Widget Disparity\n\nJuly 2017\n\nhttps://www.linkedin.com/in/shreyanshu-gupta-135176103/\nhttps://www.amazonbookreview.com\n\n\n- Due to some recent changes in Amazon configuration files, there was content disparity between\nbook description of mobile and desktop in millions of products.\n- The project required to find the reason for disparity and then find a solution that doesn\u2019t affect\nother features & is also expandable.\n- Used Java, configuration files and Amazon Tools.\n\nPhysical to Amazon Video Conversion\n\nJuly 2017\n\n- To promote Amazon Video Over Physical Video source (DVD, Blu Ray), certain features were\ncreated like Popup Window and messages above Add to Cart button.\n- This project required to create UI for each of the features and check whether it satisfies all the\ncriteria.\n- Used Java, JSP, configuration files and bean creation\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\n\n* Programming language: C, SQL, Core Java, Android, Velocity\n* Web Development - PHP, Basic knowledge of HTML\n* Basic knowledge of GIT Version Control System", "labels": []}
{"id": 23, "text": "Shrishti Chauhan\nHave total work experience of 2.5 years on Oracle Fusion Middleware -\nSOA, WebLogic and MFT Module.\n\nBilaspur, Chhattisgarh - Email me on Indeed: indeed.com/r/Shrishti-\nChauhan/89d7feb4b3957524\n\nSeeking to hone and enhance my technical skills in Oracle Fusion Middleware while working as\na professional in challenging and goal oriented environment.\n\nWilling to relocate to: Bengaluru, Karnataka\n\nWORK EXPERIENCE\n\nTechnical Consultant\n\nOracle -  Bengaluru, Karnataka -\n\nOctober 2015 to Present\n\n\u2022 Have total work experience of 2.5 years on Oracle Fusion Middleware - SOA, WebLogic and\nMFT Module.\n\n\u2022 Have extensively worked on Support, Testing, Cloning, Monitoring and Maintenance support\nand Enhancement for the E-Commerce Project with multi system module.\n\n\u2022 Have good understanding on End-to-End Business Process.\n\n\u2022 Experience in developing and deploying BPEL Processes using technology adapters (DB\nAdapter, File Adapter, FTP Adapter and JMS Adapter).\n\n\u2022 Part of a team for developing a BPEL process to integrate Oracle Fusion Applications. This\nOrchestrated BPEL Process had process activities like data conversion, transformation and fault\nhandling.\n\n\u2022 Developed and deployed BPEL processes to import sales order and add lines to the sales order\nimported from external sources (Files and Databases) into Order Orchestration Module.\n\n\u2022 Worked on BPEL process to create sales order within Order Orchestration Module using SOAPUI\nand Enterprise Manage.\n\n\u2022 Have good understanding on Synchronous and Asynchronous processes, Transformations, XSD,\nXSLT and XPath\n\n\u2022 Installation of Middle tier application server (SOA Suite 11g), configuring and deploying\napplication adapters and integrating with other ERP or 3rd Party Services.\n\n\u2022 Working with the client team and communicating with the business teams regarding integration\nwith external Delivery System.\n\nhttps://www.indeed.com/r/Shrishti-Chauhan/89d7feb4b3957524?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Shrishti-Chauhan/89d7feb4b3957524?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 As an Oracle Technical Consultant I was responsible for providing End-to-End support in the\nproject for Oracle Fusion Middleware.\n\n\u2022 Good understanding of Service Oriented Architecture (SOA) with the middleware technologies\nfor application integration.\n\nTechnical Consultant\n\nOracle\n\nHave total work experience of 2.5 years on Oracle Fusion Middleware - SOA, WebLogic and MFT\nModule.\n\nEDUCATION\n\nC.S.\n\nCHHATTISGARH SWAMI VIVEKANANDA TECHNICAL UNIVERSITY\n\n2011 to 2015\n\nSKILLS\n\nXml, Oracle MFT, Core Java, Oracle SOA, WSDL, ODI\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILL SET\nERP Packages Oracle Fusion Middleware and Oracle Fusion Application (DOO/GOP)\nFusion Middleware Modules Oracle SOA, MFT and Web Logic (11G and 12C Version)\nAdapters Database, JMS, FTP and File\nTools JDeveloper, Enterprise Manager, Administrative Console, SOAP UI and BI Publisher\nProgramming Languages XML, C++ and Core Java\nDatabases SQL\nOperating System Unix and Windows\n\nPROJECT DETAILS\nProject Client HOLTS RENFREW\nRole & Duration Technical Consultant Oct-15\nScope of Project Oracle Fusion Middleware 11G and Oracle Fusion Application R12\nProject Description\nHolt Renfrew is 180 years old chain of high-end Canadian department stores specializing in an\narray of luxury brands and designer boutiques.\n\nResponsibilities\n\u2022 Worked as Technical Consultant to the customer for the issues related to Oracle SOA (mainly\nBPEL and Mediator Components).\n\u2022 Providing daily Server Health Check for SOA and DOO/GOP (SCM) to the customer.\n\u2022 Attending/Conducting weekly customer calls and providing an update about the progress of\nthe issues.\n\n\n\n\u2022 Interacting with the Clients to understand the requirement and details of the issues and\nproviding suggestions/answers to their queries.\n\u2022 Resolving technical issues and enhancement raised by the customer.\n\u2022 Reviewing work products from the Team Members and delivering products to the client on time\nwith high quality.\n\u2022 RCA for the Complex Issues in client's version of Oracle SOA and SCM.\n\u2022 Partially worked on functional issues related to DOO/GOP - Order Management and Inventory\nManagement.\n\u2022 Working effectively with Oracle Product Support for any issue related to Standard Functionality.\n\nProject Client WHITBREAD PLC\nRole & Duration Technical Consultant SEPT-17\nScope of Project Oracle Fusion Middleware 12C and Oracle SAAS\nProject Description\nWhitbread PLC is the UK's largest hospitality company, owning Premier Inn and Costa Coffee, as\nwell as Beefeater, Brewers Fayre, and Bar Block.\n\nResponsibilities\n\u2022 Worked as Technical Consultant to the customer for the issues related to Oracle SOA and MFT.\n\u2022 Monitoring Critical Process and fixing the concurrent process running on SOA, MFT and ODI.\n\u2022 Worked on developing and executing Integration Regression Test Script.\n\u2022 Developing Application Understanding Documents for the Standard functionalities.\n\u2022 Interacting with the Clients to understand the requirement and details of the issues and\nproviding suggestions/answers to their queries.\n\u2022 Resolving technical issues and enhancement raised by the customer.\n\u2022 Reviewing work products from the Team Members and delivering products to the client on time\nwith high quality.\n\u2022 RCA for the Complex Issues in client's version of Oracle SOA and SCM.\n\u2022 Working effectively with Oracle Product Support for any issue related to Standard Functionality.\n\u2022 Co-ordinate internally with various teams, executives in providing the decisions to various\nfactors influencing the project implementation", "labels": []}
{"id": 24, "text": "Shubham Mittal\nSystem Engineer - Infosys Limited\n\nMysore, Karnataka - Email me on Indeed: indeed.com/r/Shubham-Mittal/4b29ab0545b0f67f\n\n\u2022 Having 2.0 Years of Experience as a Software Developer (System Engineer) in the IT industry.\n\u2022 Presently working as Senior System Engineer.\n\u2022 Functionally Good with Complete Order fulfillment's journey including Sales and service Cloud.\n\u2022 Expertise in implementing SQL, SOQL, SOSL, HTML, JavaScript, CSS, Salesforce (APEX, VF pages,\nothers Components)\n\u2022 Handling Deployment to Production Via CI/ANT Migration Tools.\n\u2022 Good Implementation Knowledge of Lighting, Data Loader.\n\u2022 Hands-on in Marketing cloud.\n\u2022 Good Implementation Knowledge in SOAP Web service.\n\u2022 Good Knowledge in AGILE Methodology and JIRA Tool.\n\u2022 Certified as Platform developer-1.\n\u2022 Currently working on SALESFORCE/CLOUDSENSE technology and pursuing SUPERBADGE in\nAPEX and LIGHTNING Concepts.\n\nWORK EXPERIENCE\n\nSystem Engineer\n\nInfosys Limited -\n\nJanuary 2017 to Present\n\nEnvironment: Salesforce, Cloud Sense, APEX, SOQL, GIT, Bit Bucket, AGILE\n\nResponsibilities:\n\u2022 Interaction with the onshore team to understand the requirement as per clients and functional\nrequirement document.\n\u2022 Preparation of solution document, component list and high level estimation of the tasks created\nunder sprints.\n\u2022 Technical Refinement's for User stories.\n\nContributions:\n\u2022 Developed and integrated the different modules developed by other team members.\n\u2022 Wrote the Tech specifications document, Created Class diagrams and Flow Diagram using\nfunctional requirement document.\n\u2022 Developed and deployed Lightening components.\n\u2022 Created Apex class, VF pages, Workflows, process builder, approval Process and other\ncomponents.\n\u2022 Integration of Sales cloud with other third Party vendors.\n\u2022 Designed Solution Documentations.\n\u2022 Deployments to Production and other Intermediate environments.\n\u2022 Support in SIT testing and production issues.\n\nSystem Engineer\n\nhttps://www.indeed.com/r/Shubham-Mittal/4b29ab0545b0f67f?isid=rex-download&ikw=download-top&co=IN\n\n\nInfosys Limited -  Mysore, Karnataka -\n\nJune 2016 to Present\n\nEDUCATION\n\nB.Tech in EEE\n\nNational Institute Of Technology\n\n2016\n\nSt. John's School\n\n2009\n\nSKILLS\n\nHtml, Css, Javascript, Salesforce", "labels": []}
{"id": 25, "text": "Sivaganesh Selvakumar\nDevOps Consultant with Infosys Limited\n\nChennai, Tamil Nadu - Email me on Indeed: indeed.com/r/Sivaganesh-\nSelvakumar/2d20204ef7c22049\n\nConsultant with 5.8 years of experience DevOps design , CICD implementation, Build and\ndeployment automation and Release Management.\n\nWORK EXPERIENCE\n\nDevOps Consultant\n\nInfosys Limited -\n\nDecember 2016 to Present\n\nResponsibilities:\n\u2022 Ownership of Release Management process for various web applications (Java based)\n\u2022 Involve setting the overall Delivery Automation strategy, via investments in automation at\nseveral layers of the Technology stack to implement a Continuous Deployment/Delivery pipeline.\n\u2022 Designed and delivered CloudBees Jenkins pipeline jobs with groovy code and pipeline plugin\nfor Continuous Integration and Deployments\n\u2022 Implemented a continuous delivery pipeline with CloudBees Jenkins and Scripting.\n\u2022 Manage and troubleshoot the running of automated jobs in Jenkins, TFS to support and\nstreamline the release process.\n\u2022 Integrated Code quality, Code coverage, Unit Tests, Functional Tests with CI pipeline and\nresolved issues in this process.\n\u2022 Organize and coordinate release teams across organizational boundaries.\n\u2022 Deal with release to Dev, SIT, QA and Prod including business sign offs.\n\u2022 Create/Submit applications and required associated documentation to 3rd parties for third party\nacceptance/approval testing.\n\u2022 Configure and integrate Nexus and Artifactory Repository for builds and integrating it with the\nCICD orchestration tool like Jenkins, TFS, and Bamboo.\n\u2022 Develop PowerShell scripts to upload artifacts to Artifactory through REST API's and automated\nthis as part of DevOps pipeline\n\u2022 Document knowledge articles in Confluence (Internal Wiki)\nEnvironment: TFS ( GIT - SCM), TFS Builds, CloudBees Jenkins, ANT, MAVEN, Nexus, Shell Scripts,\nUNIX, JIRA, , Microsoft Visual Studio 2015, Octopus,\n\nTechnology Analyst\n\nInfosys Limited -  Chennai, Tamil Nadu -\n\nJuly 2011 to Present\n\nEDUCATION\n\nBachelor in \"Instrumentation and Control Engineering\"\n\nhttps://www.indeed.com/r/Sivaganesh-Selvakumar/2d20204ef7c22049?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Sivaganesh-Selvakumar/2d20204ef7c22049?isid=rex-download&ikw=download-top&co=IN\n\n\nSaranathan College of Engineering -  Tiruchchirappalli, Tamil Nadu\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS:\nPlatforms Windows, Unix, Linux\nScripting Language PowerShell scripting, Shell scripting.\nDatabases Oracle, SQL Server\nBuild Tools Maven, Ant, MSBuild, Nant\nCICD Tools CloudBees Jenkins, TFS, GIT, SVN, Octopus, Jira, , Sonarqube, MSTest, Nexus,\nArtifactory, Cobertura, Ansible", "labels": []}
{"id": 26, "text": "Snehal Jadhav\nMumbai, Maharashtra - Email me on Indeed: indeed.com/r/Snehal-Jadhav/005e1ab800b4cb42\n\nWORK EXPERIENCE\n\nL1 network engineer\n\nCisco -  Mumbai, Maharashtra -\n\nFebruary 2017 to Present\n\nassociated with Cisco on the role of ACESOFTLABS (INDIA) Pvt. Ltd\n\nEDUCATION\n\nHSC\n\nMaharashtra Board\n\nMarch 2012\n\nSSC\n\nMaharashtra Board\n\nMarch 2010\n\nB.E in Electronics & Telecommunication\n\nShivaji University\n\nSKILLS\n\nArchitecture (Less than 1 year), BGP (Less than 1 year), DHCP (Less than 1 year), DNS (Less\nthan 1 year), EIGRP (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTechnical Skills\n\n\u2022 Layer 2 Technologies like VTP, VLAN, STP, and RSTP.\n\u2022 OSI Architecture, TCP/IP Module\n\u2022 TCP/UDP, DHCP, DNS\n\u2022 MP-BGP IPv4/IPv6, VPNv4, VPNv6, RT-Filter, IPv6-Multicats.\n\u2022 Access-List, Prefix-List, Distribution List, QOS.\n\u2022 MPLS, L2/L3 VPN.\n\u2022 GRE, IPSec, DMVPN.\n\u2022 FTP-Active/Passive, TFTP.\n\u2022 Configuring and Troubleshooting L2 and L3 Ether channels on Cisco 3550, 3560, 2950 switches.\n\u2022 IP Addressing, VLSM, Summarization, understanding Lease Line and Dedicated Lease Lines.\n\nhttps://www.indeed.com/r/Snehal-Jadhav/005e1ab800b4cb42?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Configuring HSRP, VRRP, GLBP on Cisco Routers and Switches.\n\u2022 VTP & Frame Tagging protocols ISL, Dot1q\n\u2022 NAT, PAT, NAT-T.\n\u2022 Strong skills in Configuring Cisco Routers (ASR901, ASR903, ASR900) using ISIS, OSPF, EIGRP\nand BGP. A good understanding of working on Small to High E1nd Routers and Switches.\n\u2022 Comprehensive understanding of networking concepts pertaining to LAN, WAN, Security, IT\ncommunication, WAN protocols, Networking devices administration and maintenance in multi-\nplatform environments.\n\u2022 Strong hands-on technical knowledge of Microsoft Operating Systems (Network & Desktop),\nestablishing & managing networks, Cisco Routers, Hardware clients & switches.\n\u2022 Expertise in managing medium to large networks with Routers, Switches.", "labels": []}
{"id": 27, "text": "Soumya Balan\nIT SUPPORT\n\nSulthan Bathery, Kerala, Kerala - Email me on Indeed: indeed.com/r/Soumya-\nBalan/97ead9542c575355\n\n\u27a2 To work in a progressive organization where I can enhance my skills and learning to contribute\nto the success of the organization.\n\nWORK EXPERIENCE\n\nTechnical support engineer\n\nMicrosoft\n\nPosition: TECHNICAL SUPPORT ENGINEER\n\nCompany: Microsoft Corporation - Microsoft India Global Technical Support Center (Microsoft\nIGTSC), Bangalore\n\nYears of Experience: 2 Years and 4 Months\n\nResponsibilities\n\n\u27a2 Represent Microsoft and communicate with corporate customers via telephone, written\ncorrespondence, or electronic service regarding technically complex escalated problems\nidentified in Microsoft software products, and manage relationships with those customers.\n\n\u27a2 Manage not only the technically complex problems, but also politically charged situations\nrequiring the highest level of customer skill.\n\n\u27a2 Receive technically complex, critical or politically hot customer issues, and maintain ownership\nof issue until resolved completely.\n\n\u27a2 Solve highly complex problems, involving broad, in-depth product knowledge or in-depth\nproduct specialty.\n\n\u27a2 Use trace analysis, and other sophisticated tools to analyze problems and develop solutions\nto meet customer needs.\n\n\u27a2 Lead triage meetings to share knowledge with other engineers and develop customer solutions\nefficiently.\n\n\u27a2 Act as technical lead, mentor, and model for a team of engineers; provide direction to others,\nreview solutions and articles, mentoring existing & aspiring Engineers.\n\n\u27a2 Write technical articles for knowledge base.\n\n\u27a2 Consult, collaborate and take escalations when necessary.\n\nhttps://www.indeed.com/r/Soumya-Balan/97ead9542c575355?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Soumya-Balan/97ead9542c575355?isid=rex-download&ikw=download-top&co=IN\n\n\n\u27a2 Maintain working knowledge of pre-release products and take ownership for improvement in\nkey technical areas.\n\n\u27a2 Manage customer escalations and recognize when to solicit additional help.\nParticipate in technical discussions and engage with product team if required to resolve issues\nand represent customer segments.\n\nExchange Server Knowledge\n\n\u27a2 Exchange Server 2007\n\u27a2 Exchange Server 2010\n\u27a2 Exchange Server 2013\n\u27a2 O365\n\nUG PROJECT TITLE: Memory Bounded Anytime Heuristic Search A* Algorithm\n\n\u27a2 This Project presents a heuristic-search algorithm called Memory-bounded Anytime Window\nA*(MAWA*), which is complete, anytime, and memory bounded. MAWA* uses the window-\nbounded anytime-search methodology of AWA* as the basic framework and combines it with the\nmemory-bounded A* -like approach to handle restricted memory situations.\nSimple and efficient versions of MAWA* targeted for tree search have also been presented.\nExperimental results of the sliding-tile puzzle problem and the traveling-salesman problem show\nthe significant advantages of the proposed algorithm over existing methods.\n\nTechnical and Co-Curricular activities\n\n\u27a2 Star Performer in Microsoft IGTSC in 2014.\n\u27a2 Paper Presentations on Applications of Robotics in INOX 2K12.\n\u27a2 Attended a Three-Day workshop on C and C++ Programming and Aliasing.\n\u27a2 Attended a One-Day workshop on Java and Hardware Workshop at VECW\n\u27a2 Paper presentation 4G Technologies, Cloud Computing, Heuristic Algorithms and Applications,\nOpen Source Software.\n\u27a2 Multimedia presentations on Artificial Intellegence, 6th Sense, and Robotics.\n\u27a2 Completed training of OCA (9i, 10g) from Oracle University.\n\u27a2 Attended SPARK training program in Infosys Mysore.\n\u27a2 Attended System Hardware Training program at HCL, Pondicherry.\n\nEDUCATION\n\nBE in Computer Science and Engineering\n\nVivekananda Engineering College for Women -  Chennai, Tamil Nadu\n\n2013\n\nBTEC HNC in Aviation in Hospitality and Travel Management\n\nFrankfinn Institute of Airhostess Training -  Calicut, Kerala\n\n\n\n2008\n\nState Board\n\n2007\n\nSKILLS\n\nLinux (Less than 1 year), Microsoft Office (Less than 1 year), MS OFFICE (Less than 1 year),\nproblem solving (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSkill Set\n\u27a2 Excellent communication and interpersonal skills.\n\u27a2 Proficient in Computer Applications -Microsoft Office Windows (Windows 2007, XP, 8, 8.1 and\nWindows 10), Linux, Fedora.\n\u27a2 Strong analytical and problem solving skills.\n\u27a2 Ability in managing a team of professionals and enjoy being in a team.", "labels": []}
{"id": 28, "text": "Soumya Balan\nSoumya Balan - BE Computer Science - 3 yr Work Experience at\nMicrosoft Corporation\n\nThiruvananthapuram, Kerala - Email me on Indeed: indeed.com/r/Soumya-\nBalan/8c7fbb9917935f20\n\n\u27a2 To work in a progressive organization where I can enhance my skills and learning to contribute\nto the success of the organization.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nTechnical Support Engineer\n\nMicrosoft iGTSC -  Bengaluru, Karnataka -\n\nJuly 2013 to October 2015\n\nPosition: TECHNICAL SUPPORT ENGINEER\n\nCompany: Microsoft Corporation - Microsoft India Global Technical Support Center (Microsoft\nIGTSC), Bangalore\n\nYears of Experience: 2 Years and 4 Months\n\nResponsibilities\n\n\u27a2 Represent Microsoft and communicate with corporate customers via telephone, written\ncorrespondence, or electronic service regarding technically complex escalated problems\nidentified in Microsoft software products, and manage relationships with those customers.\n\n\u27a2 Manage not only the technically complex problems, but also politically charged situations\nrequiring the highest level of customer skill.\n\n\u27a2 Receive technically complex, critical or politically hot customer issues, and maintain ownership\nof issue until resolved completely.\n\n\u27a2 Solve highly complex problems, involving broad, in-depth product knowledge or in-depth\nproduct specialty.\n\n\u27a2 Use trace analysis, and other sophisticated tools to analyze problems and develop solutions\nto meet customer needs.\n\n\u27a2 Lead triage meetings to share knowledge with other engineers and develop customer solutions\nefficiently.\n\n\u27a2 Act as technical lead, mentor, and model for a team of engineers; provide direction to others,\nreview solutions and articles, mentoring existing & aspiring Engineers.\n\nhttps://www.indeed.com/r/Soumya-Balan/8c7fbb9917935f20?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Soumya-Balan/8c7fbb9917935f20?isid=rex-download&ikw=download-top&co=IN\n\n\n\u27a2 Write technical articles for knowledge base.\n\n\u27a2 Consult, collaborate and take escalations when necessary.\n\n\u27a2 Maintain working knowledge of pre-release products and take ownership for improvement in\nkey technical areas.\n\n\u27a2 Manage customer escalations and recognize when to solicit additional help.\nParticipate in technical discussions and engage with product team if required to resolve issues\nand represent customer segments.\n\nExchange Server Knowledge\n\n\u27a2 Exchange Server 2007\n\u27a2 Exchange Server 2010\n\u27a2 Exchange Server 2013\n\u27a2 O365\n\nEDUCATION\n\nBE in Computer Science and Engineering\n\nVivekananda Engineering College for Women -  Chennai, Tamil Nadu\n\n2013\n\nBTEC HNC in Aviation\n\nFrankfinn Institute of Airhostess Training -  Calicut, Kerala\n\n2008\n\nState Board +2\n\n2007\n\nSSLC\n\nState\n\n2005\n\nSKILLS\n\nDBMS, O365, Communication Skills, Exchange 2013, Hospitality, Networking, Computer\nOperating, Programming, Computer Hardware, Java, Exchange 2010, Teaching\n\nADDITIONAL INFORMATION\n\nSkill Set\n\u27a2 Excellent communication and interpersonal skills.\n\n\n\n\u27a2 Proficient in Computer Applications -Microsoft Office Windows (Windows 2007, XP, 8, 8.1 and\nWindows 10), Linux, Fedora.\n\u27a2 Strong analytical and problem solving skills.\n\u27a2 Ability in managing a team of professionals and enjoy being in a team.\n\nProject Details\n\nUG PROJECT TITLE: Memory Bounded Anytime Heuristic Search A* Algorithm\n\n\u27a2 This Project presents a heuristic-search algorithm called Memory-bounded Anytime Window A*\n(MAWA*), which is complete, anytime, and memory bounded. MAWA* uses the window-bounded\nanytime-search methodology of AWA* as the basic framework and combines it with the memory-\nbounded A* -like approach to handle restricted memory situations.\nSimple and efficient versions of MAWA* targeted for tree search have also been presented.\nExperimental results of the sliding-tile puzzle problem and the traveling-salesman problem show\nthe significant advantages of the proposed algorithm over existing methods.\n\nTechnical and Co-Curricular activities\n\n\u27a2 Star Performer in Microsoft IGTSC in 2014.\n\u27a2 Paper Presentations on Applications of Robotics in INOX 2K12.\n\u27a2 Attended a Three-Day workshop on C and C++ Programming and Aliasing.\n\u27a2 Attended a One-Day workshop on Java and Hardware Workshop at VECW\n\u27a2 Paper presentation 4G Technologies, Cloud Computing, Heuristic Algorithms and Applications,\nOpen Source Software.\n\u27a2 Multimedia presentations on Artificial Intellegence, 6th Sense, and Robotics.\n\u27a2 Completed training of OCA (9i, 10g) from Oracle University.\n\u27a2 Attended SPARK training program in Infosys Mysore.\n\u27a2 Attended System Hardware Training program at HCL, Pondicherry.", "labels": []}
{"id": 29, "text": "Sowmya Karanth\nFinance Analyst\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Sowmya-Karanth/\na76c9c40c02ed396\n\nTo leverage 1+ years experience in order management and build up the core financial techniques\nand\nensure the better growth of the organisation by providing dedicated and on time delivery of the\njob.\n\nWilling to relocate to: Bengaluru, Karnataka - hyderbad, Telangana - Chennai, Tamil Nadu\n\nWORK EXPERIENCE\n\nFinance Analyst\n\nOracle India Pvt ltd -\n\nMay 2015 to March 2017\n\n* Recruited as Finance analyst for NAMER ( North Americas') region and worked for a year.\n\n* Achieved 'YAR (You Are Recognised) ' Award for the month of August 2015 and was given the\ntitle\n\"Young and early achiever\".\n\n* From June 2016 to September 2016 was trained and worked for EMEA ( Middle Eastern) regions\nand was the mentor for new joiners and trainer for new managers.\n\n* From September 2016 due to regional expansion was trained for APAC ( Asian & Pacific) regions\nand was given the title 'Global Resource' due to my constant & extensive support for the other\ntwo\nregions.\n\n* Performed two major projects 'LLC ( License Learning Cycle) ' and 'Global Review tool' and was\nthe lead and global reviewer in both.\n\n* Performed various extensive analysis to globalise the process like standardising the differences\nin the process flow and concepts between regions.\n\nEDUCATION\n\nMBA in Finance and sectorial specialisation\n\nAvinashilingam Institute for Home Science & Higher Education for Women\n\n2013 to 2015\n\nFrench\n\nhttps://www.indeed.com/r/Sowmya-Karanth/a76c9c40c02ed396?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Sowmya-Karanth/a76c9c40c02ed396?isid=rex-download&ikw=download-top&co=IN\n\n\nS.B.K.V higher secondary school\n\n2010\n\nAvinashilingam Deemed University -  Coimbatore, Tamil Nadu\n\nSKILLS\n\nEMPLOYEE RESOURCE GROUP (Less than 1 year), ENTERPRISE RESOURCE PLANNING (Less than\n1 year), ERP (Less than 1 year), Microsoft office (Less than 1 year), MS OFFICE (Less than 1\nyear)\n\nADDITIONAL INFORMATION\n\n/INTERESTS\n\n* Computer skills - Microsoft office, ERP, Oracle SQL", "labels": []}
{"id": 30, "text": "Srabani Das\nsoftware engineer - Oracle, Business Objects\n\nBishnupur, MANIPUR, 722122, IN - Email me on Indeed: indeed.com/r/Srabani-\nDas/152269fb5b986c26\n\nTo secure a challenging position where I can effectively contribute my skills as Software\nProfessional which will boost my career and can be effectively achieve company objective.\n\nWORK EXPERIENCE\n\nsoftware engineer\n\nOracle, Business Objects -\n\nJune 2015 to Present\n\n\u2022 A highly accomplished professional with 2.8 years of IT experience as a software developer\nworking with technologies like Teradata, Oracle, Business Objects.\n\u2022 Working as software engineer with Apple client in Exilant Technologies Pvt. Ltd. from June 2015\nto till date.\n\u2022 Working in multiple domains like Retail and Concierge.\n\nEDUCATION\n\nB-tech in Electronics and Telecommunication\n\nCollege of engineering -  Bhubaneshwar, Orissa\n\n2015\n\nCBSE\n\nODM Pubic school -  Bhubaneshwar, Orissa\n\n2011\n\nCBSE\n\nGreen-Field school\n\n2009\n\nSKILLS\n\nRETAIL (2 years), RETAIL MARKETING (2 years), TERADATA (2 years), ACCEPTANCE TESTING\n(Less than 1 year), APS (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS\n\nhttps://www.indeed.com/r/Srabani-Das/152269fb5b986c26?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Srabani-Das/152269fb5b986c26?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Databases (Primary): Teradata, Oracle (SQL, PL/SQL)\n\u2022 OS: Unix, Windows, Mac OS\n\u2022 Teradata Tools & Utilities: BTEQ, Muti Load, Fast Load, Tpump, TPT.\n\u2022 Reporting Tools: BO Reporting, Crystal Report, Universe design tool, BOBJ Migration tool\n\u2022 Other Tools: Radar, Espresso, SQL Workbench, SQL Developer, ETL Metadata UI, iCheck, Global\ndeployment tool, Ms office, Power Point, Ms Excel, Workload Automation.\n\u2022 Applications: Central Station, GitLab, SVN.\nKEY PROJECTS:\n\nLeaderboard\nProject Leaderboard\nClient Apple\nDescription\n- Leader Board is an iPad based application for single Point of entry to GBI Retail mobile\napplications. It is to provide automated and centralized access to different apps. Its a location to\nget key actionable metrics around Sales & Services related transactions of Apple retail store. It\nis widely used by all managers and employees in Apple stores.\n\n- This is a diverse application which has 4 sub applications:\nStore Pulse, Benchmark, Session and RedZone Mobile.\n- Real-time as well as historical data are displayed in this application.\n\nPeak Team Size 6\nRoles and Responsibility\n- Working as a database developer in this project.\n\n- Worked in complete revamp of Leader board application and done multiple enhancements to\ndevelop logic for multiple sales and service related metrics.\n\n- Created design documents and Logical Data flow Model from source study according to Business\nrequirements.\n\n- Created multiple Replication setup process (Export and Load) using Apple ETL framework tool.\n\n- Worked on Business Objects, SAP Crysral report and universe for report creation and\nmodifcation.\n\n- Coding and Support for all phases of Testing\n\n- Implementation in production for project go live.\n\n- Production support till warranty phase.\n\n- Transition KT to APS team.\n\nTechnology Used Teradata, Oracle, BO Universe and Reporting, Crystal Report, Unix and Autosys,\nETL Framework.\nReporting Tools Used Business Objects, BOBJ Migration tool, Universe design tool, SAP Crystal\nReport\n\n\n\nApple Retail Expansion\nProject Apple Retail Expansion\nClient Apple\nDescription\n- As part of New Apple Store/Country Expansion, Store/Market/Country level metadata setups are\ndone with real-time and history performance reports are sent to business users periodically.\n\nPeak Team Size 3\nRoles and Responsibility\n- Involved in Designing technical specification docs and gathering functional requirements.\n\n- Carried out DB changes alone with Unit, Integration, Functional, Regression testing and test\ncase preparation of all the modules impacted for it.\n\n- Implemented Reporting side changes for new Store expansion.\n\nTechnology Used Teradata, Oracle, BO Reporting, Crystal Report, Unix and Autosys, ETL\nFramework.\nReporting Tools Used Business Objects, SAP Crystal Report\n\nGenius & Forum Dashboard\nProject Genius & Forum Dashboard\nClient Apple\nDescription\n- This Dashboards acts as a reporting solution for Concierge - the service oriented wing of Apple.\n- Genius and Forum Dashboard has been developed as the reporting layer to report the Genius\nBar, Workshops/events, Repair related metrics.\n- Helps the store leaders to take decisions on the floor to improve customer experience analyzing\nthe trend of the store.\n- Store leaders can plan for scheduling of employees based on the demand of symptoms/\ncategories.\n\nPeak Team Size 6\nRoles and Responsibility\n- Understanding the functional requirement\n- Design Document preparation.\n- Coding and Units testing.\n- Preparing test cases and conduct Integration Testing\n- Support for User Acceptance Testing\n- Implementation\n- Support\n\nTechnology Used Teradata, Oracle, Unix and Autosys, ETL Framework.\nReporting Tools Used Business Objects, BOBJ Migration tool, Universe design tool", "labels": []}
{"id": 31, "text": "Srinivas VO\nSr. Test Manager\n\nMumbai, Maharashtra - Email me on Indeed: indeed.com/r/Srinivas-VO/39c80e42cb6bc97f\n\nA Test Manager, with a track record of 15+Yrs ( 4yrs UK onsite) delivering major test solutions\nfor global projects ($40m) on behalf of leading blue chip organisations. Delivering IT solutions,\nranging from simple to complex and challenging projects and programs, establishing an enviable\nrecord of on-time, high quality & added value delivery.\n\u25cf Testing capabilities to existing customers and prospective customers during client visits / at\ncustomer location.\n\u25cf Own and Support RFI/RFPs, proposal walkthroughs and presentations and Transition\nknowledge from pre-sales to delivery, in case of project win\n\u25cf Analyze proposal requirements in direct relation with clients, and provide innovative\nsolutions, as part of proposals\n\u25cf Develop proof of concepts to prospects during pre-sales phase, Provide test consulting\nservices, on demand & Collate repository from delivery team, along with case studies for use in\nproposals, presentations and consulting\n\u25cf Pipeline building through new opportunities, cross-selling, up-selling and Establish\ncommunication with Geo Sales and other streams of business\n\u25cf Maintain Order Book, Win /Loss analysis, pre-sales metrics\n\u25cf Handle any technical question or issue which arises during a sales cycle and setting\nappropriate customer expectations.\n\u25cf Independently executes strategic leadership to others in identifying opportunities and Expert\nin driving pilots/proof of concepts.\n\u25cf Talented in handling extremely risk and competitive situations and responsible for product\nand services, revenue goals at team and regional level.\n\u25cf Proposed and implemented TCoE setup for 2 major clients, which helped in improving the\nproductivity by reducing the staffing and operational cost by 25%.\n\u25cf Involved in setup for Custom Application Testing Services (Oracle R12, Siebel, Fusion etc., )\nin Oracle SSI & Testing Centre of Excellence (TCoE)\n\u25cf Ability to excite customers, generate awareness of new possibilities that can yield additional\nrevenue.\n\u25cf Planning, scheduling and tracking of the project modules and conducted trainings across\nteams and Experience in proposals for the projects, resource planning and estimations.\n\u25cf Extensive experience on bid management, PMO process, risk management and prepared\nproject management office documents.\n\u25cf Expertise in building automation frameworks for Front to back testing of Retail / Healthcare/\nTelecom/ BFSI/Supply Chain Trading applications\n\u25cf Expertise in design, development and implementation of frameworks using UNIX, Java, .Net,\nVB, SQL, XML, SWIFT, MQ and FMW.\n\u25cf Experience in E2E testing of variety of Oracle Applications:\n\u25cf Oracle Retail: Oracle RMS, RPM, REIM, SIM etc.\n\u25cf Oracle Ebusiness Suite R12: AP, AR, GL, OM, PA, CM, TM\n\u25cf Oracle IDM: OAM, OIM, OVD, OID, SSO\n\u25cf Oracle Siebel: Public Sector, Call Center Apps, Ebilling Apps\n\u25cf Oracle Fusion Applications: Fusion HCM, R12, OBIEE\n\u25cf Oracle Transportation Management: Order Management,\nShipments, Transport Request management, Routing and Consolidation\n\nhttps://www.indeed.com/r/Srinivas-VO/39c80e42cb6bc97f?isid=rex-download&ikw=download-top&co=IN\n\n\n\u25cf Oracle FlexCube & Oracle Health Sciences: Oracle\u00ae Clinical\n\u25cf Oracle Communications: Billing and Revenue Management\n(BRS), Order Management and Order Fulfillment (OMOF)\n\u25cf Experience in managing large diversified team across geographies\n\u25cf Experience includes customization of methodologies for the business needs and deploying\nthem\ninto programs/projects\n\u25cf Experience in proposing out of box solutions for automation and convincing clients on alternate\nsolutions and investments in automation using\n\u25cf Oracle Tools (OATS, Oracle Load Tester, Oracle Test Manager)\n\u25cf HP Tools (QTP, Load Runner, Test Director, Performance Centre)\n\u25cf Rational Suite (Reqpro, RA, Rational Robot, RFT, RPT, RCC, RCQ)\n\u25cf Open Source / Other Tools: Selenium Webdriver 2.0, Testng, JMeter,\nAppium, Junit, Cucumber, JIRA, Maven, SOAPUI, Jbehave, BDD etc.,\n\u25cf A strategic thinker, problem solver, project implementer and change leader, who has\nconsistently\nprovided organizations with added value as they move towards achieving their objectives.\n\u25cf Helped many large and small organizations to establish sound project & management practices\nintegrated into the corporate framework to deliver projects, programs and managed portfolios.\n\u25cf Ability to identify business value in customer needs and translate to innovative solutions\ncommunicating the requirements & mission, in context, to both management & IT & Quality\ndevelopment, using sound, proven and valued management practices through integrated best\nprocesses from clients and IT Governance Frameworks (RUP, CMMI and ISO)\n\nSolution Sectors\nService successes in: Logistics & SCM, Investment Banking, Public Sector, Financial, Banking,\nRetail,\nHealth Sciences, Telecom, Real Estate, Hospitality, Outsourcing, Project Management, Knowledge\nManagement & Performance Management.\n\nClientele\nDHL (UK), Mobily (Saudi Arabia), UBS (London, UK), Rolls Royce (Derby, UK), TNT (Hinckley, UK),\nRCUK (Research Councils, Swindon, UK), ADAT (Middle East), OHI (Netherlands), Citibank (USA),\nTransurban (Australia), BCBS (Blue Cross Blue Shield, USA) etc.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nQA Manager\n\nTracelink -  Mumbai, Maharashtra -\n\nApril 2017 to October 2017\n\n\u2794 Define, evangelize, and implement global, unified agile delivery processes; select\ncommon tools; specify core quality-measurement KPIs,expand automated-testing in\nContinuous Integration environment\n\u2794 Project Manager and Scrum Master of company's highest priority Tracelink Products &\nTracelink L3 Support Projects recognized by executive management as the model for\n\n\n\nproject organization and execution.\n\u2794 Manage and administer relations, budgets, and contracts with outsourcing vendor\n\u2794 Define the test strategy and approach for one or many products, provide quality\nassurance leadership, and be ultimately responsible for ensuring product quality\n\u2794 Perform hands-on job that requires strategic thinking and planning to provide leadership\nand expertise throughout the entire QA life cycle, ensuring the success of the team's\nmanual and automation efforts\n\u2794 Lead efforts to develop, document, and implement applicable QA processes and\nprocedures to provide more effective quality methods within the group in support of\nproviding quality products\n\u2794 Create, implement, maintain, and enhance test plans, test scripts, and test\nmethodologies to ensure comprehensive test coverage\n\u2794 Develop world class automation for regression and feature testing\n\u2794 Work closely with all stakeholders to ensure project alignment\n\u2794 Provide quarterly presentations to executive staff on QA accomplishments and future vision\nand goals\nEnvironment: Amazon Cloud Services (Habari, Elasticache, Cloud Search, Dynamo, RDS, Redis,\nAmazon RedShift), Java, Scala, Selenium Webdriver, Jmeter etc.,\n\nSr. Test Manager\n\nconfidential -  Bengaluru, Karnataka -\n\nJuly 2013 to March 2017\n\nTest Manager, Client\n\nOracle SSI -  London -\n\nJanuary 2011 to November 2012\n\nLondon\nDescription: Oracle will provide services to Manage process workshops with UBS to map the\nOracle\nFusion Human Capital Management (HCM) applications processes to UBS's global processes.\nImplement\nOracle Fusion HCM applications within an Oracle. Software as a Service (SaaS) environment.\nSupport\nUBS with the mapping of the Oracle Fusion Applications data structures to UBS's current\nPeopleSoft 8.8\nsystem and then design, develop and unit test data load routines. This will include the definition\nof the reconciliation process. Configure and system test the application modules to support the\nLevel 3 standard\nbusiness processes and the UBS functional requirements identified.\nResponsibilities:\nDeveloped High Level Test Strategy for UBS - Group HR Applications (Viz., Workforce\nManagement,\nRecruitment and Talent Management, Compensation management, Payrolls, Learning and\nDevelopment,\nHR Service Management)\nInvolved in Setting up Test Environments Viz., (System Testing, SIT, Conversion Testing, Security\n\n\n\nTesting, Cutover Testing, Performance Testing, UAT)\nDeveloped Master Test Plans for Oracle Fusion HCM Applications (Viz., Compensation\nManagement,\nWorkForce Management, Recruitment and Talent Management) & Non Fusion Oracle Applications\n(viz.,\nService Management)\nDeveloped High Level Test Requirements for Workforce Deployment, Workforce development,\nSystem\nTest Cases for Workforce Management, Fusion Personalization\nReview Test Scripts for Fusion HCM Applications & Non Fusion Applications\nTrack and prepare the report of testing activities like test testing results, test case coverage,\nrequired\nresources, defects discovered and their status, performance baselines etc.\nDeveloped Test Automation Strategy & Automation Framework for Fusion HCM SAAS\nApplications.\nInvolved in Setting up Test Automation Infrastructure Environment for Automation Test Assists:\na. Test project Setup, Roles and Test planning using Oracle Test Manager\n\nb. Functional Test Script Development using Oracle Open Script\n\nc. Performance Test Script Development Using Oracle Load Tester\nEnsure the timely delivery of different testing milestones.\nConducted LESS (Load Testing, Endurance Testing, Stress Testing, Spike Testing) Testing Activities\nfor\nFusion HCM SAAS Applications.\n\nTitle: WMI, Period: Oct '2010 - Dec '2011\nProject Description: The world's largest retailer implements Oracle Retail applications including\nOracle\nRetail Merchandise System, Oracle Retail Allocation and other elements of the Oracle Retail Suite\nas part of its merchandising transformation initiatives.\nResponsibilities:\n\u25cf Direct Client dealings. Getting information from client on requirement for areas where in our\nexpertise in testing can be used and giving proposal.\n\u25cf Developed Domain Level Test Strategy\n\u25cf Manage entire testing activities from Functional test preparation/execution, Integration test\npreparation/execution, System test preparation/execution\n\u25cf Prepared scheduled plan for the client and updating the same in Microsoft Project Plan effective\ntracking of progress of project.\n\u25cf Tracking of testing progress in all areas and pointing out gaps to be filled to Client and own\nresources.\n\u25cf Review daily and weekly Testers' status reports and take necessary actions and assessing the\nprogress and effectiveness of the test effort\n\u25cf Provide estimation for manual and automation testing areas.\n\u25cf Tested Oracle Retail Applications ( RMS, RPM, Allocation etc., )\n\u25cf Track Schedule and Effort deviation tracking in internal tracking tool.\n\u25cf Co-ordination of work between resources from India, USA\n\u25cf Expertise in QTP and coming out with an automation framework proposal for the client showing\ncasing advantages of an automated approach\n\n\n\nTest / Project Management\n\nOracle SSI -  Bangalore, Karnataka -\n\nMay 2008 to November 2012\n\nManaged 2 Project and Several minor releases with 50+ members Team of Test leads, Sr. test\nengineer, Test\nEngineers.\n\nTitle: UBS - Fusion HCM SAAS Implementation\n\nEDUCATION\n\nMSC in Computer science\n\nNagarjuna University\n\nBSC in Computer science\n\nKakatiya University\n\nSKILLS\n\nTesting (10+ years), Program Management (10+ years), Automation Testing (10+ years),\nSelenium Webdriver (4 years), Project Management (10+ years), Java (10+ years), AWS (10+\nyears), Cloud Computing (4 years)\n\nADDITIONAL INFORMATION\n\nExpertise\nInvolved and excelling in enterprise-wide initiatives, complex and time-critical business projects\nproviding\ndistinctive leadership at the following levels:\n\u25cf Account Management: Understanding inter-related services of the organization; representing\norganization within an account; selling entire portfolio of enterprise services; strategy to action\nby\nsuggesting right processes, skills, culture (people), technology and content; effectively\nmaintaining relationships with customers. Understanding key challenges of the customer;\nfinancial\nperformance of the client; competitors within the account; technology budget or spend; client\nspend on consulting. Key functions: business consulting; aligning IT to business; application\nportfolio review; business analysis/requirements; KPIs/scorecards; involved in writing;\nimplementing and tracking account plans; estimating budgets; business cases; RFPs; proposals\nand\nbusiness presentations.\n\u25cf Test Manager: Onsite / Offshore Operations: Involved in all key initiatives of the enterprise\nwith all vendors, pre sales activities, customer engagement, writing proposals, and Test\nconsulting.\n\n\n\nEstablished and managed practice improvement forum onsite with 50+ associates providing\ninputs\nto testing practice and provided lots of trainings.\n\u25cf Portfolio/Programme/Project Management level: Delivering programs and projects in managed\nportfolios from Initiation Business Case & Charter, through Requirements, Planning, Budgeting,\nExecution & Release Management, to closeout review while providing auditable progress &\ndeliverable completion visible to the project team, Management and stakeholders. IT projects\ncovering custom solutions, shrink-wrap product development, & commercial off-the-shelf\ndevelopment & integration.\n\u25cf Management and Governance establishing governance criteria and enforcing these through\neffective performance management & strategic planning; developing new opportunities;\npreparing\nbusiness cases, POC, POS and application review mechanisms; ensuring measurably high\ncustomer\nsatisfaction; setting BU objectives, educating, mentoring & guiding Sales & Marketing resources.\nDeveloping collaborative relationships with customers, vendors, and product support leaders.\nProviding integrated approach to CMMI, Earned Value, ROI, Training Plans, Satisfaction &\nExpectation Management, and Reporting.\n\u25cf Project Office, Project Procurement & Financial Management, developing, establishing &\nmaintaining PMO services, Organizational Governance tools & Systems, Traceability Matrix,\nproject & master Schedules, project Plans, Resource Profiles, organizational Capacity\nManagement, Cost, Risk & Issue, Integration sequence, Communication Plans, Mentoring,\nTraining, Templates, & Performance, for all PMBOK and CMMI disciplines.\n\u25cf Architecture, developing frameworks helpful for enterprises on strategic reuse, testing\nstrategy,\nautomation strategy, plans supporting Business Continuity essential to maintaining and\ncommunicating & solutions for business, development & stakeholder leaders & members.\n\u25cf Leadership, strong skills in Leadership, Motivation, Negotiation, Team building, Mentoring,\nTraining, Facilitation, Dispute Resolution, Collaboration, Communication & Organization\nDevelopment.\n\u25cf Process Engineering, Quality Assurance, Quality Control & Configuration Management,\nInvolved in SDLC definition and customization and method adaptation, process optimization,\nEnterprise-Wide Road Map for Standards Establishment, Components for Enterprise-Wide\nStandards, Standards Enforcement, Quality Assurance Continuum, Quality Control Continuum,\ndeveloping various test frameworks, methodologies and processes, Standard CMMI Appraisal\nmethod for Process Improvement (SCAMPI), establishing Quality Objectives, Goals, Metrics,\nForecasts, Enterprise level Test strategies, & tools, applying to IT Governance Frameworks.\n\nPersonal Strengths:\n\u25cf Provides direction and leadership with strong interpersonal and team building skills\n\u25cf Successfully works with people within and across organizational boundaries to build\nagreements,\nguidelines, and standards in order to resolve issues and create consistent practices.\n\u25cf Strong practitioner of clearly written purposeful communication and direction\n\u25cf Developing solutions to deliver quality products to clients through people and processes", "labels": []}
{"id": 32, "text": "Srushti Bhadale\nMumbai, Maharashtra - Email me on Indeed: indeed.com/r/Srushti-Bhadale/ffe3d9f99a4b3322\n\nWilling to relocate to: Mumbai, Maharashtra - Mumbai Central, Maharashtra - Pune, Maharashtra\n\nWORK EXPERIENCE\n\nAssociate Consultant\n\nOracle Financial Services -  Bengaluru, Karnataka -\n\nSeptember 2017 to Present\n\n\u2022 Currently associated as Associate Consultant with Oracle Financial Services Software.\n\u2022 Trained on Java, Plsql, Flexcube for 3 months from Oracle University.\n\nProject Name KeyBank Project\nTeam OBP team\nProject Duration 2 months\nProject Description\n\u2022 KeyBank, the primary subsidiary of KeyCorp is the major bank based in Cleveland. It uses\nOracle Banking Platform to provide Online and Mobile Innovation to Meet Customers' Changing\nExpectations.\n\u2022 OBP team provides Web services, Business processes, domain services functionality to\nKeyBank.\n\nProject Role and Contribution\n\u2022 Worked on migration of OBP product services from 2.3 to 2.6.1 using Java, Plsql, SOAP UI\ntechnologies.\n\u2022 Handled and worked on batch uploads.\n\u2022 Worked on Code incremental and DB incremental activities.\n\u2022 Configured Junit setup for the upgrade activities.\n\u2022 Unit Tested and modified the changes required for OBP consulting upgrade.\n\u2022 Worked on fixing various defects that were part of OBP product code and database upgrade.\n\u2022 Worked on Fund transfer module of OBP.\n\nEDUCATION\n\nHSC\n\nD.G Ruparel College\n\n2013\n\nSSC\n\nConvent Girls' High School\n\n2011\n\nhttps://www.indeed.com/r/Srushti-Bhadale/ffe3d9f99a4b3322?isid=rex-download&ikw=download-top&co=IN\n\n\nSKILLS\n\nAssociate (Less than 1 year), CSS. (Less than 1 year), HTML5 (Less than 1 year), Java (Less\nthan 1 year), JavaScript (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILL SET:\n\n\u2022 Programming Languages: C, Java, JavaScript, SQL.\n\u2022 Web Technologies: HTML5, CSS.\n\u2022 Working Platform: Windows, Linux.\nWORKSHOP/CERTIFICATION:\n\n\u2022 Ethical Hacking and IT Security workshop held at Vidyalankar Institute of Technology on 6th\nand 13th February 2016.\n\u2022 Certificate for attending Seminar cum Demo Workshop - Cloud and Virtualization held at\nVidyalankar Institute of Technology on 5th March 2016.\n\u2022 Microsoft Technological Associate course held at Vidyalankar Institute of Technology from 16th\nAugust to 7th October 2014.\n\nACHIEVEMENT:\n\n\u2022 Certificate of merit for securing first place in Semester 5.\n\u2022 Awarded scholarship for the academic year [\u2026] from Tata Education and Development Trust for\nsecuring distinction in both Semester 5 and Semester 6.", "labels": []}
{"id": 33, "text": "Sudaya Puranik\nPrincipal Engineer Technical Staff - Company 1\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Sudaya-Puranik/eaf5f7c1a67c6c38\n\nTo secure a promising position that offers both a challenge and a good opportunity for both\nprofessional and personal growth.\n\nWilling to relocate to: Bengaluru, Karnataka\n\nWORK EXPERIENCE\n\nPrincipal Engineer Technical Staff\n\nCompany 1 -  Bengaluru, Karnataka -\n\nSeptember 2005 to Present\n\nTotal Experience: 12 years 6 months.\nWorked onshore and offshore for the projects. Extensive client facing and team management\nskills.\n\nCompany 1: Oracle India Private Limited, Bangalore\nDesignation: Principal Engineer Technical Staff\nDuration: September 2005 - till date\n\nKEY TECHNICAL SKILLS\nDatabases: Oracle 9i/10g, MySql\nLanguages: PL/SQL, Shell Script.\nFrameworks and Portals: Oracle ADF\nServer side Technology: J2ee,\nIDE: Jdeveloper 10.x\n\nTECHNICAL EXPERIENCE SUMMARY\n\u27a2 1 year Oracle Applications checklist implementation\n\u27a2 3+ years of Disaster Recovery on Oracle Applications (EBSO 11i, R12) and Database.\n\u27a2 8+ years of Oracle Applications DBA with Build and Release activities.\n\u27a2 Installation of Oracle Applications (EBSO 11i, R12) at Customer environment and carry out DR\n\u27a2 Configuration of Oracle Applications (EBSO 11i, R12) as per Customer specification.\n\u27a2 Configuration of Discoverer, Admin, Concurrent, Forms, Web, Reports done on separate pillars\n\u27a2 Patching, Cloning of Oracle Apps\n\u27a2 Database conversion from non-RAC to 2 node, 3-node RAC\n\u27a2 Installation of OTO product on customer environment. Including the Infra, Asmt, discoverer,\nOC4J product.\n\u27a2 Carried out Disaster Recovery solutions for OTO\n\u27a2 Database Switchover code written for OTO\n\u27a2 Domain knowledge of Manual Testing.\n\u27a2 Oracle Apps Release and Build Engineer,\n\u27a2 Carried out 11i and R12 Upgrades on Oracle Internal Customers.\n\u27a2 Carried out 12.2.X upgrades from 11i and R12 baselines with 11g and 12c database level\n\nhttps://www.indeed.com/r/Sudaya-Puranik/eaf5f7c1a67c6c38?isid=rex-download&ikw=download-top&co=IN\n\n\n\u27a2 Experienced with Database upgrades as well. Carried out upgrades from 10g to 11g, 11g to\n12C DB.\n\u27a2 Have been awarded Extra Mile Award 2 times for execution of projects on time with quality.\n\nPrincipal Engineer\n\nORACLE -\n\nSeptember 2005 to Present\n\nOracle Apps DBA for R12 and 12.2.X baselines, responsible for all the EBS release activities ,\nhandled upgrades with latest adop technology.\n\nEDUCATION\n\nBachelor of Engineering (Information Science) in Information Science\n\nB V Bhoomaraddi College of Engg and Technology Hubli -  Hubli, Karnataka\n\n2005\n\nVisveswaraiah Technological University -  Belgaum, Karnataka\n\nSKILLS\n\nOracle Apps DBA Release Enginner\n\nADDITIONAL INFORMATION\n\nSoftware/Tools\n\u2022 Test cases written in MS Word\n\u2022 Carried out manually as QTP does not support this Application\n5) Disaster Recovery Portal for carrying out Recovery on the Customers.\n\u2022 It involves a portal through which we provide the Customer details and host types. There are\nxml plug-in, which are installed and parsed as Workflows. The workflows contain the steps of the\nDR for the MT and DB. As and when required we can pause and skip during the recovery. Tool\ntested and verified by the On Demand Operations and Customers like GE, Xerox, RIT etc.\nClient: On Demand Operations, Oracle Bangalore\nRoles and Responsibilities:\n\u2022 Developed all the plug-in needed for the recovery which include Database Plug-in, E-business\nSuite Plug-in.\n\u2022 Monitoring and Support 24/7\n\nTeam Size: 2\n\u2022 Software /Tools: Shell and Expect scripting.\n\n4) DR Solution for R12 (E-Business Suite)\nIt involves setting up the primary by installing Oracle Apps R12, setup the standby site i.e\nDatabase, Mid Tier, by cloning the same and recover. It also involves operations like Switch Over\nand Fail Over.\nBy Switch Over we mean Primary to Standby and vice versa\n\n\n\nBy Fail Over we mean the Primary has crashed, we need to make the Standby as the Primary.\nClient: On Demand Operations, Oracle Bangalore\n\nTeam Size: 2\nSoftware /Tools: Shell and Expect scripting, PLSQL.\n\nRoles and Responsibilities:\n\n\u2022 Installation, Cloning, Patching of the Oracle Applications R12.\n\u2022 Conversion of Database from non-RAC to RAC\n\u2022 Setting up of the Standby.\n\u2022 Carry out the Switchover, Fail over and Recovery.\n\u2022 Carryout the whole Recovery Process through the UI.\n\n3) On Demand Service Continuity\n\nThe project dealt with \"Disaster Recovery & Backup\" which involves automating checks so that\ndisaster could be prevented. Also the recovery of database is involved.\nClient: On Demand Operations, Oracle Bangalore\nTeam Size: 5\nSoftware /Tools:\nUnix Advanced shell scripting and expect. (Involves working on Korn Shell)\n\nRoles and Responsibilities:\n\u2022 Automated the checklists for E-business suite and PeopleSoft products.\n\n2) CCB Portal Administration\n\nProject Description\n\nThe new CCB Administration component is an integrated, web-based application intended to\nconsolidate and simplify the CCB processes for On Demand. This application:\n\n\u2022 Makes it easier to manage the process of change implementation in customer instances across\nthe various milestones in the On Demand life cycle.\n\n\u2022 Simplifies said change implementation by keeping the information organized in a centralized\nrepository.\n\n\u2022 In the CCB Administration application Release 1.0\nClient: System Assurance Centre, Oracle Unites States of America\n\nTeam Size: 3\nSoftware /Tools:\n\nPL/SQL and HTML\n\nRoles and Responsibilities:\n\n\n\n* Developed the PL/SQL procedures & packages, HTML forms required for the Portal, using HTML\nfor the user to enter the required data.\n* The data is entered is validated, using Java Scripting, and the data is stored in the database.\n* Developed the reports.\n\n1) ORACLE PRODUCTION ASSESSMENTS\n\nProject Description\nAUTO VERIFY TOOL\nAuto Verify is a utility that automates many of the tedious and repetitive tasks that are involved\nduring QA testing of an Oracle Applications Release 11i environment. Auto Verify now performs\nmany of the tasks automatically that used to be performed manually.\nClient: System Assurance Centre, Oracle Unites States of America\n\nTeam Size: 4\nSoftware /Tools:\nUNIX Advanced Shell Scripting, PL/SQL.\n\nRoles and Responsibilities:\nAutomated the all the checklists for 8 releases of E-business suite.", "labels": []}
{"id": 34, "text": "Sumit Kubade\nSAP - FI Support Consultant - SAP FI\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Sumit-Kubade/256d6054d852b2a7\n\nSeeking a challenging and rewarding position in SAP FI that will benefit from my experience,\nprofessional qualification and excellent communication skills and where I can enrich my skills\nand management capabilities further while working to achieve the organizational goals.\n\n\u2022 1.0 Months experience in IT industry.\n\u2022 I am seeking challenging assignments in a growth oriented organisation.\n\u2022 Completed graduation B.com (FINANCE) Ability to work overtime, time management, and work\nresponsibilities.\n\u2022 ERP knowledge of SAP ECC 6.0IN FI MODULE in UV TECHNOCRATS & SOLUTION. With knowledge\nof (FI organisational structure, GL creation, Accounts Payable, Accounts receivables, customer\nmaster data, vendor master data & All End user scenario)\n\u2022 Proficient Knowledge about FI End user scenario and design of configuration of FI sub-modules\nGeneral Ledger (FI-GL), Accounts Payable (FI-AP), Accounts Receivables (FI-AR)\n\nSAP FINANCE MODULE TECHNICAL &FUNCTIONAL SKILL\n\n\u2022 Enterprise structure: configuration of Define &Assign Company and company code define\nbusiness area.\n\u2022 Global setting: Define Field status group, Fiscal year Variants, open and close posting\nperiod, document number ranges, Setting up of document types and posting keys for business\ntransactions.\n\u2022 General ledger: Define chart of account, Account group, define tolerance group for GL &\nemployees.\n\u2022 GL end user scenario-creation of GL master data, knowledge about F 02/FB50 GL invoice\nposting, GL Documents recurring document, park & held document, reversal document.\n\u2022 Accounts Payable: Creation of vendor a/c group & No ranges. Define tolerance group for vendor,\ncreation of sundry creditors GL, Display of vendor balances, Define no range for invoice posting,\n\u2022 AP end user scenario: creation of customer master, FB60 /MIRO invoice posting, Payment,\nposting Process of partial payment and down payment to vendor.\n\u2022 Customization of APP program for vendors.\n\u2022 Accounts Receivable: Define customer a/c group, No range for customer a/c, creation of no.\nranges\n\u2022 AR end user scenario: creation of customer master, FB70 invoice posting, payment posting\nprocess of partial payment down payment to customer.\n\u2022 House bank- Creation of house bank, creation of cheque lot, display cheque register, cheque\nencashment, cheque cancellation.\n\u2022 Foreign exchange transaction.\n\u2022 Asset Accounting - Chart of depreciation, creation of input & output tax code, Define asset\nclass, Depreciation,\nAsset master creation, Asset purchase posting (F 90), Depreciation run (AFAB), Sale of asset (f 92)\n\u2022 Closing Entries - Carry forward the balances of customer & vendor (f.07), Asset balances ( AJAB),\nGL balances (F.16), No range (OBH2) .FSV.\n\nWilling to relocate to: Pune, Maharashtra\n\nhttps://www.indeed.com/r/Sumit-Kubade/256d6054d852b2a7?isid=rex-download&ikw=download-top&co=IN\n\n\nWORK EXPERIENCE\n\nSAP - FI Support Consultant\n\nSAP FI -  Pune, Maharashtra -\n\nJune 2016 to Present\n\nRoles and Responsibility:\n\u2022 Providing production support for SAP FI Module\n\u2022 Master data Creation and Changes as when required\n\u2022 Interaction with end users for issue resolution\n\u2022 Solving issues/tickets with moderate and at times critical impact\n\u2022 Proactively discuss on issues with other functional consultants for timely resolution\n\u2022 Participation in regular FI team meetings\n\u2022 Provide training to end user's as and when required.\n\u2022 Customizing changes as per new requirement raised by client like House Bank Creation\n\u2022 Analyzing and providing solutions on the issues raised by the client\n\u2022 Participation in performing year end closing activity\n\u2022 Solving of maintenance Issues and Tickets in the area of FI.\n\u2022 Email response to end users.\n\u2022 Clarify and rectify the pending and due issues.\n\u2022 Resolved User issues on timely basis.\n\u2022 Based on the priority of the issues and the time required to resolve the issue, issues will be\nresolved within time bound to meet the SLA.\n\u2022 Attended KT sessions & updated knowledge with new issues.\n\nAs a part of Support Team Involvement in\n\n\u2022 User Support and also End User Training.\n\u2022 Handled the End user queries through Help desk. As per the user communication by mail have\nto register, acknowledge, respond, resolved, accept and close the issues.\n\u2022 The issues include the configuration, transaction error and program/form modifications.\n\u2022 Configuring payment terms, Configuring automatic payment program, Includes House bank\nconfiguration.\n\u2022 Configuration for special G/L transactions like down payment made, down payment received.\n\u2022 Input Tax Indicator Configuration.\n\nEDUCATION\n\nIndian Institute Of Company Secretory Of India (Appeared)\n\n2014\n\nMaharashtra State Government, Pune University -  Pune, Maharashtra\n\n2013\n\nB.com\n\nPune University -  Pune, Maharashtra\n\n\n\n2012\n\nSKILLS\n\nSAP (1 year)\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILL\n\n\u2022 ERP Packages: SAP ECC 6.0, Tally.\n\u2022 Office productivity: Microsoft Word, Excel, Power point.\n\u2022 Operating Systems: Windows [\u2026]", "labels": []}
{"id": 35, "text": "Syam Devendla\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Syam-Devendla/\nc9ba7bc582b14a7b\n\nSenior software engineer with more than 9 years of experience in C, C++, Data\nStructures programming and BI analytics. Experience includes implementation and\ntesting of enterprise and mobile application and middleware component software.\n\nWORK EXPERIENCE\n\nSMTS\n\nOracle India -  Bengaluru, Karnataka -\n\nJanuary 2014 to Present\n\nwith OBIEE team.\n\u2022 Worked in Samsung R&D Operations, Bangalore since Oct- 2008 to Jan 2014, with\nMultimedia team.\n\nEDUCATION\n\nPost Graduate Diploma in Embedded Systems\n\nCDAC -  Kochi, Kerala\n\n2006\n\nBachelor of Technology in Electronics and Communications\n\nNagarjuna University\n\n2005\n\nSKILLS\n\n.NET (Less than 1 year), ALGORITHMS (Less than 1 year), ALSA (Less than 1 year), ANDROID\n(Less than 1 year), APACHE HADOOP HDFS (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nCore Competencies\n\n\u2022 Extensively worked in C, C++\n\u2022 Good understanding of Data Structures and Algorithms\n\u2022 Good understanding and knowledge of BI Analytics of OBIEE.\n\u2022 Codes at HackerRank.com and am a 4 Star Rank coder in algorithms, coded\nusing C++ STL\n\u2022 Hands on experience of GDB Debugger for Core dump analysis and Server\n\nhttps://www.indeed.com/r/Syam-Devendla/c9ba7bc582b14a7b?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Syam-Devendla/c9ba7bc582b14a7b?isid=rex-download&ikw=download-top&co=IN\n\n\nside debugging.\n\u2022 Hands on experience of Visual Studio debugger for debugging on Windows\nplatform.\n\u2022 Working experience of Multithreaded applications for more than eight years\n\u2022 Good understanding of OOPS Design principles and STL.\n\u2022 Experienced in using various debuggers that include GDB debugger, Eclipse, VC++ IDE, Trace\n32, WinDbg, Visual Studio.\n\u2022 Good knowledge of Big data technologies(Hadoop, Yarn Architecture, map-\nreduce, Hive, Sqoop, Hbase, Zookeeper)\n\u2022 Good experience in Hadoop development and Admin activities.\n\u2022 Able to understand and review Java and Scala code\n\u2022 Good Exposure to Software practices, SDLC.\n\u2022 Good understanding and porting knowledge of framework components.\n\u2022 Good understanding of multimedia concepts, Audio video synchronization,\nAudioOut, AudioIn and video zoom.\n\u2022 Knowledge of WEBRTC features.\n\u2022 Good knowledge of video Engine.\n\u2022 Basic knowledge of MFC, socket programming.\n\u2022 Good knowledge in programming and operating system concepts.\n\u2022 Experience in working in a project from product development phase to\ncommercialization phase.\n\u2022 Successfully commercialized more than 25 million mobiles spanning across\ntwenty models.\n\u2022 Good global work exposure having worked in different work environments.\n\u2022 Basic understanding of Linux Operating system.\n\u2022 Experienced in design and implementation of Multimedia Applications for\nMobile handsets.\n\u2022 Good experience working on Smart Phone platforms(Framework, Middleware\nand Application development) having worked on various mobile platforms\nSHP, Samsung Bada, Linux, WindowsMobile5.0, SLP, Android and Tizen.\nSyam Prasad Devendla\n\u2022 Good knowledge and understanding of different frameworks for multimedia.\n\u2022 Good knowledge of multi-threaded programming and IPC mechanisms.\n\u2022 Excellent interpersonal and communication skills and ability to work in a team.\n\u2022 Skilled at learning new concepts quickly, working well under pressure, and\ncommunicating ideas clearly and effectively.\n\u2022 Tools Used: Beyond Compare, Samsung memory leak tool, and Samsung code\ncoverageTool, VS 2005 Remote Tools, KlocWork, WinShark and Ethereal.\n\u2022 Experienced in working with configuration tools like VSS, Perforce, and Clear\nCase.\n\n\u2022 Operating Systems: Windows, Ubuntu Linux\n\u2022 Mobile Platforms: SHP, Bada, Linux, WindowsMobile5.0, SLP and Tizen\n\u2022 Technology: Multimedia, Content, Mobile Applications\n\u2022 Languages: C, C++(Data Structures, Design Patterns, STL)\n\u2022 Tools: Eclipse, VS2005, VS2010\nKlocWork, Clear case, Perforce\n\nProjects\n\n\n\n\u2022 OBIEE\nEnvironment: Oracle BI Languages: C++\n\u2022 Implementing enhancements, improving the diagnosabilty and address the customer\nissues by fixing the code bugs.\n\u2022 Developed a security feature in downloads module to protect the system from DOS\nattack.\n\u2022 Implemented a performance enhancer logic in Ibots to execute the agents faster.\n\u2022 Responsible for fix/enhance Ibot(Delivers/Agent/Schedulers) issues in the product.\n\u2022 Responsible for fixing the core dump issues reported by customers in the nqscheduler\ncomponent in linux 32/64 and windows 32/64 platforms.\n\u2022 Oracle-Thirdeye\nEnvironment: HDFS, Yarn, Hadoop, map-reduce, thirdeye-agent\n\u2022 GUI is able to provide all information in a usable environment.\n\u2022 Most of the use will be as an embeddable UI in applications like TLP, DTE, and Bug DB\netc.\n\nSyam Prasad Devendla\n\u2022 Provide term, line number and time indexes for word and phrase search, ability to drill\ndown to a given line number and data around it, and also have ability to see data\naround a given time.\n\n\u2022 Thirdeye-nodes-refresh\nEnvironment: HDFS, Yarn, Hadoop, map-reduce\n\u2022 All processes for thirdeye including Hadoop components running on the same node. This\noverburdens the master node and leads to frequent failures due to disk space and\nmemory issues, so worked on POC to replicate entire thirdeye setup with required\nconfiguration on new machines.\n\n\u2022 BI-Manifest-Comparison Tool\nEnvironment: Oracle BI Languages: Java\n\u2022 Implemented tool to create an excel with manifests data across platforms and painted the\nmismatched versions\n\u2022 WebRTC\nEnvironment: Browser Languages: C++\n\u2022 Media Recorder implementation.\n\u2022 Multi Party communication development.\n\u2022 Stage Fright (recorder and playback)\n\u2022 Video engine\n\u2022 Peer connection\n\u2022 PlatformsY2012-Framework (content)\nEnvironment: Tizen Languages: C++\n\u2022 Developing Tizen framework (content) on SLP.\n\u2022 File Search\n\u2022 Directory Search\n\u2022 Playlist and Playlist Manager\n\u2022 Supporting Tizen content framework.\n\u2022 PlatformsY2011 -Framework (Multimedia- player)\nEnvironment: Bada, Eclipse, SLP (Samsung Linux Platform) Languages: C++\n\n\n\n\u2022 Implemented player framework for Bada OS\n\u2022 Audio Player and video Player modules.\n\u2022 POC of AV synchronization for video zoom.\n\u2022 Supporting Bada2.0 player framework and commercialization.\n\u2022 Movie Studio (Video Editor)\nEnvironment: Bada, Eclipse Languages: C++\n\u2022 Developed application based on UI guidelines given, modules like preview, split and trim\nforms using VePlayer library.\n\u2022 Implemented VPL file creation.\n\u2022 Media Browser\nEnvironment: Bada, Eclipse Languages: C, C++\n\u2022 This application is developed to check the stability of Bada's player and Audio out\nmodules.\n\u2022 Audio Player and video Player modules.\n\u2022 Implemented progress bar of player in both Player Form and PLS Player Form.\n\u2022 Bada-NS(R&D)\nEnvironment: Linux, SLP (Samsung Linux Platform) Languages: C, C++\n\u2022 Worked on launching emulator\n\u2022 knowledge of SLP Multimedia Framework\n\u2022 Ported Player (Audio and Video) module\n\u2022 Ported Audio Out and Audio In modules using ALSA library\n\u2022 ETMs Firmware Client: Wabtec Railway Electronics, German Town\n\nSyam Prasad Devendla\nEnvironment: Windows XP, IAR Workbench Hardware: IOC board (Provided by WRE)\nLanguages: C\n\u2022 Serial - Ethernet Bridge\n\u2022 Serial communication commands handling.\n\u2022 Supt Link\nClient: Schindler Elevator & Escalator Corporation\nEnvironment: Windows Mobile 5.0, embedded VC, Visual Studio 2005\nLanguages: VC++, MFC\n\u2022 A Business application. It makes easy for the superintendents to check the status of the\nelevators and escalators which are newly installed or being maintained and rate them\naccording to their performance and report the same to the Sap server using mobility\nsolutions.\n\u2022 FldLink\nClient: Schindler Elevator & Escalator Corporation\nEnvironment: Windows Mobile 5.0, embedded VC, Visual Studio 2005\nLanguages: VC++, MFC\n\u2022 FldLink is a Mobile Application which provides the technicians with a single,\ncomprehensive view of contact information, meeting schedules, Technical information\nand repairing support while working in the field.", "labels": []}
{"id": 36, "text": "Tejasri Gunnam\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Tejasri-Gunnam/6ef1426c95ee894c\n\n\u2022 3 years of experience in IT industry, involved in Software Testing.\n\u2022 Proficient in development of Test Plans, Functional test scriptsfor Functionality Testing and\nIntegration Testing, Test Execution, Defect Reporting, RegressionTesting.\n\u2022 Very Good in GUI, Functional and Performance testing.\n\u2022 Well Acquainted With Bug Tracking Tools like CDETS, BUG SCRUBBER, MANTIS, JIRA\n\u2022 Profound Knowledge in Software Testing Life Cycle.\n\u2022 Involved in Preparing Test Cases and Test Reports.\n\u2022 Knowledge on Quality Standards like ISO\n\u2022 CCNA certified\n\u2022 Very good knowledge in security testing.\n\u2022 Knowledge in Python scripting.\n\nWORK EXPERIENCE\n\nCisco SystemsIndPvtLtd -  Bengaluru, Karnataka -\n\nSeptember 2012 to Present\n\nunder the payroll of GoldStone Technologies\n\nAgile team Member, Testing, Security testing\n\nCisco SystemsIndPvtLtd -  Bengaluru, Karnataka -\n\nSeptember 2012 to Present\n\nDuration Sep 2012 - Till date\nTeam Size 15 Members\nRole Agile team Member, Testing, Security testing\n\nDescription:\nCSPC is a collector which is integrated with different CSOs (Common Service Objects) like the\ndevice discovery CSO, core collection, connectivity and data normalization. Together we call as\nCSPC or Common Services Platform Collector.CSPC-NOS is an add-on service which is written\non top of the CSPC i.e., extra software written by the Nos team based on the CSPC 2.0 base\ncode to cater to a group of customers. CSPC-NOS support on both Windows and Linux Cent OS\nplatform. The Windows CSPC-NOS client can connect to the CSPC-NOS Linux server and vice-\nversa. Devices can be added manually or using the import seed file. When importing a seed file/\nWhen Device discovery is triggered the Device discovery module collects light inventory data\nfrom the devices. The light inventory data contain information like Sys Object Id, device family,\nOS-version etc. . (This information collected can be seen by going to Reports->Managed Devices)\nThis light inventory data is used by the inventory module to load the model rule for the respective\ndevice. This data is also used by the base collector to decide whether the device is managed\nor unmanaged.\n\nResponsibilities:\n\nhttps://www.indeed.com/r/Tejasri-Gunnam/6ef1426c95ee894c?isid=rex-download&ikw=download-top&co=IN\n\n\n\u27a2 Involved Identifying Scenarios to be tested\n\u27a2 Preparing Test Cases\n\u27a2 Executing Test Cases\n\u27a2 Automating Testcases using TCL scripting\n\u27a2 Preparing Progress Report for Sign Off\n\u27a2 Test Plan\n\u27a2 Traceability Matrix\n\u27a2 Order Progression\n\u27a2 Raising and reporting defects to dev team\n\u27a2 Defect Management on monthly basis\n\u27a2 Participated in Defect prevention meetings on quarterly basis to reduce defect density.\nSecurity Testing:\n\u27a2 Threat Modelling\n\u27a2 Gap Analysis using CSERV\n\u27a2 Manual Penetration Testing (Proxy testing and SSL scanning)\n\u27a2 Vulnerability Assessment using Nessus and Retina tools\n\nDECLARATION\nI hereby declare that the above-mentioned information is correct up to my knowledge and I bear\nthe responsibility for the correctness of the above-mentioned particulars.\n\nDate:\nPlace:Bangalore ( G Tejasri)\n\nEDUCATION\n\nB.Tech in Electrical and Electronics Engineering\n\nPondicherry University -  Puducherry, Puducherry\n\nM.Tech in Power Electronics and Instrumentation Engineering\n\nJawaharlal Nehru Technological University\n\nSKILLS\n\nGap Analysis (5 years), Nessus (5 years), Security (5 years), SSL (5 years), testing (5 years)\n\nADDITIONAL INFORMATION\n\nSKILL SET\nDatabases: Oracle &SQL Server\nOperating System: MS-DOS, Windows 98/XP &UNIX, Windows 7\nLanguages: C, C++\nTesting Tools: Q.T.P, Win Runner7.5, Load Runner 9.5\nNetworking: Hands on Experience On Cisco devices, AAA, TACACS, RADIUS, VPNs\nSecurity: Security scans using Nessus and Retina tools, Penetration\ntesting using Proxy and SSL scans, GAP Analysis using CSERV", "labels": []}
{"id": 37, "text": "Urshila Lohani\nSenior Corporate Account Executive - MongoDB\n\nGurgaon, Haryana - Email me on Indeed: indeed.com/r/Urshila-Lohani/ab8d3dc6dd8b13f0\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSenior Corporate Account Executive\n\nMongoDB -  Gurgaon, Haryana -\n\nMay 2016 to Present\n\n\u2022 Designed and implemented a 2-year sales strategy for South India Region; revenues grew 4X.\n\u2022 Trained sales team of 35 from 20 partner companies; revenues generated through partners\nincreased 50%.\n\u2022 Led Business development team of 5 to build pipeline of 4X.\n\u2022 Acquired 32 new accounts with industry leaders including Intuit, IBM, Wipro, McAfee, Airtel,\nReligare and Adobe; 100% renewals in all existing accounts.\n\u2022 Initiated, designed and executed marketing events; attendees included 200 IT heads;\ngenerated $1M\npipeline.\n\u2022 Ranked in top 5% of global sales team of 322; Awarded thrice for highest quarterly revenues\nin APAC.\n\u2022 Won Excellence Club Award in FY17 and FY18.\n\nAccount Manager\n\nRed Hat -  Bengaluru, Karnataka -\n\nJune 2014 to May 2016\n\n\u2022 Responsible for sales of entire Red Hat Product Portfolio in Mid market and Enterprise Accounts\nin West and South India Region.\n\u2022 Introduced Customer Success Program; renewals up 20%; revenues rose 12%.\n\u2022 Formulated sales strategies and achieved $4M in sales.\n\u2022 Won multiple awards (four quarters - highest revenues closed) and (2 consecutive years - 100%\nClub Award).\n\u2022 Improved brand presence in small cities and towns; inducted new partners; revenue driven\nby partner\nchannels up 26%\n\u2022 Designed events engaging IT Directors & CxOs; penetrated 7 key accounts; generated $400K\npipeline.\n\nAccount Manager\n\nOracle -  Noida, Uttar Pradesh -\n\nMay 2013 to May 2014\n\nhttps://www.indeed.com/r/Urshila-Lohani/ab8d3dc6dd8b13f0?isid=rex-download&ikw=download-top&co=IN\n\n\nBusiness Development Rep\n\nOracle -\n\nSeptember 2011 to April 2013\n\n\u2022 Responsible for MySQL, Oracle Linux and VM Sales in North Central US Region.\n\u2022 Generate opportunities using Linkedin, Hoovers, Job Portals, Marketing Leads and Oracle Install\nbase.\n\u2022 Work closely with Channel Partners, Resellers and Oracle Internal Counterparts to increase\ncustomer base.\n\u2022 Designed & developed Pipeline Generation kits for Sales team of 12.\n\u2022 Awarded in Q1 and Q2 FY13 for highest quarterly achievement in the team; 100% Annual Quota\nachieved for FY12 and FY13.\n\u2022 Revamped email marketing campaigns led to 15% higher response rate.\n\u2022 Initiated a structured mentorship program for MySQL Team; Training times down by 2 Months;\nproductivity\nup 50%.\n\nEDUCATION\n\nB Tech Honors in Technical\n\nCollege of Engineering -  Roorkee, Uttarakhand\n\nAugust 2007 to May 2011", "labels": []}
{"id": 38, "text": "Vamsi krishna\nhyderbad, Telangana - Email me on Indeed: indeed.com/r/Vamsi-krishna/15906b55159d4088\n\nWilling to relocate to: hyderbad, Telangana\n\nWORK EXPERIENCE\n\nSoftware developer\n\nMicrosoft\n\nIam need \nWhat i am\n\nEDUCATION\n\nBsc\n\nShri gnanambika degree college\n\nSKILLS\n\nAnalytics, Research\n\nhttps://www.indeed.com/r/Vamsi-krishna/15906b55159d4088?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 39, "text": "VARUN AHLUWALIA\nQuantitative Analyst\n\n- Email me on Indeed: indeed.com/r/VARUN-AHLUWALIA/725d9b113f3c4f0c\n\nWORK EXPERIENCE\n\nSoftware Engineer\n\nTavant -  Bangalore, Karnataka -\n\nApril 2005 to April 2006\n\n\u2022 Implemented online retail sale management solution for industrial manufacturing\ngiant Ingersoll Rand.\n\u2022 Implemented secondary mortgage solution for leading wholesale lender Ameriquest.\n\nSoftware Engineer\n\nPatni -  Bangalore, Karnataka -\n\nAugust 2004 to April 2005\n\n\u2022 Built profitability reports for using Oracle Financial Analytics\n\nEDUCATION\n\nMaster of Science in Financial Mathematics\n\nUniversity of Chicago -  Chicago, IL\n\nJune 2010\n\nBachelor of Technology in Civil Engineering\n\nIndian Institutes of Technology IIT Kanpur\n\nJuly 2004\n\nADDITIONAL INFORMATION\n\nSKILLS\nProgramming \u2022 JAVA, C++, C, Matlab, SQL\n\nOperating System \u2022 Windows, Linux\n\nhttps://www.indeed.com/r/VARUN-AHLUWALIA/725d9b113f3c4f0c?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 40, "text": "Vijayalakshmi Govindarajan\nSAP as a Consultant - SAP Basis\n\nChennai, Tamil Nadu - Email me on Indeed: indeed.com/r/Vijayalakshmi-Govindarajan/\nd71bfb70a66b0046\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSAP as a Consultant\n\nSAP Basis -\n\nMay 2012 to Present\n\nInvolved in 3 support Projects and 1 implementation Project.\n\u2022 System Build, System Refresh, system upgrade and system maintenance activities.\n\u2022 Self-motivated and goal-oriented, with a strong ability to organize and prioritize tasks\nefficiently.\n\u2022 Excellent team player with good communication, analytical, problem solving skills.\n\u2022 Certification Done: Oracle certified Java Programmer\n\u2022 Finished Diploma in Java course from NIIT\n\nEDUCATION\n\nMCA in Computer Applications\n\nThiagarajar School of Management -  Madurai, Tamil Nadu\n\nBSc\n\nSri Sathya Sai Institute of Higher Learning -  Anantapur, Andhra Pradesh\n\nHSC\n\nTVS Lakshmi Matriculation Higher Secondary School -  Madurai, Tamil Nadu\n\nSSLC\n\nTVS Lakshmi Matriculation Higher Secondary School -  Madurai, Tamil Nadu\n\nSKILLS\n\nJAVA (6 years), ORACLE (6 years), SAP (6 years), ABAP (Less than 1 year), ACCESS (Less than 1\nyear)\n\nADDITIONAL INFORMATION\n\nTECHNICAL EXPERTISE\n\nhttps://www.indeed.com/r/Vijayalakshmi-Govindarajan/d71bfb70a66b0046?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Vijayalakshmi-Govindarajan/d71bfb70a66b0046?isid=rex-download&ikw=download-top&co=IN\n\n\nOperating System\n\u2022 Worked on UNIX AIX and Windows Environment.\n\u2022 Troubleshooting & Identifying OS level Bottle necks.\n\u2022 Monitoring Top CPU Utilization/Memory Utilization issues at OS-level.\n\u2022 Administrating File system and permission issues at OS-Level.\nDatabase Administration\n\u2022 Database administration on Oracle 10 g, 11 g, 12 c.\n\u2022 Monitoring & perform database backup at DB13 level.\n\u2022 Cluster Administration on Oracle 11 g, 12 c..\nSAP HANA Database Administration\n\u2022 Monitoring HANA DB and to troubleshoot performance issues using HANA studio.\n\u2022 Importing and activating views and procedures.\n\u2022 Troubleshooting HANA data and log backup issues.\n\u2022 Monitoring top CPU Utilization/Memory Utilization issues at OS-level.\n\u2022 SLT Replication of tables from ECC to HANA DB.\nThird Party Tools Worked\n\u2022 Open text.\n\u2022 PAS - Pay metric Adapters.\n\u2022 TRAX\n\u2022 HP, Service now.\n\u2022 CPS, Redwood Job scheduling.\n\u2022 Vertex.\nProgramming Knowledge\nC, C++, Core Java, J2EE, EJB, Struts\n\nContributions and Achievements\n\u2022 Conducted training for New joiners\n\u2022 Conducted team meetings and internal training sessions\n\u2022 Received \"Manager's Choice Award\" thrice for managing crisis situation\n\u2022 Received \"Deep Skill Adder Award\" every quarter for taking Personal interest\nPROJECT (Support): 1\nOrganization: IBM India PVT LTD\nRole: SAP Basis Consultant\nClient: Maersk Line- MLIT Basis\nDatabase: Oracle 11g\nPlatform: Linux 2.6\nPeriod: April 2015 to September 2016 and from Aug 01, 2017 to till date\nResponsibilities:\n\u2022 Creation of OSS ID and registering Developer for Access key and Object key in SMP.\n\u2022 Apply OSS Notes using SNOTE.\n\u2022 Experience on deployment of digital signature in Java Landscape.\n\u2022 Apply SPAM/SAINT, Support Packages, and Add-ons.\n\u2022 Performance Monitoring for ECC, BI, SRM, CRM, and PI systems.\n\u2022 Handling user tickets and system performance tickets.\n\u2022 Background Job Administration such as scheduling/Cancelling and Troubleshooting background\njobs as per client requirement '\n\u2022 Performed daily health checks across the landscape.\n\u2022 Analyzing and troubleshooting daily issues occurred in health checks.\n\u2022 Index rebuild for BW and ECC systems.\n\n\n\n\u2022 Run update stats for ECC and BW tables.\n\u2022 Printer configuration.\n\u2022 Detecting Expensive SQL statements\n\u2022 Monitoring and troubleshooting JAVA systems using NWA and admin tools like Visual Admin tool\nand Config tool.\n\u2022 Database cluster Administration.\n\u2022 System Refresh from Production to Quality and Pre Prod.\n\u2022 System Restore from Production to Quality, Pre Prod. Same system restore\n\u2022 System Upgrade from EHP 6 to EHP 7.\n\u2022 Kernel upgrade for the entire landscape.\n\u2022 System Build for System Upgrade.\n\u2022 DB Export and Import across the landscape.\nPROJECT (Implementation): 2\nOrganization: IBM India PVT LTD\nRole: SAP Basis Consultant\nClient: Maersk Line- MLIT Build Basis\nDatabase: Oracle 11g\nPlatform: Linux 2.6\nPeriod: September 2016 to July 2017\nResponsibilities:\n\u2022 Creation Database Source in Java systems.\n\u2022 Creation of Product, Software Component, Business Systems and Technical Systems in PO SLDs.\n\u2022 Performance capture whenever test load went into the system.\n\u2022 Troubleshooting JAVA and ABAP stack systems.\n\u2022 Creating source systems in BW systems, Maintaining and troubleshooting RFC connections for\nthe same.\n\u2022 Client opening and closing depending up on requirement.\n\u2022 Creating OSS messages on various issues to SAP in SMP.\n\u2022 SSO Configuration in Solution Manager.\nPROJECT (Support): 3\nOrganization: IBM India PVT LTD\nRole: SAP Basis Consultant\nClient: Ericsson\nDatabase: Oracle 11g\nPlatform: Linux 2.6\nSAP: ECC 6.0, BW ABAP 7.31\nPeriod: July 2013 to November 2014.\nResponsibilities:\nPerformed all SAP Basis Activities like Performance monitoring, Monthly report generation.\n\u2022 Printer configuration.\n\u2022 Detecting Expensive SQL statements\n\u2022 Monitoring and troubleshooting JAVA systems using NWA and admin tools like Visual Admin tool\nand Config tool.\n\u2022 Table space monitoring using BRTOOLS.\n\u2022 Database Administration using BRTOOLS.\n\u2022 Kernel upgrades for the entire landscape.\n\u2022 Part of EHP upgrade from ERP6.0 EHP 6 to ERP6.0 EHP 7\n\nPROJECT (Support): 4\n\n\n\nOrganization: IBM India PVT LTD\nRole: Associate SAP Basis Consultant L1\nClient: Unilever, UK\nDatabase: Oracle 10g\nPlatform: Linux 2.6 Linux V 6.2\nSAP: ECC 6.0, BW ABAP 7.31, CRM 7.31\nPeriod: May 2012 to June 2013.\nResponsibilities:\nPerformed all SAP Basis Activities like Production System Performance monitoring, Monthly report\ngeneration.\n\u2022 Performance Monitoring for ECC, BI, SRM, CRM, and PI systems.\n\u2022 Handling User tickets.\n\u2022 Background Job Administration such as scheduling/Cancelling and Troubleshooting background\njobs as per client requirement '\n\u2022 Performed daily health checks across the landscape.\n\u2022 Analyzing and troubleshooting daily issues occurred in health checks.\n\u2022 Importing Transports across the Landscape.\n\u2022 Creating/modifying User roles as per client requirement.\n\nPESONAL DETAILS:\nName: Vijayalakshmi Govindarajan", "labels": []}
{"id": 41, "text": "Vikas Singh\nChandigarh, Chandigarh - Email me on Indeed: indeed.com/r/Vikas-Singh/8644db42854c4f6a\n\nI've 4.6 years of IT experience in Identity and Access Management with Infosys limited. It involves\ntroubleshooting and resolving workflow errors and provisioning user access. I perform identity\nand access management activities, updating existing access and provisioning workflows, manage\noperations within the IAM environment. Currently I'm working on automating the work flow of\nSecurity Intelligence and Response Team with Phantom and Python scripting.\nI'm trained in Python, Solaris administration, Java and PLSQL. I'm able to handle multiple tasks\nand work as a team Member. I've excellent analytical, problem solving and programming skills.\nI'm committed; goal oriented, & has zeal to learn new things & technologies.\nI am graduated in Electronics and Communication Engineering in 2013 with excellent grades. I\npossess good problem solving & interpersonal skills, good communication skills and team spirit.\n\nWORK EXPERIENCE\n\nTechnology Analyst\n\nInfosys Limited -  Chandigarh, Chandigarh -\n\nOctober 2013 to Present\n\nA. Change Management:\nInstalling and upgrading RT and RTIR for improved request handling with MySQL database.\n\nB. Python Automation:\nAutomating various use cases for the Security Intelligence and Response Team using Python\nscripting and Phantom tool integrating it with various tools i.e. Splunk, Request Tracker for\nIncident Response (RTIR), Remedy etc.\n\nBelow are the use cases details:\n\nUse Cases Description\nProxy Blocks Enable the ability to block domains and URLs automatically on Bluecoat proxies\nusing a list maintained in SPLUNK\nPalo Alto Blocks Enable the ability to block domains and URLs automatically on Palo Alto proxies\nusing a list maintained in SPLUNK\nThreat Intel Email feed ingestion Take emails from an external distribution group and parse the\nemails for IOCs\nEmail Eradication\nWhen a Malicious email has been detected as being received in the Microsoft Exchange email\nsystem perform eradication steps to remove the email from the email messaging platform\n\nEmail Quarantine Email addresses alerted as malicious need to be added to a quarantine list\nMalware Response When malware is detected by alert or scanning initiate containment\nprocedures for the affected device in question\nUnapproved Devices\n\nhttps://www.indeed.com/r/Vikas-Singh/8644db42854c4f6a?isid=rex-download&ikw=download-top&co=IN\n\n\nWhen alerts for Unapproved Devices, equipment that is not in Organization's asset inventory, is\ntriggered containment needs to occur for the device in question\n\nIOC Detect and Scanning using Tanium and Fireeye HX\nWhen Indicators of Compromise, IOC, are received from various sources, threat intel feeds,\nexploded malware the network environment needs to be scanned for any of the indicators of\ncompromise provided.\n\nClear Text Passwords detected\nAutomatically flag users password to reset in Active Directory when an alert in SPLUNK for a clear\ntext password detected fires\n\nCreate ticket from Splunk or MSSP Alert\nDevelop a script that takes the details of an alert from an alert generated in SPLUNK and create\nor append to a ticket in the ticketing system in use\n\nTriage and Identification Execute the triage and identification steps that are performed manually\ntoday\n\nInformation Security Analyst\n\nInfosys Limited -  Chandigarh, Chandigarh -\n\n2013 to Present\n\nA. Identity management\n\nInfosys Limited -\n\nMay 2014 to December 2017\n\nmanagement May 2014 - Dec 2017\n\nFollowing are my roles and responsibilities in the project:\nA. Identity management:\nTracking and processing identity creation for all the new hires along with basic access e.g.: Email,\nActive Directory accounts and including mandatory security related groups. Also, making sure\naccess is disabled on the user's departure date and cleaning up of all the access. Reviewing\naccess periodically and updating it accordingly.\n\nB. Access management:\nThis involves provisioning/de-provisioning access to users across 300+ applications using various\nglobal and in house tools like RSA security console, SAP, Identity IIQ etc. across multiple platforms\nlike, UNIX, Database and application front end. Making sure standard operative procedures (SOP)\nare followed, validation checks are completed and appropriate approvals are gathered before\naccess is granted.\n\nC. Quality Management:\nPerforming quality checks on random samples of requests on daily basis and sharing QAP results\nwith administrators.\n- Monitoring and tracking the corrective actions are taken within defined timeframe.\n\n\n\n- Doing RCA on major issues\n- Developing Service Improvement Plan (SIP) and Process Improvement Plan (PIP) based on the\nQAP analysis\n\nD. Risk Management:\nIdentifying risk areas through daily and periodic report E.g. Segregation of duties violation (SOD)\nreport, Active directory infraction report etc.\n- Working with various teams to mitigate the violations.\n- Assisting auditors by provide details and justification on audit samples.\n\nE. Client Coordination:\nCoordinating with client daily, weekly for the operations, issues and feedback with the respective\nreports prepared.\n\nEDUCATION\n\nBachelor of Technology in Electronics and Communication Engineering\n\nGLA Institute of Technology and Management -  Mathura, Uttar Pradesh\n\nSeptember 2009 to June 2013\n\nSKILLS\n\nSECURITY (5 years), INFORMATION SECURITY (5 years), ACTIVE DIRECTORY (3 years), UNIX\n(Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\n\n\u25cf Operating Systems: Windows, Solaris\n\u25cf Languages: Python, Core Java, SQL, Unix\n\u25cf Software: Sailpoint IIQ, Oracle IAM, Beeline, SAP, Active Directory, Phantom, Quest change\nauditor, Microsoft Office Suite\n\u25cf Information Security: Concepts and best practices", "labels": []}
{"id": 42, "text": "Yasothai Jayaramachandran\nLead Engineer - Automation & Testing\n\nChennai, TAMIL NADU, IN - Email me on Indeed: indeed.com/r/Yasothai-Jayaramachandran/\nc36e76b64d9f477f\n\n\u2756 4 years of experience in testing methodologies: Automation, Regression, Sanity and Manual.\n\u2756 Hands-on experience in Selenium Webdriver with Python Automation.\n\u2756 Skilled in IDE's Pycharm, Eclipse[Pydev plugin], Selenium for testing the web browser using\nPython.\n\u2756 Expertise in developing automation scripts and testing various components.\n\u2756 Skilled in providing effective and quality automation process.\n\u2756 Hands-on experience in functional automation scripts using TCL Programming, Python.\n\u2756 Experienced in planning, designing, developing and deploying testing strategies.\n\u2756 Trained and worked closely along with developers and dev-test (manual) engineers for\nautomation tasks.\n\u2756 Good experience in reviewing the automated TCL, Python Scripts developed by the team\nmembers.\n\u2756 Extracting data from excel and csv files, posting results in excel file with Python.\n\u2756 Experience in Agile development -Played responsible role Scrum master for Automation.\n\nWORK EXPERIENCE\n\nLead Engineer - Automation & Testing\n\nCisco Systems -\n\nJanuary 2014 to June 2015\n\nDURATION Jan-2014 to Jun-2015\nTEAM SIZE 20\nROLES AND RESPONSIBILITIES\nLead Engineer - Automation & Testing\n\u2022 Identified the cases for giving the support to the WAAS product with v6 addressing from the\ntest plan\n\u2022 Played an active role - Scrum Master for the Automation.\n\u2022 Activity participated and organized the automation team to work on agile methodologies.\n\u2022 Prepared the HLD document for the automatable cases.\n\u2022 Automated around 1000+ cases in SNMP and Platform module.\n\u2022 Automated the basic sanity test cases and executing sanity testing for the v6 support.\n\u2022 Involved is regressing all the automated suites.\n\u2022 Performed regression for all the automated cases through ATS framework with aetest script\ntemplate.\n\u2022 Reviewed the automated cases developed by team members.\n\u2022 Regression Testing\n\u2022 Defect filing in CDETS/Rally\n\u2022 Worked on Agile development methodologies\n\nLANGUAGE TCL Programming, Python, Selenium\n\nhttps://www.indeed.com/r/Yasothai-Jayaramachandran/c36e76b64d9f477f?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Yasothai-Jayaramachandran/c36e76b64d9f477f?isid=rex-download&ikw=download-top&co=IN\n\n\nRELEASES Hornet\nDESCRIPTION\n\u2022 IPv6, formerly named IPng (next generation), is the latest version of the Internet Protocol (IP). IP\nis a packet-based protocol used to exchange data, voice, and video traffic over digital networks\n\u2022 Cisco's Wide-Area Application Services IPv4 users can move to IPv6 and receive services such\nas end-to-end security, quality of service (QoS), and globally unique addresses. The IPv6 address\nspace reduces the need for private addresses and Network Address Translation (NAT) processing\nby border routers at network edges.\n\u2022 Implementing basic IPv6 connectivity in the Cisco IOS software consists of assigning IPv6\naddresses to individual router interfaces. The forwarding of IPv6 traffic can be enabled globally,\nand Cisco Express Forwarding switching for IPv6 can also be enabled\n\u2022 The primary motivation for IPv6 is the need to meet the demand for globally unique IP\naddresses.\n\u2022 Mainly involves to checks the functionality of all waas features to work with v6 addressing\n\nPRODUCT IPv6 Support on WAAS (Wide Area Application Services)\n\nPROJECT CISCO-WAAS Express (WEXP)\n\nMember Technical Staff - Automation & Testing\n\nCisco Systems -\n\nJanuary 2013 to December 2013\n\nDURATION Jan-2013 to December-2013\nTEAM SIZE 21\nROLES AND RESPONSIBILITIES\nMember Technical Staff - Automation & Testing\n\u2022 Identified the automatable cases from the test plan.\n\u2022 Prepared the HLD document for the automatable cases.\n\u2022 Automated test cases for various components on the wexp product for the functionality check.\n\u2022 Worked on Config_Sync, HTTP, HTTPS, SSL, SMB_AO component automation.\n\u2022 Automated around 200+ cases for various components.\n\u2022 Performed regression for all the automated cases through ATS framework with aetest script\ntemplate.\n\u2022 Reviewed the automated cases developed by team members.\n\u2022 Involved in executing all the automated cases for various releases as Phase1, Phase2 (pi19-25)\nand posted results in TIMS tool.\n\u2022 Regression Testing\n\u2022 Defect filing in CDETS\n\nLANGUAGE TCL Programming, Python\nRELEASES Phase1 &Phase2 (with 535)\nDESCRIPTION\n\u2022 Cisco's Wide-Area Application Services (WAAS) Express feature is a key component of the Cisco\nWAAS product portfolio. WAAS Express is a cost-effective, IOS-based, WAN optimization solution\nthat increases the amount of available bandwidth for small-to-mid-size branch offices and remote\nlocations, while accelerating TCP-based applications that operate in a WAN environment.\n\n\n\n\u2022 The Wide-Area Application Services (WAAS) Express software optimizes TCP traffic flows across\na WAN. WAAS Express integrates with native services such as security, NetFlow, and quality of\nservice (QoS). WAAS Express provides the following benefits:\n\u2756 Bandwidth optimization\n\u2756 Application acceleration\n\u2756 Increase in remote user productivity\n\u2756 Interoperation with existing Cisco WAAS infrastructure\n\u2756 Network transparency\n\u2756 Deployment flexibility with on-demand service enablement\n\u2022 The Wexp is about the wide Area Application Service on IOS (Router) itself.\n\u2022 Mainly involves to checks the functionality of optimizing the various Ao's like HTTP, SSL, HTTPS,\nSMB and Config_sync.\n\nPRODUCT WAAS Express(Wide Area Application Services)\n\nPROJECT CISCO-WAAS\nCLIENT Cisco Systems\nORGANISATION HCL Technologies Ltd\n\nSoftware Engineer - Automation & Testing\n\n-\n\nJune 2011 to December 2012\n\n\u2022 Identified the automatable cases from the test plan.\n\u2022 Prepared the HLD document for the automatable cases.\n\u2022 Automated test cases for various components on the waas product for the functionality check.\n\u2022 Worked on Plaform, SNMP, SSL, HTTP component automation.\n\u2022 Automated around 600+ cases for various components.\n\u2022 Performed regression for all the automated cases through ATS framework with aetest script\ntemplate.\n\u2022 Active Participation in automating test cases for sanity test.\n\u2022 Reviewed the automated cases developed by team members.\n\u2022 Involved in executing all the automated cases and reporting bugs/defects.\n\u2022 Worked on various releases of the products (Lancer, Phoenix, Skyhawk) for regressing the\nautomated cases and posted results in TIMS tool.\n\u2022 Defect filing in CDETS\n\nLANGUAGE TCL Programming, Python\nRELEASES LANCER, PHOENIX, SKYHAWK, SPRIT\nDESCRIPTION\n\u2022 Wide Area Application Services (WAAS) is a Cisco Systems technology that improves the\nperformance of applications on a wide area network (WAN). WAAS combines Cisco's Wide Area\nFile Services (WAFS-products allow remote office users to access and share files globally at\nLAN speeds over the WAN) with WAN optimization inside of a router, accelerating TCP-based\napplications on a given network.\n\u2022 WAAS system consists of a set of devices called wide area application engines (WAEs) that work\ntogether to optimize TCP traffic over your network. When client and server applications attempt\n\n\n\nto communicate with each other, the network intercepts and redirects this traffic to the WAEs so\nthat they can act on behalf of the client application and the destination server.\n\u2022 The WAEs examine the traffic and use built-in application policies to determine whether to\noptimize the traffic or allow it to pass through your network un optimized\n\u2022 Mainly involves optimizing the various Ao's like HTTP, SSL, HTTPS and functional wise platform\ncheck.\n\nPRODUCT WAAS(Wide Area Application Services)\n\nEDUCATION\n\nB.E in CSE\n\nANNA University\n\n2011\n\nDay Adventist Matric Hr.Sec School\n\n2005 to 2007\n\nSKILLS\n\noptimization (2 years), Python (4 years), router (4 years), TCL (4 years), Testing (4 years)\n\nADDITIONAL INFORMATION\n\nTechnical Skills\nProgramming Languages Python, TCL, Power Query, SQL, Shell Scripting, C#\nConcepts Networking, WAAS (Wan Optimization), WAAS on Router, WAE(Wide Area Engine),\nWCCP Protocol,\nAutomation Framework ATS (Automated Test System), Selenium Testing Framework\nIDE(Integrated Development Environment) Eclipse, Pycharm, Pydev[Plugin for eclipse IDE]\nSoftware\nEclipse CDETS & Rally (Bug Tracking), eARMS (Regression testing), ACME(Versioning),\nTIMS(Reporting), HTMLTestRunner, Power Query(Automation), ARAS PLM(Product Lifecycle\nManagement), AML Studio (adaptive markup language)\n\nOperating Systems Windows XP/10, Linux", "labels": []}
{"id": 43, "text": "Yathishwaran P\nMaximo Consultant - Infosys Limited\n\nNamakkal, Tamil Nadu - Email me on Indeed: indeed.com/r/Yathishwaran-P/a9c8d42210af40b8\n\n\u2022 Maximo Consultant in Infosys Limited, Chennai from August 2013 till date (around 4.8 years)\n\u2022 IT professional with 4.4 years of experience in BIRT reporting, application support in IBM MAXIMO\nApplication.\n\u2022 IBM Certified MAXIMO ASSET MANAGEMENT, PURCHASE ORDER, PROCUREMENT, PURCHASE\nREQUISITION Professional\n\u2022 Submitted Internal tutorial document on Configuration of Hover Dialogs and Its usage in Maximo\nV7.6\n\u2022 Worked on various customer requirements and having overall experience with\n\u27a2 Preventive Maintenance\n\u27a2 Maximo automation and implementation\n\u27a2 MBO customizations\n\u27a2 Data Supply chain\n\u27a2 Portals, Workflows Designing\n\u27a2 Reports/Query Creation\n\u27a2 Work order tracking, Invoicing, Purchase order\n\u27a2 Asset Management\n\u27a2 Maximo Integration Framework (MIF)\n\u27a2 Labors, Crews, Security Group Set up\n\u27a2 Domains, Database configuration\n\u27a2 Basics of BIRT, Cognos Reporting, Monitor logs from Admin console.\n\u27a2 Maximo Java customization\n\u27a2 Maximo Installation/Upgradation\n\u27a2 Basic WebSphere function\n\n\u2022 Strong interpersonal skills and the ability to work in team efficiently. Customer Service support\nexperience in a helpdesk environment.\n\u2022 Strong dedication towards work to ensure delivery of projects according to the schedules.\nProactively approached for delivering excellent customer service and liaising with stakeholders.\n\u2022 Ambitious, enthusiastic and highly motivated person with excellent problem solving skills and\nalso having ability to quickly master new technologies and skills.\n\u2022 Maintains effective work behavior in the face of setbacks or pressure. Strong multi-tasking\ncapability, target and deadline oriented.\n\u2022 Engaging and resilient communicator demonstrating influencing skills and an ability to adapt\napproaches to differing situations\n\nWilling to relocate to: Chennai, Tamil Nadu - Mysore, Karnataka - Bengaluru, Karnataka\n\nWORK EXPERIENCE\n\nMaximo Consultant\n\nInfosys Limited -  Chennai, Tamil Nadu -\n\nAugust 2013 to Present\n\nhttps://www.indeed.com/r/Yathishwaran-P/a9c8d42210af40b8?isid=rex-download&ikw=download-top&co=IN\n\n\naround 4.8 years)\n\u2022 IT professional with 4.4 years of experience in BIRT reporting, application support in IBM MAXIMO\nApplication.\n\u2022 IBM Certified MAXIMO ASSET MANAGEMENT, PURCHASE ORDER, PROCUREMENT, PURCHASE\nREQUISITION Professional\n\u2022 Submitted Internal tutorial document on Configuration of Hover Dialogs and Its usage in Maximo\nV7.6\n\u2022 Worked on various customer requirements and having overall experience with\n\u27a2 Preventive Maintenance\n\u27a2 Maximo automation and implementation\n\u27a2 MBO customizations\n\u27a2 Data Supply chain\n\u27a2 Portals, Workflows Designing\n\u27a2 Reports/Query Creation\n\u27a2 Work order tracking, Invoicing, Purchase order\n\u27a2 Asset Management\n\u27a2 Maximo Integration Framework (MIF)\n\u27a2 Labors, Crews, Security Group Set up\n\u27a2 Domains, Database configuration\n\u27a2 Basics of BIRT, Cognos Reporting, Monitor logs from Admin console.\n\u27a2 Maximo Java customization\n\u27a2 Maximo Installation/Upgradation\n\u27a2 Basic WebSphere function\n\n\u2022 Strong interpersonal skills and the ability to work in team efficiently. Customer Service support\nexperience in a helpdesk environment.\n\u2022 Strong dedication towards work to ensure delivery of projects according to the schedules.\nProactively approached for delivering excellent customer service and liaising with stakeholders.\n\u2022 Ambitious, enthusiastic and highly motivated person with excellent problem solving skills and\nalso having ability to quickly master new technologies and skills.\n\u2022 Maintains effective work behavior in the face of setbacks or pressure. Strong multi-tasking\ncapability, target and deadline oriented.\n\u2022 Engaging and resilient communicator demonstrating influencing skills and an ability to adapt\napproaches to differing situations\n\nRole: Senior Systems Engineer\n\u2022 Working as Maximo Consultant for Bombardier Transportation, Canada\n\n\u2022 Strong knowledge in Maximo configuration using Automation Scripting, Database configuration,\nApplication Designer, Domains, Workflows and other major applications\n\u2022 Providing efficient solution for the issues related to Work orders, Asset configuration, Item\nMaster, Preventive Maintenance, Inventory, Domain related issue\n\u2022 Thorough implementation, support knowledge of Bombardier Transportation Maximo version\n4, 6, 7.1 and 7.6.\n\u2022 Having experience in Maximo Implementation, L2 and L3 support, and deployment activities\n\u2022 Having experience in Incident Management, Problem management, Change management,\nService Request, and other applications.\n\u2022 Delivering 100% effectiveness in resolving ticket by ensuring compliance with client Service\nLevel Agreements (SLAs)\n\n\n\n\u2022 Developing Conditional Queries/Reports to verify the desired results.\n\u2022 Worked on various formal and informal production reports.\n\u2022 Created the template for maintaining the application maintenance activities.\n\u2022 Worked in many configuration management applications for asset management related\nactivities for future implementation.\n\u2022 Working in 24*7 support. Resolved many critical issues which appreciated by client\n\nBIRT Reports\nBTRAMR0001SYSER.rptdesign (Work order report):\n\u2022 My client was using default BTRAMR2 Work order reports. They also use a different work order\nreport in Maximo V4 and V6. The earlier used report have many drawbacks such as\n\nNo Flexibility for modification since it is used by many divisions\nEmpty boxes, lot of missing information\nNot align with Systems objectives and processes\nHistorical context on the asset is missing\nEsthetical issues: Font too small, input fields not instinctive\nNo bar coding used\n\u2022 The outcome of this report is a form to support technician's work and regulation and give\nthem simplicity to input information. It covers the full spectra of requirements of System Services\ndivisions.\n\u2022 This report is site specific one and it will display the WO details along with its task WOs and\nfailure reporting information.\n\u2022 Each site is having their own logo. This current report will display the logo based on site.\n\u2022 It was implemented in V7.1 only. In V7.1 we do not have the option of auto resetting page\nnumbers (i.e., while displaying multiple WOs, for each new WO page number will not be reset\nautomatically) . I have implemented this option in this report.\n\u2022 Language Translation was used for this report in German and French\n\u2022 I did not use IBM templates for this report. I have customized our own template in this report.\n\nBTRAMR0009.rptdesign (Availability Report):\n\n\u2022 This particular report relates to the requirement of the business to be able to create a\nAvailability Graph based on selected Train Assets, a timeframe and selected work types / work\norder parameters.\n\u2022 Availability can be calculated based on a Fleet or a single Train. The business requirement is to\nbe able to select this base as needed to suit the project or view needed at that time.\n\nBTRAMR0027.rptdesign (Card Stock Report):\nExisting report functionality:\n\u2022 Technician requiring a part will take the component in its bin along with an Inventory Card. The\ntechnician reports the consumption at the exit of the storeroom on the Kiosk computer where\nhe scans its Work Order and the Inventory Card in the application Issue and Transfer of Maximo.\nWhen we rebuild something internally, the technician will bring the new assembly in store, grab\nthe Inventory Card and he will report the return in store against the rebuilt Work order.\nObjectives:\n\u2022 Created a card stock report in order to match the existing Inventory Card tool developed by\nSystems.\n\u2022 Increase reporting of consumption\n\n\n\n\u2022 Redefine the process of cycle count (80% efficiency in Systems)\n\u2022 The report will be based on inventory information and will introduce the notion of real time to\nincrease precision\n\nBTRAMR0001SBB.rptdesign (Report Change):\n\u2022 The report BTRAMR0001SBB was developed for the European project. It is based on the\nBTRAMR0001MLM report and currently uses only launch option 2 and 3 of the BTRAMR0001MLM\nreport. The client requested to have option 1 (type=1) of the BTRAMR0001MLM implemented in\nthe BTRAMR0001SBB report as well as the translation of the labels from French to English.\nObjectives:\n\u2022 To have define the type=1, report definition for BTRAMR0001SBB\n\u2022 To have the labels translated from French to English\n\nBTRAMR0002.rptdesign (Systems Purchase Requisition Report):\n\n\u2022 The current PR report does not have bar-coding feature and is formatted too small. This is an\noperational report; it will be used in real time.\n\u2022 Have a common and standardized PR report across all Systems services sites with the support\nof bar-coding\n\nIncident Management Solution report:\n\u2022 The report displays the overall ticket progress. It shows count of tickets raised and assigned to\nboth Infosys and Client queue. We can view the total no. of tickets raised for various application\ngroups such as Maximo, Kronos and BIEM. It also further categories the ticket count based on\nticket priority and the status. This helps in analyzing the data from ticketing tool.\n\u2022 There might be cases where different teams work on the tickets raised. This reports helps to\nanalyses the team wise ticket count. We can there by monitor the performance of various teams.\n\u2022 This report displays the number of Incidents and Service Requests logged in each month. Also\nit shows total tickets (Incidents + Service requests) logged in each month.\n\u2022 This Report shows the number of tickets logged per country and it has been grouped month\nwise for various application groups such as Maximo, Kronos and BIEM within a date range.\n\nVend_Reported_hrs. rptdesign (Vendor Reported Hours):\n\u2022 This report will fetch the details of vendor type labor working hours for each WOs.\n\nRole: Quality Anchor\n\u2022 Working as Quality Anchor for my team, which involves creating defect prevention analysis,\ntracking all the works and auditing for quality\n\u2022 Providing Statistics about the monthly activities done by the team\n\u2022 From defect prevent analysis focus on specific topic and conducting brainstorming session with\nteam to avoid the defects\n\u2022 Responsible for gathering the requirements from Client and participate in Client Conferences.\n\u2022 Divide the requirements into various sprints based on the velocity.\n\u2022 Distribute the requirements to the team and monitor the development progress and provide\nclarification on the technical/functional requirements.\n\u2022 Mentor for the new members joining the team.\n\nOther Job Related Activities:\n\n\n\n\u2022 Successfully Completed Training in Design Thinking course and Kronos tool which keeps tracks\non Work force management.\n\nMaximo consultant\n\nInfosys Limited\n\naround 4.8 years)\n\u2022 IT professional with 4.4 years of experience in BIRT reporting, application support in IBM MAXIMO\nApplication.\n\u2022 IBM Certified MAXIMO ASSET MANAGEMENT, PURCHASE ORDER, PROCUREMENT, PURCHASE\nREQUISITION Professional\n\u2022 Submitted Internal tutorial document on Configuration of Hover Dialogs and Its usage in Maximo\nV7.6\n\u2022 Worked on various customer requirements and having overall experience with\n\u27a2 Preventive Maintenance\n\u27a2 Maximo automation and implementation\n\u27a2 MBO customizations\n\u27a2 Data Supply chain\n\u27a2 Portals, Workflows Designing\n\u27a2 Reports/Query Creation\n\u27a2 Work order tracking, Invoicing, Purchase order\n\u27a2 Asset Management\n\u27a2 Maximo Integration Framework (MIF)\n\u27a2 Labors, Crews, Security Group Set up\n\u27a2 Domains, Database configuration\n\u27a2 Basics of BIRT, Cognos Reporting, Monitor logs from Admin console.\n\u27a2 Maximo Java customization\n\u27a2 Maximo Installation/Upgradation\n\u27a2 Basic WebSphere function\n\n\u2022 Strong interpersonal skills and the ability to work in team efficiently. Customer Service support\nexperience in a helpdesk environment.\n\u2022 Strong dedication towards work to ensure delivery of projects according to the schedules.\nProactively approached for delivering excellent customer service and liaising with stakeholders.\n\u2022 Ambitious, enthusiastic and highly motivated person with excellent problem solving skills and\nalso having ability to quickly master new technologies and skills.\n\u2022 Maintains effective work behavior in the face of setbacks or pressure. Strong multi-tasking\ncapability, target and deadline oriented.\n\u2022 Engaging and resilient communicator demonstrating influencing skills and an ability to adapt\napproaches to differing situations\n\nRole: Senior Systems Engineer\n\u2022 Working as Maximo Consultant for Bombardier Transportation, Canada\n\n\u2022 Strong knowledge in Maximo configuration using Automation Scripting, Database configuration,\nApplication Designer, Domains, Workflows and other major applications\n\u2022 Providing efficient solution for the issues related to Work orders, Asset configuration, Item\nMaster, Preventive Maintenance, Inventory, Domain related issue\n\n\n\n\u2022 Thorough implementation, support knowledge of Bombardier Transportation Maximo version\n4, 6, 7.1 and 7.6.\n\u2022 Having experience in Maximo Implementation, L2 and L3 support, and deployment activities\n\u2022 Having experience in Incident Management, Problem management, Change management,\nService Request, and other applications.\n\u2022 Delivering 100% effectiveness in resolving ticket by ensuring compliance with client Service\nLevel Agreements (SLAs)\n\u2022 Developing Conditional Queries/Reports to verify the desired results.\n\u2022 Worked on various formal and informal production reports.\n\u2022 Created the template for maintaining the application maintenance activities.\n\u2022 Worked in many configuration management applications for asset management related\nactivities for future implementation.\n\u2022 Working in 24*7 support. Resolved many critical issues which appreciated by client\n\nBIRT Reports\nBTRAMR0001SYSER.rptdesign (Work order report):\n\u2022 My client was using default BTRAMR2 Work order reports. They also use a different work order\nreport in Maximo V4 and V6. The earlier used report have many drawbacks such as\n\nNo Flexibility for modification since it is used by many divisions\nEmpty boxes, lot of missing information\nNot align with Systems objectives and processes\nHistorical context on the asset is missing\nEsthetical issues: Font too small, input fields not instinctive\nNo bar coding used\n\u2022 The outcome of this report is a form to support technician's work and regulation and give\nthem simplicity to input information. It covers the full spectra of requirements of System Services\ndivisions.\n\u2022 This report is site specific one and it will display the WO details along with its task WOs and\nfailure reporting information.\n\u2022 Each site is having their own logo. This current report will display the logo based on site.\n\u2022 It was implemented in V7.1 only. In V7.1 we do not have the option of auto resetting page\nnumbers (i.e., while displaying multiple WOs, for each new WO page number will not be reset\nautomatically) . I have implemented this option in this report.\n\u2022 Language Translation was used for this report in German and French\n\u2022 I did not use IBM templates for this report. I have customized our own template in this report.\n\nBTRAMR0009.rptdesign (Availability Report):\n\n\u2022 This particular report relates to the requirement of the business to be able to create a\nAvailability Graph based on selected Train Assets, a timeframe and selected work types / work\norder parameters.\n\u2022 Availability can be calculated based on a Fleet or a single Train. The business requirement is to\nbe able to select this base as needed to suit the project or view needed at that time.\n\nBTRAMR0027.rptdesign (Card Stock Report):\nExisting report functionality:\n\u2022 Technician requiring a part will take the component in its bin along with an Inventory Card. The\ntechnician reports the consumption at the exit of the storeroom on the Kiosk computer where\n\n\n\nhe scans its Work Order and the Inventory Card in the application Issue and Transfer of Maximo.\nWhen we rebuild something internally, the technician will bring the new assembly in store, grab\nthe Inventory Card and he will report the return in store against the rebuilt Work order.\nObjectives:\n\u2022 Created a card stock report in order to match the existing Inventory Card tool developed by\nSystems.\n\u2022 Increase reporting of consumption\n\u2022 Redefine the process of cycle count (80% efficiency in Systems)\n\u2022 The report will be based on inventory information and will introduce the notion of real time to\nincrease precision\n\nBTRAMR0001SBB.rptdesign (Report Change):\n\u2022 The report BTRAMR0001SBB was developed for the European project. It is based on the\nBTRAMR0001MLM report and currently uses only launch option 2 and 3 of the BTRAMR0001MLM\nreport. The client requested to have option 1 (type=1) of the BTRAMR0001MLM implemented in\nthe BTRAMR0001SBB report as well as the translation of the labels from French to English.\nObjectives:\n\u2022 To have define the type=1, report definition for BTRAMR0001SBB\n\u2022 To have the labels translated from French to English\n\nBTRAMR0002.rptdesign (Systems Purchase Requisition Report):\n\n\u2022 The current PR report does not have bar-coding feature and is formatted too small. This is an\noperational report; it will be used in real time.\n\u2022 Have a common and standardized PR report across all Systems services sites with the support\nof bar-coding\n\nIncident Management Solution report:\n\u2022 The report displays the overall ticket progress. It shows count of tickets raised and assigned to\nboth Infosys and Client queue. We can view the total no. of tickets raised for various application\ngroups such as Maximo, Kronos and BIEM. It also further categories the ticket count based on\nticket priority and the status. This helps in analyzing the data from ticketing tool.\n\u2022 There might be cases where different teams work on the tickets raised. This reports helps to\nanalyses the team wise ticket count. We can there by monitor the performance of various teams.\n\u2022 This report displays the number of Incidents and Service Requests logged in each month. Also\nit shows total tickets (Incidents + Service requests) logged in each month.\n\u2022 This Report shows the number of tickets logged per country and it has been grouped month\nwise for various application groups such as Maximo, Kronos and BIEM within a date range.\n\nVend_Reported_hrs. rptdesign (Vendor Reported Hours):\n\u2022 This report will fetch the details of vendor type labor working hours for each WOs.\n\nRole: Quality Anchor\n\u2022 Working as Quality Anchor for my team, which involves creating defect prevention analysis,\ntracking all the works and auditing for quality\n\u2022 Providing Statistics about the monthly activities done by the team\n\u2022 From defect prevent analysis focus on specific topic and conducting brainstorming session with\nteam to avoid the defects\n\u2022 Responsible for gathering the requirements from Client and participate in Client Conferences.\n\n\n\n\u2022 Divide the requirements into various sprints based on the velocity.\n\u2022 Distribute the requirements to the team and monitor the development progress and provide\nclarification on the technical/functional requirements.\n\u2022 Mentor for the new members joining the team.\n\nOther Job Related Activities:\n\u2022 Successfully Completed Training in Design Thinking course and Kronos tool which keeps tracks\non Work force management.\n\nEDUCATION\n\nBachelor of Engineering in Electronics and Communication in Electronics\nand Communication\n\nNandha Engineering College -  Erode, Tamil Nadu\n\n2012\n\nSecondary Education\n\nGovernment Boys Higher Secondary School -  Pallipalayam, Namakkal, IN\n\n2008\n\nSKILLS\n\nCODA (4 years), Cognos (4 years), Database (4 years), Eclipse (Less than 1 year), IBM COGNOS\n(4 years)\n\nADDITIONAL INFORMATION\n\nTechnical Skills:\nReporting Tools BIRT, Cognos (Basics)\nDatabase Oracle\n\nOperating Systems Windows\nLanguages SQL, JavaScripting\nWeb Technologies Tools HTML SQL Developer, Eclipse", "labels": []}
{"id": 44, "text": "Yogi Pesaru\nDeveloper - Infosys Limited\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Yogi-Pesaru/2ed7aded59ecf425\n\n\u2022 Total IT experience of 4.6 years in IT Industry.\n\u2022 Currently working as SAP PI/XI and Dell Boomi developer.\n\u2022 Good communication, interpersonal and Client interaction skills with clear understanding of\nthe requirements.\n\u2022 Trained on SAP ABAP, SAP BASIS, SAP HANA (HCI), DELL Boomi.\n\u2022 Worked on PI 7.1, 7.31, Dell Boomi Atomsphere.\n\u2022 Good knowledge of Core Java and good command in writing UDFs for Graphical Mapping.\n\u2022 Created Technical systems, Business systems and Software products and components in SLD.\n\u2022 Worked on SLD configurations, IR and ID.\n\u2022 Good capabilities of trouble shooting and resolving different kinds of issues like connectivity\nproblems, mapping exceptions etc.\n\u2022 Experience in working with FILE, SOAP, SOAP (AXIS), RFC, IDOC and XI, JMS, JDBC, ARIBA, REST\nin PI and File, SOAP, Successfactor, Salesforce connectors in Boomi.\n\u2022 Implemented standard features like UDMS, message prioritization, iChannel admin.\n\u2022 Worked on Transports of IR, ID and SLD objects using file and CTS+ transports.\n\u2022 Good knowledge of Graphical Mapping with node functions and UDFs.\n\u2022 Knowledge of XSLT and Java mapping and Dynamic Configuration.\n\u2022 Production support experience in Runtime workbench, communication channel scheduling,\nMessage Monitoring.\n\u2022 Good knowledge on SLD configuration, IDoc configuration in ECC and PI and alert configuration.\n\u2022 Experience in handling quarterly and emergency releases and providing post release support.\n\nWORK EXPERIENCE\n\nDeveloper\n\nInfosys Limited -\n\nOctober 2015 to Present\n\nInfosys Limited Senior Systems Engineer 01st Oct 2015 - till date\n\nPROJECT SUMMARY - BOOMI\nProject Name Success factor integration with Sterling\nClient SYSCO\nEmployer Infosys Limited\nRole Developer\nTeam Size 3 members\nTechnologies Java script, groovy script, SuccessFactor, Sterling, Webservice\nDuration 2 years\n\nProject Abstract:\n\nhttps://www.indeed.com/r/Yogi-Pesaru/2ed7aded59ecf425?isid=rex-download&ikw=download-top&co=IN\n\n\nThe integration for Sterling is a two-way integration to support the investigation and confirm\naccuracy of background information provided by selected candidates who have received have\npassed phone screening & or accepted verbal offers of employment.\n\nBoomi is the middle ware between success factor and sterling systems.\nBoomi sender interface picks up the profiles from SF and send to sterling for background\nverification. After receiving request from boomi sterling will send synchronous response with an\nOder number, which will be updated to SF synchronously. After some time boomi receiver process\n(listener process) receives the asynchronous results from sterling which are updated back to SF.\n\nRole and responsibilities:\n\n\u2022 Requirement analysis.\n\u2022 Design as per the requirement.\n\u2022 Process development as per design.\n\u2022 Unit testing the applications.\n\nPROJECT SUMMARY - PI\nProject Sysco MS Development\n\nSystems Engineer\n\nInfosys Limited -\n\nSeptember 2013 to September 2015\n\nEDUCATION\n\nB. Tech in ECE\n\nVidya Bharati Institute Of Technology -  Hindupur, Andhra Pradesh\n\n2008 to 2012\n\nHosanna National High School\n\n2006\n\nSKILLS\n\nC+ (Less than 1 year), Citrix (Less than 1 year), integration (2 years), INTEGRATOR (2 years),\nJava (2 years)\n\nADDITIONAL INFORMATION\n\nTECHNICAL PROFICIENCY\n\nIntegration Tools SAP PI (7.1, 7.31), Eclipse, Citrix, SQL Server Data, SOAP UI, NWDS, Solution\nManager, HPQC, POSTMAN\nBackend Technologies Oracle and MS SQL.\nLanguages Core Java, C++, C.\n\n\n\nKEY STRENGTHS\n\u2022 Initiative, Leadership Qualities and Team spirit.\n\u2022 Proficiency in Communication skills, Positive attitude.\n\u2022 Good knowledge about Technology and interest towards new learning.\n\u2022 Responsibility and patience to do work assigned by superiors.", "labels": []}
{"id": 45, "text": "Anurag Asthana\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Anurag-Asthana/ea7451b2bdb6115a\n\n\u2022 Looking forward for joining a company where my creative thinking and excellent skills in\napplication development using modern development tools will be utilized.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nApril 2017 to Present\n\nLocation: Pune, India\nClient: Insurance Client from London\nDuration: April 2017 -Till Date\nTechnology/Tools: Microsoft SharePoint 2013, Azure Cognitive Services, Azure Service Bus, Azure\nBlob Storage, .net framework 4.7\n\nResponsibilities:\n\n\u2022 Independently worked on designing solution architecture of the project.\n\u2022 Lead team from technical front in all the components built\n\u2022 Followed Agile development approach to efficiently manage continuous development process\nand incremental requirement changes\n\u2022 Design SharePoint components using SharePoint CSOM and .net framework 4.7\n\u2022 Design Azure components using azure .net sdks and .net framework 4.7\n\u2022 Implement Micro Services architecture using Azure Service Bus\n\nProject Title: SharePoint 2013 Application Development and Enhancement\n\nInfosys LTD -  London -\n\nFebruary 2017 to March 2018\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nAugust 2015 to January 2017\n\nSharePoint Designer\n\nMicrosoft SharePoint -\n\n2017 to 2017\n\n2013, Nintex.\n\nResponsibilities:\n\u2022 Investigated and identified requirements via process flows, use cases communicated with\nSubject Matter Experts to define system requirement.\n\nhttps://www.indeed.com/r/Anurag-Asthana/ea7451b2bdb6115a?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Suggested improvements to be included into new system for Skills Alignment Portal\n\u2022 Followed Agile development approach to efficiently manage continuous development process\nand incremental requirement changes\n\u2022 Independently designed, developed and delivered multiple forms and workflows over\nSharePoint 2013 start from scratch using Nintex\n\u2022 Designed and Implemented useful components using SharePoint out-of-the-box capabilities and\nSharePoint REST web service\n\u2022 Utilized SharePoint APPs technology to create custom enhancements on current sites for\ncustomer satisfaction and efficiency\n\u2022 Developed underwriting support applications using asp.net MVC / Web API.\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nDecember 2013 to July 2015\n\nEnterprise IT Support\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nSeptember 2013 to November 2013\n\nLocation: Pune, India\nClient: Oil and Gas Client from USA\nDuration: September 2013 - November 2013\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nJune 2013 to August 2013\n\nLocation: Pune, India\nClient: Oil and Gas Client from USA\nDuration: June 2013 - August 2013\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nFebruary 2013 to May 2013\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nNovember 2012 to January 2013\n\nLocation: Pune, India\nClient: Infosys Ltd.\nDuration: November 2012 - January 2013\n\nSharePoint Designer, Avepoint Docave\n\nMicrosoft SharePoint -\n\n2012 to 2012\n\n6.\n\nResponsibilities:\n\n\n\n\u2022 Responsible for creating SharePoint hosted apps.\n\u2022 Responsible for developing web services [WEB API's].\n\u2022 Responsible for creating ECMA scripts.\n\u2022 Responsible for creating console applications using SharePoint Client Object Model.\n\u2022 Responsible for migrating SharePoint MOSS 2007 sites to SharePoint 2013 using Avepoint\nDocave.\n\u2022 Responsible for developing remote site provisioning mechanism using CSOM\n\u2022 Responsible for creating provider hosted apps using asp.net mvc.\n\nProject Title: Operating System\n\nMicrosoft Fast Search, SharePoint Designer\n\nMicrosoft SharePoint -\n\n2010 to 2010\n\nResponsibilities:\n\n\u2022 Responsible for maintaining database of the project.\n\u2022 Responsible for designing page layouts.\n\u2022 Responsible for creating ECMA scripts.\n\u2022 Responsible for creating console applications using SharePoint Client Object Model.\n\u2022 Responsible for creating SSIS solution to import data from multiple sources using SharePoint\nweb services.\n\u2022 Responsible for creating custom SharePoint alerts using SSIS solution.\n\u2022 Responsible for creating SSRS solution to create Dashboards.\n\u2022 Responsible for creating Stored Procedures and Functions used for various operations.\n\u2022 Responsible for customizing list forms.\n\u2022 Responsible for creating Search pages using Microsoft FAST search 2010.\n\nMicrosoft SharePoint -\n\n2010 to 2010\n\nResponsibilities:\n\n\u2022 Responsible for fixing bugs in SharePoint applications\n\u2022 Responsible for automating bug fixing process.\n\u2022 Responsible for interacting with clients for understanding business requirements.\n\u2022 Responsible for enhancing SharePoint application as per business requirements.\n\u2022 Identifying limitations and suggesting appropriate solutions.\n\nMicrosoft InfoPath Designer\n\nMicrosoft SharePoint -\n\n2010 to 2010\n\n2010, Microsoft SharePoint Designer 2010, Microsoft Office 2010.\n\nResponsibilities:\n\n\n\n\u2022 Responsible for creating multiple custom lists and document libraries.\n\u2022 Responsible for creating and customizing InfoPath forms and associating it with document\nlibraries.\n\u2022 Responsible for creating SharePoint Designer Workflows.\n\u2022 Responsible for customizing list forms and views.\n\u2022 Responsible for creating Excel reports in SharePoint.\n\nProject Title: 5 Day Close Monitoring Tool\n\nTechnology/Tools: Microsoft SharePoint 2010, Microsoft SQL Server 2008 R2, Microsoft Visual\nStudio 2010, Microsoft InfoPath Designer 2010, Microsoft SharePoint Designer 2010, Microsoft\nOffice 2010\nResponsibilities:\n\n\u2022 Responsible for creating custom lists, document libraries, SharePoint groups.\n\u2022 Responsible for creating custom permission levels in SharePoint and defining permission\npolicies on lists, libraries and web parts.\n\u2022 Responsible for maintaining database of the project.\n\u2022 Responsible to create SSIS packages.\n\u2022 Responsible to create power pivot reports.\n\u2022 Responsible for creating dashboards on SharePoint site using Power Pivot library.\n\u2022 Responsible for creating tool for generating power pivot reports on multiple servers.\n\nMicrosoft Designer\n\nMicrosoft SharePoint -\n\n2010 to 2010\n\n2010.\nResponsibilities:\n\n\u2022 Responsible for creating multiple custom lists and document libraries.\n\u2022 Responsible for creating and customizing InfoPath forms and associating it with document\nlibraries.\n\u2022 Responsible for creating SharePoint Designer Workflows.\n\u2022 Responsible for customizing list forms and views.\n\u2022 Responsible for creating custom permission levels and applying it to various lists and libraries.\n\nEDUCATION\n\nBachelor of Technology in Information Technology\n\nGraphic Era University -  Dehra Dun, Uttarakhand\n\nMay 2012\n\nSenior Secondary -  Kashipur, Uttarakhand\n\n2008\n\n\n\nSKILLS\n\nMICROSOFT SHAREPOINT (7 years), SHAREPOINT (7 years), .NET (6 years), MICROSOFT VISUAL\nSTUDIO (6 years), VISUAL STUDIO (6 years)\n\nADDITIONAL INFORMATION\n\nSKILLS\n\u2022 Vast knowledge of developing and implementing applications based on client's needs.\n\u2022 Professional Experience in creating SharePoint solutions using C#, JavaScript etc.\n\u2022 Professional Experience in creating asp.net web applications using MVC, Entity Framework.\n\u2022 Professional experience in creating application using Azure micro services architecture\n\nTECHNICAL EXPERTISE\n\nLanguages and Software\nC, C#.net, Asp.net MVC 5, Entity Framework 6, Microsoft Office, Microsoft SharePoint 2010,\nMicrosoft SharePoint 2013, Microsoft, SharePoint Designer, Microsoft Visual Studio 2008,\nMicrosoft Visual Studio 2010, Microsoft Visual Studio 2012, InfoPath, HTML, CSS, JavaScript,\njQuery, SharePoint Apps using MVC and KnockoutJS, Nintex forms and Workflows, Azure\n\nMSBI SQL Server 2008, SQL Server 2012, SSIS, SSRS\nOperating System Windows Family\n\nTOTAL EXPERIENCE\n\nNo. Company Role Duration\n1. Infosys Ltd Technology Analyst 5 Years 11 Months", "labels": []}
{"id": 46, "text": "Syed Sadath ali\nCoimbatore - Email me on Indeed: indeed.com/r/Syed-Sadath-ali/cf3a21da22da956d\n\nWORK EXPERIENCE\n\nSearching for good salary\n\nApple , Google, Microsoft -\n\n2017 to Present\n\nEDUCATION\n\nBCA,MBA\n\nKGISL\n\nSKILLS\n\nC++, Hacking, Programming\n\nhttps://www.indeed.com/r/Syed-Sadath-ali/cf3a21da22da956d?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 47, "text": "Nida Khan\nTech Support Executive - Teleperformance for Microsoft\n\nJaipur, Rajasthan - Email me on Indeed: indeed.com/r/Nida-Khan/6c9160696f57efd8\n\n\u2022 To be an integral part of the organization and enhance my knowledge to utilize it in a productive\nmanner for the growth of the company and the global.\n\nINDUSTRIAL TRAINING\n\n\u2022 BHEL, (HEEP) HARIDWAR\nOn CNC System&amp; PLC Programming.\n\nWORK EXPERIENCE\n\nTech Support Executive\n\nTeleperformance for Microsoft -\n\nSeptember 2017 to Present\n\nprocess.\n\u2022 21 months of experience in ADFC as Phone Banker.\n\nEDUCATION\n\nBachelor of Technology in Electronics & communication Engg\n\nGNIT institute of Technology -  Lucknow, Uttar Pradesh\n\n2008 to 2012\n\nClass XII\n\nU.P. Board -  Bareilly, Uttar Pradesh\n\n2007\n\nClass X\n\nU.P. Board -  Bareilly, Uttar Pradesh\n\n2005\n\nSKILLS\n\nMicrosoft office, excel, cisco, c language, cbs. (4 years)\n\nhttps://www.indeed.com/r/Nida-Khan/6c9160696f57efd8?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 48, "text": "Fenil Francis\nhead of operation and logistics\n\nTrichur, Kerala - Email me on Indeed: indeed.com/r/Fenil-Francis/445e6b4cb0b43094\n\nTo succeed in an environment of growth and excellence and earn a job which provides me job\nsatisfaction and self development and help me achieve personal as well as organization goals.\n\nWORK EXPERIENCE\n\nManager\n\nMasters equipments -  Trichur, Kerala -\n\nMay 2017 to Present\n\nSales Manager\n\nMicrosoft Power -  Trichur, Kerala -\n\nMay 2017 to Present\n\n2. Microsoft Office: Microsoft Word, Microsoft Power point, Microsoft Excel\n\nEDUCATION\n\nB.Com in Computer Application\n\nMadurai Kamaraj University -  Madurai, Tamil Nadu\n\n2017\n\nSSLC\n\nKerala State Board\n\n2012\n\nSKILLS\n\nPROBLEM SOLVING (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS &amp; ABILITIES:\n\u2022 Good Communication Skill\n\u2022 Sincere\n\u2022 Hard working\n\u2022 Leadership skill\n\u2022 Pleasing personality\n\u2022 Problem solving capability\n\nhttps://www.indeed.com/r/Fenil-Francis/445e6b4cb0b43094?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 49, "text": "Gaurav Soni\nNew Delhi, Delhi - Email me on Indeed: indeed.com/r/Gaurav-Soni/d492b5b1697f7f66\n\nWORK EXPERIENCE\n\nSoftware Developer\n\nION Trading -\n\nJune 2016 to Present\n\nAs part of ION Trading, I have been working as a core product developer on a Repurchase\nAgreements(REPO) Trading Solution, called Anvil 9 Trading Solution. Currently I am working on\nOrder Management System, which is a component/service that enable the traders to capture\norders from emails and chats. I have also worked on Apache CoreNLP to leverage its Named\nEntity Recognition feature to extracting business keywords from a given text.\n\nIntern\n\nMicrosoft -  Hyderabad, Telangana -\n\nJune 2015 to August 2015\n\nI was part of the Microsoft Service Global Delivery Internship for two months in Hyderabad. On\nmy internship, I got an opportunity to develop a Windows 10 application.\n\nEDUCATION\n\nB.Tech(Computer Science) in CSE\n\nGGSIPU\n\n2012 to 2016\n\nSKILLS\n\nC# (Less than 1 year), Java, OOPs, Guice, Microservice architecture, Software Development,\nXAML, ATDD, SOLID, SOA, Scrum, Robot Framework, TDD\n\nADDITIONAL INFORMATION\n\nSkills: JAVA, C#, WPF, XAML, Robot Framework, Scrum, Guice, ATDD, SOA, Fixed Income, CoreNLP,\nSOLID\nprogramming, OOPs, NLP, Microservice architecture.\n\nhttps://www.indeed.com/r/Gaurav-Soni/d492b5b1697f7f66?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 50, "text": "Viny Khandelwal\nSelf-employed in Family Business - SELF EMPLOYED\n\nKullu, Himachal Pradesh - Email me on Indeed: indeed.com/r/Viny-\nKhandelwal/02e488f477e2f5bc\n\n\u2022 Currently pursuing my certifications in Digital Marketing.\n\u2022 2 year's experience in managing, maintaining and assisting to run a camping site.\n\u2022 Additional experience in event managing and emcee in Army Wife Welfare Association (NGO)\n\u2022 1-year self-employed in Kudi Firang.\n\u2022 2 years of work experience in Infrastructure Management and Support in TCS.\n\u2022 Worked as Soft skill trainer in TCS.\n\u2022 Worked as Operation Analyst for Microsoft Server management team.\n\u2022 Worked as Application Support engineer for PricewaterhouseCoopers, USA.\n\u2022 Worked as Technical Support for Microsoft Office, Outlook and PowerPoint.\n\nWORK EXPERIENCE\n\nSelf-employed in Family Business\n\nSELF EMPLOYED -\n\nFebruary 2015 to Present\n\nof tourist camping site. Managing, maintaining and assisting to run a camping site.\n\u2022 Self-employed in Kudi Firang under self-proprietary. Designing Digital print art and getting it\nprinted for Handbags and Home D\u00e9cor. Actively participated in exhibitions and Trade shows.\n\nSystems Engineer\n\nMicrosoft Corporation -  Hyderabad, Telangana -\n\nOctober 2012 to January 2015\n\nPROJECTS:\n1. Microsoft Server Management\n\n\u27a2 Client: Microsoft Corporation.\n\u2022 Role: Microsoft Server infrastructure remote management - Remotely logon to and manage the\nMicrosoft Corporation datacenter servers all over the world and configure/ deploy according to\nthe incident/change management request/ticket.\n\u2022 Responsibilities: Incident management and change management, performing simple changes\nand re-assigning incident tickets to on-site work, server rename, rebuild, configuration tasks,\ndatacenter server deployments and hardware procurement/replacement follow-ups.\n\n2. PwC Application Support\n\n\u27a2 Client: PricewaterhouseCoopers.\n\u2022 Role: Incident management and other applications for corporates and firms in USA.\n\nhttps://www.indeed.com/r/Viny-Khandelwal/02e488f477e2f5bc?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Viny-Khandelwal/02e488f477e2f5bc?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Responsibilities: Handling deployment of firm application upgrades and reporting of bugs and\nerrors to development team, SLA and quality report submission and training agents according\nto new updates and best practices shared, attending team meetings for process development\nand improvement, maintain existing and generate new operational support documentation,\ncommunicating with 3rd party service providers for account activations, upgrades, password\nresets and membership renewals, troubleshooting system software and hardware configuration\nissues, log issue and user contact into customer database, translate end-user business needs\ninto working solutions.\n\nEDUCATION\n\nBachelor of Engineering in Digital Marketing\n\nNitte Meenakshi Institute of Technology -  Bengaluru, Karnataka\n\nSKILLS\n\nEXCHANGE (Less than 1 year), HTML (Less than 1 year), KITCHEN (Less than 1 year), LINUX\n(Less than 1 year), VMWARE (Less than 1 year)\n\nGROUPS\n\nAWWA\n\nNovember 2015 to Present\n\nADDITIONAL INFORMATION\n\n\u2022 Photography and video editing for Viny's Kitchen (VLOG on YouTube)\n\u2022 Good communication skill and Public Speaking as Orator.\n\u2022 Knowledge on Windows Server [\u2026] Linux, Exchange server, VMware, HTML.\n\nPERSONAL DOSSIER\n\nName: Viny Khandelwal\nPhone: [\u2026]\nEmail: <EMAIL>\nHusband's Name: Capt. Anuj Mahant\n\nPermanent Address: HNO - 240, Gandhinagar,\nDhalpur, [\u2026]", "labels": []}
{"id": 51, "text": "amarjyot sodhi\nVoice and Accent Trainer :Masters in journalism and communication with\n3 years experience\n\nFaridabad, Haryana - Email me on Indeed: indeed.com/r/amarjyot-sodhi/ba2e5a3cbaeccdac\n\nTo constantly learn, enhance my skills and capabilities to reach higher level of competence\nand apply my knowledge and skill to the best of my ability in the interest of the organization.\nAccomplishment counts, not the non-executed ideas. So I just constantly execute the ideas in\nquest for the accomplishment.\n\nWORK EXPERIENCE\n\nSales Associate\n\nShuttl -  Faridabad, Haryana -\n\nSeptember 2017 to May 2018\n\nQuery management and outbound sales\n\nAssociate\n\nSutherland -  Chennai, Tamil Nadu -\n\nOctober 2016 to June 2017\n\n\u25cf Query Management\n\u25cf Outbound/Inbound Calling\n\u25cf Collections\n\u25cf Customs Documentation work\n\nvoice and accent trainer\n\nteleperformance microsoft -  Jaipur, Rajasthan -\n\nOctober 2014 to February 2015\n\nResponsibilities\nTo train the students on there comm skills and cx handling skill\n\nEDUCATION\n\nmasters in journalism and communication in mass comm client servicing\n\namity -  Noida, Uttar Pradesh\n\n2011 to 2013\n\nhttps://www.indeed.com/r/amarjyot-sodhi/ba2e5a3cbaeccdac?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 52, "text": "Sameer Kujur\nOrrisha - Email me on Indeed: indeed.com/r/Sameer-Kujur/0771f65bfa7aff96\n\nWORK EXPERIENCE\n\nApp develop\n\nMicrosoft -\n\nAugust 2016 to Present\n\nEDUCATION\n\nElectrical engineering\n\nVSSUT,burla\n\nSKILLS\n\nApplication Development, Software Testing\n\nhttps://www.indeed.com/r/Sameer-Kujur/0771f65bfa7aff96?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 53, "text": "Zaheer Uddin\nTechnical Project Manager\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Zaheer-Uddin/fd9892e91ac9a58f\n\nTo be associated with a dynamic team in a progressive organization that gives me the scope to\napply and enrich my knowledge and skills with continual learning and professional growth.\n\nWORK EXPERIENCE\n\nTechnical Project Manager\n\nDELL EMC -\n\nAugust 2015 to May 2018\n\nProvide expertise to support the project team in the following areas:\n* Activity and resource planning.\n* Analyzing and managing project risk.\n* Planning, organizing, and Leading the Project teams from the entire metrics standpoint.\n* Scope &amp; Charter Management.\n* Handled the Account Level Audit (ERM, ITIL)\n* Estimations and cost management.\n* Project Timeline planning and management\n* Procurement, Software Asset management, Change management and Configuration\nmanagement.\n* Issue resolution and escalation\n* Communications &amp; status reporting\n* Scheduling, attending and running all project meetings.\n* Identify, track, and resolve/escalate project impediments, risk, issues, actions.\n* Implementing Power BI Business Analytics tool to analyze data and share insights.\n* Implemented Six Sigma project (DMAIC) for improving the CSAT Score overall.\n* Implemented LEAN project for optimizing the resource management and the Average handling\ntime on the tickets/resolutions.\n\nTechnical Lead/Operations Manager\n\nMicrosoft -\n\nJuly 2013 to July 2015\n\n* Managing 24x7 IT infrastructure services with the scope of Incident management, Security\npatching and software deployment management services for Microsoft Outlook.com email\nservice and O365 cloud services with team size of 68 people.\n* Transitioned and consolidated the Incident management, security patching and deployment\nmanagement work for optimizing the team by improving the productivity and reducing the cycle\ntime.\n* Prepared technical architecture documents integrating all components of the project for better\ntroubleshooting.\n\nhttps://www.indeed.com/r/Zaheer-Uddin/fd9892e91ac9a58f?isid=rex-download&ikw=download-top&co=IN\n\n\n* Deploying builds &amp; security patches as per Build calendar schedule and co-coordinating\nthe issues that arise during the build.\n* Monitoring, Remote administration, maintenance of a Datacenter Servers comprising of\n10,000+ Servers.\n* Trouble shooting on HP storage Architecture in different SKU's with RAID concepts.\n* Implementing disaster recovery procedures to ensure minimal downtime and data loss.\n* Re-building Operating Systems on servers (2003, 2008, 2008 R2) that are in the Hotmail\nEnvironment.\n* Transitioned windows infrastructure support services involving the frontend, inbound, outbound\nand storage infrastructure.\n* Handled security patching, application deployment and incident management for Windows,\nSQL and HP storage servers.\n* Participate in Feature Specification and Release Reviews to ensure complete understanding of\nthe features being deployed.\n* Created the knowledge base articles for the debugging of the software application for the benefit\nof the Customer, Partner and the engineering team.\n\nSenior Service Engineer\n\nEMC -\n\nJanuary 2011 to June 2013\n\n* Experience in installing and maintaining Windows Server 2000/2003, 2008.\n* Alert and resolve any performance issues and notify end-users and resolve any storage\nshortages issues.\n* Monitor and resolve any issues related to Usage, Performance, and availability on storage.\n* Install and configure the EMC Disk tender (archiving) application as per the customer\nrequirements.\n* Troubleshooting NAS, CAS and DAS Storage issues on the servers.\n* Replicate the break-fix implementation in the test machines to test the functionality of the\nbreak-fix and reproduce the same on the production server.\n* Perform testing of the registry exports made from the Production environment and try and test\nit on the Test environment to resolve issues.\n\nSystems Engineer\n\n-\n\nDecember 2007 to December 2010\n\n* Designing and developing computer hardware and support peripherals, including central\nprocessing units (CPUs), support logic, microprocessors, custom integrated circuits, printers, and\ndisk drives.\n* Managing, monitoring and troubleshooting all installed systems and infrastructure.\n* Installing, configuring, testing and maintaining operating systems application software and\nsystem management tools.\n* Ensuring the highest levels of systems and infrastructure availability.\n* Handling Level-2 technical escalations Tickets/Calls.\n* Performing Upgrades, Installation of Software and Drivers and essential software\ntroubleshooting.\n\n\n\n* Assisting end users in deploying Updates, services packs and hot fixes using Windows Update,\nAutomatic Updates.\n* Setting up Active Directory, creating user accounts and providing permissions as requested by\nthe network admin team.\n\nEDUCATION\n\nBSc\n\nOsmania University\n\n2007\n\nBoard of Secondary Education -  Hyderabad, Telangana\n\n2004\n\nDiploma in Computer Application\n\nMac infotech Center", "labels": []}
{"id": 54, "text": "Abdul B\nArabic Language supporter (Content Analyst)\n\nKarnataka, Karnataka - Email me on Indeed: indeed.com/r/Abdul-B/eb2d7e0d29fe31b6\n\n\u2022 7+ years of experience in supporting ,specially Arabic Language content Arabization.\nGood expertise in working at MNC. As Thomson Reuters for EMEA Project as giving\nlanguage support as a content analyst for financial Data.\n\u2022 Language testing in LG soft India as a language tester engineer.\n\u2022 The Arabic project of CRM for Microsoft project with language testing experience in\nWipro technologies.\n\u2022 Arabic, Urdu, English and Mobile features testing in L G Soft India Bangalore.\n\u2022 Present working at Al-wadi Int School as an Arabic/Islamic teacher.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nArabic Language Trainer\n\nAssociated -  Bengaluru, Karnataka -\n\n2010 to 2012\n\nand Arabization Jobs and\nworked as in interpreter for several clients. Language CA for (Arabic) CRM project in Wipro\nTechnologies.\n\nArabic Linguist\n\nHyderabad, Telangana -\n\nJune 2010 to September 2010\n\nTeam Size 30\nWindows Server 2008 R2, MS Office 2010, Windows Vista,\nVisual Studio 2008 SP1, Microsoft Exchange Server 2007,\nEnvironment Software Windows Server 2008 R2 Active Directory Server, Windows\nServer 2008 R2 Hyper-V, Product Studio, UI And\nfunctional automation testing frameworks.\n\nDescription:\nMicrosoft Dynamics CRM is CRM Software application that the businesses use to track and\nmanage the interactions with their customers - such as phone calls and emails. This project\ninvolves\ntesting the Microsoft Dynamics CRM Version5 for around 16 languages for localization and\nfunctional issues.\n\nRoles and Responsibilities:\n\u2022 Created the test cases for Microsoft Dynamics CRM modules\n\u2022 Execution of the test cases and updating the results in the bug tracking and logging internal\n\nhttps://www.indeed.com/r/Abdul-B/eb2d7e0d29fe31b6?isid=rex-download&ikw=download-top&co=IN\n\n\ntool \"Product Studio\"\n\u2022 Creating automation test cases\n\nMiddle East language tester engineer\n\n-\n\n2006 to 2010\n\nTest Engineer (Arabic language)\n\nMICROSOFT INC -\n\nJune 2007 to October 2007\n\nDuration: 2007 June -Oct 2008\nResume\nWork Experience (Wipro Technologies) CRM project\nProject Details\nPROJECT#5 Microsoft Dynamics CRM\nClient MICROSOFT INC.\nRole Test Engineer (Arabic language)\nTesting Automated (UI &amp; Functional) &amp; Manual\n\nEDUCATION\n\nMaster's\n\nSKILLS\n\nENGINEER (4 years), TESTING (Less than 1 year), UI (3 years), USER INTERFACE (Less than 1\nyear), ANALOG (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTechnical Skills: LG soft India (Bangalore)\n\nApart from teaching and training, I posses a good know how and have experience of working on\nmobilization in the field of Telecommunication while I was working as a UI tester (Software Tester\nEngineer Using with TD ) for Middle east Languages at LG Soft India.\n\nPROJECT DETAILS\n\nPlatform: Analog Devices\nDomain: GSM and GPRS\nDefect Tracking Tool: Test Director (Mercury Quality Centre)\nTest Tool: Genie (Target Testing &amp; Signal Logs)\n\nDescription: The ADI model of LG Electronics is a color display model, Contains the features of\nTelephony services, SMS, MMS, WAP, Java Application, Bluetooth and advanced multimedia\nfeatures like MP3, Camera, FM, Video Recording. This model supports PC Sync tool that is used to\n\n\n\nconnect Mobile Station with PC.\n\nResume\nResponsibilities:\n\n\u2022 Teaching students the language skills and interaction in the language of Arabic. Also\nteaching and interacting with Arabs as a language translator.\n\u2022 Interaction with Arab professionals during the tenure as a software tester in LG soft.\n\u2022 Writing test cases for new features, and Updating of check lists.\n\u2022 Involved in feature testing modules like Messages, Settings, and Browser.\n\u2022 Reported bugs and execution in Test Director.\n\u2022 Conduct Target Testing on Mobile Station (Call Flow, Roaming &amp; Mobility Management,\nRadio Resource stability and GPRS connection) using test Tool.\n\u2022 Conduct IOT in different locations of India.\n\u2022 Good knowledge command in the Middle East languages Arabic, Urdu &amp; Farsi", "labels": []}
{"id": 55, "text": "Bike Rally\nChief Coordinator of LEAR\n\nPalghat, Kerala - Email me on Indeed: indeed.com/r/Bike-Rally/e00d408e91e83868\n\nEducation\n\nWORK EXPERIENCE\n\nChief Coordinator of LEAR\n\nMicrosoft -\n\nJune 2011 to July 2013\n\nunder Office Kerala State Board\n\nEdius\ntally\n\nProshow gold Key roles held\nPhotoshop\n\nChief Coordinator of LEAR\nIt is a well equipped training team under the mentorship of JCI\nInternational member and HR international Trainer Dr. Thomas K George,\nChairman of LEAD College of Management, Dhoni.\n\nGet in Touch Key Organizer of Le Adventure, I LEAD and LEADing bands\nThese programs were conducted by LEAD College of Management.\n\nSpecial Police Officer during Panchayat elections\n\nMicrosoft -\n\nJune 2011 to July 2013\n\<EMAIL>\n\nEDUCATION\n\nMBA in marketing-tourism\n\nCalicut University -  Calicut, Kerala\n\n2016 to 2018\n\nhttps://www.indeed.com/r/Bike-Rally/e00d408e91e83868?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 56, "text": "Girish Acharya\nTechnical Architect & Sr. Software Engineer\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Girish-Acharya/6757f94ee9f4ec23\n\nI would like to describe myself as a hard-core software engineer and technical architect\npassionate for technology and someone who loves to take challenges head-on. I have been using\ncombined engineering principles wherein I was responsible for designing, developing, testing,\ndeploying and monitoring highly scalable distributed applications and services. \n\nWhile I have always been working using Microsoft technologies, I got chance to work for Microsoft\nin as a full-time employee last year. My focus as part of this role was as a Dev Ops/Site Reliability\nEngineer. The main requirement of this role was to keep-lights-on in all data centers for Windows\nDeveloper Center. While Service Engineering aspect was the main requirement, I used my\ntechnical architect and software tools for inside-out and outside-in monitoring and telemetry. \n\nAs a technical architect, I have worked in Accenture for about 7+ years. My role @ Microsoft\naccount was to demonstrate technical leadership, design technical architecture, propose\nand introduce standard design and architectural patterns and practices, incorporate new\nrequirements and feedbacks from client and execute multiple quick prototypes. As part of this\nrole \u2013 I architected Microsoft IT\u2019s (MSIT) biggest Platform As A Service (PaaS) solution back in\nthe days.\n\nI have executed multiple client facing projects wherein I was responsible for designing,\narchitecting the compute, storage, application and deployment architecture. I have experience\nin designing and architecting systems for Business Continuity and Disaster Recovery (BCP/\nDR strategies). I have designed and architected solutions for \u201cTesting in production\u201d. I have\nexperience in designing and architecting for extremely high scale (~ 3 billion requests/day). I\nhave designed and implemented auto healing, smooth traffic routing and eventual consistency\nin multiple client facing projects. \n\nI am in the Information Technology industry since 2001 working for various software organizations\nin India for 4 years till I joined Accenture in 2006. I came to USA in 2010 and was working as\nconsultant for Microsoft. I joined Microsoft in May 2015 as a full-time employee. I earned my\nElectronics Engineering degree in year 2000, Diploma in Advance Computing degree in year 2001\nand completed Microsoft Certified Professional Developer and Enterprise Architect certification\n(MCPDEA) in year 2007. I am also Microsoft Azure Certified Developer and Architect. \n\nI am frequent community contributor. I blog at http://girishthegeek.wordpress.com/ \n\nI have worked and published an asset called Framework for Ops Team considering Windows Azure\nand it was very well received by the clients and appreciated by the managers.\n\nI wrote a white paper related to AppFabric Service Bus which got published in MSDN.\n\nWilling to relocate: Anywhere\n\nhttps://www.indeed.com/r/Girish-Acharya/6757f94ee9f4ec23?isid=rex-download&ikw=download-top&co=IN\n\n\nWORK EXPERIENCE\n\nTechnical Architect\n\nAccenture Services -  Pune, Maharashtra\n\nSoftware Engineer 2\n\nMicrosoft -  Redmond -\n\nJune 2016 to February 2017\n\nEngagement and monetization (Microsoft \u2013 Universal Store/Windows Developer Center)\nJune 2016\u2013 Recently \n\n\u3013 Technologies used:\nMicrosoft Azure, C#, SQL, PowerShell, App Insights, Azure Data Factory, Redis Cache, API\nManagement Gateway, Azure App Services, Akamai, Azure CDNs, Azure Traffic Manager,\nAutomated Unit Testing, Web Testing, Coded Unit Tests, Performance Testing etc. Tools \u2013 SpecFlow,\nJMeter, Junit, NUNit, MoQ, Fluent Assertions, VSTS, GIT, Microsoft Test Manager, Continuous\nIntegration and deployment, maintaining build definitions \n\n\u3013 Roles Played:\n\no Technical architect \no Designing and architecting for compute, storage, application, security and deployment\narchitecture \no Designing and architecting for Business Continuity and Disaster Recovery (BCP DR)\no Designing and architecting for the Testing In Production (TIP)\no Designing and architecting for auto healing and auto scaling pattern \no Designing and developing for monitoring \n\u3013 Design and develop inside out and outside in monitoring and logging \n\u3013 Design and develop synthetic web tests\n\u3013 Design and develop watchdogs \no Design and develop the global traffic management using Akamai \no Keeping lights on for the services in all data centers \no Firefighting drills and actual firefighting \no Live site issue tracking, management, coordination and resolving \no Capacity monitoring and management \no Code reviews \no Implemented the security architecture\n\nEDUCATION\n\nBachelor of Engineering in Microsoft\n\nC-DAC\n\n\n\nSKILLS\n\nAzure (8 years), Asp.Net (8 years), C# (9 years), Web API (6 years), Sql (6 years), Technical\nArchitect (5 years), Automation Testing (5 years)\n\nLINKS\n\nhttp://girishthegeek.wordpress.com/\n\nhttps://www.linkedin.com/in/girishazure\n\nCERTIFICATIONS/LICENSES\n\nMicrosoft Certified Professional Developer\n\nMicrosoft Certified Professional Developer\n\nMicrosoft Certified Enterprise Architect\n\nADDITIONAL INFORMATION\n\nTechnical Expertise\n\n*Technical Architecture, Microsoft Azure Platform, Microsoft .NET Platform, C#, ASP.NET*\n\n* Designing/Architecting/Building/Testing/Deploying apps/APIs using Windows Azure, SQL Azure,\nAzure Storage, REST/WCF, AppFabric Cache, Service Bus, Topic and Subscription, Access Control\nServices from last 7 years.\n* Azure Application Architecture: Web Role, Worker Role, VM Role, admin mode, start-up tasks\nintegrating with on premise services using App Fabric Service Bus\n* Azure Security Architecture: Web Role security using SSL, WCF Security using certificates,\nData Encryption using certificates, Authentication using ADFS/STS, Single sign on using ACS.\nParticipated in ACE reviews, and provisioned related documentation/code\n* Azure Operations Architecture: Logging and monitoring using Windows Azure Diagnostics,\nintegration with AVICode, SCOM, disaster recovering and business continuity planning and\nimplementation, automated builds and deployments.\n* Azure Data Architecture: Understands SQL Azure, Windows Azure Storage (Tables, Blobs,\nQueues), Document DB, SQL Azure Data Sync, SQL Azure Federation etc.\n* Deployment Architecture: Continuous integration and deployment. Auto Build, Deployment\nusing Service Management APIs and PowerShell cmdlets\n* Akamai - design and implement traffic management, caching using Akamai\n* Azure traffic manager, CDNs, Azure Data Factory, Auto Scaling\n* Deployment automation and synthetic monitoring using application insights\n* Developing end to end APIs and applications using ASP.NET Web Forms, ASP.NET MVC, Web API\n* Performance Testing: Performance, scalability testing using Visual Studio\n* Automated Testing: Automated Unit Testing, Web Testing, Coded Unit Tests, Performance Testing\netc. Tools - SpecFlow, Jmeter, Junit, NUNit, MoQ, Fluent Assertions, VSTS, GIT\n* Big Data: Architected and developed a big solution using Windows Azure HD Insight, Hive\nqueries\n\nhttp://girishthegeek.wordpress.com/\nhttps://www.linkedin.com/in/girishazure\n\n\nProjects Undertaken\nEngagement and monetization (Microsoft - Universal Store/Windows Developer Center)\nJune 2016- Recently\n\n* Technologies used:\nMicrosoft Azure, C#, SQL, PowerShell, App Insights, Azure Data Factory, Redis Cache, API\nManagement Gateway, Azure App Services, Akamai, Azure CDNs, Azure Traffic Manager,\nAutomated Unit Testing, Web Testing, Coded Unit Tests, Performance Testing etc. Tools - SpecFlow,\nJMeter, Junit, NUNit, MoQ, Fluent Assertions, VSTS, GIT, Microsoft Test Manager, Continuous\nIntegration and deployment, maintaining build definitions\n\n* Roles Played:\n\n\u25e6 Technical architect\n\u25e6 Designing and architecting for compute, storage, application, security and deployment\narchitecture\n\u25e6 Designing and architecting for Business Continuity and Disaster Recovery (BCP DR)\n\u25e6 Designing and architecting for the Testing In Production (TIP)\n\u25e6 Designing and architecting for auto healing and auto scaling pattern\n\u25e6 Designing and developing for monitoring\n* Design and develop inside out and outside in monitoring and logging\n* Design and develop synthetic web tests\n* Design and develop watchdogs\n\u25e6 Design and develop the global traffic management using Akamai\n\u25e6 Keeping lights on for the services in all data centers\n\u25e6 Firefighting drills and actual firefighting\n\u25e6 Live site issue tracking, management, coordination and resolving\n\u25e6 Capacity monitoring and management\n\u25e6 Code reviews\n\u25e6 Implemented the security architecture\n\nChannel inclusion Services - (CIS) Client: Microsoft Corporation / Group: Devices and Studios\n(formerly known as IEB)\nSep 2012- June 2015\n\nSynopsis:\n\nCIS is bunch of REST, SOAP services exposed to around 250+ partners dealing with end-to-end\nretail workflow for around 2 million transactions/day. Microsoft uses these services to distribute\nthe digital goods (Windows, Office and so on.) . It also takes care of the brick and mortar scenario\nwhere in customers walk into the shop to buy Microsoft products\n&lt;&gt;\n* Technologies used:\n\u25e6 Windows Azure - Web Roles, Worker Roles, ASP.NET Web API, ASP.NET MVC and Web Forms,\nWCF, Service Bus, Azure Storage\n\u25e6 SQL Azure, HD Insight, Azure Scheduler\n\u25e6 Entity Framework\n\u25e6 MOQ, SpecFlow, NUNit, JMeter, RhinoMock\n* Roles Played:\n\n\n\n\u25e6 Automated unit, performance, integration testing, synthetic testing, data testing, monitoring\nand improving code coverage, building test case and bugs /defects reports\n\u25e6 Continuous Integration and deployment, maintaining build definitions\n\u25e6 Designing and architecting service/api specifications and contracts\n\u25e6 Designing and architecting for high scale\n\u25e6 Designing and architecting for compute, storage, application, security and deployment\narchitecture\n\u25e6 Designing and architecting for Business Continuity and Disaster Recovery (BCP DR)\n\u25e6 Implementing (developing, testing and monitoring) highly scalable, distributed services\n\u25e6 Senior developer consultant responsible to work independently with the clients in designing\ncontracts, defining user stories, implementing them\n\u25e6 Write, execute the BVTs, Unit tests and functional tests, monitoring and improving code\ncoverage\n\u25e6 Building test plans, test strategy\n\u25e6 Code reviews\nInvolved in Application, Security, Operations and Data architecture\n\nMicrosoft Store - Commerce Broker Services (CBS) Client: Microsoft Corporation / Group: Devices\nand Studios (formerly known as IEB)\nNov 2011 - Till Sep 2012\n\nSynopsis:\n\nCBS is Service Oriented middle tier component which is responsible for communicating with\nMicrosoft Store front end web site and down-stream systems (SAP, Fulfillment, Payment Gateway\netc.)\n&lt;&gt;\n* Technologies used:\n\u25e6 Windows Azure Queues, Service Bus Topics and subscriptions\n\u25e6 WCF\n\u25e6 WCF Workflow Services\n\u25e6 Entity Framework\n\u25e6 MOQ, SpecFlow, NUNit, JMeter\n* Roles Played:\n\n\u25e6 Building test plans, test cases, bugs reports, test strategy\n\u25e6 Continuous Integration and deployment, maintaining build definitions\n\u25e6 Automated unit, performance, integration testing, synthetic testing, data testing\n\u25e6 Senior consultant responsible to work independently with the clients in designing contracts,\ndefining user stories, implementing them\n\u25e6 Write, execute the BVTs, Unit tests and functional tests\n\u25e6 Code reviews\n\u25e6 Involved in Application, Security, Operations and Data architecture\n\nBCWeb Wrap Migration to Windows Azure Client: Microsoft Corporation / Group: VLIT\nJan 2011 - Oct 2011\n\nCase Study Featured on TechNet\n\n\n\nSynopsis:\n\nBCWeb is a Windows Azure ASP.NET Web Role application; authenticated against STS using\nFederation services that provide a set of Web forms that help capture price exception details,\ninformation on business case for doing discounts on price lists, and helps calculate percentage\nof discount. It uses the ASP.NET connector and custom Web services to pull real-time price\ninformation from SAP, and provides sales executives with a light front-end to SAP. BCWeb helps\nsimulate pricing discounts, manage business rules, customize routing and approval workflows,\nand update the LOB data stored in SAP. One of the major goals of this system is to improve\nfield productivity by simplifying the process and removing training requirements for complex\nback-end systems, in this case SAP. BCWeb integrates different promotion types into a simplified\nWeb-application. It also allows users to specify business rules using Excel 2007 files and to get\nnotifications in Outlook 2007. Additional goals of this system include:\n\u2022 Reduce revenue leakage by having a business case documented for each promotion field.\n\u2022 Obtain visibility decisions and understand who required a discount and why.\n\u2022 Understand promotion effectiveness and ensure the field is making informed decisions.\n\u2022 Provide a configurable set of files to easily change empowerment guidelines by having a single\nmaster data of workflow routing and approvals.\n\u2022 Specific business process in price-execution domain, however, pattern addresses larger\nsolutions space.\n* Roles Played:\n\n\u25e6 Responsible for technical architecture\n\u25e6 Involved in Application, Security, Operations and Data architecture\n\u25e6 Work Estimation and migration assessment\n\u25e6 Key Contributor as Windows Azure Developer, Analyst\n\nOrigin Digital Cloud Coder Client: Origin Digital\nJan 2010 - May 2010\n\nFeatured on MSDN\n\nSynopsis:\n\nOrigin Digital, a video application service provider that aggregates, transcodes, manages and\ndistributes the digital media files. As part of the business need, they wanted to increase\ntranscoding throughput without increasing capital expense.\n\nAs part of the solutioning, the onshore technical architect along with IDC development team put\ntogether an approach that allowed\n- Process intensive digital video transcoding service to-run-on Azure Platform leveraging cloud's\nreal-time scalability and compute capacity. Infrastructure as a Service (IaaS) offering.\n- Secure all business sensitive information by using On-premise data storage through Azure\n- An administration module to support Platform as a Service (PaaS) was developed.\n\nThe team completed the whole migration activity in 6 weeks using Agile methodology and\nmanaged to showcase it successfully in Microsoft PDC 2009 at Los Angeles. The solution resulted\nin reducing the compute costs by half.\n\n\n\n* Technologies used: ASP.Net 3.5, SQL Azure, Windows Azure, Azure Blob/Table Storage, WCF etc.\n\n* Roles Played:\n\n\u25e6 Over all Application and Data architecture\n\u25e6 Designing Auto Scaling Engine (Rules Based)\n\u25e6 Leading a team to do daily integration test on cloud\n\u25e6 Designing classes/database/user interface.\n\u25e6 Coordinating deployment procedure on cloud.\n\nClaims FNOL Client: StateFarm\n\nMay 2009 - Aug 2009\n\nSynopsis:\n\nFNOL (First Notice of Loss) is the front end for Insurance Company employees to log the insurance\nclaims. It implements AICS framework which is exposed for other companies to use on Azure as\nSaaS/multi-tenant (Software-as-service) service.\n\n* Technologies used: ASP.Net 3.5, SQL Azure, Windows Azure, Azure Blob/Table Storage, WCF etc.\n\n* Roles Played:\n\n\u25e6 Overall Application Architect\n\u25e6 Security and database architect\n\nPostal Return Solutions Client: USPS\nOct 2009 - Nov 2009\n\nVision\n- Electronic Recycling Return Solution to companies which would like to offer a recycling capability\non their web sites (like- HP, Dell etc.)\n- Merchandise Return Solutions for large e-Commerce customers (like- Landsend, Amazon etc)\n- Parcel Return Solutions to its business partners (like-UPS, FedEx etc.), who would like to leverage\nUSPS provided return solutions.\n\nApproach\n- Using Silverlight, create rich web-based user interface for calling Electronic Merchandise Return\nService Web Tools.\n- UI should facilitate printing and emailing EMR Labels.\n\nScope\n- Create shipping return labels using USPS's 'Electronic Merchandise Return Solution' for key\npartners (like Dell, WM) that participate in product recycling programs. It involves utilizing USPS\npublic APIs; expand functionality of existing application to create this shipping capability on\nWindows Azure Platform.\n\n\n\n* Technologies used: Silverlight, SQL Azure, Windows Azure, Azure Blob/Table Storage, WCF etc.\n\n* Roles Played:\n\n\u25e6 Delivery Lead\n\u25e6 Overall Application Architect\n\nWhat's new @ (SaaS, Multi-Tenant mobile application) Client: Starbucks, Coca Cola\n\nVision\n- Content Admin (Publisher) prepares and distributes the content to the employees of the\norganization.\n- Employees by using the mobile based application forward the content on social networking sites\nlike Twitter, Facebook etc.\n- Once the users within the network of the employees view/share the content forward, using\nanalytics capture the view/share count.\n- Branding done using dynamically loading CSSs.\n\nApproach\n- Using ASP.Net 3.5, create rich web-based user interface for mobile application.\n- Using ASP.Net 3.5, create rich web-based user interface for publishers.\n- Use Google, Facebook, BitLy and Twitter analytics to capture the view/share counts.\n\n* Technologies used: ASP.Net 3.5, SQL Azure, Windows Azure, Azure Blob/Table Storage, WCF,\nGoogle Analytics, Facebook APIs, Twitter APIS etc.\n\n* Roles Played:\n\n\u25e6 Delivery Lead\n\u25e6 Offshore Technical Architect\n\nGCP Tracker Client: Accenture Internal\nJune2008-Dec 2008\n\nSynopsis:\n\nGCP (Global career program) is a workflow based tool intended to track employees going on\nonsite assignment for more than 6 months. The GCP process starts when DU Lead nominates an\nemployee for onsite assignment and ends when employee comes back to home location. It caters\nto all GCP processes involved at home country as well as processes involved at Host country.\n\n* Technologies used: ASP.Net 2.0, SQL 2005, Windows Workflow Foundation\n* Roles Played:\n\u25e6 Offshore Technical Architect, Dev Lead\n\u25e6 Designing solution with the help of Senior Architects.\n\u25e6 Designing integration interface\n\u25e6 Was responsible for drawing/ implementing complex workflow using WF.\n\u25e6 Leading a team responsible for implementing Change Requests.\n\u25e6 Coordinating deployment procedures.\n\n\n\nAbacus DSM Client: Accenture Internal\nJune2006-June 2008\n\nSynopsis:\nAbacus Demand Supply Management was developed for Accenture HR/Scheduling/Sales teams\nby CIO (Chief Information Office)\n\n* Technologies used: ASP.Net, SQL 2005\n\n* Roles Played:\n\u25e6 Technical Architect, Dev Lead\n\u25e6 Leading a team responsible for implementing Change Requests.\n\u25e6 Talking/calling/interviewing clients to gather requirements. Documenting it. Getting sign-off on\nit.\n\u25e6 Coordinating Internal, User Acceptance, Integration and Performance Testing.\n\u25e6 Leading Production Support team.\n\u25e6 Build Manager.\n\nCFE Client: Bombay Stock Exchange\nOctober 2005 - June 2006\n\nSynopsis:\n\nCFE system (Common Front End System) is a trading platform for member (and traders of\nmembers) of BSE to trade in Multi exchange, Multi segments for trader authentication developed\nby CMC Ltd. It is designed to facilitate Members with centralized access rights management\n(has inbuilt Risk Management Server) and enable member to have a centralized Client-level Risk\nManagement at the Broker Office, wherein the risk for a client will be checked across all traders\nassociated with the broker for trading carried out in all segments and on any of the exchanges,\ncentrally at one location.\n\n* Technologies used: Windows Forms (using C#), MS SQL\n* Roles Played: Dev Lead, Technical Architect, Requirements gathering and analysis, Design,\nCMM-i documentation, writing BRS, FSD, and Program Specs etc.\n\nBond Clinician Client: Bond Technologies\nNovember 2004 - October 2005\n\nSynopsis:\n\nClinician is a complete healthcare solution for any clinic and hospital. It has got interface both\nfor Patients and doctors/physician. It has the capability of generating all sorts of lab reports etc\nand has a full-fledged Document Management System wherein clinics can maintain and scan all\ndocuments and reports in the scanned format. It also uses the third-party interface for Health\nInsurance.\n\n* Technologies used: ASP.Net web forms (using VB.Net), MS SQL, MSMQ\n* Roles Played: Developer, Database Design, Implementation and Testing", "labels": []}
{"id": 57, "text": "Asha Subbaiah\n(Microsoft Partner Readiness Operations Project Manager (APAC) -\nMicrosoft GPS\n\nBangalore, Karnataka - Email me on Indeed: indeed.com/r/Asha-Subbaiah/f7489ca1bec4570b\n\nWORK EXPERIENCE\n\n(Microsoft Partner Readiness Operations Project Manager (APAC)\n\nMicrosoft GPS -\n\nAugust 2014 to Present\n\nWorked closely with the APAC PB&amp;D teams / responsible for coordinating and tracking\nimpact, effectiveness and progress of our readiness activities with focus on driving efficiencies in\nvarious programs in Channel Readiness and Sure step and partner closely with Channel Readiness\nManager and (PCMM - SMB) from APAC SMS&amp;P.\n\nResponsibilities\n1. Tracking progress of Readiness Programs and clearly communicate the impact.\n2. Consolidate Outcomes and articulate the Partner attendance and Feedback.\n3. Track Partner Recruitment through the Sure step program; and work closely with the Tele-\nengine\n4. to ensure its alignment to Sure Step framework\n5. Interface between GPS (India, China, Korea) and APAC PB&amp;D teams for events delivered\nby GPS\n\nMicrosoft - India\nCompete Recruiter in the recently concluded pilot to recruit Google Partners across APAC.\n\nSenior operations coordinator\n\nIBM RESEARCH -  Bangalore, Karnataka -\n\nJune 2008 to 2013\n\nResponsibilities: -\n\u2022 Support for the India Technical Leaders Group (ITLG), the highest technical group in IBM India,\nincluding preparing agenda, collecting presentation materials and maintaining ITLG team room\netc.\n\u2022 Event management. I have supported the organization of International Conferences and\nWorkshops, including logistics, registration, awards, and general coordination.\n\u2022 Travel managements. The director travels on a monthly basis to all parts of world, in all six\ncontinents. I'm intimately familiar with managing complex travel itineraries to any part of the\nworld.\n\n\u2022 Setting up high-level conferences, workshops and management meetings, and special events.\nHandling visitors from around the world, and other high-level meetings.\n\nhttps://www.indeed.com/r/Asha-Subbaiah/f7489ca1bec4570b?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Calendar management. The director's schedule includes constant and daily interactions with\nthe top-most leaders of IBM, IBM's customers, Industry organizations, Academia, Government,\nand Media from around the world.\n\n\u2022 Material preparation, distribution, and archiving. I collect reading materials that are essential for\nany meeting with external or IBM internal meetings. I also maintain many presentation materials,\nas well as archive and distribute documents.\n\nAchievements\n\u2022 Joined the IRL-Bangalore team, and I was responsible for the overall operations support team\nat IRL-Bangalore.\n\u2022 Supervised the other support team members.\n\u2022 Responsible for coordinating the creation of the new premises for the IRL-Bangalore team. The\nnew premises in EGL is a 5000-square foot facility housing the research team, with conference\nrooms and a library / lab.\n\u2022 Responsible for space management including seat allocation and room allocation.\n\u2022 Asset management\n\u2022 Event Management including IRL club and visitors.\n\u2022 Responsible focal point for all interactions on location with GVI, AV, WBS, RESO, Procurement,\nSecurity, IS, ITS, STP/Customs/Logistics, BCP team, line management Business controls/ERO/BCP\nsupport at Bangalore\n\nEDUCATION\n\nBachelor's in Commerce\n\nNMKRV College, Bangalore University -  Bangalore, Karnataka", "labels": []}
{"id": 58, "text": "Divesh Singh\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Divesh-Singh/a76ddf6e110a74b8\n\nSeeking new challenges, looking to join a progressive organization that has need and offer\nopportunities for advancement. Seeking a position where I can serve with my utmost capabilities\nand where I can give full play to my creativity to excel within and to grow with institute.\n\nWORK EXPERIENCE\n\nFront Office Executive\n\nMicrosoft iGTSC -  Bangalore Urban, Karnataka -\n\nJune 2016 to July 2017\n\nHaving 11 months experience with Microsoft as a FOE. I have worked for Microsoft as a FOE for\n11 months from May 2016 to July 2017.\n\nAchievements:\n\n\u2756 Winner of inter school handwriting competition.\n\u2756 Runner up in inter college solo singing Competition.\n\u2756 Won Cricketer Of the year 2011\n\u2756 Selected for Voice Of Bangalore\n\nEDUCATION\n\nB.COM\n\nJAIN COLLEGE\n\n12TH\n\nINDIAN ACADEMY PU COLLEGE\n\nARMY PUBLIC SCHOOL\n\nADDITIONAL INFORMATION\n\nProfessional Skills:\n\n\u2756 Ability to work with team.\n\u2756 Good communication skills.\n\u2756 Good inter-personal skills.\n\u2756 Positive Attitude.\n\u2756 Hard &amp; Smart Working.\n\u2756 Building good relationship with people.\n\u2756 Ability to work under tough situation.\n\u2756 Self-Motivator.\n\nhttps://www.indeed.com/r/Divesh-Singh/a76ddf6e110a74b8?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2756 Quick learner", "labels": []}
{"id": 59, "text": "Ramesh chokkala\nTelangana - Email me on Indeed: indeed.com/r/Ramesh-chokkala/16d5fa56f8c19eb6\n\nWORK EXPERIENCE\n\nsoftware\n\nMicrosoft,Infosis, Google -\n\nMay 2018 to Present\n\nsoftware\n\nMicrosoft,Infosis, Google -\n\nMay 2018 to Present\n\nEDUCATION\n\nbtech\n\nTrinity engineering college\n\nhttps://www.indeed.com/r/Ramesh-chokkala/16d5fa56f8c19eb6?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 60, "text": "Ganesh AlalaSundaram\nA Dev-Test Professional with 8+ Yrs of exp looking for SDET Lead/SDET/\nScrum Master/Program Manager roles.\n\nChennai, Tamil Nadu, Tamil Nadu - Email me on Indeed: indeed.com/r/Ganesh-AlalaSundaram/\ndd5b500021e61f65\n\nMy long-term career objective is to lead programs that solves complex problems, responsible\nfor product delivery and building products that positively impacts millions of consumers and\nenterprise users.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nConsultant SDET\n\nMicrosoft -\n\nJune 2013 to Present\n\nJune 2013 - Present)\nProduct Technologies: Cloud &amp; Mobile Migrations, DevOps for Test Strategies, Visual Studio,\nAzure.\n\u2022 Orchestrated projects as an Individual Contributor and Led teams across global engagements.\n\u2022 Contributed to 60% of the software component automation in the projects. Streamlined process\nand tools for Dev-Ops implementation that included training, metrics and reporting.\n\u2022 Led Manual and Automated Test Management to elevate the quality of Mobile (x Platform),\nCloud and IoT Applications. Designed, Developed and Maintained automation coverage for UI\nand API layer by handshaking Visual Studio with Open Source frameworks.\n\u2022 Migrated millions of customers to Azure Cloud through FastTrack program. Developed Tools that\nreduced manual efforts worth 40 hours for each process.\n\nProgrammer Analyst\n\nCognizant -\n\nMarch 2010 to May 2013\n\nBuilt WCF services for iOS and Android Applications for a PoC which helped business to grab more\nmobile projects.\n\u2022 Delivered quality of products to customers with the stipulated time. Stand-ups, defect triage,\nbrown bag sessions, bug-bash and retrospectives. Owned and managed weekly quality report.\n\u2022 Initiated transformation of web to mobile apps within project and assisted senior stakeholders\nof the company in analyzing the opportunity.\n\nProjects\nIoT and Smart City Projects\n\u2022 Formulated the use of VSO Dashboards for ease of status reporting which reduced manual\neffort of an hour per day.\n\nhttps://www.indeed.com/r/Ganesh-AlalaSundaram/dd5b500021e61f65?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Ganesh-AlalaSundaram/dd5b500021e61f65?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Leveraged developer background to automate UI and API components through C#, Xamarin\nUI in iOS and Android apps.\n\u2022 Led process improvements that standardized operations that included on-boarding/reporting\nand customer acceptance.\n\nPubSec Projects - Dubai/Columbia/New York\n\u2022 Coordinated between in-house and client teams and kept stakeholders informed of progress\nand milestones.\n\u2022 Supervised an avg. of 10-member software QA team in developing and implementing quality-\nassurance and quality-control methodologies to ensure compliance with QA standards.\n\u2022 Created tools that helped the development ecosystem by automating the long running manual\nprocess.\n\u2022 Brought a strong focus on \"voice of the customer\" into the software development function to\nensure product and customer success\nPortfolio\nMSDN Blog - https://blogs.msdn.microsoft.com/ganesh/\nGitHub Repo- https://github.com/ganesh-alalasundaram/\nPersonal Website: http://www.ganeshalalasundaram.com\n\nEDUCATION\n\nSoftware Product Management\n\nProduct School -  New York, NY\n\nBachelor of Computer Science in Computer Science Engineering\n\nAnna University\n\nSKILLS\n\nAutomation, Testing, Mobile Testing, SDET, Scrum Master\n\nCERTIFICATIONS/LICENSES\n\nCertified ScrumMaster (CSM)\n\nPUBLICATIONS\n\nMSDN\n\nhttps://blogs.msdn.microsoft.com/ganesh\n\nGIT\n\nhttps://github.com/ganesh-alalasundaram\n\nhttps://blogs.msdn.microsoft.com/ganesh\nhttps://github.com/ganesh-alalasundaram", "labels": []}
{"id": 61, "text": "Srinu Naik Ramavath\nanymore job\n\nSerilingampalle, Andhra Pradesh - Email me on Indeed: indeed.com/r/Srinu-Naik-\nRamavath/2d9f28ccfa115f79\n\nWilling to relocate to: Gachibowli, Telangana - hyderbad, Telangana\n\nWORK EXPERIENCE\n\nsecurity officer\n\nMicrosoft in G4S\n\nCommunication and secure to the employees and no any other person's not allowed to the\ncompany\n\nSecurity Officer\n\nMicrosoft of G4S -  Hyderabad, Telangana -\n\nApril 2010 to May 2018\n\nsalaries is limited\n\nEDUCATION\n\nBsc(mpc) in Bachelor of science\n\nAcharya Nagarjuna University -  Hyderabad, Telangana\n\nMarch 2006 to March 2009\n\nSKILLS\n\nTelugu Hindi movie and English, cricket valiball\n\nADDITIONAL INFORMATION\n\ncomputer and firesafety\n\nhttps://www.indeed.com/r/Srinu-Naik-Ramavath/2d9f28ccfa115f79?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Srinu-Naik-Ramavath/2d9f28ccfa115f79?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 62, "text": "Puneet Bhandari\nSAP SD lead - Microsoft IT\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Puneet-Bhandari/c9002fa44d6760bd\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSAP SD lead\n\nMicrosoft IT -\n\nAugust 2010 to Present\n\nTeam Size: 8 Duration: Seven months\n\nScope:\n* Enhancement of Mexico invoicing process as per the current regulations\n* Requirement gathering from third party and client on new process\n* Responsible for implementing the changes in system\n\nArea of Exposure:\n* Understand the AS-IS process and develop to- Be design document to meet the business and\nGovernment requirement\n* Requirement gathering for all SD process for client\n* Developed solution blueprint and Process Design Documents for OTC 3-way and 1-way invoice\nprocesses\n* Interacting with third party to gather requirements from their end\n* Creating functional specification and Gap analysis document for different country\nimplementation with client\n* Design test scripts for functional unit testing (FUT), Integration system testing (IST) and User\nAcceptance Test (UAT)\n\nPhase: Support Phase: Implementation \\ Enhancement\nProject: JCI\nRole: SAP SD lead\nTeam Size: 15 Duration: Twelve months\n\nScope:\n* Communication with client leadership on various issues and efficiency improvement\n* Ticket handling for OTC track as L2 support lead\n* Responsible for change request across OTC and all business areas\n\nArea of Exposure:\n* End to end order to cash cycles issues\n* Interface related issues with exposure to IDOCs\n* Change request handling and process improvement\n\nhttps://www.indeed.com/r/Puneet-Bhandari/c9002fa44d6760bd?isid=rex-download&ikw=download-top&co=IN\n\n\n* SPOC for client from offshore for communication, reporting and continuous improvement\nactivities\n* Design test scripts for functional unit testing (FUT), Integration system testing (IST) and User\nAcceptance Test (UAT) for all changes for process and system improvements\nAchievements:\n* Spot Awards for exceptional contribution to project work\n* Award and appreciation from client at Global level for successful transition of new geographies\nto support global template\n\nPhase: Implementation\nProject: Adient\nRole: SAP SD lead\nTeam Size: 5 Duration: Five months\n\nScope:\n* Lead Optical archiving of all future, live and archived invoices for audit purpose\n* Harmonize invoice archiving process across all plants and SAP instances\n* Requirement and data gathering from all plants on legal aspects of invoice form structures and\ndata of last 10 years\n\nArea of Exposure:\n* Developed solution blueprint and Process Design Documents for OTC 3-way and 1-way invoice\nprocesses\n* Requirement gathering from all the plants legal department on the aspect of form structure\n* Data gathering of all changes across globe for data (customer, vendor, organizational) relative\nto the billing process in system\n* Creating functional specification and Gap analysis document for different country\nimplementation with client\n* Design test scripts for unit testing (UT), Integration system testing (IST) and User Acceptance\nTest (UAT)\n\nPhase: Implementation\nProject: JCI\nRole: Cutover Manager\nTeam Size: 15 Duration: Nine months\n\nScope:\n* Legal entity Separation of asset share and shared sale plants from the core JCI group\n* Co-coordinating with multiple vendors on behalf of client for master data migration and IT\nactivities\nArea of Exposure:\n* Defining scope of activities for the shared sale plants migration to new legal entity\n* Co-ordination with OTC, PTP, PTD and RTR streams for successful implementation of the\nobjectives\n* Analyzing issues related to intercompany transactions occurred for asset share plants\n* Gathering client requirement on basis of legacy system and current need\n* Creating functional specification and Gap analysis documents\n* Reporting to IT head of the organization on the progress of the planned activities\n* Report analysis and finding functional solutions for the issues\n\n\n\n* Hyper care support for multi SAP instance layout\n\nAchievements:\n* Appreciations from the client on smooth and successful execution of the cutover involving\nmultiple stakeholders\n\nPhase: Implementation and Roll-out\nProject: Atlas CopCo\nRole: Master Data Lead and SD team member\nTeam Size: 11 Duration: Thirty-three months\n\nScope:\n* Sales order management\n* Equipment creation in Order to cash cycle\n* Lead for master data migration\n* Conducting workshops along with client IT team for business user\n\nArea of Exposure:\n* Conducting blue print workshops with client for requirement gathering in 6 countries\n* Developed solution blueprint and Process Design Documents for OTC\n* Roll out of the template solution to UK-NORDICS\n* Defining data flow for the sales order-billing document in system\n* Order to cash cycle activity management for data creation in ECC\n* Creating functional specification and Gap analysis document for different country\nimplementation with client\n* Implementation and Configuration of different processes as per the client requirement in the\narea of SAP SD and CRM sales-ECC integration\n* Reporting - Reconciliation, pre validation, post validation\n* Involved as lead in cutover, go-live, hyper care phases of project for five countries in Europe\n\nAchievements:\n* Awarded for Creating landscape for support phase along with top management team of the\nproject\n* At onsite as equipment lead and awarded as valuable member by the client\n\nPhase: Implementation\nProject: Agri Business Client\nRole: SD Team member\nTeam Size: 25 Duration: Ten months\n\nScope:\n* Implementing end to end SAP SD scenario for MNC client\n* Creation of functional specification documents for various processes\nArea of Exposure:\n* Defining enterprise structure, shipping conditions, pricing procedure, etc.\n* Assignment of structure as per business need\n* Creation of BPP and functional specification documents\n* Master data Creation\n* Worked on DUET (SharePoint and SAP initiative)\n\n\n\n* Handling team and driving it to achieve deliverables\n\nAchievements:\n* Published various BOK (reusable artifacts) in Infosys Repository\n* Successfully completed on job internship on Sustainability study of Infosys Clients - Telstra,\nCummins and Vodafone.\n\nPhase: Implementation\nProject: COE\nRole: SD Team member\nTeam Size: 6 Duration: Three months\n\nScope:\n* Configuring sales order management for SAP SD module for client\n* Testing preconfigured solution for internal team\n\nArea of Exposure:\n* Implementation of sales order process with respect to specific SD (sales and Distribution)\nprocesses\n* Testing for SD module processes involving various pricing methodologies\n* Handling team and driving it to achieve deliverables\nAchievements:\n* Cleared domain certification in Retail category\n* Successfully completed Harvard Manage Mentor Certification in Customer Focus and Innovation\nImplementation by Harvard Business Publishing\n\nPhase: Support Client: Microsoft IT\nTeam Size: 15 Duration: Sixteen months\n\nScope:\n* Supporting complete Order to cash cycle for Microsoft Business\n* Analyzing, resolving and implementing issues or tickets\n\nArea of Exposure:\n* Pricing unification management and analysis (stand-alone system for pricing)\n* Handling of Master Data (Customer data, customer information data, condition records, partner\nprofile maintenance)\n* Resolving errors related to configuration issues, extension of sales organization and material,\norder, delivery and invoice errors.\n* Working on LSMW for mass data update in the system\n* Working on monthly rotational activities like IDOCs failure, EDI error notification, short dumps.\n\nAchievements:\n* Resolved maximum numbers of issues among the peer group members\n* Created maximum number of knowledge base articles for the project\n\nTitle: Training Duration: Two months\nScope: Understanding of ES Methodologies, Project management and SAP SD\n\n\n\nArea of Exposure:\n* Understanding fundamentals of Management with respect to IT sector\n* Learning SAP SD (Sales and distribution domain)\n* RFP creation\n\nAchievements:\n* Successfully cleared P100 and P200 certifications\n* Successfully completed RFP and POST\n* Successfully cleared all exams with score more than 4 out of 5\n\nEDUCATION\n\nMBA in Marketing\n\nIIT Roorkee -  Roorkee, Uttarakhand\n\n2008 to 2010\n\nBachelor of Engineering in Electronics and Communication\n\nShri Vaishnav Institute of Technology and Science, RGPV University -  Indore, Madhya Pradesh\n\n2004 to 2008\n\nCBSE\n\nSt. Paul H.S. School -  Indore, Madhya Pradesh\n\n2002 to 2003\n\nHigher Secondary Certificate\n\nShanti Nagar High School, Maharashtra board -  Mumbai, Maharashtra\n\n1999 to 2000\n\nSKILLS\n\nSap Sd (7 years)", "labels": []}
{"id": 63, "text": "Aarti Pimplay\nOperations Center Shift Manager (OCSM)\n\n- Email me on Indeed: indeed.com/r/Aarti-Pimplay/778c7a91033a71ca\n\nTo work with an organization where I can contribute to the growth of the\norganization through my skill &amp; knowledge for mutual benefit and to\nlearn and excel in highly competitive environment\n\nWORK EXPERIENCE\n\nOperations Center Shift Manager (OCSM)\n\nMicrosoft India -\n\nAugust 2012 to January 2016\n\n\u2022 Handling escalations, notifications, task organization, distribution of work, site status enquiries\n\u2022 Monitoring the Incidents handled by the team in real time\n\u2022 Supervising the reporting of Incidents to respective stake holders\n\u2022 Ensuring proper workflow of Incident and major incident processes\nare followed\n\u2022 Escalate events that have a potential MS impacts to Security Analyst or as directed by the\nEscalation Matrix\n\u2022 Initiate problem tickets based on the recurring incidents identified\n\u2022 Reviewing the problem records to ensure timely closure of issues\n\u2022 Responsible for publishing monthly SLA reports\n\u2022 Providing OJT, concurrent training\n\u2022 Global news monitoring (Monitor Global activities on a continual\nbasis)\n\u2022 Responsible for administrative duties like reviewing performance\nMetrics, managing breaks/lunch (All stations), Shift Changeover\nProcess and adherence, Policy Reviews and Updates, Supply and equipment requests, OCSM\nPass-down Log, Inventory Control,\nEmployee Recognition Requests, Disciplinary Actions, Annual\nEvaluations, Mentoring and Counselling\n\u2022 Maintain and share updates on emergency procedures\n\u2022 Develop and/or update all policies and procedures\n\nCommunication Supervisor\n\nMicrosoft India -\n\nFebruary 2011 to July 2012\n\n\u2022 Managing all incidents based on the priorities\n\u2022 Publishing executive business notifications during outages\n\u2022 Responsible for all email communications in GSOC Asia\n\u2022 Global news monitoring\n\u2022 Handling and initiating Major Incident conference calls and assisting the respective teams\n\nhttps://www.indeed.com/r/Aarti-Pimplay/778c7a91033a71ca?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Initiating bridge calls for P1 &amp; P2 Issues\n\u2022 Providing overall analysis of incidents by performing root cause\nanalysis and quality checks\n\u2022 Provide supervision to assigned staff\n\u2022 Maintain an in-depth knowledge of emergency procedures, and adhere to same\n\nService Desk Analyst\n\nSITEL -\n\nSeptember 2009 to January 2011\n\n\u2022 Provided technical support to end users\n\u2022 Worked as part of escalation team to identify resolution and provide\ninputs to improve/create KB articles\n\u2022 Responsible for providing First Call Resolution\n\u2022 Providing Technical assistance to customers based on the priorities\n\u2022 Resolving Issues related to networking\n\u2022 Assist in configuring LAN, Modular Routers and TCP/IP\n\u2022 Troubleshooting Hardware and System performance issues\n\u2022 Working with Users to identify and rectify the issues pertaining to\nInternet and related services\n\u2022 Worked with different Antivirus Softwares - Installation and troubleshooting\n\u2022 Team SPOC for Quality and Compliance improvements\n\nADDITIONAL INFORMATION\n\nSKILLS \u2022 Ability to build teams and motivate them towards team goals\n\u2022 Effective Communication skills\n\u2022 Able to handle and overcome objections\n\u2022 Ability to work effectively in a team environment\n\u2022 Ability to adapt to the changes in organization along with successful\nimplementation of the change in the system", "labels": []}
{"id": 64, "text": "Bangalore Tavarekere\nVolunteer Contestant, Yappon\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Bangalore-\nTavarekere/8fc92a48cbe9a47c\n\nWORK EXPERIENCE\n\nVolunteer Contestant, Yappon\n\nMicrosoft -\n\n2017 to 2017\n\nAnalyst Programmer\n\nTavarekere -  Bengaluru, Karnataka -\n\n2015 to 2015\n\n560029\n\nEDUCATION\n\nB.Tech\n\nChrist University Faculty of Engineering -  Bengaluru, Karnataka\n\n2018\n\nClass XII\n\nDelhi Public School -  Kanpur, Uttar Pradesh\n\n2013\n\nCBSE\n\nDelhi Public School -  Kanpur, Uttar Pradesh\n\n2011\n\nhttps://www.indeed.com/r/Bangalore-Tavarekere/8fc92a48cbe9a47c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Bangalore-Tavarekere/8fc92a48cbe9a47c?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 65, "text": "Avani Priya\n- Email me on Indeed: indeed.com/r/Avani-Priya/fe6b4c5516207abe\n\nWORK EXPERIENCE\n\nJavaTally Microsoft -\n\n2013 to 2015\n\noffice\n\nADDITIONAL INFORMATION\nI am a girl with simple living and high thinking .I need to work to prove myself. I am to be\nindependent I don't want to depend on someone.\n\nEDUCATION\n\nbr day public school -  Begusarai, Bihar\n\nhttps://www.indeed.com/r/Avani-Priya/fe6b4c5516207abe?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 66, "text": "Sanand Pal\nSQL and MSBI Developer with experience in Azure SQL and Data Lake\nstore.\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Sanand-Pal/5c99c42c3400737c\n\nI intend to establish myself as Software Engineer / architect with an integrated business solution\nprovider through a long time commitment, contributing to the company's growth and in turn\nensuring personal growth within the organization. I believe that my technical, functional and\ncommunication skills will enable me in facing the challenging career ahead.\n\nWilling to relocate to: Kolkata, West Bengal - hyderbad, Telangana\n\nWORK EXPERIENCE\n\nAssistant Consultant\n\nTCS\n\n\u2022 Expertise in SQL Server(2008 R2, 2012, 2014) development, Microsoft BI (SSIS)\n\u2022 Experience with Microsoft BI (SSAS, SSRS), ASP.NET, VSTO, C#.\n\u2022 Experience in all the phases of Software Development Life Cycle (SDLC).\n\u2022 Experience in Business Requirements Analysis, meeting customer expectations.\n\u2022 Have had the opportunity to handle and work in multiple projects at a time.\n\u2022 Experience in working both independently and in a team-oriented, collaborative environment.\n\u2022 Excellent written and verbal communication skills, good analytical and problem solving skills.\n\nSQL and SSIS developer/Sustain resource\n\nMICROSOFT -  Hyderabad, Telangana -\n\nAugust 2011 to June 2016\n\nProject\nUS EPG Forecast Workbook (FWB) application is designed to support the US Enterprise Partner\nGroup (US EPG) in producing and maintaining the monthly US sales forecast. It is a transactional\ndatabase combined with Microsoft Office Excel functionality that enables end users to interact\nwith the USNAForecast database. Typically, an ATU manager will connect to the corporate network\nand download data from the forecast database, which creates an offline forecast workbook. This\ndata can then be accessed offline, modelled, and changes to certain data fields are subsequently\nuploaded back to the forecast database through a CorpNet connection. These changes are then\nstored in the online database and subsequently loaded back into upstream systems.\n\nResponsibilities\n\u2022 Involved in the Technical discussions/Sessions and efforts estimations and reviews.\n\u2022 Involved in analysis of the Bugs/ Issues/defects/CRs.\n\u2022 Involved in change requests on VSTO Excel application.\n\u2022 Involved in design of database, tables and stored procedures.\n\u2022 Developed SQL Server Integration Services packages for ETL process.\n\u2022 Unit testing and bug fixing of the code.\n\nhttps://www.indeed.com/r/Sanand-Pal/5c99c42c3400737c?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Actively solved the issues that raised during the integration cycle\n\u2022 Performed build verification and Smoke tested all the defects raised before giving a delivery.\n\u2022 Prepared and updated FS, TS and deployment guides.\n\u2022 Provided knowledge sharing to Users\n\u2022 Provided post implementation support\n\u2022 Promptly check in the final Code in VSTF.\n\nHyderabad, India\n\nEDUCATION\n\nBachelor of Technology in Branch\n\nEast Point College of Engg. & Tech. -  Bengaluru, Karnataka\n\nJune 2006 to July 2010\n\nSKILLS\n\nSql Server, Ssis, T-SQL, ETL, SSRS", "labels": []}
{"id": 67, "text": "Partho Sarathi Mitra\nKolkata, West Bengal - Email me on Indeed: indeed.com/r/Partho-Sarathi-\nMitra/683dfd08d0246836\n\nWORK EXPERIENCE\n\nSenior Sales Executive\n\nNokia & Microsoft mobile -\n\nMarch 2005 to April 2016\n\nEDUCATION\n\nB.com\n\nSurendranath college Barrackpore\n\nAWARDS\n\nBest Promoter\n\nhttps://www.indeed.com/r/Partho-Sarathi-Mitra/683dfd08d0246836?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Partho-Sarathi-Mitra/683dfd08d0246836?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 68, "text": "Pranay Sathu\nSoftware Test Automation Engineer\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Pranay-Sathu/ef2fc90d9ec5dde7\n\n\u2022 Over 3.6 years of experience in IT industry developing automation code for various clients (Web\nand Mobile applications)\n\u2022 Extensive Work Experience in test automation using tools Selenium Webdriver, Xamarin.UITest\nand exposure to all phases of SDLC.\n\u2022 Involved in development of automation framework using C#.\n\u2022 Experience in Test management tools like Quality center, JIRA, VSTS.\n\u2022 Proficiency in C# programming with development of Automation scripts.\n\u2022 Strong Debugging skills using Visual Studio.\n\u2022 Involved in story grooming sessions, backlog prioritization and product road map discussions\n\u2022 Experience in working closely with the product management team (Product owner, BA and UX)\nfor requirement discussion.\n\u2022 Good experience in reviewing requirements and identify ambiguity in requirements.\n\u2022 Preparing test cases for the system covering User stories, reviewing them with the developers\nand finalizing the test cases.\n\u2022 Strong experience in facilitating UAT scenarios/sessions. Provide sprint end product demo to\ncross functional teams\n\u2022 Support Project manager in generating test coverage reports, defect metrics.\n\u2022 Good Work experience in performance testing using HP load runner (True client protocol)\n\u2022 Basic knowledge in authoring user stories in BDD format.\n\u2022 Basic knowledge in automating the applications using tool Protractor\n\u2022 Quick learner, Self-motivated and problem solving skills.\n\nWilling to relocate to: Pune, Maharashtra - hyderbad, Telangana - Bangalore Urban, Karnataka\n\nWORK EXPERIENCE\n\nConsultant\n\nNeudesic (Vendor @ Microsoft)\n\nWorking as Test Automation Engineer.\n\nSoftware Test Automation Engineer\n\nEPAM -\n\nDecember 2014 to October 2017\n\nWorked as Test Automation Engineer.\n\nEDUCATION\n\nBachelor (B Tech) in Name of the Education Establishment\n\nSR ENGINEERING COLLEGE (SREC)\n\nhttps://www.indeed.com/r/Pranay-Sathu/ef2fc90d9ec5dde7?isid=rex-download&ikw=download-top&co=IN\n\n\nSKILLS\n\nC# (Less than 1 year), Database (Less than 1 year), Java (Less than 1 year), JIRA (Less than 1\nyear), load runner (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nKey Skills:\n\n\u2022 Programming Languages: C#, Java\n\u2022 Automation Testing: Selenium Webdriver, Xamarin.UITest, Protractor, Speflow\n\u2022 Performance Testing: HP Load Runner (True Client protocol)\n\u2022 Software Testing: Test Scripts, Test Case Design, Test Summary\n\u2022 Platform: Windows.\n\u2022 Database: SQL Server 2012, Oracle\n\u2022 Management tools: JIRA, HP Quality center; vsts.", "labels": []}
{"id": 69, "text": "Tanmoy Maity\nKolkata, West Bengal - Email me on Indeed: indeed.com/r/Tanmoy-Maity/145eb1ed39df317c\n\nWORK EXPERIENCE\n\nHVAC Technician\n\nInfosys and microsoft -\n\nNovember 2015 to Present\n\nHVAC Technician\n\nInfosys and microsoft -\n\nNovember 2015 to Present\n\nEDUCATION\n\nDiploma in Mrac\n\nGtti -  Kolkata, West Bengal\n\nSKILLS\n\nHvac tech (3 years)\n\nAWARDS\n\nbest team leader of the year\n\nFebruary 2017\n\nhttps://www.indeed.com/r/Tanmoy-Maity/145eb1ed39df317c?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 70, "text": "Aanirudh Razdan\nTechnical Support Executive - Teleperformance\n\nJaipur, Rajasthan - Email me on Indeed: indeed.com/r/Aanirudh-Razdan/efbf36cc74cec0e5\n\nTo seek an organisation where my skills find ample opportunities for up-gradation of my\nknowledge and growth of my career and where I can prove myself.\n\nWORK EXPERIENCE\n\nTechnical Support Executive\n\nTeleperformance -  Jaipur, Rajasthan -\n\nJune 2017 to Present\n\n* Handling query related to Norton products\n* Resolving query related to the Operating system (Windwos XP/Vista/7/8/8.1/10)\n\nB2X Process\n\nNokia and Microsoft -\n\nSeptember 2016 to May 2017\n\nhandsets\n* Resolving problems related to Microsoft account\n* Awarded as best Nesting Executive of the month\n\nEDUCATION\n\nB. Tech\n\nJaipur Engineering College and Research Center -  Jaipur, Rajasthan\n\n2015\n\ncommunication and presentation skills\n\n(WAC) in inter Air Force School football championship\n\n2012\n\nAir Force School Jammu -  Jammu, Jammu and Kashmir\n\nAir Foce School Jammu -  Jammu, Jammu and Kashmir\n\nhttps://www.indeed.com/r/Aanirudh-Razdan/efbf36cc74cec0e5?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 71, "text": "Shiksha Bhatnagar\nchnadigarh - Email me on Indeed: indeed.com/r/Shiksha-Bhatnagar/70e68b28225ca499\n\nWORK EXPERIENCE\n\nonline job in home\n\nMicrosoft and copy past -  Chandigarh, Chandigarh -\n\nAugust 2016 to July 2017\n\ni need a online job so that i can attend \nmy regular college and i want to earn money that's it a part time online job so that i can do it\non my phone or laptop\n\nEDUCATION\n\npass 12 in medical\n\nchandigarh university -  Chandigarh, Chandigarh\n\nSeptember 2016 to August 2019\n\nSKILLS\n\nMicrosoft office and java (Less than 1 year)\n\nADDITIONAL INFORMATION\n\ni want to earn money by my hard work or smart work p\n\nhttps://www.indeed.com/r/Shiksha-Bhatnagar/70e68b28225ca499?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 72, "text": "Chhaya Prabhale\nKharadi, Pune, 411014, IN - Email me on Indeed: indeed.com/r/Chhaya-\nPrabhale/99700a3a95e3ccd7\n\nThis application is Used to information of customer and agency and to\nCheck the reorder level of cylinder.\nTitle: \"Gas Agency System\"\nPlatform: MS Access\n\nThis application is used to store daily information of Cylinder,\nCustomer. Printing Customer information, Employee information,\nSelling information and finding particular customer records etc.\n\nWORK EXPERIENCE\n\nDBA Client\n\nPune, Maharashtra -\n\nJune 2006 to Present\n\nExperiance Summary:\n\n\u27a2 Creation of tools (using Perl), shell scripting and PL-SQL for testing the quality of Data as per\nrequirements. Creating reports / feedbacks.\n\u27a2 Able to create scripts for automation.\n\u27a2 Checking data fields contents by running shell scripts &amp; sql queries\n\u27a2 Database management like, conversion and manipulation of AND's Global Road Data using\nSQL and compiling them to AND's navigational road data.\n\u27a2 Developing PL-SQL models to fetch required data.\n\u27a2 Writing shell script for automation for support and system admin.\n\nProject Name: Product Releasing Data 2018H2\n\nMicrosoft -\n\n2007 to 2007\n\nTesting of data is done by manually and customizing tools developed in Shell/Perl scripting.\nWe modify the Shell scripts as per requirements &amp; to test the Drawing &amp; Image with\nmodified scripts. The product is in Geographic data files (GDF) format this guides to the customer\nwhen the customer needs data up to a certain level.\nMy Role:\n\u2022 Performed Black Box, Functional, Integration and Regression Testing of application.\n\u2022 Involved in creation and execution of test cases.\n\u2022 Reporting of bugs.\nTools:\nPerl Scripts, Shell Scripts, Sql query, PL-SQL and in-house tools on Unix Sun Solaris\n\nhttps://www.indeed.com/r/Chhaya-Prabhale/99700a3a95e3ccd7?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Chhaya-Prabhale/99700a3a95e3ccd7?isid=rex-download&ikw=download-top&co=IN\n\n\nSKILLS\n\nPERL (10+ years), SCRIPTING (10+ years), SQL (10+ years), DATABASE (10+ years), SHELL\nSCRIPTING (10+ years)\n\nADDITIONAL INFORMATION\n\nKey Skills:\n\n\u27a2 Demonstrating responsibility and initiative with an emphasis on Teamwork &amp; Co-\noperation.\n\u27a2 Ability to work under high pressure while maintaining high standards of work.\n\u27a2 A highly consistent and pragmatic approach to problem solving.\n\nOperating Systems: Windows (XP/2000), UNIX Sun Solaris 9. Linux (Red-hat-6)\nScripts: Shell, Basic Perl (UNIX, Linux)\nLanguages: C, C++, VB 6.0\nDatabase: Oracle, MySQL, Access, DB2\nPrimary Skill: Unix shell scripting, Sql, perl (Intermediate Level), Pl-sql\nTest tools: Basic Knowledge of QTP, WIN RUNNER, Test Director\n\nStrengths:\n\n\u2022 Professional Programming Skill and experience.\n\u2022 Expert in working with various operating systems.\n\u2022 Exceptional Technical Skill.\n\u2022 Highly skilled in customer service.\n\u2022 Good deliver output in less time without losing efficiency.\n\u2022 Excellent Communication Skill.\n\u2022 Proven strength in problem solving, coordination and analysis.", "labels": []}
{"id": 73, "text": "Karthik G V\nProgram Manager, Product Manager, Product Owner, BI /\nDatawarehouse, Big Data, Azure, Agile methodologies, Product Backlog\n\nSecunderabad, Andhra Pradesh - Email me on Indeed: indeed.com/r/Karthik-G-\nV/283106d88eb4649c\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSr. Program Manager / Product Owner, Architect, Consultant\n\nMicrosoft India -  Hyderabad, Telangana -\n\nFebruary 2005 to Present\n\n\u2022 15+ years of experience in the IT &amp; Services Industry in multiple roles as DevOps Architect,\nProgram Manager / Product Owner, Architect, Consultant.\n\u2022 Successfully delivered engagements in the US, UK, LATAM, Asia Pacific and Middle East Regions.\n\n\u2022 Key specialization in building analytical platform, Datawarehouse and Business Intelligence\nsolutions. Have delivered .Net, MVC, API solutions to different customers.\n\u2022 Experience on Program Management, Product Management, DevOps practices, Quality\nAssurance, Continuous Quality, Process setup &amp; implementation, Presales.\n\u2022 Customer solutions delivered using Agile methodology, Iterative development, Test Driven\nDevelopment (TDD).\n\nEDUCATION\n\nPGDBM in Business Management\n\nNarsee Monji -  Hyderabad, Telangana\n\nSKILLS\n\nDatawarehouse / Business Intelligence (10+ years), Agile Methodologies (6 years), Program\nManagement (2 years), Agile (10+ years), Product Roadmap (2 years), Stakeholder\nManagement (10+ years), Risk Management (10+ years)\n\nLINKS\n\nhttps://www.linkedin.com/in/karthik-g-v-7a25462/\n\nAWARDS\n\nMicrosoft Technology Guru\n\nFebruary 2012\n\nhttps://www.indeed.com/r/Karthik-G-V/283106d88eb4649c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Karthik-G-V/283106d88eb4649c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.linkedin.com/in/karthik-g-v-7a25462/\n\n\nMicrosoft Role Model\n\nFebruary 2007\n\nCERTIFICATIONS/LICENSES\n\nISTQB\n\nITIL\n\nCITA\n\nJanuary 2016 to Present\n\nProduct Owner\n\nCSM", "labels": []}
{"id": 74, "text": "Mohammed Murtuza\nMajor Incident Manager / Escalation Manager - Microsoft India\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Mohammed-\nMurtuza/0cdc3284bf1bbeab\n\nWORK EXPERIENCE\n\nMajor Incident Manager / Escalation Manager\n\nMicrosoft India -\n\nJune 2016 to Present\n\nJune 2016 - Till date\n\nRoles and Responsibilities:\n\u2022 Working as a Major Incident Manager / Escalation Manager for MSIT in Microsoft. Primary\nduties include leading team operations, managing team resources, leading &amp; driving high\nimpact technical incidents to resolution. Providing executive updates throughout the incident\ntill resolution, communicate to global customers, senior executives/GMs on ongoing incidents,\nfacilitating positive and timely outcomes by evaluating and escalating incidents to appropriate\nresources when needed. Preparing post major incident reports and KPI reports of Escalation\nManagement for monthly reviews.\n\u2022 Plan and provide elevated level IT support for scheduled planned change/premium events\nacross organization.\n\u2022 Leading team of 20 L1's and managing daily operations.\n\u2022 Conduct Huddle meetings for team on daily performance targets and manage day to day shift\nactivities.\n\u2022 Validating the received escalation impact and pushing the tickets to the right severity with\nproper engagements to mitigate the issue at the earliest and understanding the Microsoft internal\nvarious service lines which is being affected in order to engage the appropriate resources to help\ndrive resolution.\n\u2022 Act as global escalation support to coordinate with various global teams within Microsoft IT and\ndrive Bridge calls on high priority incidents such as Managed P1 &amp; P2 incidents to restore the\nservices as soon as possible and send standard communications to global customers on status\nof the incident, ETA, and current restoration plan.\n\u2022 Managing the lifecycle of all incidents to restore normal service operation as quickly as possible\nand minimize the adverse impact on business operations across globe, thus insuring that the\nbest possible levels of service quality and availability are maintained.\n\u2022 Monitoring the ticketing system and reviewing the communications which will be sent to higher\nmanagement for all major incidents/outages.\n\u2022 Follow the Escalation process and keep up with the SLA's on all the new Escalation tickets and\ndocument incident chronology and timelines and support groups for Major incident resolution.\n\u2022 Complete assigned On-The-Job (OJT) training for the newly hired techs.\n\u2022 Preparing weekly meeting agenda which allows to improve coordination across teams and to\ndiscuss the overall incidents occurred in the past week.\n\u2022 Tracking the model of escalations and calculating the SLA's as a part of Quality check and\ncirculating internal group.\n\nhttps://www.indeed.com/r/Mohammed-Murtuza/0cdc3284bf1bbeab?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Mohammed-Murtuza/0cdc3284bf1bbeab?isid=rex-download&ikw=download-top&co=IN\n\n\n\u2022 Join Daily Cross-Geo shift hand over calls to review all the ongoing high priority incidents/\nescalations from Redmond, USA team to India &amp; Vice versa.\n\u2022 Create and publish post incident reports with high level incident summary and chronology\nto leadership team and thus suggest challenges and improvements to prevent reoccurrence of\nincident.\n\u2022 Identify and analyze problem requests and drive to circulate the RCA of the problem.\n\u2022 Pre Communication preparations for the various Planned Event Support which includes\nreserving the groups which allows to complete the activity as scheduled without any downtime.\n\nCompany: Genpact India\nClient: Schneider Electric\n\nMajor incident Management Team - Incident Coordinator\n\nRoles and Responsibilities:\n\u2022 Provided ultimate ownership and responsibility for end to end Management activities for all\nSeverity 1 &amp; 2 incidents.\n\u2022 Collaborate with internal and partner repair organizations, from engineers to executive and\nEnsures that the correct Technical teams are engaged and proper focus is paid to outages and\nrecovery.\n\u2022 Documented and tracked the timeline of events that occurred in the process to resolution for\neach of the incidents managed in support of post mortem/root cause analysis.\n\u2022 Performed notifications and status of all incidents to high level internal leadership and client\nwhile managing SLA's.\n\u2022 Worked directly with Incident Lifecycle Coordinators to provide initial incident response.\n\u2022 Manage, escalate, status, and assist, coordinating repair efforts on Service Assurance issues\n\u2022 Provide updates to the Management of daily outages. Updating Outage forums.\nCompany: Genpact India\nClient: Invensys\n\nIT consultant\nLevel-2 IT Consultant for Active Directory 2008, Exchange server 2007, VMware ESX 4 .1, Lync\n2010 server.\nRoles and Responsibilities:\n\u2022 Managed User Accounts on Windows NT and UNIX Platform (Creation, Deletion, Permissions,\nand VPN Access for company user's and contractors/vendors)\n\u2022 Configure users, groups, group policy objects, service accounts.\n\u2022 Developed organizational units in Active Directory (AD) and managed user security with group\npolicies.\n\u2022 Created and maintained email addresses and distribution lists in MS Exchange.\n\u2022 Compiled data to assist technical writers with IT new hire manuals and prepared data to report\nto testers for system enhancements.\n\u2022 Handled user account transfers from one field site to another moving client data to different\nservers, to ensure user accessibility.\n\u2022 Maintained Microsoft Exchange e-mail accounts and public folder access through Microsoft\nExchange System Manager.\n\u2022 Served as lead contact for Desk Side Support Technicians, to provide assistance when trouble-\nshooting desk side issues.\n\n\n\n\u2022 Setup queue's for networked printers and added clients to Blackberry Server, which enabled\nemployees to efficiently conduct business while away from the office\nCompany: Genpact India\nClient: Siemens\n\nSAP SRM consultant\n\nKey Responsibilities:\n\u2022 Functional support tickets handled in SAP SRM on the following areas of SAP-SRM.\n\u2022 Maintenance &amp; Creation of shopping carts\n\u2022 Workflow approvals\n\u2022 Creation of automatic account determination for consumables.\n\u2022 Maintenance on Purchase Orders\n\nCompany: Genpact India\nClient: Armstrong\n\nService desk representative.\n\nRoles and Responsibilities:\n\u2022 Effectively answering inbound telephone calls from clients and providing client support through\nthe use of an online knowledge base.\n\u2022 Partnered with Tier II and Tier III help desk peers based in the across the globe to resolve\ncomplex problems that required escalation. Provided detailed descriptions of issues in trouble\nticket system and followed up diligently to ensure swift resolutions\n\u2022 Configuring &amp; troubleshooting Auto Discover, Offline Address Book, Out of Office,\nScheduling &amp; free/busy, Exchange ActiveSync, Outlook Web Access, Outlook Connectivity,\nRPC over HTTP (Outlook Anywhere)\n\u2022 Troubleshooting for login issue, Microsoft Word, Excel, Access, Power Point, Front Page, Visio,\nInternet Explorer, Mozilla Firefox, Scanners, Desktop and Networked Printers.\n\u2022 Unlocking and resetting user's passwords for Active Directory, SAP application\nCompany: Wipro\nClient: HP\nWorked as Technical support representative for HP laptop support.\nRoles and Responsibilities:\n\u2022 Diagnose, troubleshoot and resolve a range of software, hardware and connectivity issues.\nExcel in asking probing questions and researching, analyzing and rectifying problems in Windows\nXP/ Vista/7, MS Office, and LAN/WAN connectivity issues.\n\u2022 Installed software, configured and tested customer PC's, analyzed functionality of peripheral\nappendages.\n\u2022 Instructed and trained end-users regarding computer literacy\n\u2022 Trained in sales and guided customer's in helping to select the right product\n\nEDUCATION\n\nMBA in Marketing and Human Resources\n\nOsmania University -  Hyderabad, Telangana\n\nB.Sc in Electronics\n\n\n\nOsmania University -  Hyderabad, Telangana\n\nSKILLS\n\nACTIVE DIRECTORY (2 years), EXCEL (2 years), EXCHANGE (2 years), INCIDENT MANAGEMENT\n(2 years), OPERATIONS (2 years)\n\nADDITIONAL INFORMATION\n\nProfessional Skills:\n\n\u2022 ITIL- Incident Management, Problem Management, Event Management, Change Management,\nand Configuration Management System.\n\u2022 People Management, Shift Rostering, IT Operations management, Resource management\n\u2022 Virtualization platforms- VMWare, Microsoft Hyper-V\n\u2022 Active Directory 2008, Exchange server 2007, VMware ESX 4 .1, Lync 2010 server, SAP SRM\n\nTools:\nServiceNow, Microsoft ICM, Send Word Now, SCOM, SolarWinds, , MS Clarity Connect, MS\nCentergy, BMC Remedy ticketing tool, Bomgar ticketing tool, Skype For Business, Microsoft\nOneNote, Microsoft StaffHub, MS Excel, PowerPoint tools.", "labels": []}
{"id": 75, "text": "Saurabh Saurabh\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Saurabh-\nSaurabh/87e6b26903460061\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nDeveloper Support Engineer\n\nMicrosoft iGTSC -  Bangalore Urban, Karnataka -\n\nAugust 2007 to Present\n\nEDUCATION\n\nCertificate of Achievement\n\nMicrosoft Virtual Academy\n\nDecember 2016\n\nSKILLS\n\nC, C++, Data Structure, Java (1 year)\n\nADDITIONAL INFORMATION\n\n\u27a2 Managerial Skills: Organizer and volunteer at many inter and intra college events, symposia\netc. in\ncollege.\n\u27a2 Co-curricular: -\n1. Selected by College (TISL) to train the first-year engineering students under the Finishing\nSchool\nProgram (January 2017)\n2. District level soccer and cricket player.\n3. Honored at State Level Singing competition; Performed in college.\n\n\u27a2 Leadership Skills: Effective leadership; associated with many student led organizations;\nStudent leader\nfor Oxygen, A movement for and by Students (2007 &amp; 2009)\n\nDECLARATION\n\nThe abovementioned is true to the best of my knowledge, information and personal belief.\nPlace: Bangalore.\n\nhttps://www.indeed.com/r/Saurabh-Saurabh/87e6b26903460061?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Saurabh-Saurabh/87e6b26903460061?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 76, "text": "Prabhu Prasad Mohapatra\nNeed job urgently\n\nBhubaneswar, Orissa - Email me on Indeed: indeed.com/r/Prabhu-Prasad-\nMohapatra/1e4b62ea17458993\n\nWORK EXPERIENCE\n\nBeta Tester\n\nMicrosoft & Xiaomi -\n\nJanuary 2018 to Present\n\nI am a beta tester\n\nEDUCATION\n\nStill Studying\n\nBhagabati Nodal High School,Sarakana\n\nSKILLS\n\nTypewriting, Editing\n\nLINKS\n\nhttps://plus.google.com/u/0/108623501355423636575\n\nhttps://www.indeed.com/r/Prabhu-Prasad-Mohapatra/1e4b62ea17458993?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Prabhu-Prasad-Mohapatra/1e4b62ea17458993?isid=rex-download&ikw=download-top&co=IN\nhttps://plus.google.com/u/0/108623501355423636575", "labels": []}
{"id": 77, "text": "Raja Chandra Mouli\nCuddapah, Andhra Pradesh - Email me on Indeed: indeed.com/r/Raja-Chandra-\nMouli/445cbf3eb0a361cd\n\nWilling to relocate to: Cuddapah, Andhra Pradesh - Vijayawada, Andhra Pradesh - Visakhapatnam,\nAndhra Pradesh\n\nWORK EXPERIENCE\n\nms office\n\nMicrosoft -  Cuddapah, Andhra Pradesh -\n\nMay 2018 to Present\n\nEDUCATION\n\nBSc,Mecs,2nd year completed in Computer science\n\nards collage kadapa -  Cuddapah, Andhra Pradesh\n\nMay 2018 to June 2019\n\nSKILLS\n\nms office, internet,java (Less than 1 year)\n\nCERTIFICATIONS/LICENSES\n\nDegree,BSc(MECs) 2nd year\n\nMay 2018 to Present\n\nADDITIONAL INFORMATION\n\n3-101 KC Narayana Street,new madavaram(v),Vontimitta(M),Kadapa(D)\n\nhttps://www.indeed.com/r/Raja-Chandra-Mouli/445cbf3eb0a361cd?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Raja-Chandra-Mouli/445cbf3eb0a361cd?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 78, "text": "Krishna Prasad\nPatna City, Bihar - Email me on Indeed: indeed.com/r/Krishna-Prasad/56249a1d0efd3fca\n\nWORK EXPERIENCE\n\nData Entry Operator\n\nMicrosoft -  Patna, Bihar\n\nEDUCATION\n\nBSc in Math\n\nMagadh univercity -  Patna, Bihar\n\nJuly 1999 to April 2001\n\nBSc in Computer\n\nMagadh univercity -  Patna City, Bihar\n\nhttps://www.indeed.com/r/Krishna-Prasad/56249a1d0efd3fca?isid=rex-download&ikw=download-top&co=IN", "labels": []}
{"id": 79, "text": "Soumya Balan\nIT SUPPORT\n\nSulthan Bathery, Kerala, Kerala - Email me on Indeed: indeed.com/r/Soumya-\nBalan/97ead9542c575355\n\n\u27a2 To work in a progressive organization where I can enhance my skills and learning to contribute\nto the success of the organization.\n\nWORK EXPERIENCE\n\nTechnical support engineer\n\nMicrosoft\n\nPosition: TECHNICAL SUPPORT ENGINEER\n\nCompany: Microsoft Corporation - Microsoft India Global Technical Support Center (Microsoft\nIGTSC), Bangalore\n\nYears of Experience: 2 Years and 4 Months\n\nResponsibilities\n\n\u27a2 Represent Microsoft and communicate with corporate customers via telephone, written\ncorrespondence, or electronic service regarding technically complex escalated problems\nidentified in Microsoft software products, and manage relationships with those customers.\n\n\u27a2 Manage not only the technically complex problems, but also politically charged situations\nrequiring the highest level of customer skill.\n\n\u27a2 Receive technically complex, critical or politically hot customer issues, and maintain ownership\nof issue until resolved completely.\n\n\u27a2 Solve highly complex problems, involving broad, in-depth product knowledge or in-depth\nproduct specialty.\n\n\u27a2 Use trace analysis, and other sophisticated tools to analyze problems and develop solutions\nto meet customer needs.\n\n\u27a2 Lead triage meetings to share knowledge with other engineers and develop customer solutions\nefficiently.\n\n\u27a2 Act as technical lead, mentor, and model for a team of engineers; provide direction to others,\nreview solutions and articles, mentoring existing &amp; aspiring Engineers.\n\n\u27a2 Write technical articles for knowledge base.\n\n\u27a2 Consult, collaborate and take escalations when necessary.\n\nhttps://www.indeed.com/r/Soumya-Balan/97ead9542c575355?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Soumya-Balan/97ead9542c575355?isid=rex-download&ikw=download-top&co=IN\n\n\n\u27a2 Maintain working knowledge of pre-release products and take ownership for improvement in\nkey technical areas.\n\n\u27a2 Manage customer escalations and recognize when to solicit additional help.\nParticipate in technical discussions and engage with product team if required to resolve issues\nand represent customer segments.\n\nExchange Server Knowledge\n\n\u27a2 Exchange Server 2007\n\u27a2 Exchange Server 2010\n\u27a2 Exchange Server 2013\n\u27a2 O365\n\nUG PROJECT TITLE: Memory Bounded Anytime Heuristic Search A* Algorithm\n\n\u27a2 This Project presents a heuristic-search algorithm called Memory-bounded Anytime Window\nA*(MAWA*), which is complete, anytime, and memory bounded. MAWA* uses the window-\nbounded anytime-search methodology of AWA* as the basic framework and combines it with the\nmemory-bounded A* -like approach to handle restricted memory situations.\nSimple and efficient versions of MAWA* targeted for tree search have also been presented.\nExperimental results of the sliding-tile puzzle problem and the traveling-salesman problem show\nthe significant advantages of the proposed algorithm over existing methods.\n\nTechnical and Co-Curricular activities\n\n\u27a2 Star Performer in Microsoft IGTSC in 2014.\n\u27a2 Paper Presentations on Applications of Robotics in INOX 2K12.\n\u27a2 Attended a Three-Day workshop on C and C++ Programming and Aliasing.\n\u27a2 Attended a One-Day workshop on Java and Hardware Workshop at VECW\n\u27a2 Paper presentation 4G Technologies, Cloud Computing, Heuristic Algorithms and Applications,\nOpen Source Software.\n\u27a2 Multimedia presentations on Artificial Intellegence, 6th Sense, and Robotics.\n\u27a2 Completed training of OCA (9i, 10g) from Oracle University.\n\u27a2 Attended SPARK training program in Infosys Mysore.\n\u27a2 Attended System Hardware Training program at HCL, Pondicherry.\n\nEDUCATION\n\nBE in Computer Science and Engineering\n\nVivekananda Engineering College for Women -  Chennai, Tamil Nadu\n\n2013\n\nBTEC HNC in Aviation in Hospitality and Travel Management\n\nFrankfinn Institute of Airhostess Training -  Calicut, Kerala\n\n\n\n2008\n\nState Board\n\n2007\n\nSKILLS\n\nLinux (Less than 1 year), Microsoft Office (Less than 1 year), MS OFFICE (Less than 1 year),\nproblem solving (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSkill Set\n\u27a2 Excellent communication and interpersonal skills.\n\u27a2 Proficient in Computer Applications -Microsoft Office Windows (Windows 2007, XP, 8, 8.1 and\nWindows 10), Linux, Fedora.\n\u27a2 Strong analytical and problem solving skills.\n\u27a2 Ability in managing a team of professionals and enjoy being in a team.", "labels": []}
{"id": 80, "text": "pradeep chauhan\npradeep chauhan\n\nNoida, Uttar Pradesh - Email me on Indeed: indeed.com/r/pradeep-\nchauhan/7fd59212dcc556bd\n\nWilling to relocate to: Noida, Uttar Pradesh\n\nWORK EXPERIENCE\n\nteachers\n\nmicrosoft -  Noida, Uttar Pradesh -\n\nApril 2017 to Present\n\nEDUCATION\n\nb.tech in computer science\n\nsunder deep group of institution -  Noida, Uttar Pradesh\n\nCERTIFICATIONS/LICENSES\n\nAndroid Application Development\n\nhttps://www.indeed.com/r/pradeep-chauhan/7fd59212dcc556bd?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/pradeep-chauhan/7fd59212dcc556bd?isid=rex-download&ikw=download-top&co=IN", "labels": []}
