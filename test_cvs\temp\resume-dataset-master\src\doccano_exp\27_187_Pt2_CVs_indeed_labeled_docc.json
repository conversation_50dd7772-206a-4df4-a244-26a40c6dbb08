{"id": 1, "text": "<PERSON><PERSON><PERSON>\nPune, Maharashtra - Email me on Indeed: indeed.com/r/<PERSON><PERSON><PERSON>-<PERSON>/b53674429e164cfc\n\nI am looking for a career which will help me in unlocking and\nemancipating my potential and strength in order to grow with the\nOrganization.\n\nWORK EXPERIENCE\n\nDeveloper\n\nMicrosoft -  Pune, Maharashtra -\n\nAugust 2016 to Present\n\nRole: Developer\nTeam Size: 6\nDescription: Project involved developing dashboards for business owner to have insights for\nbudget management and to analyze data post sales the source data was cleansed using SSIS\nand reports are created on Power BI desktop.\nTools: ETL: SSIS Database: SQL SERVER 2014 Visualization: Power BI\nRole &amp; Responsibilities:\n• Data Cleansing and Data modification were done as per project requirements using SSIS.\n• Created Power BI dashboards for Sales and Post sales data.\n• Re-modelling of data sources based on underlying model, refresh strategies.\n• Optimization for ETL process and dashboards.\n• Dashboards are created in Power BI for actionable insights for Microsoft business owners\n\nEDUCATION\n\nB.TECH in Engineering\n\nGRAPHIC ERA UNIVERSITY -  Dehra Dun, Uttarakhand\n\n2012 to 2016\n\nSTEPPING STONES SCHOOL\n\n2011 to 2012\n\nSECONDRY SCHOOL, STEPPING STONES SCHOOL\n\n2010 to 2011\n\nsenior secondary in PCM\n\nSKILLS\n\nBI (1 year), BUSINESS INTELLIGENCE (1 year), DATABASE (1 year), ETL (1 year), EXTRACT,\nTRANSFORM, AND LOAD (1 year)\n\nhttps://www.indeed.com/r/Akansha-Jain/b53674429e164cfc?isid=rex-download&ikw=download-top&co=IN\n\n\nADDITIONAL INFORMATION\n\nSKILLS ETL: Microsoft SSIS\nReporting: Power BI\nDatabase: MS SQL SERVER 2016\nLanguages: SQL, PL/SQL\n\nSTRENGTHS:\n• Good Analytical and documentation skills\n\n• Quickly integrates into a new environment\n• Good team worker, patient, responsible\n\nDECLARATION\n\nI hereby declare that the information given above is true to the best of my knowledge and belief\nDate: (Akansha Jain)\nPlace: Pune", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [13, 30, "Location"], [53, 95, "Email Address"], [256, 265, "Designation"], [267, 276, "Companies worked at"], [280, 297, "Location"], [301, 323, "Years of Experience"], [331, 340, "Designation"], [530, 534, "Skills"], [562, 571, "Skills"], [587, 590, "Skills"], [592, 596, "Skills"], [607, 622, "Skills"], [638, 646, "Skills"], [678, 692, "Skills"], [697, 714, "Skills"], [759, 763, "Skills"], [775, 784, "Skills"], [939, 949, "Skills"], [784, 794, "Skills"], [953, 963, "Skills"], [979, 987, "Skills"], [1016, 1025, "Companies worked at"], [1054, 1075, "Degree"], [1077, 1099, "College Name"], [1103, 1125, "Location"], [1127, 1139, "Years of Experience"], [1254, 1257, "Skills"], [1280, 1301, "Skills"], [1312, 1320, "Skills"], [1331, 1334, "Skills"], [1384, 1438, "Email Address"], [1518, 1532, "Skills"], [1544, 1552, "Skills"], [1563, 1581, "Skills"], [1593, 1596, "Skills"], [1598, 1604, "Skills"], [1624, 1634, "Skills"], [1639, 1652, "Skills"], [1712, 1723, "Skills"], [1885, 1889, "Location"], [1864, 1876, "Name"]]}
{"id": 2, "text": "Rishabh soni\nAnuppur, Madhya Pradesh - Email me on Indeed: indeed.com/r/Rishabh-\nsoni/503ce837ae2924ff\n\nWORK EXPERIENCE\n\nTyping work\n\nMicrosoft -\n\nJanuary 2018 to Present\n\nWork as much as i can do\n\nEDUCATION\n\nB-tech (CSE)\n\nIIT Delhi\n\nSKILLS\n\nSpecilization\n\nhttps://www.indeed.com/r/Rishabh-soni/503ce837ae2924ff?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Rishabh-soni/503ce837ae2924ff?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[13, 36, "Location"], [0, 12, "Name"], [59, 102, "Email Address"], [134, 143, "Companies worked at"], [147, 170, "Years of Experience"], [209, 226, "College Name"], [227, 232, "Location"]]}
{"id": 3, "text": "Paul Rajiv\nSecunderabad, Andhra Pradesh - Email me on Indeed: indeed.com/r/Paul-\nRajiv/2bd46ce0f01fad54\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nDelivery Specialist\n\nMicrosoft -  hyderbad, Telangana -\n\nApril 2017 to Present\n\nRegal Raptor Motorcycles India:\nA growing automobile company, being the manufacturers and distributors of premium choppers\ncruiser motorcycles across PAN India and SAARC countries.\n\nChief Operations Officer [COO]:\nDirected all facets of the manufacturing, purchasing and sales initiatives for the company.\nProvided direction for sales strategies and co-ordinated assembling functions domestically.\n\n♦ Effective planning and implementation of defined SOP's\n♦ Spearheaded the development of the brand \"Regal Raptor\" becoming one of the most\nrecognisable names in the Industry.\n♦ Proven success in sales and marketing through expertise business development modules and\nstrategic planning capabilities.\n♦ Achieved a significant result in unit sales percentage per annum during the year 2015-2016\nwith a 30% extensive increase rate.\n♦ Enabled a unique marketing strategy by manufacturing India's First fully equipped Police Bike\nwhich indeed helped in penetrating the brand across India.\n♦ Demonstrated and delegated the effective usage of the newly launched police bike to all the\ntop government officials and leaders across the states in India.\n♦ Developed cutting edge technology to be instilled in the product lines which enables problem\nsolving methodologies at break next speed and enhances competitiveness in the modern day\nbusiness.\n♦ Motivated and led a productive sales and customer service oriented teams of support\nexecutives with many receiving continuous accolade for superior performance.\n♦ Developed a strong relationship with vendors, dealers and distributors by constantly working\ntowards expanding business and building markets across PAN India.\n♦ Effective negotiation of costs to meet the gross margin goals.\n♦ Put in significant efforts in planning to set up a Manufacturing plant within the state of\nTelangana to carry out complete production activities and exports business between the SAARC\ncountries\n♦ Constantly worked upon building a strong network among the top government officials to\nfacilitate the sanction of the Manufacturing plant set up in india.\n\nhttps://www.indeed.com/r/Paul-Rajiv/2bd46ce0f01fad54?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Paul-Rajiv/2bd46ce0f01fad54?isid=rex-download&ikw=download-top&co=IN\n\n\nEDUCATION\n\nBachelor of Commerce in Commerce business management\n\nCollege (Osmania University) -  Hyderabad, Andhra Pradesh\n\n2011 to 2014\n\nBoard of Intermediate Education\n\n2009 to 2011\n\nSSC\n\nSt. Patrick's High School\n\n2009\n\nADDITIONAL INFORMATION\n\nSpecialties: Process Optimization, Strategic Management, Profit Centre Management, Customer\ncare strategies, Lean Management, Sales Overheads and Effective Management of Marketing\nstrategies and Manufacturing plant enablement's.\n\nJob Role &amp; Corporate Dossier:\n[DELL]\nA Corporate executive with substantial potential in business management, corporate strategy\nand business expansion.\n\n♦ Worked for Dell International Services, and Carrying an experience of 28 months +\n♦ Worked as a Customer Services Executive for UK/Ireland Regions.\n♦ Front Line working directly along with Customers to Resolve the Query in their best interest.\nHandle Few Major &amp; Minor Escalation in place for Swift Closure with Satisfaction.\n♦ Working with Mutual Business Vendors to get issues going on motion for immediate impact.\n♦ I have been excellent in the existing task, Job description with exceptional numbers winning\nthe Monthly Stacks for 3 times with Consecutive improvement every time.\n♦ I have handled some critical issues with very smooth processing &amp; assurance to customer\nsatisfaction having earned many recognitions with in Dell &amp; outside Dell Clients.\n♦ I have much more recognitions, feedback &amp; success stories to share.\n♦ Hence, I look forward for progression, advancement in my career to grow using the above\nmentioned skills &amp; make a difference in contribution towards expansion in any business\nfields.\n\nACADEMIA\n\nAdditional Qualifications\n\n♦ Good knowledge in Accounting Standards\n♦ Basic knowledge on Java\n♦ Web Technologies: HTML, DHTML, XML, Visual Basic, COBOL, DBMS\n♦ Operating system: Windows-xp, Windows -Vista &amp; Windows-7\n\n\n\n♦ Packages: MS-Office &amp; Basic knowledge in tally (Accounting Systems)\n\nExternal Project Experience:\n\n♦ Worked on Manufacturing India's First Fully Equipped Police Bike or FAB Motors India Pvt. Ltd,\n2017\n♦ Worked on an Industrial Project for Masqati India Pvt. Ltd, 2013.\n♦ Worked on a Stream based Project on Women Harassment and Empowerment in India, 2014.\n\nStrengths and Abilities\n\n♦ Self- Development, Communication, Interpersonal and Problem Solving Skills.\n♦ Willing to travel extensively according to the job requirements.\n♦ Team Work, Flexible, Punctual and Interest to Learn New Things.\n♦ A self-motivated and diligent team player possessing highly evolved and demonstrable\ncommunication.", "meta": {}, "annotation_approver": null, "labels": [[0, 10, "Name"], [11, 39, "Location"], [62, 103, "Email Address"], [153, 172, "Designation"], [174, 183, "Companies worked at"], [187, 206, "Location"], [210, 231, "Years of Experience"], [233, 263, "Companies worked at"], [415, 445, "Designation"], [474, 487, "Skills"], [489, 499, "Skills"], [504, 509, "Skills"], [828, 847, "Skills"], [2051, 2060, "Location"], [2513, 2545, "Degree"], [2546, 2595, "College Name"], [2599, 2624, "Location"], [2626, 2638, "Graduation Year"], [2762, 2782, "Skills"], [2784, 2804, "Skills"], [2832, 2845, "Skills"], [2858, 2873, "Skills"], [2875, 2890, "Skills"], [2905, 2939, "Skills"], [2944, 2957, "Skills"], [2994, 3011, "Designation"], [3014, 3018, "Companies worked at"], [3072, 3091, "Skills"], [3093, 3111, "Skills"], [3150, 3177, "Companies worked at"], [3195, 3220, "Years of Experience"], [3235, 3262, "Designation"], [3267, 3269, "Location"], [3270, 3277, "Location"], [3874, 3878, "Companies worked at"], [3893, 3897, "Companies worked at"], [4228, 4248, "Skills"], [4270, 4274, "Skills"], [4295, 4299, "Skills"], [4301, 4306, "Skills"], [4308, 4311, "Skills"], [4313, 4325, "Skills"], [4327, 4332, "Skills"], [4334, 4338, "Skills"], [4359, 4385, "Skills"], [4392, 4401, "Skills"], [4417, 4426, "Skills"], [4459, 4477, "Skills"], [4580, 4605, "Companies worked at"], [4607, 4611, "Years of Experience"], [4650, 4672, "Companies worked at"], [4674, 4678, "Years of Experience"], [4814, 4827, "Skills"], [4847, 4862, "Skills"]]}
{"id": 4, "text": "Karan Turkar\nBalaghat, Madhya Pradesh - Email me on Indeed: indeed.com/r/Karan-\nTurkar/9ed71ae013a9e899\n\nWORK EXPERIENCE\n\nSoftware developer\n\nMicrosoft -\n\nAugust 2016 to Present\n\nSoftware developer\n\nEDUCATION\n\nBE\n\nDAVV\n\nSKILLS\n\nC++, Html, Javascript, Software Development\n\nhttps://www.indeed.com/r/Karan-Turkar/9ed71ae013a9e899?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Karan-Turkar/9ed71ae013a9e899?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [13, 37, "Location"], [60, 103, "Email Address"], [122, 140, "Designation"], [142, 151, "Name"], [155, 177, "Years of Experience"], [179, 197, "Designation"], [210, 212, "Degree"], [228, 231, "Skills"], [233, 237, "Skills"], [239, 249, "Skills"], [251, 271, "Skills"], [273, 327, "Email Address"], [369, 423, "Email Address"]]}
{"id": 5, "text": "Akshay Dubey\nActively looking for opportunity in .NET Development\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Akshay-Dubey/87dcd40b335e6ffa\n\nTo work in a challenging environment that will test me at all level and allow me to utilize my\nprofessional as well as personal skills by way of positive contribution to the organization.\n\nWilling to relocate to: Pune, Maharashtra\n\nWORK EXPERIENCE\n\nMicrosoft Student Partner\n\nMICROSOFT -\n\nJanuary 2015 to July 2016\n\nUndergraduate Program\n\nROLES &amp; RESPONSIBILITIES:\n• The Microsoft Student Partners (MSP) is a worldwide recognizable program for students\nmajoring in disciplines related to technology.\n• A strong understanding of the overall set of Microsoft Visual languages and the .NET\nFramework along with specific demonstrable skills in one or more languages or technologies.\n• Conducted Session of Microsoft Azure Conference in September 2015.\n• Hosted tech events and gave demos on campus and actively involved in student\ntechnical clubs (Microsoft Campus Club), societies, and organizations, preferably in leadership\nroles.\n• Join exclusive Microsoft training events to learn the latest technologies.\nAttended the Student Technology Day, which included high-profile presentations, such as one by\nthe CEO of Microsoft, Satya Nadella; and TechDays or DevCon (Developer\nConference)\n\nWeb Link: https://akshaydubey.wordpress.com/\n\nMicrosoft Student Associate\n\nMICROSOFT -\n\nJuly 2014 to December 2015\n\nUndergraduate Program\n\nROLES &amp; RESPONSIBILITIES:\n• The Microsoft Student Associate (MSA) program is an educational and promotional\nprogram especially for undergraduates Students.\n• Microsoft Student Associates are technology enthusiasts, student influencers using\ntechnology for social impact.\n• Learn &amp; implement emerging technologies like Microsoft Visual Studio IDE, ASP.NET,\nand useful desktop application using the free tools &amp; emulator.\n\nhttps://www.indeed.com/r/Akshay-Dubey/87dcd40b335e6ffa?isid=rex-download&ikw=download-top&co=IN\n\n\n• Promoting and building city-level Microsoft Student User Group collaborating with other\nMSPs.\n\nWeb Link: https://admicrosoft.blogspot.in/\n\nBE PROJECT UNDERTAKEN:\n• PROJECT TITLE:\nWeb Based Authentication Providing High Security of Graphical Images Making Ninja\nPassword Authentication Tool.\n\n• PROJECT DESCRIPTION:\nIt was developed on ASP.NET framework and the main objective is to providing high\nsecurity wall on web application and make convenient to user or stakeholder to secure\ntheir web application by using MS SQL Server database.\n\nEDUCATION\n\nB.E\n\nSinhgad Institute\n\n2016\n\nHSC\n\nMaharashtra State -  Lonavale, Maharashtra\n\n2012\n\nSSC\n\nMaharashtra State\n\n2010\n\nTechnology & Science\n\nPune University -  Lonavale, Maharashtra\n\nSKILLS\n\n.NET (3 years), ASP (1 year), ASP.NET (1 year), C# (1 year), databases. (1 year), C++, Css,\nJavascript, Html, C\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\n\nProgramming Languages C# .NET, CPP, C\n\nWeb Development ASP.NET, HTML, CSS, JavaScript\n\n\n\nTools used MS visual Studio v2013, MS SQL Server v2008\n\nOperating Systems Windows 10 / 8.1/ 7\n\nDatabases MS-SQL", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [49, 65, "Designation"], [67, 84, "Location"], [107, 149, "Email Address"], [364, 381, "Location"], [427, 436, "Companies worked at"], [702, 728, "Skills"], [737, 751, "Skills"], [1416, 1425, "Companies worked at"], [1387, 1414, "Degree"], [400, 425, "Degree"], [526, 558, "Degree"], [1429, 1455, "Graduation Year"], [440, 465, "Graduation Year"], [1516, 1549, "Degree"], [1642, 1670, "Degree"], [1806, 1833, "Skills"], [1835, 1842, "Skills"], [1913, 1967, "Email Address"], [2348, 2355, "Skills"], [2527, 2540, "Skills"], [2563, 2566, "Degree"], [2568, 2585, "College Name"], [2587, 2591, "Graduation Year"], [2593, 2615, "College Name"], [2619, 2640, "Location"], [2642, 2646, "Graduation Year"], [2672, 2676, "Graduation Year"], [2678, 2698, "Degree"], [2700, 2715, "College Name"], [2719, 2740, "Location"], [2750, 2754, "Skills"], [2766, 2769, "Skills"], [2780, 2787, "Skills"], [2798, 2800, "Skills"], [2811, 2820, "Skills"], [2832, 2835, "Skills"], [2837, 2840, "Skills"], [2842, 2852, "Skills"], [2854, 2858, "Skills"], [2860, 2861, "Skills"], [2927, 2929, "Skills"], [2930, 2934, "Skills"], [2936, 2939, "Skills"], [2941, 2942, "Skills"], [2960, 2967, "Skills"], [2969, 2973, "Skills"], [2975, 2978, "Skills"], [2980, 2990, "Skills"], [3005, 3021, "Skills"], [3029, 3042, "Skills"], [3068, 3087, "Skills"], [3099, 3105, "Skills"]]}
{"id": 6, "text": "Sayani Goswami\nI Phone solution Consultant\n\nKolkata, West Bengal - Email me on Indeed: indeed.com/r/Sayani-\nGoswami/066e4d4956f82ee3\n\nWORK EXPERIENCE\n\nI Phone solution Consultant\n\n-\n\nOctober 2015 to 2017\n\nJob Profile -1) Working as Sales Consultant (Promoter)\n\nMICROSOFT -\n\nAugust 2014 to October 2015\n\nEDUCATION\n\nPeriyar University\n\n2016 to Present\n\nEnglish, Hindi & Bengali\n\nJoydev Road Girls High School -  Barddhaman, West Bengal\n\n2004\n\nhttps://www.indeed.com/r/Sayani-Goswami/066e4d4956f82ee3?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Sayani-Goswami/066e4d4956f82ee3?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [17, 42, "Designation"], [44, 64, "Location"], [87, 132, "Email Address"], [153, 178, "Designation"], [183, 203, "Years of Experience"], [232, 248, "Designation"], [250, 258, "Designation"], [261, 270, "Companies worked at"], [274, 301, "Years of Experience"], [314, 332, "College Name"], [334, 349, "Graduation Year"], [351, 358, "Skills"], [360, 365, "Skills"], [368, 375, "Skills"], [441, 497, "Email Address"]]}
{"id": 7, "text": "Sweety Garg\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Sweety-Garg/9f2d2afa546d730d\n\nWORK EXPERIENCE\n\nTechnical consultant\n\nMicrosoft -  Bangalore Urban, Karnataka -\n\nJuly 2013 to Present\n\nEDUCATION\n\nB.Tech in Engineering\n\nSRM University -  Chennai, Tamil Nadu\n\n2013\n\nEngineering\n\nCentral Academy School\n\n2006\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS:\n❖ Expertise in Microsoft Office Power Point, Microsoft Office Word.\n\n❖ Basic knowledge about C and C++ Programming.\n\n❖ Management skills.\n❖ Responsible.\n\nhttps://www.indeed.com/r/Sweety-Garg/9f2d2afa546d730d?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 11, "Name"], [55, 96, "Email Address"], [12, 32, "Location"], [115, 135, "Designation"], [137, 146, "Companies worked at"], [150, 176, "Location"], [180, 200, "Years of Experience"], [213, 234, "Degree"], [236, 250, "College Name"], [254, 273, "Location"], [275, 279, "Graduation Year"], [281, 309, "College Name"], [318, 322, "Graduation Year"], [381, 397, "Skills"], [398, 409, "Skills"], [411, 432, "Skills"], [459, 460, "Skills"], [465, 468, "Skills"], [485, 495, "Skills"], [520, 573, "Email Address"]]}
{"id": 8, "text": "Ramkrishan Bhatt\npython developer\n\nBengaluru, Karnataka, Karnataka - Email me on Indeed: indeed.com/r/Ramkrishan-Bhatt/\nda07dc6d058dfc64\n\nWORK EXPERIENCE\n\nPython Developer\n\nMicrosoft -  Bengaluru, Karnataka -\n\nJune 2017 to November 2017\n\nCareer Summary\n7.5 years IT-related employment as a Software Developer expert in Python, web development,\nfamiliar with\nDjango, Flask, Web2py, Tensorflow, jupyter Notebook, Machine learning and Artificial\nIntelligence . Played\nkey role in the team as solution provider for simplifying the existing system, while working for\nclients. saved\ncost and time per annum by automating administrative system. Wrote technical documents and\nuser's manuals.\n\nTechnical Summary\nLanguages: Python, Java, Angular JS, Polymer 1.0, Jquery.\nLibrary and tool: - Tensorflow, Deep Learning, Machine Learning, Word2vec, Artificial\nIntelligence, PyCharm, Jupiter Notebook\nFrameworks: Django, Web2py, Flask, JPA, Google App Engine platform, Google Apps\nIntegration.\nDatabases: MongoDB, Google Big Table(NoSQL database), MySQL 5.0, Sqlite, Postgresql. Google\nCloud SQL, Redis\nOperating Systems: Linux(ubuntu), Window\nApplications: Google Apps, Webex database, Zoho, MS-Office, Google Apps migration from\nlotus notes, Working with active directory syncing with the Google apps.\nElastic Search, Spring MVC, Gantter product\n\nB1 B2 US VISA till 2025\n\nEDUCATION\n\nMongo\n\nDadabari -  Ajmer, Rajasthan\n\nJune 2015 to August 2016\n\nBCA\n\naffiliated -  Kota, Rajasthan\n\nhttps://www.indeed.com/r/Ramkrishan-Bhatt/da07dc6d058dfc64?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Ramkrishan-Bhatt/da07dc6d058dfc64?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 16, "Name"], [17, 33, "Designation"], [35, 66, "Location"], [89, 136, "Email Address"], [155, 171, "Designation"], [173, 182, "Companies worked at"], [186, 206, "Location"], [210, 236, "Years of Experience"], [253, 262, "Years of Experience"], [290, 308, "Designation"], [319, 325, "Skills"], [327, 342, "Skills"], [358, 364, "Skills"], [366, 371, "Skills"], [373, 379, "Skills"], [381, 391, "Skills"], [393, 409, "Skills"], [411, 427, "Skills"], [432, 455, "Skills"], [644, 663, "Skills"], [714, 720, "Skills"], [722, 726, "Skills"], [728, 735, "Skills"], [736, 738, "Skills"], [740, 751, "Skills"], [753, 759, "Skills"], [781, 791, "Skills"], [793, 806, "Skills"], [808, 824, "Skills"], [826, 834, "Skills"], [836, 859, "Skills"], [861, 868, "Skills"], [870, 886, "Skills"], [899, 905, "Skills"], [907, 913, "Skills"], [915, 920, "Skills"], [922, 925, "Skills"], [955, 978, "Skills"], [991, 998, "Skills"], [1000, 1016, "Skills"], [1034, 1043, "Skills"], [1045, 1051, "Skills"], [1053, 1063, "Skills"], [1065, 1081, "Skills"], [1083, 1088, "Skills"], [1108, 1113, "Skills"], [1144, 1155, "Skills"], [1157, 1162, "Skills"], [1173, 1177, "Skills"], [1179, 1188, "Skills"], [1190, 1201, "Skills"], [1290, 1304, "Skills"], [1306, 1316, "Skills"], [1371, 1386, "Skills"], [1390, 1406, "Location"], [1453, 1468, "Location"], [1470, 1528, "Email Address"]]}
{"id": 9, "text": "B. Gokul\nGokul, Uttar Pradesh - Email me on Indeed: indeed.com/r/B-Gokul/ca7750b94830268d\n\nI Wish to acquire a high level of capability in project management, technical,\nCommercial &amp; project control aspects by inspiring leadership, acumen to build &amp; lead\na high\nPerforming team to deliver results within strict time lines.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSoftware\n\nMicrosoft -\n\n2018 to Present\n\n• Tamil and English\nSTRENGTH:\n• Honest and Innovative\nACADEMIC PROJECT:\n• AADHAR (RFID) Card Based Voting Machine\n\nEDUCATION\n\nGovernment\n\nState board\n\n2015\n\nSSLC in Government\n\nState board\n\n2013\n\nBOARD UNIVERSITY\n\nB.SC\n\nPeriyar University -  Salem, Tamil Nadu\n\nSKILLS\n\nEXCEL (Less than 1 year), MICROSOFT WORD (Less than 1 year), MICROSOFT WORD 2010 (Less\nthan 1 year), MS EXCEL (Less than 1 year), WORD (Less than 1 year)\n\nhttps://www.indeed.com/r/B-Gokul/ca7750b94830268d?isid=rex-download&ikw=download-top&co=IN\n\n\nADDITIONAL INFORMATION\n\nCOMPUTER SKILLS:\n• Ms Excel\n• Microsoft word 2010", "meta": {}, "annotation_approver": null, "labels": [[3, 29, "Location"], [52, 89, "Email Address"], [139, 157, "Skills"], [159, 168, "Skills"], [170, 180, "Skills"], [390, 399, "Companies worked at"], [403, 418, "Years of Experience"], [422, 427, "Skills"], [432, 439, "Skills"], [463, 473, "Skills"], [571, 575, "Graduation Year"], [610, 614, "Graduation Year"], [616, 632, "College Name"], [634, 638, "Degree"], [640, 658, "College Name"], [662, 679, "Location"], [689, 694, "Skills"], [715, 729, "Skills"], [750, 769, "Skills"], [790, 798, "Skills"], [819, 823, "Skills"], [844, 893, "Email Address"], [980, 988, "Skills"], [991, 1010, "Skills"]]}
{"id": 10, "text": "Anand S\nBangalore, Karnataka - Email me on Indeed: indeed.com/r/Anand-S/ce230cad6115ae68\n\nWORK EXPERIENCE\n\nSpace auditing\n\nMicrosoft -\n\n2017 to 2017\n\nAuditing the space.\n\nEDUCATION\n\nVijaya main\n\nVijaya\n\nSKILLS\n\nGood listener,take up responsibilities,good at communication,great at taking\nchallenges,excellent in various sports like soccer,cricket,kabbadi,cycling,running,swimming,\nfluent in English,kannada, known languages hindi,tail,telugu\n\nhttps://www.indeed.com/r/Anand-S/ce230cad6115ae68?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 7, "Name"], [8, 28, "Location"], [51, 88, "Email Address"], [123, 132, "Companies worked at"], [136, 148, "Years of Experience"], [258, 271, "Skills"], [391, 398, "Skills"], [399, 406, "Skills"], [424, 429, "Skills"], [430, 434, "Skills"], [435, 441, "Skills"], [443, 492, "Email Address"]]}
{"id": 11, "text": "Krishna Prasad\nPatna, Bihar - Email me on Indeed: indeed.com/r/Krishna-Prasad/b8d7a1135a44a37a\n\nWilling to relocate to: Patna, Bihar - Danapur, Bihar\n\nWORK EXPERIENCE\n\nData Entry Operator\n\nMicrosoft\n\nEDUCATION\n\nBSc in Computer Science\n\nMagadh university -  Patna, Bihar\n\nhttps://www.indeed.com/r/Krishna-Prasad/b8d7a1135a44a37a?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [15, 27, "Location"], [50, 94, "Email Address"], [120, 132, "Location"], [135, 149, "Location"], [168, 187, "Designation"], [189, 198, "Companies worked at"], [211, 234, "Degree"], [236, 253, "College Name"], [257, 269, "Companies worked at"], [271, 327, "Email Address"]]}
{"id": 12, "text": "Saurabh Sandhikar\nSAURABH SANDHIKAR\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Saurabh-Sandhikar/\ne490c0d49e5aa698\n\nWilling to relocate to: hyderbad, Telangana\n\nWORK EXPERIENCE\n\nAdvocacy Operations Specialist\n\nAMAZON\n\nAdvocacy operations specialist\nWhat I Do at my current job?\nInvestigation and analysis of private social media posts related to delivery\nexperience and presentation of data to the stakeholders.\nManagement of service areas and logistics routing for Amazon delivery partners is\nUK and NA marketplace.\nResolving Trouble Tickets of high severity and updating leadership and team about\nongoing issues.\nManaging rate hikes and peak pricing for Amazon delivery partners during bad\nroad/weather conditions\nMy performance at work:\nI was responsible for improving the delivery experience of hundreds Amazon flex\ndelivery partners by resolving their issues which they posted on private social media\ngroup (investigating the issues, driver related information and escalating to the right\nleadership)\nI was responsible for helping nearly 30 amazon delivery partners (flex) from wrongly\ngetting terminated from work.\nI reported many app related issues by investigating the posts by delivery\npartners, resolved flaws which effected work productivity. (Because of my computer\nscience background I was able to understand the app issues faced)\nI was awarded as the best performer of the team during peak end (annual event)\n\nMicrosoft Student partner\n\nMicrosoft\n\nMicrosoft Student partner\n\nEDUCATION\n\nB.Tech in CSE\n\nGandhi Institute of Science And Technology (GITAM) University\n\nJune 2012 to April 2016\n\nhttps://www.indeed.com/r/Saurabh-Sandhikar/e490c0d49e5aa698?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Saurabh-Sandhikar/e490c0d49e5aa698?isid=rex-download&ikw=download-top&co=IN\n\n\nDelta Academy for IIT-JEE\n\nMay 2010 to May 2012\n\nSt Georges Grammar School\n\nJune 2009 to April 2010\n\nSKILLS\n\nEXCEL (Less than 1 year), HTML (Less than 1 year), JAVASCRIPT (Less than 1 year), MS EXCEL\n(Less than 1 year), SQL (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nyears.\nMicrosoft student partner-Developed applications and games for windows and\nwindows phone store and held presentations in engineering colleges informing\nstudents about latest technologies.\nMicrosoft Certified Technology Associate: Web Development Fundamentals.\nCompleted Microsoft Leadlab &amp; Global Sharpers Fellowship.\nCompleted Ethical hacking courses by Computer Society of India and Ankit Fadia.\nParticipated in State level table tennis championship for 3 years.\n\nSKILLS SQL\nMS Excel\nHTML\nJavascript", "meta": {}, "annotation_approver": null, "labels": [[0, 17, "Name"], [18, 35, "Name"], [37, 57, "Location"], [80, 128, "Email Address"], [154, 173, "Location"], [192, 222, "Designation"], [224, 230, "Companies worked at"], [232, 262, "Designation"], [310, 318, "Skills"], [480, 486, "Companies worked at"], [508, 510, "Location"], [670, 676, "Companies worked at"], [822, 828, "Companies worked at"], [1438, 1463, "Degree"], [1465, 1501, "Degree"], [1514, 1528, "Degree"], [1529, 1590, "College Name"], [1592, 1615, "Graduation Year"], [1617, 1676, "Email Address"], [1930, 1935, "Skills"], [1956, 1960, "Skills"], [1981, 1991, "Skills"], [2012, 2020, "Skills"], [2041, 2044, "Skills"], [2096, 2121, "Degree"], [2284, 2324, "Degree"], [2326, 2354, "Skills"], [2428, 2443, "Skills"], [2573, 2576, "Skills"], [2577, 2585, "Skills"], [2586, 2590, "Skills"], [2591, 2601, "Skills"]]}
{"id": 13, "text": "Priyesh Dubey\nAzure Developer with 9 Yrs 8 months of working experience in Dot Net\nTechnologies\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Priyesh-Dubey/cd079a9e5de18281\n\nWilling to relocate to: Pune, Maharashtra - Hyderabad, Telangana - Bangalore Urban, Karnataka\n\nWORK EXPERIENCE\n\nArchitect\n\nCognizant -  Hyderabad, Telangana -\n\nAugust 2017 to Present\n\n2017.\n• Worked with Microsoft, Hyderabad as Software Engineer from January 2015 to Aug 2017.\n• Worked as Senior Software Engineer in Philips India Limited, Bangalore from October 2011 to\nDec 2014.\n• Worked as Project Engineer in Wipro Technologies from Aug 2008 to Oct 2011.\n\nTechnical Experience\n• 9 years 8 month of total IT relevant experience in analysis, design and development of windows,\nweb and cloud based (Azure) application in .NET.\n• Proficient experience of 4 years in WPF using MVVM and PRISM.\n• Proficient experience of 1.5 year in Azure Technologies.\n• Worked at the architecture, design and implementation level of the development.\n• Proficient to understand the technical &amp; functional specifications.\n• Have strong hold on Object Oriented Programming concepts with extensive exposure in OOAD\nusing various Design Patterns.\n• Good working knowledge with designer tools such as Visio.\n• Experienced in writing unit tests using NUnit and VS tests using Moq and Microsoft Fakes for\nC# modules.\n• Good working knowledge of Agile and Waterfall methodology.\n• Good at grasping any kind of technology at a tremendous pace with minimum training.\n\nProfessional Experience\n• Possess good work ethics with excellent communication and interpersonal skills.\n• Quick learner and very keen to learn new technologies.\n• Effective in working independently as well as collaboratively.\n• Experience in developing quick POC alternatives as per the new technology needs and analyze\nthe performance and maintenance aspect.\n• Highly motivated, dependable trouble shooter and possess good analytical and problem-solving\ntechniques.\n• In depth knowledge and hands on experience of Software Development Life Cycle (SDLC) and\nProject management processes.\n• Strong analytical ability, conceptualization, presentation and communication skills.\n\nhttps://www.indeed.com/r/Priyesh-Dubey/cd079a9e5de18281?isid=rex-download&ikw=download-top&co=IN\n\n\nSoftware Developer\n\nMicrosoft\n\nWorked as Individual Contributor in Technical Role\n\nEDUCATION\n\nMCA\n\nMANIT\n\nSKILLS\n\nWPF (Less than 1 year), Web API, Azure, ASP.Net MVC, Design Patterns, WCF, C#\n\nADDITIONAL INFORMATION\n\nTechnical Skills\n\nMicrosoft Technologies C#.Net, WPF, WCF, ASP.Net MVC, Web API, Biztalk Server 2013\nCloud Azure\nVersion Control Git, VSTS, SVN, IBM Rational Clear Case\nScripting PowerShell\nUnit Test MOQ, VS Test, Microsoft Fakes, N-Unit", "meta": {}, "annotation_approver": null, "labels": [[0, 13, "Name"], [14, 29, "Designation"], [35, 71, "Years of Experience"], [75, 95, "Skills"], [97, 117, "Location"], [140, 183, "Email Address"], [209, 226, "Location"], [229, 249, "Location"], [252, 278, "Location"], [297, 317, "Designation"], [321, 341, "Location"], [345, 373, "Years of Experience"], [389, 398, "Companies worked at"], [400, 409, "Location"], [413, 430, "Designation"], [436, 460, "Years of Experience"], [481, 498, "Designation"], [502, 523, "Companies worked at"], [525, 534, "Location"], [540, 564, "Years of Experience"], [578, 594, "Designation"], [598, 616, "Companies worked at"], [622, 642, "Years of Experience"], [668, 683, "Years of Experience"], [719, 727, "Skills"], [729, 735, "Skills"], [740, 751, "Skills"], [755, 811, "Skills"], [851, 854, "Skills"], [861, 865, "Skills"], [870, 875, "Skills"], [916, 934, "Skills"], [1114, 1141, "Skills"], [1197, 1212, "Skills"], [1267, 1272, "Skills"], [1316, 1321, "Skills"], [1326, 1334, "Skills"], [1341, 1344, "Skills"], [1369, 1371, "Skills"], [1409, 1414, "Skills"], [1419, 1428, "Skills"], [2089, 2107, "Skills"], [2046, 2084, "Skills"], [2184, 2197, "Skills"], [2167, 2179, "Skills"], [2207, 2262, "Email Address"], [2306, 2324, "Designation"], [2326, 2335, "Companies worked at"], [2420, 2423, "Skills"], [2444, 2451, "Skills"], [2453, 2458, "Skills"], [2460, 2471, "Skills"], [2473, 2488, "Skills"], [2490, 2493, "Skills"], [2495, 2497, "Skills"], [2541, 2550, "Skills"], [2564, 2570, "Skills"], [2572, 2575, "Skills"], [2577, 2580, "Skills"], [2582, 2593, "Skills"], [2595, 2602, "Skills"], [2604, 2623, "Skills"], [2624, 2635, "Skills"], [2652, 2655, "Skills"], [2657, 2661, "Skills"], [2663, 2666, "Skills"], [2668, 2671, "Skills"], [2702, 2712, "Skills"], [2713, 2722, "Skills"], [2728, 2735, "Skills"], [2754, 2760, "Skills"]]}
{"id": 14, "text": "Laya A\nCluster HR Manager - Velammal New\n\nChennai, Tamil Nadu - Email me on Indeed: indeed.com/r/Laya-A/74af8dc044f3fa7f\n\n• A competent HR Manager professional with over 10+ years of rich experience\nacross all domains of human resource management.\n• Demonstrated expertise to work in sync with senior management team to\nintegrate the human resource function within the organisation.\n• Carrying experience in the new line of businesses, high growth operations and\nrestructuring.\n• Proven skill set in transforming &amp; harmonizing complex and obscure ideas into an\neasily assimilable plan.\n• Exemplified leading from the front, time &amp; again and inculcated a feeling of\nmotivation and constant work towards the attainment of the firm's goal.\n• Adept in performance assessment, recruitment, induction process, compensation\nmanagement, employee welfare planning, employee retention, employee\nmotivation, grievance handling, time office management, team management &amp;\noffice administration.\n• A proactive professional with outstanding communication, interpersonal and\nrelationship management skills along with an ability to communicate effectively with personnel\nat all levels within the organisation.\n• Ensuring continuity as well as effective delivery of human resource functional\nservices.\n\nWORK EXPERIENCE\n\nCluster HR Manager\n\nVelammal New -  Chennai, Tamil Nadu -\n\nFebruary 2017 to Present\n\nCluster Manager\n\nVelammal new gen park -\n\nFebruary 2017 to Present\n\nRESPONSIBILITIES\nTALENTACQUISITION\n• Enhancing the manpower plan in sync with the expansion strategies for all LOBs in order to\nformulate new hiring strategies.\n• Implementing innovative ways to engage probable candidates for future expansion.\n• Curbing average turnaround time of recruitment by smart measures at relatively lesser cost.\n• Driving campus recruitment at elite business, law and engineering colleges with an intent to\nhire the best of the best.\n• Leading internal hiring &amp; guiding manpower forms on sourcing the best talents against\nthe vacant positions.\n• Strategizing human resource requirements in consultation with the various\n\nhttps://www.indeed.com/r/Laya-A/74af8dc044f3fa7f?isid=rex-download&ikw=download-top&co=IN\n\n\ndepartmental heads followed by conducting selection interviews for shortlisted\ncandidates.\n• Keeping an eye on the latest hiring trends in the industry and implementing them.\n• Coaching team members on effectively creating assessment and interview rating sheet.\n• Streamlining interview processes, managing recruitment tracker, screening of resumes,\nreference check, background verification, document verification and closing the position post\nsuccessful salary negotiation.\n\nCOMPENSATION MANAGEMENT\n• Devising a wage model to reach compensation goals of the organization.\n• Developing salary grid in sync with the organisational hierarchy to inculcate concord in the\nsystem.\n• Drafting, designing as well as implementing attractive incentives plan to motivate staffs across\nall levels in the organisation with an intent to accelerate the revenue by optimising the degree\nof enthusiasm nestled in the work culture.\n• Examining and analysing the industry to strive equilibrium among internal equity, external\nequity, compliance and other objectives of pay model.\n• Designing reward and recognition plan for sales and marketing employees as an attempt to\nacknowledge their efforts towards the accomplishment of the organisational goals.\n\nCentre Head Manager HR and Admin\n\nKinder -  Cherthala, KERALA, IN -\n\nNovember 2015 to January 2017\n\nHR Manager\n\nLeonine Info solutions -\n\nJune 2012 to November 2015\n\nProcess Associate@ Infosys\n\nHR SPOC -  Bengaluru, Karnataka -\n\nMay 2011 to June 2012\n\nBangalore, India\n\nEDUCATION\n\nB.B.A in UIT\n\nKerala University\n\n2008\n\nSKILLS\n\nHR (9 years), COMPENSATION (1 year), RECRUITMENT (1 year), DETAIL ORIENTED (Less than 1\nyear), DOCUMENTATION (Less than 1 year)\n\n\n\nADDITIONAL INFORMATION\n\nCORE COMPETENCIES\n• Performance Management\n• Recruitment &amp; Retention\n• Training &amp; Development\n• HR Policies &amp; Procedures\n• Employee Exit\n• Confidential Record Keeping\n• HR Department Start-up\n• Event Management\n• On-boarding, Orientation &amp; Induction\n• Employee Counselling\n• Attendance &amp; Leave Management\n• Team Building and Morale building\n• Process Documentation\n• Payroll &amp; Compensation\n\nTECHNICAL EXPERTISE\n• Well versed with Microsoft Office (Word, Excel &amp; Power Point)\n• Microsoft Project\n• Lotus, outlook and internet job portal applications.\n\nPERSONAGE ELEMENTS\n• Analytical &amp; Critical thinking\n• Compliant &amp; Adaptive\n• Detail Oriented\n• Flexible\n• Honest &amp; Hardworking\n• Leadership\n• Logical\n• Versatile\n• Work Ethic", "meta": {}, "annotation_approver": null, "labels": [[0, 6, "Name"], [15, 25, "Designation"], [28, 61, "Location"], [84, 120, "Email Address"], [136, 146, "Designation"], [170, 198, "Years of Experience"]]}
{"id": 15, "text": "Vishwanath P\nSenior Executive (MIS & Audit) - Job Profile in LabourNet Services India\nPvt Ltd\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Vishwanath-P/06a16ac2d087d3c9\n\nI look forward to a career that provides me an opportunity to improve my knowledge and\npersonality in a challenging work atmosphere. In the long run, I would like to grow leadership\nposition in the organization and make a meaningful contribution to the organization.\n\nOverview:\n➢ Total experience of 7.3years.\n➢ Have maintained superior's and peer's confidence in efficiency and accuracy of my work.\n\nWORK EXPERIENCE\n\nSenior Executive (MIS & Audit)\n\nJob Profile in LabourNet Services India Pvt Ltd -\n\nJune 2016 to Present\n\nfrom June 2016.\n\nOperating System: Windows 7\nTools Used: MS Office (Excel, Word, PowerPoint, Outlook)\n➢ Provide weekly targets to Placement officers and follow-up on the targets v/s achieved and\nmore importantly all the placement related activities are documented.\n➢ Working on projection against target achieved for each individual Pan India Placement officers\nto get there incentives.\n➢ Taking care of placement internal Audit.\n➢ Supporting Pan India on CLCS (Candidate Life Cycle System) demo to PO, initial hand holding\nof PO on using data, documentation and template creation.\n➢ Timely Sharing Regular Dashboard/Reports to Placement Officers whenever required.\n➢ Hand holding \"single point of contact\" owning the placement data, centrally and can share\nthe same to account management, MIS team, Sales team, Management and whoever needs the\ndata in the organization including NSDC.\n➢ The Data Management, colleague should have the right information and present during any\nclient audits in HQ office\n➢ Taking care of CLCS Tool and will be the one point of contact for placement related queries\nor issues in Tool.\n➢ First point of contact for any issues or queries in CLCS placement tool in the organization.\n➢ Giving request to IT Software team of placement tool changes to run the business in smooth.\n➢ Taking care of RMS (Reimbursement Management System)\n➢ Cross checking original bills and soft copies before approving claims in RMS for all placement\nofficer of Pan India (Size - 23)\n➢ Supporting other 3 projects Barclays, HRS (Human Resources Services) &amp; NAPS (National\nApprenticeship Promotion Scheme)\n• Manager role: I have a team member directly reporting to me.\n\nhttps://www.indeed.com/r/Vishwanath-P/06a16ac2d087d3c9?isid=rex-download&ikw=download-top&co=IN\n\n\nMIS Executive\n\nANI Technologies -\n\nOctober 2015 to April 2016\n\nOperating System: Windows 8.1, 10\nTools Used: MS Excel, Google Sheets, MS word, MS outlook, SQL Database.\n➢ Handling Pan India Ola Cafe Operational MIS.\n➢ Provide support to the manager in all aspect of operations.\n➢ Flashing daily business MIS to Region managers and Product manager.\n➢ Taking care of DB (Delivery Boy) attendance of pan India.\n➢ Handling Billing and invoices of DB's and coordinating with finance team for monthly salaries.\n➢ Shared and handled weekly menu data sheets with IT Software team to update in App.\n➢ Taken care of complete Ola Café process operational MIS's.\n➢ Coordinating with IT team to flash daily business summary.\n➢ Keep updating daily revenue of Cites &amp;o Zone.\n➢ Taken care of marketing MIS.\n➢ Processing of vendor bills, and also coordinating for reimbursement.\nWorked on Pilot process (Ola Prime) to set all the MIS reports to complete process.\n➢ Handled performance tracker of the prime drivers.\n➢ Handled feedback dumps of Ola prime customers.\n➢ Shared daily and weekly Prime data for the cities managers.\n➢ Taken care of daily offense tracker of calling done by customer feedback callers.\n➢ Mapped the Wi-Fi usage of each cab in pan India.\n➢ Maintained the agent, cities and over all calling dashboard and flashing on daily basis.\n\nSupport Officer\n\nStandard Chartered Financial Ltd -\n\nMarch 2013 to July 2015\n\nfrom Mar 2013 to Jul 2015\n\nOperating System: Windows 7\nTools Used: SQL Database, Visual Basic, MS Excel (Macros), MS word, MS outlook.\n\nRoles &amp; Responsibilities:\n➢ Provide support to the manager and team in administering and maintaining records.\n➢ Handled all employee related matters including joining, exit and other issues of the team and\nmaintaining relevant Employee documents.\n➢ Managed all aspects pertaining to compliance and liaising with Area HR and the governance\nProcessing of vendor bills, and also coordinating for official reimbursement of team, assisting\nthem where applicable in following and monitoring Bank processes and procedures.\n➢ Availability and easy access of all relevant employee records and documentation.\n➢ Processed the daily Internal processing files of different products for approval.\n➢ Maintaining the MIS for ENR and sending daily reports to South Manager.\n➢ Sending and maintaining monthly review letter to agencies (MRL) on monthly basis.\n➢ Maintaining monthly ESAU Communication reports of employees.\n➢ Maintaining daily, weekly and monthly MIS and updating to Center Manager.\n\n\n\n➢ Doing internal audit check once in month.\n➢ Maintaining RMS management and Attendance.\n\nManager\n\n7 Inc -\n\nApril 2012 to March 2013\n\nWorked in [24]7 Inc as HR Executive from April 2012 to Mar 2013.\n\nOperating System: Windows XP\nTools Used: SQL Database, MS Excel, MS Word, MS outlook.\n\nRoles &amp; Responsibilities:\n➢ Handling of recruitment database.\n➢ Sourcing the profiles from the job portals.\n➢ Maintained recruitment team attendance, IT requests, stationary and bills.\n➢ Sending Daily offer MIS to Manager.\n➢ Short listing the resumes as per the requirements based on Skills, Projects and Educational\nbackground and Relocation aspects.\n➢ Flashing Daily Work Flow MIS of recruiter on daily basis to Manager.\n➢ Maintained data of both joined and dropouts.\n➢ Verifying hard copies and scanned copies of candidate documents before releasing offer letter.\n➢ Taken care of online tool update of candidate details, candidate test scores and program.\n➢ Taken care of joining employee formalities.\n➢ Taken care of offers to release on time on induction day.\n➢ Releasing the offer letters and uploading the candidate documents in portal in stipulated time\nand handing over the offered candidate files to operations for further process.\n\nJob Profile in Accenture -\n\nMarch 2011 to March 2012\n\nProject Name: EDF Energy and Npower\nOperating System: Windows 7\nTools Used: CRM, ISU, CIS, MS Excel, Citrix, MS outlook, MS Word.\n\nRoles &amp; Responsibilities:\n➢ Fulfillment of requests within agreed timelines.\n➢ Maintained &amp; sharing daily status updates of workflow of entire team to supervisor.\n➢ Assigning work to the team based on team member's skill set and ensuring that the workload\nis handled effectively and efficiently.\n➢ Arranging team meetings to share \"knowledge\" &amp; process related updates within the\nteam.\n➢ Taken care of complete process MIS reports.\n➢ Handling the Internal Quality Checks of the work completed by associates.\n➢ Updating the QC checklist and SAP reporting on a daily basis.\n➢ Helping the team members in resolving their quires.\n➢ Sending and maintained N1 Notification daily wise MIS of notifications closed and pending.\n\n\n\nINFOSYS as Process Associate\n\nJob Profile with Infosys -\n\nDecember 2009 to September 2010\n\nProject Name: DB Fresher's and Laterals\nOperating System: SQL Database, MS Excel, MS Word, MS outlook.\n\nRoles &amp; Responsibilities:\n➢ Creation of candidate profile.\n➢ Handled of recruitment database.\n➢ Updating the candidate details in the databases.\n➢ Maintaining current status of candidate in the database for our future comparing purpose.\n➢ Releasing offer letter in portal of candidate those who got selected in campus recruitment.\n➢ Maintained and handled offered candidate documents.\n➢ Conducting and co-ordinate the recruitment activity (In-house)\n➢ Maintained and updating candidates profile in Infy HR portal.\n➢ Collecting scanned copies of academic details from the candidate to verify.\n\nRewords and Recognition:\n• Given with Ramp Award in Infosys for more Productivity and Performance and Meeting Targets\nfor every month with in the stipulated time and with Good Quality.\n\nEDUCATION\n\nBSc\n\nV V Pura College of Science -  Bengaluru, Karnataka", "meta": {}, "annotation_approver": "admin", "labels": [[0, 12, "Name"], [13, 29, "Designation"], [61, 93, "Companies worked at"], [95, 115, "Location"], [138, 180, "Email Address"], [468, 490, "Years of Experience"], [600, 616, "Designation"], [647, 679, "Companies worked at"], [683, 703, "Years of Experience"], [740, 749, "Skills"], [762, 771, "Skills"], [773, 778, "Skills"], [780, 784, "Skills"], [786, 796, "Skills"], [798, 805, "Skills"], [1042, 1047, "Location"], [1119, 1133, "Skills"], [1312, 1329, "Skills"], [1597, 1612, "Skills"], [2032, 2063, "Skills"], [2322, 2329, "Designation"], [2384, 2438, "Email Address"], [2482, 2495, "Designation"], [2497, 2513, "Companies worked at"], [2517, 2543, "Years of Experience"], [2563, 2578, "Skills"], [2591, 2599, "Skills"], [2601, 2614, "Skills"], [2616, 2623, "Skills"], [2625, 2635, "Skills"], [2637, 2649, "Skills"], [2901, 2921, "Skills"], [3262, 3271, "Skills"], [3300, 3305, "Skills"], [3333, 3346, "Skills"], [3822, 3837, "Designation"], [3839, 3871, "Companies worked at"], [3875, 3898, "Years of Experience"], [3945, 3954, "Skills"], [3967, 3979, "Skills"], [3981, 3993, "Skills"], [3995, 4012, "Skills"], [4014, 4021, "Skills"], [4023, 4033, "Skills"], [4393, 4405, "Skills"], [4514, 4524, "Skills"], [4525, 4539, "Skills"], [4771, 4778, "Skills"], [5031, 5045, "Skills"], [5081, 5084, "Skills"], [5113, 5120, "Designation"], [5122, 5127, "Companies worked at"], [5131, 5163, "Years of Experience"], [5171, 5176, "Companies worked at"], [5180, 5192, "Designation"], [5198, 5220, "Years of Experience"], [5241, 5251, "Skills"], [5264, 5276, "Skills"], [5278, 5286, "Skills"], [5288, 5295, "Skills"], [5297, 5307, "Skills"], [5354, 5374, "Skills"], [5435, 5446, "Skills"], [5492, 5497, "Skills"], [6272, 6281, "Companies worked at"], [6285, 6309, "Years of Experience"], [6365, 6374, "Skills"], [6387, 6390, "Skills"], [6392, 6395, "Skills"], [6397, 6400, "Skills"], [6402, 6410, "Skills"], [6412, 6418, "Skills"], [6420, 6430, "Skills"], [6432, 6439, "Skills"], [6994, 7007, "Skills"], [7176, 7183, "Companies worked at"], [7187, 7204, "Designation"], [7223, 7230, "Companies worked at"], [7234, 7265, "Years of Experience"], [7325, 7337, "Skills"], [7339, 7347, "Skills"], [7349, 7356, "Skills"], [7358, 7368, "Skills"], [7447, 7467, "Skills"], [8165, 8168, "Degree"], [8174, 8197, "College Name"], [8201, 8221, "Location"]]}
{"id": 16, "text": "Hemil Bhavsar\nJr. ASP.NET Developer in True Vision Technology\n\nAhmedabad, Gujarat - Email me on Indeed: indeed.com/r/Hemil-Bhavsar/ce3a928d837ce9e1\n\nTo seek a job in the IT industry where I can utilize my skills and technical knowledge for the\norganization's and individual growth\n\nArea of interest:\n• Software Testing.\n• Testing application manually.\n• Great interest to learn new Technologies and Languages.\n• Enterprise Application Development\n• Web Application Development\n\nWORK EXPERIENCE\n\nJr. ASP.NET Developer in True Vision Technology\n\n-\n\nSeptember 2015 to May 2016\n\nSoftware Testing Training at Unicode Technology From March 2018 to May 2018\n\nJr. ASP.NET Developer (Intern) in Softech Infosys\n\n-\n\nMarch 2015 to August 2015\n\nEDUCATION\n\nMSC\n\nKSV University -  Gandhinagar, Gujarat\n\nApril 2015\n\nB.C.A\n\nGujarat University\n\nApril 2013\n\nSKILLS\n\nASP (1 year), ASP.NET (1 year), MS ASP (1 year), SOFTWARE TESTING (Less than 1 year),\nTESTING (Less than 1 year)\n\nhttps://www.indeed.com/r/Hemil-Bhavsar/ce3a928d837ce9e1?isid=rex-download&ikw=download-top&co=IN\n\n\nADDITIONAL INFORMATION\n\nTechnical skillset:\n• Manual Testing Techniques\n• Web Application testing\n• STLC (Software Testing Life Cycle) process\n• Verification and Validation process\n• Programming Language: Software Testing, ASP.Net with C#\n\n• Frameworks: Microsoft Visual Studio 2010, Frameworks 3.5, Frameworks (C#) 4.0, 4.5\n• Database tools: MySQL, SQL Server 2008, SQL Server 2010, MS-Access 2008\n• Report generation Tool: Crystal Report", "meta": {}, "annotation_approver": "admin", "labels": [[0, 13, "Name"], [14, 35, "Designation"], [39, 61, "Companies worked at"], [63, 81, "Location"], [104, 147, "Email Address"], [302, 318, "Skills"], [322, 350, "Skills"], [412, 446, "Skills"], [449, 476, "Skills"], [495, 516, "Designation"], [520, 542, "Companies worked at"], [547, 573, "Years of Experience"], [575, 600, "Designation"], [604, 622, "Companies worked at"], [628, 650, "Years of Experience"], [652, 673, "Designation"], [675, 681, "Designation"], [686, 701, "Companies worked at"], [706, 731, "Years of Experience"], [744, 747, "Degree"], [749, 763, "College Name"], [767, 787, "Location"], [789, 799, "Graduation Year"], [801, 806, "Degree"], [808, 826, "College Name"], [828, 838, "Graduation Year"], [848, 851, "Skills"], [862, 869, "Skills"], [880, 886, "Skills"], [897, 913, "Skills"], [934, 941, "Skills"], [962, 1017, "Email Address"], [1107, 1121, "Skills"], [1135, 1158, "Skills"], [1161, 1195, "Skills"], [1266, 1282, "Skills"], [1284, 1291, "Skills"], [1297, 1299, "Skills"], [1315, 1343, "Skills"], [1345, 1359, "Skills"], [1373, 1376, "Skills"], [1404, 1409, "Skills"], [1411, 1426, "Skills"], [1428, 1443, "Skills"], [1445, 1459, "Skills"], [1462, 1479, "Skills"], [1486, 1500, "Skills"]]}
{"id": 17, "text": "Siddhartha Chetri\n7 years of experience in IT Network Telecom and Consulting Sector,\nBusiness Analysis,Infrastructure.Project Management & Coordination,\nChange & Incident Management. Good level of understanding of the ITIL\nframework. Strong hold in Project Management.\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Siddhartha-Chetri/\nf6959d21c6b91bba\n\nA career with a progressive organization that will use my education and abilities in an executive\ncapacity, where that best matches my skills and experience to ensure a positive growth for the\norganization and self.\nSeeking challenging assignment in Customer Partner relation, Project/Change Management in\nTelecom and IT Sector.\n\nWORK EXPERIENCE\n\nPartner Sites Infosys, Concentrix, Merchants and Convergys\n\nCollabera Technologies- Client- Cisco Systems, Inc -\n\nJuly 2015 to Present\n\nJuly 2015 - Till Date)\n• Responsible for ensuring that all our Partner should get the Access Control List in right way\nand timely manner.\n• Reporting regular stats on Case management, Infrastructure/Bandwidth utilization.\n• Handling 4 Partner Sites Infosys, Concentrix, Merchants and Convergys.\n• Working with different teams in the organization for streamlined output.\n• SPOC for all the Partner Sites.\n• Working as a Business Analyst/Project Coordinator in maintaining and delivering record for the\nproject related work.\n• Driving project/task for a new request from the Partner Site.\n• To be an integral part of the project/task and help our Partners in achieving their goal.\n• Preparing process documents and Local Work Instructions (LWI) for the process.\n\nTechnology Associate / Incident Management\n\nMagna Infotech- Client- Sapient Consulting -\n\nJuly 2014 to July 2015\n\n• Responsible for all the Incidents and Server Monitoring.\n• Generating reports for project and service manager of all the Incidents and Problem tickets.\n• Analyzing of the price load report for our client in deployment stage.\n• Resolving of Incidents so that SLA won't be breached.\n• Running of queries on putty on daily basis for message reprocessing for our client.\n• Generating Deployment report for the overall item listed on our site for our Client.\n• Having exceptional interpersonal, verbal and written communication and motivational skills.\n• Hands on experience on Splunk for checking the logs of all the server.\n\nhttps://www.indeed.com/r/Siddhartha-Chetri/f6959d21c6b91bba?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Siddhartha-Chetri/f6959d21c6b91bba?isid=rex-download&ikw=download-top&co=IN\n\n\n• Ability to be results-oriented, a continuous learner, flexible, multi-tasking.\n• Known tools: Splunk, Remedy, CA Introscope Workstation, TIBCO, Oracle Database.\n\nProject / Service coordinator / Change Consultant\n\nCable & Wireless Worldwide/ Vodafone Global Shared Services Private Limited -\n\nNovember 2011 to June 2014\n\nNovember 2011- June 2014)\nProject / Service coordinator / Change Consultant\n• Manage &amp; co-ordinate PSTN Service for M&amp;S (Marks &amp; Spencer), Centrica\nprojects involving Design, Planning phase for all UK Clients.\n• Expertise in making Project Plans &amp; Requirement Analysis.\n• Expertise in report generation for different customers their services, circuits, affected servers.\n• Develop, implement, and maintain the Project management plan, including risk management,\ncommunication plan, and QA plan with the help of leads.\n• Define team member roles and expectations, and ensure timely feedback.\n• Having exceptional interpersonal, verbal and written communication and motivational skills.\n• Good knowledge of Business Analysis, Project Coordination process.\n• Ability to be results-oriented, a continuous learner, flexible, multi-tasking.\n• Training new colleagues who come into the team.\n• Hands On experience on Incident, Change Management.\n• Acknowledging, raising and worked on Incident tickets as well as CRQ request for our clients.\n\nTools Handled\n• Remedy 7.6 to update track all the Changes that are going to be affected for our major\ncustomers like Airtel, Tawasul, Fatsweb etc.\n• Preparing Business case with the Project Manager.\nPrevious Organization/Duration/Roles:\n\niTunes Advisor/ Subject Matter Expert\n\nAditya Birla Minacs -\n\nAugust 2010 to November 2011\n\nManage IT Infrastructure for the entire iTunes Process.\n• Determine the project approach, staffing, responsibilities, and schedule.\n• Define team member roles and expectations, and ensure timely feedback.\n• Ensure that Colleagues receive timely updates on incidents.\n• Group Policy Management and Implementing Security Policies.\n• Handling escalations.\n• Ensuring that team meets CSAT, Quality, Productivity and SLA assigned.\n• Measure and monitor progress at milestones and ensure delivery as per schedule.\n• Represent the project team at client meetings and provide project status details.\n\nResponsibilities\n• Worked as an iTunes Advisor troubleshooting customer problems related to Apple iTunes in\ntimely manner.\n• Generating reports on customer's request.\n\n\n\n• Ensuring that team meets CSAT, Quality, Productivity and SLA assigned.\n• Group Policy Management and Implementing Security Policies.\n• In a short period become an SME.\n• Shown excellence in a very short period.\n• Ensure that Colleagues receive timely updates on incidents\n• Handling escalations.\n\nEDUCATION\n\nBachelor of Computer Application in Computer Application\n\nAdministrative Management College Bangalore University -  Bengaluru, Karnataka\n\n2007 to 2010\n\nSKILLS\n\nREMEDY (3 years), CLARIFY (Less than 1 year), EXCEL (Less than 1 year), MS OFFICE (Less than\n1 year), OUTLOOK (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSkill Set:\n\n• Systems: Windows […] 10)\n• Software: MS Office (Word, Excel, Outlook, PowerPoint), Clarify, SRMS Remedy V6 (Advanced),\nRemedy V7.4 (Proficient), Remedy 7.5, MORI, GCD, LOCM, GTOMS, Webex, Smartsheet.\n\nStrengths:\n• Adaptability\n• Positive Attitude\n• Quick learner with ability to learn new concepts, methods and technologies\n• I believe in my ability to handle any situation\n• Good customer relation management", "meta": {}, "annotation_approver": "admin", "labels": [[0, 17, "Name"], [18, 39, "Years of Experience"], [43, 61, "Designation"], [66, 76, "Designation"], [85, 102, "Skills"], [103, 117, "Skills"], [118, 136, "Skills"], [162, 181, "Skills"], [218, 232, "Skills"], [249, 267, "Skills"], [270, 290, "Location"], [313, 361, "Email Address"], [613, 638, "Designation"], [640, 665, "Designation"], [724, 731, "Companies worked at"], [733, 743, "Companies worked at"], [745, 792, "Companies worked at"], [802, 820, "Companies worked at"], [824, 867, "Years of Experience"], [986, 995, "Skills"], [1013, 1028, "Skills"], [1030, 1066, "Skills"], [1095, 1139, "Companies worked at"], [1265, 1281, "Designation"], [1282, 1301, "Designation"], [1559, 1588, "Skills"], [1607, 1627, "Designation"], [1630, 1650, "Designation"], [1651, 1665, "Companies worked at"], [1675, 1693, "Companies worked at"], [1697, 1719, "Years of Experience"], [1747, 1756, "Skills"], [1761, 1778, "Skills"], [1793, 1800, "Skills"], [1844, 1873, "Skills"], [1981, 1984, "Skills"], [2198, 2211, "Skills"], [2232, 2245, "Skills"], [2250, 2262, "Skills"], [2296, 2302, "Skills"], [2345, 2404, "Email Address"], [2565, 2581, "Skills"], [2585, 2603, "Skills"], [2615, 2628, "Skills"], [2645, 2651, "Skills"], [2653, 2659, "Skills"], [2661, 2686, "Skills"], [2688, 2693, "Skills"], [2695, 2710, "Skills"], [2713, 2742, "Designation"], [2745, 2762, "Designation"], [2764, 2790, "Companies worked at"], [2792, 2839, "Companies worked at"], [2843, 2895, "Years of Experience"], [2897, 2926, "Designation"], [2929, 2946, "Designation"], [3022, 3030, "Skills"], [3050, 3056, "Skills"], [3058, 3066, "Skills"], [3081, 3083, "Location"], [3115, 3128, "Skills"], [3135, 3155, "Skills"], [3172, 3189, "Skills"], [3297, 3315, "Skills"], [3332, 3347, "Skills"], [3349, 3367, "Skills"], [3373, 3380, "Skills"], [3499, 3512, "Skills"], [3533, 3546, "Skills"], [3592, 3609, "Skills"], [3611, 3631, "Skills"], [3657, 3673, "Skills"], [3707, 3720, "Skills"], [3797, 3824, "Skills"], [3893, 3896, "Skills"], [3939, 3950, "Skills"], [4041, 4047, "Companies worked at"], [4049, 4056, "Companies worked at"], [4058, 4065, "Companies worked at"], [4083, 4096, "Skills"], [4162, 4176, "Designation"], [4178, 4199, "Designation"], [4201, 4220, "Companies worked at"], [4224, 4252, "Years of Experience"], [4261, 4278, "Skills"], [4294, 4308, "Skills"], [4523, 4546, "Skills"], [4564, 4581, "Skills"], [4634, 4638, "Skills"], [4640, 4647, "Skills"], [4649, 4661, "Skills"], [4666, 4669, "Skills"], [4879, 4893, "Designation"], [4939, 4951, "Skills"], [5044, 5048, "Skills"], [5050, 5057, "Skills"], [5059, 5071, "Skills"], [5076, 5088, "Skills"], [5092, 5115, "Skills"], [5133, 5150, "Skills"], [5327, 5359, "Degree"], [5363, 5418, "College Name"], [5419, 5439, "College Name"], [5443, 5463, "Location"], [5465, 5477, "Years of Experience"], [5487, 5493, "Skills"], [5505, 5512, "Skills"], [5533, 5538, "Skills"], [5559, 5568, "Skills"], [5589, 5596, "Skills"], [5664, 5671, "Skills"], [5692, 5736, "Skills"], [5738, 5745, "Skills"], [5747, 5761, "Skills"], [5774, 5785, "Skills"], [5800, 5810, "Skills"], [5812, 5816, "Skills"], [5818, 5821, "Skills"], [5823, 5827, "Skills"], [5829, 5834, "Skills"], [5836, 5841, "Skills"], [5843, 5853, "Skills"], [6036, 6064, "Skills"]]}
{"id": 18, "text": "Pratik Vaidya\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Pratik-Vaidya/e88324548608d0bc\n\nLooking ahead to work in a challenging environment, that nurture my skills and help me strive for\nexcellence. I wish to join an organization, which would recognize my working abilities and help\nme with a progressive career in the field of Computer Science.\n\nWilling to relocate to: Mumbai, Maharashtra - Pune, Maharashtra - Banglore, Karnataka\n\nWORK EXPERIENCE\n\nIntern\n\nAllscripts -\n\nFebruary 2018 to Present\n\nClient Services\n\nPilot Batch Under the aegis of Infosys Campus Connect Program\n\nOnline Foundation Program -  Aurangabad, Maharashtra -\n\n2016 to 2016\n\nLoophole Ethical Hacking Workshop organized by Kyrion Digital Securities (P) Ltd.\n● Participated in 1st IETE Student Forum West Zone Congress - 2016.\n● Conducted 13 days workshop for students at Mahanubhav Ashram on Computer Basics.\n● Three Days Workshop on Emotional Intelligence organized by Maharashtra Institute of\nTechnology, Aurangabad.\n\nStrength:\n● Adaptable to changing requirements.\n● Flexible, Hard working with Leadership quality.\n● An open source enthusiast with Technology friendly nature.\n● Enthusiastic Person with excellent communication skills and strong motivation to succeed.\nPersonal details:\n\nDate of Birth : 27/12/1994\nGender : Male\nMarital status : Unmarried\nLanguages Known : English,Hindi,Marathi\nHobbies : Listening to music\nAddress : Plot no: 142, Auronoday Colony, Near Datta Mandir, Beed by Pass Road,\nAurangabad 431010.\n\nDeclaration:\nI hereby declare that the information furnished above is true to the best of my knowledge.\n\nPlace: Aurangabad.\n\nhttps://www.indeed.com/r/Pratik-Vaidya/e88324548608d0bc?isid=rex-download&ikw=download-top&co=IN\n\n\nSignature\n\nEDUCATION\n\nBtech\n\nMaharashtra Institute of Technology\n\nMay 1990 to 2016\n\nH.S.C in A'Bad\n\nDeogiri College\n\n2013\n\nS.S.C in A'Bad\n\nChate School\n\n2011\n\nSKILLS\n\nLinux\n\nCERTIFICATIONS/LICENSES\n\nRHCSA", "meta": {}, "annotation_approver": "admin", "labels": [[0, 13, "Name"], [14, 31, "Location"], [54, 97, "Email Address"], [338, 354, "Skills"], [381, 387, "Location"], [389, 400, "Location"], [403, 407, "Location"], [409, 420, "Location"], [423, 431, "Location"], [433, 442, "Location"], [461, 467, "Designation"], [469, 479, "Companies worked at"], [483, 507, "Years of Experience"], [618, 641, "Location"], [645, 657, "Email Address"], [668, 692, "Degree"], [706, 740, "Companies worked at"], [875, 890, "Skills"], [917, 939, "Skills"], [953, 988, "College Name"], [990, 1000, "Location"], [1081, 1091, "Skills"], [1106, 1117, "Skills"], [1199, 1212, "Skills"], [1359, 1366, "Skills"], [1367, 1372, "Skills"], [1373, 1380, "Skills"], [1622, 1632, "Location"], [1635, 1690, "Email Address"], [1756, 1761, "Degree"], [1763, 1798, "College Name"], [1800, 1816, "Graduation Year"], [1818, 1823, "Degree"], [1827, 1849, "College Name"], [1851, 1855, "Graduation Year"], [1857, 1862, "Degree"], [1866, 1885, "College Name"], [1887, 1891, "Graduation Year"], [1901, 1906, "Skills"], [1933, 1938, "Skills"]]}
{"id": 19, "text": "Ramakrishna Rao\nDevOps Consultant - Tech Mahindra Ltd\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Ramakrishna-\nRao/0b57f5f9d35b9e5c\n\nAn accomplished IT Professional with 15.2 years of Competent technical expertise in solution\ndesign, architecture, system analysis, components design, development and testing of client\nserver, Intranet/ Internet, N-tier systems in Microsoft, Java and middleware projects at\nDevelopment, Support, E2E and Transition, Transformation, migration Projects at Telecom (BSS\nand OSS), Networking, Finance, Content Management, Healthcare, Banking domains. Extensive\nexperience in Object-Oriented design and development, agile, DevOps, Cloud architecture,\nmiddleware, enterprise application integrations.\na) Offshore Professional Summary\n• Expertise in process, tools maturity assessment and provides the implementation /\ntransformation roadmaps.\n\n• Extensive experience in the design and implementation of Continuous Integration, Continuous\nDelivery, Continuous Testing, Continuous Deployment Pipelines and DevOps processes, best\npractices for Agile projects.\n\n• Involved in Agile implementation using JIRA, Rally, HPE ALM/Octane across various customer\nas per SAFe/Scrum methodologies.\n\n• Implementation experience in Continuous Integration/Delivery (CI/CD) pipeline using CA Clarity,\nCA Rally, JIRA, HPE ALM/Octane Jenkins, Maven, Git, Bitbukcet, Github, XebiaLabs XL Release, CA\nRelease Automation/Automic for Code Build, repository and deployment and Automation.\n\n• Proven expertise on cloud based solutions using PaaS platforms - Openshift with kubernetes,\nMS Azure (ARM templates), AWS services (EC2, ELB, IAM, S3, EFS, and EBS, CI-CD services - Code\nCommit, Build, Code Deploy, OpsWorks)\n\n• Good understating and experience in Docker, Ansible, Chef, Puppet configuration tools\n\n• Good experience in Java, Microsoft, CRM, Java Script, shell, PowerShell, Groovy, Python script\n\n• Involved in development, support activities in projects using tools such as Java/ J2EE, MS\nTechnologies (VB, ASP, VStudio.Net, VB.Net, ASP.Net, C#, SqlServer, Team Foundation Server\n(TFS), Sharepoint), Oracle, SQL Server, automated configuration and build, integration SCM tools\n(Git, GitHub, SVN, Bitbucket, Crucible, Maven, SonarQube, NANT, FxCop, NUNIT, CCNet, )\n\n• Good knowledge and integration experiences in testing tools on HPE ALM/QC/Octane, ETL\nValidator, CA SV, CA TDM and Selenium with CI-CD pipeline.\n\n• Good understanding and experienced in ITSM tools - BMC- Remedy, Servicenow integrations\n\n• Good experience in CI-CD implementation with IBM stack, HP stack, Atlassian stack using IBM\nCLM, RTC, uRelease and uDeploy, and Atlassian Products (JIRA, JIRA Agile, Crowd, Bitbucket/Stash,\nCrucible, Confluence) in DevOps solutions implementation.\n\nhttps://www.indeed.com/r/Ramakrishna-Rao/0b57f5f9d35b9e5c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Ramakrishna-Rao/0b57f5f9d35b9e5c?isid=rex-download&ikw=download-top&co=IN\n\n\n• Have proven leadership and strong interpersonal and communication skills in dealing with\npeople with diverse backgrounds.\n\nb) Onsite Professional Summary\n• Good experience in DevOps E2E solution design in planning, source code management, build\nand deployment automations using JIRA, Jenkins, maven, git/Bitbucket, XLRelease and XLDeploy,\nAnsible for VHA, Scotiabank, Volkswagen, NetApp, Optus, Cenveo accounts.\n• Developed POCs and successfully onboarded 100+ projects into CD platform as part of the\nDevOps transformation.\n• Involved in Transitions/Takeover of varies complex applications for BT, ATT, AIRBUS clients and\nmade the applications as BAU.\n• Good at client communication, customer management and giving presentations to clients.\n• Good at managing the deliveries at onshore and liaising with offshore team.\n• Involved in Application Technical Support, Requirement gathering and giving solution designs\nto clients.\n• Involved in ASG (Application Support Group) activities at onshore.\n• Good experience in conducting tools, DevOps process trainings as part of DevOps enablement\nprograms.\n\nWORK EXPERIENCE\n\nDevOps Consultant -\n\nTech Mahindra Ltd -\n\nMarch 2018 to Present\n\n15 Years ]\nBand-Sub band/ Designation U4 / DevOps Consultant -\n\nTech Mahindra Ltd -\n\nApril 2005 to Present\n\nExperience\n\nSr. Software Engineer Consultant (Client of Infosys Technologies)\n\nData Comp Services -\n\nAugust 2004 to April 2005\n\nDuration AUGUST/2004--APRIL/2005\n\nProgrammer Analyst\n\nInfoland Technologies -\n\nFebruary 2003 to August 2004\n\n\n\nEDUCATION\n\nMaster of Science in Information Technology and Management\n\nPeriyar University\n\nJune 2001\n\nP.G.D.C.A in Computer Applications\n\nNagarjuna University\n\nJune 1998\n\nB.Sc. in M.P.C\n\nAndhra University\n\nJune 1997\n\nSKILLS\n\n.Net (Less than 1 year), .Net 4.5 (Less than 1 year), Amazon Elastic Block Storage (Less than 1\nyear), Amazon Elastic Compute Cloud (Less than 1 year), ASP (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nIII. Skills\n\nPrimary Skill category\nJava/J2EE, .Net 4.5, MS.Net Tech.[ASP.Net, C#, VB.Net, Web services, WCF, EF, SQLServer,\nVS2012], Oracle 11g, DevOps process &amp;implementation, Agile planning, JIRA, Bitbucket,\nCrucible, Confluence, Crowd and Open Source Tools (Jenkins, Git, GitHub, SVN, Maven,\nSonarQube), MS TFS2013 with InRelease, XLRelease, XLDeploy, AWS services - ( EC2, ELB,\nIAM, S3, EFS, and EBS, CI-CD services - Code Commit, Build, Code Deploy, OpsWorks), Ansible\nplaybooks, Servicenow\n\nSecondary Skill category\nVB, ASP, Config Tools (Subversion, NANT, FxCop, NUNIT, CCNet), Design Tools\n(BorelandToGether2007, Visio2010), Project Management (Microsoft Office Project 2010)\n\nDomain Skill Telecom (CRM, BSS, OSS), Content Management and Healthcare, Finance and\nBanking\n\nIV. Trainings/Certifications\n\n➢ Introduction to Agile Methodology: conducted by Tech Mahindra Ltd.: Nov 2006\n➢ PMP Training: conducted by Tech Mahindra Ltd.: Aug 2011.\n➢ ITIL Training: conducted by Tech Mahindra Ltd.: Jun 2013.\n➢ LEAN SIX SIGMA YELLOW BELT training: conducted by Tech Mahindra Ltd.: April 2014.\n➢ Scrum Maser Certified (SMC) Jul 2015 from ScrumStudy.", "meta": {}, "annotation_approver": "admin", "labels": [[0, 15, "Name"], [16, 33, "Designation"], [36, 53, "Companies worked at"], [55, 75, "Location"], [98, 144, "Email Address"], [183, 193, "Years of Experience"], [230, 245, "Skills"], [247, 259, "Skills"], [261, 276, "Skills"], [278, 295, "Skills"], [297, 308, "Skills"], [313, 320, "Skills"], [324, 337, "Skills"], [339, 357, "Skills"], [359, 373, "Skills"], [377, 386, "Skills"], [388, 392, "Skills"], [420, 431, "Skills"], [433, 440, "Skills"], [442, 445, "Skills"], [450, 460, "Skills"], [477, 496, "Skills"], [523, 533, "Skills"], [535, 542, "Skills"], [544, 562, "Skills"], [564, 574, "Skills"], [576, 591, "Skills"], [617, 639, "Skills"], [657, 662, "Skills"], [664, 670, "Skills"], [672, 690, "Skills"], [692, 702, "Skills"], [704, 726, "Skills"], [943, 965, "Skills"], [967, 986, "Skills"], [988, 1006, "Skills"], [1008, 1029, "Skills"], [1044, 1050, "Skills"], [1081, 1086, "Skills"], [1112, 1117, "Skills"], [1139, 1143, "Skills"], [1145, 1150, "Skills"], [1152, 1166, "Skills"], [1198, 1222, "Skills"], [1256, 1295, "Skills"], [1311, 1321, "Skills"], [1323, 1331, "Skills"], [1333, 1337, "Skills"], [1339, 1361, "Skills"], [1363, 1368, "Skills"], [1370, 1373, "Skills"], [1375, 1384, "Skills"], [1386, 1392, "Skills"], [1394, 1414, "Skills"], [1416, 1460, "Skills"], [1492, 1502, "Skills"], [1527, 1548, "Skills"], [1555, 1569, "Skills"], [1572, 1581, "Skills"], [1587, 1597, "Skills"], [1599, 1607, "Skills"], [1625, 1637, "Skills"], [1639, 1730, "Skills"], [1771, 1777, "Skills"], [1779, 1786, "Skills"], [1788, 1792, "Skills"], [1794, 1800, "Skills"], [1843, 1847, "Skills"], [1849, 1858, "Skills"], [1860, 1863, "Skills"], [1865, 1876, "Skills"], [1878, 1883, "Skills"], [1885, 1895, "Skills"], [1897, 1903, "Skills"], [1905, 1911, "Skills"], [1998, 2008, "Skills"], [2010, 2025, "Skills"], [2027, 2121, "Skills"], [2124, 2130, "Skills"], [2132, 2142, "Skills"], [2202, 2284, "Skills"], [2354, 2371, "Skills"], [2373, 2386, "Skills"], [2388, 2393, "Skills"], [2395, 2401, "Skills"], [2406, 2414, "Skills"], [2477, 2487, "Skills"], [2490, 2493, "Skills"], [2495, 2501, "Skills"], [2678, 2682, "Skills"], [2684, 2694, "Skills"], [2696, 2701, "Skills"], [2703, 2712, "Skills"], [2730, 2740, "Skills"], [2745, 2751, "Skills"], [2779, 2836, "Email Address"], [2993, 3003, "Skills"], [3015, 3028, "Skills"], [3033, 3046, "Skills"], [3156, 3166, "Skills"], [3259, 3263, "Skills"], [3265, 3272, "Skills"], [3274, 3279, "Skills"], [3281, 3294, "Skills"], [3296, 3305, "Skills"], [3310, 3318, "Skills"], [3320, 3327, "Skills"], [3332, 3335, "Companies worked at"], [3337, 3347, "Companies worked at"], [3349, 3359, "Companies worked at"], [3361, 3367, "Companies worked at"], [3369, 3374, "Companies worked at"], [3376, 3382, "Companies worked at"], [3483, 3489, "Skills"], [3922, 3953, "Skills"], [4098, 4115, "Designation"], [4119, 4136, "Companies worked at"], [4140, 4161, "Years of Experience"], [4206, 4223, "Designation"], [4227, 4244, "Companies worked at"], [4248, 4269, "Years of Experience"], [4283, 4316, "Designation"], [4327, 4347, "Companies worked at"], [4350, 4368, "Companies worked at"], [4372, 4397, "Years of Experience"], [4408, 4431, "Years of Experience"], [4433, 4451, "Designation"], [4453, 4474, "Companies worked at"], [4478, 4506, "Years of Experience"], [4521, 4579, "Degree"], [4581, 4599, "College Name"], [4601, 4610, "Graduation Year"], [4612, 4646, "Degree"], [4648, 4668, "College Name"], [4670, 4679, "Graduation Year"], [4681, 4695, "Degree"], [4697, 4714, "College Name"], [4716, 4725, "Graduation Year"], [4735, 4739, "Skills"], [4789, 4817, "Skills"], [4838, 4866, "Skills"], [4887, 4890, "Skills"], [4971, 4980, "Skills"], [4983, 4990, "Skills"], [4760, 4768, "Skills"], [4992, 5067, "Skills"], [5069, 5079, "Skills"], [5081, 5087, "Skills"], [5101, 5115, "Skills"], [5117, 5131, "Skills"], [5133, 5137, "Skills"], [5139, 5148, "Skills"], [5150, 5158, "Skills"], [5160, 5170, "Skills"], [5201, 5208, "Skills"], [5210, 5213, "Skills"], [5215, 5221, "Skills"], [5223, 5226, "Skills"], [5228, 5233, "Skills"], [5235, 5244, "Skills"], [5247, 5257, "Skills"], [5263, 5272, "Skills"], [5274, 5283, "Skills"], [5285, 5293, "Skills"], [5295, 5307, "Skills"], [5312, 5403, "Skills"], [5406, 5423, "Skills"], [5462, 5464, "Skills"], [5466, 5469, "Skills"], [5485, 5522, "Skills"], [5539, 5570, "Skills"], [5573, 5591, "Skills"], [5593, 5622, "Skills"], [5647, 5660, "Skills"], [5663, 5681, "Skills"], [5686, 5696, "Skills"], [5698, 5705, "Skills"], [5710, 5717, "Skills"], [5751, 5784, "Degree"], [5799, 5817, "College Name"], [5819, 5827, "Graduation Year"], [5830, 5833, "Skills"], [5857, 5875, "College Name"], [5877, 5885, "Graduation Year"], [5889, 5893, "Skills"], [5937, 5945, "Graduation Year"], [5917, 5935, "College Name"], [5949, 5975, "Degree"], [5999, 6017, "College Name"], [6019, 6029, "Graduation Year"], [6033, 6060, "Degree"], [6061, 6069, "Graduation Year"]]}
{"id": 20, "text": "Keshav Dhawale\n3 TCS Security guard Access Control Room 1 year - TCS Hinjewadi Mail\nRoom Boy & HK Store\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Keshav-Dhawale/f5ce584c13e7368d\n\nWORK EXPERIENCE\n\n3 TCS Security guard Access Control Room 1 year\n\nTCS Hinjewadi Mail Room Boy & HK Store -\n\nOctober 2017 to Present\n\n4 INFOSYS Hinjewadi Guest House & Store Executive\n\nIBM India Pvt Ltd -\n\nOctober 2015 to October 2017\n\n2 year\nCurrent Work in IBM India Pvt Ltd Hinjewadi As Mail Room Executive\n\nCareer Objective\nTo develop a successful career in a service industry and seeking a position to utilize my skills and\nabilities in the organization offers professional growth.\n\nPERSNOL SKILLS\n• Good grasping power and ability to implement learned knowledge in desired areas\n• Confidence in my abilities and skill sets &amp; Ability to perform well under pressure\n\nEDUCATION\n\nSSC\n\nBoard (university) -  Aurangabad, Maharashtra\n\nMarch 2009\n\nLINKS\n\nhttp://AT.POST\n\nhttps://www.indeed.com/r/Keshav-Dhawale/f5ce584c13e7368d?isid=rex-download&ikw=download-top&co=IN\nhttp://AT.POST", "meta": {}, "annotation_approver": "admin", "labels": [[0, 14, "Name"], [105, 122, "Location"], [145, 189, "Email Address"], [214, 228, "Designation"], [299, 322, "Years of Experience"], [326, 333, "Companies worked at"], [375, 392, "Companies worked at"], [396, 424, "Years of Experience"], [449, 466, "Companies worked at"], [467, 476, "Location"], [480, 499, "Designation"], [876, 879, "Degree"], [903, 926, "Location"], [928, 938, "Graduation Year"], [971, 1019, "Years of Experience"]]}
{"id": 21, "text": "Praveen Bhaskar\nProgram Manager (Software Delivery) - Office Beacon\n\nVadodara, Gujarat - Email me on Indeed: indeed.com/r/Praveen-Bhaskar/c9868b2e3dd70df1\n\nWORK EXPERIENCE\n\nProgram Manager (Software Delivery)\n\nOffice Beacon -  Vadodara, Gujarat -\n\nJanuary 2016 to Present\n\n• Global delivery &amp; Program management.\n• Determination of a viable project mix that meets the target of the organization.\n• Regular monitoring of the planning and execution of the optimal\nselected projects.\n• Evaluating the performance of portfolio and various ways for improving it.\n• Comparing the project execution capacity of the organisation.\n• Providing recommendations to decision makers at every level of the process management.\n• Coordinating cross-project activities.\n• Lead and evaluate project managers and other staff.\n• Develop and control deadlines, budgets and activities.\n• Apply change, risk and resource management.\n• Assume responsibility for the program's people and vendors.\n• Prepare reports for program directors.\n• Managing the PMO, Development and Support Functions.\n\nProject Manager, Sr Consultant/ Infosys Limited\n\nTekskills India Pvt Ltd & Infosys Limited -  Pune, Maharashtra -\n\nDecember 2015 to October 2016\n\nManaging small to large scale projects as per the client\nrequirements.\n• Co-coordinating with the onshore and offshore team for the successful completion of projects.\n• Interacting with the customers and coordinating with internal stake\nholders.\n• Planning, scoping and designing the entire project and ensure the project is delivered as per\nthe business needs.\n• Preparing and maintaining project, stage and exception plans as required.\nResume - Project Manager\n\n• Managing project risks, including the development of contingency\nplans.\n• Applying change control and configuration management processes.\n• Maintaining an awareness of potential interdependencies with other projects and their impact\n• Identifying and obtaining support and advice required for the management, planning and\ncontrol of the project.\n\nhttps://www.indeed.com/r/Praveen-Bhaskar/c9868b2e3dd70df1?isid=rex-download&ikw=download-top&co=IN\n\n\n• Preparing any follow-on action recommendations.\n\nSupervisor (Associate Manager)\n\nRed Hat Inc -  Pune, Maharashtra -\n\nAugust 2010 to October 2015\n\nManaging a team of 20+ direct reportees that includes Technical\nSupport Engineers, Team Leads and SMEs.\n• Managing Global Support Delivery Operations from India for recruiting, training and career\ndevelopment.\n• Handling customer escalations and ensuring positive customer\nengagement. Planning and executing the organizational level\nstrategies to achieve the goals.\n• Build and oversee the day-to-day management technical team,\nconsisting of junior to senior technical support engineers\n• Manage customer escalations with highly technical support requests from enterprise customers\nvia the telephone and the web\n• Maintain a high level of customer satisfaction\n• Keep the team's technical and non-technical skills current by promoting and guiding ongoing\nprofessional and personal\ndevelopment\n• Implement strategic change for knowledge management,\ncustomer-centric support, and issue problem solving\n• Coordinate improvement programs for global support process and procedures as part of the\nfront-line support management\nteam across\n\nTeam Lead, Global Support Delivery | Red Hat Inc. PUNE, INDIA\n\n• Acted as Team Lead for support delivery team of 40+\nassociates. o Working closely with the management for day\nResume - Project Manager\n\nto day operations. o Planning and executing team level\nstrategies.\n• Prepare coverage plans to meet the business requirements.\n• Coaching/Training/Mentoring associates and providing\nfeedback to management.\n• Planning and implementation of department level projects.\n\nSoftware Engineer\n\nPOORNAM INFOVISION -  Kochi, Kerala -\n\nOctober 2009 to August 2010\n\nRemote administration of Web-hosting\nservers. Manage the IT infrastructure for dedicated clients.\n• Experience over control panels such as cPanel and Plesk.\n\n\n\n• Setting up the IT infrastructure as per customer requirements\n\nLinux Systems & Network Consultant\n\nL OGIC SOLUTIONS PVT LTD -  Thiruvananthapuram, Kerala -\n\nJanuary 2007 to September 2009\n\nTo configure and control the Linux Systems, which also includes the Head\nserver of the Institution and maintain the Network infrastructure.\n\nEDUCATION\n\nHigher Secondary Certificate\n\nKerala State\n\n2003\n\nCertificate\n\nKerala State\n\n2001\n\nSKILLS\n\nMENTORING (5 years), TRAINING (5 years), PROGRAM MANAGER (2 years), PROJECT\nMANAGEMENT (2 years), CHANGE MANAGEMENT (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSkills Project Management, People Management, Stake Holder\nManagement, Vendor Management, Project Planning Scheduling and\nImplementation, Risk Analysis, Cross-functional supervision, Team\nbuilding and mentoring, Change Management, Escalation Handling,\nChange Management, Quality Assurance, Training &amp; Presentations.\nResume - Program Manager", "meta": {}, "annotation_approver": "admin", "labels": [[0, 15, "Name"], [16, 31, "Designation"], [33, 50, "Designation"], [54, 67, "Companies worked at"], [69, 86, "Location"], [109, 154, "Email Address"], [173, 188, "Designation"], [190, 207, "Designation"], [210, 223, "Companies worked at"], [227, 244, "Location"], [248, 271, "Years of Experience"], [297, 315, "Skills"], [428, 436, "Skills"], [824, 841, "Skills"], [843, 850, "Skills"], [985, 992, "Skills"], [1031, 1034, "Skills"], [1072, 1087, "Designation"], [1089, 1102, "Designation"], [1104, 1119, "Companies worked at"], [1119, 1144, "Companies worked at"], [1147, 1162, "Companies worked at"], [1166, 1183, "Location"], [1187, 1216, "Years of Experience"], [1275, 1287, "Skills"], [1665, 1680, "Designation"], [1693, 1706, "Skills"], [1737, 1754, "Skills"], [2031, 2088, "Email Address"], [2183, 2193, "Designation"], [2195, 2212, "Designation"], [2215, 2226, "Companies worked at"], [2230, 2247, "Location"], [2251, 2278, "Years of Experience"], [2613, 2623, "Skills"], [3314, 3323, "Designation"], [3351, 3363, "Companies worked at"], [3364, 3375, "Location"], [3388, 3397, "Designation"], [3644, 3652, "Skills"], [3653, 3661, "Skills"], [3662, 3671, "Skills"], [3782, 3799, "Designation"], [3801, 3819, "Companies worked at"], [3823, 3836, "Location"], [3840, 3867, "Years of Experience"], [3894, 3913, "Skills"], [3926, 3943, "Skills"], [4046, 4063, "Skills"], [4094, 4129, "Designation"], [4130, 4154, "Companies worked at"], [4158, 4184, "Location"], [4188, 4218, "Companies worked at"], [4249, 4262, "Skills"], [4336, 4358, "Skills"], [4402, 4414, "College Name"], [4416, 4420, "Graduation Year"], [4449, 4453, "Graduation Year"], [4435, 4447, "College Name"], [4484, 4492, "Skills"], [4504, 4519, "Skills"], [4531, 4549, "Skills"], [4561, 4578, "Skills"], [4630, 4648, "Skills"], [4650, 4667, "Skills"], [4669, 4692, "Skills"], [4694, 4711, "Skills"], [4713, 4740, "Skills"], [4761, 4774, "Skills"], [4806, 4819, "Skills"], [4824, 4833, "Skills"], [4835, 4852, "Skills"], [4854, 4873, "Skills"], [4875, 4892, "Skills"], [4894, 4911, "Skills"], [4952, 4967, "Designation"]]}
{"id": 22, "text": "Gunjan Nayyar\nHoshiarpur, Punjab - Email me on Indeed: indeed.com/r/Gunjan-Nayyar/a5819ca6733a0f41\n\nTo keep on learning new skills, To serve for the company and keep on growing in necessary field.\n\nWORK EXPERIENCE\n\nPresently completed 6 months internship at INFOSYS\n\nINFOSYS\n\nMember of Discipline Committee in ALGORYTHM\n\nRed Cross Society -\n\n2015 to 2016\n\nin College on National Blood Donation day on 17thNov.2016.\n• Member of Discipline Committee in ALGORYTHM 2015, 2016.\n• Study on Humanoids: It is something that has an appearance resembling a human being in\n2016\n• Participated in Arm Wrestling organized by Hostel Committee in 2016\n• Participated in Carrom organized by Hostel Committee in 2016\n• Participated in Bollywood quiz organized by Hostel Committee in 2016\n• Participated in Tambola organized by Hostel Committee in 2016\n• Participated in Arm Wrestling organized by Hostel Committee in 2016\n• Participated in Antakshari organized by Hostel Committee in 2016\n• Organised CATECHISM (Chemistry Quiz) on April 01, 2015\n\nMember of Decoration Committee in ALGORYTHM\n\n-\n\n2014 to 2014\n\nEDUCATION\n\nB. E. in CSE\n\nChitkara University\n\n2014 to 2018\n\nClass XII\n\nTriple M Public School\n\n2013 to 2014\n\nUniversity or Board\n\nhttps://www.indeed.com/r/Gunjan-Nayyar/a5819ca6733a0f41?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": "admin", "labels": [[0, 13, "Name"], [14, 32, "Location"], [55, 98, "Email Address"], [244, 254, "Designation"], [258, 265, "Companies worked at"], [1103, 1115, "Degree"], [1117, 1136, "College Name"], [1138, 1150, "Graduation Year"], [1222, 1277, "Email Address"]]}
{"id": 23, "text": "Rupesh Reddy\nTechnology Consultant - EIT Services India Private Limited\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Rupesh-Reddy/5402dfa9c92fb7bf\n\n• Possessing 6.3 years of IT experience in the domain of Data warehousing/Business Intelligence.\n• Remarkable experience in SAP Business objects XIR2, XI 3.1, BI4.0 and BI4.1.\n• Proficient in administration of Business Objects and Cognos repositories, services and servers\nfor high availability of environments.\n• Basic overview of Tableau Administration.\n• Extensively worked on CMC, Universe Designer, Desktop Intelligence, Infoview/BI Launch pad\nand Web Intelligence.\n• Experience in SAPBI 4.0/4.1 CMC, UDT, IDT, BI Launch Pad, WebI Rich Client, UMT, LCM.\n• Experience in Migration from XIR2, XI3.1 to SAPBI 4.0 by using Upgrade Management Tool (UMT)\n• Experience in converting .UNV to .UNX by using Information Design Tool (IDT)\n• Using the Report Conversion Tool converted Desk I reports to Web I reports in BOXI 3.1 and\nBI4.0.\n• Experience in upgrading the environment from BI4.0 to BI4.1\n• Web I Reports &amp; Universes were deployed from Development to Production Environment\nby using Life Cycle Management Console (LCM)\n• Experience in setting up a Disaster Recovery Environment of Business Objects XIR3.1 and BI4.0\n• Excellent knowledge on Cognos connection, Security and Configuration.\n• Profile based Publications were created and scheduled reports to user BI Inbox.\n• File &amp; Scheduled based Events were created and scheduled the reports.\n• Familiarity with Root Cause Analysis, Troubleshooting and Debugging of issues in Production\nEnvironment.\n• Extensively worked on Installation and Configuration of Business Objects.\n• Experience in maintaining the environments.\n• Remarkable experience in applying patches/fixes for SAP Business Objects XIR3.1, BI4.0 and\nBI4.1.\n• Proficient knowledge on Architecture and functioning of SAP Business Objects.\n• Possess excellent communication, decision-making, problem solving, analytical and\ninterpersonal skills with result oriented dedication towards goals. Capable of working as a Team\nMember or Individually with minimum supervision.\n• An enthusiastic and project-oriented team player with solid communication, leadership skills\nand the ability to develop creative solutions for challenging client needs.\n• Flexible and versatile to adapt to any new environment with a strong desire to keep pace with\nlatest technologies.\n\nThis project is upgrading Business Intelligence 4.0 environment to latest available version of\nBusiness Intelligence 4.1.\nRoles and Responsibilities:\n• Lead the team of 8 resources in upgrading Business Intelligence 4.0 to 4.1\n• Coordinating with Wintel, Data protection and Database team for the backup.\n• Involving vendor in case of any installation issues.\n• Configuring the environment, Scaling of the services after the installation.\n• Validation the environment, testing the work flow of the reports and the client tools before\nreleasing the environment to the customers.\n• Fixing the issues found during end user testing, by involving vendor if needed.\n\nhttps://www.indeed.com/r/Rupesh-Reddy/5402dfa9c92fb7bf?isid=rex-download&ikw=download-top&co=IN\n\n\nPAN Card: **********\n\nIssuing Office: Hyderabad\nIssue Date: 31-Jan 2011\nExpiry Date: 30-Jan 2021\n\nWORK EXPERIENCE\n\nTechnology Consultant\n\nEIT Services India Private Limited -\n\nSeptember 2015 to Present\n\nWorked as Senior Systems Engineer for Infosys Limited from February 2012 to September 2015\n\nDXC Technology, Business objects Administrator\n\nEIT Services India Private Limited -  Bengaluru, Karnataka -\n\nSeptember 2015 to Present\n\nProject #1: State Bank of Mauritius\nDomain: Banking\nOverview:\nThe business objective of this project is to have a unified reporting platform for accessing and\nanalysing the data from any business systems as per the business requirement. It includes\nDevelopment, Test, Stage and Production based Environment supporting 300+ users.\nEnvironment:\n• Four Environments - Development, Test, Stage and Production\n• Clustered Environment\n• Windows servers\n• OS: Microsoft Windows 2008 Server\n\nRole &amp; Responsibilities:\n• As a Team Lead currently handling 5 resources and helping them on delivery issues.\n• Co-ordination with team on universe/report related issues and performance issues.\n• Preparing weekly and monthly progress report.\n• Upgraded the non-production environments to higher patch versions.\n• Performed installation, configuration and scaling of the production environment.\n• Ensure HA of the production environment. Participate and lead the DC-DR exercise.\n• 2 months of client facing experience, which included the production go-lives. Handled the issues\nraised post production go-live, ensured the stability of environment.\n• Create and manage users, groups and folders. Ensure appropriate privileges are assigned to\nthe different users.\n• Suggested and implemented publication mechanism for report spooling (file location based\ntriggering), which eliminated the manual report scheduling activity.\n• Technical Proficiency: Understanding project related technical features, raising issues,\nproactively identifying the issues.\n\n\n\n• Project Completeness: Timeliness of deliverable, Timely Escalation / Status updates / reports,\nStatus updates of calls / requests, Problem solving, Ability to work out solutions independently.\n• Independently perform complex troubleshooting, root-cause analysis, solution development.\n• Interact with end users or clients to resolve issues with Business objects.\n• Coordinate with SAP support for patches and bug fixes.\n• To manage nodes, servers and services.\n• Backup and restore of Business Objects.\n• Worked with Wintel and DBA team related to OS and Database related issues.\n• Monitoring the Jobs and sever space availability and CPU Process.\n• Deployment/Migrations of folders and objects from DEV to TEST, TEST to UAT and UAT to PROD\nenvironment.\n• Recommend and implement of automation for known and recurring activities.\n• Preparing standard operating procedures for related Business Object issues.\n• Handled the basic report development and maintenance activities.\n• ALM Owner: ALM (Tool for tracking and monitoring the defects raised for the UAT report related\nissues)\n• HPSM Lead: HPSM (Tool for tracking and monitoring the defects raised for the PROD report\nrelated issues)\n\nInfosys Limited, Business objects Administrator\n\nBusiness objects -  Bengaluru, Karnataka -\n\nFebruary 2012 to September 2015\n\nProject #1: Pfizer Pharmaceuticals\nDomain: Life sciences\n\nEDUCATION\n\nB.Tech in Electronics and Communication Engineering\n\nSri Venkateswara College of Engineering and Technology Affiliated -  Anantapur, Andhra\nPradesh\n\n2007 to 2011\n\nADDITIONAL INFORMATION\n\nSoft Skills:\n• Cross Cultural Sensitivity\n• Written Communication Skills\n• Articulation Skills\n• Effective Meetings", "meta": {}, "annotation_approver": "admin", "labels": [[0, 12, "Name"], [13, 34, "Designation"], [37, 71, "Companies worked at"], [73, 93, "Location"], [116, 158, "Email Address"], [173, 199, "Years of Experience"], [217, 233, "Skills"], [234, 255, "Skills"], [284, 287, "Skills"], [305, 309, "Skills"], [311, 317, "Skills"], [319, 324, "Skills"], [329, 334, "Skills"], [391, 410, "Skills"], [492, 499, "Skills"], [540, 543, "Skills"], [545, 562, "Skills"], [564, 584, "Skills"], [586, 594, "Skills"], [595, 629, "Skills"], [647, 664, "Skills"], [666, 669, "Skills"], [671, 674, "Skills"], [676, 689, "Skills"], [691, 707, "Skills"], [709, 712, "Skills"], [714, 717, "Skills"], [750, 774, "Skills"], [784, 813, "Skills"], [842, 845, "Skills"], [850, 853, "Skills"], [864, 892, "Skills"], [1153, 1188, "Skills"], [1268, 1274, "Skills"], [1279, 1284, "Skills"], [1310, 1316, "Skills"], [1798, 1801, "Skills"], [1902, 1922, "Skills"], [1944, 1957, "Skills"], [1959, 1974, "Skills"], [1976, 1991, "Skills"], [1993, 2003, "Skills"], [2008, 2021, "Skills"], [3104, 3158, "Email Address"], [3240, 3249, "Location"], [3317, 3338, "Designation"], [3340, 3374, "Companies worked at"], [3378, 3403, "Years of Experience"], [3415, 3438, "Designation"], [3443, 3458, "Companies worked at"], [3464, 3495, "Years of Experience"], [3497, 3511, "Companies worked at"], [3513, 3543, "Designation"], [3545, 3579, "Companies worked at"], [3583, 3603, "Location"], [3607, 3632, "Years of Experience"], [3883, 3894, "Skills"], [3896, 3900, "Skills"], [3902, 3907, "Skills"], [3912, 3922, "Skills"], [3999, 4010, "Skills"], [4012, 4016, "Skills"], [4018, 4023, "Skills"], [4028, 4038, "Skills"], [4041, 4062, "Skills"], [4065, 4080, "Skills"], [4087, 4116, "Skills"], [4584, 4589, "Skills"], [5244, 5268, "Skills"], [5306, 5321, "Skills"], [5520, 5536, "Skills"], [5556, 5559, "Skills"], [6268, 6272, "Skills"], [6501, 6523, "Companies worked at"], [6532, 6545, "Skills"], [6558, 6609, "Degree"], [6611, 6676, "College Name"], [6680, 6705, "Location"], [6707, 6719, "Graduation Year"], [6797, 6810, "Skills"]]}
{"id": 24, "text": "Puneeth R\nEscalation Specialist - HiPower Support Centre\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Puneeth-R/bc332220e733906d\n\nTo be an asset to my organization by utilizing the recent improving trends and make optimum\nuse of the available resources for the success of the company. More importantly, working\nharmoniously in a team to bring forth the best from self and everyone.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nEscalation Specialist\n\nHiPower Support Centre -\n\nJune 2015 to Present\n\nClient: Machine Zone (Supporting 3 Games)\nRoles &amp; Responsibilities:\n• Escalating tickets to clients, Creating Inquiries regarding the process to receive update\nfrom Clients, Providing feedback to Agents regarding Clients Escalations and internal invalid\nEscalations.\nTeam handling ( Mentor)\n• Tracking performances of the team members in terms of Production, Quality and CSAT.\n• Monitoring Queue, Allocating Agents to different queues, Preparing Reports, Process\nimprovements initiatives.\n• Taking operations training sessions on Gameplay, DSAT reviews, Quality, FTR and Tickets\nhandling.\n• Maintaining &amp; ensuring stringent adherence to quality standards, identifying gaps and\nopportunities by live assistance. Handled OJT and Tenured Team.\n• Handling Supervisor tickets, Lead compensations and Audits.\n\nSales Associate in Convergys\n\nAT&T -\n\nJanuary 2014 to May 2015\n\nClient: AT&amp;T\nRoles &amp; Responsibilities:\n• Addressing Customers Billing Inquiries and upselling over Chat.\n• Making Calls to customers in case of lead generations.\n\nProcess Executive - Operations in Infosys\n\nCisco -\n\nAugust 2012 to November 2013\n\nClient: Cisco\n\nhttps://www.indeed.com/r/Puneeth-R/bc332220e733906d?isid=rex-download&ikw=download-top&co=IN\n\n\nContd.,\nRoles &amp; Responsibilities:\n• Assisting Clients over Email and Phone regarding Billing discrepancies and Order Management.\n• CRM tool experience in Supporting and Reports generation.\n\nCustomer Relations Officer in HGS\n\n-\n\nDecember 2011 to July 2012\n\nClient: MTS\n• Making calls to customers to review customer satisfaction and upselling.\n• Handled Escalations from Customers.\n• Taking Calls to address Customers Inquiries.\n\nEDUCATION\n\nB.E\n\nPES College of Engineering -  Mandya, Karnataka", "meta": {}, "annotation_approver": "admin", "labels": [[0, 9, "Name"], [10, 31, "Designation"], [34, 56, "Companies worked at"], [58, 78, "Location"], [101, 140, "Email Address"], [443, 464, "Designation"], [466, 488, "Companies worked at"], [492, 512, "Years of Experience"], [522, 534, "Companies worked at"], [801, 807, "Skills"], [785, 798, "Skills"], [889, 893, "Skills"], [954, 971, "Skills"], [973, 993, "Skills"], [1058, 1062, "Skills"], [1089, 1105, "Skills"], [1326, 1341, "Designation"], [1345, 1360, "Companies worked at"], [1364, 1388, "Years of Experience"], [1460, 1467, "Skills"], [1561, 1578, "Designation"], [1595, 1609, "Companies worked at"], [1613, 1649, "Years of Experience"], [1651, 1656, "Companies worked at"], [1658, 1709, "Email Address"], [1793, 1810, "Skills"], [1888, 1891, "Skills"], [1947, 1973, "Designation"], [1977, 1980, "Companies worked at"], [1985, 2011, "Years of Experience"], [2021, 2024, "Companies worked at"], [2140, 2152, "Skills"], [2197, 2200, "Degree"], [2202, 2228, "College Name"], [2232, 2249, "Location"]]}
{"id": 25, "text": "Kandrapu Reddy\nSenior Travel Operations (Domestic, International & Leisure) -\nInternational Travel House Limited\n\nVisakhapatnam, Andhra Pradesh - Email me on Indeed: indeed.com/r/Kandrapu-\nReddy/69a289269ce9e1d1\n\nTo contribute for the success and expansion of an organization\nensuring personal and professional growth by continuously increasing my skills,\nobtaining wide exposure at all levels where job demands efforts which include\nlearning new concepts, Handling pressure situations and communicating ideas clearly\nand effectively.\n\nPERSONAL SUMMARY:\n• Professionalism with reliable commitment towards work.\n• Dedicated to personnel and organization's development\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSenior Travel Operations (Domestic, International & Leisure)\n\nInternational Travel House Limited -\n\nSeptember 2016 to Present\n\nInternational Travel House Limited an ITC Associated co.; An ISO 9001\nTravel Company also a Network partner of Global Star Travel Management,\noffers a full range of travel management services in both corporate &amp; leisure travel.\n\nThis role provides the key responsibilities of:\n\n• Handling Domestic and International GDS bookings &amp; Getting Confirmations from airlines\nfor lower class fares.\n• Preparing Itineraries as per client requirement.\n• Having a continuous track over Mail, by giving swift response for client\nqueries.\n• Issuing Flight tickets, Making leisure packages &amp; Hotel reservations.\n• Delivering Visa guidance for corporate queries.\n• Key focus on the Client maintenance with satisfaction, result design and industry development.\n• Preparing and submitting bills to the corporate clients on Fortnight basis\nevery month.\n• Perform multi-tasking by looking into Car rental operation (Implant), By\nAllocating chauffer with cab as per the standards of company.\n\nTravel consultant\n\nRIYA TOURS AND TRAVELS PRIVATE .LIMITED -  Visakhapatnam, Andhra Pradesh -\n\nhttps://www.indeed.com/r/Kandrapu-Reddy/69a289269ce9e1d1?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Kandrapu-Reddy/69a289269ce9e1d1?isid=rex-download&ikw=download-top&co=IN\n\n\nFebruary 2016 to July 2016\n\nRiya Travels &amp; Tours has an extensive network of more than 50+ branch offices across India\nand 10 internationally by delivering travel services corporate &amp; Leisure.\n\nThis role provides the key responsibilities of:\n\n• Handling Domestic and International GDS bookings (Amadeus &amp; Galileo).\n• Preparing Itineraries as per client requirement.\n• Having a continuous track over Mail, by giving swift response for client\nqueries.\n• Issuing Flight tickets as per the client need.\n• Managing the most important corporates by me, to ensure the utmost service\nrendered to our esteemed clients on time and with precision.\n• Assistance regarding visa, tourism packages, hotels &amp; airport transfers.\n\nImplant Infosys Travel consultant\n\nHead quarters of BHARATH INTERNATIONAL TRAVELS PVT.LTD -  Mysore, Karnataka -\n\nMarch 2015 to January 2016\n\nBharath International Travels (BIT), an ISO 9001:2008 Quality Assurance\nCompliant, is a leading IATA accredited travel company in Karnataka that\noffers the entire gamut of travel related services to business and leisure\ntravelers.\n\nThis role provides the key responsibilities of:\n• Handling flight bookings &amp; Getting Confirmations from airlines.\n• Looking after travel requirements of Infosys Employees by giving swift\nresponse to their queries through mail.\n• Maintaining daily sales report by ensuring the given targets to be achieved.\n• Keys focus on providing quality service to the implant employees.\n\n• Trained in AKBAR TRAVELS OF INDIA PVT.LTD, Visakhapatnam since 01st Aug, 2014\nto 31st Feb, 2015.\n\nAkbar Travels of India is the largest Travel Agent in India in terms of IATA\napproved Branches. Akbar Travels is approved by International Air Transport\nAssociation and is the member of prestigious trade bodies like Travel Agents\nAssociation of India, Travel Agents Federation of India and IATA Agents Association of India.\n\n• Trained on Amadeus &amp; Galileo airline software's.\n\nEDUCATION\n\nB.B.A in Airport & Customer Care Management\n\nNRI Junior College -  Coimbatore, Tamil Nadu\n\n2016\n\n\n\nS.S.C\n\nK.D.P.M HIGH school -  Visakhapatnam, Andhra Pradesh\n\n2012\n\nSKILLS\n\nAMADEUS (3 years), CLIENT MANAGEMENT (3 years), EXCEL (3 years), MS WORD (3 years),\nWORD (3 years)\n\nADDITIONAL INFORMATION\n\nAREAS OF INTEREST:\n• Airline GDS Software's AMADEUS &amp; GALILEO.\n• Client Management.\n• Airline and Travel Management.\n\nCOMPUTER SKILLS:\n• Proficiency in using GDS Airline Software's Amadeus &amp; Galileo.\n• Proficiency in Microsoft [MS Word, Excel, Power Point] and working\nknowledge of the Internet.\n\nSTRENGHTS:\n• Self Confidence\n• Flexibility &amp; Adaptability\n• Quick Learner\n• Always try to overcome Weakness", "meta": {}, "annotation_approver": "admin", "labels": [[0, 14, "Name"], [15, 39, "Designation"], [78, 112, "Companies worked at"], [114, 143, "Location"], [166, 211, "Email Address"], [716, 740, "Designation"], [778, 812, "Companies worked at"], [816, 841, "Years of Experience"], [843, 877, "Companies worked at"], [1378, 1400, "Skills"], [1402, 1425, "Skills"], [1432, 1450, "Skills"], [1465, 1478, "Skills"], [1699, 1712, "Skills"], [1827, 1844, "Designation"], [1846, 1885, "Companies worked at"], [1889, 1918, "Location"], [1922, 1978, "Email Address"], [2120, 2146, "Years of Experience"], [2409, 2421, "Skills"], [2423, 2430, "Skills"], [2437, 2444, "Skills"], [2449, 2470, "Skills"], [2584, 2606, "Skills"], [2865, 2882, "Designation"], [2901, 2938, "Companies worked at"], [2942, 2959, "Location"], [2963, 2989, "Years of Experience"], [2991, 3026, "Companies worked at"], [3282, 3297, "Skills"], [3615, 3645, "Companies worked at"], [3647, 3660, "Location"], [3667, 3699, "Years of Experience"], [3702, 3724, "Companies worked at"], [4040, 4047, "Skills"], [4054, 4061, "Skills"], [4094, 4137, "Degree"], [4139, 4157, "College Name"], [4161, 4183, "Location"], [4185, 4189, "Graduation Year"], [4193, 4198, "Degree"], [4223, 4252, "Location"], [4254, 4258, "Graduation Year"], [4268, 4275, "Skills"], [4287, 4304, "Skills"], [4316, 4321, "Skills"], [4333, 4340, "Skills"], [4352, 4356, "Skills"], [4436, 4443, "Skills"], [4450, 4457, "Skills"], [4461, 4478, "Skills"], [4482, 4511, "Skills"], [4554, 4574, "Skills"], [4577, 4584, "Skills"], [4591, 4598, "Skills"], [4617, 4656, "Skills"]]}
{"id": 26, "text": "Vineeth Vijayan\n\"Store Executive\" - Orange City Hospital & Research Institute\n\nNagpur, Maharashtra, Maharashtra - Email me on Indeed: indeed.com/r/Vineeth-Vijayan/\nee84e7ea0695181f\n\nI have over 2 years of experience working as a \"Store Executive\" in a reputed big organisation\nbased in India. I am a hard worker and a very good learner. I aspire to grow in my career by\njoining a good organisation.\n\nI am good at work and people management. I possess good computer knowledge. I have\nunderwent training in courses like MS Excel Level 1, 2 &amp; 3. MS Word &amp; Powerpoint. I\nhave experience working in ERP. I am a certified ISO 27001: 2013 Internal Auditor.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\n\"Store Executive\"\n\nOrange City Hospital & Research Institute -  Nagpur, Maharashtra -\n\nMarch 2016 to Present\n\nJob Profile: -\n1. Keep records to maintain inventory control, cost containment and to assure proper stock levels.\n2. Rotate stock and arrange for disposal of surpluses.\n3. Plan and perform work that involves ordering, receiving, inspecting, returning, unloading,\nshelving, packing, labeling and maintaining a perpetual inventory of items.\n4. Coordinate freight handling, equipment moving and minor repairs.\n5. Perform peripheral site-specific duties as required and instructed by supervisors.\n6. Monitoring and updating stock details etc. in Mednet store software. Use ABC, VED &amp; FSN\nStock Analysis methods when required.\n7. Report any stock discrepancy to supervisor for appropriate action.\n\n\"HR Officer \"\n\nGulf Warehousing Company Qatar - Agility Group -\n\nJanuary 2012 to July 2015\n\na multinational company catering Customers in the field of Warehousing, Freight Forwarding,\nRecords Management, Courier and International Relocation Services.\n\nJob Profile: -\n1. Deliver new hires orientation program to enable new hires to adjust themselves to new working\nenvironment and facilitating newcomers joining formalities.\n2. Design and develop Training Calendar and budget for all type of training programs, for the\nyear ahead for all designations within the company, according to the needs of the business and\nTraining Need Analysis survey results.\n\nhttps://www.indeed.com/r/Vineeth-Vijayan/ee84e7ea0695181f?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Vineeth-Vijayan/ee84e7ea0695181f?isid=rex-download&ikw=download-top&co=IN\n\n\n3. Sourcing and coordinating with external vendors for training that are requested by line\nmanagers/departments.\n4. Maintaining employee training records and updating it on a regular basis.\n5. Assist Supervisors in developing and implementing changes in HR policies and procedures.\n6. Coordinate in conducting employee engagement programs.\n7. Support departments in complying with ISO Quality Management requirements and facilitate\nvarious HR related welfare activities.\n\n\"Training Coordinator\"\n\nM-Squared Software & Services - Kerala -  Kerala, IN -\n\nJuly 2010 to December 2011\n\na US based company with headquarters in Nevada, Las Vegas that deals with security gadgets,\nmedical transcription and software development.\n\nJob Profile: -\n1. Perform regular office administration duties that includes: Office infrastructure management,\nhousekeeping supervision, responding and sending e-mails to head office and clients, monitoring\ntime-in &amp; time-out of employees, managing employee leave status and office cash\nmanagement.\n2. Handle walk-in enquires, attend telephone calls and conduct course induction sessions for new\njoiners.\n3. Train the students in work related processes.\n4. Anchoring and conducting course related seminars in well-established educational institutions.\n5. Manage training materials that are stipulated within the company regulations.\n6. Formulating training programs and sending it to training manager and to all concerned\ndepartments for approval.\n7. Preparing tools for training.\n8. Organize the training that includes: distribution of training materials, monitor trainees in\ntraining and support trainers in training.\n9. Record training plans and training programs.\n10. Maintain employees and students database.\n11. Record results of evaluation and training.\n12. Archive records of training, including course content, the training, number of students, results,\nand feedback.\n13. Conduct team huddles/counseling sessions for trainees when required.\n\n\"Manager\"\n\nNair IT Pvt. Ltd -  Nagpur, Maharashtra -\n\nSeptember 2008 to July 2010\n\nClient: Accentia Oak Technologies.\n\nJob Profile: -\n1. To manage the day-to-day planning, operation and problem-solving of the team to meet the\nrequired service level components, quality and productivity targets of the client.\n\n\n\n2. Delivery of team statistics, quality and productivity targets &amp; indicators, taking note any\ndowntime during the day and report it to the management level superiors.\n3. Operational Management: Managing the floor, adherence to schedule and monitoring of\nattendance. Submits daily report to the management level superiors.\n4. Monitoring the portal periodically, prioritizing jobs according to TAT, and assigning jobs to\ncorrect work pool.\n5. Coaching and relaying feedback to MTs/Editors.\n6. To motivate the team to achieve the client targets. Update the team on the status of work at\nthe start and end of each shift.\n7. Compiling reports on team's performance and client feedback.\n\n\"Process Executive\" in Infosys BPO Limited, Bangalore\n\nCISCO -  Bengaluru, Karnataka -\n\nJanuary 2006 to July 2008\n\nJob Profile: -\n1. To handle end to end order placement process of Cisco.\n2. Book orders for customers as per Cisco's booking policies.\n3. Customer advocacy.\n4. Check and avail special discounts, services, licensing, upgrades etc., to the customers.\n5. Receive customer feedback on products, services etc., and resolve them if any.\n6. To work along with logistic team to ensure that the products reach precise destinations safely.\n7. Provide round the clock 24/7 customer service to the client.\n\n\"Sr. Medical Transcriptionist\"\n\nDTS Information Systems Pvt. Ltd -  Bengaluru, Karnataka -\n\nDecember 2004 to January 2006\n\nin DTS Information Systems Pvt. Ltd, Bangalore (A wholly owned subsidiary of DTS America\nInc., Tennessee, USA) from Dec 2004 - Jan 2006. Client: Vanderbilt University Medical Center,\nNashville, Tennessee.\n\nJob Profile: -\n1. To transcribe documents pertaining to patient's summary of illness, diseases and diagnostic\nprocedures etc. to avail medical insurance claims as per American transcription guidelines.\n2. To ensure that finished end document meets the clients TAT and Quality requirements.\n3. To counsel MT's regarding their errors etc and if needed conduct team huddles.\nComputer Exposure\n\nOperating System: Windows XP\nMicrosoft Office Package: MS Excel, MS Word, MS Access and Power Point.\nExperience in working on Oracle.\n\n❖ Completed MS Excel Level 1, 2 &amp; 3 Courses from New Horizons Computer Training Center,\nQatar.\n\n\n\nEDUCATION\n\nB.Sc. in Microbiology in Microbiology\n\nS.N.G College, Bharathiar University\n\nApril 2004\n\nSKILLS\n\nStorekeeping / Inventory Management / Purchase (2 years), People & Time Management (2\nyears)\n\nADDITIONAL INFORMATION\n\nCompetencies\n\n❖ Effective communications skills, excellent interpersonal skills, good time management skills,\nresults-oriented individual and a very good team player.\n❖ Sincere, hardworking and willing to take up challenging assignments.\n\nOther Qualifications\n\n❖ ISO 27001: 2013 Internal Auditor Certification from Coms Vantage Consultancy, Qatar in\nOctober 2014\n❖ ISO 27001: 2013 Awareness Certification from Coms Vantage Consultancy, Qatar in September\n2014\n❖ Carriage of Dangerous Goods by Road Certification in 2012 from IRU Academy in tie up with\nMowasalat - State of Qatar owned Transport Company.\n❖ First class in Hindi Parichaya Examination conducted by Dakshina Bharat Hindi Prachar Sabha.\n❖ Second class in Prathamic Examination conducted by Dakshina Bharat Hindi Prachar Sabha.", "meta": {}, "annotation_approver": "admin", "labels": [[0, 15, "Name"], [17, 32, "Designation"], [36, 77, "Companies worked at"], [79, 111, "Location"], [134, 180, "Email Address"], [230, 245, "Designation"], [286, 291, "Location"], [518, 526, "Skills"], [547, 554, "Skills"], [561, 571, "Skills"], [602, 605, "Skills"], [614, 656, "Degree"], [708, 723, "Designation"], [726, 767, "Companies worked at"], [771, 790, "Location"], [794, 815, "Years of Experience"], [860, 877, "Skills"], [879, 895, "Skills"], [934, 946, "Skills"], [1170, 1186, "Skills"], [1188, 1204, "Skills"], [1359, 1380, "Skills"], [1515, 1525, "Designation"], [1529, 1559, "Companies worked at"], [1562, 1575, "Companies worked at"], [1579, 1604, "Years of Experience"], [1960, 1968, "Skills"], [2127, 2149, "Skills"], [2167, 2224, "Email Address"], [2621, 2647, "Skills"], [2677, 2696, "Skills"], [2748, 2770, "Skills"], [2840, 2860, "Designation"], [2863, 2892, "Companies worked at"], [2895, 2915, "Location"], [2919, 2946, "Years of Experience"], [2987, 3004, "Location"], [3173, 3198, "Skills"], [3200, 3224, "Skills"], [3368, 3390, "Skills"], [3501, 3506, "Skills"], [4158, 4165, "Skills"], [4345, 4352, "Designation"], [4355, 4371, "Companies worked at"], [4375, 4394, "Location"], [4398, 4425, "Years of Experience"], [4435, 4460, "Companies worked at"], [4495, 4514, "Skills"], [4530, 4545, "Skills"], [4671, 4686, "Skills"], [4831, 4853, "Skills"], [5102, 5110, "Skills"], [5344, 5361, "Designation"], [5291, 5298, "Skills"], [5366, 5385, "Companies worked at"], [5398, 5403, "Companies worked at"], [5407, 5427, "Location"], [5431, 5456, "Years of Experience"], [5497, 5512, "Skills"], [5596, 5613, "Skills"], [5920, 5936, "Skills"], [5954, 5982, "Designation"], [5985, 6017, "Companies worked at"], [6021, 6041, "Location"], [6045, 6074, "Years of Experience"], [6079, 6111, "Companies worked at"], [6113, 6122, "Location"], [6153, 6169, "Companies worked at"], [6171, 6185, "Location"], [6192, 6211, "Years of Experience"], [6259, 6279, "Location"], [6303, 6323, "Degree"], [6921, 6942, "Degree"], [6691, 6701, "Skills"], [6702, 6772, "Skills"], [6799, 6805, "Skills"], [6820, 6828, "Skills"], [6946, 6973, "College Name"], [6975, 6996, "College Name"], [6998, 7008, "Graduation Year"], [7018, 7030, "Skills"], [7033, 7053, "Skills"], [7056, 7064, "Skills"], [7076, 7100, "Skills"], [7162, 7176, "Skills"], [7195, 7208, "Skills"], [7399, 7445, "Degree"], [7451, 7475, "College Name"], [7477, 7482, "Location"], [7486, 7498, "Graduation Year"], [7501, 7540, "Degree"], [7546, 7570, "College Name"], [7572, 7577, "Location"], [7581, 7595, "Graduation Year"]]}
{"id": 27, "text": "Rahul Tayade\nGlobal Production Support Lead, - Infosys Ltd (Technology Lead) - HSBC\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Rahul-Tayade/ce40c3731cb69763\n\n• Total 12+ years of IT experience in the analysis, design, development, Testing support,\nimplementation, CAT support and management of full life cycle applications, project coordination,\nmanaging development and support projects.\n• More than 7 years of experience on Project Leading support and maintenance project including\nenhancements of the application.\n• Over 4 years of Project Management experience on support/maintenance projects.\n• Managed multiple applications with team more than 16 members.\n• Involved in PM activities like dealing with customer, identifying new business, get new business,\naccordingly raising quotes, and get PO approved from customer, track the work and bill customer\nbased on Resource Utilization, Workforce management, manage project costing by providing\ncost-effective solutions etc.\n• Involve in SIX Sigma and Lean initiative to remove the unwanted NVA (non-value add) improve\non costing and performance side\n• Was deputed to client side UK-IPSWICH to deal with client and handle issues, achieve the client\nconfidence by creating road map for improvement and improving performance and during that\ntime customer satisfaction rating was increased to 4.9 from 4.4 out of 5.\n• Handled responsibilities as the single point of contact for various projects, transitioning and\noffshore coordinator.\n• As a ASG Project lead involved in ASG activities like resource management, work allocation,\nshift management, handling escalations, SLA performance and dashboard reporting, ITES Metric\nreports, Highlight reports, coordinating and performing deployments, QMG audits, PMR activities\netc.\n• Practiced ITIL V3 processes during my tenure on application support projects which includes\nService Transition, Service Operations (Incident management, Change Management, Problem\nManagement) and Continual Service Improvements\n• Handled effort estimation using Function Point (IFPUG Guidelines), cost estimation and planning\nvarious projects.\n• Prepared performance improvement plan on activities related to application performance.\n• Managed and Delivered VDC Migrations, Database Migration projects.\n• Capacity planning, work load and work force planning.\n• Interact with the business analysts &amp; application leads to come up with technical designs\nbased on the functional designs.\n• Worked extensively on Oracle PL/SQL, UNIX, Scripting and have good interpersonal skills.\n• Involved in Service transition and successfully completed all quality gates\n• Documented and Managed DR activities successfully\n• Involved in BCP planning and execution.\n• Team mentoring and help team when needed\n• Involved in escalations and resolve the issue smoothly by involving business stakeholders and\nteam effectively.\n\nWORK EXPERIENCE\n\nGlobal Production Support Lead, - Infosys Ltd (Technology Lead)\n\nhttps://www.indeed.com/r/Rahul-Tayade/ce40c3731cb69763?isid=rex-download&ikw=download-top&co=IN\n\n\nHSBC -  Pune, Maharashtra -\n\nJune 2016 to Present\n\nCurrently working in Global Standards IT under the CDD (Customer Due Diligence) program which\nis responsible for tackling financial crime by implementing new tools to better understand their\ncustomers.\n\nI lead a global 24*7 production support team for a number of Tier 1 KYC (Know Your Client)\nFCR (Financial Crime Risk) applications and provide hands on application support to a user base\nacross 35 countries in 42 markets, for HSBC and its subsidiaries First Direct and Marks &amp;\nSpencer Bank.\n\nTasks:\n• Mentoring team on Technical as well as Process front\n• Implementing new processes\n• Implementing a global 24*7 Support Model\n• Supporting the rollout of new customer onboarding tool across 35 countries\n• Supporting weekly release cycles\n\nResponsibilities:\n• Offshore Leadership/People Management\n• Service Delivery Management\n• Stakeholder Management\n• Relationship Management\n• Service Recovery Management\n• Production Support\n• Incident Management\n• Problem Management\n• Change Management\n• Release Management &amp; Support during releases\n• Involve in Service Improvement Process for Faster and Smooth delivery\n• Resolving technical issues relating to application from offshore\n• Training &amp; Mentoring new resources for the Projects\n• Documentation and manage Knowledge bank for team reference\n• Documentation and execution of DR activities\n\nTechnology Lead\n\nHSBC -  Pune, Maharashtra -\n\nOctober 2015 to July 2016\n\nInvolved in Transition of application\n• Preparation of in Knowledge Transfer plan from HSBC to Infosys,\n• Managing KT schedules and made sure that it working as per plan\n• Managing variance and made sure that portion left should get discussed as per updated plans.\n• Mentoring new team on technical and process front\n• Helping team where laps in KT plan or miscommunications\n• Preparation and scheduling of Reverse KT plans and smooth execution of it\n\n\n\n• Managing stake holders and business and give them confidence for moving support to new\nteam\n• Made sure that this plan passed from all quality gates in order to start work from new team\n• Documentation of all these Knowledge and Process\n\nProject Lead\n\nBritish Telecom UK -  Pune, Maharashtra -\n\nAugust 2010 to July 2015\n\nFPQ system is used for entry and reporting of quality data. It is national database for all quality\nchecks. It is used to record field performance quality scores of resources and based on the entry\nof it evaluate the quality scores. It is also used for score sheet and contractor management. Base\non the score sheets auditors performs the transaction audits. FPQ also provides the transactional\nand statistical reports.\nV21 is crucial interface between the OSS and the network. It is based on Metasolv's component\nOMS.\nOMS: A Centralized Order Management system that allows changing processes and adding\ninterfaces automatically\n\n• Off Shore Project Manager for various integration projects for the client.\n• Requirement Understanding for future development and enhancement by interacting with\nClient, E2E Solution Designers and other stake holders.\n• Prepared application performance improvement plan such CSIP (Continuous Service\nImprovement Plan), Get-well Plan.\n• Providing estimation of effort and timescale for all the project deliveries\n• Managing components deliveries impacted by various Releases\n• Perform project planning, scheduling, monitoring, and reporting activities.\n• Interface with the client team to update them on the issues, risks and status of the offshore\ndelivery.\n• Part of Application/Detail Design team to design high level and low level of the integration work.\n• Ensure system is delivered within planned cost, timescale and resource budgets.\n• Perform Release Management\n• Effort Estimation, Cost estimation, Allocate work to the team, track and raise the queries and\nresolve issues related to deliveries.\n• Design, Development, Testing for various projects.\n• Change Requests/Maintenance Release\n• Resolving technical issues relating to application from offshore\n• Training &amp; Mentoring new resources for the Projects.\n• Oracle Database Migration, Application Migration to VDC environment.\n• Completed VDC Migration (DaaS, CaaS, MaaS) for various application\n\nAlong-side the responsibilities mentioned above I was involved in various initiatives by client for\nimprovement in team efficiency like Six Sigma and Lean management that saved lots of efforts\nand in turns monetary benefits to customer.\n\nWe also developed solutions to various issues which reduced incidents count that also reduce\nturnaround time for resolution of the issue that engineer faced. For which we have received Blue\nRibbon Award.\n\n\n\nTeam Lead\n\nBritish Telecomm UK -  UK -\n\nJune 2006 to July 2010\n\nEWMP-Tacticals contains Robotic systems formerly with the FastTrack solutions Team within BT.\nRobotic systems were developed with the purpose of reducing the manual/ repetitive work done\nby the Field engineers. This work is automated by Robots and User interface interacting with\nvarious systems like CSS, Work Manager and other components. It is bunch of 16 applications\n\n• Off Shore Project Manager for various integration projects for the client.\n• Requirement Understanding for future development and enhancement by interacting with\nClient, E2E Solution Designers and other stake holders.\n• Prepared application performance improvement plan such CSIP (Continuous Service\nImprovement Plan), GetWell Plan.\n• Providing estimation of effort and timescale for all the project deliveries\n• Managing components deliveries impacted by various Releases\n• Perform project planning, scheduling, monitoring, and reporting activities.\n• Interface with the client team to update them on the issues, risks and status of the offshore\ndelivery.\n• Part of Application/Detail Design team to design high level and low level of the integration work.\n• Ensure system is delivered within planned cost, timescale and resource budgets.\n• Perform Release Management\n• Effort Estimation, Cost estimation, Allocate work to the team, track and raise the queries and\nresolve issues related to deliveries.\n• Design, Development, Testing for various projects.\n• Change Requests/Maintenance Release\n• Resolving technical issues relating to application from offshore\n• Training &amp; Mentoring new resources for the Projects.\n• Oracle Database Migration, Application Migration to VDC environment.\n• Completed VDC Migration (DaaS, CaaS, MaaS) for various application before EOSL (End of life\ncycle of the application)\n\nFor one of the legacy application (FastQ) , we have provided L3 support, application basically\ndeveloped Oracle HTTPS based and Tomcat as Service to handle it.\nDuring change in business level we have done development and changes for this application for\nwhich Customer gives me Start Team player award as this was legacy application and before this\nchange there was no change done for more than 6 years.\n\nSystem Analyst\n\nGE Countrywide, Lending and Repay Management -\n\nMay 2005 to April 2006\n\nLeasing functionality as per the standard defined by financial institution. It includes all the\nfunctionality from Creation of Group, Company, and Creation of Trenches, Disbursing Loan\nAgreement No. Capitalizing LAN.\nIn Repayment Management System will take care of Installment receipt, Write-off cases, Charges\nfor delinquent cases, Foreclosure of LAN etc\n\n\n\nThis application was developed from Scratch so involved in every part of application life cycle\nfrom requirement gathering till UAT support before go-live\n\n• Database design, GUI design.\n• Development of database objects like procedure packages, and development of GUI.\n• Design and developed customized report as per client requirement.\n• Component testing.\n• End to end test support.\n• UAT support and post deployment support.\n\nEDUCATION\n\nBachelor of Engineering in ELECTRONICS AND TELECOMMUNICATION\n\nAmravati University -  Amravati, Maharashtra\n\n1997 to 1999\n\nDiploma in INDUSTRIAL ELECTRONICS\n\nTechnical Board of Education Bombay -  Mumbai, Maharashtra\n\n1993 to 1996\n\nSKILLS\n\nMENTORING (10+ years), SCHEDULING (9 years), ORACLE (9 years), SOLUTIONS (9 years),\nBENEFITS (4 years)\n\nADDITIONAL INFORMATION\n\nI.T KNOWLEDGE &amp; SKILLS\nProgramming Languages: VB script, C, C++, Visual Basic, COM/DCOM\nDatabases: Oracle, SQL,\nDevelopment Tools: Toad, PL/SQL Developer, Crystal Report, Putty,Clarify, GSD, RTC, JIRA,\nConfluence, VSS, PVCS,\nWeb Servers: IIS, Oracle HTTP Server on Windows, Weblogic 10.3, WebSphere, Apache Tomcat,\nMTS\nBatch Scheduling: Control-M, Cron0Jobs, Windows Schedulers\nOperating Systems: Windows […] Pro &amp; Server/XP Home &amp; Professional/2003 Server/\nVista/7 Pro &amp; Enterprise/8/10, MS-DOS, UNIX, Linux\nWindows Packages: Microsoft Office: (Word, Excel, Access, Outlook and PowerPoint), Oracle\nDBMS, Internet Explorer, Netscape, Lotus Notes, Adobe Flash, Photoshop, and CITRIX Metaframe\n1.8/XP\nTransferable Skills: Excellent business skills, project management, presentation, interpersonal,\ncommunication and report writing skills, Team Mentoring\n\nOTHER SKILLS\n• Sound customer-facing skills: drive demos, status calls, issues &amp; escalation handling and\nprovide solutions which benefits the business and customer\n\n\n\n• Dynamic Team leader, strong resource management, team building skills and conflict\nmanagement. Strong in result oriented service delivery to the customer\n• Excellent cross-vendor communication skills\n• Strong Analytical &amp; problem solving ability and proactively drive opportunities to resolution\nwithout supervision", "meta": {}, "annotation_approver": "admin", "labels": [[0, 12, "Name"], [13, 43, "Designation"], [47, 59, "Companies worked at"], [60, 75, "Designation"], [85, 102, "Location"], [125, 167, "Email Address"], [177, 203, "Years of Experience"], [211, 219, "Skills"], [221, 227, "Skills"], [229, 240, "Skills"], [242, 256, "Skills"], [259, 273, "Skills"], [275, 278, "Skills"], [305, 333, "Skills"], [335, 355, "Skills"], [546, 564, "Skills"], [579, 586, "Skills"], [687, 689, "Skills"], [929, 944, "Skills"], [1001, 1010, "Skills"], [1507, 1519, "Designation"], [1573, 1588, "Skills"], [1590, 1606, "Skills"], [1650, 1669, "Skills"], [1676, 1690, "Skills"], [1728, 1750, "Skills"], [1918, 1937, "Skills"], [1898, 1916, "Skills"], [1939, 1956, "Skills"], [1958, 1976, "Skills"], [2243, 2257, "Skills"], [2259, 2277, "Skills"], [2290, 2307, "Skills"], [2497, 2510, "Skills"], [2512, 2516, "Skills"], [2542, 2555, "Skills"], [2738, 2752, "Skills"], [2911, 2941, "Designation"], [2945, 2956, "Companies worked at"], [2958, 2973, "Designation"], [2976, 3030, "Email Address"], [3082, 3099, "Companies worked at"], [3103, 3123, "Years of Experience"], [3181, 3203, "Skills"], [3480, 3499, "Skills"], [3554, 3558, "Companies worked at"], [3732, 3758, "Skills"], [3900, 3910, "Skills"], [3911, 3928, "Skills"], [3931, 3959, "Skills"], [3961, 3983, "Skills"], [3986, 4009, "Skills"], [4012, 4039, "Skills"], [4042, 4060, "Skills"], [4063, 4082, "Skills"], [4085, 4103, "Skills"], [4106, 4123, "Skills"], [4126, 4144, "Skills"], [4188, 4207, "Skills"], [4331, 4340, "Skills"], [4481, 4496, "Designation"], [4498, 4502, "Companies worked at"], [4506, 4523, "Location"], [4527, 4552, "Years of Experience"], [4612, 4630, "Skills"], [4641, 4645, "Companies worked at"], [4649, 4656, "Companies worked at"], [5248, 5260, "Designation"], [5262, 5280, "Companies worked at"], [5284, 5301, "Location"], [5305, 5329, "Years of Experience"], [5850, 5853, "Skills"], [6133, 6155, "Skills"], [6238, 6280, "Skills"], [6814, 6832, "Skills"], [6835, 6852, "Skills"], [6854, 6869, "Skills"], [7187, 7202, "Skills"], [7214, 7235, "Skills"], [7462, 7471, "Skills"], [7476, 7491, "Skills"], [7771, 7780, "Designation"], [7782, 7801, "Companies worked at"], [7805, 7807, "Location"], [7811, 7833, "Years of Experience"], [8380, 8402, "Skills"], [8485, 8527, "Skills"], [8877, 8902, "Skills"], [9216, 9244, "Skills"], [9433, 9448, "Skills"], [9728, 9734, "Skills"], [9735, 9740, "Skills"], [9751, 9757, "Skills"], [10028, 10042, "Designation"], [10044, 10058, "Companies worked at"], [10092, 10114, "Years of Experience"], [10651, 10661, "Skills"], [10634, 10649, "Skills"], [10816, 10833, "Skills"], [10837, 10860, "Skills"], [10917, 10977, "Degree"], [10979, 10998, "College Name"], [11002, 11023, "Location"], [11025, 11037, "Graduation Year"], [11039, 11072, "Degree"], [11074, 11102, "College Name"], [11103, 11119, "Location"], [11121, 11132, "Location"], [11134, 11146, "Graduation Year"], [11156, 11165, "Skills"], [11179, 11189, "Skills"], [11201, 11207, "Skills"], [11219, 11228, "Skills"], [11240, 11248, "Skills"], [11334, 11343, "Skills"], [11345, 11346, "Skills"], [11348, 11351, "Skills"], [11353, 11365, "Skills"], [11367, 11375, "Skills"], [11387, 11393, "Skills"], [11395, 11398, "Skills"], [11419, 11423, "Skills"], [11425, 11441, "Skills"], [11443, 11457, "Skills"], [11459, 11464, "Skills"], [11465, 11472, "Skills"], [11474, 11477, "Skills"], [11479, 11482, "Skills"], [11484, 11488, "Skills"], [11490, 11500, "Skills"], [11502, 11505, "Skills"], [11507, 11511, "Skills"], [11526, 11529, "Skills"], [11531, 11542, "Skills"], [11553, 11560, "Skills"], [11577, 11586, "Skills"], [11562, 11575, "Skills"], [11588, 11601, "Skills"], [11647, 11665, "Skills"], [11636, 11645, "Skills"], [11685, 11692, "Skills"], [11707, 11716, "Skills"], [11789, 11795, "Skills"], [11797, 11801, "Skills"], [11803, 11808, "Skills"], [11827, 11890, "Skills"], [11892, 11903, "Skills"], [11924, 11932, "Skills"], [11934, 11945, "Skills"], [11947, 11958, "Skills"], [11960, 11969, "Skills"], [11975, 11995, "Skills"], [12047, 12065, "Skills"], [12081, 12094, "Skills"], [12096, 12109, "Skills"], [12114, 12128, "Skills"], [12137, 12151, "Skills"], [12238, 12257, "Skills"], [12375, 12388, "Skills"], [12400, 12419, "Skills"], [12505, 12518, "Skills"], [12552, 12567, "Skills"]]}
{"id": 28, "text": "Debasish Dasgupta\nTrainer-Finacle-Core Banking Solutions-Infosys - Onward eServices\nlimited\n\nQasba, Bihar - Email me on Indeed: indeed.com/r/Debasish-Dasgupta/a20561e10f83ae3f\n\n✓ Worked as a faculty for Infosys in PTC ( Postal Training Centre) of DOP (Department of Post-\nIndia)\n✓ Undertaken Classes for Banking Theory and practices.\n✓ Trainer- Finacle Core Banking Solutions\n✓ Maintaining client base of 20 Crores.\n✓ Investment Banking ( H.N.I section)\n✓ Admin and ATM manager (IT and branch)\n✓ Management and smooth functioning of ATM network and administration.\n✓ Implementation of Finacle10.2 in the branch level (Axis Bank)\n✓ Successfully completed the task of Transition Supervisor in the migration from Finacle7 to\nFinacle 10.2.\n✓ Over 4 years of rich experience in financial sector\n✓ Ability to support and sustain a positive work environment that fosters team performance with\nstrong communication and negotiation skills.\n✓ Thorough understanding of cash management services involving bulk payments for large\ncorporate and processing of cash and cheque collections from them.\n✓ Thorough working knowledge Branch Banking &amp; routine banking operations.\n✓ Possess excellent interpersonal, communication and organizational skills with demonstrated\nabilities in team management and customer relationship management.\n✓ Developing Customer base for the Bank and be strong player in the growth of overall business\n\nWORK EXPERIENCE\n\nTrainer-Finacle-Core Banking Solutions-Infosys\n\nOnward eServices limited -  Chennai, Tamil Nadu -\n\n2014 to Present\n\nJob Summary: Trainer in PTC (Postal Training centre) - Department of Post.\n\n• Training clients about Finacle CBS menu opttions.\n• Training postal employees about the day to day banking practice.\n• Training postal employees about Finacle 10.2 implementation in the department.\n• Supervising in the migration from manual data entry to core banking application.\n• Training client about the package.\n• Migration of the system to new software.\n\nBranch Manager\n\nRainbow Financial Services -  Kolkata, West Bengal -\n\n2012 to April 2014\n\nhttps://www.indeed.com/r/Debasish-Dasgupta/a20561e10f83ae3f?isid=rex-download&ikw=download-top&co=IN\n\n\nInvestment Banking- salt Lake- Kolkata)\nJob role: Branch Manager\n\nJob Summary:\n• Cash flow management.\n• Providing trading solutions to all clients.\n• Providing working capital solutions to small scale and big firms.\n• Treasury management.\n• Worked as Financial Product consultant (H N I)\n• Responsibility to provide financial solution to the customers\n• Channelizing the liquidity of the customer to proper fund.\n\nAsst Manager\n\nAXIS Bank Ltd -\n\nJuly 2007 to 2012\n\nJamshedpur_\n\nJob Role: Admin and ATM coordinator of branch.\n\n* Job summary: Admin (Branch &amp; IT)\n➢ Implementation of Finacle 10.2 (banking software) in the region.\n➢ Training the branch staffs In Finacle 10.2 module and supervising the transition period of every\ndepartment from Finacle 7 to Finacle 10.2. at the time of migration.\n➢ Fixed asset management through FAMS software in the region assigned to me.\n➢ Responsible of procurement of new assets and stationery through e-shop software, sale and\npurchase of old assets through quotations from different vendors.\n\n➢ Space management of the branch level and 5's implementation in the branch through proper\ncoordination with the team.\n➢ Attendance record management in the HR software.\n➢ Maintenance of all important branch documents for e.g. personal HR file, govt. document\nmaintained by all banks, all notices to be displayed in the branch.\n➢ Attending security meetings with local authorities on fortnightly basis.\n➢ Coordination with concurrent auditor and external auditor to ensure \"AAA\" ratings for the\nBranch through proper compliance in yearly audit.\n➢ Decision making authority of all petty expenses on a branch level.\n➢ IT related issues to be coordinated with the help desk (WIPRO)\n➢ Software and hardware management of all the branches in the region\n➢ Network logbook maintenance and record keeping of all the issues faced by the branches.\n➢ Record management of all the branch data and to keep a back up of the same in the main\nserver.\n➢ Responsible of smooth functioning of the server and network through regular track keeping\nand ensuring all the service request or call lodged should attended within proper TAT.\n* Job summary: ATM Manager:\n\n♦ Before outsourcing and centralization of ATM's\n➢ Reconciliation of the all ATM's manually and in software.\n\n\n\n➢ Member of Circle Audit Team for compliance in ATM reconciliation of other branches.\n➢ Reversal of all excess and short cash issues of the ATM's.\n➢ Maintaining TAT for own bank and other bank customer disputes on a regular basis.\n➢ Coordination of ATM team and local logistics &amp; courier for smooth functioning of ATM.\n➢ Keeping track of cash replenishment of all ATM's in the region.\n➢ Getting approval and maintaining record of all ATM related expenditure.\n➢ Procurement of new ATM machine through different vendors and also to decide new location\nof ATM site.\n➢ Fortnight audit and verification of ATM's and cash vaults\n➢ Rent agreements of ATM's and renewal of the same.\n\nAfter outsourcing and centralization of ATM's\n➢ Handing over the reconciliation of all the ATM's to the central ATM nodal cell at the time of\ncentralization of ATM's.\n➢ Coordination with outsource agency for smooth and proper functioning of ATM's under the\nbranch and also coordinating in acquisition of new ATM site.\n➢ Daily updating of all the details in the respective software.\n➢ Coordination with local CRA agency for smooth cash replenishment of all ATM's.\n➢ Coordination with the central reconciliation team to solve own bank and other bank disputes\nwithin the given TAT.\n\n* Having through knowledge on implementation of Finacle7 &amp; Finacle10.2 (Core Banking\nSolution Software developed by Infosys)\n* Head cashier for 2.5 years, serving ATM cash Requirement, Cash remittance to currency Chest\nand managing CDP (Cash delivery &amp; pick-up) by strictly following clean note policy.\n* Handled Cash Management Services.\n* Handled Front desk banking Operation for e.g. - D.D printing, cheque transfer, fixed deposit,\nLocker Facility, GBM challan.\n* Handling Customer Related Queries and KYC compliance and AML.\n\nKey Learning's\n• A complete training of Finacle 10.2 (Classroom and on the job)\n• A holistic experience of overall administration, logistics and operation of banking procedures\nto serve the HNI clients of the Bank and also to receive appreciation and acknowledgement from\nboth the sides.\n\n• Thorough knowledge of Audit Compliance.\n\nFront Office Credit Coordinator\n\nH.D.F.C. Ltd -  Kolkata, West Bengal -\n\nSeptember 2005 to June 2007\n\nKolkata\n\nKey Result Areas across assignments:\n\nJob Responsibilities:\n* Worked as Front Office Credit Coordinator.\n\n\n\n* Preparation of Housing loan Sanction report.\n* Coordinating sales team and their sales promotional activities\n\nKey Learning's\n• Exposure to Housing Loan industry.\n\nFinancial Product consultant\n\nI.C.I.C.I PRUDENTIAL LIFE INSURANCE -  Kolkata, West Bengal -\n\nJanuary 2004 to February 2005\n\nH N I)\n• Responsibility to provide financial solution to the customers\n• Channelizing the liquidity of the customer to proper fund.\n• different industries.\n• Strong P.R build quality developed due to daily customer interaction.\n• Successfully completed 5 years of banking as an ATM manager and branch admin.\n• Many recognition from branch level and as well as circle level.\n• Proper migration and implementation of banking software from Finacle 7 to Finacle10.2.\n• Error free audit in Spam of 2.5 years as a Branch Main Cashier.\n• Achievements Of targets in Third party Products.\n• Managing complete branch operations with key focus on bottom line profitability by ensuring\noptimal utilization of available resources.\n• Implementation &amp; Achievement of 5S for the Branch.\n\nEDUCATION\n\nB.B.M\n\nAndhra University\n\n2003\n\nGulmohar High School -  Jamshedpur, Jharkhand\n\n2000", "meta": {}, "annotation_approver": "admin", "labels": [[0, 17, "Name"], [18, 25, "Designation"], [26, 33, "Companies worked at"], [57, 64, "Companies worked at"], [67, 98, "Companies worked at"], [100, 105, "Location"], [128, 176, "Email Address"], [203, 210, "Companies worked at"], [304, 318, "Skills"], [418, 436, "Skills"], [466, 477, "Designation"], [893, 906, "Skills"], [911, 922, "Skills"], [959, 974, "Skills"], [1114, 1128, "Skills"], [1143, 1161, "Skills"], [1183, 1196, "Skills"], [1198, 1211, "Skills"], [1216, 1230, "Skills"], [1269, 1284, "Skills"], [1289, 1321, "Skills"], [1436, 1443, "Designation"], [1444, 1508, "Companies worked at"], [1512, 1531, "Location"], [1535, 1550, "Years of Experience"], [1565, 1572, "Designation"], [2082, 2141, "Email Address"], [1992, 2006, "Designation"], [2008, 2034, "Companies worked at"], [2038, 2058, "Location"], [2062, 2080, "Years of Experience"], [2235, 2249, "Designation"], [2266, 2286, "Skills"], [2300, 2307, "Skills"], [2600, 2612, "Designation"], [2614, 2627, "Companies worked at"], [2631, 2648, "Years of Experience"], [2650, 2660, "Location"], [2770, 2801, "Skills"], [3128, 3143, "Skills"], [4061, 4078, "Skills"], [4350, 4361, "Designation"], [4941, 4952, "Skills"], [5055, 5060, "Skills"], [5867, 5879, "Designation"], [5884, 5893, "Years of Experience"], [5907, 5923, "Skills"], [5925, 5940, "Skills"], [5972, 5975, "Skills"], [6587, 6603, "Skills"], [6606, 6637, "Designation"], [6639, 6651, "Companies worked at"], [6655, 6675, "Location"], [6679, 6706, "Years of Experience"], [6708, 6715, "Location"], [6789, 6820, "Designation"], [6887, 6892, "Skills"], [6967, 6988, "Skills"], [6991, 7019, "Designation"], [7021, 7057, "Companies worked at"], [7060, 7080, "Location"], [7084, 7113, "Years of Experience"], [7409, 7421, "Designation"], [7393, 7404, "Designation"], [7871, 7873, "Skills"], [7902, 7907, "Degree"], [7909, 7926, "College Name"], [7928, 7932, "Graduation Year"], [7958, 7979, "Location"], [7981, 7985, "Graduation Year"]]}
{"id": 29, "text": "Suresh Kanagala\nArchitecture SharePoint/Office 365 /Azure cloud/.Net\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Suresh-\nKanagala/04b36892f9d2e2eb\n\nLooking for challenging position\n\nWORK EXPERIENCE\n\nTechnical Architect (Manager C2)\n\n-\n\nAugust 2015 to Present\n\nSenior Professional Technology Analyst\n\nCSC India -\n\nSeptember 2005 to August 2015\n\nConsultant\n\nIBM -\n\nDecember 2004 to September 2005\n\nConsultant Infosys from Modus Systems -Tester\n\n-\n\nJune 2004 to December 2004\n\nAssociate consultant\n\nCMC -\n\nJuly 2003 to June 2004\n\nEDUCATION\n\nMonth/Year Format\n\nTech lead -  Hyderabad, Telangana\n\nSeptember 2016 to May 2017\n\nM.C.A\n\naffiliated college of Andhra University\n\n2002\n\nhttps://www.indeed.com/r/Suresh-Kanagala/04b36892f9d2e2eb?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Suresh-Kanagala/04b36892f9d2e2eb?isid=rex-download&ikw=download-top&co=IN\n\n\nB.Sc in Maths, Physics and Computers\n\nNagarjuna university\n\n1999\n\nSKILLS\n\n.NET (Less than 1 year), ASP (Less than 1 year), DATABASE (Less than 1 year), MS SQL SERVER\n(Less than 1 year), SQL (Less than 1 year)\n\nLINKS\n\nhttp://www.linkedin.com/in/sureshkanagala\n\nADDITIONAL INFORMATION\n\nOperating Systems Windows and Unix\nConfiguration management tools TFS and VSTS\nProgramming Languages VB, ASP, VB.net, C#, JavaScript, Jquery and Angular JS (Starter), CRM\nFrontend HTML and .Net\nMiddleware MVC and WCF\nDatabase SQL Server and Oracle\nContent Migration tools Metalogix and Sharegate\nAutomation PowerShell and VSTS\n\nDOMAIN EXPERIENCE\nInsurance 10 Years\nEngineering 3 Years\nBanking 2.5 Years\n\nhttp://www.linkedin.com/in/sureshkanagala", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 30, "text": "Jaspreet Kaur\nOceanic Consultants as a HR Executive\n\nFaridabad, Haryana - Email me on Indeed: indeed.com/r/Jaspreet-Kaur/1b83bc42482ed5a0\n\nTo prove my worth as an employee by working under challenging opportunities and to grow\nmyself professionally and intellectually by contributing towards my organization's progress.\n\nWORK EXPERIENCE\n\nOceanic Consultants as a HR Executive\n\nOceanic Consultants Pvt. Ltd -  Chandigarh, Chandigarh -\n\nJune 2013 to January 2014\n\nOceanic Consultants, established in 1996, is one of the most successful international student\nrecruitment companies in the world today.The company, with its headofficein Melbourne,\nAustralia has one of the largest networks of company owned offices in India.\n\nKey Responsibilities:\n• Recruitment: Identify vacancy, Advertising the vacancy (Internal &amp; External), Managing\nthe response, Resume screening, Arranging interviews (Phone round/ F2F round), Short listing\nthe candidate, Conducting interviews.\n\n• Joining Formalities: Conduct Induction &amp; Orientation program for New Joinee, Prepare all\ndetails of joinee and send to ICT for creation of login ID, Configure system, Request for attendance\ncard, Business card, ESI / PF Formalities, Generate Offer Letter, Maintain personal file of new\njoinee ( Soft and hard copies)\n\n• Retention: Assessment of new joinee after six months, Extension of service ( If any),\nArrangements of Relocation, Arrangements of Job Rotation, Promotions, Prepare due Increment,\nArrangements on birthday/ Anniversary/Wedding gifts/ Festival celebrations, Attendance and\nLeave management, Handle quires for ESI / PF benefits/ Leaves/ salary/ Policies, Maintain data\nof salary/ Confirmations/ Increments, Manage monthly manual attendance record of all the\nbranches, Mark comp off / attendance rectification/credit leaves, Time to time notifications for\nchange in policy/ branch closure/ Holidays/ Promotions/ Transfers, Employment check, Update\nascent one (Reporting change/contact details), Employee engagement\n\n• Salary &amp; Payroll: Manage monthly attendance and leaves, Notify accounts department with\nnew joinees detail/ Increments/ Promotions/ Transfers/Full and final settlement of exit employees\netc, Prepare joinee details and send twice to accounts dept. for opening of salary of bank account,\n'Leave processing.\n\n• Full and Final settlement: Exit formalities, Check with Manager for deactivation of system, email\nforwarding and mailbox, Deactivate System, Exit emails, Circulate email to all concern heads for\nexit employee, Gratuity, PF withdrawal formalities, Experience letter, Salary certificate.\n\nHR Officer\n\nhttps://www.indeed.com/r/Jaspreet-Kaur/1b83bc42482ed5a0?isid=rex-download&ikw=download-top&co=IN\n\n\nMAYA ESTATZ -  Zirakpur, Punjab, IN -\n\nJanuary 2012 to May 2013\n\nMaya Estatz specializes in providing qualitative and quantitative solutions for the Real Estate\nindustry around Punjab, Himachal, Haryana, Chandigarh, Panchkula, Zirakpur&amp;Mohali.\n\nKey Responsibilities:\n• Recruitment: Manpower Planning &amp; Approvals, Interviews and documentation. Selection\nof candidates based on identified competencies.\n• Coordination with consultants regarding recruitments.\n\n• Maintain up-to-date recruitment progress report through RAG.\n\n• Joining and Induction: Designed a Structured Programme for all new joiners and also prepared\nan Induction Manual with process detail.\n\n• Joining formalities of new employees are done at the time of joining.\n\n• Verification done from previous company, home and references of each new joiner.\n\n• Time Office Management: Leave and Attendance Record, Maintenance of personal files.\n\n• Managing Reward &amp; Recognition Programme for employees.\n\n• Event Management in the office premises like employee Birthday &amp;Anniversary\ncelebrations, Christmas celebration, Diwali, Lohri celebrations etc.\n\n• Human Resource Administration:\n\n* Stationary Management\n\n* Issuing of ID Cards\n\n* First Aid Facility\n\n* Maintenance of Ledger of all office Expenses.\n\n* Issuing of Official No and keep the record of all official no.\n\n* Coordination with Vendors regarding Advertisements and other official work.\n\n• Keep the record of all new joining and Exit Employees.\n\n• Preparing Dash board of all CV's of Candidates and Updating on daily basis.\n\n• Analysis on Employee Turnover &amp; Calculation of Attrition Rate.\n\nHR Executive- SOLITAIRE INFOSYS\n\n\n\nSOLITAIRE INFOSYS PVT. LTD -  Chandigarh, Chandigarh -\n\nJanuary 2011 to October 2011\n\nthat provides high quality comprehensive services to enterprises across a wide range of platforms\nand technologies.\n\nResponsibilities as a HR:-\n• Man Power Planning: - Analysing the vacancy in IT Department.\n\n• Recruitment &amp; Selection:-Posting the job on job portals, searching the candidate through\nconsultancies.\n\n• Joining Formalities:-Issuing of appointment Letters to new joiners and documentations of new\nemployees.\n\n• Induction Programme:- Introduce with company policies, employees etc.\n\n• Providing Trainings to the New Employees about the company, product Knowledge etc.\n\n• Maintenance the Record of Attendance of all employees.\n\n• Employee Welfare: Organizing Parties, Tours and also celebrating the festivals.\n\n• Formulation of HR Policy regarding attendance leaves.\n\nCustomer Related Activities:-\n• Maintaining Track of After Sale Activities\n• Maintaining Track of Payments/Deliveries\n• Customer Feedback and Suggestions Ways to improve Business Turnover with each customer.\n\nEDUCATION\n\nMasters of Business Administration\n\nMohali Campus -  Mohali, Punjab\n\n2011\n\nBachelor of Commerce\n\nDev Samaj College for Women -  Chandigarh, Chandigarh\n\n2009\n\nCentral Board of Secondary education\n\n2003 to 2005\n\nADDITIONAL INFORMATION\n\nTechnical Skills\n\n\n\nOS Windows XP, Vista\nOthers Microsoft word/excel/power point, Basic computer skills, Tally\n\nInternship details\n\n2 months training experience in Marketing at HCL Infosystems (Mohali)\n\nProject:\n\"CASE STUDY AND SCOPE OF ENTERPRISE RESOURCE PLANNING (ERP) APPLICATION IN\nEDUCATION DOMAIN IN PUNJAB\"", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 31, "text": "Somanath Behera\nAssociate, Cognizant technology Solutions, Bangalore\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Somanath-Behera/\ne9188fe8ba12dbbd\n\n• 6 years and 6 months of extensive experience in Requirement Gathering, Analysis, Design,\nDevelopment, Implementation, Testing and Support of Data Warehousing Applications using\nInformatica Power center ETL Tool.\n• Expertise in systems analysis, gathering functional requirements, documenting business\nprocess, programming and Quality Assurance along with excellent troubleshooting,\ncommunication, and interpersonal skills for Data Warehouse.\n• Quickly adaptable to new technology, coding standards and conventions. Being involved with\nsoftware industry shows professionalism in working in a team and have experience of working\nas POC. Delivered the expectations with Quality and proactive in every situation.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nAssociate Consultant\n\nCognizant Technology Solutions -  Bengaluru, Karnataka -\n\nJuly 2014 to Present\n\nHave working experience on end to end development using Informatica,Unix and Oracle.\nEffectively managing a small team and delivering successful codes.\n\nSenior System Engineer in Infosys Limited, Chennai\n\nTraining in Infosys Limited -  Mysore, Karnataka -\n\nAugust 2011 to November 2011\n\nMysore from 1st Aug 2011 - 30 Nov 2011. I have worked as Senior System Engineer in Infosys\nLimited, Chennai till 24th June, 2014.\n• Currently working in Cognizant from 14th July, 2014 till date.\n\nEDUCATION\n\nB.Tech in Electronics and telecommunication\n\nBPUT -  Bhubaneshwar, Orissa\n\nAugust 2007 to April 2011\n\n+2 Science in Science\n\nF.M. Junior College -  Balasore, Orissa\n\nJune 2003 to April 2005\n\nhttps://www.indeed.com/r/Somanath-Behera/e9188fe8ba12dbbd?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Somanath-Behera/e9188fe8ba12dbbd?isid=rex-download&ikw=download-top&co=IN\n\n\nSKILLS\n\nOracle Pl/Sql (6 years), Facets (3 years), Hadoop (1 year), healthcare (4 years), Informatica (6\nyears)\n\nADDITIONAL INFORMATION\n\nTechnical Skills:\n\nOperating System Unix and Windows\nProgramming languages Unix Scripting, SQL and PL/SQL, Core Java, Basics of Python\nDatabases and Tools Informatica Power Center, Oracle, Hadoop and Spark\nOther Utilities SVN, Jira\nScheduling Tools Tidal and Control-M\nCloud Technology Microsoft Azure\nDomain Knowledge Retail, Healthcare, Banking\n\nFunctional Knowledge:\n\n• Have very good functional knowledge on Healthcare and Facets application. Understanding on\nrelationship between different Facets tables and their different parameters as designed.", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 32, "text": "Ashish Indoriya\nSr. Systems Engineer at Infosys Limited\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Ashish-\nIndoriya/84f99c99ebe940be\n\n• Master of Computer Application (MCA) from Bhilai Institute of Technology, Durg, 2014.\n• Having 3.3 years of Experience on Software Development at Infosys limited.\n• Extensive working experience on Java, Spring, Hibernate and SQL\n• Knowledge of design patterns such as Singleton, Factory, Façade, Observer and MVC.\n• Knowledge of Front-end web development using JavaScript, JQuery, CSS &amp; HTML.\n• Having knowledge of Oracle SQL Database.\n• Reliable as a fully contributing, responsible and accountable member of task/ project teams\nwith highly honed creative, logical and analytical approach.\n• Automated some of HRMS processes like Hiring, transfer, termination to help speed up the QA\nprocess.\n• Hands on knowledge of C, C++ including advanced concepts such as pointers and Dynamic\nMemory Management.\n• Learning Hadoop and Big data analysis using Apache pig, hive and map reduce.\n• Learning Scala with Spark for Big Data analysis\n\nWilling to relocate to: hyderbad, Telangana\n\nWORK EXPERIENCE\n\nSr. Systems Engineer at Infosys Limited\n\nFidelity Investments -\n\nMarch 2015 to February 2018\n\nRoles &amp; Responsibilities:\nDescription: This is an HRMS customization in Java programming language with Spring, SQL and\nJavaScript. A web based highly complex application to manage the processes of HRMS, with\ndifferent types of reports. It's critical as the information managed directly or indirectly affects\nthe payroll of the employees.\n\n• Involved in Requirement Analysis and interaction with clients\n• Involved in designing of Functional Design Document\n• Involved in designing of Technical Design Document\n• Responsible for implementing Business Logic using Java Programming Language as per\nrequirements\n• Involved in developing SQL scripts for various reports to pull data from HRMS Application\ndatabase\n• Automated some of HRMS processes like Hiring, transfer, termination to help speed up the QA\nprocess\n• Involved in Zero Distance team to help the client reduce the manual work and worked on Excel\nMacro for Tax Calculation automation\n• Involved in development of java module to remove duplicates or invalid entry from XML\nconfiguration file\n\nhttps://www.indeed.com/r/Ashish-Indoriya/84f99c99ebe940be?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Ashish-Indoriya/84f99c99ebe940be?isid=rex-download&ikw=download-top&co=IN\n\n\nInfosys Internal Project:\nExcel to RDBMS data load application\nAn application to load data from Excel sheets to any database\nDescription: Much of the legacy as well as current information was available in excel sheets within\nthe organization, this application targets to keep them in database for availability, accessibility,\nfaster retrievals and better organization.\n• Flexible application to load data from different excel sheets with minimum configurations\n• Applied Industry standard Design patterns like Singleton, Observer and Strategy\n• Responsible for end to end application\n\nAchievements and Co-curricular Activities:\nAchievement\n• Awarded Memento by client for outstanding contribution and client satisfaction\n• Infy Insta Award for outstanding contribution and being reliable team member\n• IBM DB2 Academic Associate: DB2 Database and Application Fundamentals.\n\nEDUCATION\n\nSecondary School Certificate in Design Pattern\n\nChhattisgarh Board of Secondary Education -  Raipur, Chhattisgarh\n\nAugust 2010\n\nMaster of Computer Applications in Computer Applications\n\nBhilai Institute of Technology -  Bhilai, Chhattisgarh\n\nBachelor of Computer Applications in Computer Applications\n\nC V Raman University Bilaspur -  Raman, Punjab\n\nSecondary School Certificate\n\nChhattisgarh Board of Secondary Education -  Raipur, Chhattisgarh\n\nSKILLS\n\nJava, SQL, J2EE, Sping, Hibernate, JavaScript, HTML, CSS\n\nADDITIONAL INFORMATION\n\nServing notice period", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 33, "text": "Dilliraja Baskaran\nTamil Nadu - Email me on Indeed: indeed.com/r/Dilliraja-Baskaran/4a3bc8a35879ce5c\n\nWORK EXPERIENCE\n\nWorking in Infosys as a developer and support\n\nInfosys -\n\nMay 2015 to Present\n\nExperience in both development project and support project.\n\nEDUCATION\n\nB.E\n\nPanimalar Engineering College\n\nSKILLS\n\nMainframe,COBOL,JCL,DB2,IMSDB,IMSDC\n\nhttps://www.indeed.com/r/Dilliraja-Baskaran/4a3bc8a35879ce5c?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 34, "text": "Deepika S\nTest Engineer - Infosys Ltd\n\n- Email me on Indeed: indeed.com/r/Deepika-S/1b4436206cf5871b\n\nWORK EXPERIENCE\n\nTest Engineer\n\nInfosys Ltd -  Chennai, Tamil Nadu -\n\nApril 2017 to Present\n\nTesting Executive, Infosys Ltd, Chennai\n\nE-publisher -  Coimbatore, Tamil Nadu -\n\nMay 2013 to July 2014\n\nSeptember 2014 - March 2017)\n\nTest Management Tool\n\nHP ALM -\n\n2012 to 2012\n\n• Domain: Retail, Banking\n• Utilities: Putty, WinScp.\n• Operating Systems: Windows XP/7/8\n\nPROFESSIONAL EXPOSURE \nSoftware Testing Projects\n\nCiti Bank -\n\n2005 to 2005\n\nDescription of the Project: The objective of the project is to track the Gifts and Entertainment\nprovided to or received from client or potential client or anyone else whom citi does business.\nCGE it is a web based application.\nAnd the project uses separate tool IFW for reporting.\nTesting Management Tool: ALM for tracking defect\nDuration: Nov 2016 to Till Date\n\nCustomer Service Representative\n\nTNT Express -\n\n2004 to 2004\n\nDescription of the Project: The Objective of the project is to use Salesforce for servicing the\ncustomer. TNT express is a courier company which does international business with more than 60\n\nhttps://www.indeed.com/r/Deepika-S/1b4436206cf5871b?isid=rex-download&ikw=download-top&co=IN\n\n\ncountries. They are using more than 30 applications for tracking their process and it is difficult to\nprovide service for the customer. This project integrates with all application and provided single\ninterface for customer service. Also it helps CSR (Customer Service Representative) to provide\nservice for the customer by tracking where exactly their consignment is placed and how much\ntime it will take to reach the destination. Also CSR can receive voice calls and place calls to the\ncustomer through salesforce.\n\nJob scheduling tool: Director Client\nTesting Management Tool\n\nUtilities\nDatabase tools\n\nDuration\n: ALM for tracking requirements, test plans, test execution, reporting and Defect tracking\n\n: WinScp, Putty\n\n: SQL Developer, DataStage for DB2\n\n: Mar 2016 - Oct 2016\n03 Project Name: CRM Sales\n\nClient: TNT Express\nDescription of the Project: The Objective of the project is to use Salesforce for customer\nrelationship management that stores customer contact information like names, addresses, and\nphone numbers. TNT express is a courier company which does international business with more\nthan 60 countries. They used CDB for storing customer information. This project integrates with\nCDB and extracts all customer information. And it creates opportunities for the customers.\n\nTesting Management Tool\n\nUtilities\nDatabase tools\n\nDuration\n: ALM for tracking requirements, test plans, test execution, reporting and Defect tracking\n\n: WinScp, Putty\n\n: SQL Developer\n\n: Jan 2016 - Feb 2016\n02 Project Name: CDB UI Changes\n\nClient: TNT express\nDescription of the Project\n\nTest Management Tool\n\n\n\nThe project is about TNT needs some UI Changes in the CDB application.\n\nALM\n\n01 Duration\nProject Name\nClient\nDescription of Project\n\nDevices tested\n\nDefect Management Tool\nDuration\n\nE-Pub\nClient\nDescription of Project\n\nEDUCATION\n\nB.Sc. in Computer Science\n\nBharathiyar University -  Coimbatore, Tamil Nadu\n\n2013", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 35, "text": "Jacob Philip\nKottayam, Kerala - Email me on Indeed: indeed.com/r/Jacob-Philip/db00d831146c9228\n\nStrategicSales,\nexperienceinSales,\nskills.Currently,\nsuccess.\n\nWORK EXPERIENCE\n\nSales and marketing specialist\n\nASSISTANTBUSINESSDEVELOPMENTMANAGER -  Dubai, AE -\n\nFebruary 2017 to October 2017\n\nUAE\n• -Builtstrong\nclientrelationshipsandprovidedhighvalue-addingservices, resultingina15% company\nmarketshareincrease\n• Developstools,\npracticesacrosstheorganization\n• Negotiatingcontractsandpackages, Negotiatingthetermsofanagreementwithaviewto\nclosingsale\n•, expense, andnew\nbusinessdata\n• WorkedcloselywithPartners,\nthroughconductingqualityassurancetests.Actasthepointofcontactandcommunicate\nprojectstatustoallparticipantsinourteam\n\nORDINATOR\n\nMARKETINGCO -\n\nJanuary 2015 to June 2016\n\nBhimaJewelers\nKerala, India\n•, and systemreportforms\n• Plannedandexecutedeventsandmarketingprograms, producingfivetimestargetnumberof\nqualifiedleads\n• Implements marketing and advertising campaigns by assembling and analyzing sales\nforecastsandincreasedperformanceby52percent\n• Preparesmarketingreportsbycollecting, analyzing, andsummarizingsalesdata\n• Assignedtaskstoassociates, staffedprojects, trackedprogressandupdatedmanagers, and clients\n\nAtlas star medical center\n\nCUSTOMERSERVICEEXECUTIVE -  Dubai, AE -\n\nhttps://www.indeed.com/r/Jacob-Philip/db00d831146c9228?isid=rex-download&ikw=download-top&co=IN\n\n\nDecember 2012 to May 2014\n\nUnitedArabEmirates\n• Maintainandorganizeacustomerdatabaseofover10, 000members\n• Evaluatedpatientcareneeds, prioritizedtreatment, andmaintainedpatientflow\n• Responsibleforprimarycare, casemanagement, andmedicationmanagement\n• Dealtwithinsurancecards, cashcollection, billing, ande-claims;inadditiontohandling\nincomingcallsorenquiriesfrompatients\n•, and systemreportforms\n\nSun infosys\n\nSALESOFFICER -\n\nJuly 2010 to July 2012\n\nSun-Infosystems\nKerala, India\nincreasedcompanyexposure, customertraffic, andsales\nmaterialsforsalespresentationsandclientmeetings\n• Inadditiontoinventoryrecordingandmaintainstocks, assignedtaskstoassociates, staffed\nprojects, trackedprogressandupdatedmanagers, partnersandclientsasnecessary\n\nEDUCATION\n\nBachelor's\n\nSKILLS\n\nExcel (Less than 1 year), Word (Less than 1 year)\n\nLINKS\n\nhttps://www.linkedin.com/in/jacob-philip-a52744138\n\nADDITIONAL INFORMATION\n\nCORECOMPETENCIES:\n• Meetdead-lineswitheaseandefficiency\n• Pleasantandeffectivecustomerservice&amp;managementskills\n• FluentinEnglish, Hindi, Tamil, andMalayalamLanguages\n• StrongunderstandingofMicrosoftOffice (Excel, Word, andPowerpoint)\n\nhttps://www.linkedin.com/in/jacob-philip-a52744138", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 36, "text": "Yogesh Ghatole\nEngineer / Electrical Supervisor, Site at Shendra MIDC Aurangabad -\nPerkins India Ltd\n\nNagpur, Maharashtra - Email me on Indeed: indeed.com/r/Yogesh-Ghatole/b381ddf132151a29\n\nTo work in a challenging environment demanding all my skills and efforts to explore and adapt\nmyself in different fields and realize my potential where I get the opportunity for continuous\nlearning.\nJOB PROFILE\n\nWORK EXPERIENCE\n\nEngineer / Electrical Supervisor, Site at Shendra MIDC Aurangabad\n\nPerkins India Ltd -  Aurangabad, Maharashtra -\n\nJuly 2017 to Present\n\nDesignation: Engineer / Electrical Supervisor, Site at Shendra MIDC Aurangabad.\nDepartment: Plant Engineering\n• Maintaining plant Utility\n• Facility maintenance\n• Handling BMS ( Building Management System)\n• Handling Project work\n• Maintaining Electrical Equipment\n• Handling Electricians, Technicians in plant.\n• Maintaining transformer, circuit breakers, UPS, AHUs, Motors, Two pole structure and total\nlighting system in plant\n• Maintaining Plant Shutters (RAD), Motorized shutters\n• Maintaining daily Reading work formats\n• Maintaining electrical items like LT, HT MCC Panels DB's, OH Lights, Industrial fans,\n• Reporting to section head in shift ending on daily basis\n\nEngineer - Service at Infosys Ltd. Ph\n\nBlue Star Ltd -  Pune, Maharashtra -\n\nOctober 2016 to June 2017\n\n-2\n• Handling York Make, centrifugal chillers.\n• Operating &amp; maintenance DOAS unit, Actuators, HRW, Fresh Air Exhaust\n• Operating BMS (Building management system)\n• Operating &amp; maintenance of AHU s, Pumps, Cooling Towers, CSUs, FCUs, ECUs.\n• CFM adjustment, airflow adjustment.\n• Handling Technicians, Electricians &amp; Operators team.\n• Handling daily escalation of heat/cool complaints.\n\nShift Supervisor\n\nhttps://www.indeed.com/r/Yogesh-Ghatole/b381ddf132151a29?isid=rex-download&ikw=download-top&co=IN\n\n\nMAHINDRA & MAHINDRA LTD -  Nagpur, Maharashtra -\n\nJanuary 2013 to February 2016\n\nNAGPUR) Jan 2013 to Feb 2016\n(Tractor Manufacturing Plant)\nTotal Year of Experience: 3 years\nDesignation: Shift Supervisor &amp; Diploma Trainee Electrical Engineer\nDepartment: Central Maintenance\nJob Responsibilities\n• Handling complete shift planning\n• Maintaining transformer, circuit breakers, four pole structure and total lighting system in plant\n• Maintaining of plant PAC, AC's (Blue star &amp; Voltas)\n• Preventive maintenance planning &amp; attacking breakdowns\n• Maintaining plant utility.\n• Maintaining daily work management formats\n• Maintaining electrical items like DB's, OH Lights, Industrial fans, water coolers.\n• Reporting to section head in shift ending on daily basis\n• Handling Electricians in plant.\n\nEDUCATION\n\nDiploma in Electrical Engineering\n\nNagpur Polytechnic -  Nagpur, Maharashtra\n\n2012\n\nH.S.C in State Board\n\nShree Binzani nagar College -  Nagpur, Maharashtra\n\n2004\n\nS.S.C in State Board\n\nIndira Gandhi High School -  Nagpur, Maharashtra\n\n2002\n\nInstitute / Board\n\nSKILLS\n\n40 WPM (Less than 1 year), AUTOCAD (Less than 1 year), EXCEL (Less than 1 year), MS EXCEL\n(Less than 1 year), MS OFFICE (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSOFTWARE SKILLS\n• Completed successfully MS-CIT MS Office - Basic Computer Course (MS Word, MS Excel, MS\nPower Point &amp; Internet) AutoCAD in Electrical\n\n\n\n• Good English, Marathi, &amp; Hindi Typing Skills (30-40 Wpm)", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 37, "text": "Ajay Elango\nSoftware Engineer\n\nBangalore City, Karnataka, Karnataka - Email me on Indeed: indeed.com/r/Ajay-\nElango/3c79ad143578c3f2\n\n• 7+ years of professional work experience as a Software Engineer with core expertise in C++\nprogramming on both Linux and Windows environments.\n\n• Offered graduate admission in Computer Engineering into the following prestigious universities\nin\nthe world: Georgia Tech (Atlanta), Carnegie Mellon University (Pittsburgh), Cornell University\n(Ithaca), University of Southern California (Los Angeles), University of Colorado Boulder\n(Boulder), Arizona State University (Tempe) and University of Illinois Chicago (Chicago).\n\n• Graduated with Masters degree in Computer Engineering from Georgia Institute of Technology\n(Atlanta)\n\n• Worked as a Software Engineer at the following world-class corporations: MathWorks\n(Massachusetts), Cadence Design Systems (Massachusetts), National Instruments (Texas),\nInfosys (Bangalore) and Aeronautical Development Establishment (Bangalore).\n\n• Received Certifications from Stanford University (California), University of Washington\n(Seattle) and University of California San Diego (California)\n\n• Won corporate awards and received client appreciations for outstanding performance and\nsignificant individual contribution.\n\n• Proven track record of shipping secure, high quality software through all the phases of the\nsoftware development life cycle.\n\n• Passionate about designing, developing, troubleshooting, debugging, and testing large-scale\nsoftware projects.\n\n• Experienced in mentoring and managing new and experienced professionals on software\ndevelopment for enterprise applications.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSoftware Engineer\n\n-\n\nMay 2016 to October 2017\n\nWorked in the core simulation and visualization team which is one of the earliest and the most\nrevered and prestigious group of the organization.\n\nhttps://www.indeed.com/r/Ajay-Elango/3c79ad143578c3f2?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Ajay-Elango/3c79ad143578c3f2?isid=rex-download&ikw=download-top&co=IN\n\n\n• I was the owner of the Scope block which is widely considered as the most significant block\nin the Simulink toolset.\n• Designed and implemented features for a suite of interactive, real-time, and world-class\nsimulation visualization tools for MATLAB and Simulink. These tools are an essential part of\nSimulink models developed and used by engineers, researchers and scientists to visualize,\nmeasure, and analyze transitions and states of multichannel signals.\n• Implemented and maintained large scale software projects written in C++, JavaScript, Java, and\nMATLAB.\n• Collaborated with various teams including Application Support Engineering, UI, Technical\nMarketing, and Performance Engineering to develop specifications for the visualization tools.\n• Solved several complex bugs in Scope and Logic Analyzer on both Windows and Linux\nenvironments.\n• Improved performance of rendering signal names for multichannel signals in Logic Analyzer\nby over 85%.\n• Enabled Logic Analyzer to be launched from MATLAB online.\n• Involved in feature estimation, prioritization, and key discussions with product teams.\n• Collaborated with cross-functional teams to achieve team and organizational goals.\n\nLanguages &amp; Technologies: C++, Java, C, JavaScript, MATLAB, Microsoft Visual Studio,\nPerforce, Gecko, vi, gedit, gdb, ddd\n\nSoftware Engineer\n\nCadence Design Systems, Inc -\n\nJanuary 2015 to March 2016\n\nDesigned and developed complex 3D Models for PCB components used in Allegro PCB Designer\nand Allegro Package Designer (Electronic Design Automation tools which provide full-featured\nPCB design solutions).\n• Designed, implemented and tested algorithms for collision detection among all the 3D\ncomponents rendered on the 3D viewer.\n• Provided support, troubleshooting, solved several critical and sophisticated bugs in Allegro PCB\nDesign Suite and Allegro Package Designer on both Linux and Windows environments.\n• Worked closely with product engineers/technical sales and provided engineering solutions and\nworkarounds to make customers successful.\n• Provided comprehensive and easy-to-use solutions to assist designers with an efficient and\nsuccessful handoff to manufacturing.\n• Helped promote and drove sound engineering design, process and quality practices.\n• Contributed to research, design and implemented improvements to Allegro PCB Designer and\nAllegro Package Designer solutions.\n\nLanguages &amp; Technologies: C++, QT GUI, JIRA, Trello board, IBM Clearcase, CCMS, Cadet,\nvi, Valgrind, gdb, ddd, TotalView. Visual Studio\n\nSoftware Engineer, Intern\n\nNational Instruments, Austin -  US -\n\nMay 2014 to August 2014\n\n\n\nImplemented a boot up image on FPGA for the digitizer PXIe-5171 using LabVIEW.\n• Configured reference clocks, onboard clocks, ADC, transceivers, and DAC components for\nvarious sampling rates and multiple channels.\n• Automated the entire configure onboard clock sequence.\n\nSenior Systems Engineer - Infosys Technologies Limited\n\nLabVIEW, Perforce, Trello -  Bengaluru, Karnataka -\n\nNovember 2009 to June 2013\n\nBangalore, India\n(Nov 2009 - June 2013)\n\n• Implemented large scale software projects in C++ on Linux and Windows platform for a testing\nframework that was used to test the functionality of the firmware developed on wireless chipsets\n• Mentored and managed new and experienced software engineers on software development for\nenterprise applications.\n• Responsible for effectively initiating, planning, scheduling, estimating, forecasting, and\ndelivering all activities for projects and related release initiatives.\n• Won corporate awards and received client appreciations for outstanding performance and\nsignificant individual contribution.\n• Collaborated with cross-functional teams to define business requirements along with project\ndevelopment and resource estimates.\n• Consolidated, planned and developed low-level technical design documents for finalized client\nrequirements.\n• Analyzed complex technical issues and provided remediation options.\n• Reviewed detailed designs and participated in code reviews.\n• Significantly contributed to requirement analysis, feature estimates, and design improvements.\n\nLanguages &amp; Technologies: C, C++, Siebel CRM, JIRA, gdb, ddd, Valgrind, TotalView, vi,\neScripts\n\nIntern\n\nAeronautical Development Establishment (Defense Research -  Bengaluru, Karnataka -\n\nDecember 2008 to February 2009\n\nImplemented I2C protocol in VHDL for interfacing the video encoder (ADV 7174/7179) and video\ndecoder (ADV 7184) with the video tracking system.\n\nLanguages &amp; Technologies: VHDL, ModelSim\n\nEDUCATION\n\nMaster of Science in Electrical & Computer Engineering\n\nGeorgia Institute of Technology -  Atlanta, GA\n\nAugust 2013 to December 2014\n\n\n\nBachelor of Technology in Electronics & Communication Engineering\n\nAmrita School of Engineering -  Bengaluru, Karnataka\n\nJune 2005 to May 2009\n\nSKILLS\n\nC++ (6 years), LINUX (6 years), .NET (2 years), VISUAL STUDIO (2 years), JAVA (1 year)\n\nLINKS\n\nhttp://www.linkedin.com/in/ajayelango\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\n\nProgramming Languages: C++(98/11), Java, Python, C, JavaScript, MATLAB, SQL, C#, PHP\nSoftware Tools: Microsoft Visual Studio, Eclipse, gdb, ddd, TotalView Debugger,\nHoops 3DGS, OpenGL, IBM ClearCase, GitHub, vi editor, JIRA, Trello\nBoard, Valgrind, Perforce, LabVIEW, Visio, Enterprise Architect, gedit\nOperating Systems: Linux, Windows\nDatabases: Oracle […] Microsoft SQL Server, IBM DB2\n\nhttp://www.linkedin.com/in/ajayelango", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 38, "text": "Shaik Tazuddin\nSenior Process Executive - STAR India\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Shaik-Tazuddin/1366179051f145eb\n\nTo establish myself as a sincere and honest employee in a challenging organization by using my\nattitude and learning, thus enhancing my skills along with the growth of the organization.\n\nWORK EXPERIENCE\n\nSenior Process Executive\n\nSTAR India -  Bengaluru, Karnataka -\n\nNovember 2017 to Present\n\nSenior Process Executive - Cisco Client\nSTAR EMEAR &amp; US:\n➢ Creating Dart ID from the requested details and configuring products with appropriate pricing\n&amp; discounts.\n➢ Responsible for managing and analyzing backlog for the European countries with especial\nattention to France, Spain, United Kingdom, Italy, Sweden, Slovakia, Israel, Germany.\n➢ Reviewing quality figures, counts and Q flow monthly to ensure the targets are met.\n➢ Making report and C-SAT presentation regarding process for business development.\n➢ Immediate action on customer queries and escalations in order to meet quality standards.\n➢ To ensure 100% closure to all the customer queries, SLA adherence for self-productivity and\nquality.\n➢ Resolving issue and escalated cases for Europe and US countries.\n➢ Giving training for the new folks and support to quality team.\n➢ Overlap checking for the deals and AM assignment.\n➢ Promoted as SME where the responsibilities are to handle the team in the absence of Manager\nOR Team Lead and assigning the Work to the folks.\n\nSTAR- ANZ:\nStar ANZ deals with the direct partners of Cisco and the indirect distributors from the Australia\nand New Zealand, where they require deal ids for their transactions to purchase the products\nfrom cisco.\nThese direct partners and the distributors contacts us to create the deal ids with the products,\nquantity, end user and partners name and the discount.\nStar ANZ with all these creates the deal ids with the approval of account managers of the cisco\nof the respected account with the help of the various tools provided by cisco for example- CCW,\nSFDC, sales force etc.\nAnd contacting all the requesters through emails and through chat.\n\nSTAR- INDIA:\nStar India deals with the Direct partners and the Account managers of the Cisco. Deal ids created\nwill be created as per the request from the partners and the Account managers for ordering\npurpose of the Cisco products and the same will be informed to them through E-mail or Chat\nor through Phone.\n\nhttps://www.indeed.com/r/Shaik-Tazuddin/1366179051f145eb?isid=rex-download&ikw=download-top&co=IN\n\n\nSenior process executive at Infosys bpm limited\n\nInfosys BPM -  Bangalore Rural, Karnataka -\n\nNovember 2015 to Present\n\nDeal management\n\n-\n\nNovember 2015 to October 2016\n\nEDUCATION\n\nB.Com in C.A\n\nS.V University -  Tirupati, Andhra Pradesh\n\n2015\n\nEducation, A.P\n\nMargadarshi Junior College\n\n2012\n\nSSC\n\nBoard Of Secondary Education\n\n2010\n\nSKILLS\n\nHTML (Less than 1 year), MS OFFICE (Less than 1 year), Tally (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nCOMPUTER SKILLS\nPackages: MS Office, HTML, TALLY\n\nPERSONAL STRENGTHS\n➢ Dedication towards work.\n➢ Quick learner and self motivated.\n➢ Good Communication Skills and Personality.\n➢ Positive attitude.\n➢ Willing to spend more time.", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 39, "text": "Angad Waghmare\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Angad-Waghmare/42aa9e8655a5f7a3\n\n➢ Total 32 month of Experience in multiple segments of IT field as Hardware/Desktop Engineer..\n➢ Microsoft Certified Professional in Managing and Maintaining Microsoft Windows Server\n2008Environment […]\n➢ Installation of the Server and Client infrastructure.\n➢ Hardware troubleshooting.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nNetsol technology (Pune) -  Pune, Maharashtra -\n\nSeptember 2016 to Present\n\nDesignation:. System Engg at Tooltech Global Engineering Pune pvt ltd.\n➢ Skilled Installation, Configuration and Administration of Microsoft Windows products like\nWindows server 2003, Windows Server 2008, Windows 7, Win XP.\n➢ Server &amp; client environment for clients in Leading banking and software industry.\n➢ User administration, File server administration, Disk quota management.\n➢ Providing hardware &amp; network support to all clients, Installation &amp; Configuration.\n\n➢ Hardware Maintenance.\n➢ Internet Connection Sharing using Routing services on Windows server.\n➢ Data Backups and Restoration, Managing NTFS Security, Permission.\n➢ Configuring &amp; Managing Print Server, printer administration.\n➢ Software installation and updating system patches.\n➢ Setup, configure small LAN &amp; share printers at the customer end as per requirement.\n➢ Ownership of Helpdesk tickets till its closure\n➢ To co-ordinate with the appropriate teams for call resolution.\n➢ Attending to phone calls and giving appropriate answers to users.\n➢ Perform timely Patch management on Client systems as per the Schedule. (Saturday)\n➢ 10-15 System maintenance task on Saturday.\n➢ Implement change request by approval of System Administrator /Sr. System Administrator.\n➢ Coordination and communication with all System Administrator /Sr. System Administrator on\ncritical issues or helpdesk tickets.\n➢ Manage and monitor LAN/WAN network services.\n➢ Recommend and acquire equipment replacements and upgrades;\n➢ Maintain an inventory of all devices. (i.e. Computers, Laptops, Monitors, phones etc..)\n➢ Draft and maintain documentation for new changes implemented.\n➢ Perform routine audits of systems and software to avoid misuse of internet.\n➢ Troubleshoot any reported problems.\n➢ Every day fill up the daily completed and pending task report and sent to System Administrator /\nSr. System Administrator.\n➢ Complete and record day to day activity given by department.\n➢ Attend the department meeting which held on 4th Thursday every months.\n\nhttps://www.indeed.com/r/Angad-Waghmare/42aa9e8655a5f7a3?isid=rex-download&ikw=download-top&co=IN\n\n\n➢ Configure and manage and Troubleshooting of Design software i.e. AutoCAD, Pro-E, Solid works,\nCatia, corel draw, AutoCAD LT..Etc.\n\n➢ EDUCATIONAL QULIFICATION:\n\nDesktop support egg. At Infosys Pune\n\nGayatri Infotech Pvt. Ltd -  Pune, Maharashtra -\n\nJuly 2011 to January 2012\n\nThe company is working as Authorized Service Providers for leading computer manufacturers\nbrand ACER. The company Provides hardware and network support to various clients from\nBanking and software Industry.\n\nSKILLS\n\nactive directory (Less than 1 year), DHCP (Less than 1 year), DNS. (Less than 1 year), Hardware\ntroubleshooting. (Less than 1 year), Windows Server 2008 (1 year)\n\nADDITIONAL INFORMATION\n\n♦ operating Systems\nMICROSOFT: Windows server 2008\n• Build Windows Active Directory. DNS.\n• DHCP Management.\n• Diagnosed and corrected medium problems in current network\n• User Management and Management of Permissions according to requirement\n• File System Management.\n• Advanced Hardware/Software Installation, configuration and Management.\n• Hardware troubleshooting\nMICROSOFT: Windows XP, Window7, Window8 and Window10\n• Advanced Hardware/Software Installation, configuration and Management.\n• File System Management.\n• Hardware troubleshooting..\n\nPROFFESIONAL CERTIFICATION:\n• Microsoft Certified Windows server 2008 in active directory.", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 40, "text": "Sohan Dhakad\nShivpuri, Madhya Pradesh - Email me on Indeed: indeed.com/r/Sohan-\nDhakad/038dfd47a0cf071f\n\nServe the organization to apply my knowledge and skills to the best of\nmy efforts in order to achieve organizational goals and continuous\nlearning.\n\nWORK EXPERIENCE\n\nASSOCIATE TRAINEE OF INFOSYS PROJECT OF GST PORTAL\n\nGurgaon, Haryana -\n\nNovember 2016 to Present\n\nGURGAON.\n• Monitoring the problems faced for the registration.\n• Solving the problems of End User.\n• Generating reports of escalations &amp; SLAs.\n• Helping in issuance of Provisional IDs from GST.\n• Ensuring the Quality checks &amp; control.\n• Timely Delivery of resolutions for the End Users.\n• Handling and proper knowledge of GST Documents.\n• Ensuring SLA adherences and ensuring Companies\nRegulations &amp; Provisions.\n\nEDUCATION\n\nJiwaji University -  Shivpuri, Madhya Pradesh\n\n2016\n\nADDITIONAL INFORMATION\n\nParsuing cs profesional.\n\nhttps://www.indeed.com/r/Sohan-Dhakad/038dfd47a0cf071f?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Sohan-Dhakad/038dfd47a0cf071f?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 41, "text": "Madhava Konjeti\nHR Executive\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Madhava-\nKonjeti/964a277f6ace570c\n\nTo contribute to the team success by working in a positive environment with learning\norientation and a motive to grow professionally and personally.\n\nWilling to relocate to: andhra pradesh - karnataka - south India\n\nWORK EXPERIENCE\n\nHR Trainee Associate\n\nFounding Years Learning Solutions -  Bangalore Urban, Karnataka -\n\nAugust 2017 to Present\n\nResponsibilities :\n1.On boarding process\n2.Organising and coordinating with centres in pan India for new joiners joining formalities\n3.Updating data base with all new joiners information for payroll process\n4.Interacting with banks in order to open accounts for new joiners\n5.Actively taking part in other tasks of joining formalities\n\nHR Executive in Infosys\n\nRecruitment -\n\nSeptember 2016 to April 2017\n\n• Working with in the team to achieve overall team target\n• Screening the resumes\n• Interacting with candidates and assessing their skillsets\n• Organising interviews\n• Follow up\n\nPersonal Strength\n• Good inter personal skills\n• Confidence\n• Self-motivated\n• Creative\n• Presentation skills\n\nHR Intern\n\nCentral Mall -  Bangalore, Karnataka -\n\nSeptember 2016 to September 2016\n\n1month 10days\n\nhttps://www.indeed.com/r/Madhava-Konjeti/964a277f6ace570c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Madhava-Konjeti/964a277f6ace570c?isid=rex-download&ikw=download-top&co=IN\n\n\nBellandur (BBM Project)\n2. Aegis, ITPL, HR Intern 2months 4days\nBangalore (MBA project)\n3. Infosys HR Executive September2016 -April\n\nEDUCATION\n\nMBA\n\nVisvesvaraya Technological University\n\n2014 to 2016\n\nBBM in education\n\nNew Horizon College, (Bangalore University) -  Bangalore, Karnataka\n\n2010 to 2013\n\nS.S.C\n\nVijnana Vihara School\n\nAugust 2007\n\nSKILLS\n\nexcel, powerpoint, vlookup, formula, filters, paint, recruitment, (1 year)\n\nADDITIONAL INFORMATION\n\nLooking to joining in profiles relating to payroll or recruitment or operations in HR domain", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 42, "text": "Shreya Agnihotri\nSenior System Engineer at Infosys Limited - Infosys\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Shreya-Agnihotri/\nc1755567027a0205\n\n• Having 2.7 years of experience in Web Application design using python Django\nframework.\n• Highly experienced and skilled Agile Developer with a strong record of excellent\nteamwork and successful coding project management.\n• Good knowledge in python, Elasticsearch, Django using HTML5, MYSQL, JavaScript,\njQuery.\n• Performed the role of team member effectively. Involved in requirement gathering and\nanalysis of the requirements in technical perspective.\n• Extensively worked on software in all the phases including Design, Development,\nImplementation, Integration and Testing.\n• Possesses good analytical, logical ability and systematic approach to problem analysis,\nstrong debugging and troubleshooting skills.\n• Working on classic software development models along Agile Methodologies.\n\nWORK EXPERIENCE\n\nSenior System Engineer at Infosys Limited\n\nInfosys -\n\nJanuary 2016 to Present\n\ntraining program.\n• Working as Senior System Engineer at Infosys Limited from January 2016 to till date.\n\nEDUCATION\n\nB.Tech in ECE\n\nGalgotias University\n\nSKILLS\n\nAjax (Less than 1 year), APACHE KAFKA (Less than 1 year), HTML5 (2 years), Java (2 years),\nSQL (2 years)\n\nADDITIONAL INFORMATION\n\nTechnical Profile:\n\nWeb Technologies Python, JAVA, HTML5.\n\nFrameworks Django framework.\n\nhttps://www.indeed.com/r/Shreya-Agnihotri/c1755567027a0205?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Shreya-Agnihotri/c1755567027a0205?isid=rex-download&ikw=download-top&co=IN\n\n\nProgramming Languages Python, Java.\n\nScripting Language jQuery, JavaScript, Ajax.\n\nOperating Systems: Linux\n\nDatabases: SQL, kafka.\n\nTools and Utilities: Elasticsearch, Prometheus, Grafana, kibana, Docker", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 43, "text": "Tapan kumar Nayak\nBhubaneshwar, Orissa - Email me on Indeed: indeed.com/r/Tapan-kumar-Nayak/\nda1f5ffb3c4c4b17\n\nWORK EXPERIENCE\n\n10years G4S Security in Infosys\n\nG4S -  Bhubaneshwar, Orissa -\n\nFebruary 2009 to May 2018\n\nI am fire training. Usha fire training Bombay\n\nhttps://www.indeed.com/r/Tapan-kumar-Nayak/da1f5ffb3c4c4b17?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Tapan-kumar-Nayak/da1f5ffb3c4c4b17?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 44, "text": "Arpit Jain\nQuality Analyst - ThoughtWorks Technologies\n\nPune - Email me on Indeed: indeed.com/r/Arpit-Jain/3714fe32f98b03a9\n\n• Quality Analyst with a total experience of 4 years in delivering quality software\n• Expertise in Agile Development Practices.\n• Experience with Continuous Integration &amp; Delivery\n• Worked with Product Owners &amp; Business Analysts to incorporate the quality from initial\nphases\n\n• Awarded for going extra miles and making project deliveries possible on defined timelines\n• Active participation in recruitment activities\n• Organizer for the events for Quality Enthusiasts - vodQA, Pune\n\nSkills\nManual Testing\n\nJavaScript\n\nSelenium WebDriver\n\nJASMINE\n\nGitHub\n\nProtractor\n\nJava\n\nMYSQL\n\nAPI Testing\n\nWilling to relocate to: Pune, Maharashtra - NCR, Delhi\n\nWORK EXPERIENCE\n\nQuality Analyst\n\nThoughtWorks Technologies -\n\nApril 2016 to Present\n\nAlternative Assets Management\nResponsibilities:\n• Test Strategy Planning and Designing\n• Active Involvement in the Product Release Planning\n• Leading a team of 4 QA's\n• UI testing on Mobile Browsers and Desktop Browsers\n\nhttps://www.indeed.com/r/Arpit-Jain/3714fe32f98b03a9?isid=rex-download&ikw=download-top&co=IN\n\n\n• Functional Automation Testing using Protractor and Selenium WebDriver\n• Iteration Management and working closely with Product Owners\n• API Testing using Frisby and Postman\n• Performance Testing using JMeter\n• Security Testing using ZAP\n\nTest Engineer (Infosys Ltd\n\nThoughtWorks Technologies -\n\nJune 2014 to March 2016\n\nPharmaceuticals\nResponsibilities:\n• White-Box Testing of Optimization Algorithms\n• Test Data Management\n• Test Data Generation using Pandas and JavaScript libraries\n• Functional Automation Testing using Protractor\n• Web performance metrics collector using Phantomas\nJun 2014 - Mar 2016 Test Engineer (Infosys Ltd.) Project Domain: Banking\nResponsibilities:\n• Test Scenario and Test Case Preparation\n• Planning QA Activities and performing Regression Testing and UI Testing\n• Automating manual tasks by writing python scripts\n\nQuality Analyst\n\nThoughtWorks Technologies\n\nEDUCATION\n\nB.Tech\n\nJaypee Institute Of Information Technology -  Noida, Uttar Pradesh\n\n2010 to 2014\n\nCBSE\n\nGreen Valley Public School -  Bhilwara, Rajasthan\n\n2010\n\nCBSE\n\nNew Look Central School -  Bhilwara, Rajasthan\n\n2008\n\nSKILLS\n\nManual Testing, Protractor, Selenium Webdriver, Automation Testing, API Testing\n\n\n\nLINKS\n\nhttps://github.com/jainarpit\n\nhttps://www.linkedin.com/in/arpitj2402\n\nhttps://github.com/jainarpit\nhttps://www.linkedin.com/in/arpitj2402", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 45, "text": "Palani S\nSenior Technology Support Executive at Infosys - Development\n\nPorur, TAMIL NADU, IN - Email me on Indeed: indeed.com/r/Palani-S/d3b2e79f56262868\n\nTo seek a position in your organization where I can apply my knowledge &amp; creativity, acquire\nnew skills &amp; contribute effectively to the benefit of the organization and also my professional\ngrowth.\n\nWORK EXPERIENCE\n\nSenior Technology Support Executive at Infosys\n\nDevelopment -  Chennai, Tamil Nadu -\n\nMay 2015 to Present\n\nWORK EXPERINCE\n• Currently Working as Senior Technology Support Executive at Infosys, Chennai from May 2015\nto till date.\n\nSoftware Engineer\n\nAmpark Solutions Ltd -\n\nOctober 2012 to July 2014\n\nJob Responsibilities\n\n• Developing Controllers classes in java using Spring 3.2 Framework.\n• Created and manipulated JDBC connection using DAO Implementation class.\n• Implemented Business logics using service Implementation class.\n• Creating Tables and generating Sql Queries.\n• Testing, installing and monitoring new systems.\n• Preparing documentation and presenting progress reports to customers.\n• Knowledge in User Interface development using XML and HTML, Experience in installing,\ndeploying and testing with Application Servers like Tomcat 7.0, Apache 1.3/2.0.\n• Experience in preparing reports and creating defects in HP QC.\n• Knowledge in creating Lists, Libraries and and import data from excel to the custom lists in\nsharepoint site.\n\nPROFESSIONAL EXPERIENCE\n#Project 1: Finacle Knowledge Centre (FKC)\nEnvironment: C#, SharePoint 2013, SQL 2012\nRoles: Development, Client/Server Side Validation &amp; Maintenance.\n\nProject Details: Finacle Knowledge Centre is the re-engineered version of\nFinacle Help and Support Portal which is dedicated to offer customized support\nexperience to Finacle clients. The solution offers real-time, secure access to\nFinacle product knowledge and relevant support information's.\n\nhttps://www.indeed.com/r/Palani-S/d3b2e79f56262868?isid=rex-download&ikw=download-top&co=IN\n\n\n#Project 2: Container Tracking.\nEnvironment: Java, JDBC, Spring Framework, SQL, Eclipse,\nTomcat.\nRoles: Developer.\nProject Details: This System is an Exhibit of real time solution to keep track of containers and\nmake moves print document which considered being the best logistics solution for the client.\n\n#Project 3: Care Radius-Interface Module.\nEnvironment: Oracle SQL Developer (Oracle 9i), MS SQL Server 2008 R2, SSIS Packages, MS\nOffice, Service Remedy.\nRoles: Technical Support.\nProject Details: It is a complete medical management system, solution developed by EXL landcorp\nand used by HORIZON for providing end to end member's care management and the entire data\nare stored in and maintain in our database and each data is secured by our system. Key workflows\nin CR is Authorization, Appeals, case management, disease management, member compliance,\nprovider compliance.\n#Project 4: Grigora (Custom Tool)\nEnvironment: Java, JDBC, Spring Framework, MySQL, Eclipse,\nTomcat, MySQL Workbench.\nRoles: Developer.\n\nProject Details: This System is an Exhibit of real time solution to create new user for an application.\nThe System provides interfaces for assigning application specific roles to the user. Additionally,\nthe System allows the user to create workflow template to handle application functionalities.\n\n* TECHINICAL KNOWLEDGE\n\nProgramming Languages: Java 7.\nWeb Centric Tech: JDBC\nFramework: Spring Version 3.2\nDatabase: SQL, MONGO DB\nIDE: Eclipse\nTools: Hp-QC\n\nEDUCATION\n\nMCA in Engineering\n\nAnna University -  Chennai, Tamil Nadu\n\nDecember 2009\n\nB.SC.\n\nMS University -  Tirunelveli, Tamil Nadu\n\nSeptember 2006\n\nHSC\n\n\n\nSri Kanna Matriculation Higher Secondary School\n\n2006\n\nSSLC\n\nSri Kanna Matriculation Higher Secondary School\n\n2004", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 46, "text": "Meenalochani Kondya\nSystems Engineer, Infosys Bangalore\n\nBangalore Urban, Karnataka - Email me on Indeed: indeed.com/r/Meenalochani-\nKondya/81e406bd03e7a6d2\n\n• Completed M.Tech Software Systems with specialization in Data Analytics through Work\nIntegrated Learning Program of Birla Institute of Technology and Science, Pilani in 2017\n• Placed in Infosys at our college campus and attended 4 months Internship program between\nFebruary and May 2013 with Infosys at their Mysore campus\n• Over 4.5 years of software development experience in web technologies using .NET\nFramework […] and SQL Server 2012\n• In depth knowledge of .NET Framework including C#, ASP.NET, MVC 4/5\n• Good experience of Database Design and Software Development Life Cycle (SDLC)\n• Experienced in converting the Business Requirements into Technical Specifications\n• Good understanding of object oriented design and programming\n• Excellent team player as well as a team leader with problem-solving and trouble-shooting\ncapabilities\n\nWilling to relocate to: Bangalore Urban, Karnataka\n\nWORK EXPERIENCE\n\nSenior Systems Engineer\n\nInfosys\n\nSoftware development\n\nSystems Engineer, Infosys Hyderabad\n\nGas Request -  Hyderabad, Telangana -\n\nDecember 2014 to June 2015\n\nDecember 2014 to June2015\nThis project involved creating a new application for the employees to record their in- timings and\nout-timings and allowing them to regularize their working hours.\nDESCRIPTION This web application was developed using MVC4, HTML5, JavaScript, MS-SQL 2012,\nASP.NET and some open source Javascripts like Slickgrid.\nAs a developer, my responsibility in this project included design, development,\nreviews and testing.\n\nGas Request\n(As Systems Engineer, Infosys Hyderabad)\n\nSouthern Gas\nCLIENT Southern Gas, a wholly owned subsidiary of Atlanta Gas Light Resources (NYSE:\nGAS), provides natural gas delivery service to more than 1.5 million customers in\nGeorgia.\n\nhttps://www.indeed.com/r/Meenalochani-Kondya/81e406bd03e7a6d2?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Meenalochani-Kondya/81e406bd03e7a6d2?isid=rex-download&ikw=download-top&co=IN\n\n\nROLE\nDeveloper\nPERIOD\nOctober 2013 to November 2014\n\nThis project involved creating a new application that accomplished the following- 1. Enabled the\ncustomers to raise gas connection requests and register their\ncomplaints for failures in the gas connections.\n2. Intimated the contract employees of AGL for repairing the faulty gas\nconnections.\n3. Enabled tracking of their processes by capturing their analysis and repair\nDESCRIPTION status data.\nThe application enabled the employees to function offline also in remote areas through periodical\nsynchronizing of remote databases as and when connectivity\nbecame re-established\nThis web application was developed using MVC4, HTML5, JavaScript, MS-SQL\n\ndeveloper, my responsibility\n\n-\n\n2012 to 2012\n\nin this project included design, development, unit and system testing.\n\nMEENALOCHANI HARI KONDYA (.Net Developer)\nMobile: +91 9500615962\nE-mail: <EMAIL>\n\nEDUCATION\n\nM Tech in Technology & Science\n\nBirla Institute -  Pilani, Rajasthan\n\n2017\n\nB Tech in Sri Ramakrishna\n\nAnna University\n\n2013\n\nEducation\n\nBoard of Secondary -  Coimbatore, Tamil Nadu\n\n2009\n\nUniversity u\n\nTechnology\n\n\n\nEngineering College -  Coimbatore, Tamil Nadu\n\nState Board\n\nMatriculation School -  Coimbatore, Tamil Nadu\n\nSKILLS\n\nASP (1 year), ASP.NET (1 year), MS ASP (1 year), .NET (1 year), ENGINEER (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTechnical Skills\n\nLanguages C#, MVC 4.0, ASP.NET, JavaScript, JQuery, AJAX, Windows Services, SQL\nServer 2012\nIDE MS Visual Studio 2015, SQL server 2012\nWeb Authoring Tools HTML 5, CSS 3.0, XML\nMicrosoft Office MS PowerPoint, MS Word, MS Visio, MS Excel\nOperating Systems Microsoft Windows\nSource Code TFS 2013, VSS 2012\nManagement\n\nMEENALOCHANI HARI KONDYA (.Net Developer)\nMobile: +91 […]\nE-mail: <EMAIL>\n\nProject Details\n\nDoodle Web\n(As Senior Systems Engineer, Infosys Bangalore)\n\nSouthern Gas\nCLIENT Southern Gas, a wholly owned subsidiary of Atlanta Gas Light Resources\n(NYSE: GAS), provides natural gas delivery service to more than 1.5 million\ncustomers in Georgia.", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 47, "text": "Shrinidhi Selva Kumar\nNOC and QA Engineer at Skava an Infosys Company - Data Mining\n\nCoimbatore, Tamil Nadu - Email me on Indeed: indeed.com/r/Shrinidhi-Selva-\nKumar/50d8e59fabb41a63\n\nWilling to relocate to: Chennai, Tamil Nadu - Bangalore, Karnataka - Coimbatore, Tamil Nadu\n\nWORK EXPERIENCE\n\nNOC and QA Engineer at Skava an Infosys Company\n\nData Mining -\n\nAugust 2016 to Present\n\nAREAS OF INTEREST\n• Networking\n• Software Testing\n• Data Mining\n\nSKILL SET\nLanguages C, C++, Java\nDatabase MySQL\nPlatforms Linux, Windows\nTools JIRA, Application Manager, AppDynamics, AWS, Ecllipse\nWeb Technologies HTML, PHP, jQuery, AJAX\n\nINTERN DETAILS\n• Training at Mazenet Solutions on \".NET\n\nEDUCATION\n\nB.E in CSE\n\nTejaa Shakthi Institute of Technology for Womens\n\nJune 2015\n\nKongu Vellalar Mat. Hr. Sec School\n\n2009\n\nBoard/ University\n\nStatistics\n\nDid Compering in College Events\n\nhttps://www.indeed.com/r/Shrinidhi-Selva-Kumar/50d8e59fabb41a63?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Shrinidhi-Selva-Kumar/50d8e59fabb41a63?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 48, "text": "Mayank Shukla\nInfosys group as a Test Analyst - Infosys Ltd/Edgeverve Systems Ltd\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Mayank-Shukla/3c6042bd141ad353\n\n❖ 3+ years of experience in Automation Testing using TOSCA and selenium.\n❖ Worked in BFSI domain with different software engineering models like Agile methodology,\nWaterfall methodology etc.\n❖ Knowledge of project management tools like IBM CLM.\n❖ Received \"Award of Excellence\" from Tricentis for Automation testing using TOSCA.\n\nWilling to relocate to: Noida, Uttar Pradesh - Gurgaon, Haryana - Bengaluru, Karnataka\n\nWORK EXPERIENCE\n\nInfosys group as a Test Analyst\n\nInfosys Ltd/Edgeverve Systems Ltd -\n\nJanuary 2015 to Present\n\nary 2015 and got promoted to Test Analyst. Originally started with Infosys Ltd and currently\nworking in Infosys's products subsidiary Edgeverve Systems Ltd for Infosys Finacle.\n❖ Performing automation testing using agile methodology for projects like Finacle Core Banking\nSolution and Finacle CRM Solution.\n❖ Worked on automation testing using Selenium and TOSCA.\n❖ Worked for Agile Release Train (ART) in Scaled Agile Framework using IBM RCLM tool.\n❖ Working closely with Developers in Scrum Based software development model to prepare test\nplans based upon testing scope.\n❖ Conducting functional, regression and end to end testing using IBM Rational Quality Manager\ncross platforms using various APIs.\n❖ Experience in Web services testing and RESTful API testing.\n❖ Worked on distributed execution technique for Automation Testing using Jenkins.\n❖ Experience in Oracle Database, SQL queries, SQL Loader, SQL Plus\n❖ Involved in performance testing using batch processing on UNIX server.\n❖ Involved in defect logging, tracking and reporting using IBM Change and Configuration\nManagement (CCM) application on Jazz platform.\n❖ Worked on live project for Finacle Core Banking solution to provide Beta Quality Certifications\nfor the enhancements on the running solution.\n\nEDUCATION\n\nMBA in Banking and Finance\n\nManipal University -  Bengaluru, Karnataka\n\n2014\n\nB.Tech. in Information Technology\n\nhttps://www.indeed.com/r/Mayank-Shukla/3c6042bd141ad353?isid=rex-download&ikw=download-top&co=IN\n\n\nSaroj Institute of Technology & Management -  Lucknow, Uttar Pradesh\n\n2011\n\nLucknow Public Inter College -  Lucknow, Uttar Pradesh\n\n2005\n\nDIVISION\n\nSKILLS\n\nCRM (3 years), DATABASE (3 years), ORACLE (3 years), Tosca (3 years), Automation Testing (3\nyears), Selenium (1 year), Core Java (1 year)\n\nADDITIONAL INFORMATION\n\nKey Skills:\n❖ Software tools: IBM Rational Collaborative Lifecycle Management\n❖ Testing Tool: IBM Rational Quality Management on Jazz Server\n❖ Test Automation Tools: TOSCA, Selenium\n❖ Programming Language: Core Java\n❖ IDE: Eclipse\n❖ Database: Oracle, EDB, Sqlserver\n❖ Database Tools: SQL Developer, Toad, Tora\n❖ Software tools: Filezilla, MobaXterm, Putty, Office tools\n❖ Platforms: Windows, UNIX\n❖ Domain Software Knowledge: Finacle Core Banking Solution, Finacle CRM Solution.\n\nSkills: Fast learner, leadership quality, team player, presentation skills, work devotee, punctual,\ngood communication and listening skills.", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 49, "text": "Shraddha Achar\nMathura, Uttar Pradesh - Email me on Indeed: indeed.com/r/Shraddha-Achar/\nd6d4e3c0237ccc6c\n\nWORK EXPERIENCE\n\n2012 pass out with 3 months trining at Infosys\n\nIndividual -  Mangalore, Karnataka -\n\nOctober 2009 to November 2010\n\nEffectiveness Lab, Organizational Effectiveness Labs and Immersive Group Workshop conducted\nby I-Point Consulting Services Private Limited during the Academic Years 2009-10 &amp;\n2010-11.\n\n• Successfully completed ' Feel Employable' Learning And Development Intervention conducted\nby AIM INSIGHTS, Mangalore\n\n7 Seminars given:\n• \" Enzymatic Hydrolysis of Complex Carbohydrates to produce Fermentable and Digestible\nSugars\".\n8 Seminars attended:\n• Seminar on 'Microbial Polysaccarides'\n\n9 Computer Languages Known:\n• C programming basics\n• Core java\n• SQL/PLSQL\n\nEDUCATION\n\nBE\n\nNMAM Institute of Technology\n\n2008 to 2012\n\nPoorna Prajna PU College Admar\n\n2007 to 2008\n\nGanapathi High School Padubidri\n\n2005 to 2006\n\nADDITIONAL INFORMATION\n\n10 Skill Sets\n• Quick learner and team player\n\nhttps://www.indeed.com/r/Shraddha-Achar/d6d4e3c0237ccc6c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Shraddha-Achar/d6d4e3c0237ccc6c?isid=rex-download&ikw=download-top&co=IN\n\n\n• Good communication skills\n• Art of convincing, mind skills&amp; Public speaking", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 50, "text": "Arpit Godha\nSenior Process Executive\n\nJaipur, Rajasthan - Email me on Indeed: indeed.com/r/Arpit-Godha/4c363189fbff3de8\n\n-\n\nTo be associated with a progressive and growing organization and utilize my knowledge and skill\nto add value to self and the organization.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSenior Process Executive\n\nAccenture Services Pvt. Ltd -\n\nFebruary 2016 to March 2018\n\nRoles &amp; Responsibilities: -\nPlanning and Organizing: Handling and Planning team activities, priorities and handle\ncontingencies to meet the set goal.\nCustomer Orientation: Builds good report with the customer by understanding and responding\nto their needs and acting as a trusted advisor.\nCustomer Interaction: Handle escalation at the process level in order to ensure high level\ncustomer satisfaction.\nAnalytical Ability: Analyze and resolve the problem by identifying the elements and relationship\nof a problem in a systematic way.\nKnowledge and Operation Management: Knowledge of operation to implement the process with\nguidance and manage day to day operation.\nProcess Reengineering: Participate in the ideation process and produce documentation and train\nthe team on reengineered process in order to meet the client and internal commitments on a\ncontinuous basis.\nReporting: Prepare Volume Tracer, Audit Tracker, Utilization Tracker, Dashboard and SLA Tracker\nreport to the supervisor.\nOwnership: Always complete the given task before the TAT with accuracy and used to do the QC\nas well of other team members.\nLeading KT Session: Took many sessions on call with the client and understand the process or\nactivity.\nTrainer and Trainee: Give cross training to other resource for creating back up and take the\ntraining as well.\nInnovative: Give many automation ideas which reduce the AHT of the activity and give benefit\nto the client as well.\nWork with Different Regions: While working in Accenture I worked in so many countries like\nAustria, Spain, Germany and UK as well.\nPrepare the DTP: While RKT session with the client I understand all the activity and prepare many\nDTP/SOP.\nLeading Client Call: In Accenture I used to lead call on weekly basis regarding the SOM (State\nof Mind)\n\nhttps://www.indeed.com/r/Arpit-Godha/4c363189fbff3de8?isid=rex-download&ikw=download-top&co=IN\n\n\nWhiteboard: Prepare whiteboard to showcase the work status of the team regularly.\n\nKey Responsibilities in Operations: -\nCreating customer on ADMARC\nMaintain the customer master data\nBook the received orders from customers\nCreate delivery for the orders\nPrepare the AR Aging report\nPrepare the remittance for the special customers\nPrepare Pre-notification letter for pending invoices and send to the customers\nFollow-ups with the customers regarding the pending invoices\nPrepare the dunning letters for unpaid invoices\nApply payment on the customer's account as per the remittance\nWork on unapplied payments\nApply ACH (automatic clearing house) payment\nReconciliation between GL account and customer account on month end\n\nProcess Executive Infosys\n\n-\n\nJuly 2013 to February 2016\n\nEDUCATION\n\nB.COM\n\nUniversity of Rajasthan\n\n2011\n\nSenior secondary\n\n2008\n\nSKILLS\n\nTRAINING (2 years), CASH (Less than 1 year), EXCEL (Less than 1 year), SAP (Less than 1 year),\nWINDOWS 7 (Less than 1 year)\n\nADDITIONAL INFORMATION\n\n-\n\nGood communication skills\nExcellent logical reasoning\nInnovative and creative\nQuick learner\nGood knowledge of tools like (SAP, ADMARC, PPI)\nPositive attitude to words the problems and solving ability\n\n\n\nProjects Completed: -\n\nReduction in AHT for reports which resulted in $ savings for the client.\nReduction in AHT for Pre-notification letter which resulted client &amp; customer satisfaction.\n\nComputer Proficiency: -\n\nBasic Word, Excel and Power Point Presentation\nWindows XP and Windows 7\nGood knowledge of SAP\n\nProfessional Trainings Attended: -\n\nCompetencies based training: Milestone 2.0, Business communication, Walk the talk, Analytical\nability\nBusiness Excellence: OTC (Order to Cash)\nDomain Certification: T100\nQuality Based trainings: CTM", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 51, "text": "Jatin Arora\nSDET Automation Engineer, Infosys - CRD (Capital Group of Companies)\n\nPehowa, Haryana - Email me on Indeed: indeed.com/r/Jatin-Arora/a124b9609f62fbcb\n\n3.6 years of experience in Automation Testing tools like Selenium (C#), Java, dot Net Technology\nand other Testing tools like JIRA, QC. Proficient in testing on Client/Server and Web-based with\ngood experience in creating the Test Plan and testing framework\n\n• Experience in SQA including Selenium with data driven framework.\n• Experience in working on C# and dot net technologies.\n• Automated the Test data creation using Selenium as value added service for a client which is\nbeing used by the client and bought 30% cost reduction in data setup.\n• Developed Test Case Generator as value added services for client which reduced time and cost\nby\n45% being spent on data creation and provide more productivity.\n• Developed an Infosys Go Safe Android App during participation in Global Hackathon\n• Excellent communications skills and able to liaise with key customer contacts and vendors to\nresolve software issues.\n• Having excellent problem solving and analytical skills\n\nCompetencies Developed:\n\nSelenium WebDriver C# ADO.Net SQL HTML, CSS\nJava Unix Quality Center Atlassian JIRA MTM\nMicrosoft SQL Server Visual Studio Rapid SQL Toad Confluence\nEclipse Visual Studio BitBucket NUnit MindMaps\n\nTesting Skills:\nSDLC, STLC, Test Planning, Requirement Analysis, Agile Methodology, DevOps Methodology,\nScrum, Waterfall, Software Quality, Defect Tracking, Regression Testing, System Testing\n\nWORK EXPERIENCE\n\nSDET Automation Engineer, Infosys\n\nCRD (Capital Group of Companies) -  Chandigarh, Chandigarh -\n\nFebruary 2017 to Present\n\nCRD (Charles River Development IMS automates the compliance workflow and provides\ncentralized compliance monitoring and management. The highly scalable compliance engine\nsupports very high volumes of trades, compliance rules, accounts and group of accounts.\n\n• Design and implemented test scenarios, test cases, QA processes and procedures.\n• Automated and delivered high quality automation scripts using C#.\n• Writing SQL queries to validate data from database.\n• Used BitBucket for code repository.\n• Communication with stakeholder for business and functional requirements\n\nhttps://www.indeed.com/r/Jatin-Arora/a124b9609f62fbcb?isid=rex-download&ikw=download-top&co=IN\n\n\n• Perform requirements analysis and impact analysis for enhancements and changes\n• Creating weekly Test Unit creation and execution report\n\nInfosys Training, Test Engineer Trainee\n\nNext Generation Volume Licensing (Microsoft) -  Mysore, Karnataka -\n\nAugust 2014 to November 2014\n\nA detailed hands on training of C# and SQL concepts and testing training.\n\nTest Engineer, Infosys\n\nNext Generation Volume Licensing (Microsoft) -  Hyderabad, Telangana -\n\nApril 2014 to November 2014\n\nJan 2017\n\nProject Next Generation Volume Licensing (NGVL) is a strategic change initiative led by\nWorld Wide Licensing &amp; Pricing (WWLP), Operations, MSIT, and Worldwide Operations in\npartnership with EPG, SMS&amp;P, and WPG to transform the Volume Licensing business so that\nit can scale for the next 20 years, compete more quickly in new markets, and deliver a superior\nlicensing experience for our customers, partners, sellers, and operations personnel.\n\n• Designed and implemented test scenarios, test cases, QA processes and procedures.\n• Automated and delivered high quality automation scripts with Selenium using C#.\n• Preparing SQLs for validating data in database.\n• Communicated with stakeholder for business and functional requirements\n• Performed requirement analysis and impact analysis for enhancements and changes\n• Logging and tracking defects and preparing backlog report\n• Created weekly Test Unit creation and execution report\n• Performed peer reviews and estimates which helped to automate 95% web site automation\n\nEDUCATION\n\nB.Sc in Computer Science\n\nKurukshetra University -  Kurukshetra, Haryana\n\nTagore Public School -  Pehowa, Haryana", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 52, "text": "Karthik Gururaj\nTechnical Lead at Infosys Ltd. - Pharmaceuticals and Life Sciences\nDomain\n\n- Email me on Indeed: indeed.com/r/Karthik-Gururaj/a51f07b3eda3aa6c\n\nHighlights:\n\n• 90+ months of Professional Experience in Informatica, Datawarehousing, UNIX, SQL, Teradata\n• 8+ years of Domain Experience in Pharmaceuticals and Life Sciences\n• Passionate and enthusiastic towards Data, Data analysis and Data engineering.\n• In 2015, took over the application maintenance of the client data warehouse completely from\nthird party vendor and stabilized the partially defunct system in a very short span of time.\nThereby, winning a 5 year contract worth $12.5M with the Clients.\n• Managing a 4 member cross-functional team and also maintaining client operations on a day\nto day basis.\n• Implemented various enhancements and Changes to the Client DW/BI Application. The\nOptimization brought down 75% of costs in the overall application maintenance. The project was\nsuccessfully downsized from a 18 member team to a 6 member team in a span of 4 months.\n• Awarded with a 'APPLUASE AWARD' within 4 months of joining the Deloitte and received\nnumerous Apreciation for the excellent work done in terms of showcasing Technical and People\nmanagement skills.\n• Was awarded 'Star Performer of the month' for successive months during my tenure at\nAccenture.\n• Experienced on both Support and Development environments.\n• Received various distinctive appreciations on Root cause analysis and asset development\nactivities.\n• Actively involved in technical presentations and various on-stage sessions.\n• Currently a counselling member for new joiners for orientation programs.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nTechnical Lead at Infosys Ltd.\n\nPharmaceuticals and Life Sciences Domain -\n\nMay 2017 to Present\n\nHave been taking care of Oncology Datamart with Several Oncology Data Requests and Data\nAnalysis for the past 3+ Years.\n• Currently working as a Technical Lead at Infosys Ltd. From May 2017- till date.\n• Worked as a Consultant (Technology) at Deloitte Consulting India Pvt. Ltd, from March 2015\n- January 2017.\n\nAccenture India Pvt. Ltd -\n\nDecember 2009 to March 2015\n\nhttps://www.indeed.com/r/Karthik-Gururaj/a51f07b3eda3aa6c?isid=rex-download&ikw=download-top&co=IN\n\n\n63 Months)\n• Presently working on Informatica, UNIX, SQL, Teradata, Salesforce (Veeva Systems)\n• Worked on creating STM/Design documents / data modelling.\n• Competency in Structured Query Language (SQL), Informatica, UNIX and ETL.\n• Worked as an Individual contributor driving an End to End project implementation.\n• Have experience in handling very critical data and challenging deadlines.\n\nEDUCATION\n\nMasters of Science in Comp. Technology\n\nPSG College of Technology -  Coimbatore, Tamil Nadu\n\nMay 2009\n\nBachelor of Science in Comp. Technology\n\nPSG College of Technology -  Coimbatore, Tamil Nadu\n\nMay 2007\n\nHSC\n\nSBOA Mat. Hr. Sec School -  Coimbatore, Tamil Nadu\n\nMarch 2004\n\nSSLC\n\nSBOA Mat. Hr. Sec School -  Coimbatore, Tamil Nadu\n\nMarch 2002", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 53, "text": "Akila Mohideen\nSystem Engineer, Infosys Limited, India - INFOSYS LIMITED COMPANY\n\nBangalore, Karnataka - Email me on Indeed: indeed.com/r/Akila-Mohideen/cfe2854527fb6a12\n\nWilling to relocate\n\nWORK EXPERIENCE\n\nSystem Engineer, Infosys Limited, India\n\nINFOSYS LIMITED COMPANY -  Bangalore, Karnataka -\n\nMay 2015 to Present\n\nBANGALORE, India - Multinational company that provides business consulting, Information\ntechnology and outsourcing services.\nSystem Engineer, Infosys Limited, India from 5/2015 to Present\n\nSelected Accomplishments:\n● Developed a PI interface, using the File adapter for sender communication and SFTP adapter,\nusing external massage type of type XSD, for the receiver communication, allowing WEB based\ninternal Purchasing punch out catalog system to communicate with an EDI system for 850, 855\nand 856 message type.\n\n● Monitored the SAP PI 7.4 dual stack system on a daily basis to resolve system or data issue\noccurring in the interfaces in a timely manner. Provided third level production support on issues\narising with SAP PI interfaces to the third party help desk partner.\n\n● Developed a PI interface between NXTrend and SAP for payroll runs for AP as well as vendor\npayments. The adapters used were File adapter for the sender communication picking up of a\ncomma separated value file and transforming the information into XML-IDoc utilizing the IDoc\nadapter.\n\nAkila Sulfa Sulthan Mohideen ● Phone: +91-888-4350-386\n\n● Provide PI/PO technical support for patch and release upgrades. Document and maintain a\nsolution portfolio and identify opportunities for solution re-use. Identifies opportunities to re-\nengineer existing solutions to better utilize technology, optimize performance, and simplify\nsupport. Manage and coordinate work of consultants when necessary.\n\nTechnology\nSoftware: SAP PI, NWDS, JAVA, ITSM tool, Excel Report tool, Regression Testing tool, MS Office\n(Word, Access, Excel, PowerPoint)\n\nEDUCATION\n\nBachelor of Technology in Information Technology\n\nhttps://www.indeed.com/r/Akila-Mohideen/cfe2854527fb6a12?isid=rex-download&ikw=download-top&co=IN\n\n\nPSG COLLEGE OF TECHNOLOGY, ANNA UNIVERSITY -  Coimbatore, Tamil Nadu\n\nMay 2011\n\nADDITIONAL INFORMATION\n\nSkills\n\n● Corporate Communications\n● Creative Team Leadership\n● Adept problem solver\n\n• Development of Training Materials\n● Strong Analytical and logical skills", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 54, "text": "Ahmad Bardolia\nProject Lead for Infosys OpenStack\n\n- Email me on Indeed: indeed.com/r/Ahmad-Bardolia/8e2c49ea8e7dcd27\n\n• OpenStack Cloud implementation as a Project Lead for Infosys OpenStack triplO (Redhat\nDirector) Private cloud with OpenShift in India\n• OpenStack Cloud implementation as a Project Lead for Makemytrip OpenStack Private cloud\nin India\n• OpenStack Cloud implementation as a Project Lead for Solartis (Chennai, Madurai) OpenStack\nPrivate cloud in India\n• Team member of OpenStack Private cloud deployment of Cisco USA\n• Deployed Private cloud using OpenStack for Government (Bangalore Police) cloud data center\n• 6 POC on OpenStack private cloud\n• 3 production cloud deployments using OpenStack and kvm\n• Virtualization: Hyper-V, Esxi, KVM, Citrix Xen Server\n• Cloud: AWS, Azure, Google Cloud Platform (GCP), OpenStack, Red hat Cloud Director\n• AWS Cloud skills: EC2, EBS, Autoscaling, S3, cloud formation, RDS, DynamoDB, Route 53, AWS\nAPI, AWS python SDK, SNS, SQS, SWF, workspaces, Beanstalk, AWS redshift, AWS Elastic Map\nreduce\n• Cloud foundry: Developer and Deploying Pivotal cloud foundry on AWS, Google\n• OpenShift Enterprise: Implementing Redhat OpenShift Enterprise on Premise or on OpenStack\nor on AWS, Azure, OR on OpenStack\n• Azure Cloud Skills: Virtual machines, Azure Active Directory, Traffic Manager, Azure Storage,\nAzure storage, Azure networking, Implementing Azure solutions\n\n• Google Cloud Skills: hands on knowledge on Google compute engine, Migration to Google cloud,\nGCP - Container Engine-An Overview, GCP - Container Registry, GCP - Cloud Storage, GCP - Cloud\nDatastore, GCP - Cloud SQL, GCP - Networking, Load Balancing, GCP - Cloud DNS, GCP - Cloud\nDataproc ( Big Data service), GCP - Cloud IAM\n\n• Automation: Chef, Puppet and ansible essential\n\n• PaaS skills: Pivotal cloud foundry, Redhat OpenShift\n\n• Docker Skills: Docker essentials and Docker swarm advanced, Docker cloud, Docker cloud data\ncenter, GitHub integration for Docker, Kubernetes.\n\n• Version control: GitHub, Bit Bucket\n\n• DevOps Skill: Jenkins, Maven, SonarQube, Teamcity, Octopus, JFrog, Nexus\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nProject Lead for Infosys OpenStack\n\nOpenStack Cloud -  Chennai, Tamil Nadu\n\nhttps://www.indeed.com/r/Ahmad-Bardolia/8e2c49ea8e7dcd27?isid=rex-download&ikw=download-top&co=IN\n\n\nOpenStack Private cloud in India\n• Team member of OpenStack Private cloud deployment of Cisco USA\n• Deployed Private cloud using OpenStack for Government (Bangalore Police) cloud data center\n• 6 POC on OpenStack private cloud\n• 3 production cloud deployments using OpenStack and kvm\n• Virtualization: Hyper-V, Esxi, KVM, Citrix Xen Server\n• Cloud: AWS, Azure, Google Cloud Platform (GCP), OpenStack, Red hat Cloud Director\n• AWS Cloud skills: EC2, EBS, Autoscaling, S3, cloud formation, RDS, DynamoDB, Route 53, AWS\nAPI, AWS python SDK, SNS, SQS, SWF, workspaces, Beanstalk, AWS redshift, AWS Elastic Map\nreduce\n• Cloud foundry: Developer and Deploying Pivotal cloud foundry on AWS, Google\n• OpenShift Enterprise: Implementing Redhat OpenShift Enterprise on Premise or on OpenStack\nor on AWS, Azure, OR on OpenStack\n• Azure Cloud Skills: Virtual machines, Azure Active Directory, Traffic Manager, Azure Storage,\nAzure storage, Azure networking, Implementing Azure solutions\n• Google Cloud Skills: hands on knowledge on Google compute engine, Migration to Google cloud,\nGCP - Container Engine-An Overview, GCP - Container Registry, GCP - Cloud Storage, GCP - Cloud\nDatastore, GCP - Cloud SQL, GCP - Networking, Load Balancing, GCP - Cloud DNS, GCP - Cloud\nDataproc ( Big Data service), GCP - Cloud IAM\n\n• Automation: Chef, Puppet and ansible essential\n\n• Version control: GitHub, Bit Bucket\n\n• DevOps Skill: Jenkins, Maven, SonarQube, Teamcity, Octopus, JFrog, Nexus\n\nEDUCATION\n\nMaster's\n\nSKILLS\n\nSql Server,Cloud Computing,OpenStack, OpenShit, AWS,GCP,Azure, ClouStack,KVM,Hyper-\nV,Citrix Xen Server, ESXi, Chef, Ansible, Jenkins, Maven, Sonar Qube, Nexus, Teamcity, Octopus,\n(3 years)", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 55, "text": "Puran Mal\nJaipur, Rajasthan - Email me on Indeed: indeed.com/r/Puran-Mal/357ea77b3b002be6\n\nWORK EXPERIENCE\n\nAdmin assistant at Infosys limited\n\nFront office work\n\nEDUCATION\n\nBachelor's\n\nSKILLS\n\nFront office executive (1 year)\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILL\n• Basic Knowledge of Computer operating\n• MS OFFICE (MS excel, Ms Word and Power Point)\n• With typing speed 25 wpm\nPERSONAL SKILL\n• Positive Attitude\n• Good decision making\n• Good communication skills\n• Confident\n• Have zeal to learn new things\nHOBBY\n• Listening music\n• traveling\n\nhttps://www.indeed.com/r/Puran-Mal/357ea77b3b002be6?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 56, "text": "Sridevi H\nBangalore, Karnataka - Email me on Indeed: indeed.com/r/Sridevi-H/63703b24aaaa54e4\n\nTo further my career with a growth-oriented firm that will allow me to utilize my experience and\nknowledge as a Technical /Project Lead.\n\nWORK EXPERIENCE\n\nPrincipal System Engineer\n\nAricent Technologies\n\nInfosys\n\nTechnical Lead\n\nEDUCATION\n\nM.S in Software Systems\n\nBITS Pilani -  Pilani, Rajasthan\n\nB.E. in Computer Science\n\nBoard of Technical Education\n\nSKILLS\n\nNetworking/Platform/Drivers/Vxworks\n\nADDITIONAL INFORMATION\n\nKey Strengths\n• Over 15 years of experience in Ethernet Data Communication Technology Ethernet Routing\nSwitches, Metro Ethernet Routing Switches, Platform and Wireless\n• Good knowledge of Real Time operating Systems (Vxworks) and Drivers\n• Highly skilled in performing analytical and logic building functions to provide feasible solutions\nto problems\n• Brilliant interpersonal coordination and communication skills\n• Competent at leading, managing and training teams on different aspects of data\ncommunication systems\n\nTechnical Skill Set\nSYSTEMS WORKED ON * Data communication -Multi Service Transport Network Controller Cards,\nEthernet Routing Switch,\nDOMAIN KNOWLEGDE\n* Enterprise Router -L2, L3 protocols (MLT, LACP, BGP, STP, SMLT, SLPP, Provider Bridge, IPFIX,\nVRF, RSP Fastpath)\n\nhttps://www.indeed.com/r/Sridevi-H/63703b24aaaa54e4?isid=rex-download&ikw=download-top&co=IN\n\n\n* Platform -Task Architecture, Logging, , File System, Debugging tools, Crash analysis, Chassis\nManagement\n* Wireless- 802.11 Split AP Architecture, WMM 802.11 E\n\nOPERATING SYSTEMS and LANGUAGE PROFICIENCY/HARDWARE PLATFORM * Programming/\nLibrary/Platform - C, Visual Basics, Unix, RTOS-VxWorks, Windows, Power PC based processor,\nTOOLS\n* Clarify, Clearcase, Source Insight, Network Traffic Simulators like IXIA, Smart Bits, Network\nProtocol Analyzers like Ethereal and WireShark.\n\nDATABSE * Oracle, MS Access, MySQL\nREWARDS AND RECOGNITIONS\n* Spot Awards, Program Level Awards Infosys, Aricent\n* Individual Excellence Award 2010 Infosys\n* Leadership Quotient Award 2014 Aricent\n* Client Appreciation Award 2016 Aricent\n* Engineering Excellence Award 2016 Cisco\n\nSOFT SKILLS\nSoft skill trained and certified in the areas like Interpersonal Effectiveness, Client Interfacing\nSkills, Cross Cultural Skills,\n&amp;Team Management\n\nProjects Handled in Aricent\nProject 01: TNC Controller card for MSPP and MSTP Optical network\nDuration: From Date: Nov 2014 to till date\nDesignation: Principal System Engineer\nClient: Cisco\n\nRole:\n1. Design and Development of Network Driver/Pseudo Network Driver for new controller card with\nBroadcom SDK integration and Driver Implementation Tasks include Driver design for new Chip,\nBroadcom SDK compilation in VxWorks, Integration and Writing driver using Broadcom SDK.\n2. Design and Development of Broadcom Mini Driver Kit Integration in new controller card\n3. Design and Development of Features like GDT, ANSI-ETSI on Multishelf\n\n4. Front ending the Different Releases\n5. Technical Guidance and Backlog Reduction Activities\n6. Solving Critical Customer issues\nFew examples of critical issues solved in the TNC which fetched client appreciations\nFlash Mgr Stuck issue\nUsbMgr Stuck issue\nSilent Reboot caused in the standby controller\nIpv6 connectivity failure every 2-3 hours\nArp6Show causing crash in telnet session\n7. Client interaction and customer network/Test Team Network debugging\n8. Mentoring the team\n\nProject 02: VSM IPSec for ASR9K\n\n\n\nDuration: From Date: Feb 2013 to Oct 2014\nDesignation: Senior Technical Lead\nClient: Cisco\n\nSupport for IPsec on ASR9K routers. VSM Service module is extended to support IPSec Service\non ASR9K. In order to achieve the Time to market, in the first phase IPSec management, control\nand data plane are not integrated with IOS-XR. VSM card while plugged inside ASR9K chassis,\nwill provide the IPSec functionality. From an ingress LC, traffic requiring IPSec functionality\n(encryption or decryption) will be sent to VSM card. Upon completing the crypto operation, traffic\nwill be forwarded to the destination egress LC to be sent out of ASR9K system.\nRole:\n1. Initial Sessions for the team members on overall architecture\n2. Coordinating with team members and technical help to the team\n3. Initial investigation using Intel DPDK compilation, launch VMs etc.\n4. Initial investigation on High Availability Design\n5. Design/Development of Keep alive functionality between Control plane and Data plane agents\n6. Design/Development of integrating CDP in Control Plane\n7. Design/Development of having CDP in the Linux Data plane\n\nPrior Experience Outside Aricent\nCompany worked for: INFOSYS LIMITED\nDuration: From Date: 03 Sep 2007 To Date: 2 Jan 2013\nDesignation: Technology Lead\n\nProduct: MERS and ERS Routers\nClient: Avaya\nProject 01: Ethernet Routing Switch - North American OEM\nThe Ethernet Routing Switch is a proven, tested, resilient intelligent network solution that scales,\ndelivering hundreds of Gigabits per second and hundreds of millions of packets per second of\nperformance to the core. This flexible switch reduces the complexity of network design, making\nit ideal for midsize-to-large enterprise campuses and data centers. Its switching architecture is\nbased on Network Processing Units (NPU)\n\nThis project aims at design, development and sustenance of Enterprise Routers.\n\nResponsibilities/Activities\n• Bug Fixing and crash analysis in area of Routing and system architecture\n• Trouble Shooting the live customer network to understand the issue provide work around and\ncollect data\n• As Sustenance team member and lead resolved/supervised critical customer and design issues.\n- Critical customer issues in Area of Vxworks Stack, SSH, STP\ndevice drivers, BGP protocol, MLT, LACP, CFM\n\nSome good issues solved in different areas of the product which are in memory are\n- Solving tftp hang issue in Vxworks Stack\n- Providing task delete hook for the graceful task deletion for SSH Hang issue\n- Solving BGP indirect neighbor issue by trouble shooting live customer network\n- Providing analysis and solution to a non-reproducible SSH hang issue\n- Providing analysis on inter-op issue with proprietary IO card with CISCO\n\n\n\n- Fix for CF card crash issue\n- Quick solution provided in RSP Code for handling traffic on STP blocked port for IP Traffic.\n- Provided design/technical inputs for feature development \"Sys log Support through SSH\nPortforwarding \"\n- Supervised feature \"Federal IA Library\" design and implementation\n• Maintain and setting up of scaled up stability network to verify the Stability of the product\n• Training and mentoring new personnel in the project\n• Review of Designs, Enhancements &amp; Major Bug Fixes.\n-Provided Code /Design Review comments and testing guidance and testing scenarios.\n• Design Support to Product Test teams for newly developed features.\n• Design and Development of enhancements.\n• As a lead involved in tracking deliverables, estimation, status reporting (to the customer),\nproductivity and quality tracking.\n• Additionally providing consultation to test team in their deliverables\n• Ensuring high quality of deliverables through reviews and defect prevention.\n\nProject 02: Metro Ethernet Routing Switch - North American OEM\n\nResponsibilities/Activities\n• Worked in the various capacities -Technical Lead, Designer,\n• Resolving Critical customer issues in quick turnaround time\n• Driving the sustenance team size of 10 by providing the technical assistance and necessary\nsessions and trainings in turn increase the productivity of the team\n• Conducting weekly bug-scrub meetings to ensure the work in track\n• One of the key code reviewers in the program to ensure the proper quality of the product\ndelivered.\n• Defect analysis, Knowledge management are the other key responsibilities\n• Training and mentoring new personnel in the project.\n\nCompany worked for: HUAWEI TECHNOLOGIES\nDuration: From Date: 07 Jun 2004 To Date: 18 May 2007\nDesignation: SOFTWARE ENGINEER\n\nClient: Huawei China\n\nProject 01: DOPRA\n\nDescription of the project\nDOPRA is the platform for telecom products. It is a proprietary platform and has company internal\ncustomers. It has main two planes, system management and system service planes and several\nsub modules.\nI have successfully worked on the following modules.\n\nModule - MML INI Parser Tool\nMML is Configuration Tool used in communication equipments\nThis Configuration Tool has set of default and user defined commands. INI Parsing tool is a tool\nwhich accepts MML Commands in certain grammar and generates Resource file and Binary File\nrespectively which will be read by server to resister the commands.\nInvolved in the development, testing, verification of the module on windows.\n\n\n\nModule - Man Machine Language\nMML is a configuration tool widely used in communication equipments. It uses Client/Server\narchitecture. MML server communicates with both the client and the APP in the equipment.\nIt receives MML commands inputted by users from the client and relays it to APP to process\nit. After acquiring the processing result, the server generates a MML report and sends it back\nto the client. It interacts with other system modules. Connection Management, Authorization\nManagement, Command Process, Data Synchronization, Event Notification, Command Parsing,\nLOG/ALARM Handling are some of the functions of MML Server.\nInvolved in the development, defect fixing and verification of the module on windows, Linux and\nvxworks platforms Involved in several release activities.\n\nModule - Configuration Management\nConfiguration Management Module is core module of System Management. Its main function\nis to define specifications for configuration management interface between the configuration\ntool layer and the application layer, and provide related configuration command communication\nmechanism. Any information that can be modified by the user/application and that affects the\nfunctionality of the application can be regarded as configurable data. All configuration commands\ncan be sent to Configuration Management Module using a common interface type.\nConfiguration Data Management Module is a supplementary optional component that can be\nloaded to provide a persistent storage (&amp; retrieval) mechanism for applications' static\nconfiguration data.\nInvolved in the defect fixing and feature enhancement of the module on windows, Linux and\nvxworks platforms Involved in several release activities.\n\nModule - TL1\nTransaction Language 1 (TL1) is a widely used, \"legacy\", management protocol in\ntelecommunications. It is a cross-vendor, cross-technology man-machine language, and is\nwidely used to manage Synchronous Optical Networks (SONET) . It is defined for operation\nsystem/ network element (machine-to-machine) interfaces. TL1 corresponds to the User System\nLanguage (USL), which is the language for human-to machine interactions. It is used for managing\nnetwork elements in a network. TL1 Module is an interface between an Application and Client.\nClient sends the TL1 Commands to perform some configuration operations in the Applications.\nTL1 module process the client text TL1 commands according to standard specification TL1 GR\n831 and interacts with the Configuration management module and Convert Response messages\nback to TL1 Response messages\nInvolved in development, verification and customer and release activities. I was a module lead\nfor this project.\n\nModule - QX\nThe Qx interface is a company proprietary application layer protocol between target and the GUI\nof NMS. By this protocol, NE can communicate with SCC (System Communication and Control)\nsoftware in different operation system. Each command has defined format. Commands are given\nin binary form. Qx Module acts as an interface between the Client and Application.\nInvolved in development, verification and customer and release activities. I was a module lead\nfor this project.\n\nRole: Designer/Developer/ Reviewer/\n\n\n\nResponsibility: Designing and coding various modules Involvement in peer review and overall\nreview activities and project discussions Coordinating the development cycle Training team\nmembers and solving project related issues configuration management of the project using Clear\nCase\nDuration: From June 2004 to Sep 2006\nHardware: X86, CPCI, ATCA\nSoftware: C++, C\nProject2: Wireless LAN - 802.11 MAC (Split AP Architecture)\nDescription of the project\nWireless LAN 802.11 describes MAC and PHY. Wireless stations are connected to form a basic\nservice area which is controlled by a base station called Access Point. Access Point is a special\ndevice which basically acts as a bridge between wired and wireless media connected to a\ndistributed system generally Ethernet. Several Access Points can be connected to a distribution\nmedia to form an extended service set.\nIn split AP architecture AP's connect to a central controller called Access Controller in short\nAC. AP has the minimal functionalities such as receiving the 802.11 data from the radio and\nforwarding data and management frames to AC, Power management, Advertising beacons,\nhandling control frames. Management operations such as Association, Authentication, Handling\nframes, conversion between […] 802.3 will be done by AC.\nI was involved in the development of feature development, defect analysis, testing activities in\nthe CLI, Frame handling, Association and Beacon advertisement modules.\nModule: WLAN Support for WMM 802.11 E Qos Requirements\nWMM is a protocol designed by the Wi-Fi forum to provide quality of service to wireless traffic.\nThis project basically deals with providing WLAN support for WMM module, this includes handling\nQos frames in the Uplink down link data flow, Priority mapping for Qos frames, Fragmentation\nand Reassembly of the Qos frames, Security Related modifications for Qos Frames, Power\nmanagement of Qos stations etc.\nI was involved in the design, review, coding of the Uplink, Down link Data Flow, Fragmentation\nand Reassembly of the Qos frames.\n\nRole: Developer\nResponsibility: To design, and code the necessary changes. Design test scenarios for the design\nverification of the feature enhancements. Ensure proper quality of the product delivered. Smoke\ntesting of all the features in each release Performance testing and log record maintenance\nConfiguration Management for feature enhancement projects using Clear case tool\nDuration: From: Sep 2006 - May 2007\nHardware: Vxworks, Windows\nSoftware: C\nCompany worked for: ALBERTSONS INTERNATIONAL PRIVATE LIMITED\nDuration: From Date: 16 Jun 2000 To Date:29 Oct 2001\nDesignation: SOFTWARE ENGINEER\nProject Name Application Software Development\n\nDescription of the project\nSoftware for guest house, Fisheries Account management, Liquor wholesale. restaurants\n\nRole: Designer/Developer/ Reviewer/Customer Support\n\n\n\nResponsibility: Designing and coding above mentioned projects independently Involvement\nin requirement collection, prototype building, installation and support Coordinating the\ndevelopment cycle\nDuration: From June 2000 to Oct 2001\nHardware: X86\nSoftware: VB, C, MSACCESS\nManagement Skill Set\n• Release Planning\n• Work assignment and Follow up\n• Preparation of weekly bug scrub for customer and Conducting Weekly Bug scrub Meetings with\nthe team\n• Monthly Billing\n• Monthly Milestone Report Preparation\n• Monthly Metrics Report Preparation\n• Monthly OPS Review Report Preparation and Meetings with customer\n• Weekly OPS Preparation and Meetings with customer\n• TL9K Audit Preparation\n• Quarterly Defect Prevention and Analysis Report Preparation\n• Monthly CM Audit\n• Provided Macro and VB scripting and CQ queries for button click report generation\n\nSRIDEVI RAO H", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 57, "text": "Raktim Podder\n6+ Exp in banking operations and credit assessment\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Raktim-Podder/32472fc557546084\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSME\n\nTCS\n\nTata Consultancy Services: Project TELSTRA (AUSTRALIA)\nSr Process Executive SME:\nTelecom Business Case Management.\nTechnical support to the onsite engineers.\nDealers support function.\nEmails regarding connection escalations.\nTech implementations in telecom.\nLooking after the day to day business production.\nTeam management:\nHandling a team of 18 FTE’s\nDaily rostering\nOne on one’s\nPerformance management.\nCommunicating process updates.\nWFM\nEOD reporting on team performance.\nHuddles and catch-up’s\nSystem access request for new joiners.\nProviding training to new joiners.\nConflict management.\nBusiness performance reporting.\nNetwork setup and network access management as per requirement.\nTesting line and networking working capacity for minimal disruption.\nSetting up wireless channels for customers.\nProviding one stop solution for escalations from customers.\nLooking over the B2B connection setup of customers.\nException Management: Telstra Digital\nCase management\nOrder built\nEscalations handling.\n\nCCSS Rep\n\nHSBC Electronic Data Processing India Pvt -  Bengaluru, Karnataka -\n\nAugust 2014 to November 2016\n\nhttps://www.indeed.com/r/Raktim-Podder/32472fc557546084?isid=rex-download&ikw=download-top&co=IN\n\n\nHSBC: UK onboarding and KYC\nCustomer Relationship Management and on boarding:\nSetting up and checking KYC documents.\nNational ID\nAddress proofs\nIncome proofs\nAddition security for additional credits.\nCredit scoring report from external credit rating agencies in UK.\nCalling the customer and informing the current status of the process.\nAsking for additional documentation if required.\nInterviewing the customer for understanding his/her portfolio.\nUploading documents and creating profile in BMM and sending for approval.\nLiaising with branch and credit control for additional requirements and if needed execution of\nthose requirements.\nVolume of production or volume of documentation forecasting based on the current sales and\nbranch production:\nLiaising with branch to understand the volume.\nLiaising with sales to under the targets and performance ratio to determine the incoming CRM\nvolume.\nDetermining if there is a dip in CRM production or rise based on:\nVolume comparison and trend analysis reporting to the higher managements.\nWeekly/ Monthly / Quarterly dashboard creation.\nDaily volume trend analysis.\nProduction and process reporting EOD.\nTechnical Implementation support:\nRequirements specifications to the development team to improve the applications in the project.\nUAT user acceptance testing of new applications in the project and reporting the same to the\nIT team\nSit with development to understand the reason behind the implementation and communicating\nthe same to the FTE’s\nTraining provision on new applications to the FTE’s\nChanges implementation if required to the development team.\nOnline banking platform support:\nHelping the online banking team to implement the CRM online.\nUnderstanding the requirements of the management in regards to what they want and why they\nwant to integrate the CRM into online banking.\nMeeting setup to understand requirements.\nOnce the requirements have been framed we sit with the development team to provide them\nunderstanding of the CRM process and what is required by CRM that needs integration in the\nonline banking platform.\nProviding support in every stage of website coding development:\nCommunicating the scope and purpose of the website to the development teams.\nHelping with baseline setup.\nProviding data to the development team:\nWhat will be the content of the CRM tool in the website.\nHow customers can access the tool online to complete their KYC.\nIllustration of documents required will be on the page or not.\nWhat kind of documents will be uploaded in the website.\nWhat kind of files will the website support.\n\n\n\nWhere will it be stored after it gets uploaded.\nWhere the files will be sent after it gets uploaded for approvals.\nHow will the customer know of the status of the application process by automated email or by\ntext or by both.\nWhat other links will be there on the online CRM tool which can be useful for the customer.\nWhat will the customer do if they are in confusion while uploading the documents for which a\nweb chat can be introduced to provide support and direct contact with the executive.\nUnderstanding the size of the project and what resources are required to execute the project.\nUnderstanding the risk involved in executing the new codes in the running website:\nWhen will the maintenance take place at what time and what date to avoid minimal customer\nimpact.\nInformation provision providing the customer with website maintenance information and non\nusable timescale.\nHow many FTE’s required from operations for UAT of a particular code so that manpower planning\nis maintained and BAU is not effected.\nAfter the above the estimation of the project starts:\nEstimating the cost involved in this implementation\nFTE requirements\nMethodology used for development.\nResources and technical knowhow required.\nOnce the above function is performed the details of which is framed in a document template and\nsent to the higher management for approval.\nOnce the approval comes the development begins.\nTracking and estimation support is given at steps where it is required.\nAfter the implementation the UAT is again performed and asked for further requirements or if the\nmanagement is happy with the implementation then the GO LIVE is arranged and executed.\n\nInfosys\n\nProcess Executive\n\n• Infosys PVT LTD: National Australian Bank Project (Australian, Tasmania and New Zealand)\n◦ Credit assessment executive for credit cards:\n* Setting up credit memo for approval in SAP.\n* Credit appraisal or credit assessment:\n• Preparation of financial data.\n◦ Probability of default calculations.\n◦ Credit risk ratio analysis.\n• Proposal preparation.\n• Assessment of proposal.\n• Sanction/approval of proposal by appropriate sanctioning authority.\n• Documentations, agreements, mortgages.\n• Disbursement of loan Post sanctions activities such as receiving stock statements.\n• Review of accounts, renew of accounts, etc (On regular basis) if CDD is required by the client.\n◦ Credit Reporting and Analysis:\n* Weekly monthly and quarterly report generation regarding performing and non performing\nportfolios.\n• Solution design for non performing portfolios:\n◦ Setting up project documents and requirements gathering from offshore branch officials to\nunderstand the barriers and implementations expected from the project.\n\n\n\n◦ Casual meetings and client calls to understand the requirement and setting up targets or\nvolumes of expected production.\n◦ Once the requirements have been gathered a project plan is formed and the initiation is given\nfor approval.\n◦ Once the initiation have been approved a root cause analysis is performed to understand the\ncurrent standings using tools like:\n* 5 why's\n* Brainstorming\n* Decision matrix\n* Pareto diagram\n◦ Once the root cause is identified then as per the business need an elicitation of the business\nproblem is defined and communicated.\n◦ Project requirements and implementation:\n* Communicating the current standing of the portfolio to the team and communicating the\nrequirements.\n* Documenting the current standing of the portfolio:\n• Historical credit loss report.\n• Pipeline reporting.\n• Collections dashboard.\n* Once the communication is done the implementations in the projects starts:\n• We have understood using the root cause as to why the Credit Card is running at daily loss\n• We have understood using the data gathering what is the current standing of the credit card\nportfolio.\n◦ Using the above two determining what changes to be made in the process of collections.\n◦ What recovery solutions can be provided to pool in the bad debts in the portfolio.\n◦ Sitting with the sales team to ask them how to target the base.\n◦ Sitting with credit assessment officers to introduce new techniques and models to forecast\ncredit lending capacity of a customer.\n* Once the implementations is completed we move on to the controlling of the project where we\ndefine the improvements and analyse:\n• Present and Past performance analysis.\n• Trend Measurement of the portfolio:\n◦ Bad Debt percentages.\n◦ Loss increase or decrease percentage.\n• Percentage increase or decrease of the performing ratio using:\n◦ Graphs\n* Vertical\n* Trending\n* Hybrid\n◦ Pie charts\n◦ Scatter plots\n• Once the control setup has been initiated the process then goes into automation and the credit\ncycle is completed until new requirements come.\n\nEDUCATION\n\nBcom\n\nWest Bengal State University\n\n\n\nSKILLS\n\nEDD, CDD, credit risk assessment, KYC, Banking, credit risk analysis, Customer Handling,\nPortfolio Management, Customer Service\n\nAWARDS\n\nBest Quality\n\nJanuary 2011\n\nRising Star\n\nJune 2015\n\nBPS star performer\n\nJune 2016", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 58, "text": "Pavithra M\n\"Infosys\" internship\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Pavithra-M/26f392ec8251143b\n\nWilling to relocate to: Bengaluru, Karnataka\n\nWORK EXPERIENCE\n\n\"Infosys\" internship\n\nInfosys\n\nAbout: a detailed analysis of diseases and user interface based on that\ngiving clear idea which diseases has affected at what region and\ntreatment availability for that particular diseases. It was completed in\n\"Infosys\" internship\n• Event management.\n• Good communication skills.\n• Leadership qualities.\n• Team collaboration.\n• Creative thinking.\n\nEDUCATION\n\nCBSE\n\nSTATE\n\nSSLC\n\nState board\n\nSKILLS\n\nADOBE PHOTOSHOP (Less than 1 year), ANDROID (Less than 1 year), APPLICATION SOFTWARE\n(Less than 1 year), C++ (Less than 1 year), CSS (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTechnical skills:\n\n• Programing Languages: C, C++ and JAVA.\n\n• Databases: MySQL, SQL server (SQLyog)\n\n• Web tools: HTML, CSS.\n\n• Operating Systems: Windows (XP, 8, 8.1, 10), Ubuntu, fedora, Linux.\n\nhttps://www.indeed.com/r/Pavithra-M/26f392ec8251143b?isid=rex-download&ikw=download-top&co=IN\n\n\n• Application software: Microsoft Office.\n\n• IDE: Eclipse, Netbeans.\nWorkshops attended:\n\n• Basic editing tools using MS office.\n• Android workshop.\n• Adobe Photoshop.\n• Angular JS.\n\nProject and Internship:\n\n1. \"JAVA 8 applications for Login Credentials with DB connectivity\"", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 59, "text": "shrikant desai\nWorking as accountant @ infosys\n\nPune, Maharashtra, Maharashtra - Email me on Indeed: indeed.com/r/shrikant-desai/\ncc6430615ce4d44a\n\nIntercompany ,r2r,p2p,\n\nWORK EXPERIENCE\n\nAccountant @ Infosys\n\nInfosys India ltd -  Pune, Maharashtra\n\nI am working as accountant.\nr2r, p2p, IC\n\nEDUCATION\n\nB.Com in Accounts\n\nshivaji University -  Kolhapur, Maharashtra\n\nJanuary 2008 to January 2011\n\nS.M.M. MURGUD\n\nSKILLS\n\n@ Strong analytical ability @ Good communication skills @ Client relationship management @\nTeam player\n\nLINKS\n\nhttp://<EMAIL>\n\nADDITIONAL INFORMATION\n\nComputer Skills\n@ MS-CIT Course passed with 74 % (Microsoft-word, Excel, and Power-point)\n@ Diploma in Financial Accounting with tally 9 Erp. (87%)\n\nhttps://www.indeed.com/r/shrikant-desai/cc6430615ce4d44a?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/shrikant-desai/cc6430615ce4d44a?isid=rex-download&ikw=download-top&co=IN\nhttp://<EMAIL>", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 60, "text": "Kiran Kumar\nI Having 2.1 years of Experience in IT, now currently working as a Linux\nAdministration in Infosys in Bangalore.i have Good knowledge in Red\nhat Linux\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Kiran-Kumar/7e76e7a9e62e7ee5\n\nHaving 2.1 years of Experience in IT. Now working as a Linux Administration and Knowledge in\ninstalling and maintenance and troubleshooting Linux in Operating Systems.\n\n➢ Installation, Configuration, Maintenance and Administration of Red Hat Enterprise 5/6,\n➢ Checking Linux server 's CPU Utilization, Memory Utilization, and Disk Utilization for\nperformance monitoring,\n\n➢ Managing User Administration,\n➢ Creating new file systems through LVM,\n➢ Installation of software packages using RPM and YUM,\n➢ Expertise in Raid configurations,\n➢ Good knowledge in Cron jobs scheduling,\n➢ Knowledge in ACL implementation on user and groups,\n➢ Good knowledge on Disk quota management,\n➢ Network configurations assigning IP address in Static and DHCP,\n➢ Apply Sticky bits on files and directories,\n➢ RHEL booting process,\n➢ Creation Symbolic links.\n\nWilling to relocate to: hyderbad, Telangana - Bangalore, Karnataka - Chennai, Tamil Nadu\n\nWORK EXPERIENCE\n\nInfosys\n\nLinux Admiration in Infosys. -  Bengaluru, Karnataka -\n\nMay 2016 to Present\n\nLinux Administrator\n\nEDUCATION\n\nBachelor Of Technology in insurance\n\nSRIKALAHASTEESWARA INSTITUTE OF TECHNOLOGY\n\nSKILLS\n\nfile systems (2 years), Linux (2 years), maintenance (2 years), scheduling (2 years)\n\nhttps://www.indeed.com/r/Kiran-Kumar/7e76e7a9e62e7ee5?isid=rex-download&ikw=download-top&co=IN\n\n\nADDITIONAL INFORMATION\n\nOperating systems: Linux\nRole: System admin\nRoles and Responsibilities:\n➢ Perform software installation, upgrades/patches, troubleshooting, and maintenance on Linux\nservers,\n➢ Configuring Cron-job and scheduling the jobs,\n➢ User administration,\n➢ Creation New file systems,\n➢ File permissions,\n➢ Backup management by using TAR,\n➢ Day to day checking Linux server performance,\n➢ Installation of software packages using RPM and YUM,\n➢ Providing day-to-day user administration like adding or deleting users, password aging,\n➢ Installation, Configuration and Administration of various versions of Redhat Linux,\n➢ Apply Sticky bits on files and directories,\n➢ RHEL booting process,\n➢ Creation Symbolic links.", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 61, "text": "Chaban kumar Debbarma\nTripura - Email me on Indeed: indeed.com/r/Chaban-kumar-Debbarma/bf721c55fb380d19\n\nWilling to relocate to: Agartala, Tripura - Tripura\n\nWORK EXPERIENCE\n\nMicrosoft\n\n-\n\nJune 2018 to December 2018\n\nI want full time jobs\n\nEDUCATION\n\n10th\n\nSchool\n\nhttps://www.indeed.com/r/Chaban-kumar-Debbarma/bf721c55fb380d19?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 62, "text": "Akash Gulhane\nMicrosoft Certified System Engineer\n\nAmravati, Maharashtra - Email me on Indeed: indeed.com/r/Akash-\nGulhane/8b86faac48268d09\n\nI want to work with a progressive organization where I can utilize my knowledge and skills for the\nbenefit of the company.\n\nWORK EXPERIENCE\n\nMicrosoft Certified System Engineer\n\n-\n\n2012 to 2012\n\nTechnical Skills:\nCCNA (Cisco Certified Network Associate)\n\nDatabase:\nMS-Access\nOther:\nHardware & Networking, Core Java, C, C++\n\nOperating Systems: Windows server O.S 2012, Windows XP/7/8 User Level\nFinal Year Project:\nTwo factor data access control with efficient revocation for\nName of Project:\nmulty-authority Cloud Storage System\nTeam Size: 3\nMy Role: Software Developer\n.net\nFront End Tool:\nDatabase:\nSQL Server 2000\n\nEnvironment: JRE (Java Runtime Environment)\nObjective:\n\nPersonal\n\nNarsamma -  Amravati, Maharashtra -\n\n2010 to 2010\n\n53\nCollege\n\nRamkrishna krida High -  Amravati, Maharashtra -\n\nhttps://www.indeed.com/r/Akash-Gulhane/8b86faac48268d09?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Akash-Gulhane/8b86faac48268d09?isid=rex-download&ikw=download-top&co=IN\n\n\n2008 to 2008\n\n63\nSchool\n\nComputer Skills:", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 63, "text": "K. Siddharth\nSystem Administrator (Server) Microsoft - Nehru Memorial College\nPuthanampatti\n\nTiruchchirappalli, Tamil Nadu - Email me on Indeed: indeed.com/r/K-\nSiddharth/0023411a049a1441\n\nA Challenging career, which provides an opportunity for developing innovative\napplications using evolving technologies and an exposure to learn new exciting skills.\n\nWORK EXPERIENCE\n\nSystem Administrator (Server) Microsoft\n\nNehru Memorial College Puthanampatti -\n\n2013 to Present\n\n2013 at present)\nManaging and Maintaining Windows servers\n\nOver 260nos (LAB) Client Pc and 405nos Individual Pc Machine\n\nFirewall Configuring and Surveillances\nNetworking and Traffic managing\n\nVideo Capturing and Editing (Corel Studio)\nDslr camera\n\nTwo/Four Vehicles Driving\nCorrectly identify the problem & resolving fast\n\nUp-to-date in products/software/problems.\n\nSystem Administrator (Server) Microsoft\nBackup the data\n\nFirewall Surveillance periodically\n\nStock and Report Maintaining\n\nPositive Traits\nStrong will power to learn new technologies with a flexibility to adapt to all new work\nenvironment and process.\nCommitment and loyalty.\nGood organizing and leadership quality.\nCommunicating effectively with gentleness.\nI joined as a System admin, but can learn and do any type of job\nLearned and executed which was not there (Nehru Memorial college) video capturing\n\nhttps://www.indeed.com/r/K-Siddharth/0023411a049a1441?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/K-Siddharth/0023411a049a1441?isid=rex-download&ikw=download-top&co=IN\n\n\nvideo editing, dslr camera photoshoot, Car Driving, Xerox Machine.\nI will to learn anything and I do my best\n\nEDUCATION\n\nDiploma in Challenger info tech\n\nPASSING -  Madurai, Tamil Nadu\n\n2011\n\nST.Joseph college -  Tiruchchirappalli, Tamil Nadu\n\n2010\n\nDCE\n\nAdi Sankara Polytechnic College -  Tiruchchirappalli, Tamil Nadu\n\n2009\n\nS.S.L.C in PRIVATE\n\nSTATE BOARD\n\n2005", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 64, "text": "Shivam Rathi\nMicrosoft technology Associate (MTA)\n\nMuzaffarnagar, Uttar Pradesh - Email me on Indeed: indeed.com/r/Shivam-Rathi/\nd7d73269f025a981\n\nTo work in a firm with a professional work driven environment where I can utilize and apply my\nknowledge, skills which would enable me as a fresh graduate to grow while fulfilling organizational\ngoals. I want to become a Software tester.\n\nBASIC ACADEMIC CREDENTIALS\n\nWORK EXPERIENCE\n\nMicrosoft technology Associate (MTA)\n\nCompany Name -\n\nJune 2017 to July 2017\n\n• Project Title: - Currency Converter (Android)\n• Duration: - Six Weeks (16th June 17 to 31th July 17)\n\nPROJECTS REPORT\n❖ Currency Converter (Android)\n\nIT PROFICIENCY\n\n❖ Microsoft Office Word, Microsoft Office Excel, Microsoft Office Power Point\n❖ Having some Basic Knowledge of Software Testing like - Black-Box Testing, Manual Testing etc.\n❖ Internet Browsing\n❖ Language: C, Java.\n❖ General and graphic application: HTML.\n❖ Software Testing.\n\nCO-/EXTRA -CURRICULAR ACTIVITIE\n❖ Captain of School Cricket Team.\n❖ Member of Volleyball Team.\n\nEDUCATION\n\nC.B.S.E\n\nIndraprastha Public School\n\n2012\n\nB.TECH in Computer Science Engineering\n\nUttrakhand Technical University\n\nhttps://www.indeed.com/r/Shivam-Rathi/d7d73269f025a981?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Shivam-Rathi/d7d73269f025a981?isid=rex-download&ikw=download-top&co=IN\n\n\nC.B.S.E\n\nIndraprastha Public School", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 65, "text": "Nitin Verma\nAssisting Microsoft Partners - Exchange Online and Office\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Nitin-Verma/b9e8520147f728d2\n\nWORK EXPERIENCE\n\nAssisting Microsoft Partners\n\nExchange Online and Office -\n\nSeptember 2017 to Present\n\nfrom around the world with AD, Exchange Online and Office 365\nrelated issues.\n➢ Prompt assistance within the required SLA as per the case creation by users and follow up\ntimely till the issue has been resolved up to the user's satisfaction.\n\n➢ Troubleshooting and resolving various production impacting critical issues.\n\n➢ Educating Admins about various Microsoft Office 365 features and assisting them with\nimplementing the same using GUI as well as PowerShell.\n\n➢ Reproducing the users' issues on test environment and researching to find a resolution.\n\n➢ Interact with other 2nd level support team for joined troubleshooting sessions where root cause\nis\nnot well defined.\n\n➢ Assisting users/admins with various Office 365 applications, like Outlook, Word, SharePoint,\nOneDrive, Skype For Business, etc.\n\n➢ Brainstorming with the Technical Advisors from Microsoft regarding Service Incidents impacting\nmultiple tenants.\n\nTools Used: RAVE, CAP and AVAYA.\n\nEDUCATION\n\nBachelor of Engineering in EnTC\n\nPune University -  Pune, Maharashtra\n\nhttps://www.indeed.com/r/Nitin-Verma/b9e8520147f728d2?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 66, "text": "Venkateswara D\nspecialist - Technology Process\n\nIN - Email me on Indeed: indeed.com/r/Venkateswara-D/18b373e3b03b371f\n\nA technocrat with over 10 years of rich experience across various fields like Customer Services,\nEnd user support, Technical Support, Risk Management, BMC Remedy Support (Basics) and\nService Delivery Management.\n\nWORK EXPERIENCE\n\nspecialist\n\nTechnology Process -\n\n2008 to Present\n\nProduction Support Analyst (Microsoft\n\nInfosys Limited -  Hyderabad, Telangana -\n\nApril 2004 to September 2007\n\nWorked As chemist for Quality control Heterolabs Private Limited, Hyderabad from April2004 to\nSeptember 2007.\n\nPROJECTS\nProject Details (#1): Microsoft Finance Solution Delivery Tier 1\nClient: Microsoft, India\nRole: Production Support Analyst (Microsoft India)\nOrganization: Infosys Limited, India Description:\nMSFNSDT1 (Microsoft Finance Solution Delivery Tier 1) support is a group which gives the frontend\nsupport for Microsoft applications used by their FTEs (Full-Time Employees) . These employees\nare generally the higher officials of Microsoft who use various tools to generate their reports\non Quarterly basis.This Support team is having 47 applications and divided in to 4 clusters i.e.\nPlanning, Finance, MS Sales and Headcount.We, the Tier 1 team are the first point of contact for\nMS employees who use these tools. Our work is to do the troubleshooting and revert back to\nthem with emails and through calls.\n\nROLES and Responsibilities\nGatekeeper and Support Analyst\n❖ Resolving user's issues and reverting back to them with emails and through calls related to 12\napplications comes under Planning and Finance clusters ( WW Services, Calypso, and Latam)\n❖ Preparation of documents (FAQs, Articles)\n❖ Closed Service Request analysis, finding common issues and document them with suggestions.\n❖ Training new resource, monitor and review their work on daily basis.\n❖ Supporting other clusters as SA, during their pick times.\n❖ Developed complex SQL and PL/SQL scripts as part of development.\n\nProject Details (#2): Project 3- VLIT (Microsoft's Volume Licensing)\n\nhttps://www.indeed.com/r/Venkateswara-D/18b373e3b03b371f?isid=rex-download&ikw=download-top&co=IN\n\n\nClient: Microsoft\nTeam Size: 24\nTool: Visual Studio 2010 Ultimate,\nTechnologies: C#, Glider framework, SAP GUI (MSU), SQL Server\nOperating System: Windows 7,\nRole: Test Engineer\n\nDescription: Microsoft's VL programs allow customers to purchase and upgrade software licenses\nat substantial discounts, provide more flexible payment options, ease license management and\nsoftware deployment, and are the only way to purchase many Microsoft products. The Laminar\nAlpha (ECX 1.0/1.1&1.15) release to enable the E2E processes required to launch the\nVLA in market (i.e. book revenue) and to support (through a combination of automated, semi-\nautomated, and manual processes) the essential elements of the Volume Licensing experience\nrequired to sustain a production business launch.\n\nThe Licensing Operations Centre (LOC) will be a single portal for users in the Regional Operation\nCenters (User Management- Create and Manage users) to perform tasks that facilitate Volume\nLicensing (VL) transactions and support. Individuals in the ROCs perform a variety of activities\nwithin the end to end VL ecosystem, from Pre-Sales support to Quote-to-Cash transactional\noperations, and subsequently Post-Sales support of Partners, Customers and Internal Microsoft\nstakeholders.\n\nResponsibilities\n❖ Understanding and analyzing Business Requirement Document (BRD), Functional Specification\nDocument (FSD)\n❖ Involved in creation of Test Scenarios and Test cases for the User.\n❖ Involved in Creation of unit test cases.\n❖ Executing the test cases both manual and automated.\n❖ Involved in Data Base testing using SQL queries.\n❖ Reporting bugs using VSTF.\n❖ Involved in functional testing of the VLIT Laminar Web Application.\n❖ Involved in creation of Accounts (Legal, Customer and Partner) and Contacts from SAP GUI for\nfunctional testing\n\nProject Details (#3):\n\nMicrosoft Order Management\nClient: Microsoft, India\nRole: Production Support Analyst (Microsoft India, Hyderabad)\nOrganization: Infosys Limited, India\nTeam Size: 10\n\nDetails of project:-\n\nWorking as Line 2 Support Analyst for BGOS group of Microsoft Corporation, and providing 24*7\nProduction support in SQL Server 2008 for LPO division of Microsoft which provides support for\noperational deployment and support for a variety of internal corporate tools and external facing\nlocalization tools throughout Microsoft. LPO (Licensing and pricing operations) support teams\n\n\n\nin India, Redmond, Dublin and East Asia, The objective of this project is to support Incident,\nchange the release management in the production system and mission critical Microsoft internal\napplication.\n\nOrder management: - Order management is a platform which is built on .Net technology and\nis used as main application for order processing of various products manufactured and sold by\nMicrosoft which includes all operating systems, Microsoft Office, XBOX etc. In this application the\norders are placed on the websites which flow through various channels of security and validation.\nThese sub channels are also supported by us. We support this Order management application\nin 3 different environments, production, UAT (testing Environment) and POB (Production on\nboarding) . We are the actual owners or all the 3 environments.\n\nProduction Support Analyst: Microsoft Order Management Platform/Application\n\nRoles and Responsibilities\n❖ Provide 24 * 7 production support for Microsoft Order management • Updating the patches\nreleased.\n❖ DB administration which includes taking database backup, restoring, health checking.\n❖ Taking appropriate actions according to the alert from SQL servers and web servers.\n❖ Checking any connectivity issues. If so, escalating the case to respective teams.\n❖ Taking care of the Reporting Services issues.\n❖ Updating monthly Taxware Data to all production/UAT/POB servers • Taking care of production\ndatabases and various jobs.\n❖ Smoke test of the jobs\n❖ Running the various database updates given by the SE team in all the 3 environments.\n❖ Resolving web application issues.\n❖ Coordinating with business team and implementing there requirements on database.\n❖ Handling the stores and installing new DLL's and web application files like HTML, XML etc at\ngiven locations in all 3 environments.\n❖ Updating passwords when there is a password expiry.\n❖ Giving real time support to customers, when they face any OM application related issues..\n❖ DBA tasks for more than 30 databases\n❖ Monitoring and administration of user access to web application.\n❖ Administrative tasks like permissions; logins for various tools are websites too.\n❖ Monitoring Database Growth.\n❖ Creating Reports using SQL Scripts (Querying the database)\n\nActivities involves the below ITIL process\n\n❖ Incident Management:- Working on the level 2 incidents to make sure that the response times\nto the users is minimized. Analyse, troubleshoot and provide the work around/resolution to the\nincidents and resolve the issue within given SLA (Service Level Agreement)\n\n❖ Change Management: - Working on the RFC's raised by the user. Following up with the user in\nall the 11 stages with all the approvals of RFC's. Then, testing the change in Dev and acceptance.\nFinally implementing the change in production.\n\n❖ Problem Management: - Identifying the frequently failing problem with the prod jobs and\nreporting and handling the issue as part error handling and correction task.\n\n\n\nComputer Proficiency\nPlatforms: Win 9x, NT, XP, Vista (x64 & x86), Linux.\nLanguages: SQL, PL/SQL, Unix, SAP Systems; PMO; IBM Filenet\nApplications: MS-Office\nDatabase: MS-Access, working knowledge of SQL Server.\n\nEDUCATION\n\nCertificate in MSC CHEMISTRY\n\nAcharya Nagarjuna University\n\nMBA\n\nAcharya Nagarjuna University -  Pune, Maharashtra\n\nSecondary School of Education", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 67, "text": "Shivasai Mantri\nMicrosoft dynamics AX Technical consultant\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Shivasai-Mantri/\neb5df334d3959e42\n\nAchieve challenging Organizational Goals by applying creative problem solving skills to benefit\nmutual growth and success.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nMicrosoft dynamics AX Technical consultant\n\nIndian Terrain Fashions Limited -\n\nOctober 2016 to Present\n\nDeveloping the new modules and menus and customizing the existing modules in AX as per the\ncompany requirement\n\nMicrosoft dynamics AX Technical consultant\n\nABA Technology Services\n\nDeveloping the new modules and menus and customizing the existing modules in AX as per the\ncompany requirement\n\nEDUCATION\n\nBE in Computer science\n\nSreenidhi Institute of science and technology -  Hyderabad, Telangana\n\nAugust 2011 to June 2015\n\nNarayana Junior College -  Hyderabad, Telangana\n\nSSC\n\nVamshi High school -  Bodhan, Andhra Pradesh\n\nSKILLS\n\nasp.net, dynamics ax technical consultant, sql, x++\n\nhttps://www.indeed.com/r/Shivasai-Mantri/eb5df334d3959e42?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Shivasai-Mantri/eb5df334d3959e42?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 68, "text": "Prasanna Ignatius\nMICROSOFT - Backup Administrator\n\nChennai, Tamil Nadu - Email me on Indeed: indeed.com/r/Prasanna-\nIgnatius/1404633e9449f641\n\n• System's Engineering Professional with 08 Years 09 Months of Experience in Wipro InfoTech\n(From June 2008 to May 2014) and HCL Technologies (From May 2015 to April 2018) mostly as\na Backup Administrator.\n• Have Experience in Helpdesk, User Lifecycle Management (ULM) otherwise known as Access\nManagement Team, Desktop Support, Remote Support, Data Center Operations and Backup\nManagement.\n• 07 Years 06 Months of Experience in Data Protection Manager (DPM) and 05 Years in EMC Legato\nNetworker.\n• Having hands on Experience in Sun Solaris 9 & Microsoft Windows Server 2000, 2003\n& 2008 R2.\n• Expertise in concepts of end-to-end program planning and implementation from scope\nmanagement, to activity sequencing, risk analysis to quality management in adherence to quality\nguidelines and norms.\n• Technical Support, Troubleshooting, Debugging & Impact Analysis.\n• A technology driven professional with 08+ Years of Experience in the areas of System\nImplementation and Maintenance.\n• An effective Team Player with people management, demonstrating strong analytical, problem\nsolving & co-ordination skills and ability to follow through with projects from inception to\ncompletion.\n• Highly self-motivated and goal-oriented professional committed to pursuing a long-term career.\n• Possess strong communication, collaboration & team building skills with proficiency at\ngrasping new technical concepts quickly & utilize the same in a productive manner.\n• Maintained project workbooks and responsible for internal quality audits for the account.\n• Experienced Backup Administrator familiar with formulating organizational archive policies.\n• Ready to apply learned skills to any corporate decision making process in regard to protecting\ndata.\n• Can research problem solve and communicate the best ways to perform and coordinate tasks\nthroughout the organization.\n\nWORK EXPERIENCE\n\nBackup Administrator\n\nHCL -\n\nMay 2015 to April 2018\n\nMostly worked as a Backup Administrator\n\nMICROSOFT - Backup Administrator\n\nHCL TECHNOLOGIES -  Hyderabad, Telangana -\n\nMay 2015 to November 2017\n\nhttps://www.indeed.com/r/Prasanna-Ignatius/1404633e9449f641?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Prasanna-Ignatius/1404633e9449f641?isid=rex-download&ikw=download-top&co=IN\n\n\ndata using Symantec Backup Exec. Moved 300+ Assets successfully from one location to another\nwhich includes Servers and Storages.\n\nADITYA BIRLA FINANCIAL SERVICES GROUP -  Mumbai, Maharashtra -\n\nFebruary 2014 to March 2014\n\nLocation: Mumbai\n\nTATA AIA LIFE INSURANCE COMPANY - Helpdesk -  Mumbai, Maharashtra -\n\nSeptember 2008 to December 2013\n\nULM) otherwise known as Access Management Team, Desktop Support, Remote Support, Data\nCenter Operations and Backup Management\nDuration: September 2008 - December 2013\nLocation: Mumbai\nProgram Description: Tata AIA Life Insurance Company Limited (Tata AIA Life) is a joint venture\ncompany, formed by Tata Sons and AIA Group Limited (AIA) . Tata AIA Life combines Tata's pre-\neminent leadership position in India and AIA's presence as the largest, independent listed pan-\nAsia life insurance group in the world spanning 17 markets in Asia Pacific. Tata Sons holds a\nmajority stake (74 per cent) in the company and AIA holds 26 per cent through an AIA Group\ncompany. Tata AIA Life Insurance Company Limited was licensed to operate in India on February\n12, 2001 and started operations on April 1, 2001.\n\nWIPRO INFOTECH -  Mumbai, Maharashtra -\n\nAugust 2008 to September 2008\n\nLocation: Mumbai\n\nEDUCATION\n\nMS in Systems Engineering in Systems Engineering\n\nBITS Pilani -  Pilani, Rajasthan\n\n2008 to 2012\n\nB.Sc in Computer Science\n\nBishop Ambrose College, Bharathiar University -  Coimbatore, Tamil Nadu\n\n2005 to 2008\n\nHSC\n\nState Board\n\n2004 to 2005\n\nSKILLS\n\nMicrosoft DPM, EMC Legato Networker & Symantec Backup Exec\n\n\n\nADDITIONAL INFORMATION\n\nSkill Set\n• Wide experience with EMC Legato Networker & Microsoft Data Protection Manager (Backup\n& Restoration)\n• Providing Technical, Functional and Operational Support to users in the implementation and use\nof Software Applications & Installation, Configuration, Troubleshooting of Backup Libraries\nand Server's.\n• Providing support for the process such as Incident/Problem/Service Request/Change.\n• Providing training, troubleshooting, technical advice and direction to other members of the\nteam.\n• Managing the Backup infrastructure to ensure all Service Level Agreements are met.\n• Managing and creating different types of the policies for the Backup jobs.\n• Monitoring daily Backup status, handling tape changes, troubleshooting of tape libraries and\ndrives, restoration of missing files/directories and other Backup related activities.\n• Scheduling the Backup's as per the change requirements by overriding the schedule as and\nwhen required.\n• DR restoration on a monthly basis for mission critical Servers and checking the data integrity.\n• Creating and maintaining process and procedures run book for data center processes.\n• Maintaining Backup data for a 24×7 office.\n• Working with offsite storage vendors for tapes.\n• Strong ability to organize and schedule tasks as per realistic actions plans.\n• Superior ability to work independently in a demanding environment.\n• Report Generation - Daily, weekly and monthly reports on Major Incidents.\n\nTechnical Skills\n• In-depth knowledge in Configuration, Installation and Troubleshooting of Microsoft Data\nProtection Manager.\n• Have Knowledge in installing DPM and Central Console, upgrading, repairing, and uninstalling.\n• Experienced in DPM protection agent, and setting up protection for computers, servers, and\nworkloads.\n• Experienced in Troubleshooting DPM errors and issues.\n• In-depth troubleshooting of Backup and Restore failures on Windows.\n• Performed Data Restorations for Server, File System and/or Databases from the tape & disk\nlibraries upon customer request in a calm and controlled fashion while maintaining consistent\ncommunication with the customer.\n• Performed an analysis of CPU/Memory/Disk Utilization for Critical Platforms/Server's.\n• Performed EOD Activities on SQL & Window's based Platforms.\n• Performed SQL & Oracle DB Backup's and Restoration.", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 69, "text": "Pankaj Bhosale\nMicrosoft SQL-SERVER\n\nDhule, Maharashtra - Email me on Indeed: indeed.com/r/Pankaj-Bhosale/2d6f2e970b9a7ff6\n\nWORK EXPERIENCE\n\nMicrosoft SQL-SERVER\n\nTechnical Expertise:-\nEnvironment Windows 7, Windows XP, Linux.\nLanguages C, C++, C#.NET.\nWeb Technologies ASP.NET 3.5, Ajax.\nDatabases Microsoft SQL-SERVER 2005/8, MySql, MS-ACESS..\nScripting Language HTML, JavaScript, jQuery.\nIntegrated Development Environment Visual Studio 2008/2010.\n\nJr. Developer\n\nSoftzel Technologies Pvt. Ltd -  Pune, Maharashtra\n\nDatabase designing, User side coding & user side designing\nTechnologies Used ASP.NET, C#.NET, SQL Server 2005\nResponsibilities\n⇨ Can get online help to designers, engineers and Vaasthu experts for the construction of new\nhouses or buildings.\n⇨ Develop this site to get the list of contractors, engineers and hardware dealers.\n\nGrocery Shop System -  Pune, Maharashtra\n\nPune\nTechnologies Used Microsoft Visual Studio .Net 2008\nResponsibilities\n⇨ Maintain daily purchase & sales activity of Mall. Maintain daily stock of mall.\n⇨ Generate Yearly reports on sales, purchase, stock, employee salary activity.\n⇨ Maintain communication with the customer throughout the problem resolution process of\nactions taken.\n\nEDUCATION\n\nMCA\n\nRCPET's I.M.R.D College -  Jalgaon, Maharashtra\n\n2014\n\nM.D.\n\nPalesh Commerce College -  Dhule, Maharashtra\n\n2010\n\nhttps://www.indeed.com/r/Pankaj-Bhosale/2d6f2e970b9a7ff6?isid=rex-download&ikw=download-top&co=IN\n\n\nHSC\n\nJai Hind College -  Dhule, Maharashtra\n\n2007\n\nSSC in Dot Net Framework\n\nRam Chandra keshav citale High School -  Jalgaon, Maharashtra\n\n2005", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 70, "text": "Vinay Singhal\nNew Delhi, Delhi - Email me on Indeed: indeed.com/r/Vinay-Singhal/c15261079a9b5ae7\n\nTo work in a challenging atmosphere that provides me an opportunity for learning and growth,\nwhere my professional skills combined with knowledge will make effective contribution to the\ngrowth of the company.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nCustomer Care Associate Microsoft\n\nConvergys -  Gurgaon, Delhi NCR -\n\nApril 2018 to Present\n\nI am handling US, UK and EU clients for the any issue faced by then related to Microsoft products.\n\nADDITIONAL INFORMATION\n\nSKILLS\n❖ Smart Working.\n❖ Fast and keen learner.\n❖ Works well within a team and also independently.\n❖ Flexible and high adaptability to new approaches, people and environments.\n❖ Professional approach and positive attitude.\n❖ Hard working with abstract thinking.\n❖ Good personal skills like photography & software issues.\n❖ Good Communications Skills\n\nhttps://www.indeed.com/r/Vinay-Singhal/c15261079a9b5ae7?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 71, "text": "Pawan Nag\nMicrosoft Certified System Engineer\n\nDelhi, Delhi - Email me on Indeed: indeed.com/r/Pawan-Nag/e14493f28cb72022\n\nWORK EXPERIENCE\n\nMicrosoft Certified System Engineer\n\nMCSA -\n\n2000 to 2000\n\nCorporate Trainings in India\n\nEDUCATION\n\nAMIE sec A\n\nTraining Program\n\n2018\n\nLINKS\n\nhttps://mcp.microsoft.com/Anonymous//Transcript/Validate\n\nADDITIONAL INFORMATION\n\nSkill Summary:\nOperating Systems:\n• Windows Server 2000, 2003, 2008, 2012, 2012R2.\n• Exchange 2003, 2007, 2010, 2013,\n\nDate […] (Name)\nPawan Nag\n\nhttps://www.indeed.com/r/Pawan-Nag/e14493f28cb72022?isid=rex-download&ikw=download-top&co=IN\nhttps://mcp.microsoft.com/Anonymous//Transcript/Validate", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 72, "text": "Shivam Sharma\nL1 Analyst in Microsoft project - HCL Technologies\n\nGhaziabad, Uttar Pradesh - Email me on Indeed: indeed.com/r/Shivam-\nSharma/8e4755830666f3b6\n\nWORK EXPERIENCE\n\nL1 Analyst in Microsoft project\n\nHCL Technologies -  Noida, Uttar Pradesh -\n\nSeptember 2016 to Present\n\nLearnings: learnt to work on different fields such as networks, servers, Sql server, and some\nintegration software tools such as Microsoft service now (SNOW)\n● •Worked as L1 analyst in IT Command Center team for monitoring networks and server issues.\n● •Handson experience in monitoring tool service now (SNOW)\n● •Worked on user requested mails regarding servers and network break fix issues.\n● •Communicating and coordinating with different track teams for proper resolution of issues.\n● •Worked on GMO Sql servers for troubleshooting issues like job failures, disk space issues.\n● •Worked on troubleshooting servers issues like RPD, connectivity, disc space, servicesSPN\ncreation and deletion.\n● •Knowledge of DHCP, DORA process, Active directories, DNS, FSMO roles, Forests and domains,\nportioning, static and dynamic IP, trust relations\n● documenting new process\n\nEDUCATION\n\nBachelor of Technology in Computer Science Engineering\n\nIndraprastha Engineering College, Uttar Pradesh Technical University -  Ghaziabad, Uttar\nPradesh\n\n2016\n\nGreenfields Public School -  New Delhi, Delhi\n\n2010\n\nSKILLS\n\nSQL (1 year), HTML (Less than 1 year), INCIDENT MANAGEMENT (Less than 1 year), IT\nINFRASTRUCTURE (Less than 1 year), MS OFFICE (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nCORE COMPETENCIES\n● Professional Communication\n● Incident management\n\nhttps://www.indeed.com/r/Shivam-Sharma/8e4755830666f3b6?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Shivam-Sharma/8e4755830666f3b6?isid=rex-download&ikw=download-top&co=IN\n\n\n● Networks and servers monitoring\n● Understanding of IT infrastructure\n● Understanding of L1 networks and servers troubleshooting\n● MS Office\n● HTML\n● SQL\n● Windows OS\n\nPERSONAL TRAITS:\n\n● Ability to produce the best result in pressure situations.\n● Good time management\n● Ability to work in team as well as individual.\n● analytical and critical thinking\n● good presentation skills", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 73, "text": "Gaikwad Dilip\nMicrosoft Dainamic Software Billing Operator - Shreenath Engence\n(COFFEEDAY)\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Gaikwad-Dilip/6cc87ee90de2b0fe\n\nAs I am the fresher in this field, I will know about the nature of my work. Moreover, I will attempt\nto know about the various processes which form my job. By doing so, I will be able to do my job\nmore proficiently. On the other hand, I shall implement my knowledge into the practical world. A\ngrowth oriented profile in a company where my skill knowledge is effectively used for the success\nof the organization.\n\nWORK EXPERIENCE\n\nMicrosoft Dainamic Software Billing Operator\n\nShreenath Engence (COFFEEDAY) -\n\nMay 2017 to Present\n\nDaily Generate E-Way Bill.\n✓ Daily Communication Sales Team & Customer.\n✓ Fix Cost, Fix Transport, Mass Cargo, Extra Vehicle Handling Claim.\n✓ Monthly Attends Meeting.\n✓ Warehouse Management System.\n\nEDUCATION\n\nB.COM\n\nPUNE UNIVERCITY\n\nSKILLS\n\nTALLY MICROSOFT DYNAMIC OPARATER MICIT\n\nhttps://www.indeed.com/r/Gaikwad-Dilip/6cc87ee90de2b0fe?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 74, "text": "Moumita Mitra\n- Email me on Indeed: indeed.com/r/Moumita-Mitra/d63c4dc9837860db\n\nWORK EXPERIENCE\n\nInternational Maths Olympiad -\n\n2017 to 2017\n\n3. Teaching with technology 2016\n4. Educator Community Contributor\n\nCertified Microsoft Innovative Educator\n\nInternational Maths Olympiad -  Allahabad, Uttar Pradesh -\n\n2017 to 2017\n\n6. Problem based learning\n\nCo-curricular certificates: -\n\n1. Junior diploma in vocal from Prayag sangit samiti, Allahabad\n\n2. Chitra bhushan (part I) 1st year from pracheen kala Kendra, Chandigarh\n\n3. Certificate in kathak dance from Prayag sangit samiti, Allahabad\n\nActivities: -\nDissertation/ review work\n\n1.Glucagon like peptide 1:Incretin based therapies for type 2 diabetes; a focus on Exenatide\n\n2.Targeting antigen specific T cells for gene therapy of Multiple Sclerosis\n\n3.To see the role of synthetic blocking peptides that compete with autoantigens for binding to\nMHC molecule as immunotherapy of Hashimoto's thyroiditis.\n\nCU Zoology -\n\n2013 to 2013\n\n4. Participated in Poster Presentation on the topic \"Neurotoxins and its medicinal values: with\nspecial emphasis on snake neurotoxic venom\" in a one day colloquium in Spring talks at CU\nZoology 2013\n\n5. Attended UGC Sponsored National Level Seminar on \"Rediscovering 100 years of journey on\nthe field of Immunology: Present status of immunological research in India\n\nhttps://www.indeed.com/r/Moumita-Mitra/d63c4dc9837860db?isid=rex-download&ikw=download-top&co=IN\n\n\nEDUCATION\n\nEngineering\n\nWest Bengal State\n\n2015\n\nM.Sc in biology\n\nBallygunge Science College -  Kolkata, West Bengal\n\n2014\n\nB.Sc in West Bengal State\n\nBarrackpore Rastraguru Surendranath College -  Barakpur, West Bengal\n\n2012\n\nArmy Public School -  Barakpur, West Bengal\n\n2009\n\nC.B.S.E\n\nSt. Paul's Academy -  Patna, Bihar\n\n1978 to 2007\n\nBoard/ University\n\nSKILLS\n\nEXCEL (Less than 1 year), OUTLOOK (Less than 1 year), POWERPOINT (Less than 1 year),\nWORD (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSkills: -\n\nComputer: Proficient in Windows, Word, Excel, Outlook, PowerPoint Language: Fluent in verbal\nand written English, Hindi and Bengali", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 75, "text": "Suman Biswas\nSAP UI5 Lead, Native HANA Developer - Royal Dutch Shell\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Suman-Biswas/63db95fe3ae14910\n\nWilling to relocate to: Toronto, ON\n\nWORK EXPERIENCE\n\nSoftware Engineer\n\nShell -  Bangalore, Karnataka -\n\nMarch 2016 to Present\n\nSAP UI5 Lead, Native HANA Developer\n• Technical leader of frontend team (5 team members) for SAP UI5, Fiori, OData, XSJS since 2016.\n• Successfully delivered seven end to end development projects (CRV, CRD, Prelude, Charon,\nCTT, GPD, AIF).\n• Analyse business requirement, develop solution, prepare prototype, security model, application\narchitecture, effort estimation and involve in development delivery. \n• Project to expose on-premise HANA box data to outside world via HCP connectivity for mobile\napplication.\n• Requirement analysis, planning, design and developing the multiple HANA XS application.\n• Creation of HANA tables, calculation views, used unit and currency conversion. \n• Implementing row level and application enable security in applications using Analytic privilege,\nxsaccess. \n• Worked in Agile and DevOps methodology.\n• Interviewer on SAP UI5 and XS since 2016 for SAPUI5.\n• Worked in AngularJs 1.5.\n\nSoftware Engineer\n\nIBM -  Bangalore -\n\nAugust 2008 to January 2016\n\nPerformed four roles, Native HANA developer (July 2013 – January, 2016), team lead and onsite\ncoordinator (March, 2011 – June, 2013), SAP BI professional (January, 2010 – February, 2011),\nVBA developer (August, 2008 – December, 2009)\n• Worked for British Petroleum (BP, UK), National Grid (NG, UK) and Lab for SAP Solution (LSS,\nUS) account.\n• 2+ years worked in SAP HANA native development for LSS and NG. \n• Developed 9 dash boards for semi real time reports for NG using HANA calculation view and\nHANA live.\n• Developed 3 projects in SAP HANA XS and SAP UI5, Lumira applications. \n• Worked in SAP BI/IP based production support system (financial planning application) for 5+\nyears in BP downstream business. \n• Saved 60,000 man hours by implementing CI in 2012. It was best CI of the year.\n• Performed team lead role at BP account of 6 members team for more than 2 years.\n\nhttps://www.indeed.com/r/Suman-Biswas/63db95fe3ae14910?isid=rex-download&ikw=download-top&co=IN\n\n\n• Worked as onsite coordinator at BP (London, UK) for more than 2 years and used to report BP\nsenior managers (marketing and refining).\n• Managed incident, change and release management in production support system.\n• SME for Visual Basic for Application (VBA) and SAP BEx Report Customization for about 2 years.\n• Worked in National Grid account (energy industry, UK) for plant management using native HANA\ndevelopment.\n• Helped IBM presales team by developing POCs to get new business (mock applications\nshowcased to potential clients) in LSS account.\n\nSenior Web Developer – Microsoft Technology\n\nGlobal System Technologies -  Kolkata, West Bengal -\n\nJanuary 2008 to July 2008\n\n• Developed multiple web applications using ASP.Net, C#.Net, ADO.Net and SQL Server for\nclients. Used to work in MS.Net 2 version. There were online music store, shopping cart and\nmultiple B2B & B2C applications.\n\nSenior Web Developer - Microsoft Technology\n\nGlobal System Technologies Private Limited -\n\nJanuary 2008 to July 2008\n\nDeveloped multiple web applications using ASP.Net, C#.Net, ADO.Net and SQL Server for clients.\nUsed to work in MS.Net 2 version. There were online music store, shopping cart and multiple B2B\n& B2C applications.\n\nSoftware Developer-Microsoft Technologies\n\nVIBGYOR TechSolutions -  Kolkata, West Bengal -\n\nApril 2007 to December 2007\n\n• Mostly worked in C#.Net, MS SQL Server, XML, flash action script v.2.\n• Worked in network programming using TCP socket, multi-threading, image processing\napplications.\n\nSoftware Developer-Microsoft Technologies\n\nVIBGYOR TechSolutions -\n\nApril 2007 to December 2007\n\n• Mostly worked in C#.Net, MS SQL Server, XML, flash action script v.2.\n• Worked in network programming using TCP socket, multi-threading, image processing\napplications.\n\nMicrosoft.Net Developer\n\nBright Soft Solutions -\n\nMarch 2006 to February 2007\n\n\n\n• Primarily worked in MS SQL Server and C#.Net with remoting, OOP, multi-threading, 2D dynamic\nanimation.\n• Developed online poker card games (Texas hold 'em, Omaha) development for US client.\n\nAward and Recognition\n• Shell CIO award, 2016.\n• Best of IBM award, 2012.\n• Best CI of the year award, 2012.\n\nEDUCATION\n\nMBA in Information Technology\n\nSikkim Manipal University\n\n2013\n\nBachelor of Arts\n\nUniversity of Calcutta -  Kolkata, West Bengal\n\n2005\n\n'A' Level in Computer Application\n\nDOEACC\n\n2003\n\n'O' Level in Computer Application\n\nDOEACC\n\n2002\n\nSKILLS\n\nSAP HANA (4 years), SAP UI5/Fiori (4 years), AngularJs (Less than 1 year), Web Development\n(8 years), Asp.Net (5 years), C# (4 years), SQL Server (3 years), HTML5 (5 years), Javascript (6\nyears), CSS3 (6 years), Software Solution Design (2 years), VBA (2 years), SAP BI (2 years)\n\nLINKS\n\nhttps://www.linkedin.com/in/sumanbiswas2018\n\nAWARDS\n\nShell CIO award\n\nOctober 2016\n\nReceived Shell CIO award for Prelude project.\n\nhttps://www.linkedin.com/in/sumanbiswas2018\n\n\nBest of IBM award\n\nDecember 2012\n\nBest of IBM award to perform outstanding performance for the whole year.\n\nBest CI of the year award\n\nDecember 2012\n\nIn 2012 can able to save 60,000 man hours by generating continuous improvement (CI) in\ntechnical area of our project. The work won the best CI award for 2012.\n\nCERTIFICATIONS/LICENSES\n\nIntroduction to Oil Industry from “The Oxford Princeton Programme”,\n2016.\n\nJune 2016 to Present\n\nIntroduction to Gas Industry from “The Oxford Princeton Programme”,\n2016\n\nJune 2016 to Present\n\nPUBLICATIONS\n\nTechnical Blog on Socket Programming in C#\n\nhttp://socketprogramming.blogspot.com/\n\nJanuary 2011\n\nDetail technical blog on socket and multi-thread programming using C#.Net with lots of sample\ncode for beginner to advance level programmer. \nIt has more than 15 posts.\nTill now it has more than 2.7 million views.\n\nADDITIONAL INFORMATION\n\nSelf-initiative\n• Developed social network application (alap.me, 2012 - 2014) using C#.Net, ASP.Net, MySQL,\nJavaScript/jQuery, CSS, HTML5. With development.\n• Alap.me having social feature with live chat, messaging, friendship, photo and status share. \n• For alap.me, setup and managed Windows 2008 server, Web server (IIS7), MySQL DB setup,\nschedule backup, email server configuration, chat server development and setup, client-server\nload balancing to optimize CPU performance, email marking with process development.\n• Developed (in 2012) blooddonornetwork.com for social support.\n\nhttp://socketprogramming.blogspot.com/", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 76, "text": "Mansi Thanki\nStudent\n\nJamnagar, Gujarat - Email me on Indeed: indeed.com/r/Mansi-Thanki/04b8914a81df5a81\n\nproject on \"Water Quality Of Different Areas Of Ahmedabad City\n\nWORK EXPERIENCE\n\nMicrosoft Excel, Microsoft Power Point, Microsoft Word, File Management,\nInternet Use and C\n\n15 days training at Tata Chemicals Limited -  Mithapur, Gujarat\n\nMithapur (during 6th Sem)\nComputer Skill: Microsoft Excel, Microsoft Power Point, Microsoft Word, File Management, Internet\nUse and C language\n\nEDUCATION\n\nBE in Environmental Engineering\n\nGovernment Engineering College Bhuj -  Bhuj, Gujarat\n\n2009 to 2013\n\nhttps://www.indeed.com/r/Mansi-Thanki/04b8914a81df5a81?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 77, "text": "Anil Kumar\nMicrosoft Azure (Basic Management)\n\nDelhi, Delhi - Email me on Indeed: indeed.com/r/Anil-Kumar/96983a9dd7222ae5\n\nSeeking a challenging career as an IT-Manager utilizes my analytical skills experience in design\nand development area of IT development & management. Long term objective is to achieve\nan\nIT consultant position.\n\nSTRENGTH:\n\nAbility of solving problems independently, positive approach, quick learner and good performer\nboth in team & independent environment, hardworking self-confident, sincere and\ndeterministic.\n\nWORK EXPERIENCE\n\nMicrosoft Azure (Basic Management)\n\nFTP and TELNET -\n\n2008 to 2008\n\n• Design & implementation of Servers, Exchange Servers and Network according to the\ncustomer specific needs.\n• Designing and implementations of LAN /WAN, protocols used like TCP /IP, UDP, DHCP, HTTP,\nFTP and TELNET.\n• Providing remote support for maintaining Pentium, Pentium II, Pentium III, Servers on\nWindows NT, Windows 2000 Server, MS Windows Server 2003 R2 and MS Windows Server\n2008 R2.\n• Software Office 97 Pro, Office 2000 pro. Office XP Pro, Office 2003, Microsoft Office 2007,\nMicrosoft Office 2010, Microsoft Office 2013, Microsoft Office 2016 & Visual studio\ninstallation, MacAfee, Norton Antivirus, Escan Antivirus and other software & support.\n• Configuration of E-mail Clint software's that is (Outlook Express, Windows mail and\nMicrosoft Outlook.\n• Installation and maintenance of Intel dual processor servers like HP, IBM, Dell Tower and\nRack based server.\n• Managing Network Racks, LAN & WAN.\n• Installation and maintained of Backup media and devices with data backup and data\nrestoration.\n• Maintenance and support of all type of desktops and Notebooks like HP, IBM, and HCL,\nLenovo, Dell, Toshiba, Apple Mac book and All kind of Handheld Devices Etc.\nOPERATING SYSTEMS INSTALLED:\n• Microsoft Azure (Basic Management)\n• Microsoft Windows 2008 Server\n• Microsoft Windows 2003 Server\n• Microsoft Windows 2000 Server\n• Windows 8.1 Professional\n\nhttps://www.indeed.com/r/Anil-Kumar/96983a9dd7222ae5?isid=rex-download&ikw=download-top&co=IN\n\n\n• Windows 8 Professional\n• Windows 7 Professional\n• Microsoft Windows Vista\n• Microsoft Windows 2000 Prof.\n• Microsoft Windows XP Prof.\n• Microsoft Windows ME\n• Microsoft Windows 98\n• Microsoft Windows 95\n\nEDUCATION\n\nMG University\n\nCertification\n\nOXFORD SOFTWARE INSTITUTE\n\nSKILLS\n\nDATA BACKUP (1 year), EXCHANGE (1 year), LAN (1 year), MAINTENANCE (1 year), SAP (1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS\n\nServer Management, Basic Web content management by Joomla or WordPress,\nDesktop/Notebook management, IT consultancy, Data management, Software management,\nNetwork management (Active/Passive devices), Vendor development.\n\nPROFESSIONAL SUUMARY:\n\n13.7 years IT experience in implementation of LAN, WAN, MPLS, Server installation and\nmaintenance, EPABX with VOIP function, Assembling of computers, maintenance and System\nAdministration of Pentium pro, Pentium III, Pentium 4 and Servers, Software, Hardware and\nNetwork management at different organization.\n\n1. TotipotentSC Scientific Product Pvt. Ltd. Gurgoan (Cesca Therapeutics Inc. US): -\nWorking as Assistant Manager-IT from 10th September 2011 to present with all IT job\nresponsibilities include IT management, vendor development, vendor management,\nsolutions, IT asset purchasing, backup of all servers, SAP database server backup in\nNAS with Matrix and NEC Topaz EPABX management.\n\nSpiceworks:- Spiceworks ticketing software management tool implementation for All systems\ntickets management.\n\nCyberoam CR50ia:- Implementation did with Secure VPN Tunnel from Udyog Vihar office to\nFortis Stem Cell lab and implement user based security policy like TCP and IP traffic, remote\nuser's secured login, Firewall and all other policies implementation.\n\nTotipotentRX Cell Therapy Pvt. Ltd.:- Lab setup in Fortis Gurgaon with Airtel Lease Line 1 MB\n\n\n\nwith CCTV and Honeywell Biometric Access Pro 3000.\nCyberoam CR35ing: - Implementation did with Secure VPN Tunnel from Fortis Stem cell Lab to\nSuncity Success Tower Office and implement user based security policy like TCP and IP traffic,\nremote user's secured login, Firewall and all other policies implementation.\n\n2. Pal Business Systems: - Working as a Senior Engineer 24th March 2011 to 9th\nSeptember 2011.\n\nTotipotentSC Scientific Product Pvt. Ltd. Gurgoan: - Working as an IT-Consultant & SAP\nCoordinator for TotipotentSC to Upgrading the Complete IT setup on PAN India.\nImplementation of Domain Server with all the security policies, SAP Server and regular data\nbackup, Thecus 16 TB NAS with automatic backup with secure manage IT Data.\nImplementation History: - 2 IBM X3320 Server, 1 is Domain Controller with users Dedicated\nShared Folder as per data security point of view and 2nd is for SAP Server working as a SAP\nCoordinator daily basis SAP data backup via SQL Server 2008.\n\nNAS:- Thecus 16 TB Disk space and 8 TB uses as a Data backup and rest 8 TB for mirroring, and\ntaken the all server backup with all users data.\n\nLCD Console: - Aten LCD Console 18.5 with 4:1 Dlink KVM Switch.\n\nAnti Virus: - EScan Anti Virus Implementation has done in all users with Data Security Policy.\n\nMailing Software: - IQuinox Postmaster Enterprise implementation has done with the entire\nLocal and remote user's configuration.\n\nLeave for Studies. Leaves for Studies from 1st February 2010 to 23rd March 2011.\n\n3. Dayal Computers: - Working as a Technical Head 9th October 2009 to 31 January 2010.\nManaging all the engineers and Project deployment client management, manage call\nescalation and design Corporate Solution to the customers.\n\n4. INTERLINK Information Systems Pvt. Ltd: - Working As a senior customer support\nExecutive Network & System Maintenance 18th February 2008 to October 2009.\n\nTHE MICRONUTRIENT INITIATIVE: - Working as an IT-Consultant for the Micronutrient Initiative\nwith all the job responsibilities of an IT-Manager through Interlink Information Systems Pvt. Ltd.\nFrom 1st July 2008 to 20th February 2009 With Canada based Exchange server 2003, HP Prolaint\nML150, Tandberg Data Ultrium backup device, 4 ISDN Lines and Cisco PIX Firewall 501 and with\nhand held devices like Blackberry, Remote Technical Support for Asia pacific, And Coordinate\nwith different Vender's for different IT-Assets.\n\nPROJECTS IN THE MICRONUTRIENT INITIATIVE: -\n1. FIRE PROFF SAFE Purchased for MI- IT for keep the data cartridge\n\n2. Upgrade Alcatel-Lucent OmniPCX Office system M cabinet to L cabinet with 24 extra\nports with VOIP, Integrated CTI Server (PIMPhony PC telephony Software), Alcatel\nIntegrated Voice Mail System, and Personal Assistant with 5 destinations.\n\n\n\nINTERNATIONAL AIDS VACCINE INITIATIVE: - Working as a System-Cum-Server Administrator\nthrough Interlink Information Systems Pvt. Ltd. From 1st March 2008 to 30th June 2008 With IBM\nX Series 226 Xeon Server and IBM X Series 205 P-IV Server Network Printers, NAS and with hand\nheld devices like Blackberry.\n\n5. Sysware InfoTech Pvt. Ltd. Delhi: worked as a senior customer support Engineer\nNetwork & System Maintenance 23rd July 2003 to 10th February 2008.\nNational Dairy Research Institute Karnal: Posted as A Network and System Maintenance\nEngineer (Resident) Through SIPL, Delhi at NDRI Karnal from 1st August 2005 to 16th August\n2006\nin 250 Desktops, 300 Printers (Lasers and Desk Jet, MFP, Scanners) and 250 Online and Offline\nUPS, s.\n\nVipul Infrastructure Developers Ltd: Posted as a Network and System Maintenance Engineer\n(Resident) Through SIPL, Delhi from 1st July 2005 to 30th July 2005 in 160 Desktops and 50\nNotebooks with Manage Cisco L-2 and L-3 Devices.\n\nTecumseh Product India Pvt. Ltd. Hyderabad: Posted as A Network Engineer (Resident)\nthrough SIPL, Delhi, from 1st March 2005 to and 30th July 2005 rebuilt complete Network (range\nof D-Link and Cisco Active and Passive components) 200 Desktops and 50 Notebooks.\n\nTecumseh Products India Pvt. Ltd. Ballabhgarh: Posted as a Network and System Maintenance\nEngineer (Resident) Through SIPL, Delhi from 25th June 2004 to 27th February 2005 in 210\nDesktops and 40 Notebooks with Cisco and D-Link Active and Passive Network Devices.\n\nNetwork Accessories Installed:\n\n• UTM's\n• Routers (1700, 3600 Series.)\n• Access points (D-link, Linksys)\n• Network Media [RJ 45, UTP Cat 3, 4, 5, 6, 7\n• Media Converters\n• OFC Termination (With SC, ST Connectors)\n• Modems\n• Hubs\n• Switches (L-2)\n• Switches (L-3)\n• Vlans\n• CCTV DVR's Hikvision", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 78, "text": "Siddharth Choudhary\nMicrosoft Office Suite - Expert\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Siddharth-\nChoudhary/19d56a964e37fa1a\n\nGoal oriented finance professional with a great aptitude to learn. Dedicated to\nCONTACT\nmeeting aggressive/challenging business goals. Logically strong with good\nknowledge in financial management, tax, audit, and computers.\<EMAIL>\n\nDuring my training/articleship period, I have learnt how to analyse business\n[…] […]\ncycles/operations and conduct risk analysis and further audit procedures\nwww.linkedin.com/in/Sid- based on the risk analysis.\nChoudhary\n\nWORK EXPERIENCE\n\nMicrosoft Office Suite - Expert\n\nTally ERP - Intermediate SQL, Chartered -\n\n2013 to 2017\n\nICAI 464/800\nAccountancy\n\nEDUCATION\n\nScore\n\nSKILLS\n\nAUDITING (Less than 1 year), CFA (Less than 1 year), DERIVATIVES (Less than 1 year),\nFINANCIAL ANALYST (Less than 1 year), FINANCIAL STATEMENT ANALYSIS (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nCORE COMPETENCIES\n\n212, Shri Gayathri Elegant,\nKarkhana, Sec-bad. Portfolio management strategies Auditing (Statutory)\nDerivatives and Swaps Financial statement analysis\nConsolidation Ratio/Trend Analysis\n\nCOMPUTER SKILLS 2017- Chartered Level 1 -\nCFA (US)\n\nhttps://www.indeed.com/r/Siddharth-Choudhary/19d56a964e37fa1a?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Siddharth-Choudhary/19d56a964e37fa1a?isid=rex-download&ikw=download-top&co=IN\n\n\nPresent Financial Analyst Cleared", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 79, "text": "Valarmathi Dhandapani\nInvestment Banking Operations/PMO\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Valarmathi-Dhandapani/\na2b3eb340068764d\n\nA competent professional having an experience of 13 years in the areas of ITES, Banking\nand Project Management operations. Presently associated with Infosys Bangalore as a Senior\nProcess Specialist. Have experience in Banking, Capital Markets and Project Management areas.\nPossesses good analytical and communication skills.\n\nWilling to relocate to: Bangalore City, Karnataka - Hosur, Tamil Nadu - Coimbatore, Tamil Nadu\n\nWORK EXPERIENCE\n\nPMO\n\nInfosys BPO Ltd -\n\nJune 2017 to Present\n\nResponsibilities\n• Tracking and creating demands for resource allocation and buffer utilization.\n• Project financials, budget submissions for project codes to ensure revenue recognition and\ntransfer between departments involved in the program\n• Created new milestones for FP projects based on SOW/task order signed. Supported the\ninvoicing and payments for T&M projects\n• Maintaining All project related documents in SharePoint repository in a structured way and\nensure its up to date.\n• Renewals of Oracle product license AMC's for Citizens bank\n• Invoicing Citizens and Oracle vendor to ensure back to back payments\n• Closely worked with Finance and Procurement team for PO creations and dependent activities\n• In charge of resource Onboarding and Offboarding. BGV related documents compilation.\n• Prepared and created project presentation documents with manager for Client meetings.\nProvided graphics, metrics, charts and dashboards for project reporting\n• Collecting, tracking timesheets, efforts of team and following up with team to ensure\ncompliance is met for submission and to avoid risk to the projects.\n• Facilitate progress review meetings and issue minutes of meeting. Maintained project level risk\nand issue register.\n• Organized client visits and arranged video conference meetings between client and Infosys.\n• Worked on Adhoc user report requests. Involved in project discussions with Clients and project\nmanagers to understand expectations & meet them\n• Managed project inventory (RSA tokens, Virtual desktops, software access etc.)\n\nPMO Analyst\n\nCommerzbank, Infosys BPO Ltd -  Bengaluru, Karnataka -\n\nOctober 2016 to May 2017\n\nBangalore\n\nhttps://www.indeed.com/r/Valarmathi-Dhandapani/a2b3eb340068764d?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Valarmathi-Dhandapani/a2b3eb340068764d?isid=rex-download&ikw=download-top&co=IN\n\n\n(Oct 2016- May 2017)\n\nResponsibilities\n• PO Creation in E-procurement tool for Onboarding subcontractors and vendors affiliated with\nthe client\n• Actively handled and followed up on payments to vendor to ensure ETA is met\n• Acted as single point to contact for Onboarding and Offboarding of offshore and onsite\nresources\n• Tracking and scheduling the project in terms of resources helping them to submit timesheet\nand meet critical compliance metrics. Allocated tasks in the iTime for team members of RTB,\nCTB teams respectively\n• Involved in creating master and child project codes on requirement basis for allocation of\nresources - Resource Management. Allocated the resources to the said codes which is a base for\nproject budgeting and cost estimation\n• Auditing variations between the allocation and budgets provided.\n• Managed the invoicing activity as an anchor to send the invoices communicate with client on\nthe issues or any mismatches in billing amount and the calculations.\n• Acted as liaison amongst lines of business, developers, testers and senior project managers.\n\nEnvironment\nPBS (Project budgeting systems), ECMS (External consultants and vendor managements\nsystems), ALCON (Allocation and confirmation), IPM+ (Integrated Project management)\n\nPMO\n\nCloud Infrastructure Services, Infosys BPO Ltd -  Bengaluru, Karnataka -\n\nDecember 2015 to September 2016\n\nResponsibilities\n• Project Management Operations Team as PMO supporting the Project delivery team\n• Tracking BE reports, creating Invoices, working on reports related to project management\n\nProject Management\n\nDeutsche Bank - Infosys BPO Ltd -  Bengaluru, Karnataka -\n\nJune 2013 to October 2015\n\nBangalore\n(Jun 2013 - Oct 2015)\n\nResponsibilities\n• Responsible to Communicate with internal customers and client on boarding related activities\n• Worked on Project Management tools used for Allocations and Project planning\n• Worked on MIS, MSA reports\n• Setting up reports (Summary reports, dashboards) based on Service Now Tickets, monitoring\nand sending data to clients.\n• Provisioned Secure ID tokens, Virtual desktops to team members from the DB Build team\n• Handled incident and service tickets for application related access and other requirements\n\nFinancial Analyst for AMEX in iFIND\n\n\n\nAmerican Express - Infosys BPO Ltd -  Bengaluru, Karnataka -\n\nFebruary 2012 to May 2013\n\nteam (Issue Filter and Detection Team)\n• In iFIND team worked in CRL applications (Co-brand, Rebates & LAA) to retrieve Metrics\ndata from Mainframe for different functionalities\n• Handled the Admin Q entries in Oracle BPM Tool for Product Transfer scenarios of a Card Member\nenrolled in AMEX\n• Involved in the conference calls with client to discuss and review about the different rejects in\nCRL & PT to caused due to application specific error\n• Liaised and followed up with IT support teams to solve the issues in applications\n\nContract employee for JPM through Infosys\n\nJP Morgan Chase - Infosys BPO Ltd -  Bengaluru, Karnataka -\n\nApril 2011 to January 2012\n\nBangalore\n(Apr 2011 - Jan 2012)\n\nResponsibilities\n• Responsible to remediate the Third-Party Brokerage rates for executing brokers where JPM\nis a clearing broker in the Triparty EGUS agreement in Futures & Options contracts under\nBrokerage team\n• Worked as Contract employee for JPM through Infosys\n\nProcess Specialist\n\nSelect Portfolio Servicing Mortgage- Infosys BPO Ltd -  Bengaluru, Karnataka -\n\nNovember 2008 to March 2011\n\nResponsibilities\n• In REO Team (Real Estate Owned) worked in the process to Approve Invoices from the brokers\nfor the expenses incurred by SPS on the property which they own by the Foreclosure sale\n• Create Checks for the Invoices of HOA (Home Owners Associations) sent by the Attorneys of\nthe state for the property foreclosed by SPS in the foreclosure. The bill is for the maintenance\nof the property by the HOA\n\nSenior Process Associate\n\nUBS- Infosys BPO Ltd -  Bengaluru, Karnataka -\n\nOctober 2004 to October 2008\n\nResponsibilities\n• Instrument Data Strategy and Analytics - Create new securities (Bonds and Futures) in DMSI\ndatabase based on Securities Data provider (Bloomberg, Telekurs International and Euro clear)\nand the information provided by the trading desk.\n• Worked in Account openings of Institutional, Wholesale, FITS accounts in Master files APAC and\nShares AU and NZ, also learnt to use Shares and Australian Fixed Income trading system\n\n\n\n• Provide the Correct information for the securities (Issuer, Coupon details etc.) to downstream\ntrading systems like FOS, Martini, Julius, Ransom and Global 1\n• Worked in Intersystem controls team performing Cash and Stock reconciliations between the\nsystem COLT (Continuous online trading) and SSE (Securities settlement engine)\n• Fixed Income Reconciliation against Bloomberg and Central Data Repository (CDR), keep the\ndata authenticated according to Market conventions till maturity\n• Investigating the issues when the trade fails in FOS and other trading systems due to problems\nin the data and ensured to fix the problems related to Accrued Interest for Corporate and\nMortgage bonds\n\nFIRC-Analytics- Fixed Income Reconciliation against Bloomberg and Central Data Repository\n(CDR), keep the data authenticated according to Market conventions till maturity, Fix the errors\nlike that come in the Analytical output of SPIDER (Excel Macro) with Bloomberg. Maintain the\ncorrect Market data for corporate bonds in CDR according to Bloomberg.\n\nLondon Cash Equities - Worked in Intersystem controls team performing Cash and Stock\nreconciliations between the system COLT (Continuous online trading) and SSE (Securities\nsettlement engine) My primary purpose of the IBBN reconciliations is to ensure that all trades\nthat are booked to the IBBN counterparty in COLT match and settle as soon as possible The total\namount of stock being bought should equal the total amount being sold and cash considerations\nshould match, as each side should be booked at the same price.\n\nHighlights:\n• Worked for Partner Data Management operations assignment in Sydney, Australia\n• Supported client with my knowledge on data management process procedures\n\nProcess Improvements\n− Created shortcuts to do Manual bulk update of the securities from Bloomberg by using Excel\nmacros\n− Documented procedures for exceptional mortgage and Corp bonds to avoid complexity and\nloss of money for the trading desk. This approach was appreciated by the client\n\nEDUCATION\n\nMaster of Science in Bio Informatics in Bio Informatics\n\nBharathiar University Coimbatore -  Coimbatore, Tamil Nadu\n\n2004\n\nB.Sc. in Biochemistry in Biochemistry\n\nRamakrishna College -  Coimbatore, Tamil Nadu\n\n2002\n\nHSC\n\nStanes Anglo Indian High School -  Coimbatore, Tamil Nadu\n\n1996\n\n\n\nSKILLS\n\nExcel (10+ years), Operations (7 years), Project management (5 years)\n\nCERTIFICATIONS/LICENSES\n\nPrince 2 Practitioner\n\nDecember 2017 to December 2020\n\nADDITIONAL INFORMATION\n\nCore Competencies\n\n• Supporting and co-ordinating the Project Management activities of IT projects involved in\nApplication developments and support\n• Ability to work under pressure and to tight deadlines. High comfort level working in a diverse\nenvironment\n• Attention to detail and deadlines, confident and able to work on own initiative and with limited\nsupervision\n• Coordinating process operations for the set-up standards with accountability for strategic\nutilization & deployment of available resources to achieve organizational objectives\n• Mentoring the task force and ensuring development of team for service excellence\n\nIT Skills\n\n− Conversant with MS Office - Word, PowerPoint, Access and Excel.\n− Well versed with Internet Applications", "meta": {}, "annotation_approver": null, "labels": []}
{"id": 80, "text": "Pradeep Kumar\nSecurity Analyst in Infosys - Career Contour\n\nHyderabad, Telangana, Telangana - Email me on Indeed: indeed.com/r/Pradeep-\nKumar/96485546eadd9488\n\nWORK EXPERIENCE\n\nSecurity Analyst in Infosys\n\nCareer Contour -\n\nMarch 2015 to Present\n\nSecurity incident and event management (SIEM) is the process of identifying, monitoring and\nanalysing security events or incidents within a real-time IT environment. It provides centralized\nview of the security scenario of an IT infrastructure\n\nEDUCATION\n\nB- Tech\n\nJawaharlal Nehru Technological University -  Hyderabad, Telangana\n\nSKILLS\n\nSplunk, Network Security, Arc sight (2 years), SIEM (2 years), McAfee ESM (1 year)\n\nADDITIONAL INFORMATION\n\nTechnologies: McAfee SIEM, Arc sight, HP Service manager.\nDescription:\nSecurity incident and event management (SIEM) is the process of identifying, monitoring and\nanalysing security events or incidents within a real-time IT environment. It provides centralized\nview of the security scenario of an IT infrastructure.\nRoles and Responsibilities:\n• Have Experience in device integration, alarm creation in ESM.\n• Having Experience on analysing FIREWALL events.\n• Checking health status for all devices in ESM.\n• Working in Offshore SOC team, Monitoring of SOC events, detecting and preventing the\nIntrusion attempts.\n• Hands-on experience monitoring the SIEM tool.\n• Provide level 2 supports to manage SIEM components.\n• Creating reports as per client requirement.\n• Analysing daily, weekly and monthly reports.\n• Creating case for the suspicious issue and forwarding it to Onsite SOC team for further\ninvestigation.\n• Monitoring the events on SIEM tool.\n• Creating the tickets in ticketing tool.\n\nhttps://www.indeed.com/r/Pradeep-Kumar/96485546eadd9488?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Pradeep-Kumar/96485546eadd9488?isid=rex-download&ikw=download-top&co=IN\n\n\n• Finding false positive, fine tuning and escalating Security events.", "meta": {}, "annotation_approver": null, "labels": []}
