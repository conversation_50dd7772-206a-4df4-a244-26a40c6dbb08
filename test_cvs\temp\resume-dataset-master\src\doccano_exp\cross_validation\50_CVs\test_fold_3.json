{"id": 183, "text": "\"<PERSON><PERSON>    \\xc2\\xb7    June 12, 1982    \\xc2\\xb7\\xc2\\xb7    Last update on October 10, 2014    www.linkedin.com/in/ciesbreijs  \\xc2\\xb7  The  \\xc2\\xb7 Netherlands    <EMAIL>  +31.646469087  cies010 (Skype)  Mathenesserplein 84  3022 ld  Rotterdam    \\xc2\\xb7    Summary  Creative geek with roots in the open source movement, an system administration and programming (Bash, Python,  entrepreneurial mindset and a passion for delivering value Ruby & C++). By 2002 he got his pet project KTurtle  by developing maintainable software.  \\xe2\\x80\\x94a zero-entry-barrier programming environment\\xe2\\x80\\x94 included into KDEs edu module, and thereby almost every  At the age of seven (1989) <PERSON><PERSON> wrote his first lines Linux distribution.  of code in a LOGO-like language on an MSX (pre-PC).  From 2003 to 2007 he studied at the Erasmus UniverTwo years later he attended a conference on an emerging sity Rotterdam and graduated in Business and Computer  new technology, the Internet, at the Erasmus University Science (one curriculum). After graduation he travelled  from which he would graduate 16 years later.  Europe and Asia during a two year sabbatical, on which  After being introduced to the open source movement he \\xe2\\x80\\x9chustled\\xe2\\x80\\x9d several IT gigs (see experiences below) to  in 1997, he taught himself a variety of skills including extend the journey.    Experience  Hoppinger    Rotterdam, The Netherlands  Head of Technology  Apr 12  present  Hoppinger is an open source minded \\xe2\\x80\\x9cfull-service\\xe2\\x80\\x9d internet agency. Reporting directly to the general  director, Marijn Bom. In charge of drawing and carrying out the vision for the tech department  consisting of 15 developers. Streamlined datacenter operations with Puppet, introduced Rails for  custom web-app development and Capistrano for deployment automation. Intimately involved with  the software architecture of all technically challenging projects.    HRO (Rotterdam University of Applied Science)    Rotterdam, The Netherlands  Guest Lecturer  Sep 12  present  Introductory lecture on history of software development and open source for 1st year CS students.    Intellecap/ISTPL    Mumbai, Pune & Hyderabad, India  Nov 08  Feb 09  Intellecap is a social-sector advisory firm serving corporates, non-profits, development agencies and  governments working in developing markets. Assessed their software development team and methodologies, trained their developers and build several web applications. One of those apps is Mostfit, an  open source MIS for microcredit lenders.    IT Consultant    IT & Strategy Consultant    Jan 10  Aug 11  Called in to solve several technical challenges and look at potential growth strategies for Mostfit.    CTO  Oct 11  Feb 12  Proudly joined the C-family of Intellicaps software division, ISTPL, to make Mostfit the nr.1 software  solution for micro credit lenders around the globe. Contracts got terminated half a year later due to  investment issues.    Zarafa    Delft, The Netherlands  Dec 09  Jan 11  Zarafa might be the fastest growing open source product company in Europe, making a drop-in  replacement for MS Exchange. Reported directly to the CEO, Brian Josef, and worked closely with the  CTO, Steve Hardy. In charge of the 6 men strong QA department. Established test automation and  continuous integration. Architected and implemented an all-integrated documentation and translation  system that employed community effort. Got sent to India to analyse and streamline their outsourced  operations.    QA & Release Manager    Dharma Publishing    near San Francisco (CA), USA  Nov 09  Dec 09  Dharma Publishing, the worlds largest Buddhist publisher, is a non-profit, all-volunteer organisation  that helps to preserve Tibetan Buddhism and culture. Built their web shop, and moved their digital  content sales to SaaS applications.    IT Consultant    KDE    edu.kde.org/kturtle  Software Engineer  Dec 03  present  KTurtle is an educational programming environment that simplifies learning the basics of programming.  KTurtle is intended as a gift to future generations: a simple environment to get started with programming.  In 2003 KTurtle got admitted to the KDE project.  Truetopia Project  truetopiaproject.org  Initiator  Nov 07  Apr 10  The Truetopia Project is an open source web application (Rails) to facilitate self-governing communities.  It provides a workflow for collaborative problem identification and solution design.  Dhurakij Pundit University International College  Bangkok, Thailand  Guest Lecturer  Sep 09  Invited by Dr. Pilun Piyasirivej and Mr. Michel Bauwens for two guest lectures: the open source  movement and the semantic web.  Opendream  Bangkok, Thailand  IT Consultant  Aug 09  Sep 09  Architected and largely implemented an open source media sharing web service (REST api) that  facilitates video uploads, transcoding and streaming. Coached their development team on system  design, Ruby development (using Merb/Rails) and testing strategies such as TDD/BDD.  Commuun  Rotterdam, The Netherlands  Senior Visionary  Jul 06  Sep 09  Set up the technical infrastructure, defined the core competences and created a brand together with  Peter Duijnstee (the proprietor of Commuun). Then collaborated on several web applications (all Rails  apps) within the context of his company.  Erasmus University Rotterdam  Rotterdam, The Netherlands  Guest Lecturer  Jul 06  Jul 09  Conducted a guest lecture on the phenomenon of open source, as part of the first year curriculum of  Computer Science & Economics.  The Health Agency  Delft & Rotterdam, The Netherlands  Software Engineer  Jun 05  Feb 06  Worked on their CMS (written in Python and uses PostgreSQL, XML/XSLT and Twisted).  Software Auditor  Dec 06  Assessed their Python/Zope/ZoDB-based web framework re-engineering project.  Please refer to my Linked-in profile for a more complete list of work experiences along with recommendations.    Education  Erasmus University Rotterdam    Rotterdam, The Netherlands  Bachelor degree in Computer Science & Economics  2004  2007  Focused on the economics of open source, rapid application development (RAD) and the semantic web  technology stack (RDF/RDFS, OWL and SPARQL). Picked up quite some Java skills along the way.  Technical University Delft  Delft, The Netherlands  Industrial Design Engineering (discontinued)  2001  2002  Libanon Lyceum  Rotterdam, The Netherlands  VWO (pre-university secondary education)  1994  2000    Skills  Technical expertise: Software design and implementation, with(in) a team. Big fan of Agile methodologies  (Scrum and Kanban), automated deployment (Capistrano) and continuous integration (Hudson/Jenkins).  Enjoys writing Ruby/Python/Java/C++, yet flirts regularly with Haskell. Solid knowledge of web technologies: HTML+CSS, XML, RDF, REST, SOAP and JavaScript (mostly Angular and jQuery). Linux administration  skills: Bash, Apache, MySQL, PostgresSQL, virtualization/cloud (Vagrant, OpenVZ, VMware, KVM, Xen  and EC2), datacenter automation (Puppet and Chef).  Natural languages: Dutch (mother tongue), English (full professional proficiency), German (limited working proficiency), French (elementary proficiency) and Mandarin Chinese (beginner).    Interests  Non-exhaustive and in alphabetical order: art, Buddhism, cryptography, Go (board game), history, music,  open source, philosophy, software engineering (methodologies), travel, typography (e.g. graphic design,  LATEX), UI/UX-design and vegetarian/vegan cooking.\"", "meta": {}, "annotation_approver": null, "labels": [[1, 12, "Name"], [171, 182, "Email Address"], [800, 812, "Years of Experience"], [831, 838, "College Name"], [933, 984, "Degree"], [993, 1019, "College Name"], [1115, 1121, "Location"], [1126, 1130, "Location"], [1393, 1402, "Companies worked at"], [1406, 1432, "Location"], [1434, 1452, "Designation"], [1454, 1469, "Years of Experience"], [1605, 1615, "Name"], [1959, 1998, "College Name"], [2003, 2029, "Location"], [2579, 2620, "Designation"], [2624, 2638, "Years of Experience"], [2994, 3000, "Companies worked at"], [3004, 3026, "Location"], [3028, 3042, "Years of Experience"], [3111, 3117, "Location"], [3191, 3194, "Designation"], [3196, 3207, "Name"], [3238, 3241, "Designation"], [3551, 3571, "Designation"], [3575, 3592, "Companies worked at"], [3601, 3619, "Location"], [3621, 3624, "Location"], [3626, 3640, "Years of Experience"], [3642, 3659, "Companies worked at"], [3884, 3904, "Designation"], [3929, 3946, "Designation"], [3948, 3963, "Years of Experience"], [3965, 3972, "Companies worked at"], [4487, 4535, "College Name"], [4537, 4554, "Location"], [4556, 4570, "Designation"], [4709, 4718, "Companies worked at"], [4720, 4737, "Location"], [4739, 4752, "Designation"], [4754, 4768, "Years of Experience"], [5362, 5390, "College Name"], [5392, 5418, "Location"], [5420, 5434, "Designation"], [5584, 5601, "Companies worked at"], [5603, 5608, "Location"], [5611, 5620, "Location"], [5622, 5637, "Location"], [5639, 5656, "Designation"], [5658, 5672, "Years of Experience"], [5758, 5774, "Designation"], [5776, 5782, "Years of Experience"], [5985, 6013, "College Name"], [6017, 6043, "Location"], [6045, 6092, "Degree"], [6094, 6104, "Years of Experience"], [6299, 6325, "College Name"], [6327, 6349, "Location"], [6351, 6380, "Degree"], [6397, 6407, "Graduation Year"], [6409, 6423, "College Name"], [6425, 6452, "Location"], [280, 288, "Soft Skills"], [337, 358, "Job Specific Skills"], [363, 374, "Job Specific Skills"], [376, 380, "Tech Tools"], [382, 388, "Tech Tools"], [391, 406, "Soft Skills"], [450, 454, "Tech Tools"], [457, 460, "Tech Tools"], [5706, 5712, "Tech Tools"], [5722, 5732, "Tech Tools"], [5734, 5737, "Tech Tools"], [5738, 5742, "Tech Tools"], [5747, 5754, "Tech Tools"], [5799, 5805, "Tech Tools"], [5806, 5810, "Tech Tools"], [5811, 5815, "Tech Tools"], [5822, 5850, "Job Specific Skills"], [6271, 6275, "Tech Tools"], [6223, 6226, "Tech Tools"], [6227, 6231, "Tech Tools"], [6233, 6236, "Tech Tools"], [6241, 6247, "Tech Tools"], [6814, 6830, "Job Specific Skills"], [6832, 6836, "Tech Tools"], [6837, 6840, "Tech Tools"], [6842, 6845, "Tech Tools"], [6847, 6850, "Tech Tools"], [6852, 6856, "Tech Tools"], [6858, 6862, "Tech Tools"], [6867, 6877, "Tech Tools"], [6886, 6893, "Tech Tools"], [6898, 6904, "Tech Tools"], [6907, 6912, "Tech Tools"], [6937, 6941, "Tech Tools"], [6943, 6949, "Tech Tools"], [6951, 6956, "Tech Tools"], [6958, 6969, "Tech Tools"], [6971, 6985, "Tech Tools"], [6986, 6991, "Tech Tools"], [6993, 7000, "Tech Tools"], [7002, 7008, "Tech Tools"], [7010, 7016, "Tech Tools"], [7018, 7021, "Tech Tools"], [7023, 7026, "Tech Tools"], [7032, 7035, "Tech Tools"], [7038, 7059, "Job Specific Skills"], [6913, 6935, "Job Specific Skills"], [7061, 7067, "Tech Tools"], [7072, 7076, "Tech Tools"], [7099, 7104, "Soft Skills"], [7122, 7129, "Soft Skills"], [7163, 7169, "Soft Skills"], [7201, 7207, "Soft Skills"], [7237, 7245, "Soft Skills"], [7410, 7430, "Job Specific Skills"], [7498, 7510, "Job Specific Skills"]]}
{"id": 51, "text": "\"David Shapiro  <EMAIL>  http://www.linkedin.com/in/dgshapiro  Web Developer Resume & Portfolio  Summary    Fifteen years professional Internet industry experience in web development, database design, integration,  implementation, software development, network and systems administration, and training.    Skills  Expert / Advanced  Cake PHP, Drupal CMS, JQuery, Javascript, MySQL, Java, Struts 2, CSS 3, DOM 0-3, AJAX / DHTML,  Dreamweaver, Aptana IDE, Netbeans IDE, Firebug  Competent  Object Oriented PHP, J2EE, JPA, Struts 1, Webwork, Spring, Hibernate, Tomcat, Apache, SSL, JSP, Dojo, A12Y,  I18N, Eclipse IDE, Photoshop, Acrobat, Zend Server, Linux / Unix, Windows  Moderate Exposure  Web Services (WSDL/REST/SOAP), Ant, JSF, JBOSS, SEAM, Glassfish, EJB 3.0, Tiles, UML, JMS, LDAP,  Velocity, Perl, Postgress, Subversion, Oracle Toplink, Oracle SQL  Clients & Project Portfolio  Boys & Girls Club Web Development Consultant 2006 - present    o  o  o    End to end web development  Cake PHP, MySQL, jQuery, CSS, AJAX / DHTML  Project details    Conservation Corps Web Development Consultant 2008 - present    o  o  o    End to end web development  Drupal CMS, Dojo, jQuery, Java, JSP, Struts 2, Spring, Hibernate, MySQL, CSS  Project details    Taproot Foundation Account Director, Project Manager, Web Developer 2006 - present    o  o  o    Recruit multi-disciplinary teams for pro-bono advanced website grants in the non-profit sector  Manage client relationships, project scopes, and time-lines  Manage consulting teams through all project phases- research, draft proposals, design, implementation,    follow-up    o  o  o    Provide high level and detailed technical direction to other developers and designers  Ensure all deliverables conform to Best Practices and current standards  Four year record of 100% client satisfaction    SOPYPAA Web Developer - Pro Bono 2007 - present    o  o  o    End to end web development  Cake PHP, jQuery, MySQL, CSS, AJAX  Project details    ProjectHired Web Development Consultant 2009    o  o    JavaScript development  DHTML menu navigation    UniversalGiving Web Developer - Pro Bono 2007 - 2008    o  o    Web Application development and maintenance- presentation layer and client code  Javascript, Java, JSP, Struts, CVS    Rebuilding Together Peninsula Web Developer - Pro Bono 2005 - 2007    o  o    Implemented complete site redesign  HTML, CSS, JavaScript, PHP    The River Church Community Web Developer - Pro Bono 2002 - 2003    o  o    Web forms and custom content management  HTML, CSS, Java, JSP, JDBC    Selected Corporate Client List (1996 - 2002)  Netsuite, Inc. Senior Webserver Administrator  Narus, Inc. Senior System & Network Administrator  Quova, Inc. Senior System & Network Administrator  Philips Silicon Valley Center Network Engineer  Sun Microsystems (Oracle) System Administrator  Agilent Technologies Web Developer  Netscape Communications (AOL) System Administrator  Infoseek (Go.com) Software Developer  Informix Software (IBM) System Administrator    Education  University of California at Berkeley, BA Psychology  Foothill College, 42 units computer science, 4.0GPA    Project Details    Mid Peninsula Boys & Girls Club    Organizational needs    Transform design mockups into client side code that is cross-browser compatible, and adheres to Web 2.0 standards  and industry best practices. Implement ease-of-use client behaviors and navigation with unobtrusive Javascript and  jQuery. Provide custom content management and information capture for fund-raising events, donors, members, and    community. Ensure that all code is I18N ready for plans to add Spanish and Chinese languages. Ensure that all forms  and data are secure with SSL and data sanitization.    Implementations    o  o  o  o    Cake PHP framework configuration and custom extensions for MVC server-side logic  MySQL configuration as attached data-store for above  jQuery implementation for DHTML, AJAX form submits, validation, and image manipulation  Internationalization (I18N), SSL    Conservation Corps North Bay    Organizational needs  Site redesign to project a more professional appearance to the donor community. Interactive capabilities for  applicants, volunteers, donors, and community members to submit information electronically and reduce paper  usage. Content management features to allow non-technical staff to add, delete, and update dynamic content. Multilanguage capability for Spanish speaking corps members.    Implementations:    o  o  o  o    Version 2.0 (expected September, 2010)    o  o    Version 1.0 (released March, 2009)    Extensive Drupal CMS customization and configuration to meet rapidly changing needs  Complete custom Drupal theming to maintain current design  Interoperability with legacy java based features    Custom CMS, data capture, and administrative application business logic implemented with Java / Struts    2 framework with Spring    o  o  o  o  o  o    Model layer implemented with Hibernate / JPA, Spring / JTA, and MySQL for O/R mapping  Model layer architected to utilize DAO, DTO, Entity Session design patterns  View layer components include JSP, Freemarker, OGNL, Struts Tags  View layer extensions include custom tag library to support Dojo 1.0+  Client code implemented with XHTML, CSS, Javascript  Extensive Dojo implementation for off-the-shelf widgets and custom form widgets, dynamic content    display widgets    o  o    Extensive cross browser compatibility testing  SEO and Google Search integration    Bay Area Young People in AA    Organizational needs    Simple to view website that is mobile friendly and displays as much information as possible with a single page load.  Custom CMS features that are easy to learn and use for an organization with frequent turnover.    Implementations    o  o  o    PHP / Cake PHP framework configuration and custom extensions for MVC server-side logic  MySQL configuration as attached data-store for above  jQuery implementation for DHTML, AJAX form submits, content scrolling and overlay\"", "meta": {}, "annotation_approver": null, "labels": [[1, 14, "Name"], [16, 40, "Email Address"], [80, 93, "Designation"], [125, 139, "Years of Experience"], [902, 919, "Companies worked at"], [920, 946, "Designation"], [947, 961, "Years of Experience"], [1067, 1086, "Companies worked at"], [1086, 1112, "Designation"], [1113, 1127, "Years of Experience"], [1267, 1285, "Companies worked at"], [1286, 1302, "Designation"], [1304, 1319, "Designation"], [1321, 1334, "Designation"], [1335, 1349, "Years of Experience"], [1867, 1880, "Designation"], [1892, 1906, "Years of Experience"], [2017, 2043, "Designation"], [2044, 2048, "Years of Experience"], [2109, 2124, "Companies worked at"], [2125, 2138, "Designation"], [2150, 2161, "Years of Experience"], [2292, 2321, "Companies worked at"], [2322, 2335, "Designation"], [2347, 2358, "Years of Experience"], [2436, 2462, "Companies worked at"], [2463, 2476, "Designation"], [2488, 2499, "Years of Experience"], [2628, 2642, "Companies worked at"], [2614, 2625, "Years of Experience"], [2643, 2673, "Designation"], [2675, 2686, "Companies worked at"], [2687, 2724, "Designation"], [2726, 2737, "Companies worked at"], [2738, 2775, "Designation"], [2777, 2806, "Companies worked at"], [2807, 2823, "Designation"], [2825, 2850, "Companies worked at"], [2851, 2871, "Designation"], [2894, 2907, "Designation"], [2873, 2893, "Companies worked at"], [2909, 2938, "Companies worked at"], [2939, 2959, "Designation"], [2961, 2978, "Companies worked at"], [2979, 2997, "Designation"], [2999, 3022, "Companies worked at"], [3023, 3043, "Designation"], [3058, 3094, "College Name"], [3096, 3109, "Degree"], [3111, 3127, "College Name"], [3138, 3154, "Degree"], [184, 199, "Job Specific Skills"], [201, 216, "Job Specific Skills"], [218, 229, "Job Specific Skills"], [232, 246, "Job Specific Skills"], [248, 268, "Job Specific Skills"], [270, 304, "Job Specific Skills"], [350, 358, "Tech Tools"], [360, 370, "Tech Tools"], [372, 378, "Tech Tools"], [380, 390, "Tech Tools"], [392, 397, "Tech Tools"], [399, 403, "Tech Tools"], [405, 413, "Tech Tools"], [415, 420, "Tech Tools"], [422, 429, "Tech Tools"], [431, 435, "Tech Tools"], [438, 443, "Tech Tools"], [446, 457, "Tech Tools"], [459, 469, "Tech Tools"], [471, 483, "Tech Tools"], [521, 524, "Tech Tools"], [505, 520, "Job Specific Skills"], [526, 530, "Tech Tools"], [532, 535, "Tech Tools"], [537, 545, "Tech Tools"], [547, 554, "Tech Tools"], [556, 562, "Tech Tools"], [564, 573, "Tech Tools"], [575, 581, "Tech Tools"], [583, 589, "Tech Tools"], [591, 594, "Tech Tools"], [596, 599, "Tech Tools"], [601, 605, "Tech Tools"], [607, 611, "Tech Tools"], [614, 618, "Tech Tools"], [620, 631, "Tech Tools"], [633, 642, "Tech Tools"], [644, 651, "Tech Tools"], [653, 664, "Tech Tools"], [666, 671, "Tech Tools"], [674, 678, "Tech Tools"], [680, 687, "Tech Tools"], [722, 726, "Tech Tools"], [727, 731, "Tech Tools"], [732, 736, "Tech Tools"], [739, 742, "Tech Tools"], [744, 747, "Tech Tools"], [749, 754, "Tech Tools"], [756, 760, "Tech Tools"], [762, 771, "Tech Tools"], [773, 780, "Tech Tools"], [782, 787, "Tech Tools"], [789, 792, "Tech Tools"], [794, 797, "Tech Tools"], [799, 803, "Tech Tools"], [806, 814, "Tech Tools"], [816, 820, "Tech Tools"], [822, 831, "Tech Tools"], [833, 843, "Tech Tools"], [845, 859, "Tech Tools"], [861, 871, "Tech Tools"], [987, 1002, "Job Specific Skills"], [1004, 1012, "Tech Tools"], [1014, 1019, "Tech Tools"], [1021, 1027, "Tech Tools"], [1029, 1032, "Tech Tools"], [1034, 1038, "Tech Tools"], [1041, 1046, "Tech Tools"], [1153, 1168, "Job Specific Skills"], [1170, 1180, "Tech Tools"], [1182, 1186, "Tech Tools"], [1188, 1194, "Tech Tools"], [1196, 1200, "Tech Tools"], [1202, 1205, "Tech Tools"], [1207, 1215, "Tech Tools"], [1217, 1223, "Tech Tools"], [1225, 1234, "Tech Tools"], [1236, 1241, "Tech Tools"], [1243, 1246, "Tech Tools"], [1932, 1947, "Job Specific Skills"], [1949, 1957, "Tech Tools"], [1959, 1965, "Tech Tools"], [1967, 1972, "Tech Tools"], [1974, 1977, "Tech Tools"], [1979, 1983, "Tech Tools"], [2060, 2070, "Tech Tools"], [2084, 2089, "Tech Tools"], [2173, 2200, "Job Specific Skills"], [2254, 2264, "Tech Tools"], [2266, 2270, "Tech Tools"], [2272, 2275, "Tech Tools"], [2277, 2283, "Tech Tools"], [2285, 2288, "Tech Tools"], [2406, 2410, "Tech Tools"], [2412, 2415, "Tech Tools"], [2417, 2427, "Tech Tools"], [2429, 2432, "Tech Tools"], [2552, 2556, "Tech Tools"], [2558, 2561, "Tech Tools"], [2563, 2567, "Tech Tools"], [2569, 2572, "Tech Tools"], [2574, 2578, "Tech Tools"], [3459, 3469, "Tech Tools"], [3475, 3481, "Tech Tools"], [3732, 3735, "Tech Tools"], [3795, 3803, "Tech Tools"], [3854, 3857, "Job Specific Skills"], [3877, 3882, "Tech Tools"], [3931, 3937, "Tech Tools"], [3957, 3962, "Tech Tools"], [3964, 3968, "Tech Tools"], [4048, 4051, "Tech Tools"], [4632, 4642, "Tech Tools"], [4818, 4828, "Tech Tools"], [4907, 4911, "Tech Tools"], [4914, 4925, "Tech Tools"], [4941, 4947, "Tech Tools"], [5000, 5009, "Tech Tools"], [5012, 5015, "Tech Tools"], [5017, 5023, "Tech Tools"], [5026, 5029, "Tech Tools"], [5035, 5040, "Tech Tools"], [5093, 5096, "Job Specific Skills"], [5098, 5101, "Job Specific Skills"], [5103, 5117, "Job Specific Skills"], [5165, 5168, "Tech Tools"], [5170, 5180, "Tech Tools"], [5182, 5186, "Tech Tools"], [5188, 5194, "Tech Tools"], [5261, 5270, "Tech Tools"], [5301, 5306, "Tech Tools"], [5308, 5311, "Tech Tools"], [5313, 5323, "Tech Tools"], [5335, 5339, "Tech Tools"], [5499, 5502, "Job Specific Skills"], [5507, 5520, "Tech Tools"], [5716, 5719, "Tech Tools"], [5837, 5851, "Tech Tools"], [5902, 5905, "Job Specific Skills"], [5925, 5930, "Tech Tools"], [5979, 5985, "Tech Tools"], [6005, 6010, "Tech Tools"], [6012, 6016, "Tech Tools"]]}
{"id": 308, "text": "\"CV MARGARITA CECILIA MEYER AREVALO  1. Position:  Legal Manager of Concessions  2. Company:  GRUPO ODINSA. S.A.  3. Name:  Margarita Cecilia Meyer Arevalo  4. Birth Date: December 12 1972    Nationality: COLOMBIANA    5. Academic Studies:  UNIVERSITY  LAWYER  Universidad del Norte  Barranquilla, 1989-1994  Degree 1995  ADVANCED STUDIES IN PUBLIC LAW  Universidad Externado de Colombia.  Bogot\\xc3\\xa1 1996  Degree 1997  POSTGRADUATE IN POLITICAL SCIENCE  Universidad de Salamanca  Salamanca  Espa\\xc3\\xb1a, 1998  Degree 1998  ADVANCED STUDIES IN FINANCIAL LAW  Universidad Colegio Mayor Nuestra Se\\xc3\\xb1ora Del Rosario.  Bogot\\xc3\\xa1, 1998-1999  Degree 2000  OTROS ESTUDIOS  Human Capital Program  ADEN  Alta Direcci\\xc3\\xb3n Business School  Bogot\\xc3\\xa1  2011  Management Development Program  ADEN  Alta Direcci\\xc3\\xb3n Business School  Bogot\\xc3\\xa1  June 2009  Diplomado State Contracting Law 115 of 2007 and its implementing decree  C\\xc3\\xa1mara de Comercio de Bogot\\xc3\\xa1  Bogot\\xc3\\xa1  Mayo de 2008.  Directive Skills Program  ADEN  Alta Direcci\\xc3\\xb3n Business School  Bogot\\xc3\\xa1  June 2007  Diplomado Labor Law and General Social Security System  Universidad de la Sabana    Bogot\\xc3\\xa1  November de 2004  Diplomado State Contracts  Universidad del Rosario  Julio-October 1.997  6. Employment History  Since: September 1st 2014 Until: Currently  Company: Grupo Odinsa S.A.  Position held: Legal Concessions Manager  Since: October 1st 2012 Until: August 31st 2014  Company: Grupo Odinsa S.A.  Position held: Director for Legal Services  Since: September 1st 2000 Until: September 30 2012  Company: Santa Marta  Paraguach\\xc3\\xb3n Concession  Position held: Legal Director  Since: September 30 1996 Until: September 30 2012  Company: Autopistas de los Llanos S.A.  Position held: Legal Director\"", "meta": {}, "annotation_approver": null, "labels": [[4, 35, "Name"], [51, 79, "Designation"], [94, 112, "Location"], [124, 155, "Name"], [241, 259, "Degree"], [261, 282, "College Name"], [284, 296, "Location"], [298, 307, "Graduation Year"], [316, 320, "Graduation Year"], [322, 352, "Degree"], [354, 387, "College Name"], [390, 395, "Location"], [404, 408, "Graduation Year"], [417, 421, "Graduation Year"], [423, 456, "Degree"], [458, 482, "College Name"], [484, 499, "Location"], [510, 514, "Graduation Year"], [523, 527, "Graduation Year"], [529, 562, "Degree"], [564, 623, "College Name"], [626, 631, "Location"], [641, 650, "Graduation Year"], [659, 663, "Graduation Year"], [732, 747, "College Name"], [710, 722, "Degree"], [749, 754, "Location"], [764, 768, "Graduation Year"], [873, 904, "Degree"], [912, 916, "Graduation Year"], [1005, 1017, "Graduation Year"], [1117, 1171, "Degree"], [1173, 1197, "College Name"], [1201, 1206, "Location"], [1216, 1232, "Graduation Year"], [1234, 1259, "Degree"], [1261, 1284, "College Name"], [1286, 1305, "Years of Experience"], [1337, 1372, "Years of Experience"], [1383, 1400, "Companies worked at"], [1417, 1442, "Designation"], [1451, 1491, "Years of Experience"], [1502, 1519, "Companies worked at"], [1536, 1563, "Designation"], [1572, 1615, "Years of Experience"], [1626, 1648, "Companies worked at"], [1685, 1699, "Designation"], [1708, 1750, "Years of Experience"], [1761, 1790, "Companies worked at"], [1807, 1821, "Designation"]]}
{"id": 28, "text": "\"Banquet Sales Manager Resume Template    Debra W. Seles  Greenwich Vill., New York City  Phone: (*************  <EMAIL>  Objective  To work as a Banquet Sales Manager in a prestigious luxury restaurant or five-star hotel  Summary of Qualification      8 years of experience in the hotel hospitality business with the last 3 years assigned as  assistant banquet sales manager   Excellent verbal and written communication, social and interpersonal skills   Excellent organizational and time management skills with ability to work long hours  Career Experience/Job History  2002  Present: Assistant Banquet Sales Manager, Hotel Intercontinental, NYC      Meet and discuss with potential hotel clients concerning their inquiries about hotel facilities  for their banquet requirements like menu items, volume of attendees, date and time along  with other requirements such as sound stage audiovisual facilities, lightning, etc.)   Work with hotel banquet facilities manager in ensuring that all client requirements as  committed in a sales contract can be met on the schedule indicated.   Coordinate with hotel kitchen production chef to ensure that menu items committed in  banquet sales can be met.   Assist the hotel sales managers in promoting banquet sales products   Oversee hotel hands in preparing banquet room to accommodate client requirements.   Provide sales performance reports to hotel management on a regular basis.  Education  2002  Present: In-house seminars and training workshops on hospitality and customer  relations management  1999  2002: BA Hotel and Restaurant Management, University of Wisconsin, River Falls.  Professional reference will be furnished upon request\"", "meta": {}, "annotation_approver": null, "labels": [[42, 56, "Name"], [75, 88, "Location"], [113, 126, "Email Address"], [152, 173, "Designation"], [1, 22, "Designation"], [259, 280, "Years of Experience"], [288, 314, "Job Specific Skills"], [329, 336, "Years of Experience"], [350, 381, "Designation"], [394, 400, "Soft Skills"], [405, 412, "Soft Skills"], [413, 426, "Soft Skills"], [428, 434, "Soft Skills"], [439, 452, "Soft Skills"], [472, 486, "Soft Skills"], [491, 507, "Soft Skills"], [578, 591, "Years of Experience"], [593, 624, "Designation"], [626, 648, "Companies worked at"], [650, 653, "Location"], [766, 786, "Job Specific Skills"], [1367, 1392, "Job Specific Skills"], [1504, 1515, "Job Specific Skills"], [1520, 1550, "Job Specific Skills"], [1552, 1562, "Graduation Year"], [1564, 1598, "Degree"], [1600, 1623, "College Name"], [1625, 1636, "Location"]]}
{"id": 103, "text": "\"NICOLE T. RYERSON    Residence: (XXX) XXX-XXXX  Cell: (XXX) XXX-XXXX    <EMAIL>    Address  City, ST XXXXX    SENIOR MANAGEMENT EXECUTIVE  COO / GENERAL MANAGER / MANAGING DIRECTOR  ~ Consumer Packaged Goods & Appliances Expertise \\xef\\x82\\xa0 Mature, Start-Up & Turnaround Operations ~  ~ International Background - North/South/Central American & European Markets ~  ~ Valuable Network of Contacts Spanning Nearly All Mass Market Distribution Channels ~  Deeply accomplished and results-driven senior management executive with a an extraordinary record of success driving highvalue revenue and profit gains, large-scale cost savings, and improved organizational productivity and performance.  Consistent, documented ability to lead teams in developing new, profitable multimillion-dollar revenue streams and opening  thousands of new distribution points. Superb change agent with proven talents for building customer-focused organizations  that exceed goals year after year and recruiting and developing other leaders with an equal desire to excel and win.    CORE QUALIFICATIONS  General / Operations Management  P&L Management  Strategic Business Planning  Team Building & Leadership    Revenue & Profit Growth  Change Management  Distribution Expansion  Business Process Improvement    Global Business Expansion  Acquisitions & Mergers  Partnerships & Alliances  Key Customer Relationships    PROFESSIONAL EXPERIENCE  Xxxxxxxxxx  City, ST  19XX  20XX  (One of the worlds largest consumer battery and lighting device companies with $1.3 billion sales in more than 115 countries globally.)  MANAGING DIRECTOR, Xxxxxxxxxx (20XX  20XX)  Promoted to direct and provide executive guidance for operations, sales, marketing, finance, human resources, and IT for  European, Middle Eastern, and African operations including a $45 million (output) manufacturing plant, a packaging facility  in Germany, and 6 country-branch offices in Western Europe. Held full P&L management authority and headed 150+ staff.  Led the charge to increase base of customer distribution in international consumer retail channels, a challenge previously  attempted and failed by predecessors. Provided team leadership that successfully won brand recognition and product  placement in Europes most recognized DIY, electronics, and food retailers, generated dramatic category growth, and ramped  customers GMROI to new highs. Established the Rayovac brand as the #1 preferred value brand within the U.K.  Spearheaded organizational restructuring and change management initiatives that transformed the organization into crossfunctional, customer-focused teams that produced striking increases in internal efficiency while improving responsiveness  and solutions to customers challenges. Served as visionary and driving force behind numerous other initiatives, including:    -    Facilities consolidation  Business processes enhancement    -    Packaging cost reductions  Best practices implementation    -    Inventory control and reduction  Product sourcing improvements    Key Results:  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7    Drove 39% profitable revenue growth to a high of $75 million.  Doubled operating profit to $8 million in 20XX from original $4 million in 19XX.  Added 2,500 new distribution doors and increased consumer retail channel sales 59% to $43 million.  Delivered 96% improvement in Xxxxxxx divisions operating profits.  Boosted consumer division operating margin to 9.4% from original 5.6%.  Slashed operating and overhead costs $12 million.  Increased cash flow $3 million.  Championed successful acquisition of a EUR$400 million German battery company.    This r\\xc3\\xa9sum\\xc3\\xa9 is not a template and copying is prohibited; it was written for a real client (identity disguised) and is an  example of the quality and style of r\\xc3\\xa9sum\\xc3\\xa9s written by Distinctive Documents www.distinctiveweb.com.  Copyright 2010  Distinctive Career Services, LLC    NICOLE T. RYERSON  Page 2    VP, GLOBAL SALES OPERATIONS (20XX)  Selected for short-term assignment to play a primary role in transforming Xxxxxxx from a domestic company to one now  competitively operating on 3 separate continents worldwide. Led sales, marketing, and supply chain integration team  following acquisition of a Latin and South American battery company. Required visionary leadership to overcome problems  associated with highly decentralized nature of enterprise, inconsistent inter-company product transfer prices, unsuitable  landed costs, and unacceptably high inventory levels. Formed a global sales team and implemented process to better serve  Wal-Mart on an international basis, including the U.K., Mexico, Korea, and Argentina.  Key Results:  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7    Completed assignment 50% ahead of schedule, achieving all goals in just 3 months.  Won recognition as 1 of 5 original global suppliers to Wal-Mart, leading to new business worldwide.  Laid groundwork that fostered a 25% increase in Latin American sales within 1 year of acquisition.  Cut excess inventory $20 million in 6 months.  Optimized product movement within corporate supply chain by developing new transfer pricing policy.  Rationalized Latin American plant operation in conjunction with EVP of Operations.  Implemented an integrated global consensus forecasting process.    VP, NORTH AMERICAN SALES (19XX  20XX)  Advanced to direct all sales and marketing for a $320 million consumer products business, including leadership of 215 sales,  marketing, merchandising, and business analysis staff. Accelerated organic growth with existing customers and developed  new revenue streams by maximizing distribution and positioning products in nearly every mainstream retail distribution  channel. Restructured organization into customer-focused teams, successfully solving problems with poor performance in  customer delivery metrics and increasing efficiencies in promotion planning cycles and promotions speed to market.  Key Results:  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7    Generated record-setting sales and earnings for 6 consecutive quarters.  Tripled business with Sears to $20 million, growing to distinction as companys 3rd largest customer.  Produced 55% improvement in on-time, accurate, complete shipments to customers.  Expedited speed-to-shelf for new promotions and products 166%, cutting required time 5 months.  Earned honors for company as Vendor of the Year at ShopKo and Wal-Mart Canada.  Selected twice as Vendor of the Quarter by Wal-Mart (US).  Transformed supplier organization into a customer-centric team focused on meeting customer needs.    VP, CONSUMER SALES (19XX  19XX)  Recruited by CEO to drive new, profitable revenue streams and lead expansion of retail distribution. Headed a 32-person  team and managed all sales and channel marketing for the $120 million business. Held dotted-line responsibility for 80 retailmerchandising representatives. Took over during a period of sluggish sales and restructured the organization to achieve 15%  growth targets. Reduced headcount 15% and launched new customer consulting partner program.  Key Results:  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7    Jumpstarted stagnant sales and surpassed net sales and sales margin plans for 8 consecutive quarters.  Produced $30+ million new and incremental business in just 2 years.  Penetrated 4,000 new distribution points including supermarkets, hardware stores, and auto centers.  Refocused sales team initiatives and strategies to dovetail customer initiatives.  Played key role in pioneering Xxxxxxxxs distribution expansion beyond traditional retail channels.    This r\\xc3\\xa9sum\\xc3\\xa9 is not a template and copying is prohibited; it was written for a real client (identity disguised) and is an  example of the quality and style of r\\xc3\\xa9sum\\xc3\\xa9s written by Distinctive Documents www.distinctiveweb.com.  Copyright 2010  Distinctive Career Services, LLC    NICOLE T. RYERSON  Page 3    Xxxxxxxxxxx  City, ST  (Manufacturer of water treatment products for consumer markets.)    19XX  19XX    SALES MANAGER, CONSUMER DIVISION  Joined senior management team to introduce to market a new product line of consumer appliances into traditional retail  channels of distribution, including department stores, warehouse clubs, and home centers. Developed and implemented new  sales organization, recruiting and training 2 regional sales managers and 13 manufacturers rep organizations. Devised and  rolled out all aspects of commercial programs.  Key Results:  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7    Built a full-scale sales organization and all commercial programs from the ground up.  Achieved $12 million revenue target in first year of company.  Opened over 4,500 new doors of distribution in just 3 months.    Xxxxxxx  City, ST  ($180 million marketer and manufacturer of floor care cleaning appliances.)    19XX  19XX    VP, SALES AND MARKETING (19XX  19XX)  VP, SALES (19XX  19XX)  NATIONAL SALES MANAGER (19XX  19XX)  EASTERN REGIONAL SALES MANAGER (19XX  19XX)  Progressed on the fast track through positions of increasing challenge and responsibility to lead a 25-person organization and  direct all strategic marketing and sales plans for the mass retail market, including direct mail response, TV home shopping  network customers, and home centers. As VP of sales, charged with recovering lost customer bases and rebuilding revenues  following Chapter XI bankruptcy and reorganization of company.  Key Results:  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7  \\xef\\x82\\xa7    Grew sales 86% to $164 million in just 3 years and built total company share of market 30%.  Honored as Vendor of the Year at Target (2x), Venture Stores (2x), ShopKo, and Fingerhut.  Named Vendor of the Quarter by Wal-Mart 3 times.  Introduced product line extensions that contributed $10+ million incremental operating profit.  Produced a QVC infomercial that sold 20,000 units in 4 hours, a record to this day.  Increased Wal-Mart sales 5-fold in 4 years as result of introducing new net pricing program.  Doubled business to $130 million between 1986 and 1988 while reducing headcount 22%.    * Early career as a sales representative and unit sales manager with Xxxxxxxxxxxxxxxxxxx Company.    EDUCATION & CREDENTIALS  Bachelor of Arts, Economics  College of Xxxxxxxxxxx  City, ST  Kellogg Graduate School of Management, Finance for Executives Program  Xxxxxxxxx University  City, ST  Member, Vacuum Cleaner Manufacturers Association  Member, Association of Managing Directors, London  This r\\xc3\\xa9sum\\xc3\\xa9 is not a template and copying is prohibited; it was written for a real client (identity disguised) and is an  example of the quality and style of r\\xc3\\xa9sum\\xc3\\xa9s written by Distinctive Documents www.distinctiveweb.com.  Copyright 2010  Distinctive Career Services, LLC\"", "meta": {}, "annotation_approver": null, "labels": [[1, 18, "Name"], [123, 150, "Designation"], [152, 155, "Designation"], [158, 173, "Designation"], [176, 193, "Designation"], [508, 535, "Designation"], [1606, 1623, "Designation"], [1772, 1780, "Location"], [1782, 1796, "Location"], [1802, 1809, "Location"], [1900, 1907, "Location"], [1941, 1955, "Location"], [2425, 2432, "Companies worked at"], [2482, 2486, "Location"], [4039, 4056, "Name"], [4003, 4035, "Companies worked at"], [4068, 4095, "Designation"], [4366, 4390, "Location"], [4755, 4759, "Location"], [4761, 4767, "Location"], [4769, 4774, "Location"], [4780, 4789, "Location"], [5351, 5365, "Location"], [5489, 5513, "Designation"], [5514, 5526, "Years of Experience"], [4096, 4102, "Years of Experience"], [1636, 1648, "Years of Experience"], [6648, 6654, "Companies worked at"], [6659, 6667, "Companies worked at"], [6730, 6732, "Location"], [6720, 6728, "Companies worked at"], [6837, 6855, "Designation"], [6856, 6868, "Years of Experience"], [8146, 8178, "Companies worked at"], [8182, 8199, "Name"], [8302, 8312, "Years of Experience"], [8316, 8348, "Designation"], [9133, 9143, "Years of Experience"], [9147, 9170, "Designation"], [9171, 9183, "Years of Experience"], [9185, 9194, "Designation"], [9195, 9207, "Years of Experience"], [9209, 9231, "Designation"], [9232, 9244, "Years of Experience"], [9246, 9276, "Designation"], [9277, 9289, "Years of Experience"], [9584, 9595, "Designation"], [10059, 10067, "Companies worked at"], [10269, 10277, "Companies worked at"], [10461, 10481, "Designation"], [10486, 10504, "Designation"], [10567, 10583, "Degree"], [10585, 10604, "College Name"], [10630, 10667, "College Name"], [10669, 10699, "Degree"], [10826, 10832, "Location"], [11102, 11134, "Companies worked at"], [73, 92, "Email Address"], [197, 233, "Job Specific Skills"], [257, 263, "Soft Skills"], [303, 327, "Soft Skills"], [392, 411, "Soft Skills"], [586, 603, "Job Specific Skills"], [621, 646, "Job Specific Skills"], [1105, 1126, "Job Specific Skills"], [1128, 1142, "Job Specific Skills"], [1144, 1171, "Job Specific Skills"], [1173, 1199, "Soft Skills"], [1203, 1226, "Job Specific Skills"], [1228, 1245, "Job Specific Skills"], [1247, 1259, "Job Specific Skills"], [1260, 1269, "Job Specific Skills"], [1271, 1299, "Job Specific Skills"], [1303, 1328, "Job Specific Skills"], [1330, 1352, "Job Specific Skills"], [1354, 1378, "Job Specific Skills"], [1380, 1406, "Job Specific Skills"]]}
{"id": 256, "text": "\"R\\xc3\\x89SUM\\xc3\\x89 SAMPLE : HIGHER EDUCATION  Jane E. Smith  2136 G Street NW, Washington, DC 20052  <EMAIL>  EDUCATION\\xef\\xbf\\xbc    202.994-9283    The George Washington University  Graduate School of Education & Human Development  Master of Arts in Higher Education Administration - GPA 4.0    Washington, DC  Anticipated Graduation May 2011    University of Pittsburgh  Bachelor of Arts in English Literature summa cum laude - GPA 3.8  Women Studies Certificate Program  Concentration in Education  Imperial College  Study Abroad Experience    Pittsburgh, PA  May 2007    London, England  January to April 2006    WORK EXPERIENCE  THE GEORGE WASHINGTON UNIVERSITY  Washington, DC  GW Housing Programs  August 2009 to Present  House Mentor  Mentor over 500 third and fourth year undergraduate residents and implement a curriculum that stresses  Professional Development, Life Skills and opportunities unique to GW  Organize and facilitate workshops, speaker series and discussions for residents that will help them prepare for postcollegiate experiences  Advise Resident Advisory Council of 10 residents who plan community building events and initiatives for residents  Mediate and arbitrate roommate conflicts  Respond to campus and community emergencies and perform crisis management within residence hall community  GW Career Center  August 2010 to Present  Career Ambassador/Graduate Intern  Meet with students to discuss professional opportunities including internships, work study and part-time, full-time  employment, and volunteer work  Critique resumes, curriculum vitae, and cover letters for undergraduate students, graduate students, and alumni  Assist students with navigating the online including Vault job database, CareerSearch.net, Career Advisor Alumni  Network, and MyPlan.com  Attend professional development workshops and conferences including the Mid-Atlantic Career Counseling  Association meeting and Partnership for Public Service Federal Advisor Training  Conference and Summer Housing  Lead Summer Associate  Trained and supervised undergraduate summer staff in shifts of 15 to 20 students  Contacted academic program and conference representatives to organize guest services  Maintained financial and administrative records for roughly 5,000 summer guests    March to August 2009    Columbian College of Arts and Sciences  January to May 2009  Graduate Intern  Sent advising correspondence to students with regard to academic performance and progress toward graduation  Utilized Banner System to update student records and record academic information  Shadowed professional advisors during student and professional meetings    R\\xc3\\x89SUM\\xc3\\x89 SAMPLE : HIGHER EDUCATION CONTD  Jane E. Smith    Page 2    Greater Harrisburg Association of REALTORS\\xc2\\xae  Harrisburg, PA  Harrisburg REALTORS\\xc2\\xae Institute  September 2007 to August 2008  Director of Professional Development/Assistant School Director  Organized institute classes for over 2,500 students by contacting instructors, creating schedule, and registering  students  Advised students on courses and designation degree requirements  Maintained and updated aspects of Association professional standards requirements for 1,700 REALTOR\\xc2\\xae members  George T. Harrell Library, Penn State College of Medicine  Library Assistant  Assisted students and library patrons with locating materials needed for research  Organized books and patron files within library computer system    Hershey, PA  May 2004 to August 2008    London, UK  United Kingdom National Literacy Trust  January to May 2006  Literacy Campaign Intern  Assisted \\xe2\\x80\\x98Reading the Game Literacy Campaign director with organizing, scheduling and maintaining campaign  events  Performed website maintenance and updates  HONORS / AWARDS  \\xef\\xbf\\xbc  Deans List  Every semester during undergraduate and graduate coursework  Graduation Speaker, Department of English Literature, 2007  National Society of Collegiate Scholars Member  Gold Key National Honour Society Member  ACTIVITIES / ORGANIZATIONS  \\xef\\xbf\\xbc  THE GEORGE WASHINGTON UNIVERSITY  Washington, DC  Higher Education Student Association (HESA)  April 2010 to Present  Vice President of Academic Affairs  Facilitate Meet the Leaders, which brings nationally renowned higher education scholar-practitioners to The George  Washington University for a speaking event and student discussion\\t\\t  Organize and execute all academic and professional development activities  Serve as chief liaison between HESA and the Educational Symposium for Research and Innovation (ESRI), a student led  academic conference  Columbian College of Arts and Sciences Graduation Committee  January to May 2009  Committee Member\\t  Assisted with facilitation of Columbian College Graduation Ceremonies for roughly 2,000 students by distributing  tickets and organizing line-ups and seating on the National Mall  GW Housing Academic Committee  August 2008 to May 2009  Chair  August 08  May 09  Chaired committee of 10 undergraduate house staff members  Organized and facilitated educational opportunities for community of roughly 2,000 residents including career panel  discussions, cultural events and informal student workshops  Women Administrator in Higher Education  ACPA College Student Educators International  ACPA Commission for Career Development  Higher Education Student Association    August 2009 to Present  August 2008 to Present  August 2008 to Present  August 2008 to Present  OFFICE OF CAREER SERVICES  2136 G Street, NW | Washington, DC 20052  202-994-9283 <EMAIL>  gsehd.gwu.edu/career-services\"", "meta": {}, "annotation_approver": null, "labels": [[49, 62, "Name"], [82, 96, "Location"], [104, 123, "Email Address"], [170, 198, "College Name"], [200, 248, "College Name"], [250, 299, "Degree"], [313, 327, "Location"], [352, 360, "Graduation Year"], [364, 388, "College Name"], [390, 428, "Degree"], [456, 481, "Degree"], [564, 578, "Location"], [580, 588, "Graduation Year"], [592, 607, "Location"], [655, 683, "College Name"], [685, 699, "Location"], [701, 720, "Companies worked at"], [722, 744, "Years of Experience"], [746, 758, "Designation"], [1338, 1354, "Companies worked at"], [1356, 1378, "Years of Experience"], [1380, 1397, "Designation"], [1398, 1413, "Designation"], [2306, 2326, "Years of Experience"], [2330, 2368, "Companies worked at"], [2370, 2389, "Years of Experience"], [2391, 2406, "Designation"], [2728, 2741, "Name"], [2862, 2891, "Years of Experience"], [2893, 2929, "Designation"], [2930, 2955, "Designation"], [3291, 3321, "College Name"], [3492, 3503, "Location"], [3505, 3528, "Years of Experience"], [3532, 3542, "Location"], [3544, 3582, "Companies worked at"], [3584, 3603, "Years of Experience"], [3605, 3629, "Designation"], [4650, 4688, "College Name"], [4689, 4709, "Companies worked at"], [4711, 4730, "Years of Experience"], [4732, 4748, "Designation"], [4782, 4799, "College Name"], [5561, 5575, "Location"], [5596, 5615, "Email Address"], [864, 888, "Job Specific Skills"], [889, 901, "Job Specific Skills"], [2805, 2821, "Location"], [2823, 2860, "Companies worked at"], [3323, 3340, "Designation"]]}
{"id": 104, "text": "\"Barbara Ann Jones  (516) 555-5555 \\xef\\x82\\x98 <EMAIL> \\xef\\x82\\x98 LinkedIn \\xef\\x82\\x98 Facebook  Twitter Handle \\xef\\x82\\x98 My del.icio.us \\xef\\x82\\x98 My StumbleUpon \\xef\\x82\\x98 Podcast Links  Skype: SomeSkypeName \\xef\\x82\\x98 Link to download text and PDF resumes    \\xef\\x82\\x98    Blog    SOCIAL MEDIA MANAGER    www.myportfolio.com    Champion of social media tools and technologies, with a track record of creating and implementing  successful social media programs. Keep up-to-date with constantly evolving technologies in online  social networking, the blogosphere, search tools and Web 2.0, and work closely with clients to  create innovative, effective campaigns.    Partial List of Tools (full list available at myportfolio.com)  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97    Blogger  WordPress  TypePad  Six Apart  Live Writer  Podcasting/  Audacity  FeedBlitz  Bloglines    \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97    LinkedIn  Facebook  MySpace  Flickr  Twitter  Ning  Ryze  HTML/HTML  editors    \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97    YouTube  Digg  del.icio.us  StumbleUpon  Technorati  reddit  Google and  related tools  (Analytics, etc.)    \\xef\\x82\\x97  \\xef\\x82\\x97    \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97  \\xef\\x82\\x97    Camtasia  Help a  Reporter Out  (HARO)  PitchEngine  ReportingOn  Twellow  Wikipedia  Second Life    Experience  XYZ ASSOCIATES, Sometown, NY \\xe2\\x80\\x94 PR firm serving small businesses in the greater NY area  Social Media Manager, 2008 to Present    Partner with some of the most well-known technology companies in New York City to design  and execute social media strategies that meet client objectives.  \\xef\\x82\\x97    Develop and manage online marketing campaigns for ABC Co., DEF Co., GHI Co. and JKL  Co., effectively driving brand awareness, engagement and traffic to social media pages.    \\xef\\x82\\x97    Achieve a strong, visible social media presence and develop concepts with viral potential.  Continuously monitor online public relations and ensure the success of client programs.    \\xef\\x82\\x97    Assess social media marketing strategies to determine rate of return. Identify and tap into  new channels to optimize ROI and fuel revenue growth.    \\xef\\x82\\x97    Place stories in BusinessWeek, Wired News, Computerworld and other key news outlets.  Ensure placement in social content Web sites such as Digg, StumbleUpon, etc.    \\xef\\x82\\x97    Build a strong base of repeat business and serve as the #1 requested IT consultant.    ACTION GROUP, Sometown, NY \\xe2\\x80\\x94 Full-service ad agency  Senior Account Executive / Junior Account Executive, 2005 to 2008    Promoted to senior AE role, overseeing licensee management for Top Broadcasting Co. Drove  a 100% increase in revenue within three years, resulting in annual retail sales of $80M.    Education  ABC UNIVERSITY, Sometown, NY  BA in Marketing, 2004\"", "meta": {}, "annotation_approver": null, "labels": [[1, 18, "Name"], [48, 70, "Email Address"], [314, 334, "Designation"], [373, 408, "Job Specific Skills"], [471, 492, "Job Specific Skills"], [551, 576, "Job Specific Skills"], [582, 593, "Job Specific Skills"], [612, 619, "Job Specific Skills"], [885, 894, "Tech Tools"], [896, 903, "Tech Tools"], [942, 950, "Tech Tools"], [952, 961, "Tech Tools"], [963, 972, "Tech Tools"], [1090, 1098, "Tech Tools"], [1100, 1108, "Tech Tools"], [1110, 1117, "Tech Tools"], [1119, 1125, "Tech Tools"], [1127, 1134, "Tech Tools"], [1136, 1140, "Tech Tools"], [1142, 1146, "Tech Tools"], [1148, 1152, "Tech Tools"], [1311, 1321, "Tech Tools"], [1270, 1277, "Tech Tools"], [1323, 1329, "Tech Tools"], [1481, 1489, "Tech Tools"], [1594, 1608, "Companies worked at"], [1620, 1622, "Location"], [1693, 1713, "Designation"], [1715, 1730, "Years of Experience"], [2732, 2744, "Companies worked at"], [2756, 2758, "Location"], [2796, 2820, "Designation"], [2823, 2847, "Designation"], [2849, 2861, "Years of Experience"], [3059, 3073, "College Name"], [3085, 3087, "Location"], [3089, 3104, "Degree"], [3106, 3110, "Graduation Year"]]}
{"id": 132, "text": "\"Howard B. Walden  Warren Ave., Detroit, Michigan  Phone: ************  <EMAIL>  Objective  To take on a supervisory position as part of a team of automotive designers in a leading  automotive manufacturers  Summary of Qualification      Ten years of experience rising through the ranks in a major automobile manufacturing  company.        Excellent command of 3D modeling and CAD/CAM software tools        Sample of production cars models with major engineering participation available upon  request.    Career Experience/Job History  2000  Present: Assistant Automotive Engineer, Passenger Vehicle, General Motors,  Detroit      Develop components and systems that provide engineered customer value at the least  production cost.        Meet with customers in a product focus group and marketing specialists and designers  to create a concept car that address what people want in a car, station wagon or SUV.        Coordinate with 3rd party suppliers of electronic control systems for accreditation and  acceptance for use in new models.        Coordinate with marketing for pricing structure of new models.        Document crash test results and implement engineering changes as necessary.        Document the features of approved car models.    Education  2000  Present: In house workshops on automotive engineering design, fuel efficiency,  computerize automotive systems.  1996  2000: Bachelor of Science in Automotive Engineering, Michigan Technological  University\"", "meta": {}, "annotation_approver": null, "labels": [[1, 17, "Name"], [32, 49, "Location"], [72, 91, "Email Address"], [250, 273, "Years of Experience"], [373, 384, "Job Specific Skills"], [389, 405, "Job Specific Skills"], [548, 561, "Years of Experience"], [563, 592, "Designation"], [613, 627, "Companies worked at"], [630, 637, "Location"], [1273, 1286, "Years of Experience"], [1310, 1339, "Job Specific Skills"], [1341, 1356, "Job Specific Skills"], [1359, 1389, "Job Specific Skills"], [1392, 1402, "Years of Experience"], [1404, 1449, "Degree"], [1451, 1485, "College Name"]]}
{"id": 2, "text": "Rahul Bollu\nSoftware Engineer - Disney\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Rahul-Bollu/dc40f5ce78045741\n\n• Over 3.5 years of experience in implementing organization DevOps strategy in various\nenvironments of Linux and windows servers along with adopting cloud strategies based on\nAmazon Web Services.\n• Experience in Cloud Technologies like Amazon Web Services (AWS) VPC, EC2, S3, ELB, IAM,\nAuto Scaling, Route 53, SQS, SNS, RDS, Cloud Watch, Dynamo DB.\n• Utilized Cloud Watch to monitor AWS resources to set alarms for notification, and to monitor\nlogs for a better operation of the system.\n• Experience working with automated build platforms/continuous integration using DevOps\narchitecture.\n• Implementing DevOps tools like Ansible as configuration management for Continuous\nIntegration and Continuous Deployment with build tools using Maven on Cloud Infrastructure\nusing AWS.\n• Experience on version control tool GIT- Creating branches, tracking changes, maintaining the\nhistory of code and helping the Developers in GIT related issues.\n• Worked on Jenkins for continuous integration and for End to End automation for all build and\ndeployments.\n• Worked with Ansible as a configuration management tool, created playbooks to automate\nrepetitive tasks, quickly deploy applications, and proactively manage change.\n• Knowledge on Docker.\n\nWORK EXPERIENCE\n\nSoftware Engineer\n\nDisney -\n\nSeptember 2014 to Present\n\nResponsibilities:\n* Coordinate/assist developers with establishing and applying appropriate branching, labeling/\nnaming conventions using GIT source control.\n* Implemented the setup for Master slave architecture to improve the performance of Jenkins.\n* Used Jenkins to implement Continuous Integration and deployment into Tomcat/Web logic\nApplication server.\n* Created Ansible playbooks for automating the Infrastructure, deployment process.\n* Managed clients, roles, tasks, playbooks in Ansible.\n* Deploy and monitor scalable infrastructure on AWS & configuration management.\n* Worked on making application more scalable and highly available in AWS.\n* Created AWS IAM roles & total architecture deployment end to end (creation of EC2 instances\n& its infrastructure)\n\nEnvironment: GIT, Maven, Jenkins, Tomcat, Docker, Jira, AWS, Ansible, LAMP\n\nSoftware Engineer\n\nhttps://www.indeed.com/r/Rahul-Bollu/dc40f5ce78045741?isid=rex-download&ikw=download-top&co=IN\n\n\nHCL Technologies -\n\nSeptember 2014 to Present\n\n〓 Coordinate/assist developers with establishing and applying appropriate branching, labeling/\nnaming conventions using GIT source control.\n〓 Implemented the setup for Master slave architecture to improve the performance of Jenkins.\n〓 Used Jenkins to implement Continuous Integration and deployment into Tomcat/Web logic\nApplication server.\n〓 Created Ansible playbooks for automating the Infrastructure, deployment process.\n〓 Managed clients, roles, tasks, playbooks in Ansible.\n〓 Deploy and monitor scalable infrastructure on AWS & configuration management.\n〓 Worked on making application more scalable and highly available in AWS.\n〓 Created AWS IAM roles & total architecture deployment end to end (creation of EC2 instances\n& its infrastructure).\n\nProcess Associate\n\nMicrosoft -\n\nJuly 2013 to August 2014\n\nResponsibilities:\n* Collect and document user requirements.\n* Design and develop database architecture for information systems projects.\n* Design, construct, modify, integrate, implement and test data models and database\nmanagement systems.\n* Conduct research and provide advice to other informatics professionals regarding the selection,\napplication and implementation of database management tools.\n* Operate database management systems to analyze data and perform data mining analysis.\n\nEDUCATION\n\nBachelor Of Science\n\nVaughn College of Aeronautics and Technology\n\nSKILLS\n\nAWS (3 years), Tomcat, Ansible, git, LAMP, docker, jenkins, Maven, Jira\n\nADDITIONAL INFORMATION\n\nTechnical Skills:\n\nCloud Technologies: AWS\n\nOperating Systems: Linux, Windows.\nVersion Control Systems: GIT\nAutomated Build Tools: Maven\nContinuous Integration: Jenkins\n\n\n\nScripting Languages: Shell Scripting\nConfiguration Management: Ansible.\nContainer service: Docker", "meta": {}, "annotation_approver": null, "labels": [[0, 11, "Name"], [12, 29, "Designation"], [1377, 1394, "Designation"], [1406, 1431, "Years of Experience"], [2277, 2294, "Designation"], [2393, 2409, "Companies worked at"], [2413, 2438, "Years of Experience"], [1396, 1402, "Companies worked at"], [3223, 3247, "Years of Experience"], [3191, 3208, "Designation"], [3210, 3219, "Companies worked at"], [3749, 3768, "Degree"], [32, 38, "Companies worked at"], [40, 60, "Location"], [83, 124, "Email Address"], [133, 142, "Years of Experience"], [3770, 3814, "College Name"], [186, 201, "Job Specific Skills"], [229, 234, "Tech Tools"], [239, 246, "Tech Tools"], [301, 320, "Job Specific Skills"], [338, 356, "Job Specific Skills"], [362, 387, "Tech Tools"], [388, 391, "Tech Tools"], [393, 396, "Tech Tools"], [398, 400, "Tech Tools"], [402, 405, "Tech Tools"], [407, 410, "Tech Tools"], [412, 424, "Tech Tools"], [426, 434, "Tech Tools"], [436, 439, "Tech Tools"], [441, 444, "Tech Tools"], [446, 449, "Tech Tools"], [451, 462, "Tech Tools"], [464, 473, "Tech Tools"], [486, 497, "Tech Tools"], [509, 512, "Tech Tools"], [694, 713, "Job Specific Skills"], [748, 755, "Tech Tools"], [730, 736, "Job Specific Skills"], [896, 899, "Tech Tools"], [860, 865, "Tech Tools"], [869, 889, "Job Specific Skills"], [938, 941, "Tech Tools"], [1074, 1081, "Tech Tools"], [1117, 1138, "Job Specific Skills"], [1184, 1191, "Tech Tools"], [815, 836, "Job Specific Skills"], [1571, 1574, "Tech Tools"], [1675, 1682, "Tech Tools"], [1691, 1698, "Tech Tools"], [1712, 1734, "Job Specific Skills"], [1755, 1761, "Tech Tools"], [1802, 1809, "Tech Tools"], [1978, 1981, "Tech Tools"], [2079, 2082, "Tech Tools"], [2094, 2097, "Tech Tools"], [2214, 2217, "Tech Tools"], [2219, 2224, "Tech Tools"], [2226, 2233, "Tech Tools"], [2235, 2241, "Tech Tools"], [2243, 2249, "Tech Tools"], [2251, 2255, "Tech Tools"], [2257, 2260, "Tech Tools"], [2262, 2269, "Tech Tools"], [2271, 2275, "Tech Tools"], [2560, 2563, "Tech Tools"], [2608, 2633, "Job Specific Skills"], [2664, 2671, "Tech Tools"], [2680, 2687, "Tech Tools"], [2744, 2750, "Tech Tools"], [2910, 2917, "Tech Tools"], [2967, 2970, "Tech Tools"], [3083, 3086, "Tech Tools"], [3445, 3456, "Job Specific Skills"], [3461, 3488, "Job Specific Skills"], [3659, 3687, "Job Specific Skills"], [3715, 3726, "Job Specific Skills"], [3824, 3827, "Tech Tools"], [3839, 3845, "Tech Tools"], [3847, 3854, "Tech Tools"], [3856, 3859, "Tech Tools"], [3861, 3865, "Tech Tools"], [3867, 3873, "Tech Tools"], [3875, 3882, "Tech Tools"], [3884, 3889, "Tech Tools"], [3891, 3895, "Tech Tools"], [3940, 3958, "Job Specific Skills"], [3960, 3963, "Tech Tools"], [3984, 3989, "Tech Tools"], [3991, 3998, "Tech Tools"], [4025, 4028, "Tech Tools"], [4052, 4057, "Tech Tools"], [4082, 4089, "Tech Tools"], [4114, 4129, "Tech Tools"], [4156, 4163, "Tech Tools"], [4184, 4190, "Tech Tools"]]}
{"id": 184, "text": "\"Sumit Khemka    Bangalore, India  <EMAIL>  +91 **********    PROFESSIONAL SUMMARY        8.5+ years of experience in Information Technology, with 2 years at London (UK) and Houston (US).        Domain knowledge in Data Quality, Market Risk and Pricing (MVAR), Health Quality Measures (MU Quality  Reporting), Mortgage Servicing.        Experience in Shared Services, Matrix organization and Agile model of project execution.        Certified MCP and MCTS in SQL Server and Business Intelligence and .NET 2.0.        7 years of experience in Data warehousing and Business Intelligence tools.        6 years strong experience with SQL Server 2005/2008/2012 databases.        6 years strong experience with Microsoft Business Intelligence Suite .ie SSIS,SSRS & SSAS.        2 years working experience on Oracle Databases.        1 year technical hands-on experience on Informatica 8.6/9.0 and Business Objects XI R3 tools.        1.5 years experience working on LogiXML & SpagoBI reporting tool.        Strong experience on T-SQL, Query Optimization and Performance Tuning.        Very good experience and understanding of database design/modeling, OLAP, OLTP systems.        Good experience on .NET framework especially in ASP.NET client/server technology.        Strong knowledge of SDLC such as developing, testing, migrating and production support (L3 support).        Experience on Service & Quality management tools such as Remedy, JIRA, Mercury QC, and ClearQuest.        Experience on configuration management tools such as SVN, Perforce, ClearCase and VSS.        Experience on scheduling tools such as TIDAL, SQL Server Agents, Windows scheduler.        Excellent communication skills bridging Client Interaction and Team Management.        Strong analytical, problem troubleshooting/solving skills, self-starter.        Experience at providing organizational trainings especially in BI tools.        Experience of working with business users, traders and IT.        Exposure to Proposal/Estimation Discussions and SMC model.    TECHNICAL COMPETENCIES    Business Intelligence    : SSIS, SSAS, SSRS, Informatica, Business Objects, LogiXML,SpagoBI    RDBMS    : MS SQL 2005/2008/2012, ORACLE 10g, MS Access, MySQL,    LotusNotes Operating Systems : Windows 2003, XP, Win 7-64bit, Win 8, MS-DOS  Configuration Management    : Rational ClearCase, Visual SourceSafe, Perforce    Service & Quality Management    : JIRA, Remedy, ClearQuest, Mercury QC    Web Technology    : HTML, XML, Jscript, VBScript, ASP, ASP.NET, JSP, WebServices, Basic PHP    Applications & IDE    : VS.NET, BIDS, Visual Studio 6.0, intelliJ IDEA, Eclipse, MS Office,  Flash, Dreamweaver, LotusNotes Designer    Languages    : C#, C, C++, JAVA, T-SQL    Web Server    : IIS, Tomcat    Awards/ Certifications/ Achievements  \\xef\\x83\\xa0    Certificate of Excellence at Altisource for successfully taking care of the Data Quality Program(2014).    \\xef\\x83\\xa0    Certificate of Excellence at QSIH for successfully implementing Meaningful Use Stage 2 program for certifying  EHR systems (April 2013).    \\xef\\x83\\xa0    Microsoft Certified Technology Specialist for Microsoft BI  Implementation and Maintenance 2005/2008, MS  SQL Server 2005, and NET 2.0 - Application Development Foundation (April 2007)    \\xef\\x83\\xa0    Microsoft Certified IT Professional for Microsoft Business Intelligence Developer 2005    \\xef\\x83\\xa0    MSBI-COE (Center Of Excellence) anchor for Systems Integration - Business Intelligence Unit at Infosys.    \\xef\\x83\\xa0    Best Project Team award 2010 for EUS unit at Infosys.    \\xef\\x83\\xa0    Best Freshman Award (Jan-June 07) for Systems Integration - Business Intelligence Unit at Infosys.    \\xef\\x83\\xa0    Nominated as a Microsoft Student Champ, by Microsoft, at Manipal Institute of Technology (2006)    \\xef\\x83\\xa0    Topper at Infosys Foundation Training Program with a CGPA of 5.0/5.0 (Jun  Aug 2006).    \\xef\\x83\\xa0    Microsoft Certified Professional for Microsoft SQL Server 2000 (June 2005)    PROFESSIONAL EXPERIENCE  \\xef\\x83\\xa0    Altisource Business Solutions Pvt Ltd, Bangalore ( Sept 13  Till Date)    Product    : Data Governance & Data Quality    Role    : Lead Data Analyst    Bangalore    Team Size : 11  Project Overview    Responsibilities    Data Quality Program checks the quality of data across various data assets at Altisource and Ocwen.    \\xef\\x82\\xa7    Development of Data Quality Program for various Business Units & Data Systems.    \\xef\\x82\\xa7    Development & Maintenance of Enterprise Data Dictionary through Mediawiki.    \\xef\\x82\\xa7    Development of Metadata & Data Profiler using Kettle    \\xef\\x82\\xa7    Dashboard Reporting using SpagoBI    \\xef\\x82\\xa7    Working on Agile development model.    \\xef\\x82\\xa7    DWH & BI Platform : SpagoBI & Kettle    \\xef\\x82\\xa7    Programming  Oracle, Java    \\xef\\x82\\xa7    Operating System  Windows Server 2003, Win 7    \\xef\\x82\\xa7    Configuration Management  Subversion    \\xef\\x82\\xa7    DBMS: Oracle 11g    Technologies:    \\xef\\x83\\xa0    Quality Systems India Healthcare Pvt Ltd, Bangalore ( Jul 11  Sept 13)    Product    : Health Quality Measures    Role    : Associate Technical Specialist    Bangalore    Team Size : 15  Project Overview    QSI Healthcare is a new subsidiary of Quality Systems, Inc. (QSI),US which develops and market computer-based practice  management and electronic health records (EHR) solutions, ambulatory, inpatient and dental providers to empower patientcentered care and deliver results for medical and dental group practices and hospitals throughout the U.S.    Responsibilities    \\xef\\x82\\xa7    Working on Agile development model.    \\xef\\x82\\xa7    Development of HQM Portal & Quality Reporting    \\xef\\x82\\xa7    Development of new NextGen Dashboard 3.0  Using LogiXML    \\xef\\x82\\xa7    Reporting quality & clinical measures under Meaningful Use, PQRI, BTE programs.    \\xef\\x82\\xa7    Developing Quality dashboards using LogiXML & SSRS    \\xef\\x82\\xa7    BI Platform : SQL Server Reporting Services, LogiXML 10    \\xef\\x82\\xa7    Programming  SQL,C#, ASP.NET,    \\xef\\x82\\xa7    Operating System  Windows Server 2003, Win 7    \\xef\\x82\\xa7    Configuration Management  Perforce  Open Source    \\xef\\x82\\xa7    DBMS: SQL Server 2008 R2/SQL SERVER 2012    Technologies:    \\xef\\x83\\xa0    Infosys Technologies Limited, Bangalore ( Jun 06 - Jul 11)    Client    : British Petroleum ( BP)    Houston, TX (2010-2011)    Project    : Risk Reporting    Bangalore, India(2010- 2011)    Technology Analyst(TA) (Dec 2008  July 2011)  Team Size : 30  BP    : 10 (Manager, BAs, Architect, DBA)    Infosys: 20 Onshore/Offshore    London, UK (2008-2009)    Project Overview    BP is one of the largest traders of oil and gas in the world. Integrated Supply and Trading (IST) team combines all Oil, Gas,  Power, Chemical and Financial trading activities. Objective is the provide support and development for IST Risk Reporting,  NAGP, GTEL MVAR & Oil MVaR applications and also address key weaknesses within the existing system.    Responsibilities    \\xef\\x82\\xa7    Support existing data warehouse reporting platform and applications to ensure operational availability and scalability    \\xef\\x82\\xa7    Work directly with functional analysts and business users in understanding the information needs of the business and  developing new data warehouse/BI functionalities or tools to meet these requirements.    \\xef\\x82\\xa7    Assess existing and available data warehousing technologies and methods to ensure our Data warehouse/BI architecture  meets the needs of the business unit and enterprise and allows for business growth. Provide recommendations on  evolution of the architecture.    Technologies:    \\xef\\x82\\xa7    Research, define and deploy BI improvement opportunities and streamline the business intelligence processes    \\xef\\x82\\xa7    Create detail design, work on development and perform code reviews.    \\xef\\x82\\xa7    Analysis & implementation of better performance strategies.    \\xef\\x82\\xa7    Server : IIS, SQL Server, Oracle, Analysis Services, Reporting Services, Integration Services, Informatica, BO    \\xef\\x82\\xa7    Programming  SQL,C#, ASP.NET    \\xef\\x82\\xa7    Operating System  Windows Server 2003    \\xef\\x82\\xa7    Configuration Management  IBM Clear Case    Client    : Canada Pacific Railways ( CPR )    Project    : Automated Inventory Reporting (AIR)    Bangalore, India    Business Intelligence  Technical Specialist(TS) ( Oct 2008  Dec 2008)  Team Size : 8  CPR    : 3 (Manager, CSE, Bas)    Infosys : 5 ( Manager, TS, Developer)  Project Overview    At CPR, Car Management metrics are used across various departments and are published to the Operations Dashboard and  0700 for senior Canadian Pacific management up to and including the President & CEO. The BI Initiative will provide a  significant upgrade to the current Car Metrics process. Metrics will be centralized into a Car Management Portal in a supported  BI environment. Data will have consistent business rules, hierarchies and standardized metrics and calculations.    Responsibilities    \\xef\\x82\\xa7    Develop technical architecture based on detailed requirements    \\xef\\x82\\xa7    Validate the technical feasibility of business requirements    \\xef\\x82\\xa7    Infrastructure, architecture and environment evaluation    \\xef\\x82\\xa7    Interfaces with Business users and Technical SMEs to clarify requirements, review design, test cases, UAT and  deployment    Technologies:    Client    \\xef\\x82\\xa7    Server : IIS, Analysis Services, Reporting Services, SharePoint Server    \\xef\\x82\\xa7    Programming  C#, ASP.NET    \\xef\\x82\\xa7    Operating System  Windows Server 2003    \\xef\\x82\\xa7    Configuration Management MS Visual Source Safe    \\xef\\x82\\xa7    DBMS: SQL Server, MS Access, Excel.    : Underwriter Laboratories (UL)    Bangalore, India    Project : Datamart Reporting  Systems Engineer  Datamart Developer (Aug 06  Sept 08)  Team Size : 13  Client: 7 (Manager, Bas, Architect, DBA, Dev)  Infosys: 5 offshore, 1 onsite Coordinator/BA  Project Overview    The primary objective of this project to build a robust and intelligent system that will help Business people to strategize based  on analysis of conformed Facts and Dimensions. Designing and implementing the ETL processes which involves collection of  data from heterogeneous sources like LotusNotes Databases, Oracle, SQL Server 2000/2005. Provide maintenance support for  ETL systems. Creation of Datamarts. Incremental processes, LotusNotes Agents, Report Modeling, Creation of business  reports, Dashboard reports, and also the maintenance of web reports using MS Reporting services 2005.    Responsibilities    Technologies:    \\xef\\x82\\xa7    Work closely with onsite Business Analysts for requirement gathering, designing the database and creating workflow.    \\xef\\x82\\xa7    Writing LotusNotes agents, ActiveX script for pulling data from LotusNotes db.    \\xef\\x82\\xa7    Designing and Implementing the ETL processes    \\xef\\x82\\xa7    Creation of Canned reports, Cube-Based Reports, Report Models for Ad-Hoc    \\xef\\x82\\xa7    Coordinating with onsite folks and leading and mentoring the offshore team.    \\xef\\x82\\xa7    Travel to various locations to train people in new project specific technologies    Server : IIS, Analysis Services, Reporting Services  Programming  C#, ASP.NET  Operating System  Windows Server 2003  Configuration Management  IBM Visual Source Safe    DBMS: SQL Server, MS Access, Excel, Lotus Notes Database    \\xef\\x83\\xa0    i2 Technologies Limited, Bangalore (Project Trainee) (Jan  May 06)    Project    : Production Scheduler (PS)    Bangalore, India    Java Developer (Project Trainee)  Team Size : 16  Project Overview    Production Scheduler is designed to provide optimization-based scheduling, shop-floor synchronization, reactive re-scheduling,  and complete reporting and analytics. Production Scheduler is a configurable scheduling application that produces detailed  schedules for multi-stage manufacturing processes.    \\xef\\x83\\xa0    Responsibilities    \\xef\\x82\\xa7    Enhancements to the PS client and Server components, and also Customer issue fixes.    Technologies:    \\xef\\x82\\xa7    Server : OS(Optimal Scheduler)    \\xef\\x82\\xa7    Programming  Java, 3rd Party Widgets, Optimal Scheduler Language( i2 Proprietary Language)    \\xef\\x82\\xa7    Operating System  Windows Server 2003    \\xef\\x82\\xa7    Configuration Management  IBM ClearCase    \\xef\\x82\\xa7    DBMS: MS Access    Angel Movies Enterprises, Mumbai ( As a freelance ) ( May  June 2004)    Development of www.angelmoviesenterprise.org    Manipal, India    Web Developer  Team Size : 1  Project Overview    Angel Movies Enterprises is a professional & experienced production house (Domestic & International Market) in the following  areas.  \\xef\\x82\\xa7    1. Feature-Films (Dhoop, U Bomsi N Me)    \\xef\\x82\\xa7    2. TV Software, Animations, Telefilms (Saturday Suspense etc...).    Responsibilities    \\xef\\x82\\xa7    Designing of complete website.    Technologies:    \\xef\\x82\\xa7    HTML, JavaScript, Flash, Dreamweaver.    TRAINING AT ORGANIZATION      Completed 4 Days/35 PDUs PMP certification Program (Sept 2012).        Advanced Migration from Business Objects 5.x\\\\6.x to XI R2 at Infosys, By Business Objects.        Microsoft SQL Server 2005 by Synergetics-India at Infosys.        Microsoft ASP.NET 2.0 and its new features by Synergetics-India at Infosys.        Microsoft Certified Course on Programming and designing Database using MS SQL Server 2000 at NIIT,  Bangalore (May 2005).        Completed Infosys J2EE Foundation Training Program at Mysore, India.    ACADEMIC DETAILS            Bachelor of Engineering in Computer Science & Engineering      Sept 2002 - May 2006        Manipal University, Manipal, India.        Manipal Institute of Technology (MIT), Manipal, India.    HSC - Science      June 2002        Apeejay School, Saket, New Delhi    INTEREST / ACTIVITIES      Traveled extensively in India, UK, US, Europe.        Part of Energizers team at QSIH.        Participates in the Companys Cultural and Music Events and Workshops.        Novels, DVDs, Movies, Concerts        Live Sports  Cricket, F1\"", "meta": {}, "annotation_approver": null, "labels": [[1, 13, "Name"], [17, 33, "Location"], [35, 56, "Email Address"], [104, 128, "Years of Experience"], [172, 183, "Location"], [188, 200, "Location"], [447, 469, "Degree"], [3108, 3198, "Degree"], [3199, 3208, "Graduation Year"], [3312, 3393, "Degree"], [3394, 3398, "Graduation Year"], [3952, 4014, "Degree"], [4016, 4025, "Graduation Year"], [4071, 4108, "Companies worked at"], [4110, 4119, "Location"], [4122, 4140, "Years of Experience"], [5057, 5097, "Companies worked at"], [5099, 5108, "Location"], [5111, 5126, "Years of Experience"], [5181, 5211, "Designation"], [5215, 5224, "Location"], [6345, 6373, "Companies worked at"], [6375, 6384, "Location"], [6387, 6402, "Years of Experience"], [6419, 6442, "Companies worked at"], [6446, 6457, "Location"], [6504, 6520, "Location"], [6521, 6531, "Years of Experience"], [6536, 6558, "Designation"], [6560, 6579, "Years of Experience"], [8353, 8384, "Companies worked at"], [8483, 8507, "Designation"], [8510, 8528, "Years of Experience"], [9858, 9882, "Companies worked at"], [9891, 9907, "Location"], [9921, 9929, "Companies worked at"], [9930, 9957, "Designation"], [9968, 9977, "Designation"], [9979, 9994, "Years of Experience"], [11590, 11613, "Companies worked at"], [11615, 11624, "Location"], [11626, 11641, "Designation"], [11644, 11655, "Years of Experience"], [11738, 11753, "Designation"], [13753, 13810, "Degree"], [13844, 13862, "College Name"], [13864, 13878, "Location"], [13887, 13924, "College Name"], [13926, 13940, "Location"], [13945, 13958, "Degree"], [13964, 13973, "Graduation Year"], [13816, 13836, "Graduation Year"], [132, 154, "Job Specific Skills"], [229, 241, "Job Specific Skills"], [243, 273, "Job Specific Skills"], [275, 298, "Job Specific Skills"], [324, 342, "Job Specific Skills"], [365, 380, "Job Specific Skills"], [382, 401, "Job Specific Skills"], [406, 417, "Job Specific Skills"], [473, 483, "Job Specific Skills"], [488, 509, "Job Specific Skills"], [514, 523, "Job Specific Skills"], [531, 552, "Years of Experience"], [556, 572, "Job Specific Skills"], [577, 598, "Job Specific Skills"], [644, 669, "Tech Tools"], [719, 756, "Tech Tools"], [761, 765, "Tech Tools"], [766, 770, "Tech Tools"], [773, 777, "Tech Tools"], [816, 832, "Tech Tools"], [881, 900, "Tech Tools"], [905, 927, "Tech Tools"], [974, 981, "Tech Tools"], [984, 991, "Tech Tools"], [1036, 1041, "Tech Tools"], [1043, 1061, "Job Specific Skills"], [1066, 1084, "Job Specific Skills"], [1135, 1159, "Job Specific Skills"], [1161, 1165, "Tech Tools"], [1167, 1171, "Tech Tools"], [1207, 1211, "Tech Tools"], [1236, 1243, "Tech Tools"], [1399, 1427, "Job Specific Skills"], [1442, 1448, "Tech Tools"], [1450, 1454, "Tech Tools"], [1456, 1466, "Tech Tools"], [1472, 1482, "Tech Tools"], [1544, 1547, "Tech Tools"], [1549, 1557, "Tech Tools"], [1559, 1568, "Tech Tools"], [1573, 1576, "Tech Tools"], [1624, 1629, "Tech Tools"], [1631, 1648, "Tech Tools"], [1650, 1667, "Tech Tools"], [1686, 1699, "Soft Skills"], [1716, 1734, "Job Specific Skills"], [1739, 1754, "Job Specific Skills"], [1770, 1780, "Soft Skills"], [1782, 1813, "Soft Skills"], [1822, 1834, "Soft Skills"], [2077, 2099, "Job Specific Skills"], [2104, 2108, "Tech Tools"], [2110, 2114, "Tech Tools"], [2116, 2120, "Tech Tools"], [2122, 2133, "Tech Tools"], [2135, 2151, "Tech Tools"], [2153, 2160, "Tech Tools"], [2161, 2168, "Tech Tools"], [2172, 2177, "Job Specific Skills"], [2183, 2204, "Tech Tools"], [2206, 2216, "Tech Tools"], [2218, 2227, "Tech Tools"], [2229, 2234, "Tech Tools"], [2239, 2249, "Tech Tools"], [2270, 2293, "Tech Tools"], [2301, 2306, "Tech Tools"], [2308, 2314, "Tech Tools"], [2316, 2340, "Job Specific Skills"], [2346, 2364, "Tech Tools"], [2366, 2383, "Tech Tools"], [2385, 2393, "Tech Tools"], [2397, 2425, "Job Specific Skills"], [2431, 2435, "Tech Tools"], [2437, 2443, "Tech Tools"], [2445, 2455, "Tech Tools"], [2457, 2467, "Tech Tools"], [2471, 2485, "Job Specific Skills"], [2491, 2495, "Tech Tools"], [2497, 2500, "Tech Tools"], [2502, 2509, "Tech Tools"], [2511, 2519, "Tech Tools"], [2521, 2524, "Tech Tools"], [2526, 2533, "Tech Tools"], [2535, 2538, "Tech Tools"], [2540, 2551, "Tech Tools"], [2559, 2562, "Tech Tools"], [2590, 2596, "Tech Tools"], [2598, 2602, "Tech Tools"], [2604, 2621, "Tech Tools"], [2623, 2637, "Tech Tools"], [2638, 2645, "Tech Tools"], [2647, 2656, "Tech Tools"], [2659, 2664, "Tech Tools"], [2666, 2677, "Tech Tools"], [2679, 2689, "Tech Tools"], [2717, 2719, "Tech Tools"], [2721, 2722, "Tech Tools"], [2724, 2727, "Tech Tools"], [2729, 2733, "Tech Tools"], [2735, 2740, "Tech Tools"], [2760, 2763, "Tech Tools"], [2765, 2771, "Tech Tools"], [3789, 3820, "College Name"], [4202, 4219, "Designation"], [4158, 4173, "Job Specific Skills"], [4176, 4188, "Job Specific Skills"], [4675, 4694, "Job Specific Skills"], [4701, 4708, "Tech Tools"], [4739, 4756, "Job Specific Skills"], [4803, 4810, "Tech Tools"], [4813, 4819, "Tech Tools"], [4852, 4858, "Tech Tools"], [4860, 4864, "Tech Tools"], [4902, 4921, "Tech Tools"], [4923, 4928, "Tech Tools"], [5010, 5020, "Tech Tools"], [5660, 5677, "Job Specific Skills"], [5817, 5824, "Tech Tools"], [5844, 5861, "Job Specific Skills"], [5943, 5972, "Job Specific Skills"], [5979, 5986, "Tech Tools"], [5989, 5993, "Tech Tools"], [6027, 6056, "Tech Tools"], [6058, 6068, "Tech Tools"], [6101, 6104, "Tech Tools"], [6105, 6107, "Tech Tools"], [6109, 6116, "Tech Tools"], [6155, 6174, "Tech Tools"], [6176, 6181, "Tech Tools"], [6274, 6289, "Tech Tools"], [8401, 8436, "Job Specific Skills"], [8440, 8456, "Location"], [6486, 6500, "Job Specific Skills"], [11702, 11718, "Location"], [11673, 11698, "Job Specific Skills"], [11722, 11736, "Designation"], [14068, 14073, "Location"], [14075, 14077, "Location"], [14079, 14081, "Location"], [14083, 14089, "Location"]]}
