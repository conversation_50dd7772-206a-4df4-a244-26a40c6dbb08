{"id": 98, "text": "<PERSON><PERSON><PERSON>\nSenior System Engineer - Infosys Limited\n\nSalem, Tamil Nadu - Email me on Indeed: indeed.com/r/Kavitha-K/8977ce8ce48bc800\n\nSeeking to work with a software firm, to constantly upgrade my knowledge and utilize my\nexisting skills to benefit the concerned organization\n\nWORK EXPERIENCE\n\nSenior System Engineer\n\nInfosys Limited -\n\nDecember 2014 to Present\n\nUnix, CA7 scheduler\n\nInfosys Limited -\n\nDecember 2015 to February 2018\n\nQlikview Level 1 • Basic knowledge of creating simple dashboards with different\nProduction support style using Qlikview components like List Box, Slider, Buttons,\ncharts and Bookmarks.\n• Created different types of sheet objects like List boxes, Buttons,\nMulti box.\n• Good knowledge of monitoring Qlikview Dashboards\n• Monitoring Critical dashboards and communicating delay to clients\n• Involved in Qlikview initial error analysis and the concerned\nteam to trouble shoot the issue\n• Monitoring Qlikview dashboards from end to end and manually\nrefreshing the tasks if needed\n• Handling service request for manual refresh of dashboards\n• Monitoring Qlikview dependent ETL jobs in CA7 job scheduler\nLevel 1 (BI process) • Involved in monitoring batch jobs in CA7 job scheduler\n• Managing the daily workload based on priorities and maintain\nSLA's to provide quality services to end users\n• Responsible for sending daily and weekly reports to the clients\n• Initial analysis of log files and fixing of environment related\nissue in ETL Process\n• Coordinating with concerned team in troubleshooting of major\nbusiness related issues and sending notification to the clients on timely manner\n• Responsible for all Process related activities like incident\nmanagement and change management\n• Involved in documenting the process, procedures and flow of ETL Process for critical\napplications\n• Respond to user service requests and resolving them with in stipulated time\n• Participated in Incident Management and Problem\nManagement processes for root cause analysis, resolution and reporting\n\nhttps://www.indeed.com/r/Kavitha-K/8977ce8ce48bc800?isid=rex-download&ikw=download-top&co=IN\n\n\nEDUCATION\n\nNetworking, Infosys Limited\n\nFoundation Training Program in Networking\n\n2016\n\nBachelor of Engineering in Information Technology\n\nREiume Institute of road -  Erode, Tamil Nadu\n\n2014", "meta": {}, "annotation_approver": "admin", "labels": [[2292, 2296, "Graduation Year"], [2245, 2269, "College Name"], [2194, 2243, "Degree"], [2145, 2187, "Degree"], [92, 131, "Email Address"], [35, 51, "Companies worked at"], [10, 33, "Designation"], [0, 9, "Name"], [52, 69, "Location"], [293, 315, "Designation"], [317, 332, "Companies worked at"], [336, 360, "Years of Experience"], [362, 381, "Designation"], [383, 398, "Companies worked at"], [402, 432, "Years of Experience"], [434, 442, "Skills"], [545, 553, "Skills"], [730, 749, "Skills"], [832, 840, "Skills"], [1099, 1107, "Skills"], [1111, 1128, "Skills"], [1189, 1205, "Skills"], [1458, 1469, "Skills"], [1772, 1783, "Skills"], [2116, 2143, "College Name"], [2188, 2192, "Graduation Year"], [2273, 2290, "Location"]]}
{"id": 99, "text": "Kavya U.\nNetwork Ops Associate - Accenture\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Kavya-U/049577580b3814e6\n\nSeeking for opportunities to learn and grow in electronics domain.\n\nWORK EXPERIENCE\n\nNetwork Ops Associate\n\nAccenture -  Bengaluru, Karnataka -\n\nDecember 2017 to Present\n\nExposure:\n• Provisioning of different types of network speeds for multiple clients.\n• Use of Mux designing for Logical cross connect and Physical patching. We co- ordinate with\nvarious ops and field engineers to do the connections at the physical\nlevel.\n\n2) Organisation: QuadGen Wireless Engineering Services Pvt. Ltd., Bangalore.\nPosition: Network Engineer\nExperience: 1 year\nExposure:\n• RAN (Radio Access Network) Engineer.\n• New Site Build (NSB)\n\n2) Organisation: Manipal Dot Net Pvt. Ltd., Manipal.\nPosition: Intern\nExperience: 1 year\nExposure:\n• Module coding and verification using Verilog HDL\n• Worked on Linux O.S.\n• Understanding of SPI, I2C protocols\n• Compilation using Altera Quartus\n• Simulation using ModelSim\n• Report preparation and documentation\n\nEDUCATION\n\nLittle Rock Indian School\n\n2007\n\nMaster of Science in Technology in Technology\n\nSchool Of Information Sciences\n\nVLSI Design\n\nhttps://www.indeed.com/r/Kavya-U/049577580b3814e6?isid=rex-download&ikw=download-top&co=IN\n\n\nManipal Academy of Higher Education\n\nBachelor of Engineering in Engineering\n\nSrinivas Institute of Technology -  Mangalore, Karnataka\n\nElectronics and Communication\n\nVisvesvaraya Technological University\n\nVidyodaya P.U. College -  Udipi, Karnataka\n\nSKILLS\n\ncoding (Less than 1 year), HDL (Less than 1 year), Microsoft office (Less than 1 year), MS\nOFFICE (Less than 1 year), UART (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTechnical Skills\n• Verilog HDL\n• Knowledge of RTL coding, FSM based designs.\n• Understanding of UART, AMBA protocol\n• Platforms: Microsoft office, Libreoffice", "meta": {}, "annotation_approver": "admin", "labels": [[1844, 1873, "Skills"], [1844, 1860, "Skills"], [1794, 1830, "Skills"], [1748, 1790, "Skills"], [1742, 1745, "Skills"], [1734, 1745, "Skills"], [1666, 1670, "Skills"], [1636, 1645, "Skills"], [1599, 1615, "Skills"], [1575, 1578, "Skills"], [1548, 1554, "Skills"], [1496, 1518, "College Name"], [1457, 1495, "College Name"], [1368, 1400, "College Name"], [1291, 1326, "College Name"], [234, 244, "Companies worked at"], [211, 233, "Designation"], [87, 124, "Email Address"], [0, 8, "Name"], [9, 30, "Designation"], [33, 42, "Companies worked at"], [44, 64, "Location"], [247, 267, "Location"], [271, 295, "Years of Experience"], [569, 616, "Companies worked at"], [618, 627, "Location"], [639, 666, "Designation"], [668, 674, "Years of Experience"], [687, 722, "Designation"], [765, 790, "Companies worked at"], [792, 799, "Location"], [811, 817, "Designation"], [830, 836, "Years of Experience"], [856, 862, "Skills"], [886, 897, "Skills"], [910, 920, "Skills"], [940, 943, "Skills"], [945, 948, "Skills"], [979, 993, "Skills"], [1013, 1021, "Skills"], [1073, 1098, "College Name"], [1100, 1104, "Graduation Year"], [1106, 1137, "Degree"], [1153, 1183, "College Name"], [1185, 1196, "Degree"], [1328, 1351, "Degree"], [1404, 1424, "Location"], [1426, 1455, "Degree"], [1522, 1538, "Location"]]}
{"id": 100, "text": "Khushboo Choudhary\nDeveloper\n\nNoida, Uttar Pradesh - Email me on Indeed: indeed.com/r/Khushboo-Choudhary/\nb10649068fcdfa42\n\nTo pursue a challenging career and be part of a progressive organization that gives scope to\nenhance my\nknowledge, skills and to reach the pinnacle in the computing and research field with sheer\ndetermination,\ndedication and hard work.\n\nWORK EXPERIENCE\n\nDeveloper\n\nSAP ABAP -  Noida, Uttar Pradesh -\n\nJanuary 2018 to May 2018\n\nSAP ABAB 7.4 Noida, Uttar\n(5 months) Pradesh\n\nTechnical Proficiency\n1. SAP ABAP 7.4 2. OOPS\n3. DBMS 4. Core Java\n5. C/C++ 6. Data Structures\n\nRoles and Responsibilities\n• Creating report generating modules.\n• Creating interactive modules for trainers to train.\n\nOfficial Projects\n1. Uploading file from non-sap system to sap system using BAPI.\n2. Uploading excel data using BDC.\n3. Generating Adobe forms.\n4. Creating smart forms for order purchasing.\n5. Automatic email sending module using workflow.\n6. Creating classical reports.\n7. Creating function module.\n\nEDUCATION\n\nB.Tech in CSE\n\nMM University\n\n2013 to 2017\n\nCBSE\n\nhttps://www.indeed.com/r/Khushboo-Choudhary/b10649068fcdfa42?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Khushboo-Choudhary/b10649068fcdfa42?isid=rex-download&ikw=download-top&co=IN\n\n\nParatap Public School -  Karnal, Haryana\n\nDecember 2011\n\nCBSE in Technology Used\n\nSilver Bells Public School -  Muzaffarnagar, Uttar Pradesh\n\nOctober 2009\n\nEngineering College\n\nSKILLS\n\nANDROID (Less than 1 year), CISCO (Less than 1 year), NETWORKING (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nDevelopment Skills.\n\nMay 2016 1.5 Learned about \"Basic Networking\" using Cisco MMEC, Mullana\nPacket Tracer.\n\nJune, 2015 2 Built an application which have functionality of Solitaire Infosys inc.\nconverting a text into speech using text to speech class Mohali, India\nnamed \"kid'z speak\" using Android.", "meta": {}, "annotation_approver": "admin", "labels": [[1466, 1549, "Skills"], [1040, 1053, "College Name"], [1025, 1038, "Degree"], [389, 398, "Companies worked at"], [378, 387, "Designation"], [73, 123, "Email Address"], [30, 35, "Location"], [19, 28, "Designation"], [0, 18, "Name"], [37, 50, "Location"], [401, 421, "Location"], [425, 449, "Years of Experience"], [451, 463, "Skills"], [464, 476, "Location"], [576, 591, "Skills"], [522, 534, "Skills"], [538, 542, "Skills"], [546, 550, "Skills"], [554, 563, "Skills"], [567, 572, "Skills"], [1055, 1067, "Graduation Year"], [1306, 1321, "Location"], [1393, 1421, "Location"], [1281, 1303, "College Name"], [1323, 1336, "Graduation Year"], [1338, 1356, "Degree"]]}
{"id": 101, "text": "kimaya sonawane\nThane, Maharashtra - Email me on Indeed: indeed.com/r/kimaya-\nsonawane/1f27a18d2e4b1948\n\nQuality education blended with sense of responsibility to utilize my professional as well as\ninterpersonal skills that enables me to achieve the goals.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nTechnical Support Engineer\n\nSAP -  Thane, Maharashtra -\n\nNovember 2016 to Present\n\nEDUCATION\n\nBE in computer science\n\nSSVPS’s Late B. S. Deore College of Engineering ,Dhule -  Dhule, Maharashtra\n\n2011 to 2016\n\nSKILLS\n\nnetwork engineers, Networking, CCNA, knowledge of Active Directory, DHCP, DNS ,\nTroubleshooting and fix Network related issues (2 years)\n\nCERTIFICATIONS/LICENSES\n\nCCNA(Cisco Certified Network Associate- Routing & Switching) , MCSA\n(Microsoft Certified Solution Associate)\n\nJuly 2016 to Present\n\nADDITIONAL INFORMATION\n\nPROFESSIONAL INTRESTS:\n• Leading and managing teams\n• Interacting with People\nCO CURRICULAR ACTIVITES:\n• Participated in \"Mech-Tricks\" in IMPULSE 2014 National Level Event.\n• Participated in \"Mech-Tricks\" in IMPULSE 2013 National Level Event.\n• Participated in \"Tech-Quiz\" in IMPULSE 2013 National Level Event.\n• Participated in \"Management Games\" Organised in Ganesh Utsav 2012.\n• Winner in \"Rangoli Competition\" Organised in Ganesh Utsav 2013.\n\nhttps://www.indeed.com/r/kimaya-sonawane/1f27a18d2e4b1948?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/kimaya-sonawane/1f27a18d2e4b1948?isid=rex-download&ikw=download-top&co=IN\n\n\nPERSONAL TRAITS:\n\n• Self Motivated\n• Adaptable\n• Confident\n• Team facilitator\n• Hard Worker", "meta": {}, "annotation_approver": "admin", "labels": [[524, 661, "Skills"], [424, 471, "College Name"], [400, 422, "Degree"], [334, 339, "Companies worked at"], [306, 332, "Designation"], [57, 103, "Email Address"], [0, 15, "Name"], [16, 34, "Location"], [341, 359, "Location"], [361, 387, "Years of Experience"], [473, 500, "Location"], [502, 514, "Graduation Year"], [687, 747, "Degree"], [750, 795, "Degree"], [797, 817, "Graduation Year"], [1510, 1524, "Skills"], [1527, 1536, "Skills"], [1539, 1548, "Skills"], [1551, 1567, "Skills"], [1570, 1581, "Skills"]]}
{"id": 102, "text": "Koushik Katta\nDevops\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Koushik-Katta/a6b19244854199ec\n\nDevOps Administrator with an experience of 3.4 years working in a challenging agile\nenvironment, looking forward for a position where I can use my knowledge pursuing my domain\ninterests. I'm more aligned to work for companies where knowledge and intellectual ability takes\nthe lead which can utilize a performance driven individual efficiently.\n\nWORK EXPERIENCE\n\nDevops Engineer\n\nInfosys limited -  Hyderabad, Telangana -\n\nDecember 2014 to Present\n\nHyderabad, since December 2014 to till date.\n\nSkill and Abilities:\nAtlassian Tools: Jira, Confluence\nConfiguration Management: Ansible /Chef\nCI Tools: Jenkins\nMonitoring Tools: Nagios\nCloud: AWS\nContainerization: Docker\nBuild Tools: Bamboo\\Maven\nLog Tools: Splunk\nDatabases: RDBMS, MYSQL, Oracle Database\nProgramming Languages: Python and Java\nScripting: Power Shell\nOperating Systems: Windows, Linux family, Redhat Linux\nMiddleware: Websphere, Tomcat, Websphere MQ\n\nResponsibilities:\nDEVOPS ADMINISTRATOR\nINFOSYS LTD.\n\nAtlassian tools Release Management according\n\nInfosys limited -\n\nDecember 2014 to Present\n\nto project needs.\n✓ Review and upgrade of Plugins to meet project requirements and to achieve better\nperformance.\n✓ Configuring Automated Mail handlers, Webhooks as POC to test the new demands raised by\nclient.\n\nhttps://www.indeed.com/r/Koushik-Katta/a6b19244854199ec?isid=rex-download&ikw=download-top&co=IN\n\n\n✓ JIRA Project Management/Administration.\n✓ Confluence Space Management/Administration.\n✓ Bitbucket Project/Repository Management/Administration (Enterprise/DataCenter)\n✓ Integration of Webhooks in Bitbucket.\n✓ Streamlining tools access management with Crowd.\n2. Administration and Maintenance of Jenkins\n✓ Configure and maintain Jenkins slaves as per the requirement.\n✓ Jenkins release management.\n✓ Work closely with Development teams to configure CI/CD Pipelines to automate their build &\ndeployment process.\n✓ Review, Installation/Upgrade and configuration of Jenkins Plugins.\n✓ Configuring proxy on the environments to enable security\n✓ Debug build issues\n3. Administration and Maintenance of Docker registry\n4. Working with Open-Source Nagios plugins on demand basis to setup ICINGA monitoring for our\non-premise Servers/Applications.\n5. Alerting Setup Splunk.\n6. Monitoring Dashboards setup using kibana.\n7. Working with product support teams to resolve the product bugs.\n8. Involve in client meetings and tool performance reviews to ensure stakeholder satisfaction.\n9. Work closely with Infrastructure Teams to setup/maintain/improve the above mentioned\napplication on large scale.\n\nEDUCATION\n\nBachelor Of Engineering in Mechanical Engineering\n\nLovely Professional University\n\n2010 to 2014\n\nSecondary School Certificate in education\n\nBoard of Intermediate education -  Hyderabad, Telangana\n\n2008 to 2010\n\nSister Nivedita School -  Karimnagar, Telangana\n\n2008\n\nSKILLS\n\nJira, Ansible, Jenkins, Splunk, Nagios, Docker, Python, AWS, Bamboo, Linux, Git, Chef, Windows,\nPowershell Scripting\n\nADDITIONAL INFORMATION\n\n• Ability to learn new technologies and processes rapidly and implement them in the project.\n• Highly motivated with very good problem solving and analytical skills Well organized, with\nexcellent in multitasking and prioritizing the work.\n\n\n\n• Effective communicator with an ability to convey ideas in speaking and writing.\n• Excellent analytical and decision making skills.\n• Ability to work in pressurized situation.\n• Hard worker and goal oriented.\n• Always ready to learn new skills", "meta": {}, "annotation_approver": "admin", "labels": [[2957, 3074, "Skills"], [2780, 2822, "Degree"], [2734, 2764, "College Name"], [2683, 2732, "Degree"], [1125, 1140, "Companies worked at"], [1044, 1064, "Designation"], [626, 1024, "Skills"], [559, 568, "Location"], [490, 505, "Companies worked at"], [65, 108, "Email Address"], [0, 13, "Name"], [14, 20, "Designation"], [22, 42, "Location"], [110, 130, "Designation"], [153, 162, "Years of Experience"], [473, 488, "Designation"], [507, 529, "Location"], [533, 557, "Years of Experience"], [570, 602, "Years of Experience"], [1144, 1168, "Years of Experience"], [1065, 1077, "Companies worked at"], [1483, 1521, "Skills"], [1525, 1567, "Skills"], [1571, 1649, "Skills"], [1667, 1688, "Skills"], [1692, 1739, "Skills"], [1778, 1785, "Skills"], [1811, 1818, "Skills"], [1852, 1859, "Skills"], [1931, 1946, "Skills"], [2045, 2060, "Skills"], [2123, 2128, "Skills"], [2179, 2194, "Skills"], [2223, 2237, "Skills"], [2263, 2269, "Skills"], [2340, 2346, "Skills"], [2351, 2391, "Skills"], [2766, 2778, "Graduation Year"], [2823, 2854, "College Name"], [2858, 2878, "Location"], [2880, 2892, "Graduation Year"], [2920, 2941, "Location"], [3194, 3210, "Skills"], [3221, 3241, "Skills"], [3246, 3263, "Skills"], [3263, 3278, "Skills"], [3285, 3310, "Skills"], [3425, 3472, "Skills"], [3520, 3531, "Skills"], [3536, 3549, "Skills"], [3560, 3574, "Skills"]]}
{"id": 103, "text": "Kowsick Somasundaram\nCertified Network Associate Training Program\n\nErode, Tamil Nadu - Email me on Indeed: indeed.com/r/Kowsick-\nSomasundaram/3bd9e5de546cc3c8\n\nBachelor of computer science graduate seeking opportunities in the field of ITIS to contribute\nto corporate goals and objectives. Easily adapt to changes, with eagerness toward learning and\nexpanding capabilities.\n\nEXPERIENCE:-\n\nWORK EXPERIENCE\n\nCertified Network Associate Training Program\n\nCisco -\n\nJuly 2013 to October 2013\n\n• Workshop on computer Hardware& Software.\n\n• Workshop on Web development.\n\nEDUCATION\n\nBachelor of computer science in computer science\n\ninDR N.G.P ARTS AND SCIENCE COLLEGE -  Coimbatore, Tamil Nadu\n\nSKILLS\n\nDHCP (Less than 1 year), DNS (Less than 1 year), EXCHANGE (Less than 1 year), exchange\n(Less than 1 year), LAN (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS:-\n\n• Messaging: MS exchange, Lotus client and MS outlook issue coordination to user.\n\n• Users / Share folders creation and permission assigning.\n\n• Networking: TCP/IP, DNS, DHCP, and LAN/WAN.\n\n• Monthly patching update activity and server owner approval / RFC follow-ups.\n\nhttps://www.indeed.com/r/Kowsick-Somasundaram/3bd9e5de546cc3c8?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Kowsick-Somasundaram/3bd9e5de546cc3c8?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": "admin", "labels": [[696, 1129, "Skills"], [625, 661, "College Name"], [575, 624, "Degree"], [451, 457, "Companies worked at"], [406, 450, "Designation"], [107, 158, "Email Address"], [21, 65, "Designation"], [0, 20, "Name"], [67, 84, "Location"], [160, 188, "Degree"], [461, 486, "Years of Experience"], [664, 686, "Location"]]}
{"id": 104, "text": "Lakshika Neelakshi\nSenior Systems Engineer - Infosys Limited\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Lakshika-\nNeelakshi/27b31f359c52ef76\n\nAn organized and independent individual looking for role to be able to effectively coordinate\ntasks in\nproject and accomplish the result adhering with timeliness and creativity.\n\nWORK EXPERIENCE\n\nSenior Systems Engineer\n\nInfosys Limited -  Bengaluru, Karnataka -\n\nJanuary 2018 to Present\n\nEnvironment SAPUI5 version 1.4\n\nDescription:\nAirbus SE is a European multinational corporation that designs, manufactures, and sells civil and\nmilitary aeronautical products worldwide.\n\nProject Contribution:\n• Working on creating a custom Annotation Tool (AnnoQ) using a third party js library to annotate\nany 2D picture within a SAPUI5 application specifically designed to work on laptop and Desktop.\n• The custom tool can be called from any SAP or Non- SAP system to be used as a plug-in to\nannotate pictures across the Airbus SE application to be used across browsers like\nChrome/Edge and IE and should be compatible with Windows 7, 8, 10.\n• Parts of a picture can be highlighted and marked and using this tool and can be saved for\nreference or future use.\n• Worked extensively on Fabric js and created various customized objects like Call out Box,\nMeasurement Arrow, Datum, Cross Datum, Forward arrow, etc on mouse and object events.\n• Implemented various functionality like color change, crop, zoom, text size selection, width\nselection, saving the annotation in JSON format in the backend so as to retain the original\npicture as it is.\n• Also contributed in designing the layout and overall appearance of the tool.\n• Contributed in integrating the application with MNC Mobile Application.\n\nSenior Systems Engineer\n\nInfosys Limited -  Bengaluru, Karnataka -\n\nOctober 2015 to Present\n\n• Having 2.5 years of experience in SAPUI5/Fiori, Netweaver Gateway Odata Services and SAP\nABAP development of large scale ERP packages.\n• Working with SAP/R3, ECC environments also having experience in HTML, JavaScript, JSON,\nXML, CSS.\n\nhttps://www.indeed.com/r/Lakshika-Neelakshi/27b31f359c52ef76?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Lakshika-Neelakshi/27b31f359c52ef76?isid=rex-download&ikw=download-top&co=IN\n\n\n• Currently working as Senior Systems Engineer with Infosys Limited, Bangalore since October\n2015.\n\nSAP Expertise\nSAPUI5/Fiori\n\n• Expertise in developing SAP UI5 applications using ADT (Eclipse) /WebIDE, jQuery, JavaScript,\nHTML5 and CSS3. Also consuming the data by using Net weaver Gateway services.\n• Well equipped in extending and customizing various UI5 controls specially charts, vizframes\nand Smart Table, etc.\n• Experience in integrating Gateway Odata/ JSON services with UI% application.\n• Worked extensively with application having multiple Views and controllers and hence\nexperienced in navigating through it using Routing using both XML and JS views.\n• Exposed to be working in SAPUI5 Custom Applications and Standard Fiori Applications.\n• Experienced with UI5 application development in local server and debug it in Chrome/Firefox\ndebuggers.\n• Experienced in Github.\n\nAdvance Business Application Programming\n• ALV (ABAP List Viewer) - Grid and List Display.\n• SAP smart forms and Scripts.\n• Worked in BDC (Batch Data Communication) - BI and CTU method\n• RFC Function Modules.\n• Exposure in creating Data Dictionary objects (Domain, Data Elements, Structures and Views)\n• Conceptual knowledge Dialog programs using Menu Painter/Screen Painter\n• Worked on Object Oriental Programming concepts (OOPS)\n\nSystems Engineer\n\nInfosys Limited -  Bengaluru, Karnataka -\n\nAugust 2017 to December 2017\n\nEnvironment SAPUI5 version 1.28\n\nDescription:\nJabil Inc. is a US based global manufacturing services company headquartered in St. Petersburg,\nFlorida. Jabil is involved in design engineering services. The company has industrial design\nservices\nthat concentrate on designing the look and feel of plastic and metal enclosures that house printed\ncircuit board assemblies and systems.\n\nProject Contribution:\n• Created custom Fiori apps for the client including screens having functionality of create\ndocument, Inbox to store the transaction mail and Dashboards to view day to day transactions.\n• Prepared multiple screens for the dashboard with view reusability features.\n• Worked on OData binding and hence display of data in relevant format on to the screen.\n• Implemented various other functionalities based on OData consumption, Routing and\nNavigation and JSON models.\n\n\n\n• Worked on complex functionality like excel data transfer directly to UI5 tables on screen while\nkeeping the table data editable and also excel data upload functions to the table.\n• Have clear and distinct knowledge of various UI5 controls.\n\nSystems Engineer\n\nInfosys Limited -  Hyderabad, Telangana -\n\n2016 to July 2017\n\nEnvironment SAPUI5 version 1.5\n\nDescription:\nAmerican Water is an American public utility company operating in the United States and Canada.\nIt\nwas founded in 1886 as the American Water Works & Guarantee Company.\n\nProject Contribution:\n• Worked as SAPUI5 developer in KPI project for American Waters account.\n• Developed KPI Dashboards using controls like Vizframe, Tables etc.\n• Worked with different types Micro charts control available like Column, Comparison, Radial etc.\n• Also extended some of them as per project requirements.\n• Experience of working with OData to get the data to be displayed on the dashboards.\n• Additionally, implemented some special features in the Dashboard development like Export to\nexcel link, Download Image of the UI charts & documentation link in Fiori tiles.\n• Also worked on multiple levels of drill downs using multiple controllers and views.\n• Individually worked and delivered an extension for one of the UI5 controls which got much\nappreciated from clients and offshore team as well.\n• Implementation of the apps on Fiori Launchpad.\n• Implemented multiple filters on the data pulled from the service for desired results as per\nproject requirements.\n\nSAP UI5:\nUI5 Controls:\n• Created dashboards by various UI5 controls such as Tables, vizFrame, Tab Filters to name a few.\n• Exposure in extending various UI5 standard controls to get the desired result.\n• Used SAP best practices while using all these controls.\n\nSystems Engineer\n\nInfosys Limited -  Hyderabad, Telangana -\n\nJuly 2016 to September 2016\n\nEnvironment SAP 5.0\n\nDescription:\nHarley Davidson is an American motorcycle manufacturer, founded in Milwaukee, Wisconsin in\n1903.\n\nProject Contribution:\n\n\n\n• Innovation track dashboard preparation.\n• Creation of POC application to filter the list based upon multiple filters with the help of Dialog\nProgramming.\n• The application also had functionalities like add new POCs, update and delete the existing\nones and modify others.\n• Data Upload to the SAP system from Excel sheet using BDC.\n• Additionally, the task required knowledge of Data Dictionary, Report Programming- Classical &\nALV, Module Pool Programming, Batch Data Communication- Call Transaction & Session\nMethod.\n\nEDUCATION\n\nBachelor of Engineering in Instrumentation Technology in\nInstrumentation Technology\n\nDayananda Sagar College of Engineering -  Sagar, Karnataka\n\nSKILLS\n\nSAPUI5 (2 years), CSS. (2 years), EMPLOYEE RESOURCE GROUP (2 years), ENTERPRISE\nRESOURCE PLANNING (2 years), SAP ABAP (2 years)\n\nADDITIONAL INFORMATION\n\nTechnical skills\nLanguages SAPUI5 (Primary Skill),\nABAP/4, C, C++, HTML, CSS, JavaScript, PHP,\njQuery, Ajax.\nERP SAP R/3 in 4.7, 5.0 (E)\nOperating Systems Windows\nDatabase MySQL, Oracle", "meta": {}, "annotation_approver": "admin", "labels": [[2329, 2345, "Companies worked at"], [2301, 2324, "Designation"], [1741, 1764, "Designation"], [352, 375, "Designation"], [105, 154, "Email Address"], [44, 60, "Companies worked at"], [19, 42, "Designation"], [0, 18, "Name"], [62, 82, "Location"], [377, 392, "Companies worked at"], [396, 416, "Location"], [420, 443, "Years of Experience"], [1766, 1781, "Companies worked at"], [1785, 1805, "Location"], [1809, 1832, "Years of Experience"], [1843, 1852, "Years of Experience"], [1870, 1882, "Skills"], [1921, 1929, "Skills"], [1986, 1992, "Skills"], [1994, 2010, "Skills"], [2037, 2041, "Skills"], [2043, 2053, "Skills"], [2055, 2059, "Skills"], [2061, 2064, "Skills"], [2066, 2069, "Skills"], [2347, 2356, "Location"], [2357, 2375, "Years of Experience"], [2378, 2381, "Skills"], [2392, 2398, "Skills"], [2399, 2404, "Skills"], [2432, 2435, "Skills"], [2436, 2439, "Skills"], [2459, 2462, "Skills"], [2464, 2471, "Skills"], [2474, 2480, "Skills"], [2482, 2488, "Skills"], [2490, 2500, "Skills"], [2502, 2507, "Skills"], [2512, 2516, "Skills"], [2633, 2636, "Skills"], [2664, 2673, "Skills"], [2678, 2689, "Skills"], [2923, 2926, "Skills"], [2931, 2933, "Skills"], [2724, 2743, "Skills"], [2968, 2974, "Skills"], [3107, 3131, "Skills"], [3150, 3156, "Skills"], [3202, 3224, "Skills"], [3252, 3255, "Skills"], [3326, 3343, "Skills"], [3584, 3588, "Skills"], [3591, 3607, "Designation"], [3609, 3624, "Companies worked at"], [3628, 3648, "Location"], [3652, 3680, "Years of Experience"], [3694, 3700, "Skills"], [4362, 4367, "Skills"], [4492, 4497, "Skills"], [4538, 4542, "Skills"], [4625, 4628, "Skills"], [4782, 4785, "Skills"], [4797, 4813, "Designation"], [4815, 4830, "Companies worked at"], [4834, 4854, "Location"], [4858, 4875, "Years of Experience"], [4889, 4895, "Skills"], [5125, 5131, "Skills"], [5198, 5212, "Skills"], [5440, 5445, "Skills"], [5822, 5825, "Skills"], [6068, 6089, "Skills"], [6123, 6126, "Skills"], [6144, 6173, "Skills"], [6221, 6224, "Skills"], [6277, 6280, "Skills"], [6329, 6345, "Designation"], [6347, 6362, "Companies worked at"], [6366, 6386, "Location"], [6390, 6417, "Years of Experience"], [6431, 6438, "Skills"], [6870, 6873, "Skills"], [6886, 6891, "Skills"], [6956, 6971, "Skills"], [6973, 6991, "Skills"], [6993, 7059, "Skills"], [7108, 7161, "Degree"], [7193, 7231, "College Name"], [7235, 7251, "Location"], [7261, 7599, "Skills"]]}
{"id": 105, "text": "Madas Peddaiah\nAnantapur, Andhra Pradesh - Email me on Indeed: indeed.com/r/Madas-\nPeddaiah/557069069de72b14\n\n• Having 3 moths of experience in Manual Testing.\n• Previously worked with Infosys Limited, Mysore as a Software Test Engineer.\n• Having good experience in Executed Test Cases as per Client Requirements.\n• Having good experience in identifying the test scenarios and designing the test cases.\n• Worked on IE, Firefox and Chrome Driver using Selenium.\n• Good Knowledge in Core Java, SQL.\n• Experience in designing, preparing and executing test cases for client server and web based\napplications STLC Concepts.\n➢ Web Based Application Testing\n• Experience in understanding business requirements, preparing and execution of test cases for\nSystem Customizations/Enhancements and Initiatives.\n• Quick learner with the ability to grasp new technologies.\n• Excellent team player having ability to finish the tight deadlines and work under pressure.\n• Good exposure on Manual Testing & Bug Life Cycle.\n\nWORK EXPERIENCE\n\nInfosys Limited -  Mysore, Karnataka -\n\nSeptember 2014 to December 2014\n\nEducational Technologies:\n\nSoftware Test Engineer\n\nInfosys Limited -\n\nSeptember 2014 to December 2014\n\n-September 2014 to December 2014.\nProject: 1\nClient: Loan Account\nRole: Software Test Engineer\nTeam Size: 4\nTechnologies: J2EE, Oracle 10g, Windows 7\nUsed Tools: Selenium, Core Java, HPQC and Eclipse.\nDuration: September 2014 to December 2014\n\nProject Description:\nIn this project we maintain all details about account transaction details, customer loan account\ndetails, calculate monthly EMI's and there activity like - Account login details, Account transaction,\nLoan account details etc.\nResponsibilities:\n• Participated in identifying the test scenarios and designing the test cases.\n• Prepared and Executed Test Cases as per Client Requirements.\n\nhttps://www.indeed.com/r/Madas-Peddaiah/557069069de72b14?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Madas-Peddaiah/557069069de72b14?isid=rex-download&ikw=download-top&co=IN\n\n\n• Performed Manual Testing on some modules.\n• Feasibility analysis of Manual Test Cases.\n• Performed Functional, Compatibility testing on different browsers like Firefox & Chrome.\n• Defect tracking & Bug Reporting.\nProject: 2\nClient: Hospital Management\nRole: Software Test Engineer\nTeam Size: 4\n\nEDUCATION\n\nB-Tech\n\nKuppam Engineering College -  Kuppam, Andhra Pradesh\n\n2014\n\nEducation, A.P\n\nVani Jr college\n\nOctober 1977 to 2010\n\nEducation, A.P\n\nPadmavani High School\n\n2008\n\nADDITIONAL INFORMATION\n\nTechnical Skills:\nTools: Manual Testing, Selenium (Selenium IDE, Selenium Web Driver), Eclipse IDE.\nLanguages: C, Core Java\nDatabase: SQL\nOperating Systems: Windows XP, 7, 8\nManagement Tool: HP Quality Center\nDefect Tracking Tool: JIRA\n\nProjects Summary:\n\nTechnologies: J2EE, Oracle 10g, Windows 7\nUsed Tools: Selenium, Core Java, HPQC and Eclipse.\nDuration: September 2014 to December 2014.\nProject Description:\nIn this project, we maintain all details about Hospital details like- Hospital address, Consultant\nDoctor, Doctor Details, Permanent Doctor, Medicine, Lab Test, In Patient, Out Patient etc.\nResponsibilities:\n• Prepared and Executed Test Cases as per Client Requirements.\n• Participated in identifying the test scenarios and designing the test cases.\n• Performed Manual Testing on some modules.\n\n\n\n• Feasibility analysis of Manual Test Cases.\n• Performed Functional, Compatibility testing on different browsers like Firefox & Chrome.\n• Defect tracking & Bug Reporting.", "meta": {}, "annotation_approver": "admin", "labels": [[2566, 2783, "Skills"], [2418, 2422, "Graduation Year"], [2364, 2390, "College Name"], [2356, 2362, "Degree"], [2308, 2331, "Designation"], [1270, 1293, "Designation"], [1146, 1161, "Companies worked at"], [1122, 1145, "Designation"], [1022, 1037, "Companies worked at"], [185, 200, "Companies worked at"], [63, 109, "Email Address"], [0, 14, "Name"], [15, 40, "Location"], [119, 140, "Years of Experience"], [202, 208, "Location"], [214, 236, "Designation"], [266, 285, "Skills"], [415, 459, "Skills"], [481, 490, "Skills"], [492, 495, "Skills"], [1041, 1058, "Location"], [1062, 1093, "Years of Experience"], [1163, 1196, "Years of Experience"], [1199, 1230, "Years of Experience"], [1409, 1440, "Years of Experience"], [1360, 1368, "Skills"], [1370, 1379, "Skills"], [1381, 1385, "Skills"], [1390, 1397, "Skills"], [2394, 2416, "Location"], [2858, 2866, "Skills"], [2868, 2877, "Skills"], [2879, 2883, "Skills"], [2888, 2895, "Skills"], [2907, 2938, "Years of Experience"]]}
{"id": 106, "text": "Madhuri Sripathi\nBanglore, Karnataka, Karnataka - Email me on Indeed: indeed.com/r/Madhuri-\nSripathi/04a52a262175111c\n\nAround 4 years of IT experience in analysis, testing and scripting on L2/L3 layer protocols and\ndesiging testcases and automating the same in TCL/TK and Python.\n\n• Expertise in Networking Protocols L2, L3 protocols, Manual, Performance, Platform, Regression\nand Automation Testing.\n• Experience in python scripting and PYATS framework.\n• Coordinating with onsite/offsite teams in resolving the defects found in Testing and working on\nqueries raised by customers..\n• Reviewing the automated scripts.\n• Exposure to Networking Protocols such as DHCP, OSPF, RIP, VLAN, STP/RSTP, LACP, TCP/IP,\nIPv4, Ipv6, Ethernet.\n• Automation in Python.\n• Excellent ability to plan, organize and prioritize my work to meet on time the deadlines of my\nclients and keep customer's satisfaction at the highest level possible.\n• Proven ability in quick understanding and learning of new technologies and their application\nin business solutions\n• Good debugging and problem solving skills with excellent understanding of system\ndevelopment methodologies, techniques and tools.\n• Highly motivated team member with strong communication, analytical and organizational\nskills.\n• Strong communication, interpersonal and analytical skills with proficiency at grasping new\nconcepts quickly and utilizing the same in a productive manner.\n• Willingness and ability to quickly adapt to new environment.\n• Good positive attitude and ability to learn new things independently.\n\n• Worked as Senior project engineer in Wipro Technologies, from Jan2014 to till date.\n\nLanguages: C\nNetwork Analysis Tools: QDDTS, GNS3, IXIA, SPIRENT, PAGENT\nRouting protocols VLAN, ETHECHANNELSTP, RSTP, RIP, EIGRP, OSPF, BGP, MPLS, L2VPN, L3VPN,\nIPSEC and MULTICAST.\nScripting Language Perl, Tcl/TK, Python\nTraffic Generators IXIA, PAGENT, SPIRENT\nManagement Protocols Telnet, SNMP\n\nWilling to relocate to: UAE - Dubai - abu-dabhi\n\nWORK EXPERIENCE\n\nSenior project engineer\n\nCisco -  Bengaluru, Karnataka -\n\nMarch 2014 to Present\n\nhttps://www.indeed.com/r/Madhuri-Sripathi/04a52a262175111c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Madhuri-Sripathi/04a52a262175111c?isid=rex-download&ikw=download-top&co=IN\n\n\nCisco 7600 is a router which supports both layer2 and layer3 protocols. It mainly deploys protocols\nlike MPLS and having specific modules to support the protocols IPSEC. Worked as system testing,\nperformance testing, stress testing and regression testing for all the IOS release on all layer 2\nand layer 3 protocols.\n\nWipro Technologies Limited (Bangalore, Karnataka)\nSenior project engineer (March 2014 till date)\n\nSenior software engineer\n\nWipro -\n\nFebruary 2014 to Present\n\nResponsibilities:\n• Responsible for regression and Manual testing of CISCO IOS -7600\n• Test case execution, test case results tracking, debugging, logging defects in CDETS,\nreproductions and fix verification.\n• Configuration and Testing on Routing protocols OSPF, BGP, OSPF, MPLS, L3VPN, L2VPN, IPSEC,\nQOS, SNMP and MULTICAST features on Cisco 7600 Routers.\n• Filed critical bugs of high severity, through root cause analysis and effective testing methods\nBug verification, Bug tracking, and documentation and review bug fixes.\n• Engaged in regression testing, filing bugs against Cisco IOS images to improve the quality of\nthe images and send weekly test reports.\n• Mentoring of new joiners in the team and conducting technical training sessions.\n• Responsibility for the 7600 platform customer queries (AT&T, Bharati, Vodafone, German IT,\netc )\n• Involved in Sev1, Sev2 and sev3 cases and MW related to L2/L3 Features.\n• Create a Local Repro of the issue which was raised by the customer.\n• Analyzed the customer issues and will provide the solutions to the customers\n• Worked with Developer to verify the DDTs fix for the customer Found Defects\n• System Testing on every New IOS build for the L2/L3 protocols.\n• Configuration and Testing on routing protocols\n• Working on Functionality, Scalability and Performance testing\n• Preparing of Test beds and topologies using Line cards - SIP200, SIP400, SIP600, ES+, ES20, GIG\nand TenGig Lancards, pagent, IXIA Traffic generators etc. to create customer setup in local Labs\n• Knowledge on TCL scripting and automated customer found issues into regression testing and\nalso able to troubleshoot the script issues.\n\nEDUCATION\n\nMaster degree in computer science in computer science\n\nPES college\n\nS.S.C in computer science\n\nRajah college\n\n\n\nSKILLS\n\nLINUX (4 years), UNIX (4 years), ospf (4 years), bgp (4 years), mpls (4 years), ipsec (4 years),\nmulticast (4 years), l2vpn (4 years), l3vpn (4 years), tcl (4 years), python (2 years)\n\nADDITIONAL INFORMATION\n\nOperating Systems: LINUX, UNIX\nOther protocols ARP, RARP, ICMP,ospf,bgp,mpls,l2vpn,l3vpn\nAutomaton tools: tcl,python", "meta": {}, "annotation_approver": "admin", "labels": [[4563, 4747, "Skills"], [4538, 4552, "College Name"], [4511, 4536, "Degree"], [4498, 4509, "College Name"], [4443, 4496, "Degree"], [2737, 2742, "Companies worked at"], [2711, 2736, "Designation"], [2663, 2686, "Designation"], [2037, 2042, "Companies worked at"], [2012, 2035, "Designation"], [1573, 1596, "Designation"], [62, 118, "Email Address"], [0, 16, "Name"], [17, 47, "Location"], [126, 133, "Years of Experience"], [261, 267, "Skills"], [272, 278, "Skills"], [296, 333, "Skills"], [335, 399, "Skills"], [438, 453, "Skills"], [417, 433, "Skills"], [732, 752, "Skills"], [943, 995, "Skills"], [1047, 1056, "Skills"], [1061, 1083, "Skills"], [1174, 1190, "Skills"], [1208, 1266, "Skills"], [1270, 1327, "Skills"], [1443, 1486, "Skills"], [1495, 1512, "Skills"], [1600, 1618, "Companies worked at"], [1620, 1645, "Years of Experience"], [1659, 1660, "Skills"], [1685, 1719, "Skills"], [1849, 1869, "Skills"], [1889, 1910, "Skills"], [1932, 1944, "Skills"], [1970, 1993, "Location"], [2046, 2066, "Location"], [2070, 2091, "Years of Experience"], [2613, 2639, "Companies worked at"], [2641, 2661, "Location"], [2688, 2708, "Years of Experience"], [2746, 2770, "Years of Experience"], [4791, 4796, "Skills"], [4798, 4802, "Skills"], [4878, 4881, "Skills"], [4882, 4888, "Skills"], [4819, 4860, "Skills"]]}
{"id": 107, "text": "Mahesh Vijay\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Mahesh-Vijay/a2584aabc9572c30\n\nOver 6.5 years of functional enriched experience in ERP in the Procurement to Pay domain. Was\nassociated with Oracle India Pvt Ltd, Bangalore as Team lead - Supplier Data Management in\ntheir Global Financial Information Centre (Global Shared Service Center) for Oracle's Business\nfrom Sep 2007- Feb 2014.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nTeam lead - supplier data management\n\nOracle India -  Bangalore, Karnataka -\n\nMarch 2014 to December 2016\n\nManaging Partner of family business of Tours & Travels\n\nTeam Lead\n\nOracle India Pvt Ltd -\n\nOctober 2013 to February 2014\n\nSupplier Data Management\n\nLead Analyst -SME -Supplier Data Management\n\nOracle India Pvt Ltd -\n\nSeptember 2012 to October 2013\n\nSenior Analyst -Supplier Data Management\n\nOracle India Pvt Ltd -  Bengaluru, Karnataka -\n\nJanuary 2010 to September 2012\n\nAcademia\n• Bachelors in Commerce (B.Com) from Vivekananda Degree College, Bangalore\nUniversity(2007)\n• Pre University from Vivekananda PU College, Bangalore(2004)\n• Passed 10th STD from Angels High School, Bangalore(2002)\nFunctional & Technical Expertise\nFunctional\n- Ensuring data quality in Purchasing Supplier management (PSM) registry and Trading\nCommunity Architecture of the Oracle e-business suite bundle.\n\nhttps://www.indeed.com/r/Mahesh-Vijay/a2584aabc9572c30?isid=rex-download&ikw=download-top&co=IN\n\n\n- Managing all projects and ensuring the completion of the same within timeframe. Projects\nlike - Oracle Fusion Supplier Self Service, Supplier cleanup, migration of merger and acquisition\nsuppliers, UAT\n- Managing activities like synchronizing creation and updates of supplier records.\n- Oracle Fusion - Related to Procurement modules -Fusion Supplier Portal\n- Sound knowledge in the Oracle Financial applications domain that includes various cycles like\nthe Expense Reporting, Accounts Payables, Accounts Receivables, and Tactical Purchasing.\n- R12 User Acceptance Testing, writing test cases and author test reports which analyze the\nreported defects.\nTechnical\n- Oracle Applications Releases: 12, 11.5, Oracle Applications Modules Purchasing, iProcurement\n- Business Intelligence Reporting Tools: Oracle Discoverer & Accounting Software Tally 7.2\nProjects & Accomplishments\nOracle Master Data Management- Legacy Data Cleanup Project\nRole:\n• Global Country wise clean up initiative focusing on achieving a clean and accurate database\n• Supplier Information retrieval based on information in Purchase orders\nOracle Fusion UAT- Supplier Self Service\nRole:\n• Internal UAT-Part of upgrade team, testing all functionality and interfaces.\n• Monitoring the new add on features in Fusion and old features assigned\n\n11i to R12 Migration- Manual UAT\nRole:\n• Testing for Supplier creations, Merges, Tax set ups, Withholding and TDS code, Bank details etc\n• Monitoring the new add on features in R12 and old features assigned\n\nOracle Supplier Life Cycle Management (SLM) or Supplier Hub Project\nRole:\n• Internal UAT- testing all functionality and interfaces for creating a 360 degree view for each\nand every supplier.\n• Responsible for setting up suppliers' online, assisting requesters and suppliers to register a\nsupplier and iSupplier access. Testing fast and flexible supplier searches that can be made into\ntemplates resulting in quick report generation Create and test blended supplier records from\nmultiple sources\n\nSKILLS\n\nBCP (6 years), Data Governance (6 years), Data Management (6 years), Oracle (6 years),\nReporting Tools (6 years)\n\nADDITIONAL INFORMATION\n\nKey Skills\n• Process Management & Improvement\n• Operations & Team Management\n• Data Governance & Automation\n• Oracle E- Business Systems experience in Supplier Data/Vendor Data management\n• BCP Policies & Procedures\n\n\n\n• Desk Manuals/Business Process & Navigation Documentation\n• Business Ethics\n• Professional Communication\n• Reporting Tools & Microsoft Office Applications", "meta": {}, "annotation_approver": "admin", "labels": [[1089, 1093, "Graduation Year"], [1055, 1077, "College Name"], [1027, 1031, "Graduation Year"], [978, 1026, "College Name"], [943, 973, "Degree"], [810, 850, "Designation"], [709, 753, "Designation"], [492, 498, "Companies worked at"], [105, 114, "Years of Experience"], [56, 98, "Email Address"], [0, 12, "Name"], [13, 33, "Location"], [385, 403, "Years of Experience"], [210, 230, "Companies worked at"], [232, 241, "Location"], [245, 281, "Designation"], [454, 490, "Designation"], [499, 528, "Location"], [532, 559, "Years of Experience"], [617, 626, "Designation"], [628, 648, "Companies worked at"], [652, 681, "Years of Experience"], [754, 774, "Companies worked at"], [778, 808, "Years of Experience"], [852, 872, "Companies worked at"], [876, 896, "Location"], [900, 930, "Years of Experience"], [1079, 1088, "Location"], [3466, 3978, "Skills"]]}
{"id": 108, "text": "Manisha Bharti\nSoftware Automation Engineer\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Manisha-Bharti/3573e36088ddc073\n\n• 3.5 years of professional IT experience in Banking and Finance domain and currently working\nas Software\nAutomation Engineer in Infosys Limited, Pune.\n• Have experience in accounts and customers domain in banking.\n• Woking on SOA technology.\n• Hands on experience of 2+ years in Oracle 11g\n• 2.9 years of professional experience in Middleware Testing and\nFunctional Testing\n• 4 months of experience with UiPath.\n• Experience on GUI and API testing on HP UFT\n• Working on agile methodology where involved as a senior tester.\n• Involved in various STLC stages including Test Planning, Test analysis, Test Execution, Defect\nmanagement and\nTest reporting.\n• Possess sound knowledge of SQL, STLC, Testing Procedures, HP ALM, HP UFT, HP SV, SOAP\nUI, JIRA, JENKINS, CICD, UiPath.\n• Involved in various client presentation.\n\nTraining & Achievement\n\nTitle: Infosys E&R Training\nDescription: Has undergone E&R training in Infosys Limited (Mysore) in Microsoft. Net Stream.\nThere I had been explored SQL,\nRDBMS, OOPS, Mainframes, Software Testing and Software Engineering. Has been trained in\nAutomation Testing\nTools used- Eclipse, UFT, RPT, SQL Server Studio\nReceived two times FS INSTA award from Infosys for excellence in work in automation and team\nsupport Got Appreciation from Project Manager for root cause analysis of defects\nGot Client Appreciations for successful execution in releases. ( Almost 240 service operations go\nlive in a year.)\n\nWilling to relocate to: Pune, Maharashtra\n\nWORK EXPERIENCE\n\nNOT WORKING\n\nSoftware Automation Engineer\n\nInfosys Limited -\n\nAugust 2014 to July 2017\n\n->Worked as an software automation tester more than 3 years.\n->Working experience in Agile methodology.\n\nhttps://www.indeed.com/r/Manisha-Bharti/3573e36088ddc073?isid=rex-download&ikw=download-top&co=IN\n\n\n->Robotic Process automation certified.(UiPath)\n->Involved in CICD implementation in projects.\n->Having strong knowledge about HP UFT/QTP,HP ALM/QC,JIRA.JENKINS,SQL.\n\nSystem Engineer Trainee\n\nInfosys Limited -\n\nFebruary 2014 to July 2014\n\nCORE COMPETENCIES\nTechnology:\nService Oriented Architecture\n(SOA) Languages: •\n• SQL (Oracle DB) •\n• VB Scripting •\n.NET •\nSTLC:\n• Test Planning •\n• Requirement Analysis •\n• Test Scenario •\n• Test Case Preparation •\n• Test Case Execution •\n• Defect Logging •\nTesting:\n• Functional Testing •\n• Middleware Testing •\nRegression Testing •\nGUI testing & API testing•\nLanguages VB Scripting, JAVA\n\nWeb Technologies ASP.Net, XML, HTML\nDatabases SQL Server 2008/2005, ORACLE 11g\n\nDatabase Connectivity ODBC\nDistributed Computing Web Services, API, Windows Services\nModelling Tools Microsoft Vision\n\nEDUCATION\n\nB.Tech in CSE\n\nMeghnad saha institute of technology\n\n2013\n\nSKILLS\n\nUft/qtp,alm/qc,jira,jenkins,automation testing,cicd,service vitualization,uipath\n\n\n\nADDITIONAL INFORMATION\n\nOperating Systems Windows 10 / 8 / 7 / Vista / XP\n\nDomains Banking and Finance\n\nFrameworks Data driven framework, Keyword driven framework\n\nTools HP-UFT, HP-SV, HP-ALM/QC, SOAP UI, JENKINS, UiPath\n\nMethodologies STLC, Agile and waterfall.\n\nProject Management Tools JIRA\n\nTECHNICAL SKILLS\n\n• 2.5+years of professional experience in SQL.\n• Has hands on experience on Oracle DB (Oracle 11g)\n• Has extensive knowledge of Testing Procedures and various\nphases of Testing Has 2+ years of experience on QC/ALM\n• Has 2+ years working experience in API & GUI testing\nusing HP UFT. Has 4 months of experience with uiPath\n• Has 2+ years working experience in SOAP UI.\n• Has 1.5+ year working experience on service virtualization using HP SV tool.\n• Has 6 months working experience in JIRA during work under agile methodology.\n• Has undergone Infosys Training in .Net Testing.\n• Has knowledge about CICD (Continuous integration and continuous delivery)\n\nPROJECT UNDERTAKEN\n\nDI-Middleware testing (August 14 -\nJuly 17) Domain- Accounts and customer.\nClient- ABN AMRO Bank (Netherland's bank)\nProject Name- ESB (Enterprise service bus)\n• Tools- ALM, SQL Developer, HP UFT, HP SV, SOAP UI, JENKINS, JIRA.\nIn this project, we were validating end to end communication of consumer & provider via ESB.\nWhat consumer actually\nsent to the Provider and how provider responds to the consumer. Testing included System\nIntegration Testing,\nRegression Testing, GUI Testing and Reports.\n\nResponsibilities-\n\nAutomation work\n• Preparing automation scripts using HP UFT tool where focus on Middleware logging as per the\nESB behavior.\n• Integrating all automation scripts with the ALM so that on one click we are able to execute test\ncases and collecting all\ntest results and logged defects in ALM without any manual efforts.\nManual Work -\n\n\n\n• Requirement analysis and Test Planning.\n• Test Scenarios preparation for various functionalities as per the Requirement.\n• Test Cases Creation and their execution for various functionalities of ESB and different provider\nservices\n• Prioritization of test cases as per the business requirement\n• Test Data Preparation as per the requirement using HP ALM.\n• Defect logging in case of any unusual behavior of the solution.\n• Preparing Weekly Progress Reports.\n• Leading the defect call\n• Virtualizing services using HP SV tool and deploy on central server so that in downtime testing\nshould not be impacted.\nCICD-JENKINS:\n• Involve in Continuous integration and continuous deployment strategy, with the help of JENKINS\n& UFT (automation scri\npt integrated with HP ALM) successfully implemented for currently working project.\n\nTDM implantation in CICD pipeline.\n\nPresentation - Direct communication to clients:\n• Present my team and our work to the client directly. (including CICD and TDM job\nimplementation in the same)\n\nuiPath exposure within same project:\n• Convert existing/new projects which are using UFT for automation into uiPath based\nautomation.\n• Do the feasibility analysis for the conversion and come up with a plan to convert maximum\nartifacts with minimum\nefforts\n• Setup basic skeleton for the new project", "meta": {}, "annotation_approver": "admin", "labels": [[2819, 2823, "Graduation Year"], [2781, 2818, "College Name"], [2766, 2779, "Degree"], [1645, 1674, "Designation"], [133, 142, "Years of Experience"], [85, 130, "Email Address"], [15, 44, "Designation"], [0, 14, "Name"], [45, 62, "Location"], [228, 256, "Designation"], [260, 275, "Companies worked at"], [277, 281, "Location"], [358, 372, "Skills"], [411, 421, "Skills"], [464, 482, "Skills"], [487, 505, "Skills"], [536, 542, "Skills"], [560, 589, "Skills"], [678, 689, "Skills"], [813, 816, "Skills"], [818, 822, "Skills"], [825, 903, "Skills"], [1028, 1040, "Degree"], [1044, 1068, "College Name"], [1072, 1081, "Skills"], [1121, 1192, "Skills"], [1245, 1252, "Skills"], [1254, 1257, "Skills"], [1259, 1262, "Skills"], [1264, 1267, "Skills"], [1268, 1281, "Skills"], [1596, 1613, "Location"], [1675, 1690, "Companies worked at"], [1694, 1718, "Years of Experience"], [1735, 1761, "Designation"], [1772, 1779, "Years of Experience"], [1965, 1971, "Skills"], [1987, 1991, "Skills"], [2052, 2090, "Skills"], [2092, 2115, "Designation"], [2117, 2132, "Companies worked at"], [2136, 2162, "Years of Experience"], [2245, 2248, "Skills"], [2265, 2277, "Skills"], [2280, 2284, "Skills"], [2287, 2291, "Skills"], [2295, 2308, "Skills"], [2313, 2333, "Skills"], [2338, 2351, "Skills"], [2356, 2377, "Skills"], [2382, 2401, "Skills"], [2406, 2420, "Skills"], [2423, 2430, "Skills"], [2434, 2452, "Skills"], [2457, 2475, "Skills"], [2478, 2496, "Skills"], [2499, 2524, "Skills"], [2536, 2548, "Skills"], [2550, 2554, "Skills"], [2556, 2572, "Skills"], [2573, 2580, "Skills"], [2582, 2585, "Skills"], [2587, 2591, "Skills"], [2592, 2601, "Skills"], [2602, 2622, "Skills"], [2624, 2644, "Skills"], [2658, 2662, "Skills"], [2663, 2697, "Skills"], [2699, 2702, "Skills"], [2704, 2753, "Skills"], [2959, 2990, "Skills"], [3087, 3178, "Skills"], [3206, 3210, "Skills"], [3272, 3275, "Skills"], [3306, 3328, "Skills"], [3437, 3443, "Skills"], [3481, 3498, "Skills"], [3505, 3511, "Skills"], [3545, 3551, "Skills"], [3589, 3596, "Skills"], [3665, 3675, "Skills"], [3714, 3718, "Skills"], [3792, 3804, "Skills"], [3828, 3832, "Skills"], [4072, 4129, "Skills"], [4328, 4399, "Skills"], [4474, 4485, "Skills"], [4591, 4594, "Skills"], [4797, 4811, "Skills"], [4878, 4897, "Skills"], [4949, 4952, "Skills"], [5101, 5107, "Skills"], [5268, 5278, "Skills"], [5360, 5372, "Skills"], [5463, 5476, "Skills"], [5598, 5602, "Skills"], [5728, 5732, "Skills"], [5737, 5740, "Skills"], [5774, 5780, "Skills"], [5859, 5862, "Skills"]]}
{"id": 109, "text": "Manjari Singh\nSenior Software Analyst - Accenture\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Manjari-Singh/fd072d33991401f0\n\n• Test Lead with more than 6.5 years of professional experience in TELECOM domain specifically\nin OSS applications including Order\nmanagement, Inventory Management, Service provisioning, Service Activation and Service\nAssurance\n• An accomplished QA Lead with extensive experience in Functional Testing, Regression Testing,\nUnit Testing, Integration Testing, Test strategy\ndefinition, Test Plan, Test Estimation, Test Cases/Scenarios, Test Procedures, Results and\ndocumentation\n• Proficient in Project management, requirements definition, software testing, SDLC, STLC, Agile,\nV-Model and Waterfall test methodologies\n• Worked as primary liaison with Business, QA, Development teams and Vendors for major, minor\nand emergency releases\n• Proficient in testing Web Based applications, Web Services, SOAP UI, High Speed Broadband\nand IPTV Testing (Modem, STBs, DSLAMs)\n• Expertise in handling and coordinating defect triage meetings, project meeting with various\nstakeholders in onshore-offshore model\n• A certified business analyst from BCS, The Chartered Institute for IT with proficiency in\nrequirement management and project definition\n\nWORK EXPERIENCE\n\nSenior Software Analyst\n\nAccenture -  Bengaluru, Karnataka -\n\nDecember 2017 to Present\n\nClient Leading telecommunication and digital entertainment service provider in US operating in\nfour segments: Business solutions,\nEntertainment Group, Consumer Mobility and International.\n• As an Agile Test Lead responsible for analyzing, estimating and sizing the user stories which\nwill help product owners to prioritize the story cards\n• Participating in project planning and scrum meetings to understand business and technical\nrequirements\nRoles & • Responsible for testing and validation of user stories, developing testing plans, and\nestimating project resources\nResponsibilities\n• Facilitate the resolution of testing roadblocks, ensure execution of QA deliverables and guiding\nteam members on agile\nstandards and best practices\n• Responsible for conducting smoke, functional, regression testing as well as ad-hoc testing of\nclient's B2B web application\n• Creating project specific documents including system understanding documents and\nrequirement traceability metrics\n\nhttps://www.indeed.com/r/Manjari-Singh/fd072d33991401f0?isid=rex-download&ikw=download-top&co=IN\n\n\nProject 1: ASPEN - Unified Communication\n• Role: Senior QA Lead\n• Key Responsibilities:\nProject\no Extensively collaborated with external Vendor to understand the new B2B Portal and\ndocumented the same\no Mentored/Trained new team members with Unified Communication concepts and functionality\nof the portal\no Responsible for leading a team to deliver test results for unified communication platform\no Lead Iteration Retrospective meetings to define problems, prioritize actions items and decide\nnext steps\n\nSenior Software Analyst\n\nAccenture -  Bengaluru, Karnataka -\n\nJune 2011 to November 2017\n\nOne of the leading telecommunication services and products provider in Canada providing wide\nrange of products and services\nClient\nincluding mobile telephony, internet access, voice entertainment, video and satellite television\n• As a Test Lead responsible for testing the End-to-End flow of Business Requirement\n• Develop test suite, test approach, test plan and high level scenarios for the project\n• To provide Project estimates, resource commitment for functional testing\n• Working with the Project Leads to establish time tables and agree on a QA plan for the\nimplementation\nRoles & • Lead defect triage calls, business reviews and training session to help subordinates\nproduce quality deliverables\nResponsibilities • Mentoring a testing team of 14 personnel through all phases of application\ndevelopment, ensuring that systems, products, and services meet or exceed organization/\nindustry quality standards and end user requirements\n• Sending daily status reports to the stakeholders regarding test progress and defect updates\n• Participating in risk and mitigation planning discussing the project planning and related risks\n• Assisting/leading GO-LIVE activities for smooth code delivery in short duration\nProject 1: Service Assurance Enablement\n• Role: QA Lead\n• Key Responsibilities:\no Worked as a QA prime on multi-million CAD$ project for assuring service quality and enabling\nglitch free service delivery\no Lead the QA team of 6 members both at onshore and offshore at different time periods\no As a Subject Matter expert, managed and delivered complex end to end data setup for AT,\nPT, UAT testing\n\nProject 2: HDM Upgrade and firmware testing of Customer Premises Equipment (CPE)\n• Role: QA Lead\n• Key Responsibilities:\no Owned and managed the only live lab environment as Onshore lead\n\n\n\no Performed network testing in established lab on DSLAMs, modems, STBs with various firmware\nversions\no Extensively collaborated with the external vendors and provided support to offshore team in\ntesting phase\nProjects\nProject 3: TV3- THOR Program\n• Role: QA Lead\n• Key Responsibilities:\no Worked as an Onshore QA lead for multiple agile releases during the launch of new next gen\nIPTV app\no Collaborated closely with project team to resolve the outstanding issues within strict timelines\no Performed Web based, Android & IOS testing for the new TV content app while collaborating\nwith the vendor team\n\nProject 4: Multiple HSIA projects like HSIA 250/250, Optik 150, Optik 50\n• Role: QA Lead\n• Key Responsibilities:\no Worked on requirements analysis, estimation, test planning, test approach, and defect\nmanagement for the release\no Collaborated with developers and BSAs to daily triage the defects logged during the testing\no Performed database testing for one of the activation tool and automated the process using\nWorksoft Certify\n\nQA\n\nAccenture -\n\nAugust 2015 to October 2016\n\nLead, worked on quality assurance and service enablement for multiple newly launched digital\napplications in live environment.\n\nAccenture -  Vancouver, BC -\n\n2015 to 2016\n\nEDUCATION\n\nB.TECH. in Information Technology\n\nAmity University -  Lucknow, Uttar Pradesh\n\n2011\n\nClass XII\n\nCanossa Convent Girls Inter College\n\n2007\n\nClass X\n\n\n\nCanossa Convent School\n\n2005\n\nSKILLS\n\nQA (7 years), TESTING (6 years), ESTIMATION (6 years), AMDOCS (Less than 1 year), BILLING\n(Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS AND COMPETENCIES\nDomain and Functional Expertise Tools and Languages:\n• Telecommunications and IPTV Systems • NetCracker, NetProvision, Insight (IISY), HDM (Nokia),\nTrouble Ticketing\n• Test/QA Lead • Click Schedule, FieldLink, AMDOCS-Ordering, Billing - Product Catalog\n• Project Management • IBM - TOCP, Mediaroom, MediaFirst TV3 platform (Ericsson)\n• Software Testing and Defect Management • HP QC 10, JIRA, TDP, Accenture Test Estimation Tool\n• Lead to Order and Order to Cash Management • Caliber RM (Requirement Gathering),\nConfluence, Clear Quest\n• Service Provisioning and Activation • Worksoft Certify, QTP, Java, Selenium, SQL, HTML, XML\n• Business Analysis (Requirement Management) • Splunk, WinSCP, SOAP UI, Kibana, Wireshark\n• Automation • Proficiency in MS Office (Word, Excel, PowerPoint and Visio)", "meta": {}, "annotation_approver": "admin", "labels": [[6305, 7258, "Skills"], [6250, 6254, "Graduation Year"], [6213, 6249, "College Name"], [6196, 6200, "Graduation Year"], [6152, 6169, "College Name"], [6117, 6151, "Degree"], [6062, 6071, "Companies worked at"], [5892, 5901, "Companies worked at"], [2987, 2996, "Companies worked at"], [2962, 2985, "Designation"], [1317, 1326, "Companies worked at"], [1292, 1315, "Designation"], [94, 138, "Email Address"], [40, 49, "Companies worked at"], [14, 37, "Designation"], [0, 13, "Name"], [51, 71, "Location"], [166, 175, "Years of Experience"], [141, 150, "Designation"], [385, 392, "Designation"], [422, 440, "Skills"], [442, 460, "Skills"], [462, 474, "Skills"], [476, 495, "Skills"], [497, 521, "Skills"], [523, 532, "Skills"], [534, 549, "Skills"], [551, 571, "Skills"], [677, 693, "Skills"], [695, 699, "Skills"], [701, 705, "Skills"], [707, 712, "Skills"], [714, 721, "Skills"], [726, 735, "Skills"], [888, 980, "Skills"], [1330, 1350, "Location"], [1354, 1378, "Years of Experience"], [1576, 1591, "Designation"], [2506, 2520, "Designation"], [3000, 3020, "Location"], [3024, 3050, "Years of Experience"], [4313, 4320, "Designation"], [4752, 4759, "Designation"], [5109, 5116, "Designation"], [5888, 5890, "Designation"], [5905, 5932, "Years of Experience"], [6075, 6088, "Location"], [6092, 6104, "Years of Experience"], [6172, 6194, "Location"]]}
{"id": 110, "text": "Mohamed Ameen\nSystem engineer\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Mohamed-Ameen/\nba052bfa70e4c0b7\n\nI am looking for a job opportunity as a System Engineer that gives me professional growth and\nexcellence and enables me to contribute my efforts in the success of the organization.\n\nWORK EXPERIENCE\n\nIT Operations Analyst\n\nAccenture\n\nI am looking for a job as system engineer that gives me professional growth and excellence and\nenables me to contribute my efforts in the success of the organization.\n\ntechnical support engineer\n\nConvergys for Microsoft -\n\nNovember 2014 to November 2015\n\nCurrently working with Accenture as a Subject Matter Expert for the Remote Technology Support\nteam in IT Operations.\n\nEDUCATION\n\nB.E in Electronics & Communication\n\nVisveswaraiah Technological University -  Bengaluru, Karnataka\n\n2013\n\nElectronics Project\n\nAl-Ameen PU College\n\nRajiv Gandhi Institute of Technology\n\nSKILLS\n\nActive Directory (2 years), Microsoft office, Windows,End user computing (3 years)\n\nCERTIFICATIONS/LICENSES\n\nCCNA\n\nCCNA certified\n\nhttps://www.indeed.com/r/Mohamed-Ameen/ba052bfa70e4c0b7?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Mohamed-Ameen/ba052bfa70e4c0b7?isid=rex-download&ikw=download-top&co=IN\n\n\nADDITIONAL INFORMATION\n\nSkill set:\n\nNetworking:\n\n➢ Knowledge of OSI and TCP/IP architecture.\n➢ Knowledge of Routing Protocols.\n\n➢ Knowledge of Virtual Private Network and IPv6.\n\n➢ Wireless LAN\n\n➢ Installing, configuration & Troubleshooting of Operating System such as Windows client (XP,\nVista, 7, 8x and 10)\n\n➢ Installing, configuration & Troubleshooting Microsoft Office {2013, 2016, 365 pro plus (Outlook\nand Other Office Tools Such as Excel, word, Power Point) }\n\n➢ Configuring and Troubleshooting Microsoft outlook in Blackberry, iPhone & iPad.\n\n➢ Configuring Group Policies in Domain & Work Group Environment.\n\n➢ Create, modify and Manage User Account in AD Server.\n\n➢ Creating Folder groups in File Server and providing Network folder access.\n\n➢ Configuring & Troubleshooting Wireless Network & LAN, WAN Network,\n\n➢ Installing and Configuration of VPN clients.\n\n➢ Workgroup and Domain level security policies.\n\n➢ Migration and Up gradation of Server/Desktops.\n\n➢ Installation and Troubleshooting of Symantec Endpoint Antivirus.\n\n➢ Installation and Troubleshooting of Avecto Defend point client.\n\n➢ Managing & Troubleshooting Printer, Scanner, Fax.\n\n➢ Configuring Managing and Troubleshooting SCCM on End user machines.\n\n➢ Perform 1st Level troubleshooting and/or escalate as appropriate issue to warranty Vendors.\n\n➢ Maintain Inventory of all warranty / AMC assets for the current year.\n\n➢ Maintain an Inventory of parts for emergency repairs.\n\n➢ Coordinate with vendors and with company personnel to facilitate purchases.\n\n\n\n➢ Working on Web Tickets Tools.\n\n➢ Handling Escalation and Severity for Incidents.\n\nOperating System:\n\n➢ Windows 7, Windows 8, Windows 8.1 and Windows 10.\n\nApplication:\n\n➢ MS Office, Service Now, ITSM, LogMeIn Rescue", "meta": {}, "annotation_approver": "admin", "labels": [[931, 1013, "Skills"], [885, 921, "College Name"], [864, 883, "College Name"], [837, 841, "Graduation Year"], [773, 811, "College Name"], [737, 772, "Degree"], [631, 640, "Companies worked at"], [342, 351, "Companies worked at"], [319, 340, "Designation"], [0, 13, "Name"], [14, 29, "Designation"], [31, 51, "Location"], [74, 118, "Email Address"], [160, 175, "Designation"], [379, 394, "Designation"], [521, 547, "Designation"], [549, 572, "Companies worked at"], [576, 606, "Years of Experience"], [646, 667, "Designation"], [815, 835, "Location"], [1294, 1304, "Skills"], [1322, 1349, "Skills"], [1366, 1383, "Skills"], [1401, 1424, "Skills"], [1429, 1433, "Skills"], [1438, 1450, "Skills"], [1526, 1533, "Skills"], [1542, 1544, "Skills"], [1546, 1551, "Skills"], [1553, 1565, "Skills"], [1614, 1656, "Skills"], [1658, 1665, "Skills"], [1697, 1702, "Skills"], [1704, 1708, "Skills"], [1710, 1721, "Skills"], [1760, 1777, "Skills"], [2113, 2124, "Skills"], [2178, 2223, "Skills"], [2129, 2173, "Skills"], [2011, 2076, "Skills"], [2264, 2291, "Skills"], [2332, 2358, "Skills"], [2363, 2412, "Skills"], [2416, 2482, "Skills"], [2896, 2905, "Skills"], [2907, 2916, "Skills"], [2918, 2929, "Skills"], [2934, 2944, "Skills"], [2963, 2972, "Skills"], [2974, 2985, "Skills"], [2987, 2991, "Skills"], [2993, 3007, "Skills"]]}
{"id": 111, "text": "Mohini Gupta\nServer Support Engineer\n\nGurgaon, Haryana - Email me on Indeed: indeed.com/r/Mohini-Gupta/08b5b8e1acd8cf07\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nServer Support Engineer\n\nMicrosoft -\n\nJuly 2015 to November 2017\n\nKey Responsibilities:\n● Worked as Technical Support Engineer for Microsoft Enterprise Platforms Support.\n\n● Worked in U.S., U.K., India & APAC time zones.\n\n● Always available (24X7) for any explanation, support or information required by team, client\nand managers.\n\n● Configuring, deploying and troubleshooting SCCM with remote/local SQL.\n● Perform Software distribution and ensure successful deployment on end assets.\n● Patch management of servers and end user estate along with troubleshooting.\n● Performed on checks through server, to perform server operations, check services, analyzed\nthe logs to check the communication from the new server to the primary server and vice versa,\nalso checked server's communication with its client.\n● Setting up new packages along with new collection.\n● Collection of inventory i.e. hardware inventory and software inventory.\n● Troubleshooting client connectivity and package installation issues by analysis of logs.\n● Working on incidents logged by end users as a daily activity.\n● Fixing operational issues and performing installation or un-installation of applications.\n● Create new groups, add users and grant permissions.\n● Good understanding of SCCM architecture, operations and management.\n● Knowledge of Active Directory and networking required in SCCM environment.\n● Deploying Operating System with SCCM.\n\nServer Support Engg.\n\nConvergys -\n\nJuly 2015 to November 2017\n\nServer Support Engg.\n\nEDUCATION\n\nB.tech\n\nhttps://www.indeed.com/r/Mohini-Gupta/08b5b8e1acd8cf07?isid=rex-download&ikw=download-top&co=IN\n\n\nKIIT college of Engg.\n\nSKILLS\n\nactive directory, iis, sccm, dhcp, sql, wsus, dns\n\nADDITIONAL INFORMATION\n\nComputer Skills\n● MS Office Tools: MS Excel, MS Word, MS Power Point.\n● Hands on experience on all versions of Windows.\n● Sound knowledge of internet and networking.\n● Coding Languages: C, C++, Java.\n\nOther Information\n● Regular Swimmer.\n● Interested in playing Table Tennis, Lawn Tennis.\n● Professional Proficiency: English and Hindi\n\nI hereby declare that all the above particulars are true to the best of my knowledge.\n\nPLACE: Gurgaon (MOHINI GUPTA)", "meta": {}, "annotation_approver": "admin", "labels": [[2326, 2333, "Location"], [1821, 2095, "Skills"], [1790, 1811, "College Name"], [1684, 1690, "Degree"], [194, 203, "Companies worked at"], [169, 192, "Designation"], [77, 119, "Email Address"], [13, 36, "Designation"], [0, 12, "Name"], [38, 54, "Location"], [207, 233, "Years of Experience"], [269, 295, "Designation"], [546, 550, "Skills"], [569, 572, "Skills"], [1424, 1428, "Skills"], [1485, 1501, "Skills"], [1529, 1533, "Skills"], [1581, 1585, "Skills"], [1588, 1607, "Designation"], [1610, 1619, "Companies worked at"], [1623, 1649, "Years of Experience"], [1651, 1670, "Designation"], [2213, 2220, "Skills"], [2225, 2230, "Skills"]]}
{"id": 112, "text": "Navas Koya\nTest Engineer\n\nMangalore, Karnataka - Email me on Indeed: indeed.com/r/Navas-Koya/23c1e4e94779b465\n\nWilling to relocate to: Mangalore, Karnataka - Bangalore, Karnataka - Chennai, Tamil Nadu\n\nWORK EXPERIENCE\n\nSystem Engineer\n\nInfosys -\n\nAugust 2014 to Present\n\n.NET application Maintenance and do the code changes if required\n\nTest Engineer\n\nInfosys -\n\nJune 2015 to February 2016\n\nPrProject 2:\n\nTitle: RBS W&G Proving testing.\nTechnology: Manual testing\nRole: Software Test Engineer\n\nDomain: Banking\nDescription:\n\nWrite test cases & descriptions. Review the entries. Upload and map the documents into\nHP QC. Execute the testing operations in TPROD mainframe. Upload the result in QC along with\nthe proof.\nRoles and Responsibilities:\n•Prepared the Test Scenarios\n\n•Prepared and Executed Test Cases\n•Performed functional, Regression testing, Sanity testing.\n\n•Reviewed the Test Reports and Preparing Test Summary Report.\n•Upload Test cases to the QC.\n•Execute in TPROD Mainframe.\n•Defect Track and Report.\n\nTest Executive\n\nInfosys Limited -\n\nAugust 2014 to May 2015\n\nhttps://www.indeed.com/r/Navas-Koya/23c1e4e94779b465?isid=rex-download&ikw=download-top&co=IN\n\n\nProject 1:\nTitle: CAWP (Compliance Automated Work Paper)\n\nTechnology: Manual testing\nRole: Software Test Executive\nDomain: Banking\nDescription:\nThe Admin can create and maintain annual test plan, and users can only view and add\ndetails. Testers will get Business Requirement which explains the flows and Functional\nrequirements which gives the full detail of the project.\nRoles and Responsibilities:\n\n•Prepared the Test Scenarios\n•Prepared and Executed Test Cases\n•Performed functional, Regression testing, Sanity testing.\n•Reviewed the Test Reports and Preparing Test Summary Report.\n•Defect Track and Report.\n\nEDUCATION\n\nBachelor of Computer Applications\n\nMangalore University, Mangalore\n\nJune 2011 to April 2014\n\nSKILLS\n\nC# (Less than 1 year), .NET, SQL Server, Css, Html5\n\nADDITIONAL INFORMATION\n\nBachelor of computer application: with 74% from Milagres College, Kallianpur under\nMangalore University, Karnataka.\n\nNavas Najeer Koya 2\n\nSKILL SET • ASP.NET, C# • QA tools\n\n• Coding and modularization • Excellent communication skills\n\n• VB, VB.net, ASP • Technical specifications creation\n\n• HTML • System backups\n\n• Sql server 2005, Oracle • System upgrades\n\n• Java/C/C++ • Excellent problem-solving abilities\n\nNavas Najeer Koya 3", "meta": {}, "annotation_approver": "admin", "labels": [[1895, 1947, "Skills"], [1851, 1860, "Location"], [352, 359, "Companies worked at"], [337, 351, "Designation"], [236, 243, "Companies worked at"], [219, 234, "Designation"], [11, 25, "Designation"], [0, 10, "Name"], [26, 46, "Location"], [69, 109, "Email Address"], [135, 155, "Location"], [158, 178, "Location"], [181, 200, "Location"], [247, 269, "Years of Experience"], [271, 275, "Skills"], [363, 389, "Years of Experience"], [470, 492, "Designation"], [611, 616, "Skills"], [652, 657, "Skills"], [690, 692, "Skills"], [796, 806, "Skills"], [818, 864, "Skills"], [971, 986, "Skills"], [1015, 1029, "Designation"], [1031, 1046, "Companies worked at"], [1050, 1073, "Years of Experience"], [1262, 1285, "Designation"], [1586, 1600, "Skills"], [1624, 1634, "Skills"], [1646, 1692, "Skills"], [1794, 1827, "Degree"], [1829, 1849, "College Name"], [1862, 1885, "Graduation Year"], [1972, 2004, "Degree"], [2020, 2036, "College Name"], [2038, 2048, "Location"], [2055, 2075, "College Name"], [2077, 2086, "Location"], [2122, 2133, "Skills"], [2136, 2144, "Skills"], [2147, 2173, "Skills"], [2175, 2206, "Skills"], [2210, 2225, "Skills"], [2228, 2261, "Skills"], [2265, 2269, "Skills"], [2272, 2286, "Skills"], [2290, 2305, "Skills"], [2307, 2313, "Skills"], [2316, 2331, "Skills"], [2335, 2345, "Skills"], [2348, 2383, "Skills"]]}
{"id": 113, "text": "Navjyot Singh Rathore\nUlhasnagar, Maharashtra - Email me on Indeed: indeed.com/r/Navjyot-Singh-Rathore/\nad92079f3f1a4cad\n\nWORK EXPERIENCE\n\nfresher job\n\nAccenture -  Ulhasnagar, Maharashtra\n\nFresher\n\nAny post\n\nEDUCATION\n\nTYBMS in Management Studies\n\nVedanta College of management and information technology -  Mumbai, Maharashtra\n\n2015 to 2018\n\nH.S.C\n\nGuru Nanak English High School and Jr cllg\n\n2013 to 2015\n\nS.S.C\n\nswami Vivekananda school\n\n2013\n\nSKILLS\n\nFresher\n\nADDITIONAL INFORMATION\n\n● Can switch to any environment within a short span\n● Dedication towards Hard work\n● Willingness to learn\n\nSKILLS\n\n● Basic Computers knowledge\n● Good Understanding of Business Ethics, Operational Research.\n● Completed Project Work on working capital with A+ Grade.\n\nhttps://www.indeed.com/r/Navjyot-Singh-Rathore/ad92079f3f1a4cad?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Navjyot-Singh-Rathore/ad92079f3f1a4cad?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": "admin", "labels": [[351, 393, "College Name"], [344, 349, "Degree"], [338, 342, "Graduation Year"], [330, 334, "Graduation Year"], [249, 305, "College Name"], [220, 247, "Degree"], [152, 161, "Companies worked at"], [139, 150, "Designation"], [68, 120, "Email Address"], [0, 21, "Name"], [22, 45, "Location"], [165, 188, "Location"], [309, 328, "Location"], [395, 407, "Graduation Year"], [495, 540, "Skills"], [543, 571, "Skills"], [574, 594, "Skills"], [606, 631, "Skills"], [634, 671, "Skills"], [672, 693, "Skills"]]}
{"id": 114, "text": "Nazish Alam\nConsultant - SAP ABAP\n\nGhaziabad, Uttar Pradesh - Email me on Indeed: indeed.com/r/Nazish-Alam/\nb06dbac9d6236221\n\nWilling to relocate to: Delhi, Delhi - Noida, Uttar Pradesh - Lucknow, Uttar Pradesh\n\nWORK EXPERIENCE\n\nConsultant\n\nSAP ABAP -  Noida, Uttar Pradesh -\n\nNovember 2016 to Present\n\nCredence Systems, Noida\n\nCredence Systems is IT Infrastructure Management Company, offers end-to-end solutions.\nCombining deep domain expertise with new technologies and a cost effective on-site/ offshore\nmodel. Helping companies integrate key business processes, improving their operational\nefficiencies and extracting, better business value from their investment.\n\nPROJECT UNDERTAKEN\nClient ECC Version Role and Responsibilities\nWelspun Group Plate & Coil Mills Division\nSAP ECC 6.0\n\nConsultant\n\nSAP ABAP -\n\nJanuary 2016 to Present\n\nReports:\n• Designed technical program specifications based on business requirements.\n• Generated basic lists and Interactive Reports for information in the MM/SD including Sales,\nBilling, Purchasing, Goods Received, Inspection Plan, and Batch Determination using ABAP\nprograms, Screen, Report Painter and Menu Painters. Used Parameters, Select-options and Match\nCodes to make the reports more friendly and intuitive to the user.\n• Generated different kind of reports like for PR (Purchase Requisition) analysis using ALV, PO\n(Purchase Order) Pricing details, Pending Export Sales order etc.\n• Developed report for the daily production done.\nSAP Scripts:\n• Generated various client specific Layout sets and form letters using SAP Script.\n• Involved in modification of SAP scripts for Purchase orders (MEDRUCK) and indents, Delivery\nnotes (RVDELNOTE), and Invoices (RVINVOICE) according to customer needs.\n• Modified existing layout sets for Purchase Order and GR using SAP Script.\nData Migration:\n\nhttps://www.indeed.com/r/Nazish-Alam/b06dbac9d6236221?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Nazish-Alam/b06dbac9d6236221?isid=rex-download&ikw=download-top&co=IN\n\n\n• Implemented both Call Transaction and Session Method of BDC accordingly, depending upon\nthe size, type, state and created routines for data upload using data extracts for sequential files\non the application server and UPLOAD/WS_UPLOAD for local files on the presentation server.\n• Wrote ABAP programs for extracting data from SAP tables (Vendor master, Purchase Orders,\nInvoices and remittance) to be transferred to vendors using non-SAP systems for reconciliation\nand their local use.\nObject Oriented:\n• Created local and global classes with SE24 and within programs.\n• Used the Standard ALV classes in OOPs ALV reports.\n• Used ABSTRACT classes and Interfaces.\n• Having knowledge and used the different object oriented concepts technically.\n\nSKILLS\n\nSAP (2 years), ABAP (2 years), ADBC (Less than 1 year), C++ (Less than 1 year), DATA\nMODELING (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nOTHER TECHNICAL SKILLS\n• Trained on SAP S4 HANA.\n• Having knowledge of Code Push down, CDS view and it's consumption in ABAP.\n• Data Modeling, creation of different type of views.\n• AMDP.\n• ADBC connectivity.\n• Familiar with SQL, DDL, DML syntaxes.\n• Work on Windows 7, Windows XP, Windows 8, Windows 10 OS, can work on C, C++\nACADEMEIC CREDENTIALS\n2015 Master of Computer Application\nUPTU. India", "meta": {}, "annotation_approver": "admin", "labels": [[3303, 3314, "College Name"], [3272, 3302, "Degree"], [3267, 3271, "Graduation Year"], [2941, 3244, "Skills"], [2780, 2892, "Skills"], [801, 809, "Companies worked at"], [789, 799, "Designation"], [241, 249, "Companies worked at"], [229, 239, "Designation"], [82, 124, "Email Address"], [25, 33, "Companies worked at"], [12, 22, "Designation"], [0, 11, "Name"], [35, 59, "Location"], [150, 162, "Location"], [165, 185, "Location"], [187, 210, "Location"], [253, 273, "Location"], [277, 301, "Years of Experience"], [813, 836, "Years of Experience"], [963, 986, "Skills"], [1479, 1490, "Skills"], [1563, 1573, "Skills"], [1605, 1616, "Skills"], [1638, 1645, "Skills"], [1676, 1685, "Skills"], [1621, 1636, "Skills"], [1660, 1674, "Skills"], [1692, 1700, "Skills"], [1702, 1711, "Skills"], [1806, 1816, "Skills"], [1818, 1832, "Skills"], [2463, 2474, "Skills"], [2515, 2530, "Skills"], [2733, 2757, "Skills"]]}
{"id": 115, "text": "Nidhi Pandit\nTest Engineer - Infosys Limited\n\n- Email me on Indeed: indeed.com/r/Nidhi-Pandit/b4b383dbe14789c5\n\nOverall around 4 years of work experience - Currently working with Infosys Limited designated\nas Test\nEngineer. Experience in Automation and Manual Testing in telecom and banking domain.\n\nWORK EXPERIENCE\n\nTest Engineer\n\nInfosys Limited -\n\nJune 2016 to Present\n\nProject Description:\nCBIL (Core Banking Integration Layer) is a crucial integration layer that is specifically addressing\nthe architectural complexity due to multiple core banking systems and variants at HSBC. It is a\nstandard service\ninterface across different core banking systems to facilitate easy integration with other global\nsystems.\nCBIL is a strategic initiative to standardize all interfaces with core banking without impacting the\nunderlying\ncore banking system.\nRoles & Responsibilities:\n\n• Understanding the functional requirements of the API.\n• Involvement in Test Planning.\n• Verifying the documents provided by the development team.\n• Creating test data request document to test the service on the certified environment.\n• Create and automate Test Cases.\n• Preparing Stub to virtualize the API.\n• Executing test cases in local and certified environments and validating the responses.\n• Participation in Stand up Calls, Scrum Calls, Sprint Planning, Retrospective Meetings.\n• Defect Management in JIRA.\n• Involvement in Automation Scripting.\n• Presenting completed APIs to the client.\n• Experience in working under client environment, multi-vendor environment.\n\nTest Engineer\n\nInfosys Limited -\n\nFebruary 2014 to Present\n\nTest Engineer\n\nInfosys Limited -\n\nhttps://www.indeed.com/r/Nidhi-Pandit/b4b383dbe14789c5?isid=rex-download&ikw=download-top&co=IN\n\n\nJuly 2014 to January 2016\n\nProject Description:\nOrder Management Fulfillment (OMFUL), which belongs to Telecommunication Domain, is a\nunified\nbusiness process management framework that orchestrates, automates and manages the\nservice\nfulfillment process, aligning people, processes and technology. This product caters the end to end\nfunctionality for telecom services in OSS space. Our role as a team was to ensure any Initiate/\nChange\nRequest towards the product is delivered successfully in time with no compromise in quality.\nRoles & Responsibilities:\n\n• Understanding the client requirement\n• Creating SQL scripts and deploying on the local environment (UNIX)\n• Sanity testing on different environments\n• Performing Manual Testing on OMFUL Application\n• Creating and maintaining test cases as per the requirement\n• Run and validate the test cases in the system which is integrated in a real production like\nenvironment\n• Creating manual stubs to complete the process fulfillment flow\n• Defect Management\n• Creating Show And Tell Related documents\n• Participation in support team at the time of Production Deployment\n\nEDUCATION\n\nState Board\n\n2008\n\nEducation\n\nPassing\n\nBachelor in Electronics\n\nCentral India Institute\n\nEngineering\n\nTechnical University\n\nSKILLS\n\nAPI. (1 year), Scripting. (1 year), SOAP (1 year), UI (1 year), XML (3 years)\n\nADDITIONAL INFORMATION\n\nKey Technical Skills\n\n\n\nTechnical Experience: - Automation Testing (REST API, Service Virtualization), Functional Testing,\nRegression Testing\nManual Testing, Scripting (SQL)\nDomain Experience: - Telecom, Banking\nProgramming & Scripting Languages: - HTML, CSS, XML, SQL, JAVA (Basic), JSON\nSDLC Model: -Waterfall, Agile\nTesting Tools: - CA LISA, APM (Amdocs Process Manager), SOAP UI, TOSCA, HP-ALM (QC)\nTest Management Tools: -JIRA, Quality Center\nOther Tools: -SQL Developer, TOAD.\nDatabase: - DB2, SQL", "meta": {}, "annotation_approver": "admin", "labels": [[3005, 3083, "Skills"], [2937, 2961, "College Name"], [2912, 2935, "Degree"], [2885, 2892, "Graduation Year"], [1610, 1623, "Designation"], [1550, 1563, "Designation"], [317, 330, "Designation"], [68, 110, "Email Address"], [29, 45, "Companies worked at"], [13, 26, "Designation"], [0, 12, "Name"], [127, 134, "Years of Experience"], [179, 194, "Companies worked at"], [209, 222, "Designation"], [238, 267, "Skills"], [332, 347, "Companies worked at"], [351, 371, "Years of Experience"], [947, 960, "Skills"], [1132, 1142, "Skills"], [1385, 1389, "Skills"], [1408, 1428, "Skills"], [1565, 1580, "Companies worked at"], [1584, 1608, "Years of Experience"], [1625, 1640, "Companies worked at"], [1742, 1767, "Years of Experience"], [2347, 2350, "Skills"], [2461, 2475, "Skills"], [2962, 2995, "College Name"], [3156, 3611, "Skills"]]}
{"id": 116, "text": "Nikhileshkumar Ikhar\nProduct development engineer with 7+ years of experience with\nM.Tech. in IT. Successfully developed & deployed, platform & behaviour\ndesign strategies in well-established corporations as well as emerging\nstartups.\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Nikhileshkumar-Ikhar/\ncb907948c3299ef4\n\nWilling to relocate to: Hyderabad, Telangana - Mumbai, Maharashtra\n\nWORK EXPERIENCE\n\nProduct Development\n\nAggrigator -  Bengaluru, Karnataka -\n\n2015 to Present\n\nAggrigator is Stanford incubated startup. It is an agriculture-based online marketplace in US B2B\nmarket.\nFirst non-co-founding engineer to come on board reported directly to CTO.\nDeveloped Reverse Auction Engine. A farmer would bid for their SKU and engine would pick\nfarmer based on bidding and capacity.\nDeveloped Palletization engine. A pallet can have multiple SKU boxes from multiple buyers. The\nengine generates a packing sequence of boxes in pallets in a truck container according to the\ndelivery route.\nDeveloped prototype to categorize SKU with Deep learning.\nOwned delivery of functionalities development, behavioural nudges, shaping of platform business\nmodel, aligning of product development to business.\nArchitected, designed & deployed websites, database, UI/UX to facilitate farm fresh produce\nprocurement & delivery to end consumers. Owned and developed various features like, \nInventory management plays a big role in optimizing cold storage, warehouse and trucking\nrequirement. It helped in reducing crop wastage.\nInvoicing of sold crops and tracking payments.\nOrder tracking for buyer and seller.\nFetching USDA price list daily via a web crawler.\nGenerating various reports in online, CSV and PDF format.\nWorked with technologies like Python, Django, Celery, MySQL, MongoDB, Ubuntu, Neural\nNetwork.\nFirst, six months employer was Above Solutions.\n\nSoftware Engineer\n\nCisco -  Bengaluru, Karnataka -\n\n2012 to 2015\n\nOwned and developed several products to help network migration, upgradation, tracking bugs,\ntracking\n\nhttps://www.indeed.com/r/Nikhileshkumar-Ikhar/cb907948c3299ef4?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Nikhileshkumar-Ikhar/cb907948c3299ef4?isid=rex-download&ikw=download-top&co=IN\n\n\nvulnerabilities.\n\n● Presented several proofs of concept for new business requirement.\n\n● Worked on various technologies such as Python, Java, Django, Celery, Cisco routers, SDN,\nOnePk & Hadoop.\n\nWorked on network migration during intern\n\nCisco -\n\nJanuary 2012 to June 2012\n\nSystem Engineer\n\nInfosys -  Pune, Maharashtra -\n\n2008 to 2010\n\nWorked as an SAP Basis consultant. Organised various in house events.\n\nEDUCATION\n\nM.Tech. in IT in VIT, Pune\n\nIIIT -  Bengaluru, Karnataka\n\n2010 to 2012\n\nSKILLS\n\nDjango (6 years), Java (3 years), MongoDB (3 years), MySQL (3 years), Python (6 years)\n\nLINKS\n\nhttp://github.com/nik-hil\n\nhttp://linkedin.com/in/nikhar\n\nADDITIONAL INFORMATION\n\nSkills\nPython, Django, Celery, Java, AngularJS, HTML, Bootstrap 3, Shell Script, MySQL, MongoDB,\nUbuntu\n\nBusiness Skills\nPlatform business model, Behaviour design\n\nhttp://github.com/nik-hil\nhttp://linkedin.com/in/nikhar", "meta": {}, "annotation_approver": "admin", "labels": [[3036, 3078, "Skills"], [2922, 3018, "Skills"], [2738, 2824, "Skills"], [2675, 2678, "College Name"], [2658, 2671, "Degree"], [2530, 2537, "Companies worked at"], [2513, 2528, "Designation"], [2477, 2482, "Companies worked at"], [1880, 1885, "Companies worked at"], [1861, 1878, "Designation"], [493, 503, "Companies worked at"], [438, 448, "Companies worked at"], [417, 437, "Designation"], [279, 331, "Email Address"], [83, 96, "Degree"], [54, 63, "Years of Experience"], [21, 49, "Designation"], [0, 20, "Name"], [236, 256, "Location"], [356, 376, "Location"], [379, 398, "Location"], [452, 472, "Location"], [476, 491, "Years of Experience"], [1748, 1754, "Skills"], [1756, 1762, "Skills"], [1764, 1770, "Skills"], [1772, 1777, "Skills"], [1779, 1786, "Skills"], [1788, 1794, "Skills"], [1796, 1810, "Skills"], [1889, 1909, "Location"], [1913, 1925, "Years of Experience"], [2367, 2373, "Skills"], [2375, 2379, "Skills"], [2381, 2387, "Skills"], [2389, 2395, "Skills"], [2397, 2410, "Skills"], [2412, 2415, "Skills"], [2417, 2422, "Skills"], [2425, 2431, "Skills"], [2469, 2475, "Designation"], [2486, 2511, "Years of Experience"], [2541, 2558, "Location"], [2562, 2574, "Years of Experience"], [2589, 2609, "Designation"], [2680, 2684, "Location"], [2694, 2714, "Location"], [2716, 2728, "Graduation Year"]]}
{"id": 117, "text": "Nitin Tr\nPeopleSoft Consultant\n\nBangalore Urban, Karnataka - Email me on Indeed: indeed.com/r/Nitin-Tr/e7e3a2f5b4c1e24e\n\nAn e-commerce website I built as my college project. The website contains all the basic elements\nof an e-commerce website which are\n\nThe landing page, categorization of items based on filters, basic session level security, product\npage, Cart, share button, empty cart button, paginations etc.\n\nIt consists of a separate seller accounts where sellers can register and later upload their products\nto be sold, which can later be edited or deleted.\n\nIt consists of an admin panel where all the products listed can be viewed and edited by the\nmoderator.\n\n60 days auto delete feature, which deletes the product listing 60 days from the date of upload\nif seller has not modified the listing upon the next login.\n\nUsage of Modals for registration and Login which reduces the number of pages to navigate.\n\nLanguages used: PHP, MySQL, Html, CSS, Bootstrap, JavaScript and jQuery.\n\nWORK EXPERIENCE\n\nPeopleSoft consultant\n\nOracle -\n\nSeptember 2017 to Present\n\nPerforming customisations, enhancements and bug fixes for front end and backend inpeople code\nusing appdesigner.\n\nPeopleSoft Consultant\n\nOracle India Ltd -\n\nSeptember 2017 to April 2018\n\n• Develop customizations to meet business process requirements using application designer.\n\n• Involved in modification enhancement and bug-fixing of the PeopleSoft application both front-\nend and back-end to suit business needs.\n\n• Communicate with the business and get clear requirements if adequate information is not\navailable and also follow-up with them until final resolution is obtained.\n\n• Release Enhancements for UAT and communicate with business to migrate into production.\n\nhttps://www.indeed.com/r/Nitin-Tr/e7e3a2f5b4c1e24e?isid=rex-download&ikw=download-top&co=IN\n\n\n• Also have to work on service requests, Incident Creation, Incident Assignment additionally and\nconstantly involved in querying the database and running reports.\n\nFreelance Development\n\nI am a passionate Web Developer and love to build clean, smooth and responsive websites. I\nhave built many websites for individuals on freelance or per-project basis which are Responsive in\nnature. I am capable of building clean and responsive websites. They include personal portfolios\nand Small business websites.\n• http://www.pramodprakash.com/shop: Responsive and Dynamic e-commerce portal.\n\n• http://www.pramodprakash.com/tickItBusDemoUrl: A bus booking platform, source:\nKathmandu, Destination: Pokhra.\n• http://www.pramodprakash.com/geisle/index.php: A small business website focused on\nanimations (under construction) also check (geisle/index2.php)\n• http://pramodprakash.com/fulllogin: complete login module with email account activation and\npassword reset.\n• http://pramodprakash.com/sec: A small business website, built to showcase color combinations\nand layout.\n• http://pramodprakash.com/r&d: A small business website built in parallax format. Completely\nresponsive.\n• http://pramodprakash.com/web1: A template built according to given specifications and also\nsmall map feature included which also supports location search.\n\nEDUCATION\n\nBtech information science in BCET\n\nVtu -  Bengaluru, Karnataka\n\n2017\n\nVijaya composite p.u. college\n\ne-commerce\n\nVijaya High School\n\nSKILLS\n\nPhp, Html5, Javascript, Css, Bootstrap, Jquery, Sql\n\nLINKS\n\nhttps://www.linkedin.com/in/nitin-tr-*********\n\nADDITIONAL INFORMATION\n\nSoftware Skills\n\nhttps://www.linkedin.com/in/nitin-tr-*********\n\n\n• Programming Language: core java, peoplecode.\n• Scripting languages: PHP, JavaScript.\n• Web Languages: HTML, CSS.\n• Database Language: Sql.\n• Frameworks: Bootstrap, JQuery.\n• IDE's: NetBeans, Eclipse.\n• Tools: Application Designer, Toad.\n\nPersonal Skills\n\n• Excellent Communication Skills both Written and verbal.\n• Honest, trustworthy and highly motivated team player and Strong Negotiator.\n• Supportive and creative.\n• Quick Learner and Flexible.\n• Good Analytical Ability and Logical Reasoning.\n• Good listening skills.", "meta": {}, "annotation_approver": "admin", "labels": [[3511, 3749, "Skills"], [3313, 3364, "Skills"], [3285, 3303, "College Name"], [3273, 3283, "Degree"], [3242, 3271, "College Name"], [3235, 3240, "Graduation Year"], [3207, 3211, "College Name"], [3172, 3206, "Degree"], [1032, 1038, "Companies worked at"], [1008, 1031, "Designation"], [81, 119, "Email Address"], [0, 8, "Name"], [32, 58, "Location"], [9, 30, "Designation"], [934, 937, "Skills"], [939, 944, "Skills"], [946, 950, "Skills"], [952, 955, "Skills"], [957, 966, "Skills"], [968, 978, "Skills"], [983, 989, "Skills"], [1042, 1067, "Years of Experience"], [1183, 1204, "Designation"], [1206, 1222, "Companies worked at"], [1226, 1254, "Years of Experience"], [2000, 2021, "Skills"], [2041, 2054, "Designation"], [2237, 2275, "Skills"], [2289, 2309, "Skills"], [2314, 2337, "Skills"], [3214, 3234, "Location"], [3769, 3824, "Skills"], [3828, 3880, "Skills"], [3885, 3902, "Skills"], [3906, 3929, "Skills"], [3933, 3946, "Skills"], [3951, 3959, "Skills"], [3963, 3986, "Skills"], [3991, 4008, "Skills"], [4011, 4033, "Skills"]]}
{"id": 118, "text": "Pradeeba V\nLEAD ENGINEER - CISCO\n\n- Email me on Indeed: indeed.com/r/Pradeeba-V/19ff20f4b8552375\n\nWORK EXPERIENCE\n\nLEAD ENGINEER\n\nCISCO -\n\nJune 2014 to Present\n\nPROJECT SPECIFIC SKILLS JAVASCRIPT, OOJS, HTML5, CSS3, REST, DOJO,\nAngular JS\nTOOLS USED SVN, Code Collaborator\nDescription:\n\nCisco Prime Infrastructure simplifies the management of wireless and wired networks. The Prime\nUI\noffers Prime Widget Toolkit (XWT) which provides dojo widgets. The UI supports HTML 5 features.\nThe\nPrime UI offers rich UI experience which includes consistency, better look and feel and scalable\ndesigns to handle large volume of data.\n\nResponsibilities:\n• Creating Widgets in dojo\n• Enhancement of existing widget\n• Handling REST calls\n• Writing Test cases\n• Unit Testing\n\n2. Project Title FINUX\n\nINDUSTRY FINACLE - BANKING\n\nSENIOR SYSTEMS ENGINEER\n\n-\n\nJune 2012 to June 2014\n\nPROJECT SPECIFIC SKILLS JAVASCRIPT, DOJO, CSS, HTML\n\nDescription:\nFinacle is a core banking software package. It is used by multiple banks across several countries,\nit can\nhandle multi-currency transactions.\n\nhttps://www.indeed.com/r/Pradeeba-V/19ff20f4b8552375?isid=rex-download&ikw=download-top&co=IN\n\n\nTo achieve the common look and feel for all the screens where the interaction done with the\nuser in the form of getting input values as well as retrieval of some kind of data and display of\nsuch data.\nI was totally involved in developing the Datagrid UI component in DOJO to display the search\nresults which are obtained as a result of inquiring the transactions.\n\nResponsibilities:\n• Front end enhancements for the Core product.\n• Discussing and finalizing the end UI screens with Functional and Design teams.\n• Writing front end and back end validation routines.\n• Regression testing for menus using Service Testing Framework.\n3. Project Title FINACLE\n\nINDUSTRY BANKING\nCLIENT Universal Banking Product from INFOSYS\n\nSYSTEMS ENGINEER\n\n-\n\nOctober 2011 to May 2012\n\nPROJECT SPECIFIC SKILLS JAVASCRIPT, DOJO, CSS, HTML\n\nDescription:\nFinacle is a core banking software package. It is used by multiple banks across several countries,\nit can\nhandle multi-currency transactions.\nBeing a part of Finacle team, I had involved in making the front end enhancement for Core\nproduct.\nResponsibilities:\n\n• Front end enhancements for the Core product.\n• Bug Fixing\n\nEDUCATION\n\nB.Tech\n\nInstitute of Road and Transport May\n\nOctober 1980\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\nProgramming Languages: Core Java\nScripting languages: JavaScript, OOJS\nDatabases: Oracle\nOperating systems: Windows (7, XP) and UNIX\nTools & Utilities: Eclipse, SSH, WinSCP, Code Collaborator, SVN\n\n\n\nWeb Designing Tools: HTML 4, HTML 5, CSS 3, DOJO, Angular JS\nWeb Service: REST Web services", "meta": {}, "annotation_approver": "admin", "labels": [[2707, 2711, "Skills"], [2683, 2693, "Skills"], [2677, 2681, "Skills"], [2670, 2673, "Skills"], [2662, 2666, "Skills"], [2654, 2658, "Skills"], [2626, 2629, "Skills"], [2607, 2624, "Skills"], [2599, 2605, "Skills"], [2594, 2597, "Skills"], [2585, 2592, "Skills"], [2561, 2565, "Skills"], [2541, 2556, "Skills"], [2515, 2521, "Skills"], [2499, 2503, "Skills"], [2487, 2497, "Skills"], [2456, 2465, "Skills"], [2341, 2376, "College Name"], [2333, 2340, "Degree"], [1982, 1986, "Skills"], [1977, 1980, "Skills"], [1971, 1975, "Skills"], [1959, 1969, "Skills"], [1888, 1904, "Designation"], [1436, 1440, "Skills"], [911, 915, "Skills"], [906, 909, "Skills"], [900, 904, "Skills"], [888, 898, "Skills"], [130, 135, "Companies worked at"], [115, 128, "Designation"], [56, 96, "Email Address"], [27, 32, "Companies worked at"], [11, 24, "Designation"], [0, 10, "Name"], [139, 159, "Years of Experience"], [812, 835, "Designation"], [185, 195, "Skills"], [197, 201, "Skills"], [203, 208, "Skills"], [210, 214, "Skills"], [216, 220, "Skills"], [222, 226, "Skills"], [228, 238, "Skills"], [250, 253, "Skills"], [840, 862, "Years of Experience"], [1909, 1933, "Years of Experience"], [2378, 2390, "Graduation Year"]]}
{"id": 119, "text": "Prakriti Shaurya\nSenior System Engineer - Infosys Limited\n\nMangalore, Karnataka - Email me on Indeed: indeed.com/r/Prakriti-\nShaurya/5339383f9294887e\n\nDetail-oriented individual with three years of experience as an IT Consultant looking\nfor opportunity to develop my professional skills in a vibrant and stable environment,\nand to use those skills for the benefits of the organization in best possible way.\n\nWilling to relocate to: Bengaluru, Karnataka - Bangalore Urban, Karnataka\n\nWORK EXPERIENCE\n\nSenior System Engineer\n\nInfosys Limited -  Mangalore, Karnataka -\n\nJanuary 2017 to Present\n\nWorking as an IT Consultant under application maintenance and support for METLIFE Insurance\ncompany.\n\nSystem Engineer\n\nInfosys Limited -  Mangalore, Karnataka -\n\nDecember 2014 to December 2016\n\nWorked as an IT Consultant under application maintenance and support for METLIFE Insurance\ncompany.\n\nSOFTWARE\n\nEDUCATION\n\nBachelor of Technology in Information Technology\n\nVellore Institute of Technology -  Vellore, Tamil Nadu\n\n2010 to 2014\n\nC.B.S.E.\n\nNotre Dame Academy -  Patna, Bihar\n\n2007 to 2009\n\nSKILLS\n\nJava, Jsp, Html, Sql, C++, Javascript\n\nhttps://www.indeed.com/r/Prakriti-Shaurya/5339383f9294887e?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Prakriti-Shaurya/5339383f9294887e?isid=rex-download&ikw=download-top&co=IN\n\n\nADDITIONAL INFORMATION\n\nSKILLS\n• Good communication - written and oral skills\n• Excellent conceptual and analytical skills\n• Effective interpersonal skills\n\nPERSONALITY\n• Communicative • Punctuality\n• Creativity • Organized", "meta": {}, "annotation_approver": "admin", "labels": [[1368, 1560, "Skills"], [1096, 1133, "Skills"], [1038, 1057, "College Name"], [1028, 1037, "Degree"], [958, 990, "College Name"], [908, 956, "Degree"], [711, 726, "Companies worked at"], [524, 539, "Companies worked at"], [500, 522, "Designation"], [102, 149, "Email Address"], [42, 57, "Companies worked at"], [17, 39, "Designation"], [0, 16, "Name"], [59, 79, "Location"], [432, 452, "Location"], [455, 481, "Location"], [543, 563, "Location"], [567, 590, "Years of Experience"], [694, 709, "Designation"], [730, 750, "Location"], [754, 784, "Years of Experience"], [993, 1012, "Location"], [1014, 1026, "Graduation Year"], [1060, 1072, "Location"], [1074, 1086, "Graduation Year"]]}
{"id": 120, "text": "PRASHANTH BADALA\nDevops Engineer ,Cloud Engineer -Oracle\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/PRASHANTH-BADALA/\nbf4c4b7253a8ece7\n\n• Hands on experience in end-end process of Build Management, Release Management and\nConfiguration Management.\n• Hands on experience on supporting configuration management tools on both physical and cloud\nenvironment\n• Involved in setting up Jenkins in Distributed Environments with Master and Slave\n• Working experience on Subversion (SVN) administration and basic usage\n• Creating Branches, tags and providing SVN user access to all developers in the organization\n• Managing application server instances running on AWS\n• Involved in configuring EC2 instances along with Auto scale up options.\n• Involved in configuration Management using CHEF and automated the complete platform\nsetup.\n• Good knowledge on Cookbooks, Chef Kitchen, Burks file and Metadata.\n• Involved in automating the release using UDeploy and deploying the application to different\nenvironments.\n• Involved in container setup with Docker. Having good understanding of creating and running\nDocker images.\n• Involved in writing Applications, Componenets, Resources and component process flow.\n• Configured Jenkins as contionus integration tool for regular source code builds.\n• Experience in setting up branching strategies, merging and taking regular backups of the source\ncode on SVN server\n• Helping the Developers in SVN related issues\n• Written the integrated build automation scripts using Ant\n• Perform the QA & Stress and UAT builds and deployed the binaries on respective environment\nservers\n• Monitoring the deployment in all the servers\n• Supported setting up of various environments in multi-tier architecture involving load balancers,\nApache webservers, Oracle database,\n• Developed custom scripts to automate the build and release process.\n• Implemented a custom authentication with WebLogic for user authentication, authorization and\npassword policy\n• Monitoring environment using monitoring tools like Wily monitoring tool and custom shell script.\n• Involved in integrating WebLogic with Wily introscope.\n• Involved in resolving tickets with different SLAs like SEV1, SEV2, SEV3 and SEV4.\n• Involved in change management process like opening change records using change\nmanagement tools like INFRA, Managenow.\n• Providing on call, weekend and deployment support.\n• Involved in applying security patches using WebLogic Utility.\n• Deploying applications using different deployment strategies like Stage, No Stage and External\nstage.\nWork Experience:\n\n• Working as Configuration Engineer in Oracle, Hyderabad from July 2015 to till date.\n\nhttps://www.indeed.com/r/PRASHANTH-BADALA/bf4c4b7253a8ece7?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/PRASHANTH-BADALA/bf4c4b7253a8ece7?isid=rex-download&ikw=download-top&co=IN\n\n\nTechnical Skills:\n\nVersion Control Tools \n\nSubversion(SVN),GIT\n\nWeb/Appservers\nContinuous Integration Tools\nWebLogic 11g, Apache HTTP Server 2.4\nJenkins 1.6, Hudson\n\nBuild Tools \n\nMaven 3\n\nScripting Languages \n\nShell Scripting and Python\n\nOperating Systems \n\nWindows servers […] Windows XP/7, Red Hat LINUX 5\n\nDatabase\n\nOracle 11g\n\nRelease Tools\n\nUDeploy,Jenkins\n\nCloud\n\nAWS\n\nConfiguration Tools\n\nCHEF\n\nEducation Qualification:\nB.Tech From Annamacharya Institute of Technology, JNTU Hyderabad - 2015 \n\nProject Details\n\nTitle: E-Banking solutions \n\nClient : Union Bank of Canada, Canada \nRole : Configuration Engineer \nEnvironment : Maven, Jenkins, CHEF, UDeploy, SVN, Linux, Weblogic,Aws \n\n\n\nDuration : Nov 2016 to till date\n\nProject Description:\n\nThis is a banking project and basic objective of this project is to deal with the loans. This\napplication is based on Java technology. For this we have to schedule the tasks and have to collect\ncode from development team and have to build and deploy the code later have to support the\nrelease management team of which executing Java applications build and deployments in Dev,\nQA, performance and production environments.\n. .\nResponsibilities:\n• Involved in automation of Configuration Management using CHEF and automated multiple\nenvironments like Prod and Non Prod.\n• Involved in configuring AWS Environment to deploy applications.\n• Involved in Release Management and automated the overall release process using Urban Code\nDeployments (UDeploy)\n• For on boarding existing application, performing knowledge transition from development team\nto SCM team on build and deployment process.\n• For new applications, work with development to get the requirements of application build and\ndeployment process.\n• Creating War/Ear files using Ant Script\n• Creating Jenkins/Hudson jobs.\n• Monitor and track requests in Subversion.\n• Monitor and fix the continuous integration builds running in Jenkins.\n• Troubleshooting the compilation errors and provide the technical support to the developers\nteam on that issue.\n• Installation, configuring, administration of Web Logic 9.x, 10.x on Linux environment.\n• Supporting Builds for Multiple environments like Dev, QA, Staging, Perf and Production.\n• Performing Manual and Automation Builds using Maven and Jenkins.\n• Troubleshooting the middle ware issues and resolving the P2,P3 tickets with in SLA\n• Provided on call support on 24/7 basis\n• Involved in creation and configuration of Domains, JVM instances in production, QA and UAT\nenvironments.\n• Configured clusters to provide fail over, load balancing and deployed applications on clusters.\n• Installed, configured and administration to Web logic 10.x, JDBC Data source and Connection\npool configuration with Oracle. Web Logic Administration, Monitoring and Troubleshooting using\nAdmin Console.\n• Have excellent experience in Client Interaction\n• Provided on call support for production tickets on 24/7 basis.\n• Deployment of web and enterprise applications and their updates in dev, production, pre-\nproduction using Admin console.\n\n• Creating branches & merging using Subversion.\n• Performing deployments to multiple environments like Dev, QA, Field, Perf, UAT & Production\nenvs\n• Troubleshooting application related issues by log verification.\n• Writing a UNIX Shell Script and schedule in the respective run levels for automate day-to-day\nactivities such as auto start application server.\n• Automate code deployments by using ANT and Jenkins.\n\n\n\nSprint Value Added Services\n\nClient : Sprint, U.S \nRole : Build and Release Engineer\nEnvironment: Maven, Jenkins, SVN, Linux, Weblogic, Apache,AWS,Docker\nDuration : Dec 2015 to Oct 2016\n\nProject Description:\n\nThis is a Value Added Services provided by Sprint. This application is based on Java technology.\nFor this we have to schedule the tasks and have to collect code from development team and\nhave to build and deploy the code later have to support the release management team of which\nexecuting Java applications build and deployments in Dev, QA, performance and production\nenvironments.\n\nResponsibilities:\n\n• For on boarding existing application, performing knowledge transition from development team\nto SCM team on build and deployment process.\n• For new applications, work with development to get the requirements of application build and\ndeployment process.\n• Installing and configuring Subversion (SVN) and Jenkins.\n• Providing support to Subversion (SVN) related issues.\n• Developing and maintaining build files by using Ant script.\n• Integrate Unit Testing in Ant builds\n• Installation, configuring, administration of Web Logic 9.x, 10.x on Linux environment.\n• Supporting Builds for Multiple environments like Dev, QA, Staging, Perf and Production.\n• Performing Manual and Automation Builds using Maven and Jenkins.\n• Troubleshooting the middle ware issues and resolving the P2,P3 tickets with in SLA\n• Provided on call support on 24/7 basis\n• Involved in creation and configuration of Domains, JVM instances in production, QA and UAT\nenvironments.\n• Configured clusters to provide fail over, load balancing and deployed applications on clusters.\n• Installed, configured and administration to Web logic 10.x, JDBC Data source and Connection\npool configuration with Oracle. Web Logic Administration, Monitoring and Troubleshooting using\nAdmin Console.\n• Have excellent experience in Client Interaction\n• Provided on call support for production tickets on 24/7 basis.\n• Deployment of web and enterprise applications and their updates in dev, production, pre-\nproduction using Admin console.\n• Building the source code using Jenkins.\n• Helped developers in resolving SVN issues and concerns.\n• Responsible for weekly and daily work checks and backups.\n• Environment: WebLogic Application Server 10.3, JDK1.6, Oracle, Apache Webserver, Linux,\nJIRA,Infra,SSH,TOAD\n\nKOhls Retail Services\n\n\n\nClient : Kohls, US\nRole : Build and Release Engineer. \nEnvironment: Maven, Jenkins, SVN, Linux, WebLogic, Apache\nDuration : July 2015 to Nov 2015\n\nProject Description:\n\nThis application is based on Java technology. For this we have to schedule the tasks and have to\ncollect code from development team and have to build and deploy the code later have to support\nthe release management team of which executing Java applications build and deployments in\nDev, QA, performance and production environments.\n\nResponsibilities: \n\n• Developing and maintaining build files by using Ant script.\n• Integrate Unit Testing in Ant builds\n• Installation, configuring, administration of Web Logic 9.x, 10.x on Linux environment.\n• Supporting Builds for Multiple environments like Dev, QA, Staging, Perf and Production.\n\n• Supporting Builds for Multiple environments like Dev, QA, Staging, Perf and Production.\n• Performing Manual and Automation Builds using Maven and Jenkins.\n• Deploying WAR, EAR applications on various targeted servers in the clustered environments.\n• Web Logic Administration, Monitoring and Troubleshooting using Admin Console.\n• Analyzing log files and periodic removal of them\n• Creating branches & merging using Subversion.\n• Performing deployments to multiple environments like Dev, QA, Field, Perf, UAT & Production\nenvs\n• Troubleshooting application related issues by log verification.\n• Writing a UNIX Shell Script and schedule in the respective run levels for automate day-to-day\nactivities such as auto start application server.\n• Automate code deployments by using ANT and Jenkins.\n\n• Involved in changing heap parameters like –Xms, -Xmx, -XnoOpt,-XnoHup etc.\n• Perform daily environment health-check\n• Good in taking the thread dumps and finding the root cause analysis.\n• Created and configured web logic server instances, clusters in domain environment.\n• Installed web logic on production boxes in console mode.\n• Strong experience in administrating by using Admin console.\n\nEnvironment: WebLogic Application Server 9.2, Java, Oracle, Apache Webserver, Linux, JIRA,\nManagenow, Putty, TOAD\n\nWilling to relocate to: Hyderabad, Telangana - Bangalore Urban, Karnataka - Chennai, Tamil Nadu\n\nWORK EXPERIENCE\n\nConfiguration Engineer\n\nOracle -  Hyderabad, Telangana -\n\n\n\nJuly 2015 to Present\n\n• Hands on experience in end-end process of Build Management, Release Management and\nConfiguration Management.\n• Hands on experience on supporting configuration management tools on both physical and cloud\nenvironment\n• Involved in setting up Jenkins in Distributed Environments with Master and Slave\n• Working experience on Subversion(SVN) administration and basic usage\n• Creating Branches, tags and providing SVN user access to all developers in the organization\n• Managing application server instances running on AWS\n• Involved in configuring EC2 instances along with Auto scale up options.\n• Involved in configuration Management using CHEF and automated the complete platform\nsetup.\n• Good knowledge on Cookbooks, Chef Kitchen, Burks file and Metadata.\n• Involved in automating the release using UDeploy and deploying the application to different\nenvironments.\n• Involved in container setup with Docker. Having good understanding of creating and running\nDocker images.\n• Involved in writing Applications, Componenets, Resources and component process flow.\n• Configured Jenkins as contionus integration tool for regular source code builds.\n• Experience in setting up branching strategies, merging and taking regular backups of the source\ncode on SVN server\n• Helping the Developers in SVN related issues\n• Written the integrated build automation scripts using Ant\n• Perform the QA & Stress and UAT builds and deployed the binaries on respective environment\nservers\n• Monitoring the deployment in all the servers\n• Supported setting up of various environments in multi-tier architecture involving load balancers,\nApache webservers, Oracle database, \n• Developed custom scripts to automate the build and release process.\n• Implemented a custom authentication with WebLogic for user authentication, authorization and\npassword policy\n• Monitoring environment using monitoring tools like Wily monitoring tool and custom shell script.\n• Involved in integrating WebLogic with Wily introscope.\n• Involved in resolving tickets with different SLAs like SEV1, SEV2, SEV3 and SEV4.\n• Involved in change management process like opening change records using change\nmanagement tools like INFRA, Managenow.\n• Providing on call, weekend and deployment support.\n• Involved in applying security patches using WebLogic Utility.\n• Deploying applications using different deployment strategies like Stage, No Stage and External\nstage.\n\nConfiguration Engineer\n\nOracle\n\nDevops\n\n\n\nEDUCATION\n\nB.TECH/B.E\n\nAnnamacharya Institute of Technology, JNTU, Hyderabad -  Hyderabad, Telangana\n\n2015\n\nSKILLS\n\nAWS (1 year), CHEF (1 year), Linux (2 years), git, svn, maven, devops, jenkins, Docker,\nweblogic\n\nADDITIONAL INFORMATION\n\nOperating Systems Windows servers […] Windows XP/7, Red Hat LINUX 5\nDatabase Oracle 11g\nRelease Tools UDeploy, Jenkins\nCloud AWS\nConfiguration Tools CHEF", "meta": {}, "annotation_approver": "admin", "labels": [[13907, 13911, "Skills"], [13883, 13886, "Skills"], [13724, 13732, "Skills"], [13716, 13722, "Skills"], [13707, 13714, "Skills"], [13699, 13705, "Skills"], [13691, 13697, "Skills"], [13687, 13690, "Skills"], [13682, 13685, "Skills"], [13665, 13670, "Skills"], [13650, 13654, "Skills"], [13636, 13639, "Skills"], [13587, 13596, "Location"], [13543, 13579, "College Name"], [13502, 13508, "Companies worked at"], [13478, 13500, "Designation"], [12020, 12026, "Skills"], [11962, 11968, "Skills"], [11701, 11705, "Skills"], [11578, 11582, "Skills"], [11004, 11010, "Companies worked at"], [10980, 11002, "Designation"], [10829, 10834, "Skills"], [9450, 9455, "Skills"], [8846, 8851, "Skills"], [8704, 8709, "Skills"], [7512, 7517, "Skills"], [6507, 6513, "Skills"], [6503, 6506, "Skills"], [6479, 6484, "Skills"], [4994, 4999, "Skills"], [4213, 4216, "Skills"], [4122, 4126, "Skills"], [3466, 3488, "Designation"], [3367, 3371, "Graduation Year"], [3355, 3364, "Location"], [3312, 3348, "College Name"], [3300, 3306, "Degree"], [3269, 3273, "Skills"], [3248, 3268, "Skills"], [3243, 3246, "Skills"], [3236, 3242, "Skills"], [3219, 3234, "Skills"], [3083, 3110, "Skills"], [3052, 3059, "Skills"], [3017, 3036, "Skills"], [2980, 3016, "Skills"], [2951, 2979, "Skills"], [2936, 2950, "Skills"], [2915, 2935, "Skills"], [2891, 2914, "Skills"], [2630, 2639, "Location"], [2622, 2628, "Companies worked at"], [2596, 2618, "Designation"], [1109, 1115, "Skills"], [1051, 1057, "Skills"], [790, 794, "Skills"], [101, 148, "Email Address"], [50, 56, "Companies worked at"], [34, 48, "Designation"], [17, 32, "Designation"], [0, 16, "Name"], [58, 78, "Location"], [392, 427, "Skills"], [474, 490, "Skills"], [562, 565, "Skills"], [667, 670, "Skills"], [697, 700, "Skills"], [858, 867, "Skills"], [869, 881, "Skills"], [883, 893, "Skills"], [898, 906, "Skills"], [951, 958, "Skills"], [1224, 1231, "Skills"], [1400, 1403, "Skills"], [1439, 1442, "Skills"], [1514, 1517, "Skills"], [1532, 1543, "Skills"], [1766, 1783, "Skills"], [1785, 1800, "Skills"], [1915, 1923, "Skills"], [2036, 2056, "Skills"], [2108, 2116, "Skills"], [2122, 2126, "Skills"], [2186, 2222, "Skills"], [2443, 2459, "Skills"], [2640, 2667, "Years of Experience"], [3131, 3178, "Skills"], [3192, 3203, "Skills"], [3350, 3354, "Skills"], [3504, 3559, "Skills"], [4333, 4366, "Skills"], [4464, 4467, "Skills"], [4652, 4662, "Skills"], [4674, 4693, "Skills"], [4727, 4737, "Skills"], [4802, 4809, "Skills"], [4971, 4990, "Skills"], [5151, 5156, "Skills"], [5161, 5168, "Skills"], [5340, 5347, "Skills"], [5349, 5352, "Skills"], [5378, 5380, "Skills"], [5385, 5388, "Skills"], [5547, 5561, "Skills"], [5619, 5625, "Skills"], [5627, 5636, "Skills"], [5980, 5990, "Skills"], [6169, 6186, "Skills"], [6340, 6343, "Skills"], [6348, 6355, "Skills"], [6458, 6463, "Skills"], [6417, 6444, "Designation"], [6465, 6472, "Skills"], [6474, 6477, "Skills"], [6486, 6494, "Skills"], [6496, 6502, "Skills"], [6525, 6545, "Years of Experience"], [7255, 7271, "Skills"], [7276, 7283, "Skills"], [7308, 7324, "Skills"], [7391, 7401, "Skills"], [7415, 7427, "Skills"], [7431, 7434, "Skills"], [7489, 7508, "Skills"], [7669, 7674, "Skills"], [7679, 7686, "Skills"], [7867, 7870, "Skills"], [7896, 7898, "Skills"], [7903, 7906, "Skills"], [8065, 8079, "Skills"], [8137, 8143, "Skills"], [8081, 8097, "Skills"], [8145, 8154, "Skills"], [8494, 8501, "Skills"], [8536, 8539, "Skills"], [8636, 8668, "Skills"], [8670, 8676, "Skills"], [8678, 8684, "Skills"], [8686, 8702, "Skills"], [8711, 8715, "Skills"], [8716, 8721, "Skills"], [8722, 8725, "Skills"], [8782, 8809, "Designation"], [8825, 8830, "Skills"], [8832, 8839, "Skills"], [8841, 8844, "Skills"], [8853, 8861, "Skills"], [8863, 8869, "Skills"], [8880, 8902, "Years of Experience"], [9353, 9365, "Skills"], [9369, 9379, "Skills"], [9329, 9339, "Skills"], [9427, 9446, "Skills"], [9698, 9703, "Skills"], [9708, 9715, "Skills"], [9812, 9821, "Skills"], [9977, 9987, "Skills"], [10166, 10183, "Skills"], [10337, 10340, "Skills"], [10345, 10352, "Skills"], [10764, 10772, "Skills"], [10803, 10809, "Skills"], [10811, 10827, "Skills"], [10836, 10840, "Skills"], [10842, 10851, "Skills"], [10853, 10858, "Skills"], [10890, 10910, "Location"], [10913, 10939, "Location"], [10942, 10961, "Location"], [11014, 11034, "Location"], [11040, 11060, "Years of Experience"], [11304, 11311, "Skills"], [11386, 11401, "Skills"], [11473, 11476, "Skills"], [11608, 11611, "Skills"], [11769, 11778, "Skills"], [11780, 11792, "Skills"], [11794, 11817, "Skills"], [11862, 11869, "Skills"], [12135, 12142, "Skills"], [12311, 12314, "Skills"], [12350, 12353, "Skills"], [12425, 12428, "Skills"], [12696, 12702, "Skills"], [12677, 12683, "Skills"], [12827, 12835, "Skills"], [12948, 12952, "Skills"], [13020, 13028, "Skills"], [13034, 13038, "Skills"], [13238, 13243, "Skills"], [13245, 13254, "Skills"], [13355, 13363, "Skills"], [13510, 13516, "Skills"], [13581, 13585, "College Name"], [13531, 13541, "Degree"], [13600, 13620, "Location"], [13622, 13626, "Graduation Year"], [13776, 13845, "Skills"], [13860, 13867, "Skills"], [13869, 13876, "Skills"]]}
{"id": 121, "text": "Pratibha P\nPrincipal Consultant at Oracle\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Pratibha-P/b4c1202741d63c6c\n\nOver 14 years of experience in estimation, design, development, implementation, testing and\nenhancement of various Oracle applications. Currently working as Principal Consultant (Oracle\nApplications) with the consulting group Global Service Delivery (GSD) of Oracle India Pvt Ltd,\nBangalore. Possesses excellent communication, extensive functional and technical experience in\nimplementing Oracle E-business Suite applications.\nWorked on various modules like AR, AP, GL, FA, OLFM, HRMS and PO. Has gained proficiency in\nOracle 9i/10g, SQL, PL/SQL, Discoverer 10g, Oracle reports 6i/10g, BI Publisher reports, OEM (For\ndata masking), Methodologies (Oracle AIM, OUM) & Migration Tool (Kintana)\n\nWilling to relocate to: Bangalore City, Karnataka\n\nWORK EXPERIENCE\n\nPrincipal Consultant\n\nOracle -\n\nJuly 2012 to Present\n\nOver 14 years of experience in estimation, design, development, implementation, testing and\nenhancement of various Oracle applications. Currently working as Principal Consultant (Oracle\nApplications) with the consulting group Global Service Delivery (GSD) of Oracle India Pvt Ltd,\nBangalore. Possesses excellent communication, extensive functional and technical experience in\nimplementing Oracle E-business Suite applications.\nWorked on various modules like AR, AP, GL, FA, OLFM, HRMS and PO. Has gained proficiency in\nOracle 9i/10g, SQL, PL/SQL, Discoverer 10g, Oracle reports 6i/10g, BI Publisher reports, OEM(For\ndata masking),Methodologies(Oracle AIM,OUM)& Migration Tool(Kintana)\n\nSenor Consultant\n\nOracle -\n\n2011 to July 2011\n\nStaff Consultant\n\nCaterpillar -\n\nDecember 2005 to December 2007\n\nDuration/Size: 4 months/10\n\nAssociate Consultant\n\nCaterpillar -\n\nOctober 2004 to December 2005\n\nhttps://www.indeed.com/r/Pratibha-P/b4c1202741d63c6c?isid=rex-download&ikw=download-top&co=IN\n\n\nDuration/Size: 4 months/10\n\nAssociate Consultant\n\nVault Consulting -\n\nOctober 2003 to October 2004\n\nProject Name: AMAT\nClient: Applied Materials\nDuration/Size: 1 months/3\nOrganization: Oracle\nEnvironment: Oracle Applications (11i)\nResponsibilities: AMAT was an implementation project. I was involved in developing complex\nDiscoverer reports which fully meets the objectives for forecasting the history and exception\ndetails of AMAT's buyers/Suppliers with capability to review FGI (Finished Goods Inventory), LLI\n(Linked Level Inventory) details\n\nProject Name: Dollar General\nClient: Dollar General\nDuration/Size: 2 months/3\nOrganization: Oracle\nEnvironment: Data Warehousing\nResponsibilities: Dollar General was an implementation project. I was involved in developing\nDiscoverer reports.\n\nProject Name: Emerson\nClient: Emerson Process Management\nDuration/Size: 2 months/3\nOrganization: Oracle\nEnvironment: Data Warehousing\nResponsibilities: Dollar General was an implementation project. I was involved in developing\nDiscoverer report for each division-country, division-world area and total division (single reports) .\nThe divisions were also compared against each other (comparison reports)\n\nEDUCATION\n\nB.E\n\nVisvesvaraya Technological University\n\n2017\n\nBachelor of Engineering in Engineering\n\nDayanand Sagar College of Engineering Bangalore (Visvesvaraya Technological University) -\nSagar, Karnataka\n\n\n\nSKILLS\n\nExtension, PLSQL, BI Publisher Reports, Oracle Reports, AP, HRMS, GL, Conversion, Oracle Apps\ntechnical, Interface, AR, TOAD, OLFM, SQL, FA\n\nADDITIONAL INFORMATION\n\nSoftware Skills:\n\nERP-Oracle Applications 11i, R12\nAccounts Receivable, Accounts Payable, Fixed Assets, General Ledger, Oracle Lease and Finance\nManagement, Purchasing and HRMS (Employee, Assignment, Contacts, Pay methods, Job and\nLocation)\n\nRDBMS Oracle […]\nLANGUAGES C++\nTOOLS Toad, SQL Developer, Kintana, Report Builder 6i/10g, Discoverer Admin/Desktop/Plus/\nViewer and BI Publisher", "meta": {}, "annotation_approver": "admin", "labels": [[3345, 3896, "Skills"], [3276, 3313, "College Name"], [3227, 3264, "College Name"], [3187, 3225, "Degree"], [3142, 3179, "College Name"], [3137, 3141, "Degree"], [2117, 2123, "Companies worked at"], [910, 916, "Companies worked at"], [888, 908, "Designation"], [35, 41, "Companies worked at"], [11, 31, "Designation"], [0, 10, "Name"], [43, 63, "Location"], [86, 126, "Email Address"], [133, 141, "Years of Experience"], [243, 249, "Skills"], [285, 327, "Designation"], [354, 383, "Companies worked at"], [387, 407, "Companies worked at"], [409, 418, "Location"], [517, 553, "Skills"], [647, 689, "Skills"], [691, 758, "Skills"], [775, 790, "Skills"], [810, 817, "Skills"], [843, 869, "Location"], [920, 940, "Years of Experience"], [947, 955, "Years of Experience"], [1057, 1076, "Skills"], [1099, 1141, "Designation"], [1168, 1197, "Companies worked at"], [1200, 1222, "Companies worked at"], [1223, 1232, "Location"], [1331, 1367, "Skills"], [1461, 1503, "Skills"], [1505, 1571, "Skills"], [1586, 1600, "Skills"], [1618, 1625, "Skills"], [1628, 1644, "Designation"], [1646, 1652, "Companies worked at"], [1656, 1673, "Years of Experience"], [1675, 1691, "Designation"], [1693, 1704, "Companies worked at"], [1708, 1738, "Years of Experience"], [1768, 1788, "Designation"], [1790, 1801, "Companies worked at"], [1805, 1834, "Years of Experience"], [1960, 1980, "Designation"], [1982, 1998, "Companies worked at"], [2002, 2030, "Years of Experience"], [2137, 2156, "Skills"], [2571, 2589, "Skills"], [2819, 2837, "Skills"], [3181, 3185, "Graduation Year"], [3265, 3274, "Location"], [3317, 3333, "Location"]]}
{"id": 122, "text": "Prem Koshti\nOfficer-HR & Administration in H.& R. Johnson (India) - SAP - R\n\nDewas, Madhya Pradesh - Email me on Indeed: indeed.com/r/Prem-Koshti/a1fec9e7289496f0\n\n❖ To acquire a key Position in Human Resource Management / SAP field by continuously improving\nknowledge and skills.\n❖ Very strong logical, analytical skills with vast experience in MS-EXCEL.\n❖ Very energetic, hardworking and highly self-motivated team player with strong problem solving\nskills and very good communication and leadership skills. Very flexible.\n\nProjects:-\nProject Name: SAP HR, Employee Administration\nClient: H.& R. Johnson (India) [A Division of Prism Cement Limited], DEWAS (M.P.)\n\nWORK EXPERIENCE\n\nOfficer-HR & Administration in H.& R. Johnson (India)\n\nSAP - R -  Dewas, Madhya Pradesh -\n\nJuly 2002 to Present\n\n- SAP - R/3, 06 years' experience in SAP HR-Functional Module\nCurrent Employer:\nPresently working as Officer-HR & Administration in H.& R. Johnson (India), [A Division of Prism\nCement Limited], DEWAS (M.P.) from 30.07.2002 to till date.\n\nEDUCATION\n\nB.Com. in Dr. Harisingh Gour V.V\n\nPolytechnic College Damoh -  Sagar, Madhya Pradesh\n\n1990\n\nSKILLS\n\nHR (10+ years), SAP (10+ years), APPRAISAL (Less than 1 year), BUYING/PROCUREMENT (Less\nthan 1 year), DATABASE (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTechnical skills: SAP HR Module.\nDescription:-\nMaintaining electronic punching system, Daily Production MIS Report, Monthly Manpower\nreport. I.F. Annual Returns, Form-5 Holiday wages. Employee's gratuity policy updation.\nMaintaining all record's & document related to personal / HR department, Employees data\nbank. Employees leave, attendance, c-off, absenteeism statement. Payroll Preparation, Bonus,\n\nhttps://www.indeed.com/r/Prem-Koshti/a1fec9e7289496f0?isid=rex-download&ikw=download-top&co=IN\n\n\nOvertime, Attendance Incentive, Production Incentive, Arrear Wages, Wage slip, Full & Final\nSettlement, No Dues Certificate, Issue of certificate if any, ID / Punching card, Birth day card etc.\n\nRoles and responsibilities: Personal & HR Functions:\n\n✓ Performance Appraisal / Increments preparation co-ordination.\n\n✓ Computerized Time Office Management, HR Policy.\n\n✓ Handle Payroll on SAP & responsible for smooth functioning of payroll cycle.\n\n✓ Maintain employee data on SAP and updates them as and when required\n\n✓ Reconciling statutory reports i.e. PF, ESIC, and coordinating with Finance Team for timely\npayment.\n\n✓ Production & manpower MIS data in MS-Excel daily, Monthly & Yearly basis.\n\n✓ Performing of Exit Formalities and processing Full and Final Settlement for exit cases.\n\n✓ Maintain Attendance record in Electronic Punching Machine.\n\n✓ Joining Formalities (Pre & Post recruitment activities)\n\nGeneral Administration\n\nOffice stationery, Telephones, Fax, Computers, Reception, Purchasing First Aid, mineral water,\nbiscuits etc\n\nTechnical Expertise\nFront End Tool\nLanguages\nSAP Technologies HR and Administration Functional Module\nDatabase FOXPRO 6.22\nConcepts OOP'S, Networking, DBMS, Operating System.\nOperating System MS-Dos […] & MS OFFICE 2007, 2008 & 2010\n\nTRAININGIG PROGRAMME & CONFERENCE ATTENDED:\n\n❖ First Aid Procedure conducted by St. John Ambulance Association.\n\n❖ Fire Fighting by Usha Fire Safety.\n\n❖ Interpersonal Skills, Communication, Motivational related various Training programmes\norganize.\n\n❖ SAP - HR Module conducted by Covansys, Mumbai.\n\n\n\n❖ Internal Auditors Training Program on Environmental & Occupational Health & Safety\nManagement System", "meta": {}, "annotation_approver": "admin", "labels": [[2861, 3074, "Skills"], [1145, 1274, "Skills"], [798, 801, "Companies worked at"], [120, 162, "Email Address"], [0, 11, "Name"], [12, 39, "Designation"], [75, 99, "Location"], [183, 220, "Designation"], [288, 321, "Skills"], [346, 354, "Skills"], [363, 372, "Skills"], [374, 385, "Skills"], [390, 423, "Skills"], [429, 458, "Skills"], [468, 486, "Skills"], [490, 508, "Skills"], [510, 523, "Skills"], [591, 613, "Companies worked at"], [652, 664, "Location"], [683, 710, "Designation"], [714, 745, "Companies worked at"], [43, 75, "Companies worked at"], [749, 770, "Location"], [774, 794, "Years of Experience"], [809, 817, "Years of Experience"], [833, 836, "Skills"], [897, 924, "Designation"], [928, 950, "Companies worked at"], [990, 1002, "Location"], [1008, 1031, "Years of Experience"], [1045, 1051, "Degree"], [1055, 1098, "College Name"], [1099, 1129, "Location"], [1131, 1135, "Graduation Year"], [1318, 1331, "Skills"], [1404, 1414, "Skills"], [1416, 1439, "Skills"], [1442, 1460, "Skills"], [1461, 1482, "Skills"], [2185, 2188, "Skills"], [2273, 2276, "Skills"], [3327, 3342, "Skills"]]}
{"id": 123, "text": "Pulkit Saxena\nNew Delhi, Delhi - Email me on Indeed: indeed.com/r/Pulkit-Saxena/ad3f35bfe88a0410\n\nI have a high degree of technical competence, a strong learning aptitude and an excellent\nwork ethic. I am a technical expert in a number of network areas, in particular connectivity,\nperformance, scalability and security. As an articulate communicator, I have ability to influence\npeople at every level by ensuring that issues are discussed, conflicts are resolved and the best\nsolutions are delivered. In my current role I work with the rest of the team to ensure the successful\ndelivery and operation of all supported services. Right now, I would like to join a growing company\nthat wants to recruit proven and experienced IT people.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nCisco -  Samba, Jammu and Kashmir -\n\n2000 to 2000\n\nAmple knowledge in Windows 98 \\ Me \\ Xp \\ 2000 \\ Server 2003 \\Server 2008 \\Server\n2008r2\\Server 2012\n✓ Active directory management, NTFS security, disk quota management\n✓ Good understanding of OSI Model, TCP/IP protocol suite (IP, ARP, ICMP, TCP, UDP, RARP, FTP,\nTFTP)\n✓ Ample understanding of Bridging and switching concepts and LAN technologies\n✓ IP addressing and subnetting, Routing concepts\n✓ Sound knowledge of routing protocols - RIP V1/V2, OSPF, IGRP & EIGRP\n✓ Switches: Basic Configuration & VLAN setup on Cisco 1900, 2950, 2960 Switches &\nTroubleshooting.\n✓ Router: Basic Configuration & monitoring of Cisco 2500, 2600, 1800\n✓ Vlan: configuration, switching isl, dotlq\n✓ Cisco Firewall 5500 Series: Configuration and policies Setup and Troubleshooting.\n✓ Back-up and restore of all critical resources including router & switches IOS, Outlook, DHCP,\nDNS.\n✓ Functioning knowledge of wan solution, protocol HDLC, PPP\n✓ Working knowledge of, DHCP Server, DNS Server, ADDS, Proxy Server on Linux and Complete\nKnowledge on Windows\n✓ Security administration port security on switch and IP security on Router via Access list\n✓ Familiar with web technology HTML CSS\n✓ Setting up Secure NFS Servers with multiple Clients for File and Disk sharing.\n✓ Setting up SAMBA servers, to enable Windows clients to communicate with Linux without the\nneed of additional software on the Windows side.\n✓ Configuring NIS Servers for Centralized and secure Password and Login Management\n✓ Linux user management creating, changing group, and assign permission on resources\n✓ Recover of root password securing terminals\nHardware\n✓ Computer assembling and maintenance.\n✓ Troubleshooting hardware and software problems.\n✓ Installing and configuring the peripherals, components and drivers.\n\nhttps://www.indeed.com/r/Pulkit-Saxena/ad3f35bfe88a0410?isid=rex-download&ikw=download-top&co=IN\n\n\n✓ Installing software and application to user standards.\n\nEDUCATION\n\nMCL in Computer Application\n\nIGNOU\n\nBACHELOR'S IN SCIENCE in COMPUTER NETWORKING\n\nKarnataka State University\n\nComputer Networking\n\nAptech Institute of Technology\n\nComputing\n\nAptech Institute of Technology\n\nSKILLS\n\nFirewall (1 year), HTML (1 year), MICROSOFT WINDOWS (1 year), Router (1 year), security. (1\nyear)\n\nADDITIONAL INFORMATION\n\nSKILLS\n✓ A computer expert with hardware and software grab.\n✓ Administration and Back-end handling (Technical) on overall basis of the Branch.\n✓ Convincing people tactfully so as to make the company an option for the user.\n\nAREAS OF EXPERTISE\n✓ Server administration\n✓ Technical documentation\n✓ Network security\n✓ Network management\n✓ Data backups\n✓ Disaster recovery\n✓ Cisco Router\n✓ Cisco Switch\n✓ Network management\n✓ Switching\n✓ Routers\n✓ Firewalls\n✓ Firewall principles\n✓ Remote Access\n\nPROFESSIONAL\n✓ ACESE RIM\n\n\n\n✓ MICROSOFT Certified\n\nADDITIONAL SKILLS\n✓ ADVANCED MS OFFICE (WORD, POWERPOINT, EXCEL, ETC.)\n✓ ADOBE PHOTOSHOP, READER X, ACROBAT, COREL DRAW, MICROMEDIA FLASH ETC.\n✓ JAVA NETBEANS, SQL, HTML, CMD PROMPT, ETC.\n✓ FAMILIAR WITH ALL SORTS OF WINDOW SETS AND WEB BROWSERS.\n✓ ALL KINDS OF UTILITY SOFTWARES AND HARDWARE OPTIONS TO INCREASE EFFICIENCY AND\nEFFECTIVENESS IN WORKING.\n✓ COMPLETE KNOWLEDGE OF MICROSOFT EXCHANGE SERVER 2012.\n✓ CAN DEVELOP WEBSITES BASED ON HTML, PHP ETC.\n✓ BASIC KNOWLEDGE OF ANDROID AND MAC APPLICATIONS.\n✓ COMPLETE KNOWLEDGE OF SERVER AND CLIENT BASED ENVIRONMENT\n✓ BASIC KNOWLEDGE OF MICROSOFT WINDOWS FIREWALL", "meta": {}, "annotation_approver": "admin", "labels": [[3673, 4270, "Skills"], [3121, 3334, "Skills"], [2989, 3086, "Skills"], [2949, 2979, "College Name"], [2906, 2936, "College Name"], [2885, 2904, "Degree"], [2857, 2884, "College Name"], [2811, 2855, "Degree"], [2804, 2810, "College Name"], [2774, 2803, "Degree"], [784, 789, "Companies worked at"], [53, 97, "Email Address"], [0, 13, "Name"], [14, 30, "Location"], [146, 170, "Skills"], [178, 198, "Skills"], [793, 817, "Location"], [821, 833, "Years of Experience"], [854, 935, "Skills"], [938, 954, "Skills"], [966, 980, "Skills"], [982, 1003, "Skills"], [1028, 1037, "Skills"], [1039, 1060, "Skills"], [1165, 1181, "Skills"], [1656, 1697, "Skills"], [1783, 1794, "Skills"], [1796, 1806, "Skills"], [1808, 1812, "Skills"], [1814, 1826, "Skills"], [1830, 1835, "Skills"], [1862, 1869, "Skills"], [1872, 1895, "Skills"], [1993, 1997, "Skills"], [1998, 2001, "Skills"], [2015, 2033, "Skills"], [2096, 2101, "Skills"], [2121, 2128, "Skills"], [2157, 2162, "Skills"], [2238, 2249, "Skills"]]}
