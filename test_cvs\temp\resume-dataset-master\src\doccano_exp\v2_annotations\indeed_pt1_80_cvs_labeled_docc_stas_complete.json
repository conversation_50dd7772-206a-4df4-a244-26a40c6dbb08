{"id": 1, "text": "<PERSON><PERSON>\nAssociate Software Engineer\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/<PERSON><PERSON>-<PERSON>/cb1ede9ee6d21bca\n\nWilling to relocate to: Bangalore Urban, Karnataka\n\nWORK EXPERIENCE\n\nAssociate Software Engineer\n\noracle\n\n• Associate Software Engineer in Oracle India Pvt Ltd. (through Trigent Software Pvt Ltd)\n• Experience in Oracle Hyperion Products and Automation Scripting. (September 2017 onwards)\nPerform automation through python and batch scripting.\nProvide application maintenance and client support.\n\nEDUCATION\n\nB.E. in ENC\n\nNorth Maharashtra University\n\n2017\n\nCertificate\n\nInstitute/Board\n\nSKILLS\n\nC++ (Less than 1 year), DATABASES (Less than 1 year), ECLIPSE (Less than 1 year), ESSBASE\n(Less than 1 year), HYPERION (Less than 1 year), python, java\n\nLINKS\n\nhttp://www.linkedin.com/in/puneet-singh-277a11151\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\n\n• Languages: Java, C, C++, Python, Batch\n• Databases: Oracle, Mysql\n• Ides: Eclipse, PyCharm\n• Operating systems: Windows and Linux\n• Dataware housing Tools: ODI, Hyperion Essbase and Planning, FDMEE\n\nhttps://www.indeed.com/r/Puneet-Singh/cb1ede9ee6d21bca?isid=rex-download&ikw=download-top&co=IN\nhttp://www.linkedin.com/in/puneet-singh-277a11151", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [536, 540, "Degree"], [544, 577, "College Name"], [579, 583, "Graduation Year"], [42, 62, "Location"], [13, 40, "Designation"], [237, 264, "Designation"], [299, 323, "Companies worked at"], [393, 415, "Years of Experience"], [198, 225, "Designation"], [227, 233, "Companies worked at"], [85, 127, "Email Address"], [153, 179, "Location"], [268, 288, "Companies worked at"], [341, 356, "Tech Tools"], [370, 390, "Job Specific Skills"], [444, 450, "Tech Tools"], [455, 470, "Tech Tools"], [623, 626, "Tech Tools"], [647, 656, "Job Specific Skills"], [677, 684, "Tech Tools"], [705, 712, "Tech Tools"], [733, 741, "Tech Tools"], [762, 768, "Tech Tools"], [770, 774, "Tech Tools"], [895, 896, "Tech Tools"], [889, 893, "Tech Tools"], [898, 901, "Tech Tools"], [903, 909, "Tech Tools"], [911, 916, "Tech Tools"], [919, 928, "Job Specific Skills"], [930, 936, "Tech Tools"], [938, 943, "Tech Tools"], [952, 959, "Tech Tools"], [961, 968, "Tech Tools"], [990, 997, "Tech Tools"], [1002, 1007, "Tech Tools"]]}
{"id": 2, "text": "Rahul Bollu\nSoftware Engineer - Disney\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Rahul-Bollu/dc40f5ce78045741\n\n• Over 3.5 years of experience in implementing organization DevOps strategy in various\nenvironments of Linux and windows servers along with adopting cloud strategies based on\nAmazon Web Services.\n• Experience in Cloud Technologies like Amazon Web Services (AWS) VPC, EC2, S3, ELB, IAM,\nAuto Scaling, Route 53, SQS, SNS, RDS, Cloud Watch, Dynamo DB.\n• Utilized Cloud Watch to monitor AWS resources to set alarms for notification, and to monitor\nlogs for a better operation of the system.\n• Experience working with automated build platforms/continuous integration using DevOps\narchitecture.\n• Implementing DevOps tools like Ansible as configuration management for Continuous\nIntegration and Continuous Deployment with build tools using Maven on Cloud Infrastructure\nusing AWS.\n• Experience on version control tool GIT- Creating branches, tracking changes, maintaining the\nhistory of code and helping the Developers in GIT related issues.\n• Worked on Jenkins for continuous integration and for End to End automation for all build and\ndeployments.\n• Worked with Ansible as a configuration management tool, created playbooks to automate\nrepetitive tasks, quickly deploy applications, and proactively manage change.\n• Knowledge on Docker.\n\nWORK EXPERIENCE\n\nSoftware Engineer\n\nDisney -\n\nSeptember 2014 to Present\n\nResponsibilities:\n* Coordinate/assist developers with establishing and applying appropriate branching, labeling/\nnaming conventions using GIT source control.\n* Implemented the setup for Master slave architecture to improve the performance of Jenkins.\n* Used Jenkins to implement Continuous Integration and deployment into Tomcat/Web logic\nApplication server.\n* Created Ansible playbooks for automating the Infrastructure, deployment process.\n* Managed clients, roles, tasks, playbooks in Ansible.\n* Deploy and monitor scalable infrastructure on AWS & configuration management.\n* Worked on making application more scalable and highly available in AWS.\n* Created AWS IAM roles & total architecture deployment end to end (creation of EC2 instances\n& its infrastructure)\n\nEnvironment: GIT, Maven, Jenkins, Tomcat, Docker, Jira, AWS, Ansible, LAMP\n\nSoftware Engineer\n\nhttps://www.indeed.com/r/Rahul-Bollu/dc40f5ce78045741?isid=rex-download&ikw=download-top&co=IN\n\n\nHCL Technologies -\n\nSeptember 2014 to Present\n\n〓 Coordinate/assist developers with establishing and applying appropriate branching, labeling/\nnaming conventions using GIT source control.\n〓 Implemented the setup for Master slave architecture to improve the performance of Jenkins.\n〓 Used Jenkins to implement Continuous Integration and deployment into Tomcat/Web logic\nApplication server.\n〓 Created Ansible playbooks for automating the Infrastructure, deployment process.\n〓 Managed clients, roles, tasks, playbooks in Ansible.\n〓 Deploy and monitor scalable infrastructure on AWS & configuration management.\n〓 Worked on making application more scalable and highly available in AWS.\n〓 Created AWS IAM roles & total architecture deployment end to end (creation of EC2 instances\n& its infrastructure).\n\nProcess Associate\n\nMicrosoft -\n\nJuly 2013 to August 2014\n\nResponsibilities:\n* Collect and document user requirements.\n* Design and develop database architecture for information systems projects.\n* Design, construct, modify, integrate, implement and test data models and database\nmanagement systems.\n* Conduct research and provide advice to other informatics professionals regarding the selection,\napplication and implementation of database management tools.\n* Operate database management systems to analyze data and perform data mining analysis.\n\nEDUCATION\n\nBachelor Of Science\n\nVaughn College of Aeronautics and Technology\n\nSKILLS\n\nAWS (3 years), Tomcat, Ansible, git, LAMP, docker, jenkins, Maven, Jira\n\nADDITIONAL INFORMATION\n\nTechnical Skills:\n\nCloud Technologies: AWS\n\nOperating Systems: Linux, Windows.\nVersion Control Systems: GIT\nAutomated Build Tools: Maven\nContinuous Integration: Jenkins\n\n\n\nScripting Languages: Shell Scripting\nConfiguration Management: Ansible.\nContainer service: Docker", "meta": {}, "annotation_approver": null, "labels": [[0, 11, "Name"], [12, 29, "Designation"], [1377, 1394, "Designation"], [1406, 1431, "Years of Experience"], [2277, 2294, "Designation"], [2393, 2409, "Companies worked at"], [2413, 2438, "Years of Experience"], [1396, 1402, "Companies worked at"], [3223, 3247, "Years of Experience"], [3191, 3208, "Designation"], [3210, 3219, "Companies worked at"], [3749, 3768, "Degree"], [32, 38, "Companies worked at"], [40, 60, "Location"], [83, 124, "Email Address"], [133, 142, "Years of Experience"], [3770, 3814, "College Name"], [186, 201, "Job Specific Skills"], [229, 234, "Tech Tools"], [239, 246, "Tech Tools"], [301, 320, "Job Specific Skills"], [338, 356, "Job Specific Skills"], [362, 387, "Tech Tools"], [388, 391, "Tech Tools"], [393, 396, "Tech Tools"], [398, 400, "Tech Tools"], [402, 405, "Tech Tools"], [407, 410, "Tech Tools"], [412, 424, "Tech Tools"], [426, 434, "Tech Tools"], [436, 439, "Tech Tools"], [441, 444, "Tech Tools"], [446, 449, "Tech Tools"], [451, 462, "Tech Tools"], [464, 473, "Tech Tools"], [486, 497, "Tech Tools"], [509, 512, "Tech Tools"], [694, 713, "Job Specific Skills"], [748, 755, "Tech Tools"], [730, 736, "Job Specific Skills"], [896, 899, "Tech Tools"], [860, 865, "Tech Tools"], [869, 889, "Job Specific Skills"], [938, 941, "Tech Tools"], [1074, 1081, "Tech Tools"], [1117, 1138, "Job Specific Skills"], [1184, 1191, "Tech Tools"], [815, 836, "Job Specific Skills"], [1571, 1574, "Tech Tools"], [1675, 1682, "Tech Tools"], [1691, 1698, "Tech Tools"], [1712, 1734, "Job Specific Skills"], [1755, 1761, "Tech Tools"], [1802, 1809, "Tech Tools"], [1978, 1981, "Tech Tools"], [2079, 2082, "Tech Tools"], [2094, 2097, "Tech Tools"], [2214, 2217, "Tech Tools"], [2219, 2224, "Tech Tools"], [2226, 2233, "Tech Tools"], [2235, 2241, "Tech Tools"], [2243, 2249, "Tech Tools"], [2251, 2255, "Tech Tools"], [2257, 2260, "Tech Tools"], [2262, 2269, "Tech Tools"], [2271, 2275, "Tech Tools"], [2560, 2563, "Tech Tools"], [2608, 2633, "Job Specific Skills"], [2664, 2671, "Tech Tools"], [2680, 2687, "Tech Tools"], [2744, 2750, "Tech Tools"], [2910, 2917, "Tech Tools"], [2967, 2970, "Tech Tools"], [3083, 3086, "Tech Tools"], [3445, 3456, "Job Specific Skills"], [3461, 3488, "Job Specific Skills"], [3659, 3687, "Job Specific Skills"], [3715, 3726, "Job Specific Skills"], [3824, 3827, "Tech Tools"], [3839, 3845, "Tech Tools"], [3847, 3854, "Tech Tools"], [3856, 3859, "Tech Tools"], [3861, 3865, "Tech Tools"], [3867, 3873, "Tech Tools"], [3875, 3882, "Tech Tools"], [3884, 3889, "Tech Tools"], [3891, 3895, "Tech Tools"], [3940, 3958, "Job Specific Skills"], [3960, 3963, "Tech Tools"], [3984, 3989, "Tech Tools"], [3991, 3998, "Tech Tools"], [4025, 4028, "Tech Tools"], [4052, 4057, "Tech Tools"], [4082, 4089, "Tech Tools"], [4114, 4129, "Tech Tools"], [4156, 4163, "Tech Tools"], [4184, 4190, "Tech Tools"]]}
{"id": 3, "text": "Rajeev Kumar\nSenior Associate Consultant - Infosys/Offshore Lead\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Rajeev-Kumar/3f560fd91275495b\n\n•An erudite professional with 2 years of IT/Presales/Project Management experience\n•Possess knowledge of Project Management, Digital/Social Media Marketing, Analytics, BI\n(Business Intelligence), presales, Business\ndevelopment, strategy and planning, etc.\n•Endowed with a passion for winning as exemplified through excellence in academic,\nextracurricular areas and organizational experience\n•An effective communicator with strong analytical/logical skills to relate to people at any level\nof business and management\n•Exposure in improving business performance by leveraging market insights to influence\nstrategic and tactical plans that delivered differentiated offerings to customers\n•Possess problem solving capability keeping constraints in purview, innovation &amp;\nadaptability\n•A quick learner with the expertise to work under pressure and meet deadlines\n•Pleasing personality with a zest for life, knowledge and sound understanding of technology and\nthe present world\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSenior Associate Consultant\n\nInfosys Limited -  Bangalore Urban, Karnataka -\n\nJuly 2017 to Present\n\n- Project Management Office (PMO)/Offshore Lead for a leading Global Oil &amp; Gas utility giant\n- Handling multiple service areas viz. risks and issues, planning and scheduling and assurance\nprocesses for all projects and engagements across the EMEA geography\n\nAssociate Consultant\n\nInfosys -  Pune, Maharashtra -\n\nMay 2016 to June 2017\n\n- Presales (Domain and Process Consulting) for Enterprise Asset Management (EAM) Supply Chain\nDomain\n- Pega Marketing and BPM (Business Process Management) team as a functional consultant\n- Problem Definition: Conduct secondary research that helps identify problem areas\n- Effort Estimation and preparing Deal Pricing with vendors to provide data points for proposal\ndevelopment\n- Solution Evaluation &amp; Recommendation: Understand solutions recommended and explore\nalternatives based on research (literature survey, information based in public domains etc.)\n-Requirement Analysis &amp; Detailing of Processes: Create requirement specifications from\nbusiness needs. Define detailed functional, process, infrastructure based on requirements\n\nhttps://www.indeed.com/r/Rajeev-Kumar/3f560fd91275495b?isid=rex-download&ikw=download-top&co=IN\n\n\n- Development/Configuration: Configure and build the application, process solution in line with\nthe design document\n- Issue Resolution: Understand the issue, diagnose the root cause and shortlist solution\nalternatives\n- Marketing &amp; Branding: Create marketing material in the form of case studies or solution\ndocumentation\n- Configure and assist in evaluating micro vertical/service/solution requirements on the products,\ndocumenting these in presentations and creating solution brochures\n\nEDUCATION\n\nPGDM in Marketing\n\nGoa Institute of Management -  Goa\n\nJune 2014 to March 2016\n\nB.Tech in Computer Science and Engineering\n\nUttar Pradesh Technical University -  Ghaziabad, Uttar Pradesh\n\nJuly 2010 to April 2014\n\nSKILLS\n\nAdobe Photoshop (Less than 1 year), Ads (Less than 1 year), BI (1 year), Business Intelligence\n(1 year), MS Excel (2 years), presales (1 year), Tableau (1 year), Pega Marketing and BPM (Less\nthan 1 year), MS Word (2 years), MS Powerpoint (2 years), MS Sharepoint (2 years), Spss (Less\nthan 1 year), IBM Maximo (Less than 1 year), marketing strategy (2 years), Business Analysis\n(2 years), communication and soft skills (2 years), Project Management (2 years), Project\nPlanning (2 years), Team Management (1 year)\n\nLINKS\n\nhttp://www.linkedin.com/in/rajeevkumar91\n\nCERTIFICATIONS/LICENSES\n\nGoogle Adwords\n\nDecember 2016 to December 2017\n\nGoogle Analytics\n\nFebruary 2017 to August 2018\n\nHubSpot Inbound Marketing\n\nDigital Vidya Certified Digital Marketer\n\nhttp://www.linkedin.com/in/rajeevkumar91\n\n\nADDITIONAL INFORMATION\n\nSoft Skills\n\nCommunicator, Innovator, Team Player, Analytical Collaborator. Intuitive\n\nTechnical Skills\n• Google AdWords, Google Analytics, Inbound Marketing, SEO (On-page and Off- page), SEM,\nFacebook Ads and Social\n•Project Management, MS Project Professional\nMedia Campaigns (Content Bucketing)\n• Tableau, Microsoft Power BI, SPSS\n• MS Office Suite, Advanced Excel, MS SharePoint, Visio, Adobe Photoshop, Pega Marketing and\nBPM", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [13, 40, "Designation"], [183, 190, "Years of Experience"], [1177, 1204, "Designation"], [1206, 1221, "Companies worked at"], [1225, 1251, "Location"], [1255, 1275, "Years of Experience"], [1279, 1324, "Designation"], [1572, 1589, "Location"], [1593, 1614, "Years of Experience"], [66, 86, "Location"], [109, 151, "Email Address"], [2961, 2978, "Degree"], [2980, 3007, "College Name"], [3041, 3083, "Degree"], [3085, 3119, "College Name"], [3123, 3147, "Location"], [43, 64, "Companies worked at"], [197, 205, "Job Specific Skills"], [206, 224, "Job Specific Skills"], [258, 276, "Job Specific Skills"], [278, 308, "Job Specific Skills"], [310, 319, "Soft Skills"], [321, 347, "Job Specific Skills"], [349, 357, "Job Specific Skills"], [359, 379, "Job Specific Skills"], [381, 402, "Job Specific Skills"], [847, 862, "Soft Skills"], [906, 916, "Soft Skills"], [939, 952, "Soft Skills"], [1539, 1559, "Designation"], [1561, 1568, "Companies worked at"], [1737, 1771, "Job Specific Skills"], [2178, 2198, "Job Specific Skills"], [2459, 2484, "Job Specific Skills"], [2575, 2591, "Job Specific Skills"], [2677, 2686, "Job Specific Skills"], [2693, 2701, "Job Specific Skills"], [3011, 3014, "Location"], [3016, 3039, "Graduation Year"], [3182, 3197, "Tech Tools"], [3218, 3221, "Tech Tools"], [3242, 3244, "Tech Tools"], [3255, 3276, "Job Specific Skills"], [3307, 3315, "Job Specific Skills"], [3344, 3358, "Job Specific Skills"], [3363, 3366, "Job Specific Skills"], [3387, 3394, "Tech Tools"], [3287, 3295, "Tech Tools"], [3326, 3333, "Tech Tools"], [3406, 3419, "Tech Tools"], [3431, 3444, "Tech Tools"], [3456, 3460, "Tech Tools"], [3481, 3491, "Tech Tools"], [3542, 3559, "Job Specific Skills"], [3512, 3530, "Job Specific Skills"], [3571, 3584, "Soft Skills"], [3612, 3630, "Job Specific Skills"], [3642, 3658, "Job Specific Skills"], [3670, 3685, "Job Specific Skills"], [3770, 3784, "Tech Tools"], [3818, 3834, "Tech Tools"], [3907, 3933, "Degree"], [4015, 4027, "Soft Skills"], [4029, 4038, "Soft Skills"], [4040, 4051, "Soft Skills"], [4053, 4063, "Soft Skills"], [4108, 4122, "Tech Tools"], [4124, 4140, "Tech Tools"], [4142, 4159, "Tech Tools"], [4161, 4164, "Job Specific Skills"], [4190, 4193, "Job Specific Skills"], [4195, 4207, "Tech Tools"], [4220, 4238, "Job Specific Skills"], [4240, 4250, "Tech Tools"], [4302, 4309, "Tech Tools"], [4311, 4329, "Tech Tools"], [4331, 4335, "Tech Tools"], [4338, 4353, "Tech Tools"], [4364, 4369, "Tech Tools"], [4371, 4384, "Tech Tools"], [4386, 4391, "Tech Tools"], [4393, 4408, "Tech Tools"], [4410, 4424, "Job Specific Skills"], [4429, 4432, "Job Specific Skills"]]}
{"id": 4, "text": "Ram Edupuganti\nSoftware Development Director - Oracle Inc\n\n- Email me on Indeed: indeed.com/r/Ram-Edupuganti/3ecdecbcba549e21\n\n• Offering over 22 years of rich national & international experience in developing & deploying\nsoftware appplicatoins development, customized solutions across Oracle Fusion HCM Global\nCore HR, Global Payroll, Global Absences, Compensation and all Talent modules - Performance\nManagement, Goals Management, Career Development, Succession Management & Talent\nReview Meeting\n• Directed planning, strategy development and implementation & business solution delivery with\nrecent successful implantations at Schneider Electric, Macy's, Pike International, British Telcom.\n• Accomplished in relational data modeling, data warehouse design and implementation, object\noriented design and development, developing transactional & business analytics in CRM, SCM\nand HCM domains from client server to cloud SaaS.\n• Led large-scale business application architecture and design efforts; developed and maintained\nsolutions for various business functional areas; assisted in resolving integration and interface\nissues between various systems with focus on optimizing performance and scalability\n• Extensive experience in all aspects of project management including budgeting and cost\noptimization, risk assessments and control, technical feasibility studies, project scope definition,\nestimations & cost control & so on\n• Recruit, Develop and led high-performing teams with high motivation and enhanced cross-\nfunctional collaboration\n\nWORK EXPERIENCE\n\nSoftware Development Director\n\nOracle Inc -\n\nApril 2015 to Present\n\nOracle Inc -\n\nJanuary 1998 to Present\n\n98 with Oracle Inc.\nGrowth Path & Deputations:\n\nSenior Development Manager\n\nOracle Inc -\n\nDecember 2011 to March 2015\n\nSenior Development Manager\n\nOracle Inc -\n\nMarch 2010 to November 2011\n\nhttps://www.indeed.com/r/Ram-Edupuganti/3ecdecbcba549e21?isid=rex-download&ikw=download-top&co=IN\n\n\nPrincipal Applications Developer\n\nOracle Inc -\n\nSeptember 2004 to February 2010\n\nProject Lead\n\nOracle Inc -\n\nSeptember 1999 to August 2004\n\nSenior Software Developer\n\nOracle Inc -\n\nJanuary 1998 to August 1999\n\nR-Systems -  Sacramento, CA -\n\nJanuary 1997 to January 1998\n\nKey Result Areas:\n• Providing overall leadership to the entire project team, mapping clients' requirements,\ntransforming requirements into stipulations and providing them best solutions within the scope\nof project requirements\n• Creating and managing the estimates, project plan, project schedule, resource allocation and\nexpenses; monitoring and reporting on standards & performance targets\n• Ensuring the delivery of quality releases on schedules using continuous improvement\ninitiatives; adhering to quality norms throughout the project implementation\n• Working on service strategy, transition, operations, process improvement, process\nmanagement, team building, training, hiring and client relationship management\n• Identifying and implementing strategies for building team effectiveness by promoting a spirit\nof cooperation among the team members\n• Supporting continuous improvement by investigating alternatives and new technologies and\npresenting the same for architectural review\n• Liaising with stakeholders during the course of problem diagnoses, requirements gathering,\ndetailed level design, development, system test and production implementation to ensure that\noptimal resolutions are achieved\n\nKNOWLEDGE PURVIEW\n• Machine Learning Algorithms supervised, unsupervised, NLP, chat bot and deep learning with\nPython\n• Building transactional applications & analytical solutions in SaaS model\n• Oracle fusion role based data security set up and customization for complex security\nrequirements\n• Reporting Layer (Subject Areas) for fusion HCM cloud for all HCM modules\n• Data Warehousing design methodologies, star and snowflake schema designs, aggregations\n• OBIEE, Data Visualization Desktop, BICS, DVCS, Oracle Analytics Cloud (OAC), Tableau\n• Design and build dashboards, KPIs using Oracle BI platform and OTBI, Standard Reports using\nBI Publisher\n• Design and build mappings, knowledge modules, load plans using Oracle Data Integrator (ODI)\n\n\n\n• Relational data modeling, Object Oriented Modeling & Design, UML, SOAP\n• Oracle 10g, 11g, 12c\n• SQL, PL/SQL, HTML, XML, JAVA, JDK, J2EE, Oracle ADF, Oracle JDeveloper\n• Applications Development for Oracle CRM, Oracle ebusiness SCM,\n• Applications/database/SQL/Batch program performance tuning\n• UNIX, Windows, Linux\n\nR-Systems -\n\nApril 1996 to January 1998\n\nGrowth Path & Deputations:\n\nDepartment of Corrections -  Sacramento, CA -\n\nFebruary 1996 to January 1997\n\nSecretary of State\n\nSalem, Tamil Nadu -\n\nDecember 1995 to February 1996\n\nOracle Corporation -  Redwood Shores, CA -\n\nJanuary 1995 to December 1995\n\nEDUCATION\n\nM.S. in Computer Engineering\n\nTexas A&M University -  Kingsville, TX\n\n1994\n\nB.S. in Electronics & Communications Engineering\n\nNagarjuna University\n\n1992\n\nSKILLS\n\nSOFTWARE DEVELOPMENT (3 years), STRUCTURED SOFTWARE (3 years), BUSINESS\nINTELLIGENCE (1 year), ORACLE (1 year), ARCHITECTURE (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nCORE COMPETENCIES\n\nSoftware Development & Consulting\n\nOracle Fusion Applications Architecture and Functionality\n\n\n\nOracle Business Intelligence development and implementations\n\nTechnology Planning\n\nDelivery Management\n\nClient Engagements (Stakeholders/Business)\n\nContinuous Process Enhancement\n\nAgile/Scrum Methodologies\n\nProject Management", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [15, 44, "Designation"], [47, 57, "Companies worked at"], [81, 125, "Email Address"], [138, 151, "Years of Experience"], [1563, 1592, "Designation"], [1594, 1604, "Companies worked at"], [1608, 1629, "Years of Experience"], [1631, 1641, "Companies worked at"], [1645, 1668, "Years of Experience"], [1678, 1688, "Companies worked at"], [1718, 1744, "Designation"], [1746, 1756, "Companies worked at"], [1760, 1787, "Years of Experience"], [1789, 1815, "Designation"], [1817, 1827, "Companies worked at"], [1831, 1858, "Years of Experience"], [1960, 1992, "Designation"], [1994, 2004, "Companies worked at"], [2008, 2039, "Years of Experience"], [2041, 2053, "Designation"], [2055, 2065, "Companies worked at"], [2069, 2098, "Years of Experience"], [2100, 2125, "Designation"], [2127, 2137, "Companies worked at"], [2141, 2168, "Years of Experience"], [2170, 2179, "Companies worked at"], [2183, 2197, "Location"], [2201, 2229, "Years of Experience"], [4519, 4545, "Years of Experience"], [4604, 4618, "Location"], [4622, 4651, "Years of Experience"], [4653, 4662, "Designation"], [4680, 4690, "Location"], [4666, 4678, "Companies worked at"], [4694, 4724, "Years of Experience"], [4726, 4744, "Companies worked at"], [4748, 4766, "Location"], [4770, 4799, "Years of Experience"], [4842, 4862, "College Name"], [4866, 4880, "Location"], [4882, 4887, "Graduation Year"], [4888, 4936, "Degree"], [4812, 4840, "Degree"], [4938, 4958, "College Name"], [4960, 4965, "Graduation Year"], [199, 244, "Job Specific Skills"], [286, 299, "Tech Tools"], [391, 413, "Job Specific Skills"], [415, 431, "Job Specific Skills"], [433, 451, "Job Specific Skills"], [453, 474, "Job Specific Skills"], [477, 490, "Job Specific Skills"], [501, 518, "Job Specific Skills"], [711, 735, "Job Specific Skills"], [737, 758, "Job Specific Skills"], [779, 801, "Job Specific Skills"], [846, 864, "Job Specific Skills"], [868, 871, "Job Specific Skills"], [915, 925, "Job Specific Skills"], [945, 978, "Job Specific Skills"], [1246, 1264, "Job Specific Skills"], [2269, 2279, "Soft Skills"], [2799, 2815, "Job Specific Skills"], [2862, 2880, "Job Specific Skills"], [2882, 2895, "Soft Skills"], [2897, 2905, "Soft Skills"], [2918, 2949, "Job Specific Skills"], [3459, 3486, "Job Specific Skills"], [3487, 3497, "Job Specific Skills"], [3499, 3511, "Job Specific Skills"], [3513, 3516, "Job Specific Skills"], [3531, 3544, "Job Specific Skills"], [3550, 3556, "Tech Tools"], [3568, 3594, "Job Specific Skills"], [3621, 3632, "Job Specific Skills"], [3809, 3825, "Job Specific Skills"], [3945, 3973, "Tech Tools"], [3975, 3982, "Tech Tools"], [4025, 4034, "Tech Tools"], [4077, 4089, "Tech Tools"], [4155, 4183, "Tech Tools"], [4215, 4239, "Job Specific Skills"], [4255, 4259, "Tech Tools"], [4250, 4253, "Tech Tools"], [4262, 4272, "Tech Tools"], [4285, 4288, "Tech Tools"], [4290, 4296, "Tech Tools"], [4298, 4302, "Tech Tools"], [4304, 4307, "Tech Tools"], [4309, 4313, "Tech Tools"], [4315, 4318, "Tech Tools"], [4320, 4324, "Tech Tools"], [4326, 4336, "Tech Tools"], [4338, 4355, "Tech Tools"], [4387, 4397, "Tech Tools"], [4399, 4415, "Tech Tools"], [4484, 4488, "Tech Tools"], [4490, 4497, "Tech Tools"], [4499, 4504, "Tech Tools"], [4966, 4994, "Soft Skills"], [5006, 5025, "Soft Skills"], [5037, 5058, "Soft Skills"], [5069, 5075, "Tech Tools"], [5086, 5098, "Soft Skills"], [5162, 5182, "Soft Skills"], [5197, 5203, "Tech Tools"], [5258, 5286, "Job Specific Skills"], [5320, 5339, "Job Specific Skills"], [5341, 5360, "Job Specific Skills"], [5362, 5380, "Job Specific Skills"], [5406, 5436, "Job Specific Skills"], [5438, 5463, "Job Specific Skills"], [5465, 5483, "Job Specific Skills"]]}
{"id": 5, "text": "Ramesh HP\nCES ASSOCIATE CONSULTANT\n\nBangalore, Karnataka - Email me on Indeed: indeed.com/r/Ramesh-HP/95fc615713630c4e\n\n• 4 years of experience in engineering Technology software sales and strategic sourcing in B2B\nplatform.\n• Proven track record of generating increased revenue by involving in professional sales\nstrategies.\n• Responsible for software installation, network configuration, application integration with\nexisting system and Technical support.\n• Effectively work with cross functional teams to deliver right solutions to client's requirements.\n• Complete involvement in client meetings with respect to requirement collection, suggesting\nsolutions and financial negotiations.\n• Good experience in account management, having a track record of generating repeated\nbusiness.\n• Responsible for report generation with respect qualified leads and expected commitments in\nclosing deals.\n• Worked on a multiple market sector, responsibility to manage sector wise market analysis and\ndrive business parallely.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nCES ASSOCIATE CONSULTANT\n\nSAP ARIBA -\n\nNovember 2016 to October 2017\n\n• Responsible for supplier management via Ariba Discovery Which is B2B consulting platform.\n• Strategic sourcing of supplier corresponding to the buyer's products & service based\ncommodities globally.\n• Effectively analyse and conduct commodity research on project description from the buyer's\npostings.\n• Effective handling of multiple projects and converting potential leads into revenue for Ariba.\n• Having track record of maintaining 100% revenue target by monthly & quarterly.\n\nSALES ENGINEER - CONCEPT TECHNOLOGY SOLUTION\n\n-\n\nJanuary 2014 to November 2016\n\n• Effective selling of CAD, CAM & Analysis Software's. Which has got multiple market sector.\n• Generating qualified leads by sourcing market, sector wise & implementing sales action plans.\n• Giving presentation about the company, products & service offers.\n• Identifying the client requirements, Plan for proposing a solution by integrating with internal\ntechnical team.\n• Provide value addition to the prospect by involving my superiors with client management team.\n\nhttps://www.indeed.com/r/Ramesh-HP/95fc615713630c4e?isid=rex-download&ikw=download-top&co=IN\n\n\n• Responsible to make negotiations and up closing the deals.\n• Provide technical support after sales and maintain healthy relationship with the prospect.\n• Responsible to achieve targets monthly, quarterly & annually.\n\nEDUCATION\n\nMCA in COMPUTER APPLICATION\n\nDayananda Sagar College of Engineering -  Bengaluru, Karnataka\n\nBACHELOR OF SCIENCE in Electronics\n\nGovt Science College -  Hassan, Karnataka\n\nSKILLS\n\nLead genearation, Customer Handling, cold calling, Negotiation, upselling, IT sales, outbound\ncalling, Technical Support, sales forcasting, Software sale, product demonstration, cross selling,\nInside Sales, Technical sales, MS office, software integration, Network Management\n\nADDITIONAL INFORMATION\n\n• Excellent Communication both verbal & written MS-Office\n• Sales Forecasting SAP Business Objective Tool\n• Strategic Prospecting Ariba Network Admin Tool\n• Product Knowledge Ariba B2B Cloud Platform\n• Social Networking\n• Negotiation\n• Customer Relationship Management\n• Technical Support", "meta": {}, "annotation_approver": null, "labels": [[10, 34, "Designation"], [36, 56, "Location"], [79, 118, "Email Address"], [122, 129, "Years of Experience"], [1063, 1087, "Designation"], [1089, 1098, "Companies worked at"], [1102, 1131, "Years of Experience"], [1616, 1630, "Designation"], [1633, 1660, "Companies worked at"], [1665, 1694, "Years of Experience"], [2489, 2516, "Degree"], [2518, 2556, "College Name"], [2560, 2580, "Location"], [2582, 2616, "Degree"], [2618, 2638, "College Name"], [2642, 2659, "Location"], [0, 9, "Name"], [159, 184, "Job Specific Skills"], [211, 223, "Job Specific Skills"], [308, 324, "Job Specific Skills"], [344, 365, "Job Specific Skills"], [367, 388, "Job Specific Skills"], [439, 456, "Job Specific Skills"], [710, 728, "Job Specific Skills"], [1175, 1180, "Tech Tools"], [1348, 1355, "Soft Skills"], [1378, 1386, "Soft Skills"], [1527, 1532, "Tech Tools"], [1719, 1722, "Tech Tools"], [1724, 1727, "Tech Tools"], [2330, 2347, "Job Specific Skills"], [2669, 2685, "Job Specific Skills"], [2687, 2704, "Job Specific Skills"], [2720, 2731, "Soft Skills"], [2733, 2742, "Job Specific Skills"], [2744, 2752, "Job Specific Skills"], [2772, 2789, "Job Specific Skills"], [2791, 2807, "Job Specific Skills"], [2809, 2822, "Job Specific Skills"], [2824, 2845, "Job Specific Skills"], [2847, 2860, "Soft Skills"], [2862, 2874, "Soft Skills"], [2876, 2891, "Job Specific Skills"], [2893, 2902, "Tech Tools"], [2904, 2924, "Job Specific Skills"], [2926, 2944, "Job Specific Skills"], [2982, 2995, "Soft Skills"], [3001, 3007, "Soft Skills"], [3010, 3017, "Soft Skills"], [3018, 3027, "Tech Tools"], [3030, 3047, "Job Specific Skills"], [3048, 3075, "Tech Tools"], [3078, 3100, "Job Specific Skills"], [3100, 3105, "Tech Tools"], [3145, 3150, "Tech Tools"], [3155, 3169, "Job Specific Skills"], [3172, 3189, "Job Specific Skills"], [3192, 3203, "Soft Skills"], [3206, 3238, "Job Specific Skills"], [3241, 3258, "Job Specific Skills"]]}
{"id": 6, "text": "Ramya. P\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Ramya-P/00f125c7b9b95a35\n\n( 2 year Experience)\n• Working currently with Accenture services Pune as a Software Test Engineer with Client\nAccenture on British Telecom Open reach Project, using Agile Methodologies.\n• Experience in automating provision journey in QTP (UFT)\n• Good understanding of the project with deep domain knowledge of Telecom with respect to\nProvisioning and Assurance.\n• Expertise in functional, CST, Integration and E2E testing on CRM application for the BT\nAssurance journey with respect to different products.\n• Expertise in understanding of the new requirements and developing test cases for new features\nbeing implemented in the code base and executing them.\n• Good in understanding of the change with involvement in regular solution design discussions,\ntechnical changes discussions with Solution Designers and Stake holders accordingly.\n• Proficient with the SDLC, STLC and Defect life cycle.\n• Excellent communication skills with proficiency at grasping new technical concepts quickly and\nutilize the same in a productive manner.\n• Mentored new joiners on the Overall view of the project, Features and Functionality of Testing.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSoftware Engineer\n\nAccenture -  Hyderabad, Telangana -\n\nDecember 2015 to Present\n\nBT Openreach (British Telecom)\n\nBT Openreach is responsible for looking after the local network (connection from your property\nto local exchange) that makes the running of telecommunication services possible. BT helps to\nprovide lines for telephone connection. It has products like PSTN, Ethernet, NGA, and LLU. BT acts\nas an intermediate between customer and connection provider.\nBT Openreach is a software module which manages BT business right from the placement of the\norder, managing appointments, monitoring status to order completion.\n\nRole and Responsibilities:\n\n• Performing a Sanity test on the application and working on stabilizing the test environment for\nsmooth testing activities everyday SOP.\n• Assigning the task and updating the reports to management by collating the data of each\nindividual in the team.\n• Testing the application with respect to CST, Integration, Functional, E2E and Regression testing.\n• Writing of Test Cases and execution based on the inputs such as customer requirements/test\nplan/test approach/ test techniques/ test strategies given based on the functionalities using Agile\nMethodologies.\n\nhttps://www.indeed.com/r/Ramya-P/00f125c7b9b95a35?isid=rex-download&ikw=download-top&co=IN\n\n\n• Execution of test cases and bug reporting in Quality center\n• Coordinated with developers to get the bugs reproduced and resolved\n• Reviewing the release and build notes documents.\n\n• Carry out regression testing every time when changes are made to the code to fix defects.\n• Regular involvement in RCA calls for the defects raised on daily basis and attending Scrum calls\nfor updating the status of the stack present in a given timelines.\n\nLiberty global international (LGI):\n\nPresently working as a application support Engineer for LGI.\n\nRoles and Responsibilities:\n\nDaily monitoring servers heath check .\nSolving the incident using tools like nagious , splunk,graphana.\n\nAcheviments:\nReceived appreciation excuting high number of test cases with in the given time\nReceived appreciation from Client for finding show stopper defects.\nReceived appreciate for identifying P1 issue within the SLA\n\nEDUCATION\n\nBSc in Computer Science\n\nvasundhara womens\n\n2015\n\nnarayana jr.college\n\n2012\n\nSSC\n\npragathi high school\n\n2010\n\nSKILLS\n\nBlack Box (2 years), Black Box Testing (2 years), Database (Less than 1 year), java (Less than\n1 year), logging (Less than 1 year), SQL (Less than 1 year), Nagious (Less than 1 year), Splunk\n(Less than 1 year), Manual Testing (2 years), SDLC&STLC (2 years)\n\nADDITIONAL INFORMATION\n\nOperating Systems: Windows,\nLanguages: Core java\nTest Management Tool: HP Quality Center, HP ALM\n\n\n\nDatabase mySQL\nDefect Management Tools: AIT ( Accenture Intelligent Testing), QC\n\nProfessional Skills:\n\n• Proficient in overall Manual testing concepts.\n• Proficient in Functional E2E Testing, Black Box Testing, Regression Testing.\n• Good at analyzing the requirements and preparing assessments for the new requirements.\n• Expertise in Development of test cases for the new features being implemented using different\ntest design techniques. This included Analysis of the Feature Documents and developing a\nstrategy for testing the feature.\n• Good in defect logging and tracking activities till the closure of a defect using tools QC and AIT.", "meta": {}, "annotation_approver": null, "labels": [[0, 8, "Name"], [9, 29, "Location"], [52, 89, "Email Address"], [166, 188, "Designation"], [214, 234, "Companies worked at"], [1269, 1286, "Designation"], [1288, 1297, "Companies worked at"], [1301, 1321, "Location"], [1325, 1349, "Years of Experience"], [1351, 1363, "Companies worked at"], [3485, 3508, "Degree"], [3510, 3527, "College Name"], [3529, 3533, "Graduation Year"], [3535, 3554, "College Name"], [3556, 3561, "Graduation Year"], [137, 155, "Companies worked at"], [156, 160, "Location"], [256, 275, "Job Specific Skills"], [293, 321, "Job Specific Skills"], [325, 334, "Tech Tools"], [501, 512, "Job Specific Skills"], [516, 519, "Job Specific Skills"], [950, 954, "Job Specific Skills"], [956, 960, "Job Specific Skills"], [965, 982, "Job Specific Skills"], [996, 1009, "Soft Skills"], [2246, 2272, "Job Specific Skills"], [2276, 2297, "Job Specific Skills"], [2592, 2601, "Job Specific Skills"], [2606, 2619, "Job Specific Skills"], [2623, 2637, "Tech Tools"], [3224, 3231, "Tech Tools"], [3234, 3240, "Tech Tools"], [3241, 3249, "Tech Tools"], [3603, 3612, "Tech Tools"], [3624, 3641, "Job Specific Skills"], [3653, 3661, "Job Specific Skills"], [3682, 3686, "Tech Tools"], [3707, 3714, "Job Specific Skills"], [3735, 3738, "Tech Tools"], [3759, 3766, "Tech Tools"], [3787, 3793, "Tech Tools"], [3814, 3828, "Soft Skills"], [3840, 3849, "Soft Skills"], [3904, 3911, "Tech Tools"], [3924, 3933, "Tech Tools"], [3956, 3973, "Tech Tools"], [3975, 3981, "Tech Tools"], [3994, 3999, "Tech Tools"], [4025, 4061, "Tech Tools"], [4063, 4065, "Tech Tools"], [4165, 4176, "Tech Tools"], [4178, 4195, "Job Specific Skills"], [4197, 4215, "Job Specific Skills"], [4615, 4617, "Tech Tools"], [4622, 4625, "Tech Tools"]]}
{"id": 7, "text": "R Arunravi\nFunctional Consultant / WM Lead - SAP EWM\n\nBangalore, Karnataka - Email me on Indeed: indeed.com/r/R-Arunravi/0da1137537d8b159\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nFunctional Consultant / WM Lead\n\nSAP EWM -\n\nDecember 2015 to Present\n\nCATERPILLAR]\n\nSAP EWM WM Analyst\n\nCATERPILLAR -\n\nOctober 2013 to December 2015\n\nMagna Info Tech]\nProject 1: Implementation & Support of EWM\nProject 2: Johannesburg Distribution Center - WM/SD/MM Support\n• Involved in Business requirement Gathering for setting up a new warehouse. Understanding\nthe AS-IS Business Process of the current distribution facility by Interacting with the Key business\nusers.\n• Prepared Blue print of the new warehouse facility by understanding and Mapping the Business\nProcess to the SAP EWM module.\n• Involved in configuration and maintenance of goods issue processes, Storage type search\nstrategies for Put away and picking based on the Business design for receiving and delivery\nprocess.\n• Configured the Stock Removal Control Indicators, Storage type sequences, Storage Section\nIndicators, Bin Type Indicators and storage process steps.\n• Training the users for carrying out their warehouse activities and using the warehouse monitor\nto view outbound and inbound transaction results.\n• Involved in WM RF screen developments and configurations.\n• Identify and debug defects in BO report - 'TU Zero Weight' and Emergency Charge Waiving\nissue and Worked on Partial Invoicing change.\n• Worked on a CR to create unique pack with number for URG Orders, based on Dealer PO.\n• Set up new storage types that were extended as a part of Rearrangement process in JDC\nwarehouse.\n• Provide Post-Go Live Incident support and involved in both technical & functional changes.\n• Delivered 13 CRs for the year 2015 and 6 CRs till June 2016.\n\nWM Functional Consultant / Visual PLM Consultant / Lead Technical\n\nSAP EWM -\n\nhttps://www.indeed.com/r/R-Arunravi/0da1137537d8b159?isid=rex-download&ikw=download-top&co=IN\n\n\nFebruary 2011 to October 2013\n\nClientis S3G - India\n\nProject 1: Implementation of Kair IT EWM / PLM\nProject 2: Bauer Hockey Corp - WM Support\n• Collaborated with Warehouse internal team members to understand the Business process in\ninbound and outbound logistics in India and Dubai, Gap analysis and design & finalization of\nblueprints.\n• Involved in configuration of ERP-EWM integration - ALE setting, ERP settings and EWM settings.\n• Involved in configuration and maintenance of goods issue processes and participated in End\nuser training.\n• Developed and configured Merchandise Plan, Timeline Management as per client needs.\n• Integrated VPLM system with SAP R/3 system, mapped fields for use in Tech Pack report, BOM,\nand Production Cost details.\n• Prepared Functional / Technical specs and involved extensively in Unit Testing and Integration\nTesting.\n• Involved in Supporting WM module on both functional and technical issues.\n\nConsultant / ABAP Consultant\n\nSAP WM -\n\nOctober 2007 to February 2011\n\nVoletix IT Solutions - India\n\nProject 1: Momentive Project Implementation - ABAP Support\nProject 2: ECCI - WM Implementation\n• Developed Smart Forms for Transfer Order and Pay slips.\n• Updated VAT, Service Tax data via customization of Sales Order and Invoice SAP scripts.\n• Created reports for validation of Bill of Material, and others outlining billing details in ALV format.\n• Designed Smart Forms layout representing Invoices and Delivery Notes.\n• Uploaded vendor and customer data through call transaction method.\n• Configured the Storage Type Indicators, Bin Type Indicators, Bin structure and stock\ndetermination sequence.\n• Prepared Functional / Technical specifications and involved extensively in Unit Testing and\nIntegration Testing.\n• Involved in Supporting WM module on both functional and technical issues.\n\nEDUCATION\n\nMaster of Engineering in Computer and Communication\n\nAnna University\n\n2005 to 2007\n\nBachelor of Technology in Information Technology\n\nAnna University\n\n\n\n2001 to 2005\n\nADDITIONAL INFORMATION\n\nProfessional competencies\nBusiness /IT Consulting Configuration/ Support Requirements/ Blue print /Gap Analysis\nCoding/Debugging Change Management Functional/ Technical Spec Design\n\nComputer skills\n\n• SAP Knowledge: SAP EWM, SAP WM, SAP SD, SAP MM, SAP AFS, SAP PP, SAP XI\n• Computing languages: SQL, MS-Office, Open office, HTML, PHP", "meta": {}, "annotation_approver": null, "labels": [[0, 10, "Name"], [11, 32, "Designation"], [35, 42, "Designation"], [45, 52, "Companies worked at"], [54, 74, "Location"], [97, 137, "Email Address"], [187, 208, "Designation"], [211, 218, "Designation"], [220, 227, "Companies worked at"], [231, 255, "Years of Experience"], [257, 268, "Companies worked at"], [291, 302, "Companies worked at"], [306, 335, "Years of Experience"], [271, 289, "Designation"], [337, 352, "Companies worked at"], [443, 459, "Designation"], [1766, 1783, "Years of Experience"], [1794, 1808, "Years of Experience"], [1811, 1835, "Designation"], [1838, 1859, "Designation"], [1862, 1885, "Designation"], [1985, 2014, "Years of Experience"], [2959, 2988, "Years of Experience"], [2990, 3010, "Companies worked at"], [3824, 3845, "Degree"], [3849, 3892, "College Name"], [3908, 3930, "Degree"], [3934, 3973, "College Name"], [2096, 2113, "Companies worked at"], [474, 494, "Job Specific Skills"], [1127, 1135, "Soft Skills"], [3250, 3253, "Tech Tools"], [3977, 3989, "Graduation Year"], [3894, 3906, "Graduation Year"], [4051, 4064, "Job Specific Skills"], [4065, 4087, "Job Specific Skills"], [4088, 4100, "Job Specific Skills"], [4114, 4126, "Job Specific Skills"], [4127, 4143, "Job Specific Skills"], [4144, 4161, "Job Specific Skills"], [4216, 4219, "Tech Tools"], [4231, 4238, "Tech Tools"], [4240, 4246, "Tech Tools"], [4248, 4254, "Tech Tools"], [4256, 4262, "Tech Tools"], [4264, 4271, "Tech Tools"], [4273, 4279, "Tech Tools"], [4281, 4287, "Tech Tools"], [4311, 4314, "Tech Tools"], [4316, 4325, "Tech Tools"], [4327, 4338, "Tech Tools"], [4340, 4344, "Tech Tools"], [4346, 4349, "Tech Tools"]]}
{"id": 8, "text": "Ravi Shankar\nWorking as Escalation Engineer with Microsoft.\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Ravi-Shankar/befa180dc0449299\n\n• Working in Microsoft - EPS (SCS-MON) at Convergys India Private Ltd., Pune from 5th October,\n2015 till date.\n• Currently working as an Escalation Engineer with SCS-MON team.\n• Done B.E. from Padmshree Dr. D.Y.Patil Institute of Engineering and Technology, Pimpri, Pune\nUniversity.\n\nWilling to relocate to: Pune, Maharashtra - hyderbad, Telangana - Bangalore, Karnataka\n\nWORK EXPERIENCE\n\nEscalation Engineer\n\nMicrosoft\n\nWorking on escalated issues with System Centre Operations Manager, Service Manager and\nOrchestrator. Have trained batches for the same. Supported junior Engineers in resolving tough\nissues.\n\nHave worked as a Support Engineer\n\nMicrosoft -  Pune, Maharashtra -\n\nOctober 2015 to April 2017\n\nsince May 2017 as a Subject Matter Expert to Jan 2018 and as Escalation Engineer since Feb\n2018 at Convergys India Pvt. Ltd. Pune for MICROSOFT Enterprise Support Team (SCS-MON)\n\nRoles and responsibilities: -\n• Working in Microsoft Commercial Technical Support SCS-MON Team providing advanced\ntechnical support by handling escalated or complex customer issues.\n• Identify, investigate, research and provide resolution on Customer queries and problems related\nto System Center Products.\n• Provide subject matter expertise in area of assignment and serve as a resource to other support\npersonnel. Maintain high level of customer satisfaction always with an eye on productivity.\n• Maintain a high level of technical product knowledge in the specified software/hardware\nproducts and become knowledgeable in new products when deployed.\n• Have supported SCOM, SCSM and Orchestrator platform for Microsoft customers.\n• My work includes helping the Engineers with Technical issues on System Center platform\nsupport.\n\nCore Competencies: -\nSystem Center Operations Manager:\n• Installation, deployment and troubleshooting of System Center Operations Manager\n• Cross-domain and Cross platform monitoring.\n• Network devices and Web Application monitoring\n\nhttps://www.indeed.com/r/Ravi-Shankar/befa180dc0449299?isid=rex-download&ikw=download-top&co=IN\n\n\n• Custom reports / Issue with blank reports\n• Network Devices monitoring\n• Integrating and monitoring other System Center Products such as SCCM, SCDPM, SCVMM etc.\nwith SCOM\n• Notifications\n\nSystem Center Orchestrator:\n• Installation, deployment and troubleshooting of System Center Orchestrator\n• Deployment of various Microsoft and Non-Microsoft Integration Packs\n• Integration of SCSM-Orchestrator\n• Creating and Deploying Runbooks\n• Troubleshooting Runbook Server Performance Issues\n\nSystem Center Service Manager:\n• Installation, deployment and troubleshooting of System Center Service Manager\n• Configuring Connectors\n• Notifications\n• Portal deployment (SharePoint and HTML5)\n• Troubleshooting workflows\n• Troubleshooting Data-warehouse Jobs issues\n\nEDUCATION\n\nB.E.\n\nPune University\n\nSKILLS\n\nOPERATIONS MANAGER (1 year), SCOM (1 year), SYSTEM CENTER OPERATIONS MANAGER (1\nyear), Orchestrator SCSM\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\n\nOperating systems\n• Microsoft Windows Server 2012 (R2) /2008 (R2) /2003 (R2)\n• Microsoft Windows Win8.1/Win8/Win7/Vista/XP\n• Linux - Various Flavors and Distributions (Red Hat/Fedora /CentOS/Debian)\n\nServer Applications\n• Microsoft System Center Operations Manager (SCOM) 2016 /2012 […] R2\n• Microsoft System Center Service Manager (SCSM) 2016 /2012 R2/2012\n• Microsoft System Center Orchestrator 2016 /2012 R2/2012\n• Microsoft SQL […] R2\n\nVirtual Appliances\n• Microsoft Hyper-V\n\n\n\n• VMware Workstation / VirtualBox\n\nOther Applications\n• Microsoft Office […]\n• Trained and worked on Forefront Identity Manager, Microsoft Identity Manager, Forefront Threat\nManagement Gateway and Unified Access Gateway for Windows security.\n• Good knowledge of basic Networking concepts\n• Good knowledge of basics of Microsoft Active Directory\n• Basic Knowledge of Windows Failover Clusters, Microsoft Exchange and IIS.", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [49, 58, "Companies worked at"], [61, 78, "Location"], [101, 143, "Email Address"], [158, 167, "Companies worked at"], [187, 215, "Companies worked at"], [217, 222, "Location"], [222, 254, "Years of Experience"], [282, 301, "Designation"], [24, 43, "Designation"], [328, 331, "Degree"], [338, 401, "College Name"], [403, 409, "Location"], [411, 426, "College Name"], [453, 481, "Location"], [483, 504, "Location"], [506, 515, "Location"], [534, 553, "Designation"], [555, 565, "Companies worked at"], [792, 801, "Companies worked at"], [805, 822, "Location"], [826, 868, "Years of Experience"], [874, 895, "Designation"], [896, 907, "Years of Experience"], [915, 934, "Designation"], [935, 949, "Years of Experience"], [953, 977, "Companies worked at"], [988, 1031, "Companies worked at"], [2962, 2965, "Degree"], [2968, 2983, "College Name"], [1920, 1932, "Job Specific Skills"], [1934, 1944, "Job Specific Skills"], [1949, 1964, "Job Specific Skills"], [1884, 1916, "Tech Tools"], [2049, 2064, "Job Specific Skills"], [2069, 2095, "Job Specific Skills"], [2241, 2267, "Job Specific Skills"], [2577, 2594, "Tech Tools"], [2620, 2628, "Tech Tools"], [2763, 2792, "Tech Tools"], [2855, 2865, "Tech Tools"], [2870, 2875, "Tech Tools"], [2879, 2904, "Job Specific Skills"], [2993, 3011, "Job Specific Skills"], [3022, 3026, "Tech Tools"], [3037, 3069, "Tech Tools"], [3080, 3092, "Tech Tools"], [3161, 3190, "Tech Tools"], [3220, 3237, "Tech Tools"], [3266, 3271, "Tech Tools"], [3363, 3412, "Tech Tools"], [3625, 3643, "Tech Tools"], [3602, 3619, "Tech Tools"], [3559, 3572, "Tech Tools"], [3646, 3656, "Tech Tools"], [3679, 3695, "Tech Tools"], [3752, 3778, "Tech Tools"], [3724, 3750, "Tech Tools"], [3891, 3910, "Job Specific Skills"], [3941, 3967, "Job Specific Skills"], [4016, 4034, "Tech Tools"], [4039, 4042, "Tech Tools"]]}
{"id": 9, "text": "Ravi Shivgond\nBidar, Karnataka - Email me on Indeed: indeed.com/r/Ravi-Shivgond/4018c67548312089\n\nTo associate with an organization that promises a creative and challenging career in progressive\nEnvironment to enhance my knowledge, skills to be a part of team that excels in work towards\nthe growth of the organization.\n\nWORK EXPERIENCE\n\nTechnical Support Engineer\n\nSAP -  Bengaluru, Karnataka -\n\nNovember 2017 to February 2018\n\nI completed PLC Automation course that's way I will rejoin that job\n\nEDUCATION\n\nGovt. High school Yernalli\n\nB.E. in Electrical\n\nKarnataka State -  Bidar, Karnataka\n\nSKILLS\n\nAC (Less than 1 year), Allen Bradley (Less than 1 year), Engineer (Less than 1 year), FANUC\n(Less than 1 year), GE FANUC (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTECHNICAL QUALIFICATION:\n\n➢ Obtained Post graduate diploma in Industrial Plant Automation Engineer from Prolific Systems\nand Automation Bangalore. With hands on practical experience in Industrial Automation Tools\nspecializing in PLC, SCADA and VFD.\n\n➢ Attended a 3-day National level seminar on \"Mat Lab and its Applications\" organized by E&EE\ndepartment of GNDEC Bidar.\n\nACADEMIC PROJECT:\n\nSpeed synchronization of multiple DC motors in industries using wireless RF.\n\n➢ Aim: To find Speed synchronization of multiple DC motors in industries using wireless RF.\n\nhttps://www.indeed.com/r/Ravi-Shivgond/4018c67548312089?isid=rex-download&ikw=download-top&co=IN\n\n\n➢ Main Components Used: LCD Display, Diodes, Potentiometer, Rectifier, transformer, DC motor,\nDC motor drive, […] microcontroller, RF transistor, RF receiver, IR sensor, etc.\n\n➢ TECHNICAL SKILLS: Post Graduate Diploma in Industrial Automation (PGDIA) Course from M/s.\nProlific Systems and Technologies Pvt. Ltd., Bangalore which includes:\n\nAllen Bradley Micro Logix1000, SLC 5 / 03 RS Logix 500\nSiemens S7-300 SIMATIC MANAGER-5.4\nGE FANUC Versamax Micro, Proficy Machine\nDelta WPL Soft 2.42\nMitsubishi FX3U 16M GX-Developer\nOmron Sysmac - CP1E CX-Programmer\nDCS AC-800 F Freelance Industrial IT\nField Basics Of Fi, Interface With Live Plant\nInstrumentation Setup With SLC 5 / 03 Controller Interfaced With In touch SCADA", "meta": {}, "annotation_approver": null, "labels": [[0, 13, "Name"], [14, 30, "Location"], [53, 96, "Email Address"], [338, 364, "Designation"], [366, 369, "Companies worked at"], [373, 393, "Location"], [397, 427, "Years of Experience"], [537, 555, "Degree"], [557, 592, "Location"], [606, 622, "Years of Experience"], [640, 656, "Years of Experience"], [669, 685, "Years of Experience"], [695, 711, "Years of Experience"], [724, 740, "Years of Experience"], [1131, 1136, "Location"], [1624, 1678, "Degree"], [1741, 1751, "Location"], [441, 455, "Job Specific Skills"], [602, 604, "Tech Tools"], [659, 667, "Job Specific Skills"], [804, 865, "Degree"], [871, 902, "College Name"], [903, 912, "Location"], [952, 979, "Job Specific Skills"], [996, 999, "Tech Tools"], [1001, 1006, "Tech Tools"], [1011, 1014, "Tech Tools"], [1696, 1739, "College Name"], [1768, 1797, "Tech Tools"], [1799, 1804, "Tech Tools"], [1883, 1913, "Tech Tools"], [2143, 2148, "Tech Tools"]]}
{"id": 10, "text": "Rohit Bijlani\nJAVA Developer/Senior Systems Engineer - INFOSYS LIMITED\n\nItarsi, Madhya Pradesh - Email me on Indeed: indeed.com/r/Rohit-Bijlani/06ecf59ddac448c7\n\nA Dynamic, Hardworking, Focused and Result oriented person with 2 years 10 months\nof experience in development using Java Technology and other cutting edge technologies.\nExperience in design and development of enterprise applications using agile methodology. Rich\nexperience in build and deployment process.\n\nWilling to relocate to: Pune, Maharashtra\n\nWORK EXPERIENCE\n\nJAVA Developer/Senior Systems Engineer\n\nINFOSYS LIMITED -  Pune, Maharashtra -\n\nFebruary 2016 to Present\n\nProject Idea: The Aetna Quoting Center (AQC) comes under the Aetna Group in Health Industry\nand its HeadOffice is in Hartford. AQC is a Java enterprise Web based application in Sales Rating\nand Quoting (SRQ) domain. AQC is developed as a single Rating/Quoting engine for Middle and\nNational Market Customers for new business offering both Traditional and Health Maintenance\nOrganization Products.\n\nAQC is used by the Aetna Sales Representatives and Underwriters for creation of New Business\nQuotes.A Quote typically comprised of Customer, Member Information along with the Product/\nBenefits being offered and the corresponding Premium to be paid for the HealthCare Plan.\n\nResponsibilities:\n✓ Analysing business and system requirements and creating design patterns for applications.\n✓ Presenting the ideas to client for application and implementing it in the application.\n✓ Clear understanding of Requirement gathering and designing phase. Created Detailed Level\ndesign (DLD) document of all subsequent releases till date.\n✓ Experience in working over deployment process, deploying code over different servers,\nmaintaining deployment documents and handling different task in subsequent releases.\n✓ Excellent hands-on in performing request analysis, finding defects and giving code fixes in\nminimal amount of time.\n✓ Resolving Client Issue\n\nOther Responsibilities:\n✓ Sharing knowledge with the team by sharing KM tips/quizzes/sessions.\n✓ Mentoring 2-3 team members and providing technical guidance in AQC project\n\nJAVA Intern\n\nINFOSYS LIMITED -  Mysore, Karnataka -\n\nJune 2015 to November 2015\n\nhttps://www.indeed.com/r/Rohit-Bijlani/06ecf59ddac448c7?isid=rex-download&ikw=download-top&co=IN\n\n\nUndergone 6 months of internship with Infosys, which includes theoretical as well as hands on\nexperience in Core Java, Hibernate and SQL.\n- Successfully completed Infosys's Mysore Internship.\n\nEDUCATION\n\nBachelor of Engineering in Mechanical Eng\n\nITM University Gwalior -  Gwalior, Madhya Pradesh\n\n2015\n\nSKILLS\n\nJ2Ee, Sql server, Core Java, Spring mvc, Hibernate, Spring\n\nADDITIONAL INFORMATION\n\nSKILLS Languages: JAVA.\nTechnologies/Frmwks: Hibernate, Spring, Spring MVC.\nIDE/Tools: Eclipse, Rational Application Development (RAD)\nDatabase: SQL Server 2008\nMiscellaneous: Xml.\nOS: Windows", "meta": {}, "annotation_approver": null, "labels": [[0, 13, "Name"], [14, 52, "Designation"], [55, 70, "Companies worked at"], [72, 94, "Location"], [117, 160, "Email Address"], [226, 243, "Years of Experience"], [495, 512, "Location"], [571, 586, "Companies worked at"], [590, 607, "Location"], [611, 635, "Years of Experience"], [2149, 2160, "Designation"], [2162, 2177, "Companies worked at"], [2181, 2198, "Location"], [2202, 2228, "Years of Experience"], [2533, 2556, "Degree"], [2560, 2598, "College Name"], [2602, 2625, "Location"], [2627, 2631, "Graduation Year"], [164, 171, "Soft Skills"], [173, 184, "Soft Skills"], [186, 193, "Soft Skills"], [198, 213, "Soft Skills"], [279, 283, "Tech Tools"], [261, 272, "Job Specific Skills"], [346, 395, "Job Specific Skills"], [402, 419, "Job Specific Skills"], [450, 468, "Job Specific Skills"], [773, 777, "Tech Tools"], [531, 545, "Designation"], [546, 569, "Designation"], [1339, 1371, "Job Specific Skills"], [1385, 1417, "Job Specific Skills"], [1584, 1611, "Job Specific Skills"], [2442, 2446, "Tech Tools"], [2448, 2457, "Tech Tools"], [2462, 2465, "Tech Tools"], [2641, 2645, "Tech Tools"], [2647, 2657, "Tech Tools"], [2659, 2668, "Tech Tools"], [2670, 2680, "Tech Tools"], [2682, 2691, "Tech Tools"], [2693, 2699, "Tech Tools"], [2743, 2747, "Tech Tools"], [2770, 2779, "Tech Tools"], [2781, 2787, "Tech Tools"], [2789, 2795, "Tech Tools"], [2796, 2799, "Tech Tools"], [2812, 2819, "Tech Tools"], [2821, 2859, "Tech Tools"], [2870, 2885, "Tech Tools"], [2901, 2904, "Tech Tools"], [2910, 2917, "Tech Tools"]]}
{"id": 11, "text": "Roshan Sinha\nApplication Developer - SAP ABAP\n\nKolkata, West Bengal - Email me on Indeed: indeed.com/r/Roshan-Sinha/ab398efcd288724f\n\nWilling to relocate to: Kolkata, West Bengal - Bangalore City, Karnataka - NCR, Delhi\n\nWORK EXPERIENCE\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nMarch 2011 to April 2018\n\nJoined as Associate System Engineer, worked as Application Developer for maintenance and\ndevelopment of SAP systems for clients from Distribution, Chemicals and Petrochemicals and\nIndustrial sectors.\n\nApplication Developer\n\nIBM India Pvt. Ltd. -\n\nApril 2011 to March 2018\n\nApplication Development and Maintainence services on SAP using ABAP as programming\nlanguage.\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nJune 2017 to November 2017\n\nNon-ALE Outbound File Interfaces for Forward and Reverse Logistics Application from SAP to EMM\nsystem Return, Replacement and Dispatched IMEI and SIM Serial Data Interfaces.\n\nApplication Developer\n\nSAP ABAP -\n\nJuly 2016 to November 2017\n\nIMSP Applications built on OOPS-ABAP, BAPI, FM.\nSmartform for Credit/Invoice Note\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nMay 2017 to June 2017\n\nhttps://www.indeed.com/r/Roshan-Sinha/ab398efcd288724f?isid=rex-download&ikw=download-top&co=IN\n\n\nfrom Repacking - Batch creation, Create Inbound Delivery, Reprint Handling Unit, Post Goods\nMovement, Goods Receipt and Goods Issue.\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nJanuary 2017 to May 2017\n\nAudit and Tax applications built on reports and enhancements in ABAP, Purchase TAX Reports,\nC/F Forms.\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nJuly 2013 to July 2016\n\nUpload Purchase Order Header Data.\nDIM Monthly Price deviation table.\nEHS Module: -\nLACK1 report, Where-Used Report and CK2/CK3 Interface\n\nApplication Developer\n\nSAP ABAP -  Bengaluru, Karnataka -\n\nFebruary 2013 to June 2013\n\nDesign and Implement Technical Strategies for the Team Deliverable along with Leads.\n\nUploading of Internal Orders (Cost Objects) using Batch Input Recording.\n\nUploading of Direct Purchase Orders using BAPI.\n\nImplementation of User Exit for overriding standard billing document number ranges.\n\nImplementation of Pricing List Category Quote Order Header in CRM and SD.\n\nEnhancement for Filter products by material type in GTS.\n\nDevelop adding custom fields and field logic in CRM WEBUI\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nJanuary 2013 to February 2013\n\nDeveloped Outbound Interface Report from SAP to CLM, spool generation and Enhancement of\nScreen Elements using Dynamic Search Help.\n\nApplication Developer\n\n\n\nSAP ABAP -  Kolkata, West Bengal -\n\nJanuary 2012 to December 2012\n\nWorked on Remedy Tickets for issues with following applications: -\nOvertime Request on BP People Portal enhancement from Time Attendance domain in HR.\nTAX certificate Administration from Payroll.\nWorked on Custom Infotype for TAX Form.\n\nApplication Developer\n\nSAP ABAP -  Kolkata, West Bengal -\n\nAugust 2011 to December 2011\n\nEnhancement of standard and custom transaction, interfaces and e-forms\n\nEDUCATION\n\nB.Tech in Technology\n\nAnna University Chennai -  Chennai, Tamil Nadu\n\n2010\n\nSKILLS\n\nSap Abap\n\nADDITIONAL INFORMATION\n\nSKILLS\n\nSAP ABAP\nOOPS-ABAP", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [47, 67, "Location"], [90, 132, "Email Address"], [158, 178, "Location"], [181, 206, "Location"], [209, 219, "Location"], [273, 293, "Location"], [297, 321, "Years of Experience"], [333, 358, "Designation"], [370, 391, "Designation"], [524, 545, "Designation"], [547, 566, "Companies worked at"], [570, 594, "Years of Experience"], [725, 745, "Location"], [749, 775, "Years of Experience"], [987, 1013, "Years of Experience"], [1133, 1153, "Location"], [1157, 1178, "Years of Experience"], [1447, 1467, "Location"], [1471, 1495, "Years of Experience"], [1636, 1656, "Location"], [1660, 1682, "Years of Experience"], [1858, 1878, "Location"], [1882, 1908, "Years of Experience"], [2431, 2451, "Location"], [2455, 2484, "Years of Experience"], [2656, 2677, "Location"], [2680, 2709, "Years of Experience"], [2983, 3003, "Location"], [3007, 3035, "Years of Experience"], [3120, 3126, "Degree"], [3130, 3165, "College Name"], [3190, 3195, "Graduation Year"], [13, 45, "Designation"], [238, 269, "Designation"], [690, 721, "Designation"], [952, 983, "Designation"], [1098, 1128, "Designation"], [1412, 1443, "Designation"], [1601, 1632, "Designation"], [1823, 1854, "Designation"], [2396, 2427, "Designation"], [2619, 2652, "Designation"], [2948, 2979, "Designation"], [427, 438, "Tech Tools"], [596, 636, "Job Specific Skills"], [649, 652, "Tech Tools"], [659, 663, "Tech Tools"], [861, 864, "Tech Tools"], [1015, 1032, "Job Specific Skills"], [1042, 1051, "Tech Tools"], [1053, 1057, "Tech Tools"], [1497, 1523, "Job Specific Skills"], [1561, 1565, "Tech Tools"], [1931, 1952, "Job Specific Skills"], [2046, 2067, "Job Specific Skills"], [2112, 2116, "Tech Tools"], [2527, 2530, "Tech Tools"], [2534, 2537, "Tech Tools"], [3169, 3188, "Location"], [3246, 3249, "Tech Tools"], [3250, 3254, "Tech Tools"], [3255, 3264, "Tech Tools"]]}
{"id": 12, "text": "Sai Dhir\n- Email me on Indeed: indeed.com/r/Sai-Dhir/e6ed06ed081f04cf\n\nWORK EXPERIENCE\n\nSasken Technologies Pvt. Ltd -  Pune, Maharashtra -\n\nJanuary 2017 to Present\n\nORACLE -\n\nJanuary 2011 to Present\n\nSTP is basically a router that realys ss7 messages through various signally points. In project all\nSTPs were replaced by ORACLE STP due to its advanced features, high end support, flexibility.\nThe STP is connected to adjacent SEPs and STPs via signaling links. Based on the address fields\nof the SS7 messages, the STP routes the messages to the appropriate outgoing signaling link.\n\nClient: ORACLE\nTeam Size: 4\nRole: fetching data, analyzing, monitoring, troubleshooting\nTechnologies: filezilla, putty\nMajor Development\n➢ Currently working on External Browser Integration for the Payment Gateway\n➢ Multiple Shipping methods Inside Order Invoice\n\nORACLE -  Gurgaon, Haryana -\n\nOctober 2016 to January 2017\n\nKarizma Order Manager & Karizma Order System\n\nORACLE -\n\nMarch 2011 to October 2011\n\nStamp Duty means a tax payable on certain legal documents specified by statute; the duty may\nbe fixed or ad valorem meaning that the tax paid as a stamp duty may be a fixed amount or\nan amount which varies based on the value of the products, services or property on which it is\nlevied. It is basically a kind of tax paid on any transaction based on exchange of documents or\nexecution of instruments.\n\nClient: Max Life Insurance\nTeam Size: 4\nRole: Business Analyst, Testing and Bug Fixing.\nTechnologies: Group Asia\nMajor Development\n➢ Reading of the new requirements and have a thorough knowledge regarding the functionality\nand generating test case regarding it.\n\nhttps://www.indeed.com/r/Sai-Dhir/e6ed06ed081f04cf?isid=rex-download&ikw=download-top&co=IN\n\n\nPMJJ BY: PMJJBY (Pradhan Mantri Jeevan Jyoti Bima Yojana) is a one year Life insurance scheme,\nauto renewed every year, offering coverage for death due to any reason. The PMJJBY scheme is\navailable to anyone between 18 and 50 years of age and with a CSB Savings bank account. The\nmain administrator within the scheme could be life insurance corporation in addition to hardly\nany other insurance providers who will be able to give similar benefits under the scheme\nTeam Size: 4\nRole: business analyst, testing\nTechnologies: group asia\n\nEDUCATION\n\nClient-Server Architecture\n\nCDAC -  Mohali, Punjab\n\nJanuary 2017 to Present\n\nBachelor of Engineering in Engineering\n\nPunjab Technical University (PTU) Jalhandar\n\n2012 to 2016\n\nB.E\n\nPunjab Technical University\n\n2012 to 2016\n\nAndroid\n\nCDAC Mohali -  Mohali, Punjab\n\nSKILLS\n\nCHANGE MANAGEMENT (Less than 1 year), Configuration Management (Less than 1 year), Git\n(Less than 1 year), Incident Management (Less than 1 year), Linux (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nOperating Systems Windows 7, ubuntu Linux\nConfiguration Management Git, svn\nIncident Management IBM i series 400\n\nDomain insurance\n\nPlatform - Windows\n\nFramework-Group Asia\n\nDomian Telecom\n\n\n\nDomain telecom\n\nPlatform-windows\n\nFramework-filezilla, putty", "meta": {}, "annotation_approver": null, "labels": [[0, 8, "Name"], [31, 69, "Email Address"], [88, 116, "Companies worked at"], [120, 137, "Location"], [141, 164, "Years of Experience"], [166, 172, "Companies worked at"], [176, 199, "Years of Experience"], [2372, 2395, "Degree"], [2446, 2456, "Location"], [2399, 2445, "College Name"], [2465, 2470, "Graduation Year"], [2471, 2474, "Degree"], [2476, 2503, "College Name"], [2513, 2518, "Graduation Year"], [2543, 2549, "Location"], [2551, 2558, "Location"], [2331, 2337, "Location"], [2339, 2346, "Location"], [618, 631, "Job Specific Skills"], [633, 642, "Soft Skills"], [656, 671, "Job Specific Skills"], [686, 695, "Tech Tools"], [697, 702, "Tech Tools"], [847, 853, "Companies worked at"], [857, 873, "Location"], [877, 905, "Years of Experience"], [915, 928, "Designation"], [953, 959, "Companies worked at"], [963, 989, "Years of Experience"], [1438, 1454, "Designation"], [1456, 1463, "Job Specific Skills"], [1468, 1478, "Job Specific Skills"], [2232, 2248, "Designation"], [2250, 2257, "Job Specific Skills"], [2567, 2584, "Job Specific Skills"], [2295, 2327, "Job Specific Skills"], [2347, 2370, "Graduation Year"], [2605, 2629, "Job Specific Skills"], [2650, 2653, "Tech Tools"], [2674, 2693, "Job Specific Skills"], [2714, 2719, "Tech Tools"], [2782, 2791, "Tech Tools"], [2793, 2805, "Tech Tools"], [2806, 2830, "Job Specific Skills"], [2831, 2834, "Tech Tools"], [2981, 2988, "Tech Tools"], [3000, 3009, "Tech Tools"], [3011, 3016, "Tech Tools"]]}
{"id": 13, "text": "Sai Patha\nMule ESB Integration Developer - Cisco Systems\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Sai-Patha/981ba615ab108e29\n\n• 6+ years of professional experience in end-to-end designing, developing and implementation\nof\nsoftware solutions in the areas of Middleware Integration and J2EE based applications.\n• Expertise in the areas of Core Java, Servlet 2.3, JSP, Web Services, MESB, and OSB.\n• Expertise in PL SQL programming and Oracle Apps (Oracle Order management)\n• Having 2.5+ years of experience in Mule and expert in Mule ESB development (3.7v & 3.8v),\nMule\nESB administration and Mule API management (API GW 1.x, 2.x, 3.x) CloudHub.\n• Experience in building Mule ESB & API management platform for organizations\n• Experience in performance tuning, testing, and benchmarking the platform for the\norganization.\n• Expert in building middleware systems using Message Routing, Content Enrichment, Cache\nMechanism, Message Filtering, Message Transformation, Message sequencing, Batch message\nprocessing, Error handling and reconciliation mechanisms.\n• Expertise in designing and implementing multi-tiered application with high performance using\nJ2EE standards.\n• Good understanding of API management systems - Mulesoft and RAML.\n• Experience in compiling proof of concepts and presenting to customers.\n• Deep knowledge of all phases of software engineering involving analysis, design and\nimplementation.\n• Hands on experience in load testing and performance testing and setting up the environment.\n• Very strong debugging skills.\n• Expertise in implementing different J2EE design patterns and Java Multi-Threading.\n• Hands on experience in creating Splunk dashboard, App for production proactive monitoring\nand\nfor reporting.\n• Good understanding in Web services security.\n• Extensively worked on servers Apache Tomcat, JBoss, Web logic and WebSphere.\n• Familiar with reviewing Functional Requirements and writing Technical Specifications.\n• Extensive Expertise in using Oracle 11i.\n• Broad knowledge of version control systems, build scripts and logging mechanisms.\n• Analysis, design and development of Applications based on J2EE & allied technologies using\nAgile\nmethodology.\n• Implementing high-end performance REST services for Middleware using Core Java.\n• Deploying Builds to DEV/TEST/PROD environment using Kintana, Udeploy & URelease tools.\n• Implementation of modules using Core Java APIs, Java collection, XML technologies and\nintegrating the modules.\n• Responsible for converting the understood business into Technical Specification document.\n• Taking KT sessions from client on Application functionality and discuss with Business Analysts to\nintegrate new functionalities with existing systems.\n• Utilize Oracle as back-end database and TOAD for querying.\n• Utilize Log4j as logging mechanism and developed wrapper class to configure the logs.\n• Utilize CVS, SVN, and GIT as Version control tool.\n\nhttps://www.indeed.com/r/Sai-Patha/981ba615ab108e29?isid=rex-download&ikw=download-top&co=IN\n\n\n• Used Maven build scripts to create and deploy the release builds.\n• Prepare the Functional and Technical Design documents.\n• Module implementation and customization based on the Change Requests.\n• Development and support for different releases before and after implementation of launch.\n• Review the fellow developers' code as an exercise of internal code review.\n• Carry out Configuration Management activities for projects.\n• Carry out Weekly Status reporting activities such as MOM updates and health sheet generation.\n• Excellent communication and interpersonal skills. Involved in client interactions for scoping,\neffort\nestimates and status reporting.\n\nWORK EXPERIENCE\n\nMule ESB Integration Developer\n\nCisco Systems -  San Jose, CA -\n\nApril 2017 to Present\n\nProject: Extended Enterprise B2B Transformation\n\nResponsibilities\n• Followed agile methodology and Scrum.\n• Involved in application design and participated in technical meetings, Effort estimations,\nbacklog\ngrooming, I&A etc.\n• Involved in analyzing, developing, troubleshooting, debugging, and optimizing ESB application.\n• Involved in documenting and presenting designed technical solutions.\n• Extensively used Anypoint studio to develop and design the business process.\n• Implemented complex transformation Logics using MEL\n• Building RESTful Web Services with Anypoint Platform for APIs\n• Involved in data transformation and mapping using data weave\n• Tested the business process in test mode for debugging\n• Build and deployed using Anypoint studio, maven.\n• Participating i n meeting and on-calls\n• Code reviews and independent unit testing for components\n• Manage code release deployment into development, SIT, OAT and production\n• Error handling is properly done in all the business processes\nTechnologies: Mule Server 3.7 EE, Anypoint studio 5.4, cloud hub, Maven, core java, GIT, RAML,\nAPIKit, SOAP 01, Postman, Agile, Jenkins.\n\nMule Soft Team Lead\n\nCisco Systems -  San Jose, CA -\n\nJune 2016 to March 2017\n\nProject:FSMS\n\nResponsibilities:\n• Followed agile methodology and Scrum.\n\n\n\n• Involved in application design and participated in technical meetings, Effort estimations,\nbacklog\ngrooming, I&A etc.\n• Gather requirements and planning on integration of oracle data base with cloud applications\nusing\nMule ESB.\n• Tightly integrated applications using MULE ESB.\n• Involved in implementing ESB flows, Proxies, logging and exception handling.\n• Extensively used Mule ESB components like File Transport, SMTP Transport, FTP/SFTP\nTransport, JDBC Connector, JMS and Transaction Manager.\n• Used TOAD for internal data storage and retrieval.\n• Involved in setting up Connection pooling and used JMS for Asynchronous messaging.\n• Setting up Mule ESB for the development environment.\n• Developed application using Mule ESB and deployed the services in dev, test and prod\nenvironments. And also done with Unit testing using Test Utility.\n• Migrated Mule ESB 3.7.0 apps to Mule ESB 3.8.4\n• Applied OAUTH authentication policy for API proxies\n• Have integrated web services including SOAP as well as REST using Mule ESB.\n• QA, UAT & Production issues investigation and supporting business users.\n\nMule Soft Team Lead\n\nCisco Systems -  San Jose, CA -\n\nAugust 2015 to May 2016\n\nProject: Bay Bridge RMA Services\n\n• Design and implement the Mule ESB platform for Cisco.\n• Design and implement the RESTful WS using RAML to interact with AI system and storing in\nC3 Database.\n• Implemented the security for the SOAP and REST Web services using OAUTH and Basic\nAuthentication.\n• Designed and developed the core modules, which pulls service request details from CSOne\nSystem.\n• Design and developed common modules like Audit Logging, which can be used as a common\nmodule and shared resources for all the applications.\n• Designed the Exception handling for all the apps on Mule platform.\n• Designed the Domain to share the resource like HTTP, HTTPS & DB connector references.\n• Created flows for basic authentication and caching the token for OAUTH.\n• Have carried out performance testing for the ESB flows for memory leakage and for fine-tuning.\n• Worked with Mule team on some of the issue with performance on DB connector.\n• Interacted with dependent teams (CSONE and PEGA) and came up with the design on the\nimplementation of the flows and architecture and design of services.\n• Developed required back Java components.\n• Reported and worked on DB connector Connection pool issues to Mulesoft.\n• Reported and worked on MMC deployment issues with Mulesoft.\nTechnologies: Java 1.8, Oracle11i Web Services, Mule ESB, Mule API Manager, XML, JSON,\nAnypoint Studio, Maven, GIT, SVN, ESB Servers.\n\nTechnology Analyst\n\n\n\nCisco Systems -  San Jose, CA -\n\nDecember 2013 to July 2015\n\nProject: OMLSS\n\n• Understanding of the complete architecture of the system including boundary systems.\n• Understand the client and project requirements by studying the existing documentation and\nseeking clarifications, if any, to participate efficiently in the Development and Testing phases of\nthe project.\n• Create program specifications, unit test plans for software programs by studying functional\nand non-functional requirements, the application architecture document, and converting the\nassigned\nfunctionalities into pseudo code/algorithms/test cases.\n• Develop code using knowledge of relevant technology as per design specifications and\ndocument\nartifacts such as unit test scripts, etc. independently and support peers in identifying code defects\nand ensuring that the output is as per the given specifications and SLAs.\n• Perform testing - self and independent (Functional, Integration, System) - as per defined\nprocesses and guidelines to ensure accurate program output; identify and resolve defects, if any.\n• Work on 'Go Live' activities as per the Implementation plan and manage any issues related to\nfunctionalities, user interface, performance, etc. that may arise.\n• Respond to the issues assigned, conduct analysis of the issues assigned, identify and evaluate\ndifferent workarounds/ solution alternatives, implement the most optimal solution, support other\nteam members on issue resolution in areas of expertise as required, manage stakeholder\ncommunication and close the issues assigned in order to ensure support availability as per\nagreed\nSLAs.\n• Understand application architecture document and seek inputs from the architecture / design\nteam to understand the overall architecture IN ORDER TO provide deliverables that are in line\nwith architectural requirements.\n\nTechnologies: Java 1.5, Web services, Oracle 11i, XML, Eclipse, HP Quality Center, SVN, Jenkins,\nuDeploy and uRelease.\n\nSr.Software Engineer\n\nCisco Systems -  San Jose, CA -\n\nJune 2013 to November 2013\n\nProject: SPED Integration\n\nResponsibilities:\n• Responsible for development of Oracle Interfaces and Mapping data as per requirements of\nCisco\nBrazil.\n• Involved in the development of PL/SQL queries to fetch data from the oracle and insertion of\nData into Synchro Open Interface tables. This is a very critical data reporting as the data is to be\nreported to the Government of Brazil by Cisco as per the Legal Procedures. He was involved in the\n\n\n\nDevelopment of Packages, Concurrent Programs and many other Custom functionalities as per\nthe Requirements.\n• Experience in Cisco Quality Control process and Migration of Code into Different Environments.\n• Implemented the PL/SQL based on the requirements of AP and RI modules.\n\nTechnologies: PL/SQL, Oracle Applications (Financials), PVCS, Kintana, Toad.\n\nSoftware Engineer\n\nCisco Systems -  San Jose, CA -\n\nApril 2012 to May 2013\n\nProject: IT Creative Solutions (ITCS)\n\nResponsibilities:\n• Understand the business process and build interactive online dashboards based on client\nrequirement.\n• Do the data modelling to hold the current data, to sustain future needs and drilldowns in the\ndashboards.\n• Use dashboard building tools Xcelsius, Tableau to build interactive dashboards.\n• Develop business layer to perform all the calculations used in dashboard.\n• Do client interaction and communication to get required inputs.\n• Participated in Client Demos and meetings.\n\nTechnologies: SQL Server, Tableau, Xcelsius.\n\nSoftware Engineer\n\nArrow Electronics Inc -\n\nAugust 2011 to March 2012\n\nProject: Arrow Unity (Sales Work Bench)\n\nResponsibilities:\n• Requirements gathering, designing, development and testing.\n• Ownership of the deliverables.\n• Involved in Post Production support for SWB application.\n• Coding and testing for enhancements.\n• Wrote Oracle PL/SQL Stored procedures, triggers, and views for backend database access.\n• UAT Testing Support.\n\nTechnologies: Unix, ExtJs, Pl/Sql\n\nEDUCATION\n\nBachelor of Technology in Technology\n\nAmrita School of Engineering\n\n2007 to 2011\n\n\n\nSKILLS\n\nORACLE (3 years), JAVA (3 years), SOAP (2 years), Subversion (2 years), SVN (2 years)\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS:\n\nLanguages Java, Java Script, PL/SQL, SQL SERVER, HTML, XML,\nXSLT\nApplication Servers J Boss, Apache Tomcat, Web logic, IBM web sphere\n\nDatabases Oracle, SQL SERVER\n\nDevelopment Tools MVC, Singleton, Session Facade, DTO, DAO,\n\nService Locator\n\nSOA Restful Web Service, Soap web service, JAX-RS, XML, JSON, WS\nSecurity, Mule ESB\n\nIDE's/TOOLS Eclipse, Mule Anypoint Studio\n\nJava/J2EETechnologies Java, Servlets, JSP, JDBC, EJB, JMS\n\nProtocols HTTP, FTP, TCP/IP\n\nVersion Control tools CVS, SVN, GIT\n\nBuild Tool Ant, Maven\n\nBug Tracking Tools HP Quality Center, Rally\n\nFrameworks Spring, Hibernate, Struts, Spring MVC, Micro Services, EJB, JMS\n\nOperating Systems Windows, UNIX, LINUX", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [15, 40, "Designation"], [43, 56, "Companies worked at"], [58, 78, "Location"], [101, 140, "Email Address"], [144, 152, "Years of Experience"], [496, 506, "Years of Experience"], [3704, 3734, "Designation"], [3736, 3749, "Companies worked at"], [3753, 3765, "Location"], [3769, 3790, "Years of Experience"], [4931, 4950, "Designation"], [4952, 4965, "Companies worked at"], [4969, 4981, "Location"], [4985, 5008, "Years of Experience"], [6188, 6207, "Designation"], [6209, 6222, "Companies worked at"], [6226, 6238, "Location"], [6242, 6265, "Years of Experience"], [11699, 11721, "Degree"], [11725, 11765, "College Name"], [11775, 11780, "Graduation Year"], [183, 203, "Job Specific Skills"], [205, 246, "Job Specific Skills"], [273, 295, "Job Specific Skills"], [300, 304, "Tech Tools"], [353, 362, "Tech Tools"], [364, 375, "Tech Tools"], [377, 380, "Tech Tools"], [382, 394, "Tech Tools"], [396, 400, "Tech Tools"], [406, 409, "Tech Tools"], [426, 432, "Tech Tools"], [433, 444, "Job Specific Skills"], [449, 461, "Tech Tools"], [524, 528, "Tech Tools"], [543, 551, "Tech Tools"], [579, 587, "Tech Tools"], [607, 615, "Tech Tools"], [650, 658, "Tech Tools"], [856, 874, "Job Specific Skills"], [1165, 1169, "Tech Tools"], [1525, 1541, "Job Specific Skills"], [1449, 1461, "Job Specific Skills"], [1205, 1227, "Job Specific Skills"], [1588, 1592, "Tech Tools"], [1613, 1617, "Tech Tools"], [1770, 1791, "Job Specific Skills"], [1825, 1838, "Tech Tools"], [1840, 1845, "Tech Tools"], [1847, 1856, "Tech Tools"], [1861, 1870, "Tech Tools"], [1991, 2002, "Tech Tools"], [2089, 2097, "Soft Skills"], [2099, 2137, "Job Specific Skills"], [2147, 2151, "Tech Tools"], [2235, 2239, "Tech Tools"], [2253, 2263, "Job Specific Skills"], [2270, 2279, "Tech Tools"], [2404, 2418, "Tech Tools"], [2799, 2804, "Tech Tools"], [2770, 2774, "Tech Tools"], [3404, 3428, "Job Specific Skills"], [3562, 3575, "Soft Skills"], [3580, 3593, "Soft Skills"], [4807, 4825, "Tech Tools"], [4827, 4846, "Tech Tools"], [4848, 4857, "Tech Tools"], [4859, 4864, "Tech Tools"], [4866, 4875, "Tech Tools"], [4877, 4880, "Tech Tools"], [4882, 4886, "Tech Tools"], [4888, 4894, "Tech Tools"], [4896, 4900, "Tech Tools"], [4905, 4912, "Tech Tools"], [4914, 4919, "Tech Tools"], [4921, 4928, "Tech Tools"], [3108, 3149, "Job Specific Skills"], [3869, 3896, "Job Specific Skills"], [5053, 5080, "Job Specific Skills"], [4330, 4350, "Job Specific Skills"], [4356, 4364, "Tech Tools"], [5207, 5226, "Job Specific Skills"], [5099, 5117, "Job Specific Skills"], [5592, 5596, "Tech Tools"], [5898, 5910, "Job Specific Skills"], [5917, 5929, "Tech Tools"], [5942, 5956, "Tech Tools"], [5965, 5979, "Tech Tools"], [6075, 6079, "Tech Tools"], [6091, 6095, "Tech Tools"], [7532, 7540, "Tech Tools"], [7556, 7564, "Tech Tools"], [7566, 7588, "Tech Tools"], [7590, 7598, "Tech Tools"], [7600, 7616, "Tech Tools"], [7618, 7621, "Tech Tools"], [7623, 7627, "Tech Tools"], [7629, 7644, "Tech Tools"], [7646, 7651, "Tech Tools"], [7653, 7656, "Tech Tools"], [7658, 7661, "Tech Tools"], [7677, 7695, "Designation"], [7699, 7712, "Companies worked at"], [7716, 7728, "Location"], [7732, 7758, "Years of Experience"], [8100, 8116, "Job Specific Skills"], [9563, 9571, "Tech Tools"], [9573, 9585, "Tech Tools"], [9587, 9597, "Tech Tools"], [9599, 9602, "Tech Tools"], [9604, 9611, "Tech Tools"], [9613, 9630, "Tech Tools"], [9632, 9635, "Tech Tools"], [9637, 9644, "Tech Tools"], [9646, 9653, "Tech Tools"], [9658, 9666, "Tech Tools"], [9669, 9689, "Designation"], [9691, 9704, "Companies worked at"], [9708, 9720, "Location"], [9724, 9750, "Years of Experience"], [9935, 9941, "Tech Tools"], [10422, 10428, "Tech Tools"], [10492, 10498, "Tech Tools"], [10500, 10506, "Tech Tools"], [10556, 10573, "Designation"], [10575, 10588, "Companies worked at"], [10592, 10604, "Location"], [10608, 10630, "Years of Experience"], [10931, 10939, "Tech Tools"], [10941, 10948, "Tech Tools"], [11184, 11194, "Tech Tools"], [11196, 11203, "Tech Tools"], [11205, 11213, "Tech Tools"], [11216, 11233, "Designation"], [11235, 11256, "Companies worked at"], [11260, 11285, "Years of Experience"], [11502, 11508, "Job Specific Skills"], [11513, 11520, "Job Specific Skills"], [11547, 11560, "Tech Tools"], [11667, 11671, "Tech Tools"], [11673, 11678, "Tech Tools"], [11680, 11686, "Tech Tools"], [11791, 11797, "Tech Tools"], [11809, 11813, "Tech Tools"], [11825, 11829, "Tech Tools"], [11841, 11851, "Tech Tools"], [11863, 11866, "Tech Tools"], [11931, 11935, "Tech Tools"], [11937, 11948, "Tech Tools"], [11950, 11956, "Tech Tools"], [11958, 11968, "Tech Tools"], [11970, 11974, "Tech Tools"], [11976, 11979, "Tech Tools"], [11981, 11985, "Tech Tools"], [11986, 12012, "Tech Tools"], [12014, 12027, "Tech Tools"], [12029, 12038, "Tech Tools"], [12066, 12072, "Tech Tools"], [12074, 12084, "Tech Tools"], [12104, 12107, "Tech Tools"], [12109, 12118, "Tech Tools"], [12120, 12134, "Tech Tools"], [12056, 12065, "Job Specific Skills"], [12040, 12054, "Tech Tools"], [12136, 12139, "Tech Tools"], [12141, 12144, "Tech Tools"], [12168, 12187, "Tech Tools"], [12189, 12205, "Tech Tools"], [12207, 12213, "Tech Tools"], [12215, 12218, "Tech Tools"], [12220, 12224, "Tech Tools"], [12226, 12237, "Tech Tools"], [12239, 12247, "Tech Tools"], [12261, 12268, "Tech Tools"], [12270, 12290, "Tech Tools"], [12292, 12301, "Tech Tools"], [12314, 12318, "Tech Tools"], [12320, 12328, "Tech Tools"], [12330, 12333, "Tech Tools"], [12335, 12339, "Tech Tools"], [12341, 12344, "Tech Tools"], [12361, 12365, "Tech Tools"], [12367, 12370, "Tech Tools"], [12372, 12378, "Tech Tools"], [12402, 12405, "Tech Tools"], [12407, 12410, "Tech Tools"], [12412, 12415, "Tech Tools"], [12428, 12431, "Tech Tools"], [12433, 12438, "Tech Tools"], [12459, 12476, "Tech Tools"], [12478, 12483, "Tech Tools"], [12496, 12502, "Tech Tools"], [12504, 12513, "Tech Tools"], [12515, 12521, "Tech Tools"], [12523, 12533, "Tech Tools"], [12535, 12549, "Tech Tools"], [12551, 12554, "Tech Tools"], [12556, 12559, "Tech Tools"], [12579, 12586, "Tech Tools"], [12588, 12592, "Tech Tools"], [12594, 12599, "Tech Tools"]]}
{"id": 14, "text": "Sai Vivek Venkatraman\nDecisive, Data driven and results-oriented professional offering\n13 years of experience in Infosys Limited handling and managing\nInformation Technology projects in Telecom domain with the last 3+\nyears focused on Project Management.\n\nChennai, Tamil Nadu - Email me on Indeed: indeed.com/r/Sai-Vivek-Venkatraman/\na009f49bfe728ad1\n\nfor excellence in project delivery (2015 - 2016)\n\nOptimistic Project Manager with a\ntotal experience of 13 years, TECHNOLOGY LEAD / ANALYST\naccomplished in prioritizing and INFOSYS LIMITED, INDIA & USA\ndelivering projects with competence. December 2008 - January 2015\nData driven decision maker, creative • Key responsibilities included: Requirements Gathering\nand elucidation,\nproblem solver and a resilient Estimation, Defect Management & Warranty Support, Team /\nResource\nnegotiator with a remarkable Management, On-time Escalation, Status reporting and Client\nunderstanding of business goals and engagement\noperational methodologies. • Spear-headed the effort of making an entire suite of Consumer\napplications Web Accessibility complaint based on a WCAG 2.0 and\nWAI-ARIA guidelines for one of the World's largest Telecom service\n\nWilling to relocate to: Pune, Maharashtra - Bengaluru, Karnataka - Chennai, Tamil Nadu\n\nWORK EXPERIENCE\n\nProject Manager / Technology Lead\n\nInfosys Limited -\n\nFebruary 2015 to Present\n\n• Key responsibilities included: Project Scoping, Estimation, Resource Planning, Scheduling,\nQuality Planning & Management, Risk Planning & Mitigation, Defect Management & Warranty\nSupport, Team / Resource Management, Continuous evaluation and providing feedback through\nappraisals, On-time Escalation, Status reporting and Client engagement.\n• An ardent believer and practitioner of Data driven decision making; skilled in Business Analytics\nand Intelligence encompassing Statistical\nconcepts, Data Analysis, Data Visualization and Presentation, Predictive Modeling, Artificial\nIntelligence and Machine Learning algorithms and basics of web scraping - Text/Sentiment\nAnalytics.\n• Adept in effectively contributing and handling all Agile ceremonies, have extensively worked\non Waterfall model based projects as well.\n• Proposed and Implemented Project and Process level improvement techniques which resulted\nin mitigating the risk and bringing back project\ndelivery on-track.\n• Independently managed and mentored a dynamic 30 member team.\n\nhttps://www.indeed.com/r/Sai-Vivek-Venkatraman/a009f49bfe728ad1?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Sai-Vivek-Venkatraman/a009f49bfe728ad1?isid=rex-download&ikw=download-top&co=IN\n\n\n• Winner of the Most Valuable Player Award in large engagement category\n\nProject Manager / Technology Lead\n\nInfosys Limited -\n\nFebruary 2015 to Present\n\n- Key responsibilities included: Project Scoping, Estimation, Resource Planning, Scheduling,\nQuality Planning & Management, Risk Planning & Mitigation, Defect Management & Warranty\nSupport, Team / Resource Management, Continuous evaluation and providing feedback through\nappraisals, On-time Escalation, Status reporting and Client engagement.\n- An ardent believer and practitioner of Data driven decision making; skilled in Business\nAnalytics and Intelligence encompassing Statistical concepts, Data Analysis, Data Visualization\nand Presentation, Predictive Modeling, Artificial Intelligence and Machine Learning algorithms\nand basics of web scraping - Text/Sentiment Analytics.\n- Adept in effectively contributing and handling all Agile ceremonies, have extensively worked\non Waterfall model based projects as well.\n- Proposed and Implemented Project and Process level improvement techniques which resulted\nin mitigating the risk and bringing back project delivery on-track.\n- Independently managed and mentored a dynamic 30 member team.\n- Winner of the Most Valuable Player Award in large engagement category for excellence in project\ndelivery (2015 – 2016).\n\nTechnology Lead / Analyst\n\nInfosys Limited\n\n• Key responsibilities included: Requirements Gathering and elucidation, Estimation, Defect\nManagement & Warranty Support, Team / Resource Management, On-time Escalation, Status\nreporting and Client engagement\n• Spear-headed the effort of making an entire suite of Consumer applications Web Accessibility\ncomplaint based on a WCAG 2.0 and WAI-ARIA guidelines for one of the World’s largest Telecom\nservice provider\n• Working with diverse clients, across different cities in US resulted in enhancing customer\nrelationship.\n• Exceptional client interfacing and engagement skills resulted in significant project expansion\nwhich included addition of two new applications to the suite of applications that we managed\n• Winner of the Best Team Award in mid-size project category for excellence in Project execution\n(2014 – 2015)\n\nEDUCATION\n\nPost Graduate Program in Business Analytics & Business Intelligence in\nBUSINESS\n\nGreat Lakes Institute Of Management -  Chennai, Tamil Nadu\n\n2017 to 2018\n\nBACHELOR OF ENGINEERING in ELECTRONICS & COMMUNICATION\nENGINEERING\n\n\n\nAdhiparasakthi Engineering College (Anna University)\n\n2001 to 2005\n\nIBM Jazz\n\nQuality Center (ALM)", "meta": {}, "annotation_approver": null, "labels": [[87, 95, "Years of Experience"], [113, 128, "Companies worked at"], [256, 275, "Location"], [298, 350, "Email Address"], [388, 399, "Years of Experience"], [456, 464, "Years of Experience"], [466, 491, "Designation"], [525, 540, "Companies worked at"], [542, 553, "Location"], [591, 619, "Years of Experience"], [1211, 1228, "Location"], [1231, 1251, "Location"], [1254, 1273, "Location"], [1327, 1342, "Companies worked at"], [1346, 1370, "Years of Experience"], [2751, 2775, "Years of Experience"], [2697, 2712, "Designation"], [2715, 2730, "Designation"], [2732, 2747, "Companies worked at"], [3939, 3954, "Designation"], [3957, 3965, "Designation"], [3966, 3981, "Companies worked at"], [4818, 4885, "Degree"], [4889, 4934, "College Name"], [4938, 4957, "Location"], [4973, 5039, "Degree"], [5043, 5095, "College Name"], [0, 21, "Name"], [22, 30, "Soft Skills"], [32, 43, "Soft Skills"], [48, 64, "Soft Skills"], [151, 182, "Job Specific Skills"], [235, 253, "Job Specific Skills"], [632, 646, "Soft Skills"], [648, 656, "Soft Skills"], [413, 428, "Job Specific Skills"], [402, 412, "Soft Skills"], [690, 712, "Job Specific Skills"], [730, 744, "Soft Skills"], [773, 790, "Job Specific Skills"], [793, 809, "Job Specific Skills"], [827, 837, "Soft Skills"], [856, 866, "Job Specific Skills"], [1292, 1307, "Designation"], [1310, 1325, "Designation"], [1405, 1420, "Job Specific Skills"], [1434, 1451, "Job Specific Skills"], [1422, 1432, "Job Specific Skills"], [1465, 1494, "Job Specific Skills"], [1496, 1509, "Job Specific Skills"], [1524, 1541, "Job Specific Skills"], [1544, 1560, "Job Specific Skills"], [1867, 1880, "Soft Skills"], [1882, 1900, "Soft Skills"], [1919, 1938, "Job Specific Skills"], [1905, 1917, "Soft Skills"], [1940, 1963, "Job Specific Skills"], [1968, 1995, "Job Specific Skills"], [2010, 2022, "Job Specific Skills"], [2025, 2049, "Job Specific Skills"], [2104, 2109, "Job Specific Skills"], [2149, 2158, "Job Specific Skills"], [2216, 2253, "Job Specific Skills"], [2810, 2825, "Job Specific Skills"], [2827, 2837, "Job Specific Skills"], [2839, 2856, "Job Specific Skills"], [2870, 2899, "Job Specific Skills"], [2901, 2927, "Job Specific Skills"], [2929, 2946, "Job Specific Skills"], [2949, 2965, "Job Specific Skills"], [2974, 2993, "Job Specific Skills"], [3201, 3219, "Job Specific Skills"], [3250, 3270, "Job Specific Skills"], [3272, 3285, "Soft Skills"], [3287, 3305, "Soft Skills"], [3310, 3322, "Soft Skills"], [3325, 3343, "Job Specific Skills"], [3345, 3368, "Job Specific Skills"], [3373, 3400, "Job Specific Skills"], [3415, 3427, "Job Specific Skills"], [3430, 3454, "Job Specific Skills"], [3554, 3563, "Job Specific Skills"], [3509, 3514, "Job Specific Skills"], [4016, 4038, "Job Specific Skills"], [4056, 4066, "Job Specific Skills"], [4068, 4085, "Job Specific Skills"], [4088, 4104, "Job Specific Skills"], [4113, 4132, "Job Specific Skills"], [4959, 4971, "Graduation Year"], [5097, 5109, "Graduation Year"]]}
{"id": 15, "text": "Sameer Kujur\nOrrisha - Email me on Indeed: indeed.com/r/Sameer-Kujur/0771f65bfa7aff96\n\nWORK EXPERIENCE\n\nApp develop\n\nMicrosoft -\n\nAugust 2016 to Present\n\nEDUCATION\n\nElectrical engineering\n\nVSSUT,burla\n\nSKILLS\n\nApplication Development, Software Testing\n\nhttps://www.indeed.com/r/Sameer-Kujur/0771f65bfa7aff96?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 20, "Name"], [43, 85, "Email Address"], [104, 115, "Designation"], [117, 127, "Companies worked at"], [130, 152, "Years of Experience"], [165, 200, "College Name"], [210, 233, "Job Specific Skills"], [234, 251, "Job Specific Skills"]]}
{"id": 16, "text": "Samyuktha Shivakumar\nLooking for strategic opportunities in the field of marketing\n\nBangalore, Karnataka - Email me on Indeed: indeed.com/r/Samyuktha-Shivakumar/\ncabce09fe942cb85\n\nWORK EXPERIENCE\n\nMarketing Operations Specialist & Product Marketer\n\nThoughtWorks -  Bengaluru, Karnataka -\n\nAugust 2012 to June 2016\n\nProduct Marketer - GoCD, ThoughtWorks Studios\n* Responsible for shaping the product marketing strategy of GoCD, an open source continuous\ndelivery product supported by ThoughtWorks\n* Worked cross-functionally with product research and development, design, customer operations\nand PR to deliver integrated go-to-market strategies for new app releases and product launches\n* Led research initiatives analyzing behavioural metrics, driving and monitoring member insights,\nand owning competitive insights by using tools like Google Analytics\n* Project headed the revamp of the GoCD website by working in tandem with the design and\ndevelopment team\n* Established a consistent process to handle the launch of new product versions and ensured it\nworked on a recurring basis\n* Worked with the development and management team to publish relevant content to the open\nsource community on a regular basis\n* Built a strong digital marketing strategy and leveraged the use of several tools to stay on top\nthe product's online presence and reach:\n◦ BuzzSumo, Hootsuite, and Tweet Deck for social media marketing\n◦ Google Adwords for SEO\n◦ Buzzstream, Medium, Wistia and Quora for content marketing\n\nMarketing Operations Specialist - Global Marketing\n* Planned and executed global marketing events to build email campaigns, target audience lists,\ncommunication collateral and follow-up processes\n* Expert user of the marketing automation tool Marketo; worked with regional marketing teams\nglobally to set up their systems from scratch\n* Proactively worked on identifying new customer groups and developed optimal channels to\nreach them\n* Led the creation and management of a marketing performance dashboard that reported on\nmarketing investment, conversion, and overall campaign performance\n* Responsible for providing reliable and consistent metrics on the effectiveness of marketing\ncampaigns to stakeholders, and strategies to improve them\n* Worked in tandem with the design team to develop email and landing page templates, and\ncreated relevant content for them\n* Provided expertise and guidance about marketing automation best practices to global\nmarketing teams by setting internal benchmarks, and ensuring high quality across all regional\ncampaigns\n\nhttps://www.indeed.com/r/Samyuktha-Shivakumar/cabce09fe942cb85?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Samyuktha-Shivakumar/cabce09fe942cb85?isid=rex-download&ikw=download-top&co=IN\n\n\n* Established an effective process for onboarding new users to Marketo, and developed the\nrequired training material\n* Co-ordinated with sales leads to develop an ongoing program that managed all stages of lead\ndevelopment, from cold nurtured inquiries to closed opportunities, ensuring timely follow-up on\nqualified leads\n\nHighlights at ThoughtWorks:\n• Successfully worked with marketing teams in US, UK, Australia, China, Brazil, South Africa and\nUganda\n• Underwent training with expert Marketo consultants in San Mateo, USA\n• Spoke at a number of internal conferences on the impact of marketing automation on business\nand related topics\n• Consistently recognised by management for working effectively with globally distributed team\nmembers and successfully completing campaigns\n\nMarketing Specialist\n\nOracle -  Bengaluru, Karnataka -\n\nJune 2009 to July 2012\n\nMarketing Specialist – Hardware Programs Lead \n♣ Designed and executed campaigns based on key industry trends and strategic market\nopportunities\n♣ Developed innovative programs aligned to business requirements and sales priorities\n♣ Worked on campaign target lists, key messages, calls to action and success metrics\n♣ Aligned closely with stakeholders like Product Marketing, Field Marketing, Sales Consulting and\nSales Management teams to execute campaigns\n♣ Published regular reports on program results, highlighting patterns, growth trends, best\npractices and key learnings\n♣ Ensured sales team’s readiness through effective sales training and continually worked to\noptimize processes \n\nBusiness Analyst - Channel Support Specialist \n♣ Extended complete sales support to Oracle North America Alliances & Channel Sales team\n♣ Assisted Field Channel Managers in indirect deal closures with complete operations support\nfrom order to cash\n♣ Primary point of contact to the four exclusive distributors of Oracle and other tier two partners\nin the North America region\n♣ Managed the non-standard approval requests through Oracle’s unified approval chain\n♣ Supported Group Sales Director in identifying new growth areas & practices in line with sales\nstrategies\n\nHighlights at Oracle:\n• Was awarded the ‘Most Valuable Player’, an accolade given to the top performer of the team \n• Winner of an internal contest for achieving the highest SLA on a consecutive basis\n• Instrumental in training the new hires and on-boarding them within a short span of time\n• Recognised by senior management for being a key contributor to the sales team\n\nAnalyst - Operations\n\n\n\nTESCO -  Bengaluru, Karnataka -\n\nJune 2006 to November 2006\n\n♣ Worked as an analyst with the Accounts Payable team\n♣ Responsible for processing vendor invoices\n\nEDUCATION\n\nPost Graduate Diploma in Business Administration in Marketing and\nHuman Resource\n\nMount Carmel Institute of Management -  Bengaluru, Karnataka\n\nJune 2007 to May 2009\n\nSKILLS\n\nProject Management, Product Marketing, Campaigns Management, Digital Marketing, Social\nMedia Marketing, Marketing Operations, Content Marketing, Channels Operations, Marketo\nAdmin User, Microsoft Office, Cross Functional Marketing, Salesforce, Marketing Programs,\nGlobal Marketing, Marketo, Training\n\nLINKS\n\nhttps://www.linkedin.com/in/samyuktha-shivakumar-********/\n\nADDITIONAL INFORMATION\n\nCurrently on a break to be with toddler child. Looking to get back to work now.\n\nhttps://www.linkedin.com/in/samyuktha-shivakumar-********/", "meta": {}, "annotation_approver": null, "labels": [[0, 20, "Name"], [84, 104, "Location"], [127, 178, "Email Address"], [197, 228, "Designation"], [231, 247, "Designation"], [249, 261, "Companies worked at"], [265, 285, "Location"], [289, 313, "Years of Experience"], [315, 331, "Designation"], [334, 360, "Companies worked at"], [3548, 3568, "Designation"], [3570, 3576, "Companies worked at"], [3580, 3600, "Companies worked at"], [3604, 3626, "Years of Experience"], [3628, 3648, "Designation"], [5292, 5312, "Location"], [5269, 5288, "Companies worked at"], [5259, 5266, "Designation"], [5316, 5342, "Years of Experience"], [5455, 5535, "Degree"], [5537, 5573, "College Name"], [5577, 5597, "Location"], [391, 417, "Job Specific Skills"], [836, 852, "Tech Tools"], [1225, 1251, "Job Specific Skills"], [1349, 1357, "Tech Tools"], [1359, 1368, "Tech Tools"], [1374, 1384, "Tech Tools"], [1389, 1411, "Job Specific Skills"], [1414, 1428, "Tech Tools"], [1433, 1436, "Job Specific Skills"], [1439, 1449, "Tech Tools"], [1451, 1457, "Tech Tools"], [1459, 1465, "Tech Tools"], [1470, 1475, "Tech Tools"], [1480, 1497, "Job Specific Skills"], [1499, 1530, "Designation"], [1716, 1736, "Job Specific Skills"], [1533, 1549, "Job Specific Skills"], [1742, 1749, "Tech Tools"], [2829, 2836, "Tech Tools"], [3255, 3262, "Tech Tools"], [3651, 3673, "Designation"], [4318, 4334, "Designation"], [4337, 4363, "Designation"], [5599, 5620, "Graduation Year"], [5630, 5648, "Job Specific Skills"], [5650, 5667, "Job Specific Skills"], [5669, 5689, "Job Specific Skills"], [5691, 5708, "Job Specific Skills"], [5710, 5732, "Job Specific Skills"], [5733, 5754, "Job Specific Skills"], [5756, 5773, "Job Specific Skills"], [5775, 5794, "Job Specific Skills"], [5796, 5803, "Tech Tools"], [5816, 5832, "Tech Tools"], [5834, 5860, "Job Specific Skills"], [5862, 5872, "Tech Tools"], [5874, 5892, "Job Specific Skills"], [5894, 5910, "Job Specific Skills"], [5912, 5919, "Tech Tools"]]}
{"id": 17, "text": "Santosh Ganta\nSenior Systems Engineer - mainframe\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Santosh-Ganta/4270d63f03e71ee8\n\nWilling to relocate to: Bengaluru, Karnataka - hyderbad, Telangana - Chennai, Tamil Nadu\n\nWORK EXPERIENCE\n\nSenior Systems Engineer\n\nInfosys Limited -  Chennai, Tamil Nadu -\n\nFebruary 2014 to Present\n\nDevelopment,Testing,Support\n\nSenior system engineer\n\nInfosys limited\n\nDevelopment,Testing,Support\n\nEDUCATION\n\nB.Tech in Information Technology\n\nGMR Institute of Technology and Management -  Kakinada, Andhra Pradesh\n\n2013\n\nPratibha Junior College\n\n2009\n\nEnglish, Hindi\n\nS.R high School -  Chennai, Tamil Nadu\n\n2006\n\nSKILLS\n\nCA7 (4 years), DB2 (4 years), QMF (4 years), Cobol (4 years), Mainframe (4 years), Cics (4\nyears), Rexx (4 years)\n\nADDITIONAL INFORMATION\n\n• Adopt to any kind of Environment.\n\nTechnical Summary\n\nhttps://www.indeed.com/r/Santosh-Ganta/4270d63f03e71ee8?isid=rex-download&ikw=download-top&co=IN\n\n\n• Tools: ISPF, SPUFI, QMF, File-Aid, MainView, Librarian, CA7, Control-M, Xpeditor\n• Operating System: Windows 7\n• Database: DB2, SQL Server\n• Domain: Retail\n• Packages: MS office\n\n• Secondary Skills: Java Script, HTML, JSP, Java, Oracle 10g, Unix", "meta": {}, "annotation_approver": null, "labels": [[0, 13, "Name"], [14, 37, "Designation"], [51, 71, "Location"], [94, 137, "Email Address"], [163, 183, "Location"], [186, 205, "Location"], [208, 227, "Location"], [246, 269, "Designation"], [271, 286, "Companies worked at"], [290, 309, "Location"], [313, 337, "Years of Experience"], [339, 390, "Designation"], [392, 407, "Companies worked at"], [449, 481, "Degree"], [483, 525, "College Name"], [529, 553, "Location"], [555, 559, "Graduation Year"], [561, 584, "College Name"], [586, 590, "Graduation Year"], [592, 599, "Soft Skills"], [601, 606, "Soft Skills"], [627, 646, "Location"], [662, 665, "Tech Tools"], [677, 680, "Tech Tools"], [692, 695, "Tech Tools"], [707, 712, "Tech Tools"], [724, 733, "Tech Tools"], [745, 749, "Tech Tools"], [761, 765, "Tech Tools"], [965, 969, "Tech Tools"], [971, 976, "Tech Tools"], [978, 981, "Tech Tools"], [983, 991, "Tech Tools"], [993, 1001, "Tech Tools"], [1003, 1012, "Tech Tools"], [1014, 1017, "Tech Tools"], [1019, 1028, "Tech Tools"], [1030, 1038, "Tech Tools"], [1059, 1068, "Tech Tools"], [1071, 1079, "Job Specific Skills"], [1081, 1084, "Tech Tools"], [1086, 1096, "Tech Tools"], [1126, 1135, "Tech Tools"], [1157, 1168, "Tech Tools"], [1170, 1174, "Tech Tools"], [1176, 1179, "Tech Tools"], [1181, 1185, "Tech Tools"], [1187, 1197, "Tech Tools"], [1199, 1203, "Tech Tools"]]}
{"id": 18, "text": "Sarfaraz Ahmad\nAssociate network engineer - TATA Communications Ltd\n\nMuzaffarpur, Bihar - Email me on Indeed: indeed.com/r/Sarfaraz-Ahmad/1498048ada755ac3\n\nCisco Certified Internetwork Associate in Routing & Switching with progressive experience in\ndeployment and administration of Network infrastructure. I am looking for opportunities to further\nimprove my abilities & skills in the field of Network and Security technologies.\n\n➢ Professional 2.5 years of experience in network Implementation & Troubleshooting in local and\nremote environments.\n➢ Well accented with the key IT skills in the domain of LAN/WAN/Network Security, installation &\nconfiguration of IT networks, maintenance & troubleshooting.\n➢ Potential of handling multiple tasks easily and capable of meeting deadlines.\n➢ Possess excellent interpersonal communications and organizational skills.\n\nWORK EXPERIENCE\n\nAssociate network engineer\n\nTATA Communications Ltd -  Pune, Maharashtra -\n\nMarch 2017 to Present\n\nDepartment: CMIP (IP Provisioning)\n\nResponsibilities:-\n➢ Working on Alcatel Migration project for Tata Communications Ltd, responsible for provisioning\nand migrating customer services from Cisco (7200/7600) /Juniper (MX104) to Alcatel provider\nedge router (SR 7750)\n➢ To carry out bulk migration and planned events of enterprise customers to enhance the network\ncapabilities and compatibilities for new technologies.\n➢ Responsible for carrying domestic as well as international customer services migration\nactivities in bulk. Involving all other stakeholders responsible for end to end delivery of customer\nservices, to carry the successful migration.\n➢ Parenting existing business switch ring with Alcatel Routers in combination with cisco routers\nwith the help of field Engineers and synchronizing vlan database on each switch.\n➢ Shut the interface and protocol on Cisco Routers and un-shut the interface on Alcatel routers\nduring Migration activity.\n➢ Comparing the results of pre-check and post-checks and revert the links to cisco that are not\ncoming Up on Alcatel or not compatible with cisco i.e. EIGRP.\n➢ Troubleshooting and resolving the customer issues related to either at routing level such as\nEIGRP, OSPF, BGP, MPLS or at access level Post migration activity.\n➢ Troubleshooting and managing LAN-to-LAN VPN on cisco router.\n➢ Troubleshooting and managing GRE Tunnel on services Getaway.\n➢ Configuration on cisco, juniper, Huawei switch creation Ether channel, VLAN, MSTP, QNQ,\nDot1Q etc.\n➢ Configuring and managing SVI interface on a Layer3 switch to provide inter VLAN routing.\n➢ Configuration Line VTY on customer CISCO router.\n\nhttps://www.indeed.com/r/Sarfaraz-Ahmad/1498048ada755ac3?isid=rex-download&ikw=download-top&co=IN\n\n\n➢ Configuration port security on customer hand up switch port.\n➢ Troubleshooting end user network connectivity issue.\n➢ Performing technical escalations in line with company policy.\n➢ Accountable for customer review meetings weekly basis & giving feedback to the subordinate\nteam and Management.\n➢ Responsible for any internal or external escalation (Customer's End)\n➢ Focus on customer requirements and providing them strategic plans for backups, redundancies\nand minimum downtimes under severe outages.\n\nNetwork engineer\n\nCisco -  Mumbai, Maharashtra -\n\nAugust 2015 to February 2017\n\nResponsibilities/Achievement\n➢ Experience in configuring and troubleshooting of routing protocols and Firewalls\n➢ Backing up and Upgrading software on Cisco router, switches using TFTP server\n➢ Troubleshooting end user network connectivity issue\n➢ Experience in installing and configuring DHCP services on Cisco Router and Switch.\n➢ Configuring and troubleshooting Ether channel/Port-channel issue\n➢ Configuring and troubleshooting VLAN, VTP and STP.\n➢ Configuring and managing BPDU guard and Filter on a switch\n➢ Providing port security on switch interface\n➢ Dealing with Dynamic routing protocol Such as EIGRP, OSPF and BGP\n➢ Configuring and managing SVI interface on a Layer3 switch to provide inter VLAN routing\n➢ Worked on networks with WAN protocols such as HDLC and PPP.\n➢ Worked on Gateway Redundancy protocols such as HSRP on Cisco Router.\n➢ Implemented traffic filters using Standard and Extended access-lists, Distribute-Lists, and\nRoute Maps.\n➢ Installation, upgradation, Backup and restoration of checkpoint firewall\n➢ Implemented rules, NAT, URL filtering on checkpoint firewall.\n➢ Configuring and managing Rules and NAT on firewalls.\n\nEDUCATION\n\nBachelor of Commerce in Commerce\n\nR.D.S COLLEGE -  Bihar Sharif, Bihar\n\nJuly 2013\n\nH.S.C\n\nDr. R.M.L.S COLLEGE -  Bihar Sharif, Bihar\n\n2009\n\nS.S.C in G.N.H.S\n\nBihar School Examination Board -  Patna, Bihar\n\n\n\n2007\n\nSKILLS\n\nBGP (2 years), EIGRP. (2 years), OSPF (2 years), security (2 years), vlan (2 years)\n\nADDITIONAL INFORMATION\n\nTechnical Skills\n\nNetwork\n➢ Switching: CST, PVST, PVST+, RSTP and MST, VLAN, Private VLAN, Trucking, Inter-VLAN Routing,\nSPAN/RSPAN, Ether channel or Port Bundling using static and dynamic protocol (PAgP and LACP)\n➢ FHRP: HSRP (V1 & V2), VRRP and GLBP.\n➢ Routing: Configuration and Troubleshooting Layer 3 Routing protocols such as Static routing,\nfloating routing, RIP, EIGRP, OSPF, BGP, VRF-Lite and MPLS.\n\nData Security\n➢ Checkpoint Firewall: FW Monitor, TCP-Dumps, Rules, NAT, IPsec VPN, Backup & Restore, URL\nfiltering, Installation, Migration, Upgradation, ClusterXL etc.\n➢ ASA Firewall: Access Control Lists with object, Address Translation, IPsec VPN, LAN-to-LAN VPN,\nHigh Availability in Active-Active & Active-Standby Mode, Redundant interface, SLA Monitoring,\nSecurity Contexts.\n➢ IOS Firewall: Standard and extended access list and Zone Based Firewall (ZBF), DMVPN, IPsec,\nIPsec over GRE.\n\nServices\n➢ Services: ARP, GARP, RARP, DNS, DHCP, FTP, TFTP, TCP, ICMP, TELNET, SSH, HTTP, HTTPS. IP\nSLA, NTP, SNMP\n\nAreas of Interest\n\n➢ To perform troubleshooting and network performance planning.\n➢ To implement enterprise level network architectures.", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [15, 41, "Designation"], [44, 67, "Companies worked at"], [69, 87, "Location"], [110, 154, "Email Address"], [445, 454, "Years of Experience"], [879, 905, "Designation"], [907, 930, "Companies worked at"], [934, 951, "Location"], [955, 976, "Years of Experience"], [3227, 3243, "Designation"], [3245, 3250, "Companies worked at"], [3254, 3273, "Location"], [3277, 3305, "Years of Experience"], [4468, 4488, "Degree"], [4492, 4515, "College Name"], [4519, 4538, "Location"], [4551, 4556, "Degree"], [4558, 4577, "College Name"], [4581, 4600, "Location"], [4602, 4606, "Graduation Year"], [4608, 4613, "Degree"], [4617, 4638, "College Name"], [4660, 4672, "Location"], [4676, 4680, "Graduation Year"], [156, 217, "Degree"], [249, 304, "Job Specific Skills"], [394, 427, "Job Specific Skills"], [472, 494, "Job Specific Skills"], [497, 512, "Job Specific Skills"], [603, 606, "Tech Tools"], [607, 610, "Tech Tools"], [611, 627, "Job Specific Skills"], [805, 818, "Soft Skills"], [819, 833, "Soft Skills"], [838, 852, "Soft Skills"], [4540, 4549, "Graduation Year"], [4739, 4747, "Job Specific Skills"], [4759, 4763, "Job Specific Skills"], [4705, 4710, "Job Specific Skills"], [4690, 4693, "Job Specific Skills"], [4723, 4727, "Job Specific Skills"], [4827, 4836, "Job Specific Skills"], [4838, 4841, "Tech Tools"], [4843, 4847, "Tech Tools"], [4848, 4854, "Tech Tools"], [4856, 4860, "Tech Tools"], [4865, 4868, "Tech Tools"], [4870, 4874, "Tech Tools"], [4875, 4888, "Tech Tools"], [4890, 4898, "Tech Tools"], [5054, 5061, "Job Specific Skills"], [5081, 5096, "Job Specific Skills"], [5208, 5221, "Job Specific Skills"], [5224, 5243, "Job Specific Skills"], [5245, 5255, "Tech Tools"], [5257, 5266, "Tech Tools"], [5379, 5391, "Job Specific Skills"], [5591, 5603, "Tech Tools"], [5914, 5952, "Job Specific Skills"]]}
{"id": 19, "text": "Senthil Kumar\nSenior Technical Lead - HCL Cisco\n\n- Email me on Indeed: indeed.com/r/Senthil-Kumar/d9d82865dd38d449\n\n• Result oriented Networking Professional with 11 years' experience in\ntesting network products diversified skills includes testing ACI SDN fabric,\nUCSD orchestration, Routing and Switching: design, deploy and\ntroubleshoot customer network\n• Extensive knowledge of Testing and Deploying network test plans,\nprotocols, Strategies and Lab Setup.\n• In-depth knowledge of ACI fabric, Routing and switching protocols\n• Proven communication skills and the ability to troubleshoot and solve\nnetwork issues\n• Skilled in providing support in various networking testing activities to\nprovide effective support and handling of customer escalation and for\nensuring timely and effective support\n\nWORK EXPERIENCE\n\nSenior Technical Lead\n\nHCL Cisco -  Bengaluru, Karnataka -\n\nOctober 2015 to Present\n\nProject Title: Feature testing and Deployment on ACI fabric for Cisco IT and UCSD\n\n• ACI Fabric deployment in single POD and Multi-POD environment\n• Deployed Cisco ACI Multi-Site Controller with ACI fabric for Testing stretched\nEPG's between Data centers\n• Deployed Cisco IT replica Setup in Local Lab\n• Handling Cisco IT customer network and pre-deployment of image testing before handing to\nCISCO IT Customer\n• End to end testing of Cisco IT test bed, upgrade and downgrade of Various\nimages\n• Configured SDN Controller (APIC) Decommissioned and Commissioned in Cluster\n• Configured UCS manager\n• APIC upgrade downgrade, Cisco Nexus 9k leaf spine downgrade upgrade testing.\n• Bring up ACI leaf and spine fabric with UCS fabric interconnect\n• Implementation of VMware DVS and Cisco AVS in Esxi host and tested the AVS\nfunctionality\n• Tested redundancy testing on leaf, spine and fabric interconnect\n• Communicated inconsistencies between System Specifications and Test Results\nto development and/or analyst team\n• Involved in Defect Review meetings escalating and prioritizing issues effectively\n• Experience in implementation of VMware Infrastructure 5.5 / 6.0\n• Managed Virtualization environment using VMware EXI 5.5/6.0, Vcenter 5.5/6.0\n• Creating and Managing virtual machines and Templates\n\nhttps://www.indeed.com/r/Senthil-Kumar/d9d82865dd38d449?isid=rex-download&ikw=download-top&co=IN\n\n\n• Migration of VM using Vmotion between Clusters\n• Configured Windows iSCSI SAN and integrated to VMware\n• Testing Cisco APIC with UCSD integration with Multi Site Controller\n\nTechnical Lead\n\nAricent Cisco -\n\nJuly 2012 to September 2015\n\nClient Cisco Systems\nProject Title: Feature testing and Deployment on CISCO CARRIER PACKET\nTRANSPORT\n\n• Worked on EFT with Bharti Telecom and China Telecom Demo for 9.7 rings features\n• Conducting the Dev-test activities for CTP for Rings 9.7 feature. Bring up new setup,\n• Written test cases for CPT 50 Ring feature to test REP and also to validate MPLS TP and P2MP\nEVC\n• Tested MPLS TP, single segment and multi-segment Pseudowire, P2MP EVC, REP\nfeatures.\n• Tested EVC P2MP type on that carried out Single tag, Double tab of 802.1q and Dot1ad\n• Based on Cisco Resilient Ethernet Protocol feature CPT 9.7 testing is carried out.\n• Created open and closed end segment for REP topology and tested Rings CTP 9.7 feature\n• Tested Mac learning, aging, flushing and limiting features\n• Tested multiple frame size and JUMBO frame verification with MPLS TP\n• Tested Link Aggregation verification with MPLS TP\n• Tested Memory Backup verification with fully loaded CPT configuration\n• Hardware redundant Plug in and Plug out Circuit Pack test and High availability testing\nfor CTP 600\n• Network Protection verification testing with MPLS TP working LSP and Protected LSP\n• Tested DHCP Snooping\n• Tested EVC P2MP type on that carried out Single tag, Double tab of 802.1q and Dot1ad\n• Tested Mac learning, aging and limiting features\n• Supporting customer queries for the product\n\nTechnical Lead\n\nAricent Group -\n\nDecember 2011 to June 2012\n\nClient KeyMile Germany\n\n• Conducting the Dev-test activities for Eth-Sys series switch. Bring up new setup,\npreparation of FT test plan, derive test estimate for the Release.\n• Tested the various images submitted by release -ops team\n• Carried out manual testing on Vlan 802.1Q ( Mac aging, Mac flooding, Tag and untag)\nusing SNMP mib browser and Aricent ISS\n• Written test cases for RSTP and MSTP features and tested it.\n• Debug the failures by configuring the test bed manually and filing the defects\n\nTechnical Lead (HCL)\n\n\n\nClient Cisco Systems -\n\nJanuary 2010 to December 2011\n\nCisco 12000 Gigabit\nSwitching Router)\n\n• Performed the Role of Technical lead for a team member size of 4\n• Test the various images submitted by release -ops team\n• Carried out manual testing of Connection Server (HSRP) and OSPF features to verify\nthe functionality\n• Tested OSPF point to point and Broadcast network\n• Bring up new setup, prepare test plan, derive test cases and execute the test cases\n• Debug the failures by configuring the test bed manually and filing the defects\n• Interact & co-ordinate with Dev-test and Development team to review the CFD's and script\ncoverage.\n• Find defects through manual testing and verify the defects\n• Analyze both internal and externally found defects and come up with a plan to reduce the\ndefects missed out in the scripts.\n\nTest Lead (HCL)\n\nClient Cisco Systems -\n\nMay 2007 to December 2011\n\nVirtualization)\n\n• Performed the Role of lead for a team member size of 5\n• Image testing on ACE module and ACE 4710 Appliance.\n• Coordinating with onsite/offsite teams in resolving the defects found when performing\nTesting.\n• Given white paper & Presentation document in ACE High Availability and Securing\nACE 4710 with ACS server\n• Test Plan writing, reviewing and planning test strategies.\n• Peer to Peer Reviews to avoid Post Delivery Defects\n• Lab Test Bed creation and Infrastructure Maintenance\n• Configured High Availability of ACE Module, ACE Appliance and ANM Servers\n• Configured End to End traffic Load balancing of Data center using the ACE 4710, ACE\nmodule and CSM devices\n• Deployed different topologies NAT, Routed and Bridged for End to End traffic Load\nbalancing of Data center\n\nLab Administrator\n\nHCL Cisco -  Chennai, Tamil Nadu -\n\nMay 2003 to June 2007\n\nKey responsibility is to perform CISCO Network LAB Administration\n\n\n\nEDUCATION\n\nB.Tech in Computer Science in Computer Science\n\nDr MGR University Chennai -  Chennai, Tamil Nadu\n\nSKILLS\n\nCPT (3 years), Data Center (4 years), Ethernet (3 years), testing (10+ years), Virtualization (7\nyears)\n\nLINKS\n\nhttp://www.linkedin.com/in/senthil-kumar-9600101b\n\nADDITIONAL INFORMATION\n\nSkills\n\nVirtualization VMware-VSphere, DVS, Cisco AVS, Cisco\nACE\n\nSDN Cisco Application Centric Infrastructure\n(ACI)\n\nL2/L3 Protocols OSPF, EIGRP, MPLS, VLAN, VXLAN,\nSTP, RSTP, VPC\n\nMetro Ethernet Protocols Carrier Ethernet protocols EVC, MPLS TP,\nPsudoWire, REP\n\nTraffic Generation Tools Traffic tools IXIA Iexplore and Smart Bit\n\nTesting Tools Wireshark and MIB Browser\n\nNetwork Products Cisco SDN ACI Fabric\nVMWare 5.5 and 6.0\nCisco UCSD Orchestration\nCarrier Ethernet devices (CPT […]\nCisco Metro Ethernet 2600\nCisco routers GSR 12000, 7600\nData center Virtualization using the ACE\nNexus 5k and ACI 9K switches\nCat 6500 and 3550 Switches\n\nhttp://www.linkedin.com/in/senthil-kumar-9600101b", "meta": {}, "annotation_approver": null, "labels": [[0, 13, "Name"], [14, 35, "Designation"], [38, 47, "Companies worked at"], [71, 114, "Email Address"], [163, 171, "Years of Experience"], [816, 837, "Designation"], [839, 848, "Companies worked at"], [852, 872, "Location"], [876, 899, "Years of Experience"], [3905, 3919, "Designation"], [3921, 3934, "Companies worked at"], [3938, 3964, "Years of Experience"], [4470, 4490, "Designation"], [4494, 4514, "Companies worked at"], [4518, 4547, "Years of Experience"], [5322, 5337, "Designation"], [5339, 5359, "Companies worked at"], [5363, 5388, "Years of Experience"], [6191, 6209, "Designation"], [6210, 6215, "Companies worked at"], [6219, 6238, "Location"], [6242, 6263, "Years of Experience"], [6345, 6371, "Degree"], [6375, 6418, "College Name"], [6422, 6441, "Location"], [118, 133, "Soft Skills"], [134, 157, "Designation"], [187, 211, "Job Specific Skills"], [240, 262, "Job Specific Skills"], [264, 282, "Job Specific Skills"], [284, 305, "Job Specific Skills"], [537, 550, "Soft Skills"], [577, 589, "Job Specific Skills"], [6298, 6330, "Job Specific Skills"], [6466, 6477, "Job Specific Skills"], [6489, 6497, "Job Specific Skills"], [6509, 6516, "Job Specific Skills"], [6530, 6544, "Job Specific Skills"], [6451, 6454, "Job Specific Skills"], [6646, 6660, "Job Specific Skills"], [6661, 6667, "Tech Tools"], [6668, 6675, "Tech Tools"], [6677, 6680, "Tech Tools"], [6682, 6691, "Tech Tools"], [6708, 6754, "Job Specific Skills"]]}
{"id": 20, "text": "Shabnam Saba\nOffshore SAP CRM Functional Consultant\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Shabnam-Saba/dc70fc366accb67f\n\nTo understand the organization and to identify its needs and correlate them with my goals so as\nto apply myself to responsibility with total dedication and dynamism so as to grow along with\nthe organization.\n\nPast Organization: Tata Consultancy Services as SAP CRM functional consultant (July 2014 - Jan\n2016), SAP Labs, India (from Feb 2011-June 2014), Cognizant Technology Solutions (May 2010-\nOctober 2010)\n\nWORK EXPERIENCE\n\nOffshore SAP CRM Functional Consultant\n\nSAP AG -\n\nJuly 2014 to January 2016\n\nDescription: The project involves SAP IT support with respect to AGS and SAP Cloud Process .SAP\nIT support involves handling incident and service requests from SAP CRM users and customers\nacross the globe.\n\nResponsibilities:\n• Problem Analysing and Handling Tickets of SAP CRM (AGS and Cloud process)\n• Handling incident and providing solution with in SLA time frame.\n• Configuring the system to resolve the issues.\n• Worked on changes to the functional specifications required as per the clients\nrequirement.\n• Preparing test cases and taking approval from client before moving new changes to production.\n• Coordinated with the technical team in solving the tickets.\n\nQuality Engineer\n\nSAP Labs -\n\nJuly 2002 to January 2014\n\nResponsibilities:\n\nWorked in configuring and testing different areas of Framework:\n\n• Nav. Bar profile\n• Role Config key\n• Business Roles and UI Config tool\n• Creating A Business Role\n\nFunctional Consultant\n\nhttps://www.indeed.com/r/Shabnam-Saba/dc70fc366accb67f?isid=rex-download&ikw=download-top&co=IN\n\n\nSAP Labs -\n\nMarch 2012 to June 2013\n\nDescription:\n\nMobile Client Technology is client technology designed for Microsoft Windows-based, occasionally\nserver-connected CRM field applications. These applications offer a rich function set, such as\nSAP CRM Mobile Sales and SAP CRM Mobile Service. Mobile Sales for SAP CRM allows users to\naccess all their accounts, contacts, leads, opportunities, and activities from a single point. All\nrelationships between these business objects are automatically mapped in the application, which\nallows for fast and easy navigation.\n\nResponsibilities:\nTesting the various business objects:\nOpportunities, Quotation, Sales orders, Activities.\nWorked on system set up (creation of sites, subscriptions), opportunity, quotation, order\nmanagement.\n\n5. CRM Sales ( CRM 7.01, CRM 7.02, CRM 7.03) -Feb '2011-June 2014\n\nClient: SAP Labs India\nRole: Functional Consultant\nDescription:\n\nThis area in SAP Customer Relationship Management (SAP CRM) enables you to manage your\nsales cycle, starting with creating appointments and business opportunities, through to managing\nsales orders, contracts, and invoicing. It also allows you to organize and structure your sales\nterritories according to your business requirements.\n\nResponsibilities:\nWas involved in customizations and testing of:\n\n• Territory management\n• Account and contact management\n• Activity management, visit Planning\n• opportunity planning, opportunity management\n• quotation and order management\n• Pricing\n• Organizational Management and Billing\n• Customizations of Surveys\n\nEDUCATION\n\nB.E in CSE\n\nPadmanava College of Engineering\n\n2009\n\nSt. Joseph's convent school\n\n\n\n2003\n\nSKILLS\n\nCRM (10+ years), CUSTOMER RELATIONSHIP MANAGEMENT (10+ years), TESTING (10+ years),\nUI (10+ years), USER INTERFACE (10+ years)\n\nADDITIONAL INFORMATION\n\nOther Skills:\n\nCRM Middleware:\n\n• Worked on downloading initial and delta download between ECC and CRM,\nCRM and MSA.\n• Monitoring middleware data between ECC and CRM.\n• Monitoring Queues and error handling of BDocs.\n• Worked on subscriptions and Publications, Replication Objects.\n\nSAP ECC Sales and Distribution:\n\n• Strong Understanding of SAP Customizing and Detailed knowledge of core SD functions such\nas Item Categories, Text Determination, output determination, taxes\n• Customer Master and Material Master data, item proposal, variant configuration, Product\nhierarchy\n• Sales document types (Orders, Returns, CMR, DMR)\n• Billing and Pricing concept, worked on bill plans\n• Sales Enterprise structure\n• Copy control, Incompletion log, Material listing and Exclusion\n• Partner determination, Customization of Account groups\n• Worked with cross-functional teams during development and configuration activities to ensure\nimpact to other SAP modules and processes is considered.\n\nSAP CRM Skills:\n• CRM Sales and Service order management (Extensive experience in configuration for Text\nDetermination Procedures, Status Profile, Org. Data Determination and Transaction Types)\n\n• SAP Fiori (creation of test data and application testing in different landscapes including browser\ntesting)\n• SAP Mobile Sales (creation of test data and system set up)\n• Well versed in base customizing and WEBUI configuration along with CRM Tables.\n• Hands on experience in CRM middleware (creation of sites, subscription, publication, checking\nbdocs, idocs, download objects) and trouble shooting.\n• In depth knowledge in CRM One Order Framework.\n• Extensive experience in the configuration of Web UI for multiple Business Roles, Actions,\nNavigation Bar Profile.\n\nCompetencies and Skills:\n\n\n\n• CR-100 (BP, Product, Org model, Partner/Text determination, Transaction type, Item categories,\nTerritory)\n• CR-300\n• CRM Mobile Sales\n• CRM Middleware Basics\n• Basic Debugging (ABAP)\n• Knowledge on Idocs (set-up, filtration, reprocessing)\n\nTesting:\n• Experience in SAP CRM Module (sales and service) with testing\n• Good understanding of application testing process.\n• Coordinated with SAP CRM technical team members to understand testing functionalities\n• Written Test cases for different CRM modules (Account Management, opportunity, activity\nmanagement, sales order creation) Sales & Service.\n• Managed issue logs / defects and subsequent closures.\n\nBasic Debugging:\nKnowledge of ABAP Debugging, Basic ABAP (Tables, data elements, Working with Table\nMaintenance Generator, Creating a Transaction Variant, Creating an SAP Area Menus, Find the\nSAP IMG Customizing Activity from the Table Name, basic knowledge of Smartforms)", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [53, 73, "Location"], [96, 138, "Email Address"], [368, 393, "Companies worked at"], [397, 426, "Designation"], [428, 448, "Years of Experience"], [451, 459, "Companies worked at"], [461, 466, "Location"], [468, 491, "Years of Experience"], [494, 524, "Companies worked at"], [526, 548, "Years of Experience"], [568, 606, "Designation"], [26, 51, "Designation"], [608, 614, "Companies worked at"], [618, 643, "Years of Experience"], [1314, 1330, "Designation"], [1332, 1340, "Companies worked at"], [1344, 1369, "Years of Experience"], [1556, 1577, "Designation"], [1677, 1685, "Companies worked at"], [1689, 1712, "Years of Experience"], [2550, 2571, "Designation"], [2529, 2543, "Companies worked at"], [2500, 2519, "Years of Experience"], [3250, 3253, "Degree"], [3257, 3294, "College Name"], [3296, 3300, "Graduation Year"], [3347, 3350, "Job Specific Skills"], [3364, 3396, "Soft Skills"], [3410, 3417, "Job Specific Skills"], [3431, 3433, "Job Specific Skills"], [3447, 3461, "Job Specific Skills"], [3514, 3517, "Job Specific Skills"], [3518, 3528, "Job Specific Skills"], [3840, 3843, "Tech Tools"], [4075, 4095, "Job Specific Skills"], [4178, 4204, "Job Specific Skills"], [4480, 4487, "Tech Tools"], [4677, 4686, "Tech Tools"], [4787, 4803, "Tech Tools"], [5622, 5637, "Job Specific Skills"], [5471, 5476, "Tech Tools"], [5433, 5448, "Job Specific Skills"], [5450, 5454, "Tech Tools"], [5925, 5940, "Job Specific Skills"], [5955, 5959, "Tech Tools"], [5960, 5969, "Job Specific Skills"], [5977, 5981, "Tech Tools"], [6092, 6095, "Tech Tools"]]}
{"id": 21, "text": "Shaheen Unissa\nSAP ABAP Consultant\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Shaheen-Unissa/\nc54e7a04da30c354\n\n• Having 8 years of experience in IT industry as SAP ABAP Developer. She\nwas involved in multiple projects viz. 3 Live Cycle Implementations of SAP and\nworked on Rollout Project (Federal Mogul) . Worked in end to end implementation\nproject MPT (Mormugao Port Trust), GOA at client location and also offshore at pune\nfor MPT.\n• She has worked on end to end implementation project with client Etisalat on SRM\nserver.\n• She has worked for GE (General Electric Company) Support Project and Acelor Mittal\nend to end implementation project at offshore Hyderabad.\n• She has worked for NNIT (Novo Nordisk) and M.R.S oil and gas company at offshore\nHyderabad. She has worked on Windows 98, 2000, windows vista platforms.\n• Worked for MPT (Mormugao Port Trust) at client location GOA for 7 months which is\nAn end to end implementation so got an good exposure in port operating system.\n\nOrganization Designation Duration\nTech Mahindra Limited, Hyderabad, India. Sr. Software Engineer May/2008 - May/2016\n\nWORK EXPERIENCE\n\nSAP ABAP Consultant\n\nAcelorMittal -\n\nMarch 2015 to April 2016\n\nProject Description:\nArcelorMittal is the world's leading steel and mining company. Guided by a philosophy to produce\nsafe, sustainable steel, it is the leading supplier of quality steel products in all major markets\nincluding automotive, construction, household appliances and packaging. ArcelorMittal is present\nin 60 countries and has an industrial footprint in 19 countries.\n\nContribution:\n* Developed reports both interactive and classical.\n* Developed interactive reports.\n* Developed LSMW to upload material master data.\n* Developed LSMW to upload vendor master data.\n* Developed LSMW to upload Purchase info records.\n* Developed Smartform to print sales order confirmation.\n* Developed Smartform to print purchase order details.\n* Developed Smartform to print Invoice details.\n\nHandled error in workflow.\n\nhttps://www.indeed.com/r/Shaheen-Unissa/c54e7a04da30c354?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Shaheen-Unissa/c54e7a04da30c354?isid=rex-download&ikw=download-top&co=IN\n\n\nSAP Modules/ Tools:\n• PP, MM and SD.\n\nConsultant\n\nSAP ABAP -  BADI, MADHYA PRADESH, IN -\n\nOctober 2012 to February 2015\n\nProject Description:\nEtisalat is the Middle East's leading telecommunications operator and one of the largest\ncorporations in the six Arab countries of the Gulf Cooperation Council, with a market value of\napproximately Dh81 billion (US$22 billion) and annual revenues of over Dh32.9 billion (US$9\nbillion)\n\nA multinational, blue-chip organization, Etisalat has operations in 15 countries in the Middle East,\nAfrica and Asia. Nearly 42, 000 people are directly employed by the company.\n\nEtisalat's international acquisition program began in earnest in 2004 when it won the second\nmobile license - the first third-generation (3G) mobile license - in Saudi Arabia. Since then, the\ncompany has witnessed rapid expansion that has positioned it as one of the world's fastest\ngrowing operators, with subscribers rocketing around 3, 475 per cent from four million in 2004\nto 141 million in2013.\n\nContribution:\n• Developed class using methods to increase the RFx number ranges based on opco code and\nadding prefix for portal system in SRM server.\n• Developed class with methods to get the details of supplier profile and updating the same in\ncustom table and generating a email notification to the vendor manager using web dynpro for\nportal system in SRM server.\n• Developed a smartform for purchase order in SRM server.\n• Modified standard smartform for Bid bond invitation form \"BBP_ BID_INVITATION\" adding cover\nletter as first page.\n• Implemented a BADI \"BBP_OUTPUT_CHANGE_SF\" to trigger the Bid bond smartforms based on\nopco code.\n• Implemented a BADI \"BBP_DOC_CHANGE_BADI\" to change the purchase organization and\npurchase group based on opco code.\n\n• Developed a class to download multi excel sheet to transfer the data from SRM to CLM on\npresentation server as well as in CLM server.\n\n• Developed a class for generating mail for RFX responses rejected.\n\nConsultant\n\nSAP ABAP -\n\nDecember 2011 to March 2012\n\nProject Description:\n\n\n\nM.R.S. Oil and Gas Company Limited distribute and supplies petroleum products. It offers\nkerosene and gas oils. The company was founded in January 1998 and is based in Lagos, Nigeria.\nM.R.S. Oil and Gas Company Limited operate as a subsidiary of MRS Group. The company has\nacquired a total of 426 fuel stations spread across the country and plans were on course toward\nbranding more filling stations.\n\nContribution:\n• Developed smartforms for Bank Wire Transfers for MRS Oil Nigeria. (a) Payment made in\nNigerian Naira - Local Payment. (b) Payment made in USD to Local Vendors. (c) Payment made\nin Foreign Currency to Foreign Vendor.\n• Developed a smartform to print the Request for Quotation as per client Requirement.\n• Developed a smartform for Physical Inventory is used to conduct physical stock counts in\nstorage locations in order to check book inventory to physical inventory (MI01)\n• Developed a smartform for Operational Work Clearance Document in work clearance\nmanagement.\n• Developed smartform for work permit.\n\nSAP Modules/ Tools:\n• PP, MM, PM, SD and IS-Oil.\n\nConsultant\n\nSAP ABAP -\n\nJuly 2011 to November 2011\n\nProject Description:\nArcelorMittal is the worlds leading steel company, with operations in more than 60 countries.\nArcelorMittal is the leader in all major global steel markets, including automotive, construction,\nhousehold appliances and packaging, with leading technology, as well as sizeable captive\nsupplies of raw materials and outstanding distribution networks. In 2010, ArcelorMittal had\nrevenues of $78.0 billion and crude steel production of 90.6 million tonnes, representing\napproximately 8 per cent of world steel output.\n\nContribution:\n• Developed Quality Claims Report for purchase orders for which whole cycle is done and also\nat sales distribution side.\n• Developed a Zbapi for portal to display the data in portal from different tables.\n\nSAP Modules/ Tools:\n• PP, MM, PM and SD.\n\nConsultant\n\nSAP ABAP -  Bangalore, Karnataka -\n\nOctober 2010 to June 2011\n\nGE employees over 13, 000 people in India. It exports over $1 billion in products and services.\n\nContribution:\n\n\n\n• Handling HPSD tickets as well as GMR tickets.\n• Modified existing production summary report which gives shop performance for past fiscal\nweeks. It also gives data regarding the current shop situation and expected outcome for the\nupcoming quarter.\n• Modified induction slot module pool program to display the comments entered in production\nstatus report for a given project.\n• Modified life limited parts smart form which gives the list of LLP information for each Engine. This\nform compiles the Outgoing Shop Visit Documentation Pack for the customer for each Engine/\nModule shop visit and the audience will be outgoing customer records business users from the\nDocumentation department.\n• Modified cost accumulation invoicing process to upload file for billing request.\n\nSAP Modules/ Tools:\n• SAP ECC 6.0\n• PM\n\nConsultant\n\nSAP ABAP -\n\nMay 2010 to September 2010\n\nProject Description:\nNovo Nordisk is a Healthcare company and a world leader in diabetes care. Since 1923, Novo\nNordisk has been committed to providing the best possible solutions for people with diabetes and\ntheir caregivers. Novo Nordisk products and services help millions of people worldwide better\nmanage their condition with the broadest diabetes product portfolio in the industry, including the\nmost advanced products within the area of insulin delivery systems, other treatment areas like\nhaemophilia and chronic inflammation. Novo Nordisk is a world leader in diabetes care.\n\nContribution:\n• Reviewing and auditing the enhancements, reports, bi developments, interfaces and forms and\nmigrating them from Lotus notes to Solution manager.\n• Used an USER EXIT SAPMF02K in program ZXF05U01 for validating vendor tax number range.\n• Completing the Technical Specifications.\n• Creation of Handling over documentation\n\nConsultant\n\nSAP ABAP -  Marmagao, Goa -\n\nOctober 2008 to April 2010\n\nProject Description:\nMormugao Port Trust (MPT), GOA is end to end implementation project. MPT is one of the leading\nport in India from 1962.Mormugao Port, GOA is the premier iron ore exporting Port of India\nwith an annual throughput of around 27.33 million tonnes of iron ore traffic. Though ore is the\npredominant cargo, there has been a steady increase in liquid bulk and general cargo traffic ever\nsince it's joining the ranks of the Major Ports of India.\n\nContribution:\n\n\n\n• Developed Customized screens for Container Maintenance for Port Operating System (POS)\nbased on service order number and vessel number, fetching the Import containers details from\nIW33 for this containers estimating and re-estimating for the new containers added and creating\nthe sales order with reference to the quotation number and extension of sales order bapi has\ndone.\n• Developed Customized screens for Goods Movement (IN) for Port Operating System (POS) goods\ninward has to be done for MOHP plot and displaying the Stock Details.\n• Developed customized screens for Barge unloading for MOHP (Mechanical ore hydraulic power)\nas per MPT users as well as Agents of MPT.\n• Development of ALV Grid report to display the Warranty of the Equipment which is going to\nexpire in the given period or month.\n• Developed Classical report to display the Vessel Shifted from different Berths during the\nTurnaround period.\n• Developed a smart form to display the arrival status of vessels for the given period of time.\n• Implemented User Exit MV45AFZZ for SD module to calculate the Penal rent based on days\nof start of period and billing to be done for every 10 days Routine - Created Routine-920 in SD\nmodule RV61A920 to calculate the % as per number of days\n• Created a batch input program to upload Plant Maintenance Task List for long text tcode IA05.\n• Property: Used Standard function module 'BAPI_RE_PR_CREATE' to transfer Property master\ndata from legacy system to R/3 system for Real Estate through 'REBDPR'. -Building: Used\nStandard function module 'BAPI_RE_BU_CREATE' to transfer Building master data from legacy\nsystem to R/3 system for Real Estate through 'REBDBU'. -Contract: Used Standard function\nmodule 'BAPI_RE_CN_CREATE' to transfer Contract master data from legacy system to R/3\nsystem for Real Estate through 'RECN'. -Rental object: Used Standard function module\n'BAPI_RE_RO_CREATE' to transfer Rental Object master data from legacy system to R/3\nsystem for Real Estate through 'REBDRO'. -Business Partner: Used Standard function module\n'BAPI_BUPA_CREATE_FROM_DATA' to Create partners and 'BAPI_BUPA_ROLE_ADD_2' to create\nRolls for the partners from the file on legacy system to R/3 system for Real Estate.\n\nSAP Modules/ Tools:\n• SAP ECC 6.0\n• SD, MM, FICO, PM, RE, POS\n\nSAP ABAP Consultant\n\n8 Federal Mogul -\n\nOctober 2007 to September 2008\n\nProject Description:\nFederal-Mogul Corporation is an innovative and diversified $6.3 billion global supplier of quality\nproducts, trusted brands and creative solutions to the automotive, light commercial, heavy-duty\ntruck, off-highway, agricultural, marine, rail and industrial markets. The 45, 000 people of Federal-\nMogul located in 35 countries drive excellence in all they do.\n\nContribution:\n• Development of ALV Hierarchical report to display the backorders that are cancelled by\ncustomers and updates the database as \"BC\". An ALV report to display the Monthly Earned rebate\naccrued by month for all the active programs in SAP for the Sales organizations selected.\n• Creation of the BDC for loading Return orders cancelled by customers in Sales order.\n\n\n\n• Developed smartform for Certificate of origin and export invoice.\n• Modified standard smartform of PO layout change\n\nSAP Modules/ Tools:\n• SD, MM, PP, OTC\n\nEDUCATION\n\nB-TECH\n\nJAWAHARLAL NEHRU TECHNOLOGICAL UNIVERSITY\n\n2005\n\nADDITIONAL INFORMATION\n\nSAP Skills\n\n• Classical and Interactive Reports\n• BDC's, LSMW, BAPI,\n• BADI, User Exits\n• Data Dictionary objects\n• Dialog Programming\n• ALE/ IDOCS\n• SAP Scripts, Smart Forms\n• Possess hands on skill set on SD & MM modules.\n• Experience on Unit testing (UT), functional testing (FT), UAT scripts and regression\nTesting\n• Worked on SAP webdynpro applications.\n• Worked on SRM server.\n• Worked on Abap Workflow.", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [36, 56, "Location"], [15, 34, "Designation"], [79, 124, "Email Address"], [135, 142, "Years of Experience"], [175, 193, "Designation"], [1036, 1057, "Companies worked at"], [1059, 1075, "Location"], [1077, 1098, "Designation"], [1099, 1118, "Years of Experience"], [1137, 1156, "Designation"], [1158, 1170, "Companies worked at"], [1174, 1198, "Years of Experience"], [2250, 2270, "Designation"], [2274, 2294, "Location"], [2302, 2331, "Years of Experience"], [4185, 4205, "Designation"], [4209, 4236, "Years of Experience"], [4262, 4296, "Companies worked at"], [5337, 5357, "Designation"], [5361, 5387, "Years of Experience"], [6185, 6205, "Designation"], [6209, 6229, "Location"], [6233, 6258, "Years of Experience"], [7187, 7207, "Designation"], [7211, 7237, "Years of Experience"], [8160, 8180, "Designation"], [8184, 8197, "Location"], [8201, 8227, "Years of Experience"], [10992, 11011, "Designation"], [11015, 11028, "Companies worked at"], [11032, 11062, "Years of Experience"], [11993, 11999, "Degree"], [12001, 12042, "College Name"], [12044, 12048, "Graduation Year"], [270, 273, "Tech Tools"], [332, 357, "Job Specific Skills"], [471, 496, "Job Specific Skills"], [795, 805, "Tech Tools"], [813, 826, "Tech Tools"], [1596, 1613, "Job Specific Skills"], [1648, 1677, "Job Specific Skills"], [4690, 4700, "Job Specific Skills"], [7169, 7180, "Tech Tools"], [8091, 8115, "Job Specific Skills"], [10951, 10962, "Tech Tools"], [10929, 10932, "Tech Tools"], [12074, 12077, "Tech Tools"], [12088, 12121, "Job Specific Skills"], [12190, 12208, "Job Specific Skills"], [12211, 12221, "Tech Tools"], [12224, 12235, "Tech Tools"], [12314, 12331, "Job Specific Skills"], [12333, 12356, "Job Specific Skills"], [12358, 12369, "Job Specific Skills"], [12374, 12392, "Job Specific Skills"], [12405, 12431, "Tech Tools"], [12469, 12473, "Tech Tools"]]}
{"id": 22, "text": "Sharan Adla\n- Email me on Indeed: indeed.com/r/Sharan-Adla/3a382a7b7296a764\n\n• Having 4yrs. of solid work experience in designing experiences for Digital (Web, Mobile) and\nPrint media\n• Hands-on experience with tools such as Adobe Photoshop, Illustrator, InDesign, and\nDreamweaver\n• Experience in creating low/high-detailed annotated wireframes, and user flows for applications\nusing prototyping tools\n• Having good knowledge on developing use cases, user stories, & personas\n• Hands-on experience with HTML5 & CSS3, including cross-browser compatibility\n• Hands-on experience in creating RWD (Responsive Web-Design) layouts\n• Having good exposure on Marketing and Health Care domains\n• Good knowledge on marketing automation tool Eloqua\n• Hands-on experience on version control tool SVN\n• Quick learner, self-driven, problem-solver, highly motivated team player and ability to quickly\nadapt to new trends and technologies\n• Excellent written and verbal communication skills to engage clients and the team\ncollaboratively\n• Having good leadership and team management skills\n\nWORK EXPERIENCE\n\nSpecialist II, Marketing\n\n-\n\nMarch 2017 to Present\n\n2017\n• Worked as Software Engineer with Prolifics Corporation Limited from Dec' 2015 to Sept' 2016\n• Worked for National Informatics Centre under multiple payrolls from Apr' 2014 to Oct' 2015\n• Worked as an Inter Trainee under National Informatics Center as a Web Designer from Sept'\n2013 to Apr' 2014\n\nSkill Set\nSoftware: Adobe Photoshop, Adobe InDesign, Adobe Illustrator,\nAdobe Dreamweaver, MS PowerPoint\n\nMarketing Team\n\n-\n\nNovember 2017 to November 2017\n\nCourses and Certifications\n• Currently a member and pursuing multiple user experience related certification courses from\n\"The Interaction Design Foundation\"\n\nMarketing Automation Tool\n\nhttps://www.indeed.com/r/Sharan-Adla/3a382a7b7296a764?isid=rex-download&ikw=download-top&co=IN\n\n\nOracle Eloqua -\n\n2010 to 2010\n\nWeb Technologies: HTML (5), CSS (3)\nFront End Framework: Twitter Bootstrap\nAwards and Recognitions\n• SPARKLE Award - Certificate of Appreciation - Oct 2017\n• Award of appreciation for outstanding performance and contribution towards development of\nseveral digital assets & microsites for OOW 2017\n\nEDUCATION\n\nBachelors in Computer Science\n\nM. V. G. R College of Engineering -  Vizianagaram, Andhra Pradesh\n\nDecember 2008\n\nMPC subject\n\nGowtham Jr. College -  Vijayawada, Andhra Pradesh\n\nAugust 2006\n\nSSC\n\nVignan Vidyalayam High School -  Visakhapatnam, Andhra Pradesh\n\nLINKS\n\nhttps://behance.net/sharanadla\n\nhttps://www.linkedin.com/in/sharanadla\n\nhttps://behance.net/sharanadla\nhttps://www.linkedin.com/in/sharanadla", "meta": {}, "annotation_approver": null, "labels": [[0, 11, "Name"], [34, 75, "Email Address"], [86, 90, "Years of Experience"], [1092, 1116, "Designation"], [1121, 1148, "Years of Experience"], [1161, 1178, "Designation"], [1184, 1213, "Companies worked at"], [1214, 1242, "Years of Experience"], [1313, 1335, "Years of Experience"], [1256, 1283, "Companies worked at"], [1404, 1416, "Designation"], [1371, 1398, "Companies worked at"], [1417, 1445, "Years of Experience"], [1572, 1602, "Years of Experience"], [2226, 2255, "Degree"], [2257, 2290, "College Name"], [2294, 2322, "Location"], [2324, 2337, "Graduation Year"], [2352, 2371, "College Name"], [2339, 2342, "Degree"], [2375, 2401, "Location"], [172, 183, "Job Specific Skills"], [225, 240, "Tech Tools"], [242, 253, "Tech Tools"], [255, 263, "Tech Tools"], [269, 280, "Tech Tools"], [306, 344, "Job Specific Skills"], [350, 360, "Job Specific Skills"], [384, 401, "Job Specific Skills"], [440, 449, "Job Specific Skills"], [451, 463, "Job Specific Skills"], [503, 508, "Tech Tools"], [511, 515, "Tech Tools"], [527, 554, "Job Specific Skills"], [589, 616, "Job Specific Skills"], [705, 725, "Job Specific Skills"], [731, 737, "Tech Tools"], [790, 803, "Soft Skills"], [805, 816, "Soft Skills"], [818, 832, "Soft Skills"], [935, 942, "Soft Skills"], [947, 953, "Soft Skills"], [954, 967, "Soft Skills"], [1036, 1046, "Soft Skills"], [1051, 1067, "Soft Skills"], [1467, 1482, "Tech Tools"], [1484, 1498, "Tech Tools"], [1500, 1517, "Tech Tools"], [1519, 1536, "Tech Tools"], [1538, 1551, "Tech Tools"], [1762, 1782, "Job Specific Skills"], [1893, 1899, "Tech Tools"], [1935, 1939, "Tech Tools"], [1945, 1948, "Tech Tools"], [1974, 1991, "Tech Tools"]]}
{"id": 23, "text": "Shreyanshu Gupta\nSoftware Development Engineer with 6 months of experience in Java, C,\nVelocity, Web Development at Amazon Development Centre.\n\nBangalore, Karnataka - Email me on Indeed: indeed.com/r/Shreyanshu-\nGupta/6bd08d76c29d63c7\n\nSelf-Motivated and Hardworking Computer Science student seeking to apply my skills and in the\nprocess, develop\nprofessionally.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSoftware Development Engineer\n\nAmazon\n\nInterned at Amazon Development Centre for 6 months 2017\n\nProjects did during Internship\n\n• Blog Migration from TypePad to Amazon\n- Amazon owns a blog omnivoracious.com which is used to improve the book sales in\namazon.com. It was\nrecently hosted in a 3rd party blogging platform TypePad.\n- The project required to find a platform which satisfies all the requirements and transfer all\nTypePad content\nto the Amazon-owned platform maintaining the functionalities and compatibility.\n- Used Velocity Templating Language, Java, Amazon Tools and Many Blogging Platforms.\n• Mobile Widget Disparity\n- Due to some recent changes in Amazon configuration, there was a content disparity between\nbook\ndescription of mobile and desktop in millions of products.\n- The project required to find the reason for disparity and then find a solution that doesn’t affect\nother\nfeatures & is also expandable.\n- Used Java, configuration files, and Amazon Tools.\n• Physical to Amazon Video Conversion\n- To promote Amazon Video Over Physical Video source (DVD, Blu Ray), certain features were\ncreated like\nPopup Window and messages above Add to Cart button.\n- This project required to create UI for each of the features and check whether it satisfies all the\ncriteria.\n- Used Java, JSP, configuration files and bean creation.\n\nhttps://www.indeed.com/r/Shreyanshu-Gupta/6bd08d76c29d63c7?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Shreyanshu-Gupta/6bd08d76c29d63c7?isid=rex-download&ikw=download-top&co=IN\n\n\nEDUCATION\n\nB.Tech in Computer Science\n\nKalinga Institute of Industrial Technology\n\nJuly 2013 to April 2017\n\nSKILLS\n\nANDROID (Less than 1 year), Git (Less than 1 year), HTML (Less than 1 year), PHP (Less than\n1 year), SQL (Less than 1 year), Java (Less than 1 year), C, Velocity (Less than 1 year), C++,\nDatabase Management (Less than 1 year)\n\nLINKS\n\nhttps://www.linkedin.com/in/shreyanshu-gupta-135176103/\n\nCERTIFICATIONS/LICENSES\n\nOnline Content Writer\n\nJuly 2016 to Present\n\nWas Online Content Writer for Greymeter.com.\n\nAndroid Application Development\n\nJune 2015 to Present\n\nLearned to create basic Android Apps using Eclipse and Android Studio.\n\nPUBLICATIONS\n\nBlog Migration from TypePad to Amazon\n\nhttps://www.amazonbookreview.com\n\nJuly 2017\n\nAmazon owns a blog amazonbookreview.com which is used to improve the book sales in\namazon.com. It was recently hosted in a 3rd party blogging platform TypePad.\n- The project required to find a platform which satisfies all the requirements and transfer\nall TypePad content to the Amazon owned platform while maintaining its functionalities and\ncompatibility.\n- Used Velocity Templating Language, Java, Amazon Tools and Many Blogging Platforms.\n\nMobile Widget Disparity\n\nJuly 2017\n\nhttps://www.linkedin.com/in/shreyanshu-gupta-135176103/\nhttps://www.amazonbookreview.com\n\n\n- Due to some recent changes in Amazon configuration files, there was content disparity between\nbook description of mobile and desktop in millions of products.\n- The project required to find the reason for disparity and then find a solution that doesn’t affect\nother features & is also expandable.\n- Used Java, configuration files and Amazon Tools.\n\nPhysical to Amazon Video Conversion\n\nJuly 2017\n\n- To promote Amazon Video Over Physical Video source (DVD, Blu Ray), certain features were\ncreated like Popup Window and messages above Add to Cart button.\n- This project required to create UI for each of the features and check whether it satisfies all the\ncriteria.\n- Used Java, JSP, configuration files and bean creation\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\n\n* Programming language: C, SQL, Core Java, Android, Velocity\n* Web Development - PHP, Basic knowledge of HTML\n* Basic knowledge of GIT Version Control System", "meta": {}, "annotation_approver": null, "labels": [[0, 16, "Name"], [17, 46, "Designation"], [144, 164, "Location"], [187, 234, "Email Address"], [412, 441, "Designation"], [463, 488, "Companies worked at"], [489, 506, "Years of Experience"], [1964, 1990, "Degree"], [1992, 2034, "College Name"], [2509, 2529, "Years of Experience"], [52, 74, "Years of Experience"], [78, 82, "Tech Tools"], [84, 85, "Tech Tools"], [87, 95, "Tech Tools"], [97, 112, "Designation"], [116, 141, "Companies worked at"], [236, 250, "Soft Skills"], [938, 946, "Tech Tools"], [968, 972, "Tech Tools"], [1343, 1347, "Tech Tools"], [974, 986, "Tech Tools"], [1374, 1386, "Tech Tools"], [1700, 1704, "Tech Tools"], [1706, 1709, "Tech Tools"], [2036, 2059, "Graduation Year"], [2069, 2076, "Tech Tools"], [2097, 2100, "Tech Tools"], [2121, 2125, "Tech Tools"], [2146, 2149, "Tech Tools"], [2170, 2173, "Tech Tools"], [2194, 2198, "Tech Tools"], [2219, 2220, "Tech Tools"], [2222, 2230, "Tech Tools"], [2251, 2254, "Tech Tools"], [2256, 2275, "Job Specific Skills"], [2385, 2406, "Designation"], [2408, 2428, "Years of Experience"], [2476, 2507, "Designation"], [2574, 2581, "Tech Tools"], [2586, 2600, "Tech Tools"], [3939, 3943, "Tech Tools"], [3944, 3948, "Tech Tools"], [3950, 3953, "Tech Tools"], [4060, 4061, "Tech Tools"], [4063, 4066, "Tech Tools"], [4068, 4077, "Tech Tools"], [4079, 4086, "Tech Tools"], [4088, 4096, "Tech Tools"], [4117, 4120, "Tech Tools"], [4141, 4145, "Tech Tools"], [4167, 4170, "Tech Tools"]]}
{"id": 24, "text": "Shrishti Chauhan\nHave total work experience of 2.5 years on Oracle Fusion Middleware -\nSOA, WebLogic and MFT Module.\n\nBilaspur, Chhattisgarh - Email me on Indeed: indeed.com/r/Shrishti-\nChauhan/89d7feb4b3957524\n\nSeeking to hone and enhance my technical skills in Oracle Fusion Middleware while working as\na professional in challenging and goal oriented environment.\n\nWilling to relocate to: Bengaluru, Karnataka\n\nWORK EXPERIENCE\n\nTechnical Consultant\n\nOracle -  Bengaluru, Karnataka -\n\nOctober 2015 to Present\n\n• Have total work experience of 2.5 years on Oracle Fusion Middleware - SOA, WebLogic and\nMFT Module.\n\n• Have extensively worked on Support, Testing, Cloning, Monitoring and Maintenance support\nand Enhancement for the E-Commerce Project with multi system module.\n\n• Have good understanding on End-to-End Business Process.\n\n• Experience in developing and deploying BPEL Processes using technology adapters (DB\nAdapter, File Adapter, FTP Adapter and JMS Adapter).\n\n• Part of a team for developing a BPEL process to integrate Oracle Fusion Applications. This\nOrchestrated BPEL Process had process activities like data conversion, transformation and fault\nhandling.\n\n• Developed and deployed BPEL processes to import sales order and add lines to the sales order\nimported from external sources (Files and Databases) into Order Orchestration Module.\n\n• Worked on BPEL process to create sales order within Order Orchestration Module using SOAPUI\nand Enterprise Manage.\n\n• Have good understanding on Synchronous and Asynchronous processes, Transformations, XSD,\nXSLT and XPath\n\n• Installation of Middle tier application server (SOA Suite 11g), configuring and deploying\napplication adapters and integrating with other ERP or 3rd Party Services.\n\n• Working with the client team and communicating with the business teams regarding integration\nwith external Delivery System.\n\nhttps://www.indeed.com/r/Shrishti-Chauhan/89d7feb4b3957524?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Shrishti-Chauhan/89d7feb4b3957524?isid=rex-download&ikw=download-top&co=IN\n\n\n• As an Oracle Technical Consultant I was responsible for providing End-to-End support in the\nproject for Oracle Fusion Middleware.\n\n• Good understanding of Service Oriented Architecture (SOA) with the middleware technologies\nfor application integration.\n\nTechnical Consultant\n\nOracle\n\nHave total work experience of 2.5 years on Oracle Fusion Middleware - SOA, WebLogic and MFT\nModule.\n\nEDUCATION\n\nC.S.\n\nCHHATTISGARH SWAMI VIVEKANANDA TECHNICAL UNIVERSITY\n\n2011 to 2015\n\nSKILLS\n\nXml, Oracle MFT, Core Java, Oracle SOA, WSDL, ODI\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILL SET\nERP Packages Oracle Fusion Middleware and Oracle Fusion Application (DOO/GOP)\nFusion Middleware Modules Oracle SOA, MFT and Web Logic (11G and 12C Version)\nAdapters Database, JMS, FTP and File\nTools JDeveloper, Enterprise Manager, Administrative Console, SOAP UI and BI Publisher\nProgramming Languages XML, C++ and Core Java\nDatabases SQL\nOperating System Unix and Windows\n\nPROJECT DETAILS\nProject Client HOLTS RENFREW\nRole & Duration Technical Consultant Oct-15\nScope of Project Oracle Fusion Middleware 11G and Oracle Fusion Application R12\nProject Description\nHolt Renfrew is 180 years old chain of high-end Canadian department stores specializing in an\narray of luxury brands and designer boutiques.\n\nResponsibilities\n• Worked as Technical Consultant to the customer for the issues related to Oracle SOA (mainly\nBPEL and Mediator Components).\n• Providing daily Server Health Check for SOA and DOO/GOP (SCM) to the customer.\n• Attending/Conducting weekly customer calls and providing an update about the progress of\nthe issues.\n\n\n\n• Interacting with the Clients to understand the requirement and details of the issues and\nproviding suggestions/answers to their queries.\n• Resolving technical issues and enhancement raised by the customer.\n• Reviewing work products from the Team Members and delivering products to the client on time\nwith high quality.\n• RCA for the Complex Issues in client's version of Oracle SOA and SCM.\n• Partially worked on functional issues related to DOO/GOP - Order Management and Inventory\nManagement.\n• Working effectively with Oracle Product Support for any issue related to Standard Functionality.\n\nProject Client WHITBREAD PLC\nRole & Duration Technical Consultant SEPT-17\nScope of Project Oracle Fusion Middleware 12C and Oracle SAAS\nProject Description\nWhitbread PLC is the UK's largest hospitality company, owning Premier Inn and Costa Coffee, as\nwell as Beefeater, Brewers Fayre, and Bar Block.\n\nResponsibilities\n• Worked as Technical Consultant to the customer for the issues related to Oracle SOA and MFT.\n• Monitoring Critical Process and fixing the concurrent process running on SOA, MFT and ODI.\n• Worked on developing and executing Integration Regression Test Script.\n• Developing Application Understanding Documents for the Standard functionalities.\n• Interacting with the Clients to understand the requirement and details of the issues and\nproviding suggestions/answers to their queries.\n• Resolving technical issues and enhancement raised by the customer.\n• Reviewing work products from the Team Members and delivering products to the client on time\nwith high quality.\n• RCA for the Complex Issues in client's version of Oracle SOA and SCM.\n• Working effectively with Oracle Product Support for any issue related to Standard Functionality.\n• Co-ordinate internally with various teams, executives in providing the decisions to various\nfactors influencing the project implementation", "meta": {}, "annotation_approver": null, "labels": [[0, 16, "Name"], [47, 56, "Years of Experience"], [118, 140, "Location"], [163, 210, "Email Address"], [391, 411, "Location"], [430, 450, "Designation"], [452, 458, "Companies worked at"], [462, 482, "Location"], [486, 509, "Years of Experience"], [2482, 2533, "College Name"], [2476, 2479, "Degree"], [60, 84, "Tech Tools"], [87, 90, "Tech Tools"], [92, 100, "Tech Tools"], [105, 108, "Tech Tools"], [263, 287, "Tech Tools"], [529, 552, "Years of Experience"], [556, 580, "Tech Tools"], [729, 739, "Job Specific Skills"], [643, 650, "Job Specific Skills"], [652, 659, "Job Specific Skills"], [661, 668, "Job Specific Skills"], [804, 831, "Job Specific Skills"], [875, 889, "Job Specific Skills"], [1199, 1213, "Job Specific Skills"], [1368, 1380, "Job Specific Skills"], [2086, 2113, "Designation"], [2146, 2164, "Job Specific Skills"], [2184, 2208, "Tech Tools"], [2235, 2270, "Job Specific Skills"], [2334, 2354, "Designation"], [2380, 2403, "Years of Experience"], [2407, 2431, "Tech Tools"], [2535, 2547, "Graduation Year"], [2557, 2560, "Tech Tools"], [2562, 2572, "Tech Tools"], [2574, 2584, "Tech Tools"], [2585, 2595, "Tech Tools"], [2665, 2689, "Tech Tools"], [2694, 2719, "Tech Tools"], [2756, 2766, "Tech Tools"], [2776, 2807, "Tech Tools"], [2827, 2830, "Tech Tools"], [2832, 2835, "Tech Tools"], [2851, 2861, "Tech Tools"], [2954, 2957, "Tech Tools"], [2959, 2962, "Tech Tools"], [2967, 2976, "Tech Tools"], [2977, 2986, "Job Specific Skills"], [2987, 2990, "Tech Tools"], [3008, 3012, "Tech Tools"], [3017, 3024, "Tech Tools"], [3132, 3160, "Tech Tools"], [3165, 3190, "Tech Tools"], [4140, 4156, "Job Specific Skills"], [4161, 4181, "Job Specific Skills"], [4613, 4633, "Designation"], [4838, 4854, "Job Specific Skills"]]}
{"id": 25, "text": "Shubham Mittal\nSystem Engineer - Infosys Limited\n\nMysore, Karnataka - Email me on Indeed: indeed.com/r/Shubham-Mittal/4b29ab0545b0f67f\n\n• Having 2.0 Years of Experience as a Software Developer (System Engineer) in the IT industry.\n• Presently working as Senior System Engineer.\n• Functionally Good with Complete Order fulfillment's journey including Sales and service Cloud.\n• Expertise in implementing SQL, SOQL, SOSL, HTML, JavaScript, CSS, Salesforce (APEX, VF pages,\nothers Components)\n• Handling Deployment to Production Via CI/ANT Migration Tools.\n• Good Implementation Knowledge of Lighting, Data Loader.\n• Hands-on in Marketing cloud.\n• Good Implementation Knowledge in SOAP Web service.\n• Good Knowledge in AGILE Methodology and JIRA Tool.\n• Certified as Platform developer-1.\n• Currently working on SALESFORCE/CLOUDSENSE technology and pursuing SUPERBADGE in\nAPEX and LIGHTNING Concepts.\n\nWORK EXPERIENCE\n\nSystem Engineer\n\nInfosys Limited -\n\nJanuary 2017 to Present\n\nEnvironment: Salesforce, Cloud Sense, APEX, SOQL, GIT, Bit Bucket, AGILE\n\nResponsibilities:\n• Interaction with the onshore team to understand the requirement as per clients and functional\nrequirement document.\n• Preparation of solution document, component list and high level estimation of the tasks created\nunder sprints.\n• Technical Refinement's for User stories.\n\nContributions:\n• Developed and integrated the different modules developed by other team members.\n• Wrote the Tech specifications document, Created Class diagrams and Flow Diagram using\nfunctional requirement document.\n• Developed and deployed Lightening components.\n• Created Apex class, VF pages, Workflows, process builder, approval Process and other\ncomponents.\n• Integration of Sales cloud with other third Party vendors.\n• Designed Solution Documentations.\n• Deployments to Production and other Intermediate environments.\n• Support in SIT testing and production issues.\n\nSystem Engineer\n\nhttps://www.indeed.com/r/Shubham-Mittal/4b29ab0545b0f67f?isid=rex-download&ikw=download-top&co=IN\n\n\nInfosys Limited -  Mysore, Karnataka -\n\nJune 2016 to Present\n\nEDUCATION\n\nB.Tech in EEE\n\nNational Institute Of Technology\n\n2016\n\nSt. John's School\n\n2009\n\nSKILLS\n\nHtml, Css, Javascript, Salesforce", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [15, 30, "Designation"], [33, 48, "Companies worked at"], [50, 67, "Location"], [90, 134, "Email Address"], [174, 192, "Designation"], [194, 209, "Designation"], [254, 276, "Designation"], [916, 931, "Designation"], [933, 948, "Companies worked at"], [952, 975, "Years of Experience"], [1920, 1935, "Designation"], [2037, 2052, "Companies worked at"], [2056, 2073, "Location"], [2077, 2097, "Years of Experience"], [2110, 2116, "Degree"], [2120, 2157, "College Name"], [2159, 2163, "Graduation Year"], [145, 168, "Years of Experience"], [403, 406, "Tech Tools"], [408, 412, "Tech Tools"], [414, 418, "Tech Tools"], [420, 424, "Tech Tools"], [426, 436, "Tech Tools"], [438, 441, "Tech Tools"], [443, 453, "Tech Tools"], [501, 525, "Job Specific Skills"], [530, 536, "Tech Tools"], [537, 552, "Job Specific Skills"], [589, 597, "Tech Tools"], [626, 641, "Job Specific Skills"], [678, 694, "Tech Tools"], [716, 733, "Job Specific Skills"], [738, 742, "Tech Tools"], [809, 819, "Companies worked at"], [820, 830, "Companies worked at"], [990, 1000, "Tech Tools"], [1002, 1013, "Tech Tools"], [1015, 1019, "Tech Tools"], [1021, 1025, "Tech Tools"], [1027, 1030, "Tech Tools"], [1032, 1042, "Tech Tools"], [1044, 1049, "Job Specific Skills"], [1154, 1176, "Job Specific Skills"], [1453, 1472, "Job Specific Skills"], [2198, 2202, "Tech Tools"], [2204, 2207, "Tech Tools"], [2209, 2219, "Tech Tools"], [2221, 2231, "Tech Tools"]]}
{"id": 26, "text": "Sivaganesh Selvakumar\nDevOps Consultant with Infosys Limited\n\nChennai, Tamil Nadu - Email me on Indeed: indeed.com/r/Sivaganesh-\nSelvakumar/2d20204ef7c22049\n\nConsultant with 5.8 years of experience DevOps design , CICD implementation, Build and\ndeployment automation and Release Management.\n\nWORK EXPERIENCE\n\nDevOps Consultant\n\nInfosys Limited -\n\nDecember 2016 to Present\n\nResponsibilities:\n• Ownership of Release Management process for various web applications (Java based)\n• Involve setting the overall Delivery Automation strategy, via investments in automation at\nseveral layers of the Technology stack to implement a Continuous Deployment/Delivery pipeline.\n• Designed and delivered CloudBees Jenkins pipeline jobs with groovy code and pipeline plugin\nfor Continuous Integration and Deployments\n• Implemented a continuous delivery pipeline with CloudBees Jenkins and Scripting.\n• Manage and troubleshoot the running of automated jobs in Jenkins, TFS to support and\nstreamline the release process.\n• Integrated Code quality, Code coverage, Unit Tests, Functional Tests with CI pipeline and\nresolved issues in this process.\n• Organize and coordinate release teams across organizational boundaries.\n• Deal with release to Dev, SIT, QA and Prod including business sign offs.\n• Create/Submit applications and required associated documentation to 3rd parties for third party\nacceptance/approval testing.\n• Configure and integrate Nexus and Artifactory Repository for builds and integrating it with the\nCICD orchestration tool like Jenkins, TFS, and Bamboo.\n• Develop PowerShell scripts to upload artifacts to Artifactory through REST API's and automated\nthis as part of DevOps pipeline\n• Document knowledge articles in Confluence (Internal Wiki)\nEnvironment: TFS ( GIT - SCM), TFS Builds, CloudBees Jenkins, ANT, MAVEN, Nexus, Shell Scripts,\nUNIX, JIRA, , Microsoft Visual Studio 2015, Octopus,\n\nTechnology Analyst\n\nInfosys Limited -  Chennai, Tamil Nadu -\n\nJuly 2011 to Present\n\nEDUCATION\n\nBachelor in \"Instrumentation and Control Engineering\"\n\nhttps://www.indeed.com/r/Sivaganesh-Selvakumar/2d20204ef7c22049?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Sivaganesh-Selvakumar/2d20204ef7c22049?isid=rex-download&ikw=download-top&co=IN\n\n\nSaranathan College of Engineering -  Tiruchchirappalli, Tamil Nadu\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS:\nPlatforms Windows, Unix, Linux\nScripting Language PowerShell scripting, Shell scripting.\nDatabases Oracle, SQL Server\nBuild Tools Maven, Ant, MSBuild, Nant\nCICD Tools CloudBees Jenkins, TFS, GIT, SVN, Octopus, Jira, , Sonarqube, MSTest, Nexus,\nArtifactory, Cobertura, Ansible", "meta": {}, "annotation_approver": null, "labels": [[0, 21, "Name"], [22, 39, "Designation"], [45, 60, "Companies worked at"], [62, 81, "Location"], [104, 156, "Email Address"], [158, 169, "Designation"], [309, 326, "Designation"], [328, 343, "Companies worked at"], [347, 371, "Years of Experience"], [1895, 1913, "Designation"], [1915, 1930, "Companies worked at"], [1934, 1953, "Location"], [1957, 1977, "Years of Experience"], [1990, 2043, "Degree"], [2257, 2290, "College Name"], [2294, 2323, "Location"], [174, 197, "Years of Experience"], [406, 432, "Job Specific Skills"], [445, 461, "Job Specific Skills"], [463, 467, "Tech Tools"], [505, 533, "Job Specific Skills"], [622, 661, "Job Specific Skills"], [688, 697, "Tech Tools"], [698, 705, "Tech Tools"], [850, 859, "Tech Tools"], [860, 867, "Tech Tools"], [872, 881, "Job Specific Skills"], [1764, 1767, "Tech Tools"], [1776, 1779, "Tech Tools"], [1788, 1797, "Tech Tools"], [1798, 1805, "Tech Tools"], [1807, 1810, "Tech Tools"], [1812, 1817, "Tech Tools"], [1819, 1824, "Tech Tools"], [1530, 1537, "Tech Tools"], [1539, 1542, "Tech Tools"], [1548, 1554, "Tech Tools"], [951, 954, "Tech Tools"], [942, 949, "Tech Tools"], [1566, 1584, "Tech Tools"], [1758, 1761, "Tech Tools"], [1826, 1839, "Tech Tools"], [1841, 1845, "Tech Tools"], [1847, 1851, "Tech Tools"], [1855, 1883, "Tech Tools"], [1885, 1892, "Tech Tools"], [2377, 2384, "Tech Tools"], [2386, 2390, "Tech Tools"], [2392, 2397, "Tech Tools"], [2417, 2437, "Tech Tools"], [2439, 2454, "Tech Tools"], [2456, 2465, "Job Specific Skills"], [2466, 2472, "Tech Tools"], [2474, 2484, "Tech Tools"], [2497, 2502, "Tech Tools"], [2504, 2507, "Tech Tools"], [2509, 2516, "Tech Tools"], [2518, 2522, "Tech Tools"], [2523, 2527, "Tech Tools"], [2534, 2543, "Tech Tools"], [2544, 2551, "Tech Tools"], [2553, 2556, "Tech Tools"], [2558, 2561, "Tech Tools"], [2563, 2566, "Tech Tools"], [2568, 2575, "Tech Tools"], [2577, 2581, "Tech Tools"], [2596, 2602, "Tech Tools"], [2604, 2609, "Tech Tools"], [2611, 2622, "Tech Tools"], [2624, 2633, "Tech Tools"], [2635, 2642, "Tech Tools"]]}
{"id": 27, "text": "Snehal Jadhav\nMumbai, Maharashtra - Email me on Indeed: indeed.com/r/Snehal-Jadhav/005e1ab800b4cb42\n\nWORK EXPERIENCE\n\nL1 network engineer\n\nCisco -  Mumbai, Maharashtra -\n\nFebruary 2017 to Present\n\nassociated with Cisco on the role of ACESOFTLABS (INDIA) Pvt. Ltd\n\nEDUCATION\n\nHSC\n\nMaharashtra Board\n\nMarch 2012\n\nSSC\n\nMaharashtra Board\n\nMarch 2010\n\nB.E in Electronics & Telecommunication\n\nShivaji University\n\nSKILLS\n\nArchitecture (Less than 1 year), BGP (Less than 1 year), DHCP (Less than 1 year), DNS (Less\nthan 1 year), EIGRP (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTechnical Skills\n\n• Layer 2 Technologies like VTP, VLAN, STP, and RSTP.\n• OSI Architecture, TCP/IP Module\n• TCP/UDP, DHCP, DNS\n• MP-BGP IPv4/IPv6, VPNv4, VPNv6, RT-Filter, IPv6-Multicats.\n• Access-List, Prefix-List, Distribution List, QOS.\n• MPLS, L2/L3 VPN.\n• GRE, IPSec, DMVPN.\n• FTP-Active/Passive, TFTP.\n• Configuring and Troubleshooting L2 and L3 Ether channels on Cisco 3550, 3560, 2950 switches.\n• IP Addressing, VLSM, Summarization, understanding Lease Line and Dedicated Lease Lines.\n\nhttps://www.indeed.com/r/Snehal-Jadhav/005e1ab800b4cb42?isid=rex-download&ikw=download-top&co=IN\n\n\n• Configuring HSRP, VRRP, GLBP on Cisco Routers and Switches.\n• VTP & Frame Tagging protocols ISL, Dot1q\n• NAT, PAT, NAT-T.\n• Strong skills in Configuring Cisco Routers (ASR901, ASR903, ASR900) using ISIS, OSPF, EIGRP\nand BGP. A good understanding of working on Small to High E1nd Routers and Switches.\n• Comprehensive understanding of networking concepts pertaining to LAN, WAN, Security, IT\ncommunication, WAN protocols, Networking devices administration and maintenance in multi-\nplatform environments.\n• Strong hands-on technical knowledge of Microsoft Operating Systems (Network & Desktop),\nestablishing & managing networks, Cisco Routers, Hardware clients & switches.\n• Expertise in managing medium to large networks with Routers, Switches.", "meta": {}, "annotation_approver": null, "labels": [[0, 13, "Name"], [14, 33, "Location"], [56, 99, "Email Address"], [118, 137, "Designation"], [139, 145, "Companies worked at"], [148, 167, "Location"], [171, 195, "Years of Experience"], [234, 262, "Companies worked at"], [275, 278, "Degree"], [280, 297, "College Name"], [305, 310, "Graduation Year"], [311, 315, "Degree"], [316, 333, "College Name"], [341, 346, "Graduation Year"], [347, 385, "Degree"], [387, 405, "College Name"], [415, 427, "Job Specific Skills"], [448, 451, "Job Specific Skills"], [472, 476, "Job Specific Skills"], [497, 500, "Job Specific Skills"], [521, 526, "Job Specific Skills"], [591, 611, "Job Specific Skills"], [617, 620, "Tech Tools"], [622, 626, "Tech Tools"], [628, 631, "Tech Tools"], [637, 641, "Tech Tools"], [645, 661, "Job Specific Skills"], [663, 669, "Tech Tools"], [679, 686, "Tech Tools"], [688, 692, "Tech Tools"], [694, 697, "Tech Tools"], [976, 989, "Job Specific Skills"], [1198, 1224, "Job Specific Skills"], [1319, 1332, "Job Specific Skills"], [1711, 1738, "Job Specific Skills"], [1760, 1792, "Job Specific Skills"], [1794, 1807, "Job Specific Skills"], [1853, 1886, "Job Specific Skills"]]}
{"id": 28, "text": "Soumya Balan\nIT SUPPORT\n\nSulthan Bathery, Kerala, Kerala - Email me on Indeed: indeed.com/r/Soumya-\nBalan/97ead9542c575355\n\n➢ To work in a progressive organization where I can enhance my skills and learning to contribute\nto the success of the organization.\n\nWORK EXPERIENCE\n\nTechnical support engineer\n\nMicrosoft\n\nPosition: TECHNICAL SUPPORT ENGINEER\n\nCompany: Microsoft Corporation - Microsoft India Global Technical Support Center (Microsoft\nIGTSC), Bangalore\n\nYears of Experience: 2 Years and 4 Months\n\nResponsibilities\n\n➢ Represent Microsoft and communicate with corporate customers via telephone, written\ncorrespondence, or electronic service regarding technically complex escalated problems\nidentified in Microsoft software products, and manage relationships with those customers.\n\n➢ Manage not only the technically complex problems, but also politically charged situations\nrequiring the highest level of customer skill.\n\n➢ Receive technically complex, critical or politically hot customer issues, and maintain ownership\nof issue until resolved completely.\n\n➢ Solve highly complex problems, involving broad, in-depth product knowledge or in-depth\nproduct specialty.\n\n➢ Use trace analysis, and other sophisticated tools to analyze problems and develop solutions\nto meet customer needs.\n\n➢ Lead triage meetings to share knowledge with other engineers and develop customer solutions\nefficiently.\n\n➢ Act as technical lead, mentor, and model for a team of engineers; provide direction to others,\nreview solutions and articles, mentoring existing & aspiring Engineers.\n\n➢ Write technical articles for knowledge base.\n\n➢ Consult, collaborate and take escalations when necessary.\n\nhttps://www.indeed.com/r/Soumya-Balan/97ead9542c575355?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Soumya-Balan/97ead9542c575355?isid=rex-download&ikw=download-top&co=IN\n\n\n➢ Maintain working knowledge of pre-release products and take ownership for improvement in\nkey technical areas.\n\n➢ Manage customer escalations and recognize when to solicit additional help.\nParticipate in technical discussions and engage with product team if required to resolve issues\nand represent customer segments.\n\nExchange Server Knowledge\n\n➢ Exchange Server 2007\n➢ Exchange Server 2010\n➢ Exchange Server 2013\n➢ O365\n\nUG PROJECT TITLE: Memory Bounded Anytime Heuristic Search A* Algorithm\n\n➢ This Project presents a heuristic-search algorithm called Memory-bounded Anytime Window\nA*(MAWA*), which is complete, anytime, and memory bounded. MAWA* uses the window-\nbounded anytime-search methodology of AWA* as the basic framework and combines it with the\nmemory-bounded A* -like approach to handle restricted memory situations.\nSimple and efficient versions of MAWA* targeted for tree search have also been presented.\nExperimental results of the sliding-tile puzzle problem and the traveling-salesman problem show\nthe significant advantages of the proposed algorithm over existing methods.\n\nTechnical and Co-Curricular activities\n\n➢ Star Performer in Microsoft IGTSC in 2014.\n➢ Paper Presentations on Applications of Robotics in INOX 2K12.\n➢ Attended a Three-Day workshop on C and C++ Programming and Aliasing.\n➢ Attended a One-Day workshop on Java and Hardware Workshop at VECW\n➢ Paper presentation 4G Technologies, Cloud Computing, Heuristic Algorithms and Applications,\nOpen Source Software.\n➢ Multimedia presentations on Artificial Intellegence, 6th Sense, and Robotics.\n➢ Completed training of OCA (9i, 10g) from Oracle University.\n➢ Attended SPARK training program in Infosys Mysore.\n➢ Attended System Hardware Training program at HCL, Pondicherry.\n\nEDUCATION\n\nBE in Computer Science and Engineering\n\nVivekananda Engineering College for Women -  Chennai, Tamil Nadu\n\n2013\n\nBTEC HNC in Aviation in Hospitality and Travel Management\n\nFrankfinn Institute of Airhostess Training -  Calicut, Kerala\n\n\n\n2008\n\nState Board\n\n2007\n\nSKILLS\n\nLinux (Less than 1 year), Microsoft Office (Less than 1 year), MS OFFICE (Less than 1 year),\nproblem solving (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSkill Set\n➢ Excellent communication and interpersonal skills.\n➢ Proficient in Computer Applications -Microsoft Office Windows (Windows 2007, XP, 8, 8.1 and\nWindows 10), Linux, Fedora.\n➢ Strong analytical and problem solving skills.\n➢ Ability in managing a team of professionals and enjoy being in a team.", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [13, 23, "Designation"], [25, 56, "Location"], [79, 122, "Email Address"], [275, 301, "Designation"], [303, 313, "Companies worked at"], [324, 350, "Designation"], [361, 382, "Companies worked at"], [385, 450, "Companies worked at"], [452, 462, "Location"], [484, 504, "Years of Experience"], [3644, 3682, "Degree"], [3684, 3725, "College Name"], [3729, 3748, "Location"], [3750, 3755, "Graduation Year"], [3756, 3764, "Degree"], [3768, 3857, "College Name"], [3861, 3876, "Location"], [3880, 3885, "Graduation Year"], [3886, 3897, "College Name"], [3899, 3904, "Graduation Year"], [2222, 2242, "Tech Tools"], [2193, 2218, "Job Specific Skills"], [2245, 2265, "Tech Tools"], [2268, 2288, "Tech Tools"], [3152, 3153, "Tech Tools"], [3158, 3161, "Tech Tools"], [3162, 3173, "Job Specific Skills"], [3221, 3225, "Tech Tools"], [3294, 3309, "Job Specific Skills"], [3277, 3292, "Job Specific Skills"], [3311, 3348, "Job Specific Skills"], [3350, 3370, "Job Specific Skills"], [3913, 3918, "Tech Tools"], [3939, 3955, "Tech Tools"], [3976, 3985, "Tech Tools"], [4006, 4021, "Soft Skills"], [4088, 4101, "Soft Skills"], [4235, 4240, "Tech Tools"], [4167, 4183, "Tech Tools"], [4184, 4191, "Tech Tools"], [4242, 4248, "Tech Tools"], [4259, 4269, "Soft Skills"], [4106, 4119, "Soft Skills"], [4274, 4289, "Soft Skills"]]}
{"id": 29, "text": "Soumya Balan\nSoumya Balan - BE Computer Science - 3 yr Work Experience at\nMicrosoft Corporation\n\nThiruvananthapuram, Kerala - Email me on Indeed: indeed.com/r/Soumya-\nBalan/8c7fbb9917935f20\n\n➢ To work in a progressive organization where I can enhance my skills and learning to contribute\nto the success of the organization.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nTechnical Support Engineer\n\nMicrosoft iGTSC -  Bengaluru, Karnataka -\n\nJuly 2013 to October 2015\n\nPosition: TECHNICAL SUPPORT ENGINEER\n\nCompany: Microsoft Corporation - Microsoft India Global Technical Support Center (Microsoft\nIGTSC), Bangalore\n\nYears of Experience: 2 Years and 4 Months\n\nResponsibilities\n\n➢ Represent Microsoft and communicate with corporate customers via telephone, written\ncorrespondence, or electronic service regarding technically complex escalated problems\nidentified in Microsoft software products, and manage relationships with those customers.\n\n➢ Manage not only the technically complex problems, but also politically charged situations\nrequiring the highest level of customer skill.\n\n➢ Receive technically complex, critical or politically hot customer issues, and maintain ownership\nof issue until resolved completely.\n\n➢ Solve highly complex problems, involving broad, in-depth product knowledge or in-depth\nproduct specialty.\n\n➢ Use trace analysis, and other sophisticated tools to analyze problems and develop solutions\nto meet customer needs.\n\n➢ Lead triage meetings to share knowledge with other engineers and develop customer solutions\nefficiently.\n\n➢ Act as technical lead, mentor, and model for a team of engineers; provide direction to others,\nreview solutions and articles, mentoring existing & aspiring Engineers.\n\nhttps://www.indeed.com/r/Soumya-Balan/8c7fbb9917935f20?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Soumya-Balan/8c7fbb9917935f20?isid=rex-download&ikw=download-top&co=IN\n\n\n➢ Write technical articles for knowledge base.\n\n➢ Consult, collaborate and take escalations when necessary.\n\n➢ Maintain working knowledge of pre-release products and take ownership for improvement in\nkey technical areas.\n\n➢ Manage customer escalations and recognize when to solicit additional help.\nParticipate in technical discussions and engage with product team if required to resolve issues\nand represent customer segments.\n\nExchange Server Knowledge\n\n➢ Exchange Server 2007\n➢ Exchange Server 2010\n➢ Exchange Server 2013\n➢ O365\n\nEDUCATION\n\nBE in Computer Science and Engineering\n\nVivekananda Engineering College for Women -  Chennai, Tamil Nadu\n\n2013\n\nBTEC HNC in Aviation\n\nFrankfinn Institute of Airhostess Training -  Calicut, Kerala\n\n2008\n\nState Board +2\n\n2007\n\nSSLC\n\nState\n\n2005\n\nSKILLS\n\nDBMS, O365, Communication Skills, Exchange 2013, Hospitality, Networking, Computer\nOperating, Programming, Computer Hardware, Java, Exchange 2010, Teaching\n\nADDITIONAL INFORMATION\n\nSkill Set\n➢ Excellent communication and interpersonal skills.\n\n\n\n➢ Proficient in Computer Applications -Microsoft Office Windows (Windows 2007, XP, 8, 8.1 and\nWindows 10), Linux, Fedora.\n➢ Strong analytical and problem solving skills.\n➢ Ability in managing a team of professionals and enjoy being in a team.\n\nProject Details\n\nUG PROJECT TITLE: Memory Bounded Anytime Heuristic Search A* Algorithm\n\n➢ This Project presents a heuristic-search algorithm called Memory-bounded Anytime Window A*\n(MAWA*), which is complete, anytime, and memory bounded. MAWA* uses the window-bounded\nanytime-search methodology of AWA* as the basic framework and combines it with the memory-\nbounded A* -like approach to handle restricted memory situations.\nSimple and efficient versions of MAWA* targeted for tree search have also been presented.\nExperimental results of the sliding-tile puzzle problem and the traveling-salesman problem show\nthe significant advantages of the proposed algorithm over existing methods.\n\nTechnical and Co-Curricular activities\n\n➢ Star Performer in Microsoft IGTSC in 2014.\n➢ Paper Presentations on Applications of Robotics in INOX 2K12.\n➢ Attended a Three-Day workshop on C and C++ Programming and Aliasing.\n➢ Attended a One-Day workshop on Java and Hardware Workshop at VECW\n➢ Paper presentation 4G Technologies, Cloud Computing, Heuristic Algorithms and Applications,\nOpen Source Software.\n➢ Multimedia presentations on Artificial Intellegence, 6th Sense, and Robotics.\n➢ Completed training of OCA (9i, 10g) from Oracle University.\n➢ Attended SPARK training program in Infosys Mysore.\n➢ Attended System Hardware Training program at HCL, Pondicherry.", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [13, 25, "Name"], [28, 47, "Degree"], [50, 54, "Years of Experience"], [74, 95, "Companies worked at"], [97, 123, "Location"], [146, 189, "Email Address"], [373, 399, "Designation"], [401, 416, "Companies worked at"], [420, 440, "Location"], [444, 469, "Years of Experience"], [481, 507, "Designation"], [518, 539, "Companies worked at"], [542, 607, "Companies worked at"], [609, 618, "Location"], [641, 661, "Years of Experience"], [2465, 2503, "Degree"], [2505, 2546, "College Name"], [2550, 2569, "Location"], [2571, 2576, "Graduation Year"], [2577, 2585, "Degree"], [2589, 2641, "College Name"], [2645, 2660, "Location"], [2662, 2667, "Graduation Year"], [2668, 2679, "College Name"], [2684, 2689, "Graduation Year"], [2350, 2375, "Job Specific Skills"], [2379, 2399, "Tech Tools"], [2402, 2422, "Tech Tools"], [2425, 2445, "Tech Tools"], [2717, 2721, "Job Specific Skills"], [2729, 2742, "Soft Skills"], [2723, 2727, "Tech Tools"], [2751, 2764, "Tech Tools"], [2779, 2789, "Job Specific Skills"], [2811, 2822, "Job Specific Skills"], [2833, 2841, "Job Specific Skills"], [2843, 2847, "Tech Tools"], [2849, 2862, "Tech Tools"], [2920, 2933, "Soft Skills"], [2938, 2951, "Soft Skills"], [3002, 3018, "Tech Tools"], [3019, 3026, "Tech Tools"], [3028, 3067, "Tech Tools"], [3070, 3075, "Tech Tools"], [3077, 3083, "Tech Tools"], [3094, 3104, "Soft Skills"], [3109, 3124, "Soft Skills"], [4080, 4081, "Tech Tools"], [4086, 4089, "Tech Tools"], [4090, 4101, "Job Specific Skills"], [4149, 4153, "Tech Tools"], [4205, 4220, "Job Specific Skills"], [4222, 4237, "Job Specific Skills"], [4239, 4276, "Job Specific Skills"], [4278, 4298, "Job Specific Skills"], [4404, 4407, "Tech Tools"], [4453, 4458, "Tech Tools"], [4547, 4558, "Location"]]}
{"id": 30, "text": "Sowmya Karanth\nFinance Analyst\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Sowmya-Karanth/\na76c9c40c02ed396\n\nTo leverage 1+ years experience in order management and build up the core financial techniques\nand\nensure the better growth of the organisation by providing dedicated and on time delivery of the\njob.\n\nWilling to relocate to: Bengaluru, Karnataka - hyderbad, Telangana - Chennai, Tamil Nadu\n\nWORK EXPERIENCE\n\nFinance Analyst\n\nOracle India Pvt ltd -\n\nMay 2015 to March 2017\n\n* Recruited as Finance analyst for NAMER ( North Americas') region and worked for a year.\n\n* Achieved 'YAR (You Are Recognised) ' Award for the month of August 2015 and was given the\ntitle\n\"Young and early achiever\".\n\n* From June 2016 to September 2016 was trained and worked for EMEA ( Middle Eastern) regions\nand was the mentor for new joiners and trainer for new managers.\n\n* From September 2016 due to regional expansion was trained for APAC ( Asian & Pacific) regions\nand was given the title 'Global Resource' due to my constant & extensive support for the other\ntwo\nregions.\n\n* Performed two major projects 'LLC ( License Learning Cycle) ' and 'Global Review tool' and was\nthe lead and global reviewer in both.\n\n* Performed various extensive analysis to globalise the process like standardising the differences\nin the process flow and concepts between regions.\n\nEDUCATION\n\nMBA in Finance and sectorial specialisation\n\nAvinashilingam Institute for Home Science & Higher Education for Women\n\n2013 to 2015\n\nFrench\n\nhttps://www.indeed.com/r/Sowmya-Karanth/a76c9c40c02ed396?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Sowmya-Karanth/a76c9c40c02ed396?isid=rex-download&ikw=download-top&co=IN\n\n\nS.B.K.V higher secondary school\n\n2010\n\nAvinashilingam Deemed University -  Coimbatore, Tamil Nadu\n\nSKILLS\n\nEMPLOYEE RESOURCE GROUP (Less than 1 year), ENTERPRISE RESOURCE PLANNING (Less than\n1 year), ERP (Less than 1 year), Microsoft office (Less than 1 year), MS OFFICE (Less than 1\nyear)\n\nADDITIONAL INFORMATION\n\n/INTERESTS\n\n* Computer skills - Microsoft office, ERP, Oracle SQL", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [15, 30, "Designation"], [32, 52, "Location"], [75, 120, "Email Address"], [347, 367, "Location"], [370, 389, "Location"], [392, 411, "Location"], [430, 445, "Designation"], [447, 467, "Companies worked at"], [471, 493, "Years of Experience"], [510, 525, "Designation"], [715, 747, "Years of Experience"], [874, 893, "Years of Experience"], [1374, 1417, "Degree"], [1419, 1489, "College Name"], [1786, 1808, "Location"], [1750, 1782, "College Name"], [134, 154, "Years of Experience"], [1491, 1503, "Years of Experience"], [1505, 1511, "Soft Skills"], [1862, 1890, "Job Specific Skills"], [1935, 1951, "Tech Tools"], [1911, 1914, "Tech Tools"], [1972, 1981, "Tech Tools"], [2058, 2074, "Tech Tools"], [2076, 2079, "Tech Tools"], [2081, 2091, "Tech Tools"]]}
{"id": 31, "text": "Srabani Das\nsoftware engineer - Oracle, Business Objects\n\nBishnupur, MANIPUR, 722122, IN - Email me on Indeed: indeed.com/r/Srabani-\nDas/152269fb5b986c26\n\nTo secure a challenging position where I can effectively contribute my skills as Software\nProfessional which will boost my career and can be effectively achieve company objective.\n\nWORK EXPERIENCE\n\nsoftware engineer\n\nOracle, Business Objects -\n\nJune 2015 to Present\n\n• A highly accomplished professional with 2.8 years of IT experience as a software developer\nworking with technologies like Teradata, Oracle, Business Objects.\n• Working as software engineer with Apple client in Exilant Technologies Pvt. Ltd. from June 2015\nto till date.\n• Working in multiple domains like Retail and Concierge.\n\nEDUCATION\n\nB-tech in Electronics and Telecommunication\n\nCollege of engineering -  Bhubaneshwar, Orissa\n\n2015\n\nCBSE\n\nODM Pubic school -  Bhubaneshwar, Orissa\n\n2011\n\nCBSE\n\nGreen-Field school\n\n2009\n\nSKILLS\n\nRETAIL (2 years), RETAIL MARKETING (2 years), TERADATA (2 years), ACCEPTANCE TESTING\n(Less than 1 year), APS (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS\n\nhttps://www.indeed.com/r/Srabani-Das/152269fb5b986c26?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Srabani-Das/152269fb5b986c26?isid=rex-download&ikw=download-top&co=IN\n\n\n• Databases (Primary): Teradata, Oracle (SQL, PL/SQL)\n• OS: Unix, Windows, Mac OS\n• Teradata Tools & Utilities: BTEQ, Muti Load, Fast Load, Tpump, TPT.\n• Reporting Tools: BO Reporting, Crystal Report, Universe design tool, BOBJ Migration tool\n• Other Tools: Radar, Espresso, SQL Workbench, SQL Developer, ETL Metadata UI, iCheck, Global\ndeployment tool, Ms office, Power Point, Ms Excel, Workload Automation.\n• Applications: Central Station, GitLab, SVN.\nKEY PROJECTS:\n\nLeaderboard\nProject Leaderboard\nClient Apple\nDescription\n- Leader Board is an iPad based application for single Point of entry to GBI Retail mobile\napplications. It is to provide automated and centralized access to different apps. Its a location to\nget key actionable metrics around Sales & Services related transactions of Apple retail store. It\nis widely used by all managers and employees in Apple stores.\n\n- This is a diverse application which has 4 sub applications:\nStore Pulse, Benchmark, Session and RedZone Mobile.\n- Real-time as well as historical data are displayed in this application.\n\nPeak Team Size 6\nRoles and Responsibility\n- Working as a database developer in this project.\n\n- Worked in complete revamp of Leader board application and done multiple enhancements to\ndevelop logic for multiple sales and service related metrics.\n\n- Created design documents and Logical Data flow Model from source study according to Business\nrequirements.\n\n- Created multiple Replication setup process (Export and Load) using Apple ETL framework tool.\n\n- Worked on Business Objects, SAP Crysral report and universe for report creation and\nmodifcation.\n\n- Coding and Support for all phases of Testing\n\n- Implementation in production for project go live.\n\n- Production support till warranty phase.\n\n- Transition KT to APS team.\n\nTechnology Used Teradata, Oracle, BO Universe and Reporting, Crystal Report, Unix and Autosys,\nETL Framework.\nReporting Tools Used Business Objects, BOBJ Migration tool, Universe design tool, SAP Crystal\nReport\n\n\n\nApple Retail Expansion\nProject Apple Retail Expansion\nClient Apple\nDescription\n- As part of New Apple Store/Country Expansion, Store/Market/Country level metadata setups are\ndone with real-time and history performance reports are sent to business users periodically.\n\nPeak Team Size 3\nRoles and Responsibility\n- Involved in Designing technical specification docs and gathering functional requirements.\n\n- Carried out DB changes alone with Unit, Integration, Functional, Regression testing and test\ncase preparation of all the modules impacted for it.\n\n- Implemented Reporting side changes for new Store expansion.\n\nTechnology Used Teradata, Oracle, BO Reporting, Crystal Report, Unix and Autosys, ETL\nFramework.\nReporting Tools Used Business Objects, SAP Crystal Report\n\nGenius & Forum Dashboard\nProject Genius & Forum Dashboard\nClient Apple\nDescription\n- This Dashboards acts as a reporting solution for Concierge - the service oriented wing of Apple.\n- Genius and Forum Dashboard has been developed as the reporting layer to report the Genius\nBar, Workshops/events, Repair related metrics.\n- Helps the store leaders to take decisions on the floor to improve customer experience analyzing\nthe trend of the store.\n- Store leaders can plan for scheduling of employees based on the demand of symptoms/\ncategories.\n\nPeak Team Size 6\nRoles and Responsibility\n- Understanding the functional requirement\n- Design Document preparation.\n- Coding and Units testing.\n- Preparing test cases and conduct Integration Testing\n- Support for User Acceptance Testing\n- Implementation\n- Support\n\nTechnology Used Teradata, Oracle, Unix and Autosys, ETL Framework.\nReporting Tools Used Business Objects, BOBJ Migration tool, Universe design tool", "meta": {}, "annotation_approver": null, "labels": [[0, 11, "Name"], [12, 29, "Designation"], [32, 56, "Companies worked at"], [58, 76, "Location"], [111, 153, "Email Address"], [353, 370, "Designation"], [372, 396, "Companies worked at"], [400, 420, "Years of Experience"], [464, 474, "Years of Experience"], [595, 612, "Designation"], [665, 692, "Years of Experience"], [763, 769, "Degree"], [773, 830, "College Name"], [834, 854, "Location"], [856, 861, "Graduation Year"], [2435, 2453, "Designation"], [236, 257, "Designation"], [546, 554, "Tech Tools"], [556, 562, "Tech Tools"], [564, 580, "Job Specific Skills"], [634, 663, "Companies worked at"], [974, 990, "Job Specific Skills"], [1002, 1010, "Tech Tools"], [1022, 1040, "Job Specific Skills"], [1311, 1320, "Job Specific Skills"], [1332, 1340, "Tech Tools"], [1342, 1348, "Tech Tools"], [1350, 1353, "Tech Tools"], [1355, 1361, "Tech Tools"], [1369, 1373, "Tech Tools"], [1375, 1382, "Tech Tools"], [1384, 1390, "Tech Tools"], [1393, 1401, "Tech Tools"], [1480, 1492, "Tech Tools"], [1494, 1508, "Tech Tools"], [1510, 1530, "Tech Tools"], [1532, 1552, "Tech Tools"], [1567, 1572, "Tech Tools"], [1574, 1582, "Tech Tools"], [1584, 1597, "Tech Tools"], [1599, 1612, "Tech Tools"], [1614, 1617, "Tech Tools"], [1618, 1629, "Tech Tools"], [1631, 1637, "Tech Tools"], [1663, 1672, "Tech Tools"], [1674, 1685, "Tech Tools"], [1687, 1695, "Tech Tools"], [1697, 1716, "Job Specific Skills"], [1734, 1749, "Tech Tools"], [1751, 1757, "Tech Tools"], [2804, 2823, "Tech Tools"], [3121, 3129, "Tech Tools"], [3131, 3137, "Tech Tools"], [3139, 3164, "Tech Tools"], [3166, 3180, "Tech Tools"], [3182, 3186, "Tech Tools"], [3191, 3198, "Tech Tools"], [3200, 3213, "Tech Tools"], [3275, 3295, "Tech Tools"], [4052, 4068, "Tech Tools"], [4070, 4088, "Tech Tools"], [4694, 4716, "Job Specific Skills"], [4719, 4746, "Job Specific Skills"], [4761, 4774, "Job Specific Skills"], [4845, 4868, "Job Specific Skills"], [4913, 4921, "Tech Tools"], [4923, 4929, "Tech Tools"], [4931, 4935, "Tech Tools"], [4940, 4947, "Tech Tools"], [4949, 4962, "Tech Tools"], [4985, 5001, "Tech Tools"], [5003, 5022, "Tech Tools"], [5024, 5044, "Tech Tools"]]}
{"id": 32, "text": "Srinivas VO\nSr. Test Manager\n\nMumbai, Maharashtra - Email me on Indeed: indeed.com/r/Srinivas-VO/39c80e42cb6bc97f\n\nA Test Manager, with a track record of 15+Yrs ( 4yrs UK onsite) delivering major test solutions\nfor global projects ($40m) on behalf of leading blue chip organisations. Delivering IT solutions,\nranging from simple to complex and challenging projects and programs, establishing an enviable\nrecord of on-time, high quality & added value delivery.\n● Testing capabilities to existing customers and prospective customers during client visits / at\ncustomer location.\n● Own and Support RFI/RFPs, proposal walkthroughs and presentations and Transition\nknowledge from pre-sales to delivery, in case of project win\n● Analyze proposal requirements in direct relation with clients, and provide innovative\nsolutions, as part of proposals\n● Develop proof of concepts to prospects during pre-sales phase, Provide test consulting\nservices, on demand & Collate repository from delivery team, along with case studies for use in\nproposals, presentations and consulting\n● Pipeline building through new opportunities, cross-selling, up-selling and Establish\ncommunication with Geo Sales and other streams of business\n● Maintain Order Book, Win /Loss analysis, pre-sales metrics\n● Handle any technical question or issue which arises during a sales cycle and setting\nappropriate customer expectations.\n● Independently executes strategic leadership to others in identifying opportunities and Expert\nin driving pilots/proof of concepts.\n● Talented in handling extremely risk and competitive situations and responsible for product\nand services, revenue goals at team and regional level.\n● Proposed and implemented TCoE setup for 2 major clients, which helped in improving the\nproductivity by reducing the staffing and operational cost by 25%.\n● Involved in setup for Custom Application Testing Services (Oracle R12, Siebel, Fusion etc., )\nin Oracle SSI & Testing Centre of Excellence (TCoE)\n● Ability to excite customers, generate awareness of new possibilities that can yield additional\nrevenue.\n● Planning, scheduling and tracking of the project modules and conducted trainings across\nteams and Experience in proposals for the projects, resource planning and estimations.\n● Extensive experience on bid management, PMO process, risk management and prepared\nproject management office documents.\n● Expertise in building automation frameworks for Front to back testing of Retail / Healthcare/\nTelecom/ BFSI/Supply Chain Trading applications\n● Expertise in design, development and implementation of frameworks using UNIX, Java, .Net,\nVB, SQL, XML, SWIFT, MQ and FMW.\n● Experience in E2E testing of variety of Oracle Applications:\n● Oracle Retail: Oracle RMS, RPM, REIM, SIM etc.\n● Oracle Ebusiness Suite R12: AP, AR, GL, OM, PA, CM, TM\n● Oracle IDM: OAM, OIM, OVD, OID, SSO\n● Oracle Siebel: Public Sector, Call Center Apps, Ebilling Apps\n● Oracle Fusion Applications: Fusion HCM, R12, OBIEE\n● Oracle Transportation Management: Order Management,\nShipments, Transport Request management, Routing and Consolidation\n\nhttps://www.indeed.com/r/Srinivas-VO/39c80e42cb6bc97f?isid=rex-download&ikw=download-top&co=IN\n\n\n● Oracle FlexCube & Oracle Health Sciences: Oracle® Clinical\n● Oracle Communications: Billing and Revenue Management\n(BRS), Order Management and Order Fulfillment (OMOF)\n● Experience in managing large diversified team across geographies\n● Experience includes customization of methodologies for the business needs and deploying\nthem\ninto programs/projects\n● Experience in proposing out of box solutions for automation and convincing clients on alternate\nsolutions and investments in automation using\n● Oracle Tools (OATS, Oracle Load Tester, Oracle Test Manager)\n● HP Tools (QTP, Load Runner, Test Director, Performance Centre)\n● Rational Suite (Reqpro, RA, Rational Robot, RFT, RPT, RCC, RCQ)\n● Open Source / Other Tools: Selenium Webdriver 2.0, Testng, JMeter,\nAppium, Junit, Cucumber, JIRA, Maven, SOAPUI, Jbehave, BDD etc.,\n● A strategic thinker, problem solver, project implementer and change leader, who has\nconsistently\nprovided organizations with added value as they move towards achieving their objectives.\n● Helped many large and small organizations to establish sound project & management practices\nintegrated into the corporate framework to deliver projects, programs and managed portfolios.\n● Ability to identify business value in customer needs and translate to innovative solutions\ncommunicating the requirements & mission, in context, to both management & IT & Quality\ndevelopment, using sound, proven and valued management practices through integrated best\nprocesses from clients and IT Governance Frameworks (RUP, CMMI and ISO)\n\nSolution Sectors\nService successes in: Logistics & SCM, Investment Banking, Public Sector, Financial, Banking,\nRetail,\nHealth Sciences, Telecom, Real Estate, Hospitality, Outsourcing, Project Management, Knowledge\nManagement & Performance Management.\n\nClientele\nDHL (UK), Mobily (Saudi Arabia), UBS (London, UK), Rolls Royce (Derby, UK), TNT (Hinckley, UK),\nRCUK (Research Councils, Swindon, UK), ADAT (Middle East), OHI (Netherlands), Citibank (USA),\nTransurban (Australia), BCBS (Blue Cross Blue Shield, USA) etc.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nQA Manager\n\nTracelink -  Mumbai, Maharashtra -\n\nApril 2017 to October 2017\n\n➔ Define, evangelize, and implement global, unified agile delivery processes; select\ncommon tools; specify core quality-measurement KPIs,expand automated-testing in\nContinuous Integration environment\n➔ Project Manager and Scrum Master of company's highest priority Tracelink Products &\nTracelink L3 Support Projects recognized by executive management as the model for\n\n\n\nproject organization and execution.\n➔ Manage and administer relations, budgets, and contracts with outsourcing vendor\n➔ Define the test strategy and approach for one or many products, provide quality\nassurance leadership, and be ultimately responsible for ensuring product quality\n➔ Perform hands-on job that requires strategic thinking and planning to provide leadership\nand expertise throughout the entire QA life cycle, ensuring the success of the team's\nmanual and automation efforts\n➔ Lead efforts to develop, document, and implement applicable QA processes and\nprocedures to provide more effective quality methods within the group in support of\nproviding quality products\n➔ Create, implement, maintain, and enhance test plans, test scripts, and test\nmethodologies to ensure comprehensive test coverage\n➔ Develop world class automation for regression and feature testing\n➔ Work closely with all stakeholders to ensure project alignment\n➔ Provide quarterly presentations to executive staff on QA accomplishments and future vision\nand goals\nEnvironment: Amazon Cloud Services (Habari, Elasticache, Cloud Search, Dynamo, RDS, Redis,\nAmazon RedShift), Java, Scala, Selenium Webdriver, Jmeter etc.,\n\nSr. Test Manager\n\nconfidential -  Bengaluru, Karnataka -\n\nJuly 2013 to March 2017\n\nTest Manager, Client\n\nOracle SSI -  London -\n\nJanuary 2011 to November 2012\n\nLondon\nDescription: Oracle will provide services to Manage process workshops with UBS to map the\nOracle\nFusion Human Capital Management (HCM) applications processes to UBS's global processes.\nImplement\nOracle Fusion HCM applications within an Oracle. Software as a Service (SaaS) environment.\nSupport\nUBS with the mapping of the Oracle Fusion Applications data structures to UBS's current\nPeopleSoft 8.8\nsystem and then design, develop and unit test data load routines. This will include the definition\nof the reconciliation process. Configure and system test the application modules to support the\nLevel 3 standard\nbusiness processes and the UBS functional requirements identified.\nResponsibilities:\nDeveloped High Level Test Strategy for UBS - Group HR Applications (Viz., Workforce\nManagement,\nRecruitment and Talent Management, Compensation management, Payrolls, Learning and\nDevelopment,\nHR Service Management)\nInvolved in Setting up Test Environments Viz., (System Testing, SIT, Conversion Testing, Security\n\n\n\nTesting, Cutover Testing, Performance Testing, UAT)\nDeveloped Master Test Plans for Oracle Fusion HCM Applications (Viz., Compensation\nManagement,\nWorkForce Management, Recruitment and Talent Management) & Non Fusion Oracle Applications\n(viz.,\nService Management)\nDeveloped High Level Test Requirements for Workforce Deployment, Workforce development,\nSystem\nTest Cases for Workforce Management, Fusion Personalization\nReview Test Scripts for Fusion HCM Applications & Non Fusion Applications\nTrack and prepare the report of testing activities like test testing results, test case coverage,\nrequired\nresources, defects discovered and their status, performance baselines etc.\nDeveloped Test Automation Strategy & Automation Framework for Fusion HCM SAAS\nApplications.\nInvolved in Setting up Test Automation Infrastructure Environment for Automation Test Assists:\na. Test project Setup, Roles and Test planning using Oracle Test Manager\n\nb. Functional Test Script Development using Oracle Open Script\n\nc. Performance Test Script Development Using Oracle Load Tester\nEnsure the timely delivery of different testing milestones.\nConducted LESS (Load Testing, Endurance Testing, Stress Testing, Spike Testing) Testing Activities\nfor\nFusion HCM SAAS Applications.\n\nTitle: WMI, Period: Oct '2010 - Dec '2011\nProject Description: The world's largest retailer implements Oracle Retail applications including\nOracle\nRetail Merchandise System, Oracle Retail Allocation and other elements of the Oracle Retail Suite\nas part of its merchandising transformation initiatives.\nResponsibilities:\n● Direct Client dealings. Getting information from client on requirement for areas where in our\nexpertise in testing can be used and giving proposal.\n● Developed Domain Level Test Strategy\n● Manage entire testing activities from Functional test preparation/execution, Integration test\npreparation/execution, System test preparation/execution\n● Prepared scheduled plan for the client and updating the same in Microsoft Project Plan effective\ntracking of progress of project.\n● Tracking of testing progress in all areas and pointing out gaps to be filled to Client and own\nresources.\n● Review daily and weekly Testers' status reports and take necessary actions and assessing the\nprogress and effectiveness of the test effort\n● Provide estimation for manual and automation testing areas.\n● Tested Oracle Retail Applications ( RMS, RPM, Allocation etc., )\n● Track Schedule and Effort deviation tracking in internal tracking tool.\n● Co-ordination of work between resources from India, USA\n● Expertise in QTP and coming out with an automation framework proposal for the client showing\ncasing advantages of an automated approach\n\n\n\nTest / Project Management\n\nOracle SSI -  Bangalore, Karnataka -\n\nMay 2008 to November 2012\n\nManaged 2 Project and Several minor releases with 50+ members Team of Test leads, Sr. test\nengineer, Test\nEngineers.\n\nTitle: UBS - Fusion HCM SAAS Implementation\n\nEDUCATION\n\nMSC in Computer science\n\nNagarjuna University\n\nBSC in Computer science\n\nKakatiya University\n\nSKILLS\n\nTesting (10+ years), Program Management (10+ years), Automation Testing (10+ years),\nSelenium Webdriver (4 years), Project Management (10+ years), Java (10+ years), AWS (10+\nyears), Cloud Computing (4 years)\n\nADDITIONAL INFORMATION\n\nExpertise\nInvolved and excelling in enterprise-wide initiatives, complex and time-critical business projects\nproviding\ndistinctive leadership at the following levels:\n● Account Management: Understanding inter-related services of the organization; representing\norganization within an account; selling entire portfolio of enterprise services; strategy to action\nby\nsuggesting right processes, skills, culture (people), technology and content; effectively\nmaintaining relationships with customers. Understanding key challenges of the customer;\nfinancial\nperformance of the client; competitors within the account; technology budget or spend; client\nspend on consulting. Key functions: business consulting; aligning IT to business; application\nportfolio review; business analysis/requirements; KPIs/scorecards; involved in writing;\nimplementing and tracking account plans; estimating budgets; business cases; RFPs; proposals\nand\nbusiness presentations.\n● Test Manager: Onsite / Offshore Operations: Involved in all key initiatives of the enterprise\nwith all vendors, pre sales activities, customer engagement, writing proposals, and Test\nconsulting.\n\n\n\nEstablished and managed practice improvement forum onsite with 50+ associates providing\ninputs\nto testing practice and provided lots of trainings.\n● Portfolio/Programme/Project Management level: Delivering programs and projects in managed\nportfolios from Initiation Business Case & Charter, through Requirements, Planning, Budgeting,\nExecution & Release Management, to closeout review while providing auditable progress &\ndeliverable completion visible to the project team, Management and stakeholders. IT projects\ncovering custom solutions, shrink-wrap product development, & commercial off-the-shelf\ndevelopment & integration.\n● Management and Governance establishing governance criteria and enforcing these through\neffective performance management & strategic planning; developing new opportunities;\npreparing\nbusiness cases, POC, POS and application review mechanisms; ensuring measurably high\ncustomer\nsatisfaction; setting BU objectives, educating, mentoring & guiding Sales & Marketing resources.\nDeveloping collaborative relationships with customers, vendors, and product support leaders.\nProviding integrated approach to CMMI, Earned Value, ROI, Training Plans, Satisfaction &\nExpectation Management, and Reporting.\n● Project Office, Project Procurement & Financial Management, developing, establishing &\nmaintaining PMO services, Organizational Governance tools & Systems, Traceability Matrix,\nproject & master Schedules, project Plans, Resource Profiles, organizational Capacity\nManagement, Cost, Risk & Issue, Integration sequence, Communication Plans, Mentoring,\nTraining, Templates, & Performance, for all PMBOK and CMMI disciplines.\n● Architecture, developing frameworks helpful for enterprises on strategic reuse, testing\nstrategy,\nautomation strategy, plans supporting Business Continuity essential to maintaining and\ncommunicating & solutions for business, development & stakeholder leaders & members.\n● Leadership, strong skills in Leadership, Motivation, Negotiation, Team building, Mentoring,\nTraining, Facilitation, Dispute Resolution, Collaboration, Communication & Organization\nDevelopment.\n● Process Engineering, Quality Assurance, Quality Control & Configuration Management,\nInvolved in SDLC definition and customization and method adaptation, process optimization,\nEnterprise-Wide Road Map for Standards Establishment, Components for Enterprise-Wide\nStandards, Standards Enforcement, Quality Assurance Continuum, Quality Control Continuum,\ndeveloping various test frameworks, methodologies and processes, Standard CMMI Appraisal\nmethod for Process Improvement (SCAMPI), establishing Quality Objectives, Goals, Metrics,\nForecasts, Enterprise level Test strategies, & tools, applying to IT Governance Frameworks.\n\nPersonal Strengths:\n● Provides direction and leadership with strong interpersonal and team building skills\n● Successfully works with people within and across organizational boundaries to build\nagreements,\nguidelines, and standards in order to resolve issues and create consistent practices.\n● Strong practitioner of clearly written purposeful communication and direction\n● Developing solutions to deliver quality products to clients through people and processes", "meta": {}, "annotation_approver": null, "labels": [[72, 113, "Email Address"], [30, 49, "Location"], [0, 11, "Name"], [117, 129, "Designation"], [5009, 5011, "Location"], [5022, 5034, "Location"], [5042, 5052, "Location"], [5068, 5077, "Location"], [5085, 5097, "Location"], [5106, 5136, "Location"], [5145, 5156, "Location"], [5164, 5175, "Location"], [5188, 5191, "Location"], [5206, 5215, "Location"], [5248, 5251, "Location"], [5319, 5328, "Companies worked at"], [5307, 5317, "Designation"], [12, 28, "Designation"], [5332, 5351, "Location"], [5355, 5381, "Years of Experience"], [5585, 5600, "Designation"], [5605, 5617, "Designation"], [6954, 6984, "Designation"], [6988, 7008, "Location"], [7012, 7035, "Years of Experience"], [7037, 7049, "Designation"], [7073, 7079, "Location"], [7083, 7112, "Years of Experience"], [7114, 7121, "Location"], [10682, 10687, "Location"], [10689, 10692, "Location"], [10861, 10871, "Companies worked at"], [10834, 10859, "Designation"], [10875, 10895, "Location"], [10899, 10924, "Years of Experience"], [11172, 11191, "College Name"], [11125, 11150, "College Name"], [11100, 11123, "Degree"], [11151, 11170, "Degree"], [5648, 5689, "Companies worked at"], [12383, 12396, "Designation"]]}
{"id": 33, "text": "Srushti Bhadale\nMumbai, Maharashtra - Email me on Indeed: indeed.com/r/Srushti-Bhadale/ffe3d9f99a4b3322\n\nWilling to relocate to: Mumbai, Maharashtra - Mumbai Central, Maharashtra - Pune, Maharashtra\n\nWORK EXPERIENCE\n\nAssociate Consultant\n\nOracle Financial Services -  Bengaluru, Karnataka -\n\nSeptember 2017 to Present\n\n• Currently associated as Associate Consultant with Oracle Financial Services Software.\n• Trained on Java, Plsql, Flexcube for 3 months from Oracle University.\n\nProject Name KeyBank Project\nTeam OBP team\nProject Duration 2 months\nProject Description\n• KeyBank, the primary subsidiary of KeyCorp is the major bank based in Cleveland. It uses\nOracle Banking Platform to provide Online and Mobile Innovation to Meet Customers' Changing\nExpectations.\n• OBP team provides Web services, Business processes, domain services functionality to\nKeyBank.\n\nProject Role and Contribution\n• Worked on migration of OBP product services from 2.3 to 2.6.1 using Java, Plsql, SOAP UI\ntechnologies.\n• Handled and worked on batch uploads.\n• Worked on Code incremental and DB incremental activities.\n• Configured Junit setup for the upgrade activities.\n• Unit Tested and modified the changes required for OBP consulting upgrade.\n• Worked on fixing various defects that were part of OBP product code and database upgrade.\n• Worked on Fund transfer module of OBP.\n\nEDUCATION\n\nHSC\n\nD.G Ruparel College\n\n2013\n\nSSC\n\nConvent Girls' High School\n\n2011\n\nhttps://www.indeed.com/r/Srushti-Bhadale/ffe3d9f99a4b3322?isid=rex-download&ikw=download-top&co=IN\n\n\nSKILLS\n\nAssociate (Less than 1 year), CSS. (Less than 1 year), HTML5 (Less than 1 year), Java (Less\nthan 1 year), JavaScript (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILL SET:\n\n• Programming Languages: C, Java, JavaScript, SQL.\n• Web Technologies: HTML5, CSS.\n• Working Platform: Windows, Linux.\nWORKSHOP/CERTIFICATION:\n\n• Ethical Hacking and IT Security workshop held at Vidyalankar Institute of Technology on 6th\nand 13th February 2016.\n• Certificate for attending Seminar cum Demo Workshop - Cloud and Virtualization held at\nVidyalankar Institute of Technology on 5th March 2016.\n• Microsoft Technological Associate course held at Vidyalankar Institute of Technology from 16th\nAugust to 7th October 2014.\n\nACHIEVEMENT:\n\n• Certificate of merit for securing first place in Semester 5.\n• Awarded scholarship for the academic year […] from Tata Education and Development Trust for\nsecuring distinction in both Semester 5 and Semester 6.", "meta": {}, "annotation_approver": null, "labels": [[0, 15, "Name"], [16, 35, "Location"], [58, 103, "Email Address"], [129, 148, "Location"], [151, 178, "Location"], [181, 198, "Location"], [217, 237, "Designation"], [239, 264, "Companies worked at"], [268, 288, "Location"], [292, 317, "Years of Experience"], [345, 365, "Designation"], [371, 405, "Companies worked at"], [460, 477, "College Name"], [1376, 1395, "College Name"], [1371, 1374, "Degree"], [1397, 1401, "Graduation Year"], [1929, 1964, "College Name"], [2085, 2120, "College Name"], [2191, 2226, "College Name"], [2142, 2182, "Degree"], [2227, 2263, "Graduation Year"], [2121, 2138, "Graduation Year"], [1965, 1994, "Graduation Year"], [420, 424, "Tech Tools"], [426, 431, "Tech Tools"], [433, 441, "Tech Tools"], [660, 683, "Tech Tools"], [1581, 1584, "Tech Tools"], [1606, 1611, "Tech Tools"], [1632, 1636, "Tech Tools"], [1657, 1667, "Tech Tools"], [1736, 1747, "Job Specific Skills"], [1759, 1760, "Tech Tools"], [1762, 1766, "Tech Tools"], [1768, 1778, "Tech Tools"], [1780, 1783, "Tech Tools"], [1787, 1803, "Job Specific Skills"], [1805, 1810, "Tech Tools"], [1812, 1815, "Tech Tools"], [1837, 1844, "Tech Tools"], [1846, 1851, "Tech Tools"], [1880, 1895, "Job Specific Skills"], [1900, 1911, "Job Specific Skills"]]}
{"id": 34, "text": "Sudaya Puranik\nPrincipal Engineer Technical Staff - Company 1\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Sudaya-Puranik/eaf5f7c1a67c6c38\n\nTo secure a promising position that offers both a challenge and a good opportunity for both\nprofessional and personal growth.\n\nWilling to relocate to: Bengaluru, Karnataka\n\nWORK EXPERIENCE\n\nPrincipal Engineer Technical Staff\n\nCompany 1 -  Bengaluru, Karnataka -\n\nSeptember 2005 to Present\n\nTotal Experience: 12 years 6 months.\nWorked onshore and offshore for the projects. Extensive client facing and team management\nskills.\n\nCompany 1: Oracle India Private Limited, Bangalore\nDesignation: Principal Engineer Technical Staff\nDuration: September 2005 - till date\n\nKEY TECHNICAL SKILLS\nDatabases: Oracle 9i/10g, MySql\nLanguages: PL/SQL, Shell Script.\nFrameworks and Portals: Oracle ADF\nServer side Technology: J2ee,\nIDE: Jdeveloper 10.x\n\nTECHNICAL EXPERIENCE SUMMARY\n➢ 1 year Oracle Applications checklist implementation\n➢ 3+ years of Disaster Recovery on Oracle Applications (EBSO 11i, R12) and Database.\n➢ 8+ years of Oracle Applications DBA with Build and Release activities.\n➢ Installation of Oracle Applications (EBSO 11i, R12) at Customer environment and carry out DR\n➢ Configuration of Oracle Applications (EBSO 11i, R12) as per Customer specification.\n➢ Configuration of Discoverer, Admin, Concurrent, Forms, Web, Reports done on separate pillars\n➢ Patching, Cloning of Oracle Apps\n➢ Database conversion from non-RAC to 2 node, 3-node RAC\n➢ Installation of OTO product on customer environment. Including the Infra, Asmt, discoverer,\nOC4J product.\n➢ Carried out Disaster Recovery solutions for OTO\n➢ Database Switchover code written for OTO\n➢ Domain knowledge of Manual Testing.\n➢ Oracle Apps Release and Build Engineer,\n➢ Carried out 11i and R12 Upgrades on Oracle Internal Customers.\n➢ Carried out 12.2.X upgrades from 11i and R12 baselines with 11g and 12c database level\n\nhttps://www.indeed.com/r/Sudaya-Puranik/eaf5f7c1a67c6c38?isid=rex-download&ikw=download-top&co=IN\n\n\n➢ Experienced with Database upgrades as well. Carried out upgrades from 10g to 11g, 11g to\n12C DB.\n➢ Have been awarded Extra Mile Award 2 times for execution of projects on time with quality.\n\nPrincipal Engineer\n\nORACLE -\n\nSeptember 2005 to Present\n\nOracle Apps DBA for R12 and 12.2.X baselines, responsible for all the EBS release activities ,\nhandled upgrades with latest adop technology.\n\nEDUCATION\n\nBachelor of Engineering (Information Science) in Information Science\n\nB V Bhoomaraddi College of Engg and Technology Hubli -  Hubli, Karnataka\n\n2005\n\nVisveswaraiah Technological University -  Belgaum, Karnataka\n\nSKILLS\n\nOracle Apps DBA Release Enginner\n\nADDITIONAL INFORMATION\n\nSoftware/Tools\n• Test cases written in MS Word\n• Carried out manually as QTP does not support this Application\n5) Disaster Recovery Portal for carrying out Recovery on the Customers.\n• It involves a portal through which we provide the Customer details and host types. There are\nxml plug-in, which are installed and parsed as Workflows. The workflows contain the steps of the\nDR for the MT and DB. As and when required we can pause and skip during the recovery. Tool\ntested and verified by the On Demand Operations and Customers like GE, Xerox, RIT etc.\nClient: On Demand Operations, Oracle Bangalore\nRoles and Responsibilities:\n• Developed all the plug-in needed for the recovery which include Database Plug-in, E-business\nSuite Plug-in.\n• Monitoring and Support 24/7\n\nTeam Size: 2\n• Software /Tools: Shell and Expect scripting.\n\n4) DR Solution for R12 (E-Business Suite)\nIt involves setting up the primary by installing Oracle Apps R12, setup the standby site i.e\nDatabase, Mid Tier, by cloning the same and recover. It also involves operations like Switch Over\nand Fail Over.\nBy Switch Over we mean Primary to Standby and vice versa\n\n\n\nBy Fail Over we mean the Primary has crashed, we need to make the Standby as the Primary.\nClient: On Demand Operations, Oracle Bangalore\n\nTeam Size: 2\nSoftware /Tools: Shell and Expect scripting, PLSQL.\n\nRoles and Responsibilities:\n\n• Installation, Cloning, Patching of the Oracle Applications R12.\n• Conversion of Database from non-RAC to RAC\n• Setting up of the Standby.\n• Carry out the Switchover, Fail over and Recovery.\n• Carryout the whole Recovery Process through the UI.\n\n3) On Demand Service Continuity\n\nThe project dealt with \"Disaster Recovery & Backup\" which involves automating checks so that\ndisaster could be prevented. Also the recovery of database is involved.\nClient: On Demand Operations, Oracle Bangalore\nTeam Size: 5\nSoftware /Tools:\nUnix Advanced shell scripting and expect. (Involves working on Korn Shell)\n\nRoles and Responsibilities:\n• Automated the checklists for E-business suite and PeopleSoft products.\n\n2) CCB Portal Administration\n\nProject Description\n\nThe new CCB Administration component is an integrated, web-based application intended to\nconsolidate and simplify the CCB processes for On Demand. This application:\n\n• Makes it easier to manage the process of change implementation in customer instances across\nthe various milestones in the On Demand life cycle.\n\n• Simplifies said change implementation by keeping the information organized in a centralized\nrepository.\n\n• In the CCB Administration application Release 1.0\nClient: System Assurance Centre, Oracle Unites States of America\n\nTeam Size: 3\nSoftware /Tools:\n\nPL/SQL and HTML\n\nRoles and Responsibilities:\n\n\n\n* Developed the PL/SQL procedures & packages, HTML forms required for the Portal, using HTML\nfor the user to enter the required data.\n* The data is entered is validated, using Java Scripting, and the data is stored in the database.\n* Developed the reports.\n\n1) ORACLE PRODUCTION ASSESSMENTS\n\nProject Description\nAUTO VERIFY TOOL\nAuto Verify is a utility that automates many of the tedious and repetitive tasks that are involved\nduring QA testing of an Oracle Applications Release 11i environment. Auto Verify now performs\nmany of the tasks automatically that used to be performed manually.\nClient: System Assurance Centre, Oracle Unites States of America\n\nTeam Size: 4\nSoftware /Tools:\nUNIX Advanced Shell Scripting, PL/SQL.\n\nRoles and Responsibilities:\nAutomated the all the checklists for 8 releases of E-business suite.", "meta": {}, "annotation_approver": null, "labels": [[15, 49, "Designation"], [0, 14, "Name"], [63, 83, "Location"], [106, 150, "Email Address"], [303, 323, "Location"], [342, 376, "Designation"], [391, 411, "Location"], [415, 440, "Years of Experience"], [619, 628, "Location"], [642, 685, "Designation"], [687, 713, "Years of Experience"], [2246, 2252, "Companies worked at"], [2224, 2244, "Designation"], [2256, 2281, "Years of Experience"], [2436, 2481, "Degree"], [2485, 2558, "College Name"], [2562, 2578, "Location"], [2580, 2584, "Graduation Year"], [2586, 2625, "College Name"], [2628, 2646, "Location"], [52, 61, "Companies worked at"], [376, 388, "Companies worked at"], [460, 477, "Years of Experience"], [578, 587, "Companies worked at"], [589, 617, "Companies worked at"], [553, 568, "Soft Skills"], [736, 745, "Job Specific Skills"], [747, 760, "Tech Tools"], [762, 767, "Tech Tools"], [779, 785, "Tech Tools"], [787, 798, "Tech Tools"], [825, 835, "Tech Tools"], [860, 864, "Tech Tools"], [871, 886, "Tech Tools"], [926, 945, "Tech Tools"], [2656, 2667, "Tech Tools"], [2753, 2761, "Tech Tools"], [4607, 4611, "Tech Tools"], [4621, 4636, "Tech Tools"], [5469, 5475, "Tech Tools"], [5499, 5503, "Tech Tools"], [5541, 5545, "Tech Tools"], [5629, 5633, "Tech Tools"], [5888, 5898, "Job Specific Skills"], [5905, 5924, "Tech Tools"], [6139, 6143, "Tech Tools"], [6153, 6168, "Tech Tools"], [6170, 6176, "Tech Tools"], [5405, 5411, "Tech Tools"], [5416, 5420, "Tech Tools"]]}
{"id": 35, "text": "Sumit Kubade\nSAP - FI Support Consultant - SAP FI\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Sumit-Kubade/256d6054d852b2a7\n\nSeeking a challenging and rewarding position in SAP FI that will benefit from my experience,\nprofessional qualification and excellent communication skills and where I can enrich my skills\nand management capabilities further while working to achieve the organizational goals.\n\n• 1.0 Months experience in IT industry.\n• I am seeking challenging assignments in a growth oriented organisation.\n• Completed graduation B.com (FINANCE) Ability to work overtime, time management, and work\nresponsibilities.\n• ERP knowledge of SAP ECC 6.0IN FI MODULE in UV TECHNOCRATS & SOLUTION. With knowledge\nof (FI organisational structure, GL creation, Accounts Payable, Accounts receivables, customer\nmaster data, vendor master data & All End user scenario)\n• Proficient Knowledge about FI End user scenario and design of configuration of FI sub-modules\nGeneral Ledger (FI-GL), Accounts Payable (FI-AP), Accounts Receivables (FI-AR)\n\nSAP FINANCE MODULE TECHNICAL &FUNCTIONAL SKILL\n\n• Enterprise structure: configuration of Define &Assign Company and company code define\nbusiness area.\n• Global setting: Define Field status group, Fiscal year Variants, open and close posting\nperiod, document number ranges, Setting up of document types and posting keys for business\ntransactions.\n• General ledger: Define chart of account, Account group, define tolerance group for GL &\nemployees.\n• GL end user scenario-creation of GL master data, knowledge about F 02/FB50 GL invoice\nposting, GL Documents recurring document, park & held document, reversal document.\n• Accounts Payable: Creation of vendor a/c group & No ranges. Define tolerance group for vendor,\ncreation of sundry creditors GL, Display of vendor balances, Define no range for invoice posting,\n• AP end user scenario: creation of customer master, FB60 /MIRO invoice posting, Payment,\nposting Process of partial payment and down payment to vendor.\n• Customization of APP program for vendors.\n• Accounts Receivable: Define customer a/c group, No range for customer a/c, creation of no.\nranges\n• AR end user scenario: creation of customer master, FB70 invoice posting, payment posting\nprocess of partial payment down payment to customer.\n• House bank- Creation of house bank, creation of cheque lot, display cheque register, cheque\nencashment, cheque cancellation.\n• Foreign exchange transaction.\n• Asset Accounting - Chart of depreciation, creation of input & output tax code, Define asset\nclass, Depreciation,\nAsset master creation, Asset purchase posting (F 90), Depreciation run (AFAB), Sale of asset (f 92)\n• Closing Entries - Carry forward the balances of customer & vendor (f.07), Asset balances ( AJAB),\nGL balances (F.16), No range (OBH2) .FSV.\n\nWilling to relocate to: Pune, Maharashtra\n\nhttps://www.indeed.com/r/Sumit-Kubade/256d6054d852b2a7?isid=rex-download&ikw=download-top&co=IN\n\n\nWORK EXPERIENCE\n\nSAP - FI Support Consultant\n\nSAP FI -  Pune, Maharashtra -\n\nJune 2016 to Present\n\nRoles and Responsibility:\n• Providing production support for SAP FI Module\n• Master data Creation and Changes as when required\n• Interaction with end users for issue resolution\n• Solving issues/tickets with moderate and at times critical impact\n• Proactively discuss on issues with other functional consultants for timely resolution\n• Participation in regular FI team meetings\n• Provide training to end user's as and when required.\n• Customizing changes as per new requirement raised by client like House Bank Creation\n• Analyzing and providing solutions on the issues raised by the client\n• Participation in performing year end closing activity\n• Solving of maintenance Issues and Tickets in the area of FI.\n• Email response to end users.\n• Clarify and rectify the pending and due issues.\n• Resolved User issues on timely basis.\n• Based on the priority of the issues and the time required to resolve the issue, issues will be\nresolved within time bound to meet the SLA.\n• Attended KT sessions & updated knowledge with new issues.\n\nAs a part of Support Team Involvement in\n\n• User Support and also End User Training.\n• Handled the End user queries through Help desk. As per the user communication by mail have\nto register, acknowledge, respond, resolved, accept and close the issues.\n• The issues include the configuration, transaction error and program/form modifications.\n• Configuring payment terms, Configuring automatic payment program, Includes House bank\nconfiguration.\n• Configuration for special G/L transactions like down payment made, down payment received.\n• Input Tax Indicator Configuration.\n\nEDUCATION\n\nIndian Institute Of Company Secretory Of India (Appeared)\n\n2014\n\nMaharashtra State Government, Pune University -  Pune, Maharashtra\n\n2013\n\nB.com\n\nPune University -  Pune, Maharashtra\n\n\n\n2012\n\nSKILLS\n\nSAP (1 year)\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILL\n\n• ERP Packages: SAP ECC 6.0, Tally.\n• Office productivity: Microsoft Word, Excel, Power point.\n• Operating Systems: Windows […]", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [13, 40, "Designation"], [43, 49, "Companies worked at"], [51, 68, "Location"], [91, 133, "Email Address"], [2845, 2862, "Location"], [2977, 2987, "Companies worked at"], [2988, 3014, "Designation"], [3018, 3035, "Location"], [3039, 3059, "Years of Experience"], [4679, 4736, "College Name"], [4738, 4742, "Graduation Year"], [4793, 4810, "Location"], [4812, 4816, "Graduation Year"], [4818, 4823, "Degree"], [4825, 4840, "College Name"], [4844, 4861, "Location"], [4865, 4869, "Graduation Year"], [4744, 4789, "College Name"], [548, 563, "Degree"], [636, 649, "Job Specific Skills"], [653, 656, "Tech Tools"], [4936, 4939, "Tech Tools"], [4950, 4961, "Tech Tools"], [4993, 5007, "Tech Tools"], [5009, 5014, "Tech Tools"], [5016, 5027, "Tech Tools"], [5050, 5057, "Tech Tools"]]}
{"id": 36, "text": "Syam Devendla\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Syam-Devendla/\nc9ba7bc582b14a7b\n\nSenior software engineer with more than 9 years of experience in C, C++, Data\nStructures programming and BI analytics. Experience includes implementation and\ntesting of enterprise and mobile application and middleware component software.\n\nWORK EXPERIENCE\n\nSMTS\n\nOracle India -  Bengaluru, Karnataka -\n\nJanuary 2014 to Present\n\nwith OBIEE team.\n• Worked in Samsung R&D Operations, Bangalore since Oct- 2008 to Jan 2014, with\nMultimedia team.\n\nEDUCATION\n\nPost Graduate Diploma in Embedded Systems\n\nCDAC -  Kochi, Kerala\n\n2006\n\nBachelor of Technology in Electronics and Communications\n\nNagarjuna University\n\n2005\n\nSKILLS\n\n.NET (Less than 1 year), ALGORITHMS (Less than 1 year), ALSA (Less than 1 year), ANDROID\n(Less than 1 year), APACHE HADOOP HDFS (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nCore Competencies\n\n• Extensively worked in C, C++\n• Good understanding of Data Structures and Algorithms\n• Good understanding and knowledge of BI Analytics of OBIEE.\n• Codes at HackerRank.com and am a 4 Star Rank coder in algorithms, coded\nusing C++ STL\n• Hands on experience of GDB Debugger for Core dump analysis and Server\n\nhttps://www.indeed.com/r/Syam-Devendla/c9ba7bc582b14a7b?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Syam-Devendla/c9ba7bc582b14a7b?isid=rex-download&ikw=download-top&co=IN\n\n\nside debugging.\n• Hands on experience of Visual Studio debugger for debugging on Windows\nplatform.\n• Working experience of Multithreaded applications for more than eight years\n• Good understanding of OOPS Design principles and STL.\n• Experienced in using various debuggers that include GDB debugger, Eclipse, VC++ IDE, Trace\n32, WinDbg, Visual Studio.\n• Good knowledge of Big data technologies(Hadoop, Yarn Architecture, map-\nreduce, Hive, Sqoop, Hbase, Zookeeper)\n• Good experience in Hadoop development and Admin activities.\n• Able to understand and review Java and Scala code\n• Good Exposure to Software practices, SDLC.\n• Good understanding and porting knowledge of framework components.\n• Good understanding of multimedia concepts, Audio video synchronization,\nAudioOut, AudioIn and video zoom.\n• Knowledge of WEBRTC features.\n• Good knowledge of video Engine.\n• Basic knowledge of MFC, socket programming.\n• Good knowledge in programming and operating system concepts.\n• Experience in working in a project from product development phase to\ncommercialization phase.\n• Successfully commercialized more than 25 million mobiles spanning across\ntwenty models.\n• Good global work exposure having worked in different work environments.\n• Basic understanding of Linux Operating system.\n• Experienced in design and implementation of Multimedia Applications for\nMobile handsets.\n• Good experience working on Smart Phone platforms(Framework, Middleware\nand Application development) having worked on various mobile platforms\nSHP, Samsung Bada, Linux, WindowsMobile5.0, SLP, Android and Tizen.\nSyam Prasad Devendla\n• Good knowledge and understanding of different frameworks for multimedia.\n• Good knowledge of multi-threaded programming and IPC mechanisms.\n• Excellent interpersonal and communication skills and ability to work in a team.\n• Skilled at learning new concepts quickly, working well under pressure, and\ncommunicating ideas clearly and effectively.\n• Tools Used: Beyond Compare, Samsung memory leak tool, and Samsung code\ncoverageTool, VS 2005 Remote Tools, KlocWork, WinShark and Ethereal.\n• Experienced in working with configuration tools like VSS, Perforce, and Clear\nCase.\n\n• Operating Systems: Windows, Ubuntu Linux\n• Mobile Platforms: SHP, Bada, Linux, WindowsMobile5.0, SLP and Tizen\n• Technology: Multimedia, Content, Mobile Applications\n• Languages: C, C++(Data Structures, Design Patterns, STL)\n• Tools: Eclipse, VS2005, VS2010\nKlocWork, Clear case, Perforce\n\nProjects\n\n\n\n• OBIEE\nEnvironment: Oracle BI Languages: C++\n• Implementing enhancements, improving the diagnosabilty and address the customer\nissues by fixing the code bugs.\n• Developed a security feature in downloads module to protect the system from DOS\nattack.\n• Implemented a performance enhancer logic in Ibots to execute the agents faster.\n• Responsible for fix/enhance Ibot(Delivers/Agent/Schedulers) issues in the product.\n• Responsible for fixing the core dump issues reported by customers in the nqscheduler\ncomponent in linux 32/64 and windows 32/64 platforms.\n• Oracle-Thirdeye\nEnvironment: HDFS, Yarn, Hadoop, map-reduce, thirdeye-agent\n• GUI is able to provide all information in a usable environment.\n• Most of the use will be as an embeddable UI in applications like TLP, DTE, and Bug DB\netc.\n\nSyam Prasad Devendla\n• Provide term, line number and time indexes for word and phrase search, ability to drill\ndown to a given line number and data around it, and also have ability to see data\naround a given time.\n\n• Thirdeye-nodes-refresh\nEnvironment: HDFS, Yarn, Hadoop, map-reduce\n• All processes for thirdeye including Hadoop components running on the same node. This\noverburdens the master node and leads to frequent failures due to disk space and\nmemory issues, so worked on POC to replicate entire thirdeye setup with required\nconfiguration on new machines.\n\n• BI-Manifest-Comparison Tool\nEnvironment: Oracle BI Languages: Java\n• Implemented tool to create an excel with manifests data across platforms and painted the\nmismatched versions\n• WebRTC\nEnvironment: Browser Languages: C++\n• Media Recorder implementation.\n• Multi Party communication development.\n• Stage Fright (recorder and playback)\n• Video engine\n• Peer connection\n• PlatformsY2012-Framework (content)\nEnvironment: Tizen Languages: C++\n• Developing Tizen framework (content) on SLP.\n• File Search\n• Directory Search\n• Playlist and Playlist Manager\n• Supporting Tizen content framework.\n• PlatformsY2011 -Framework (Multimedia- player)\nEnvironment: Bada, Eclipse, SLP (Samsung Linux Platform) Languages: C++\n\n\n\n• Implemented player framework for Bada OS\n• Audio Player and video Player modules.\n• POC of AV synchronization for video zoom.\n• Supporting Bada2.0 player framework and commercialization.\n• Movie Studio (Video Editor)\nEnvironment: Bada, Eclipse Languages: C++\n• Developed application based on UI guidelines given, modules like preview, split and trim\nforms using VePlayer library.\n• Implemented VPL file creation.\n• Media Browser\nEnvironment: Bada, Eclipse Languages: C, C++\n• This application is developed to check the stability of Bada's player and Audio out\nmodules.\n• Audio Player and video Player modules.\n• Implemented progress bar of player in both Player Form and PLS Player Form.\n• Bada-NS(R&D)\nEnvironment: Linux, SLP (Samsung Linux Platform) Languages: C, C++\n• Worked on launching emulator\n• knowledge of SLP Multimedia Framework\n• Ported Player (Audio and Video) module\n• Ported Audio Out and Audio In modules using ALSA library\n• ETMs Firmware Client: Wabtec Railway Electronics, German Town\n\nSyam Prasad Devendla\nEnvironment: Windows XP, IAR Workbench Hardware: IOC board (Provided by WRE)\nLanguages: C\n• Serial - Ethernet Bridge\n• Serial communication commands handling.\n• Supt Link\nClient: Schindler Elevator & Escalator Corporation\nEnvironment: Windows Mobile 5.0, embedded VC, Visual Studio 2005\nLanguages: VC++, MFC\n• A Business application. It makes easy for the superintendents to check the status of the\nelevators and escalators which are newly installed or being maintained and rate them\naccording to their performance and report the same to the Sap server using mobility\nsolutions.\n• FldLink\nClient: Schindler Elevator & Escalator Corporation\nEnvironment: Windows Mobile 5.0, embedded VC, Visual Studio 2005\nLanguages: VC++, MFC\n• FldLink is a Mobile Application which provides the technicians with a single,\ncomprehensive view of contact information, meeting schedules, Technical information\nand repairing support while working in the field.", "meta": {}, "annotation_approver": null, "labels": [[0, 13, "Name"], [14, 34, "Location"], [57, 101, "Email Address"], [103, 127, "Designation"], [359, 377, "Companies worked at"], [381, 401, "Location"], [405, 428, "Years of Experience"], [459, 481, "Companies worked at"], [483, 492, "Location"], [493, 520, "Years of Experience"], [556, 577, "Degree"], [581, 603, "College Name"], [607, 620, "Location"], [622, 626, "Graduation Year"], [628, 650, "Degree"], [654, 706, "College Name"], [706, 712, "Graduation Year"], [143, 164, "Years of Experience"], [168, 169, "Tech Tools"], [171, 174, "Tech Tools"], [176, 203, "Job Specific Skills"], [208, 220, "Job Specific Skills"], [722, 726, "Tech Tools"], [747, 757, "Job Specific Skills"], [778, 782, "Tech Tools"], [803, 810, "Tech Tools"], [831, 849, "Tech Tools"], [937, 938, "Tech Tools"], [940, 943, "Tech Tools"], [968, 983, "Job Specific Skills"], [988, 998, "Job Specific Skills"], [1037, 1049, "Job Specific Skills"], [1053, 1058, "Tech Tools"], [1140, 1143, "Tech Tools"], [1617, 1639, "Job Specific Skills"], [1703, 1715, "Tech Tools"], [1717, 1724, "Tech Tools"], [1726, 1730, "Tech Tools"], [1736, 1744, "Tech Tools"], [1746, 1752, "Tech Tools"], [1754, 1767, "Tech Tools"], [1789, 1797, "Job Specific Skills"], [1811, 1817, "Tech Tools"], [1819, 1836, "Tech Tools"], [1838, 1849, "Tech Tools"], [1851, 1855, "Tech Tools"], [1857, 1862, "Tech Tools"], [1864, 1869, "Tech Tools"], [1871, 1880, "Tech Tools"], [1976, 1980, "Tech Tools"], [1985, 1990, "Tech Tools"], [2349, 2360, "Job Specific Skills"], [2677, 2682, "Tech Tools"], [3781, 3782, "Tech Tools"], [3784, 3787, "Tech Tools"], [3946, 3949, "Tech Tools"], [3845, 3851, "Tech Tools"], [3853, 3859, "Tech Tools"], [5487, 5490, "Tech Tools"], [5704, 5708, "Tech Tools"], [5975, 5978, "Tech Tools"], [7024, 7031, "Tech Tools"], [7099, 7100, "Tech Tools"], [7309, 7313, "Tech Tools"], [7697, 7715, "Tech Tools"]]}
{"id": 37, "text": "Tejasri Gunnam\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Tejasri-Gunnam/6ef1426c95ee894c\n\n• 3 years of experience in IT industry, involved in Software Testing.\n• Proficient in development of Test Plans, Functional test scriptsfor Functionality Testing and\nIntegration Testing, Test Execution, Defect Reporting, RegressionTesting.\n• Very Good in GUI, Functional and Performance testing.\n• Well Acquainted With Bug Tracking Tools like CDETS, BUG SCRUBBER, MANTIS, JIRA\n• Profound Knowledge in Software Testing Life Cycle.\n• Involved in Preparing Test Cases and Test Reports.\n• Knowledge on Quality Standards like ISO\n• CCNA certified\n• Very good knowledge in security testing.\n• Knowledge in Python scripting.\n\nWORK EXPERIENCE\n\nCisco SystemsIndPvtLtd -  Bengaluru, Karnataka -\n\nSeptember 2012 to Present\n\nunder the payroll of GoldStone Technologies\n\nAgile team Member, Testing, Security testing\n\nCisco SystemsIndPvtLtd -  Bengaluru, Karnataka -\n\nSeptember 2012 to Present\n\nDuration Sep 2012 - Till date\nTeam Size 15 Members\nRole Agile team Member, Testing, Security testing\n\nDescription:\nCSPC is a collector which is integrated with different CSOs (Common Service Objects) like the\ndevice discovery CSO, core collection, connectivity and data normalization. Together we call as\nCSPC or Common Services Platform Collector.CSPC-NOS is an add-on service which is written\non top of the CSPC i.e., extra software written by the Nos team based on the CSPC 2.0 base\ncode to cater to a group of customers. CSPC-NOS support on both Windows and Linux Cent OS\nplatform. The Windows CSPC-NOS client can connect to the CSPC-NOS Linux server and vice-\nversa. Devices can be added manually or using the import seed file. When importing a seed file/\nWhen Device discovery is triggered the Device discovery module collects light inventory data\nfrom the devices. The light inventory data contain information like Sys Object Id, device family,\nOS-version etc. . (This information collected can be seen by going to Reports->Managed Devices)\nThis light inventory data is used by the inventory module to load the model rule for the respective\ndevice. This data is also used by the base collector to decide whether the device is managed\nor unmanaged.\n\nResponsibilities:\n\nhttps://www.indeed.com/r/Tejasri-Gunnam/6ef1426c95ee894c?isid=rex-download&ikw=download-top&co=IN\n\n\n➢ Involved Identifying Scenarios to be tested\n➢ Preparing Test Cases\n➢ Executing Test Cases\n➢ Automating Testcases using TCL scripting\n➢ Preparing Progress Report for Sign Off\n➢ Test Plan\n➢ Traceability Matrix\n➢ Order Progression\n➢ Raising and reporting defects to dev team\n➢ Defect Management on monthly basis\n➢ Participated in Defect prevention meetings on quarterly basis to reduce defect density.\nSecurity Testing:\n➢ Threat Modelling\n➢ Gap Analysis using CSERV\n➢ Manual Penetration Testing (Proxy testing and SSL scanning)\n➢ Vulnerability Assessment using Nessus and Retina tools\n\nDECLARATION\nI hereby declare that the above-mentioned information is correct up to my knowledge and I bear\nthe responsibility for the correctness of the above-mentioned particulars.\n\nDate:\nPlace:Bangalore ( G Tejasri)\n\nEDUCATION\n\nB.Tech in Electrical and Electronics Engineering\n\nPondicherry University -  Puducherry, Puducherry\n\nM.Tech in Power Electronics and Instrumentation Engineering\n\nJawaharlal Nehru Technological University\n\nSKILLS\n\nGap Analysis (5 years), Nessus (5 years), Security (5 years), SSL (5 years), testing (5 years)\n\nADDITIONAL INFORMATION\n\nSKILL SET\nDatabases: Oracle &SQL Server\nOperating System: MS-DOS, Windows 98/XP &UNIX, Windows 7\nLanguages: C, C++\nTesting Tools: Q.T.P, Win Runner7.5, Load Runner 9.5\nNetworking: Hands on Experience On Cisco devices, AAA, TACACS, RADIUS, VPNs\nSecurity: Security scans using Nessus and Retina tools, Penetration\ntesting using Proxy and SSL scans, GAP Analysis using CSERV", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [15, 35, "Location"], [58, 102, "Email Address"], [106, 113, "Years of Experience"], [740, 762, "Companies worked at"], [766, 786, "Location"], [790, 815, "Years of Experience"], [908, 930, "Companies worked at"], [934, 954, "Location"], [958, 1002, "Years of Experience"], [3140, 3162, "Location"], [3185, 3247, "College Name"], [3175, 3181, "Degree"], [3251, 3261, "Location"], [3263, 3274, "Location"], [3275, 3281, "Degree"], [3285, 3377, "College Name"], [156, 172, "Job Specific Skills"], [205, 215, "Job Specific Skills"], [217, 240, "Job Specific Skills"], [244, 265, "Job Specific Skills"], [270, 289, "Job Specific Skills"], [291, 305, "Job Specific Skills"], [307, 323, "Job Specific Skills"], [325, 342, "Job Specific Skills"], [359, 362, "Job Specific Skills"], [364, 398, "Job Specific Skills"], [423, 441, "Job Specific Skills"], [447, 452, "Tech Tools"], [454, 466, "Tech Tools"], [468, 474, "Tech Tools"], [476, 480, "Tech Tools"], [505, 532, "Job Specific Skills"], [671, 687, "Job Specific Skills"], [704, 710, "Tech Tools"], [2408, 2428, "Job Specific Skills"], [2431, 2451, "Job Specific Skills"], [2455, 2474, "Job Specific Skills"], [2481, 2484, "Tech Tools"], [2538, 2547, "Job Specific Skills"], [2572, 2589, "Job Specific Skills"], [2781, 2797, "Job Specific Skills"], [3387, 3399, "Job Specific Skills"], [3411, 3417, "Tech Tools"], [3429, 3437, "Job Specific Skills"], [3449, 3452, "Tech Tools"], [3464, 3471, "Job Specific Skills"], [3517, 3526, "Job Specific Skills"], [3528, 3534, "Tech Tools"], [3536, 3546, "Tech Tools"], [3565, 3571, "Tech Tools"], [3573, 3580, "Tech Tools"], [3588, 3592, "Tech Tools"], [3594, 3601, "Tech Tools"], [3615, 3616, "Tech Tools"], [3618, 3621, "Tech Tools"], [3637, 3642, "Tech Tools"], [3644, 3654, "Tech Tools"], [3659, 3670, "Tech Tools"], [3854, 3866, "Job Specific Skills"], [3843, 3846, "Tech Tools"]]}
{"id": 38, "text": "Urshila Lohani\nSenior Corporate Account Executive - MongoDB\n\nGurgaon, Haryana - Email me on Indeed: indeed.com/r/Urshila-Lohani/ab8d3dc6dd8b13f0\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSenior Corporate Account Executive\n\nMongoDB -  Gurgaon, Haryana -\n\nMay 2016 to Present\n\n• Designed and implemented a 2-year sales strategy for South India Region; revenues grew 4X.\n• Trained sales team of 35 from 20 partner companies; revenues generated through partners\nincreased 50%.\n• Led Business development team of 5 to build pipeline of 4X.\n• Acquired 32 new accounts with industry leaders including Intuit, IBM, Wipro, McAfee, Airtel,\nReligare and Adobe; 100% renewals in all existing accounts.\n• Initiated, designed and executed marketing events; attendees included 200 IT heads;\ngenerated $1M\npipeline.\n• Ranked in top 5% of global sales team of 322; Awarded thrice for highest quarterly revenues\nin APAC.\n• Won Excellence Club Award in FY17 and FY18.\n\nAccount Manager\n\nRed Hat -  Bengaluru, Karnataka -\n\nJune 2014 to May 2016\n\n• Responsible for sales of entire Red Hat Product Portfolio in Mid market and Enterprise Accounts\nin West and South India Region.\n• Introduced Customer Success Program; renewals up 20%; revenues rose 12%.\n• Formulated sales strategies and achieved $4M in sales.\n• Won multiple awards (four quarters - highest revenues closed) and (2 consecutive years - 100%\nClub Award).\n• Improved brand presence in small cities and towns; inducted new partners; revenue driven\nby partner\nchannels up 26%\n• Designed events engaging IT Directors & CxOs; penetrated 7 key accounts; generated $400K\npipeline.\n\nAccount Manager\n\nOracle -  Noida, Uttar Pradesh -\n\nMay 2013 to May 2014\n\nhttps://www.indeed.com/r/Urshila-Lohani/ab8d3dc6dd8b13f0?isid=rex-download&ikw=download-top&co=IN\n\n\nBusiness Development Rep\n\nOracle -\n\nSeptember 2011 to April 2013\n\n• Responsible for MySQL, Oracle Linux and VM Sales in North Central US Region.\n• Generate opportunities using Linkedin, Hoovers, Job Portals, Marketing Leads and Oracle Install\nbase.\n• Work closely with Channel Partners, Resellers and Oracle Internal Counterparts to increase\ncustomer base.\n• Designed & developed Pipeline Generation kits for Sales team of 12.\n• Awarded in Q1 and Q2 FY13 for highest quarterly achievement in the team; 100% Annual Quota\nachieved for FY12 and FY13.\n• Revamped email marketing campaigns led to 15% higher response rate.\n• Initiated a structured mentorship program for MySQL Team; Training times down by 2 Months;\nproductivity\nup 50%.\n\nEDUCATION\n\nB Tech Honors in Technical\n\nCollege of Engineering -  Roorkee, Uttarakhand\n\nAugust 2007 to May 2011", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [15, 49, "Designation"], [52, 59, "Companies worked at"], [61, 77, "Location"], [100, 144, "Email Address"], [194, 228, "Designation"], [230, 238, "Companies worked at"], [241, 257, "Location"], [261, 280, "Years of Experience"], [957, 972, "Designation"], [985, 1005, "Location"], [1009, 1030, "Years of Experience"], [1623, 1638, "Designation"], [1640, 1646, "Companies worked at"], [1650, 1670, "Location"], [1674, 1694, "Years of Experience"], [974, 982, "Companies worked at"], [1796, 1821, "Designation"], [1822, 1828, "Companies worked at"], [1832, 1860, "Years of Experience"], [2540, 2553, "Degree"], [2557, 2590, "College Name"], [2594, 2614, "Location"], [2616, 2639, "Graduation Year"], [318, 332, "Job Specific Skills"]]}
{"id": 39, "text": "Vamsi krishna\nhyderbad, Telangana - Email me on Indeed: indeed.com/r/Vamsi-krishna/15906b55159d4088\n\nWilling to relocate to: hyderbad, Telangana\n\nWORK EXPERIENCE\n\nSoftware developer\n\nMicrosoft\n\nIam need \nWhat i am\n\nEDUCATION\n\nBsc\n\nShri gnanambika degree college\n\nSKILLS\n\nAnalytics, Research\n\nhttps://www.indeed.com/r/Vamsi-krishna/15906b55159d4088?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 22, "Name"], [24, 33, "Location"], [56, 99, "Email Address"], [163, 181, "Designation"], [183, 193, "Companies worked at"], [226, 229, "Degree"], [231, 261, "College Name"], [125, 144, "Location"], [271, 280, "Soft Skills"], [282, 290, "Soft Skills"]]}
{"id": 40, "text": "VARUN AHLUWALIA\nQuantitative Analyst\n\n- Email me on Indeed: indeed.com/r/VARUN-AHLUWALIA/725d9b113f3c4f0c\n\nWORK EXPERIENCE\n\nSoftware Engineer\n\nTavant -  Bangalore, Karnataka -\n\nApril 2005 to April 2006\n\n• Implemented online retail sale management solution for industrial manufacturing\ngiant Ingersoll Rand.\n• Implemented secondary mortgage solution for leading wholesale lender Ameriquest.\n\nSoftware Engineer\n\nPatni -  Bangalore, Karnataka -\n\nAugust 2004 to April 2005\n\n• Built profitability reports for using Oracle Financial Analytics\n\nEDUCATION\n\nMaster of Science in Financial Mathematics\n\nUniversity of Chicago -  Chicago, IL\n\nJune 2010\n\nBachelor of Technology in Civil Engineering\n\nIndian Institutes of Technology IIT Kanpur\n\nJuly 2004\n\nADDITIONAL INFORMATION\n\nSKILLS\nProgramming • JAVA, C++, C, Matlab, SQL\n\nOperating System • Windows, Linux\n\nhttps://www.indeed.com/r/VARUN-AHLUWALIA/725d9b113f3c4f0c?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 15, "Name"], [16, 36, "Designation"], [60, 105, "Email Address"], [124, 141, "Designation"], [153, 173, "Location"], [177, 201, "Years of Experience"], [391, 408, "Designation"], [419, 439, "Location"], [443, 468, "Years of Experience"], [549, 566, "Degree"], [569, 615, "College Name"], [618, 629, "Location"], [631, 640, "Years of Experience"], [642, 664, "Degree"], [668, 722, "College Name"], [723, 729, "Location"], [731, 740, "Years of Experience"], [410, 416, "Companies worked at"], [217, 246, "Job Specific Skills"], [143, 149, "Companies worked at"], [773, 784, "Job Specific Skills"], [787, 791, "Tech Tools"], [793, 796, "Tech Tools"], [798, 799, "Tech Tools"], [801, 807, "Tech Tools"], [809, 812, "Tech Tools"], [833, 840, "Tech Tools"], [842, 847, "Tech Tools"]]}
{"id": 41, "text": "Vijayalakshmi Govindarajan\nSAP as a Consultant - SAP Basis\n\nChennai, Tamil Nadu - Email me on Indeed: indeed.com/r/Vijayalakshmi-Govindarajan/\nd71bfb70a66b0046\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSAP as a Consultant\n\nSAP Basis -\n\nMay 2012 to Present\n\nInvolved in 3 support Projects and 1 implementation Project.\n• System Build, System Refresh, system upgrade and system maintenance activities.\n• Self-motivated and goal-oriented, with a strong ability to organize and prioritize tasks\nefficiently.\n• Excellent team player with good communication, analytical, problem solving skills.\n• Certification Done: Oracle certified Java Programmer\n• Finished Diploma in Java course from NIIT\n\nEDUCATION\n\nMCA in Computer Applications\n\nThiagarajar School of Management -  Madurai, Tamil Nadu\n\nBSc\n\nSri Sathya Sai Institute of Higher Learning -  Anantapur, Andhra Pradesh\n\nHSC\n\nTVS Lakshmi Matriculation Higher Secondary School -  Madurai, Tamil Nadu\n\nSSLC\n\nTVS Lakshmi Matriculation Higher Secondary School -  Madurai, Tamil Nadu\n\nSKILLS\n\nJAVA (6 years), ORACLE (6 years), SAP (6 years), ABAP (Less than 1 year), ACCESS (Less than 1\nyear)\n\nADDITIONAL INFORMATION\n\nTECHNICAL EXPERTISE\n\nhttps://www.indeed.com/r/Vijayalakshmi-Govindarajan/d71bfb70a66b0046?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Vijayalakshmi-Govindarajan/d71bfb70a66b0046?isid=rex-download&ikw=download-top&co=IN\n\n\nOperating System\n• Worked on UNIX AIX and Windows Environment.\n• Troubleshooting & Identifying OS level Bottle necks.\n• Monitoring Top CPU Utilization/Memory Utilization issues at OS-level.\n• Administrating File system and permission issues at OS-Level.\nDatabase Administration\n• Database administration on Oracle 10 g, 11 g, 12 c.\n• Monitoring & perform database backup at DB13 level.\n• Cluster Administration on Oracle 11 g, 12 c..\nSAP HANA Database Administration\n• Monitoring HANA DB and to troubleshoot performance issues using HANA studio.\n• Importing and activating views and procedures.\n• Troubleshooting HANA data and log backup issues.\n• Monitoring top CPU Utilization/Memory Utilization issues at OS-level.\n• SLT Replication of tables from ECC to HANA DB.\nThird Party Tools Worked\n• Open text.\n• PAS - Pay metric Adapters.\n• TRAX\n• HP, Service now.\n• CPS, Redwood Job scheduling.\n• Vertex.\nProgramming Knowledge\nC, C++, Core Java, J2EE, EJB, Struts\n\nContributions and Achievements\n• Conducted training for New joiners\n• Conducted team meetings and internal training sessions\n• Received \"Manager's Choice Award\" thrice for managing crisis situation\n• Received \"Deep Skill Adder Award\" every quarter for taking Personal interest\nPROJECT (Support): 1\nOrganization: IBM India PVT LTD\nRole: SAP Basis Consultant\nClient: Maersk Line- MLIT Basis\nDatabase: Oracle 11g\nPlatform: Linux 2.6\nPeriod: April 2015 to September 2016 and from Aug 01, 2017 to till date\nResponsibilities:\n• Creation of OSS ID and registering Developer for Access key and Object key in SMP.\n• Apply OSS Notes using SNOTE.\n• Experience on deployment of digital signature in Java Landscape.\n• Apply SPAM/SAINT, Support Packages, and Add-ons.\n• Performance Monitoring for ECC, BI, SRM, CRM, and PI systems.\n• Handling user tickets and system performance tickets.\n• Background Job Administration such as scheduling/Cancelling and Troubleshooting background\njobs as per client requirement '\n• Performed daily health checks across the landscape.\n• Analyzing and troubleshooting daily issues occurred in health checks.\n• Index rebuild for BW and ECC systems.\n\n\n\n• Run update stats for ECC and BW tables.\n• Printer configuration.\n• Detecting Expensive SQL statements\n• Monitoring and troubleshooting JAVA systems using NWA and admin tools like Visual Admin tool\nand Config tool.\n• Database cluster Administration.\n• System Refresh from Production to Quality and Pre Prod.\n• System Restore from Production to Quality, Pre Prod. Same system restore\n• System Upgrade from EHP 6 to EHP 7.\n• Kernel upgrade for the entire landscape.\n• System Build for System Upgrade.\n• DB Export and Import across the landscape.\nPROJECT (Implementation): 2\nOrganization: IBM India PVT LTD\nRole: SAP Basis Consultant\nClient: Maersk Line- MLIT Build Basis\nDatabase: Oracle 11g\nPlatform: Linux 2.6\nPeriod: September 2016 to July 2017\nResponsibilities:\n• Creation Database Source in Java systems.\n• Creation of Product, Software Component, Business Systems and Technical Systems in PO SLDs.\n• Performance capture whenever test load went into the system.\n• Troubleshooting JAVA and ABAP stack systems.\n• Creating source systems in BW systems, Maintaining and troubleshooting RFC connections for\nthe same.\n• Client opening and closing depending up on requirement.\n• Creating OSS messages on various issues to SAP in SMP.\n• SSO Configuration in Solution Manager.\nPROJECT (Support): 3\nOrganization: IBM India PVT LTD\nRole: SAP Basis Consultant\nClient: Ericsson\nDatabase: Oracle 11g\nPlatform: Linux 2.6\nSAP: ECC 6.0, BW ABAP 7.31\nPeriod: July 2013 to November 2014.\nResponsibilities:\nPerformed all SAP Basis Activities like Performance monitoring, Monthly report generation.\n• Printer configuration.\n• Detecting Expensive SQL statements\n• Monitoring and troubleshooting JAVA systems using NWA and admin tools like Visual Admin tool\nand Config tool.\n• Table space monitoring using BRTOOLS.\n• Database Administration using BRTOOLS.\n• Kernel upgrades for the entire landscape.\n• Part of EHP upgrade from ERP6.0 EHP 6 to ERP6.0 EHP 7\n\nPROJECT (Support): 4\n\n\n\nOrganization: IBM India PVT LTD\nRole: Associate SAP Basis Consultant L1\nClient: Unilever, UK\nDatabase: Oracle 10g\nPlatform: Linux 2.6 Linux V 6.2\nSAP: ECC 6.0, BW ABAP 7.31, CRM 7.31\nPeriod: May 2012 to June 2013.\nResponsibilities:\nPerformed all SAP Basis Activities like Production System Performance monitoring, Monthly report\ngeneration.\n• Performance Monitoring for ECC, BI, SRM, CRM, and PI systems.\n• Handling User tickets.\n• Background Job Administration such as scheduling/Cancelling and Troubleshooting background\njobs as per client requirement '\n• Performed daily health checks across the landscape.\n• Analyzing and troubleshooting daily issues occurred in health checks.\n• Importing Transports across the Landscape.\n• Creating/modifying User roles as per client requirement.\n\nPESONAL DETAILS:\nName: Vijayalakshmi Govindarajan", "meta": {}, "annotation_approver": null, "labels": [[0, 26, "Name"], [36, 46, "Designation"], [49, 67, "Companies worked at"], [69, 79, "Location"], [102, 159, "Email Address"], [243, 263, "Years of Experience"], [230, 239, "Companies worked at"], [218, 228, "Designation"], [771, 792, "Location"], [707, 710, "Degree"], [714, 769, "College Name"], [792, 797, "Degree"], [799, 842, "College Name"], [846, 863, "Location"], [931, 950, "Location"], [6310, 6336, "Name"], [209, 212, "Companies worked at"], [327, 339, "Job Specific Skills"], [341, 355, "Job Specific Skills"], [357, 371, "Job Specific Skills"], [523, 534, "Soft Skills"], [545, 558, "Soft Skills"], [560, 570, "Soft Skills"], [572, 587, "Soft Skills"], [618, 650, "Degree"], [673, 677, "Tech Tools"], [1040, 1044, "Tech Tools"], [1056, 1062, "Tech Tools"], [1074, 1077, "Tech Tools"], [1089, 1093, "Tech Tools"], [1114, 1120, "Tech Tools"], [1437, 1441, "Tech Tools"], [2309, 2320, "Job Specific Skills"], [2331, 2332, "Tech Tools"], [2334, 2337, "Tech Tools"], [2339, 2348, "Tech Tools"], [2350, 2354, "Tech Tools"], [3627, 3630, "Tech Tools"]]}
{"id": 42, "text": "Vikas Singh\nChandigarh, Chandigarh - Email me on Indeed: indeed.com/r/Vikas-Singh/8644db42854c4f6a\n\nI've 4.6 years of IT experience in Identity and Access Management with Infosys limited. It involves\ntroubleshooting and resolving workflow errors and provisioning user access. I perform identity\nand access management activities, updating existing access and provisioning workflows, manage\noperations within the IAM environment. Currently I'm working on automating the work flow of\nSecurity Intelligence and Response Team with Phantom and Python scripting.\nI'm trained in Python, Solaris administration, Java and PLSQL. I'm able to handle multiple tasks\nand work as a team Member. I've excellent analytical, problem solving and programming skills.\nI'm committed; goal oriented, & has zeal to learn new things & technologies.\nI am graduated in Electronics and Communication Engineering in 2013 with excellent grades. I\npossess good problem solving & interpersonal skills, good communication skills and team spirit.\n\nWORK EXPERIENCE\n\nTechnology Analyst\n\nInfosys Limited -  Chandigarh, Chandigarh -\n\nOctober 2013 to Present\n\nA. Change Management:\nInstalling and upgrading RT and RTIR for improved request handling with MySQL database.\n\nB. Python Automation:\nAutomating various use cases for the Security Intelligence and Response Team using Python\nscripting and Phantom tool integrating it with various tools i.e. Splunk, Request Tracker for\nIncident Response (RTIR), Remedy etc.\n\nBelow are the use cases details:\n\nUse Cases Description\nProxy Blocks Enable the ability to block domains and URLs automatically on Bluecoat proxies\nusing a list maintained in SPLUNK\nPalo Alto Blocks Enable the ability to block domains and URLs automatically on Palo Alto proxies\nusing a list maintained in SPLUNK\nThreat Intel Email feed ingestion Take emails from an external distribution group and parse the\nemails for IOCs\nEmail Eradication\nWhen a Malicious email has been detected as being received in the Microsoft Exchange email\nsystem perform eradication steps to remove the email from the email messaging platform\n\nEmail Quarantine Email addresses alerted as malicious need to be added to a quarantine list\nMalware Response When malware is detected by alert or scanning initiate containment\nprocedures for the affected device in question\nUnapproved Devices\n\nhttps://www.indeed.com/r/Vikas-Singh/8644db42854c4f6a?isid=rex-download&ikw=download-top&co=IN\n\n\nWhen alerts for Unapproved Devices, equipment that is not in Organization's asset inventory, is\ntriggered containment needs to occur for the device in question\n\nIOC Detect and Scanning using Tanium and Fireeye HX\nWhen Indicators of Compromise, IOC, are received from various sources, threat intel feeds,\nexploded malware the network environment needs to be scanned for any of the indicators of\ncompromise provided.\n\nClear Text Passwords detected\nAutomatically flag users password to reset in Active Directory when an alert in SPLUNK for a clear\ntext password detected fires\n\nCreate ticket from Splunk or MSSP Alert\nDevelop a script that takes the details of an alert from an alert generated in SPLUNK and create\nor append to a ticket in the ticketing system in use\n\nTriage and Identification Execute the triage and identification steps that are performed manually\ntoday\n\nInformation Security Analyst\n\nInfosys Limited -  Chandigarh, Chandigarh -\n\n2013 to Present\n\nA. Identity management\n\nInfosys Limited -\n\nMay 2014 to December 2017\n\nmanagement May 2014 - Dec 2017\n\nFollowing are my roles and responsibilities in the project:\nA. Identity management:\nTracking and processing identity creation for all the new hires along with basic access e.g.: Email,\nActive Directory accounts and including mandatory security related groups. Also, making sure\naccess is disabled on the user's departure date and cleaning up of all the access. Reviewing\naccess periodically and updating it accordingly.\n\nB. Access management:\nThis involves provisioning/de-provisioning access to users across 300+ applications using various\nglobal and in house tools like RSA security console, SAP, Identity IIQ etc. across multiple platforms\nlike, UNIX, Database and application front end. Making sure standard operative procedures (SOP)\nare followed, validation checks are completed and appropriate approvals are gathered before\naccess is granted.\n\nC. Quality Management:\nPerforming quality checks on random samples of requests on daily basis and sharing QAP results\nwith administrators.\n- Monitoring and tracking the corrective actions are taken within defined timeframe.\n\n\n\n- Doing RCA on major issues\n- Developing Service Improvement Plan (SIP) and Process Improvement Plan (PIP) based on the\nQAP analysis\n\nD. Risk Management:\nIdentifying risk areas through daily and periodic report E.g. Segregation of duties violation (SOD)\nreport, Active directory infraction report etc.\n- Working with various teams to mitigate the violations.\n- Assisting auditors by provide details and justification on audit samples.\n\nE. Client Coordination:\nCoordinating with client daily, weekly for the operations, issues and feedback with the respective\nreports prepared.\n\nEDUCATION\n\nBachelor of Technology in Electronics and Communication Engineering\n\nGLA Institute of Technology and Management -  Mathura, Uttar Pradesh\n\nSeptember 2009 to June 2013\n\nSKILLS\n\nSECURITY (5 years), INFORMATION SECURITY (5 years), ACTIVE DIRECTORY (3 years), UNIX\n(Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\n\n● Operating Systems: Windows, Solaris\n● Languages: Python, Core Java, SQL, Unix\n● Software: Sailpoint IIQ, Oracle IAM, Beeline, SAP, Active Directory, Phantom, Quest change\nauditor, Microsoft Office Suite\n● Information Security: Concepts and best practices", "meta": {}, "annotation_approver": null, "labels": [[0, 11, "Name"], [12, 34, "Location"], [57, 98, "Email Address"], [105, 114, "Years of Experience"], [887, 892, "Graduation Year"], [1031, 1049, "Designation"], [1051, 1066, "Companies worked at"], [1070, 1092, "Location"], [1096, 1119, "Years of Experience"], [3310, 3338, "Designation"], [3340, 3355, "Companies worked at"], [3359, 3381, "Location"], [3385, 3400, "Years of Experience"], [5240, 5282, "College Name"], [5171, 5238, "Degree"], [5286, 5308, "Location"], [5310, 5337, "Graduation Year"], [135, 165, "Job Specific Skills"], [526, 533, "Tech Tools"], [538, 544, "Tech Tools"], [842, 884, "Degree"], [930, 945, "Soft Skills"], [948, 961, "Soft Skills"], [975, 988, "Soft Skills"], [1000, 1011, "Soft Skills"], [571, 577, "Tech Tools"], [579, 586, "Tech Tools"], [603, 607, "Tech Tools"], [612, 617, "Tech Tools"], [695, 705, "Soft Skills"], [707, 722, "Soft Skills"], [1124, 1141, "Job Specific Skills"], [1215, 1220, "Tech Tools"], [1235, 1241, "Tech Tools"], [1242, 1252, "Job Specific Skills"], [3405, 3424, "Job Specific Skills"], [3426, 3441, "Companies worked at"], [3567, 3586, "Job Specific Skills"], [3928, 3945, "Job Specific Skills"], [4153, 4157, "Tech Tools"], [4358, 4376, "Job Specific Skills"], [4658, 4683, "Job Specific Skills"], [4612, 4642, "Job Specific Skills"], [4719, 4734, "Job Specific Skills"], [5021, 5040, "Job Specific Skills"], [5367, 5387, "Job Specific Skills"], [5427, 5431, "Tech Tools"], [5515, 5522, "Tech Tools"], [5524, 5531, "Tech Tools"], [5545, 5551, "Tech Tools"], [5553, 5562, "Tech Tools"], [5564, 5567, "Tech Tools"], [5569, 5573, "Tech Tools"], [5586, 5599, "Tech Tools"], [5601, 5611, "Tech Tools"], [5613, 5620, "Tech Tools"], [5622, 5625, "Tech Tools"], [5627, 5643, "Tech Tools"], [5399, 5415, "Tech Tools"], [5645, 5652, "Tech Tools"], [5654, 5674, "Tech Tools"], [5676, 5698, "Tech Tools"], [5701, 5721, "Job Specific Skills"]]}
{"id": 43, "text": "Yasothai Jayaramachandran\nLead Engineer - Automation & Testing\n\nChennai, TAMIL NADU, IN - Email me on Indeed: indeed.com/r/Yasothai-Jayaramachandran/\nc36e76b64d9f477f\n\n❖ 4 years of experience in testing methodologies: Automation, Regression, Sanity and Manual.\n❖ Hands-on experience in Selenium Webdriver with Python Automation.\n❖ Skilled in IDE's Pycharm, Eclipse[Pydev plugin], Selenium for testing the web browser using\nPython.\n❖ Expertise in developing automation scripts and testing various components.\n❖ Skilled in providing effective and quality automation process.\n❖ Hands-on experience in functional automation scripts using TCL Programming, Python.\n❖ Experienced in planning, designing, developing and deploying testing strategies.\n❖ Trained and worked closely along with developers and dev-test (manual) engineers for\nautomation tasks.\n❖ Good experience in reviewing the automated TCL, Python Scripts developed by the team\nmembers.\n❖ Extracting data from excel and csv files, posting results in excel file with Python.\n❖ Experience in Agile development -Played responsible role Scrum master for Automation.\n\nWORK EXPERIENCE\n\nLead Engineer - Automation & Testing\n\nCisco Systems -\n\nJanuary 2014 to June 2015\n\nDURATION Jan-2014 to Jun-2015\nTEAM SIZE 20\nROLES AND RESPONSIBILITIES\nLead Engineer - Automation & Testing\n• Identified the cases for giving the support to the WAAS product with v6 addressing from the\ntest plan\n• Played an active role - Scrum Master for the Automation.\n• Activity participated and organized the automation team to work on agile methodologies.\n• Prepared the HLD document for the automatable cases.\n• Automated around 1000+ cases in SNMP and Platform module.\n• Automated the basic sanity test cases and executing sanity testing for the v6 support.\n• Involved is regressing all the automated suites.\n• Performed regression for all the automated cases through ATS framework with aetest script\ntemplate.\n• Reviewed the automated cases developed by team members.\n• Regression Testing\n• Defect filing in CDETS/Rally\n• Worked on Agile development methodologies\n\nLANGUAGE TCL Programming, Python, Selenium\n\nhttps://www.indeed.com/r/Yasothai-Jayaramachandran/c36e76b64d9f477f?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Yasothai-Jayaramachandran/c36e76b64d9f477f?isid=rex-download&ikw=download-top&co=IN\n\n\nRELEASES Hornet\nDESCRIPTION\n• IPv6, formerly named IPng (next generation), is the latest version of the Internet Protocol (IP). IP\nis a packet-based protocol used to exchange data, voice, and video traffic over digital networks\n• Cisco's Wide-Area Application Services IPv4 users can move to IPv6 and receive services such\nas end-to-end security, quality of service (QoS), and globally unique addresses. The IPv6 address\nspace reduces the need for private addresses and Network Address Translation (NAT) processing\nby border routers at network edges.\n• Implementing basic IPv6 connectivity in the Cisco IOS software consists of assigning IPv6\naddresses to individual router interfaces. The forwarding of IPv6 traffic can be enabled globally,\nand Cisco Express Forwarding switching for IPv6 can also be enabled\n• The primary motivation for IPv6 is the need to meet the demand for globally unique IP\naddresses.\n• Mainly involves to checks the functionality of all waas features to work with v6 addressing\n\nPRODUCT IPv6 Support on WAAS (Wide Area Application Services)\n\nPROJECT CISCO-WAAS Express (WEXP)\n\nMember Technical Staff - Automation & Testing\n\nCisco Systems -\n\nJanuary 2013 to December 2013\n\nDURATION Jan-2013 to December-2013\nTEAM SIZE 21\nROLES AND RESPONSIBILITIES\nMember Technical Staff - Automation & Testing\n• Identified the automatable cases from the test plan.\n• Prepared the HLD document for the automatable cases.\n• Automated test cases for various components on the wexp product for the functionality check.\n• Worked on Config_Sync, HTTP, HTTPS, SSL, SMB_AO component automation.\n• Automated around 200+ cases for various components.\n• Performed regression for all the automated cases through ATS framework with aetest script\ntemplate.\n• Reviewed the automated cases developed by team members.\n• Involved in executing all the automated cases for various releases as Phase1, Phase2 (pi19-25)\nand posted results in TIMS tool.\n• Regression Testing\n• Defect filing in CDETS\n\nLANGUAGE TCL Programming, Python\nRELEASES Phase1 &Phase2 (with 535)\nDESCRIPTION\n• Cisco's Wide-Area Application Services (WAAS) Express feature is a key component of the Cisco\nWAAS product portfolio. WAAS Express is a cost-effective, IOS-based, WAN optimization solution\nthat increases the amount of available bandwidth for small-to-mid-size branch offices and remote\nlocations, while accelerating TCP-based applications that operate in a WAN environment.\n\n\n\n• The Wide-Area Application Services (WAAS) Express software optimizes TCP traffic flows across\na WAN. WAAS Express integrates with native services such as security, NetFlow, and quality of\nservice (QoS). WAAS Express provides the following benefits:\n❖ Bandwidth optimization\n❖ Application acceleration\n❖ Increase in remote user productivity\n❖ Interoperation with existing Cisco WAAS infrastructure\n❖ Network transparency\n❖ Deployment flexibility with on-demand service enablement\n• The Wexp is about the wide Area Application Service on IOS (Router) itself.\n• Mainly involves to checks the functionality of optimizing the various Ao's like HTTP, SSL, HTTPS,\nSMB and Config_sync.\n\nPRODUCT WAAS Express(Wide Area Application Services)\n\nPROJECT CISCO-WAAS\nCLIENT Cisco Systems\nORGANISATION HCL Technologies Ltd\n\nSoftware Engineer - Automation & Testing\n\n-\n\nJune 2011 to December 2012\n\n• Identified the automatable cases from the test plan.\n• Prepared the HLD document for the automatable cases.\n• Automated test cases for various components on the waas product for the functionality check.\n• Worked on Plaform, SNMP, SSL, HTTP component automation.\n• Automated around 600+ cases for various components.\n• Performed regression for all the automated cases through ATS framework with aetest script\ntemplate.\n• Active Participation in automating test cases for sanity test.\n• Reviewed the automated cases developed by team members.\n• Involved in executing all the automated cases and reporting bugs/defects.\n• Worked on various releases of the products (Lancer, Phoenix, Skyhawk) for regressing the\nautomated cases and posted results in TIMS tool.\n• Defect filing in CDETS\n\nLANGUAGE TCL Programming, Python\nRELEASES LANCER, PHOENIX, SKYHAWK, SPRIT\nDESCRIPTION\n• Wide Area Application Services (WAAS) is a Cisco Systems technology that improves the\nperformance of applications on a wide area network (WAN). WAAS combines Cisco's Wide Area\nFile Services (WAFS-products allow remote office users to access and share files globally at\nLAN speeds over the WAN) with WAN optimization inside of a router, accelerating TCP-based\napplications on a given network.\n• WAAS system consists of a set of devices called wide area application engines (WAEs) that work\ntogether to optimize TCP traffic over your network. When client and server applications attempt\n\n\n\nto communicate with each other, the network intercepts and redirects this traffic to the WAEs so\nthat they can act on behalf of the client application and the destination server.\n• The WAEs examine the traffic and use built-in application policies to determine whether to\noptimize the traffic or allow it to pass through your network un optimized\n• Mainly involves optimizing the various Ao's like HTTP, SSL, HTTPS and functional wise platform\ncheck.\n\nPRODUCT WAAS(Wide Area Application Services)\n\nEDUCATION\n\nB.E in CSE\n\nANNA University\n\n2011\n\nDay Adventist Matric Hr.Sec School\n\n2005 to 2007\n\nSKILLS\n\noptimization (2 years), Python (4 years), router (4 years), TCL (4 years), Testing (4 years)\n\nADDITIONAL INFORMATION\n\nTechnical Skills\nProgramming Languages Python, TCL, Power Query, SQL, Shell Scripting, C#\nConcepts Networking, WAAS (Wan Optimization), WAAS on Router, WAE(Wide Area Engine),\nWCCP Protocol,\nAutomation Framework ATS (Automated Test System), Selenium Testing Framework\nIDE(Integrated Development Environment) Eclipse, Pycharm, Pydev[Plugin for eclipse IDE]\nSoftware\nEclipse CDETS & Rally (Bug Tracking), eARMS (Regression testing), ACME(Versioning),\nTIMS(Reporting), HTMLTestRunner, Power Query(Automation), ARAS PLM(Product Lifecycle\nManagement), AML Studio (adaptive markup language)\n\nOperating Systems Windows XP/10, Linux", "meta": {}, "annotation_approver": null, "labels": [[0, 25, "Name"], [26, 39, "Designation"], [42, 71, "Companies worked at"], [73, 87, "Location"], [110, 166, "Email Address"], [1136, 1149, "Designation"], [1152, 1187, "Companies worked at"], [1191, 1216, "Years of Experience"], [1227, 1247, "Years of Experience"], [3456, 3478, "Designation"], [3520, 3549, "Years of Experience"], [5609, 5626, "Designation"], [5654, 5680, "Years of Experience"], [7652, 7655, "Degree"], [7659, 7679, "College Name"], [7679, 7685, "Graduation Year"], [3560, 3586, "Years of Experience"], [170, 191, "Years of Experience"], [195, 216, "Job Specific Skills"], [218, 228, "Job Specific Skills"], [230, 240, "Job Specific Skills"], [286, 304, "Tech Tools"], [310, 316, "Tech Tools"], [348, 355, "Tech Tools"], [357, 364, "Tech Tools"], [380, 388, "Tech Tools"], [393, 416, "Job Specific Skills"], [423, 429, "Tech Tools"], [457, 475, "Job Specific Skills"], [634, 649, "Job Specific Skills"], [651, 657, "Tech Tools"], [676, 740, "Job Specific Skills"], [2680, 2699, "Job Specific Skills"], [2701, 2719, "Job Specific Skills"], [3481, 3501, "Job Specific Skills"], [3503, 3516, "Companies worked at"], [5629, 5649, "Job Specific Skills"], [6476, 6491, "Job Specific Skills"], [6555, 6585, "Job Specific Skills"], [7745, 7757, "Job Specific Skills"], [7769, 7775, "Tech Tools"], [7787, 7793, "Job Specific Skills"], [7805, 7808, "Tech Tools"], [7820, 7827, "Job Specific Skills"], [7902, 7908, "Tech Tools"], [7910, 7913, "Tech Tools"], [7915, 7926, "Tech Tools"], [7928, 7931, "Tech Tools"], [7933, 7948, "Tech Tools"], [7950, 7952, "Tech Tools"], [7974, 7997, "Job Specific Skills"], [7962, 7972, "Job Specific Skills"], [7999, 8013, "Job Specific Skills"], [8015, 8036, "Job Specific Skills"], [8038, 8052, "Job Specific Skills"], [8053, 8073, "Job Specific Skills"], [8074, 8101, "Tech Tools"], [8103, 8129, "Tech Tools"], [8170, 8177, "Tech Tools"], [8179, 8186, "Tech Tools"], [8188, 8193, "Tech Tools"], [8227, 8234, "Tech Tools"], [8235, 8240, "Tech Tools"], [8243, 8248, "Tech Tools"], [8250, 8262, "Job Specific Skills"], [8265, 8270, "Tech Tools"], [8272, 8290, "Job Specific Skills"], [8293, 8297, "Tech Tools"], [8311, 8315, "Tech Tools"], [8328, 8342, "Tech Tools"], [8344, 8355, "Tech Tools"], [8369, 8377, "Tech Tools"], [8378, 8406, "Job Specific Skills"], [8409, 8419, "Tech Tools"], [8421, 8445, "Job Specific Skills"], [8466, 8479, "Tech Tools"], [8481, 8486, "Tech Tools"]]}
{"id": 44, "text": "Yathishwaran P\nMaximo Consultant - Infosys Limited\n\nNamakkal, Tamil Nadu - Email me on Indeed: indeed.com/r/Yathishwaran-P/a9c8d42210af40b8\n\n• Maximo Consultant in Infosys Limited, Chennai from August 2013 till date (around 4.8 years)\n• IT professional with 4.4 years of experience in BIRT reporting, application support in IBM MAXIMO\nApplication.\n• IBM Certified MAXIMO ASSET MANAGEMENT, PURCHASE ORDER, PROCUREMENT, PURCHASE\nREQUISITION Professional\n• Submitted Internal tutorial document on Configuration of Hover Dialogs and Its usage in Maximo\nV7.6\n• Worked on various customer requirements and having overall experience with\n➢ Preventive Maintenance\n➢ Maximo automation and implementation\n➢ MBO customizations\n➢ Data Supply chain\n➢ Portals, Workflows Designing\n➢ Reports/Query Creation\n➢ Work order tracking, Invoicing, Purchase order\n➢ Asset Management\n➢ Maximo Integration Framework (MIF)\n➢ Labors, Crews, Security Group Set up\n➢ Domains, Database configuration\n➢ Basics of BIRT, Cognos Reporting, Monitor logs from Admin console.\n➢ Maximo Java customization\n➢ Maximo Installation/Upgradation\n➢ Basic WebSphere function\n\n• Strong interpersonal skills and the ability to work in team efficiently. Customer Service support\nexperience in a helpdesk environment.\n• Strong dedication towards work to ensure delivery of projects according to the schedules.\nProactively approached for delivering excellent customer service and liaising with stakeholders.\n• Ambitious, enthusiastic and highly motivated person with excellent problem solving skills and\nalso having ability to quickly master new technologies and skills.\n• Maintains effective work behavior in the face of setbacks or pressure. Strong multi-tasking\ncapability, target and deadline oriented.\n• Engaging and resilient communicator demonstrating influencing skills and an ability to adapt\napproaches to differing situations\n\nWilling to relocate to: Chennai, Tamil Nadu - Mysore, Karnataka - Bengaluru, Karnataka\n\nWORK EXPERIENCE\n\nMaximo Consultant\n\nInfosys Limited -  Chennai, Tamil Nadu -\n\nAugust 2013 to Present\n\nhttps://www.indeed.com/r/Yathishwaran-P/a9c8d42210af40b8?isid=rex-download&ikw=download-top&co=IN\n\n\naround 4.8 years)\n• IT professional with 4.4 years of experience in BIRT reporting, application support in IBM MAXIMO\nApplication.\n• IBM Certified MAXIMO ASSET MANAGEMENT, PURCHASE ORDER, PROCUREMENT, PURCHASE\nREQUISITION Professional\n• Submitted Internal tutorial document on Configuration of Hover Dialogs and Its usage in Maximo\nV7.6\n• Worked on various customer requirements and having overall experience with\n➢ Preventive Maintenance\n➢ Maximo automation and implementation\n➢ MBO customizations\n➢ Data Supply chain\n➢ Portals, Workflows Designing\n➢ Reports/Query Creation\n➢ Work order tracking, Invoicing, Purchase order\n➢ Asset Management\n➢ Maximo Integration Framework (MIF)\n➢ Labors, Crews, Security Group Set up\n➢ Domains, Database configuration\n➢ Basics of BIRT, Cognos Reporting, Monitor logs from Admin console.\n➢ Maximo Java customization\n➢ Maximo Installation/Upgradation\n➢ Basic WebSphere function\n\n• Strong interpersonal skills and the ability to work in team efficiently. Customer Service support\nexperience in a helpdesk environment.\n• Strong dedication towards work to ensure delivery of projects according to the schedules.\nProactively approached for delivering excellent customer service and liaising with stakeholders.\n• Ambitious, enthusiastic and highly motivated person with excellent problem solving skills and\nalso having ability to quickly master new technologies and skills.\n• Maintains effective work behavior in the face of setbacks or pressure. Strong multi-tasking\ncapability, target and deadline oriented.\n• Engaging and resilient communicator demonstrating influencing skills and an ability to adapt\napproaches to differing situations\n\nRole: Senior Systems Engineer\n• Working as Maximo Consultant for Bombardier Transportation, Canada\n\n• Strong knowledge in Maximo configuration using Automation Scripting, Database configuration,\nApplication Designer, Domains, Workflows and other major applications\n• Providing efficient solution for the issues related to Work orders, Asset configuration, Item\nMaster, Preventive Maintenance, Inventory, Domain related issue\n• Thorough implementation, support knowledge of Bombardier Transportation Maximo version\n4, 6, 7.1 and 7.6.\n• Having experience in Maximo Implementation, L2 and L3 support, and deployment activities\n• Having experience in Incident Management, Problem management, Change management,\nService Request, and other applications.\n• Delivering 100% effectiveness in resolving ticket by ensuring compliance with client Service\nLevel Agreements (SLAs)\n\n\n\n• Developing Conditional Queries/Reports to verify the desired results.\n• Worked on various formal and informal production reports.\n• Created the template for maintaining the application maintenance activities.\n• Worked in many configuration management applications for asset management related\nactivities for future implementation.\n• Working in 24*7 support. Resolved many critical issues which appreciated by client\n\nBIRT Reports\nBTRAMR0001SYSER.rptdesign (Work order report):\n• My client was using default BTRAMR2 Work order reports. They also use a different work order\nreport in Maximo V4 and V6. The earlier used report have many drawbacks such as\n\nNo Flexibility for modification since it is used by many divisions\nEmpty boxes, lot of missing information\nNot align with Systems objectives and processes\nHistorical context on the asset is missing\nEsthetical issues: Font too small, input fields not instinctive\nNo bar coding used\n• The outcome of this report is a form to support technician's work and regulation and give\nthem simplicity to input information. It covers the full spectra of requirements of System Services\ndivisions.\n• This report is site specific one and it will display the WO details along with its task WOs and\nfailure reporting information.\n• Each site is having their own logo. This current report will display the logo based on site.\n• It was implemented in V7.1 only. In V7.1 we do not have the option of auto resetting page\nnumbers (i.e., while displaying multiple WOs, for each new WO page number will not be reset\nautomatically) . I have implemented this option in this report.\n• Language Translation was used for this report in German and French\n• I did not use IBM templates for this report. I have customized our own template in this report.\n\nBTRAMR0009.rptdesign (Availability Report):\n\n• This particular report relates to the requirement of the business to be able to create a\nAvailability Graph based on selected Train Assets, a timeframe and selected work types / work\norder parameters.\n• Availability can be calculated based on a Fleet or a single Train. The business requirement is to\nbe able to select this base as needed to suit the project or view needed at that time.\n\nBTRAMR0027.rptdesign (Card Stock Report):\nExisting report functionality:\n• Technician requiring a part will take the component in its bin along with an Inventory Card. The\ntechnician reports the consumption at the exit of the storeroom on the Kiosk computer where\nhe scans its Work Order and the Inventory Card in the application Issue and Transfer of Maximo.\nWhen we rebuild something internally, the technician will bring the new assembly in store, grab\nthe Inventory Card and he will report the return in store against the rebuilt Work order.\nObjectives:\n• Created a card stock report in order to match the existing Inventory Card tool developed by\nSystems.\n• Increase reporting of consumption\n\n\n\n• Redefine the process of cycle count (80% efficiency in Systems)\n• The report will be based on inventory information and will introduce the notion of real time to\nincrease precision\n\nBTRAMR0001SBB.rptdesign (Report Change):\n• The report BTRAMR0001SBB was developed for the European project. It is based on the\nBTRAMR0001MLM report and currently uses only launch option 2 and 3 of the BTRAMR0001MLM\nreport. The client requested to have option 1 (type=1) of the BTRAMR0001MLM implemented in\nthe BTRAMR0001SBB report as well as the translation of the labels from French to English.\nObjectives:\n• To have define the type=1, report definition for BTRAMR0001SBB\n• To have the labels translated from French to English\n\nBTRAMR0002.rptdesign (Systems Purchase Requisition Report):\n\n• The current PR report does not have bar-coding feature and is formatted too small. This is an\noperational report; it will be used in real time.\n• Have a common and standardized PR report across all Systems services sites with the support\nof bar-coding\n\nIncident Management Solution report:\n• The report displays the overall ticket progress. It shows count of tickets raised and assigned to\nboth Infosys and Client queue. We can view the total no. of tickets raised for various application\ngroups such as Maximo, Kronos and BIEM. It also further categories the ticket count based on\nticket priority and the status. This helps in analyzing the data from ticketing tool.\n• There might be cases where different teams work on the tickets raised. This reports helps to\nanalyses the team wise ticket count. We can there by monitor the performance of various teams.\n• This report displays the number of Incidents and Service Requests logged in each month. Also\nit shows total tickets (Incidents + Service requests) logged in each month.\n• This Report shows the number of tickets logged per country and it has been grouped month\nwise for various application groups such as Maximo, Kronos and BIEM within a date range.\n\nVend_Reported_hrs. rptdesign (Vendor Reported Hours):\n• This report will fetch the details of vendor type labor working hours for each WOs.\n\nRole: Quality Anchor\n• Working as Quality Anchor for my team, which involves creating defect prevention analysis,\ntracking all the works and auditing for quality\n• Providing Statistics about the monthly activities done by the team\n• From defect prevent analysis focus on specific topic and conducting brainstorming session with\nteam to avoid the defects\n• Responsible for gathering the requirements from Client and participate in Client Conferences.\n• Divide the requirements into various sprints based on the velocity.\n• Distribute the requirements to the team and monitor the development progress and provide\nclarification on the technical/functional requirements.\n• Mentor for the new members joining the team.\n\nOther Job Related Activities:\n\n\n\n• Successfully Completed Training in Design Thinking course and Kronos tool which keeps tracks\non Work force management.\n\nMaximo consultant\n\nInfosys Limited\n\naround 4.8 years)\n• IT professional with 4.4 years of experience in BIRT reporting, application support in IBM MAXIMO\nApplication.\n• IBM Certified MAXIMO ASSET MANAGEMENT, PURCHASE ORDER, PROCUREMENT, PURCHASE\nREQUISITION Professional\n• Submitted Internal tutorial document on Configuration of Hover Dialogs and Its usage in Maximo\nV7.6\n• Worked on various customer requirements and having overall experience with\n➢ Preventive Maintenance\n➢ Maximo automation and implementation\n➢ MBO customizations\n➢ Data Supply chain\n➢ Portals, Workflows Designing\n➢ Reports/Query Creation\n➢ Work order tracking, Invoicing, Purchase order\n➢ Asset Management\n➢ Maximo Integration Framework (MIF)\n➢ Labors, Crews, Security Group Set up\n➢ Domains, Database configuration\n➢ Basics of BIRT, Cognos Reporting, Monitor logs from Admin console.\n➢ Maximo Java customization\n➢ Maximo Installation/Upgradation\n➢ Basic WebSphere function\n\n• Strong interpersonal skills and the ability to work in team efficiently. Customer Service support\nexperience in a helpdesk environment.\n• Strong dedication towards work to ensure delivery of projects according to the schedules.\nProactively approached for delivering excellent customer service and liaising with stakeholders.\n• Ambitious, enthusiastic and highly motivated person with excellent problem solving skills and\nalso having ability to quickly master new technologies and skills.\n• Maintains effective work behavior in the face of setbacks or pressure. Strong multi-tasking\ncapability, target and deadline oriented.\n• Engaging and resilient communicator demonstrating influencing skills and an ability to adapt\napproaches to differing situations\n\nRole: Senior Systems Engineer\n• Working as Maximo Consultant for Bombardier Transportation, Canada\n\n• Strong knowledge in Maximo configuration using Automation Scripting, Database configuration,\nApplication Designer, Domains, Workflows and other major applications\n• Providing efficient solution for the issues related to Work orders, Asset configuration, Item\nMaster, Preventive Maintenance, Inventory, Domain related issue\n\n\n\n• Thorough implementation, support knowledge of Bombardier Transportation Maximo version\n4, 6, 7.1 and 7.6.\n• Having experience in Maximo Implementation, L2 and L3 support, and deployment activities\n• Having experience in Incident Management, Problem management, Change management,\nService Request, and other applications.\n• Delivering 100% effectiveness in resolving ticket by ensuring compliance with client Service\nLevel Agreements (SLAs)\n• Developing Conditional Queries/Reports to verify the desired results.\n• Worked on various formal and informal production reports.\n• Created the template for maintaining the application maintenance activities.\n• Worked in many configuration management applications for asset management related\nactivities for future implementation.\n• Working in 24*7 support. Resolved many critical issues which appreciated by client\n\nBIRT Reports\nBTRAMR0001SYSER.rptdesign (Work order report):\n• My client was using default BTRAMR2 Work order reports. They also use a different work order\nreport in Maximo V4 and V6. The earlier used report have many drawbacks such as\n\nNo Flexibility for modification since it is used by many divisions\nEmpty boxes, lot of missing information\nNot align with Systems objectives and processes\nHistorical context on the asset is missing\nEsthetical issues: Font too small, input fields not instinctive\nNo bar coding used\n• The outcome of this report is a form to support technician's work and regulation and give\nthem simplicity to input information. It covers the full spectra of requirements of System Services\ndivisions.\n• This report is site specific one and it will display the WO details along with its task WOs and\nfailure reporting information.\n• Each site is having their own logo. This current report will display the logo based on site.\n• It was implemented in V7.1 only. In V7.1 we do not have the option of auto resetting page\nnumbers (i.e., while displaying multiple WOs, for each new WO page number will not be reset\nautomatically) . I have implemented this option in this report.\n• Language Translation was used for this report in German and French\n• I did not use IBM templates for this report. I have customized our own template in this report.\n\nBTRAMR0009.rptdesign (Availability Report):\n\n• This particular report relates to the requirement of the business to be able to create a\nAvailability Graph based on selected Train Assets, a timeframe and selected work types / work\norder parameters.\n• Availability can be calculated based on a Fleet or a single Train. The business requirement is to\nbe able to select this base as needed to suit the project or view needed at that time.\n\nBTRAMR0027.rptdesign (Card Stock Report):\nExisting report functionality:\n• Technician requiring a part will take the component in its bin along with an Inventory Card. The\ntechnician reports the consumption at the exit of the storeroom on the Kiosk computer where\n\n\n\nhe scans its Work Order and the Inventory Card in the application Issue and Transfer of Maximo.\nWhen we rebuild something internally, the technician will bring the new assembly in store, grab\nthe Inventory Card and he will report the return in store against the rebuilt Work order.\nObjectives:\n• Created a card stock report in order to match the existing Inventory Card tool developed by\nSystems.\n• Increase reporting of consumption\n• Redefine the process of cycle count (80% efficiency in Systems)\n• The report will be based on inventory information and will introduce the notion of real time to\nincrease precision\n\nBTRAMR0001SBB.rptdesign (Report Change):\n• The report BTRAMR0001SBB was developed for the European project. It is based on the\nBTRAMR0001MLM report and currently uses only launch option 2 and 3 of the BTRAMR0001MLM\nreport. The client requested to have option 1 (type=1) of the BTRAMR0001MLM implemented in\nthe BTRAMR0001SBB report as well as the translation of the labels from French to English.\nObjectives:\n• To have define the type=1, report definition for BTRAMR0001SBB\n• To have the labels translated from French to English\n\nBTRAMR0002.rptdesign (Systems Purchase Requisition Report):\n\n• The current PR report does not have bar-coding feature and is formatted too small. This is an\noperational report; it will be used in real time.\n• Have a common and standardized PR report across all Systems services sites with the support\nof bar-coding\n\nIncident Management Solution report:\n• The report displays the overall ticket progress. It shows count of tickets raised and assigned to\nboth Infosys and Client queue. We can view the total no. of tickets raised for various application\ngroups such as Maximo, Kronos and BIEM. It also further categories the ticket count based on\nticket priority and the status. This helps in analyzing the data from ticketing tool.\n• There might be cases where different teams work on the tickets raised. This reports helps to\nanalyses the team wise ticket count. We can there by monitor the performance of various teams.\n• This report displays the number of Incidents and Service Requests logged in each month. Also\nit shows total tickets (Incidents + Service requests) logged in each month.\n• This Report shows the number of tickets logged per country and it has been grouped month\nwise for various application groups such as Maximo, Kronos and BIEM within a date range.\n\nVend_Reported_hrs. rptdesign (Vendor Reported Hours):\n• This report will fetch the details of vendor type labor working hours for each WOs.\n\nRole: Quality Anchor\n• Working as Quality Anchor for my team, which involves creating defect prevention analysis,\ntracking all the works and auditing for quality\n• Providing Statistics about the monthly activities done by the team\n• From defect prevent analysis focus on specific topic and conducting brainstorming session with\nteam to avoid the defects\n• Responsible for gathering the requirements from Client and participate in Client Conferences.\n\n\n\n• Divide the requirements into various sprints based on the velocity.\n• Distribute the requirements to the team and monitor the development progress and provide\nclarification on the technical/functional requirements.\n• Mentor for the new members joining the team.\n\nOther Job Related Activities:\n• Successfully Completed Training in Design Thinking course and Kronos tool which keeps tracks\non Work force management.\n\nEDUCATION\n\nBachelor of Engineering in Electronics and Communication in Electronics\nand Communication\n\nNandha Engineering College -  Erode, Tamil Nadu\n\n2012\n\nSecondary Education\n\nGovernment Boys Higher Secondary School -  Pallipalayam, Namakkal, IN\n\n2008\n\nSKILLS\n\nCODA (4 years), Cognos (4 years), Database (4 years), Eclipse (Less than 1 year), IBM COGNOS\n(4 years)\n\nADDITIONAL INFORMATION\n\nTechnical Skills:\nReporting Tools BIRT, Cognos (Basics)\nDatabase Oracle\n\nOperating Systems Windows\nLanguages SQL, JavaScripting\nWeb Technologies Tools HTML SQL Developer, Eclipse", "meta": {}, "annotation_approver": null, "labels": [[35, 51, "Companies worked at"], [52, 72, "Location"], [95, 139, "Email Address"], [0, 14, "Name"], [15, 32, "Designation"], [143, 160, "Designation"], [164, 179, "Companies worked at"], [181, 188, "Location"], [189, 234, "Years of Experience"], [1910, 1929, "Location"], [1932, 1949, "Location"], [1952, 1972, "Location"], [1991, 2008, "Designation"], [2010, 2025, "Companies worked at"], [2029, 2048, "Location"], [2052, 2074, "Years of Experience"], [3937, 3944, "Location"], [19125, 19181, "Degree"], [19185, 19242, "College Name"], [19246, 19263, "Location"], [19265, 19269, "Graduation Year"], [19335, 19361, "Location"], [19363, 19368, "Graduation Year"]]}
{"id": 45, "text": "Yogi Pesaru\nDeveloper - Infosys Limited\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Yogi-Pesaru/2ed7aded59ecf425\n\n• Total IT experience of 4.6 years in IT Industry.\n• Currently working as SAP PI/XI and Dell Boomi developer.\n• Good communication, interpersonal and Client interaction skills with clear understanding of\nthe requirements.\n• Trained on SAP ABAP, SAP BASIS, SAP HANA (HCI), DELL Boomi.\n• Worked on PI 7.1, 7.31, Dell Boomi Atomsphere.\n• Good knowledge of Core Java and good command in writing UDFs for Graphical Mapping.\n• Created Technical systems, Business systems and Software products and components in SLD.\n• Worked on SLD configurations, IR and ID.\n• Good capabilities of trouble shooting and resolving different kinds of issues like connectivity\nproblems, mapping exceptions etc.\n• Experience in working with FILE, SOAP, SOAP (AXIS), RFC, IDOC and XI, JMS, JDBC, ARIBA, REST\nin PI and File, SOAP, Successfactor, Salesforce connectors in Boomi.\n• Implemented standard features like UDMS, message prioritization, iChannel admin.\n• Worked on Transports of IR, ID and SLD objects using file and CTS+ transports.\n• Good knowledge of Graphical Mapping with node functions and UDFs.\n• Knowledge of XSLT and Java mapping and Dynamic Configuration.\n• Production support experience in Runtime workbench, communication channel scheduling,\nMessage Monitoring.\n• Good knowledge on SLD configuration, IDoc configuration in ECC and PI and alert configuration.\n• Experience in handling quarterly and emergency releases and providing post release support.\n\nWORK EXPERIENCE\n\nDeveloper\n\nInfosys Limited -\n\nOctober 2015 to Present\n\nInfosys Limited Senior Systems Engineer 01st Oct 2015 - till date\n\nPROJECT SUMMARY - BOOMI\nProject Name Success factor integration with Sterling\nClient SYSCO\nEmployer Infosys Limited\nRole Developer\nTeam Size 3 members\nTechnologies Java script, groovy script, SuccessFactor, Sterling, Webservice\nDuration 2 years\n\nProject Abstract:\n\nhttps://www.indeed.com/r/Yogi-Pesaru/2ed7aded59ecf425?isid=rex-download&ikw=download-top&co=IN\n\n\nThe integration for Sterling is a two-way integration to support the investigation and confirm\naccuracy of background information provided by selected candidates who have received have\npassed phone screening & or accepted verbal offers of employment.\n\nBoomi is the middle ware between success factor and sterling systems.\nBoomi sender interface picks up the profiles from SF and send to sterling for background\nverification. After receiving request from boomi sterling will send synchronous response with an\nOder number, which will be updated to SF synchronously. After some time boomi receiver process\n(listener process) receives the asynchronous results from sterling which are updated back to SF.\n\nRole and responsibilities:\n\n• Requirement analysis.\n• Design as per the requirement.\n• Process development as per design.\n• Unit testing the applications.\n\nPROJECT SUMMARY - PI\nProject Sysco MS Development\n\nSystems Engineer\n\nInfosys Limited -\n\nSeptember 2013 to September 2015\n\nEDUCATION\n\nB. Tech in ECE\n\nVidya Bharati Institute Of Technology -  Hindupur, Andhra Pradesh\n\n2008 to 2012\n\nHosanna National High School\n\n2006\n\nSKILLS\n\nC+ (Less than 1 year), Citrix (Less than 1 year), integration (2 years), INTEGRATOR (2 years),\nJava (2 years)\n\nADDITIONAL INFORMATION\n\nTECHNICAL PROFICIENCY\n\nIntegration Tools SAP PI (7.1, 7.31), Eclipse, Citrix, SQL Server Data, SOAP UI, NWDS, Solution\nManager, HPQC, POSTMAN\nBackend Technologies Oracle and MS SQL.\nLanguages Core Java, C++, C.\n\n\n\nKEY STRENGTHS\n• Initiative, Leadership Qualities and Team spirit.\n• Proficiency in Communication skills, Positive attitude.\n• Good knowledge about Technology and interest towards new learning.\n• Responsibility and patience to do work assigned by superiors.", "meta": {}, "annotation_approver": null, "labels": [[0, 11, "Name"], [12, 22, "Designation"], [24, 39, "Companies worked at"], [41, 61, "Location"], [84, 125, "Email Address"], [201, 235, "Designation"], [1589, 1598, "Designation"], [1600, 1615, "Companies worked at"], [1619, 1642, "Years of Experience"], [1644, 1659, "Companies worked at"], [1660, 1683, "Designation"], [1684, 1709, "Years of Experience"], [3063, 3077, "Degree"], [3079, 3116, "College Name"], [3120, 3144, "Location"], [3146, 3158, "Graduation Year"], [152, 161, "Years of Experience"], [244, 257, "Soft Skills"], [259, 272, "Soft Skills"], [277, 295, "Soft Skills"], [362, 370, "Tech Tools"], [372, 381, "Tech Tools"], [399, 408, "Tech Tools"], [480, 489, "Tech Tools"], [518, 522, "Tech Tools"], [527, 544, "Job Specific Skills"], [944, 954, "Tech Tools"], [923, 927, "Tech Tools"], [1160, 1177, "Job Specific Skills"], [1223, 1227, "Tech Tools"], [1232, 1236, "Tech Tools"], [2804, 2824, "Job Specific Skills"], [2861, 2880, "Job Specific Skills"], [2898, 2910, "Job Specific Skills"], [3202, 3206, "Tech Tools"], [3227, 3233, "Tech Tools"], [3254, 3265, "Job Specific Skills"], [3277, 3287, "Tech Tools"], [3299, 3303, "Tech Tools"], [3380, 3386, "Tech Tools"], [3400, 3407, "Tech Tools"], [3409, 3415, "Tech Tools"], [3417, 3432, "Tech Tools"], [3434, 3441, "Tech Tools"], [3443, 3447, "Tech Tools"], [3473, 3480, "Tech Tools"], [3467, 3471, "Tech Tools"], [3449, 3465, "Tech Tools"], [3513, 3519, "Tech Tools"], [3502, 3508, "Tech Tools"], [3531, 3540, "Tech Tools"], [3542, 3545, "Tech Tools"], [3547, 3548, "Tech Tools"], [3569, 3579, "Soft Skills"], [3581, 3591, "Soft Skills"], [3606, 3617, "Soft Skills"], [3636, 3649, "Soft Skills"], [3658, 3675, "Soft Skills"]]}
{"id": 46, "text": "Anurag Asthana\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Anurag-Asthana/ea7451b2bdb6115a\n\n• Looking forward for joining a company where my creative thinking and excellent skills in\napplication development using modern development tools will be utilized.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nApril 2017 to Present\n\nLocation: Pune, India\nClient: Insurance Client from London\nDuration: April 2017 -Till Date\nTechnology/Tools: Microsoft SharePoint 2013, Azure Cognitive Services, Azure Service Bus, Azure\nBlob Storage, .net framework 4.7\n\nResponsibilities:\n\n• Independently worked on designing solution architecture of the project.\n• Lead team from technical front in all the components built\n• Followed Agile development approach to efficiently manage continuous development process\nand incremental requirement changes\n• Design SharePoint components using SharePoint CSOM and .net framework 4.7\n• Design Azure components using azure .net sdks and .net framework 4.7\n• Implement Micro Services architecture using Azure Service Bus\n\nProject Title: SharePoint 2013 Application Development and Enhancement\n\nInfosys LTD -  London -\n\nFebruary 2017 to March 2018\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nAugust 2015 to January 2017\n\nSharePoint Designer\n\nMicrosoft SharePoint -\n\n2017 to 2017\n\n2013, Nintex.\n\nResponsibilities:\n• Investigated and identified requirements via process flows, use cases communicated with\nSubject Matter Experts to define system requirement.\n\nhttps://www.indeed.com/r/Anurag-Asthana/ea7451b2bdb6115a?isid=rex-download&ikw=download-top&co=IN\n\n\n• Suggested improvements to be included into new system for Skills Alignment Portal\n• Followed Agile development approach to efficiently manage continuous development process\nand incremental requirement changes\n• Independently designed, developed and delivered multiple forms and workflows over\nSharePoint 2013 start from scratch using Nintex\n• Designed and Implemented useful components using SharePoint out-of-the-box capabilities and\nSharePoint REST web service\n• Utilized SharePoint APPs technology to create custom enhancements on current sites for\ncustomer satisfaction and efficiency\n• Developed underwriting support applications using asp.net MVC / Web API.\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nDecember 2013 to July 2015\n\nEnterprise IT Support\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nSeptember 2013 to November 2013\n\nLocation: Pune, India\nClient: Oil and Gas Client from USA\nDuration: September 2013 - November 2013\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nJune 2013 to August 2013\n\nLocation: Pune, India\nClient: Oil and Gas Client from USA\nDuration: June 2013 - August 2013\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nFebruary 2013 to May 2013\n\nInfosys LTD -  MAHARASHTRA, IN -\n\nNovember 2012 to January 2013\n\nLocation: Pune, India\nClient: Infosys Ltd.\nDuration: November 2012 - January 2013\n\nSharePoint Designer, Avepoint Docave\n\nMicrosoft SharePoint -\n\n2012 to 2012\n\n6.\n\nResponsibilities:\n\n\n\n• Responsible for creating SharePoint hosted apps.\n• Responsible for developing web services [WEB API's].\n• Responsible for creating ECMA scripts.\n• Responsible for creating console applications using SharePoint Client Object Model.\n• Responsible for migrating SharePoint MOSS 2007 sites to SharePoint 2013 using Avepoint\nDocave.\n• Responsible for developing remote site provisioning mechanism using CSOM\n• Responsible for creating provider hosted apps using asp.net mvc.\n\nProject Title: Operating System\n\nMicrosoft Fast Search, SharePoint Designer\n\nMicrosoft SharePoint -\n\n2010 to 2010\n\nResponsibilities:\n\n• Responsible for maintaining database of the project.\n• Responsible for designing page layouts.\n• Responsible for creating ECMA scripts.\n• Responsible for creating console applications using SharePoint Client Object Model.\n• Responsible for creating SSIS solution to import data from multiple sources using SharePoint\nweb services.\n• Responsible for creating custom SharePoint alerts using SSIS solution.\n• Responsible for creating SSRS solution to create Dashboards.\n• Responsible for creating Stored Procedures and Functions used for various operations.\n• Responsible for customizing list forms.\n• Responsible for creating Search pages using Microsoft FAST search 2010.\n\nMicrosoft SharePoint -\n\n2010 to 2010\n\nResponsibilities:\n\n• Responsible for fixing bugs in SharePoint applications\n• Responsible for automating bug fixing process.\n• Responsible for interacting with clients for understanding business requirements.\n• Responsible for enhancing SharePoint application as per business requirements.\n• Identifying limitations and suggesting appropriate solutions.\n\nMicrosoft InfoPath Designer\n\nMicrosoft SharePoint -\n\n2010 to 2010\n\n2010, Microsoft SharePoint Designer 2010, Microsoft Office 2010.\n\nResponsibilities:\n\n\n\n• Responsible for creating multiple custom lists and document libraries.\n• Responsible for creating and customizing InfoPath forms and associating it with document\nlibraries.\n• Responsible for creating SharePoint Designer Workflows.\n• Responsible for customizing list forms and views.\n• Responsible for creating Excel reports in SharePoint.\n\nProject Title: 5 Day Close Monitoring Tool\n\nTechnology/Tools: Microsoft SharePoint 2010, Microsoft SQL Server 2008 R2, Microsoft Visual\nStudio 2010, Microsoft InfoPath Designer 2010, Microsoft SharePoint Designer 2010, Microsoft\nOffice 2010\nResponsibilities:\n\n• Responsible for creating custom lists, document libraries, SharePoint groups.\n• Responsible for creating custom permission levels in SharePoint and defining permission\npolicies on lists, libraries and web parts.\n• Responsible for maintaining database of the project.\n• Responsible to create SSIS packages.\n• Responsible to create power pivot reports.\n• Responsible for creating dashboards on SharePoint site using Power Pivot library.\n• Responsible for creating tool for generating power pivot reports on multiple servers.\n\nMicrosoft Designer\n\nMicrosoft SharePoint -\n\n2010 to 2010\n\n2010.\nResponsibilities:\n\n• Responsible for creating multiple custom lists and document libraries.\n• Responsible for creating and customizing InfoPath forms and associating it with document\nlibraries.\n• Responsible for creating SharePoint Designer Workflows.\n• Responsible for customizing list forms and views.\n• Responsible for creating custom permission levels and applying it to various lists and libraries.\n\nEDUCATION\n\nBachelor of Technology in Information Technology\n\nGraphic Era University -  Dehra Dun, Uttarakhand\n\nMay 2012\n\nSenior Secondary -  Kashipur, Uttarakhand\n\n2008\n\n\n\nSKILLS\n\nMICROSOFT SHAREPOINT (7 years), SHAREPOINT (7 years), .NET (6 years), MICROSOFT VISUAL\nSTUDIO (6 years), VISUAL STUDIO (6 years)\n\nADDITIONAL INFORMATION\n\nSKILLS\n• Vast knowledge of developing and implementing applications based on client's needs.\n• Professional Experience in creating SharePoint solutions using C#, JavaScript etc.\n• Professional Experience in creating asp.net web applications using MVC, Entity Framework.\n• Professional experience in creating application using Azure micro services architecture\n\nTECHNICAL EXPERTISE\n\nLanguages and Software\nC, C#.net, Asp.net MVC 5, Entity Framework 6, Microsoft Office, Microsoft SharePoint 2010,\nMicrosoft SharePoint 2013, Microsoft, SharePoint Designer, Microsoft Visual Studio 2008,\nMicrosoft Visual Studio 2010, Microsoft Visual Studio 2012, InfoPath, HTML, CSS, JavaScript,\njQuery, SharePoint Apps using MVC and KnockoutJS, Nintex forms and Workflows, Azure\n\nMSBI SQL Server 2008, SQL Server 2012, SSIS, SSRS\nOperating System Windows Family\n\nTOTAL EXPERIENCE\n\nNo. Company Role Duration\n1. Infosys Ltd Technology Analyst 5 Years 11 Months", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [15, 32, "Location"], [55, 99, "Email Address"], [314, 325, "Companies worked at"], [329, 344, "Location"], [348, 369, "Years of Experience"], [381, 392, "Location"], [401, 417, "Designation"], [440, 461, "Years of Experience"], [2339, 2360, "Designation"], [2362, 2373, "Companies worked at"], [2377, 2392, "Location"], [2396, 2427, "Years of Experience"], [2439, 2450, "Location"], [6428, 6450, "Degree"], [6454, 6500, "College Name"], [6504, 6526, "Location"], [6528, 6537, "Graduation Year"], [7675, 7692, "Years of Experience"], [7644, 7655, "Companies worked at"], [7656, 7674, "Designation"], [229, 240, "Job Specific Skills"], [480, 505, "Tech Tools"], [507, 531, "Tech Tools"], [533, 550, "Tech Tools"], [552, 570, "Tech Tools"], [572, 576, "Tech Tools"], [757, 774, "Job Specific Skills"], [882, 892, "Tech Tools"], [930, 948, "Tech Tools"], [1116, 1155, "Job Specific Skills"], [1157, 1168, "Companies worked at"], [1172, 1178, "Location"], [1182, 1209, "Years of Experience"], [1211, 1222, "Companies worked at"], [1226, 1242, "Location"], [1245, 1272, "Years of Experience"], [1274, 1293, "Designation"], [1295, 1315, "Tech Tools"], [1705, 1722, "Job Specific Skills"], [2292, 2307, "Location"], [2277, 2288, "Companies worked at"], [3542, 3562, "Tech Tools"], [4775, 4796, "Tech Tools"], [5132, 5137, "Tech Tools"], [5149, 5159, "Tech Tools"], [5224, 5249, "Tech Tools"], [5251, 5279, "Tech Tools"], [5281, 5309, "Tech Tools"], [5311, 5343, "Tech Tools"], [5345, 5379, "Tech Tools"], [5381, 5402, "Tech Tools"], [6882, 6892, "Tech Tools"], [6909, 6911, "Tech Tools"], [6913, 6923, "Tech Tools"], [6998, 7001, "Tech Tools"], [7003, 7019, "Tech Tools"], [7156, 7157, "Tech Tools"], [7159, 7165, "Tech Tools"], [7166, 7180, "Tech Tools"], [7182, 7200, "Tech Tools"], [7202, 7218, "Tech Tools"], [7220, 7245, "Tech Tools"], [7247, 7272, "Tech Tools"], [7285, 7304, "Tech Tools"], [7306, 7334, "Tech Tools"], [7336, 7364, "Tech Tools"], [7366, 7394, "Tech Tools"], [7396, 7404, "Tech Tools"], [7406, 7410, "Tech Tools"], [7412, 7415, "Tech Tools"], [7417, 7427, "Tech Tools"], [7429, 7435, "Tech Tools"], [7437, 7447, "Tech Tools"], [7459, 7462, "Tech Tools"], [7467, 7477, "Tech Tools"], [7479, 7485, "Tech Tools"], [7507, 7534, "Tech Tools"], [7536, 7551, "Tech Tools"], [7553, 7557, "Tech Tools"], [7559, 7563, "Tech Tools"], [7581, 7588, "Tech Tools"]]}
{"id": 47, "text": "Syed Sadath ali\nCoimbatore - Email me on Indeed: indeed.com/r/Syed-Sadath-ali/cf3a21da22da956d\n\nWORK EXPERIENCE\n\nSearching for good salary\n\nApple , Google, Microsoft -\n\n2017 to Present\n\nEDUCATION\n\nBCA,MBA\n\nKGISL\n\nSKILLS\n\nC++, Hacking, Programming\n\nhttps://www.indeed.com/r/Syed-Sadath-ali/cf3a21da22da956d?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 15, "Name"], [16, 27, "Location"], [49, 94, "Email Address"], [197, 200, "Degree"], [201, 205, "Degree"], [206, 211, "College Name"], [221, 224, "Tech Tools"], [226, 233, "Job Specific Skills"], [235, 246, "Job Specific Skills"]]}
{"id": 48, "text": "Nida Khan\nTech Support Executive - Teleperformance for Microsoft\n\nJaipur, Rajasthan - Email me on Indeed: indeed.com/r/Nida-Khan/6c9160696f57efd8\n\n• To be an integral part of the organization and enhance my knowledge to utilize it in a productive\nmanner for the growth of the company and the global.\n\nINDUSTRIAL TRAINING\n\n• BHEL, (HEEP) HARIDWAR\nOn CNC System&amp; PLC Programming.\n\nWORK EXPERIENCE\n\nTech Support Executive\n\nTeleperformance for Microsoft -\n\nSeptember 2017 to Present\n\nprocess.\n• 21 months of experience in ADFC as Phone Banker.\n\nEDUCATION\n\nBachelor of Technology in Electronics & communication Engg\n\nGNIT institute of Technology -  Lucknow, Uttar Pradesh\n\n2008 to 2012\n\nClass XII\n\nU.P. Board -  Bareilly, Uttar Pradesh\n\n2007\n\nClass X\n\nU.P. Board -  Bareilly, Uttar Pradesh\n\n2005\n\nSKILLS\n\nMicrosoft office, excel, cisco, c language, cbs. (4 years)\n\nhttps://www.indeed.com/r/Nida-Khan/6c9160696f57efd8?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 9, "Name"], [10, 32, "Designation"], [66, 83, "Location"], [106, 145, "Email Address"], [400, 422, "Designation"], [424, 453, "Companies worked at"], [35, 64, "Companies worked at"], [457, 491, "Years of Experience"], [522, 527, "Companies worked at"], [530, 542, "Designation"], [556, 609, "Degree"], [610, 644, "College Name"], [648, 670, "Location"], [672, 684, "Graduation Year"], [711, 734, "Location"], [365, 380, "Job Specific Skills"], [495, 518, "Years of Experience"], [804, 820, "Tech Tools"], [822, 827, "Tech Tools"], [829, 834, "Tech Tools"], [836, 837, "Tech Tools"]]}
{"id": 49, "text": "Fenil Francis\nhead of operation and logistics\n\nTrichur, Kerala - Email me on Indeed: indeed.com/r/Fenil-Francis/445e6b4cb0b43094\n\nTo succeed in an environment of growth and excellence and earn a job which provides me job\nsatisfaction and self development and help me achieve personal as well as organization goals.\n\nWORK EXPERIENCE\n\nManager\n\nMasters equipments -  Trichur, Kerala -\n\nMay 2017 to Present\n\nSales Manager\n\nMicrosoft Power -  Trichur, Kerala -\n\nMay 2017 to Present\n\n2. Microsoft Office: Microsoft Word, Microsoft Power point, Microsoft Excel\n\nEDUCATION\n\nB.Com in Computer Application\n\nMadurai Kamaraj University -  Madurai, Tamil Nadu\n\n2017\n\nSSLC\n\nKerala State Board\n\n2012\n\nSKILLS\n\nPROBLEM SOLVING (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSKILLS &amp; ABILITIES:\n• Good Communication Skill\n• Sincere\n• Hard working\n• Leadership skill\n• Pleasing personality\n• Problem solving capability\n\nhttps://www.indeed.com/r/Fenil-Francis/445e6b4cb0b43094?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 13, "Name"], [14, 45, "Designation"], [85, 128, "Email Address"], [333, 360, "Designation"], [383, 402, "Years of Experience"], [404, 434, "Designation"], [457, 476, "Years of Experience"], [566, 571, "Degree"], [575, 623, "College Name"], [627, 646, "Location"], [648, 652, "Graduation Year"], [47, 55, "Location"], [56, 63, "Companies worked at"], [364, 371, "Location"], [373, 380, "Companies worked at"], [438, 445, "Location"], [447, 454, "Companies worked at"], [499, 513, "Tech Tools"], [515, 536, "Tech Tools"], [538, 553, "Tech Tools"], [694, 709, "Soft Skills"], [780, 798, "Soft Skills"], [807, 814, "Soft Skills"], [817, 829, "Soft Skills"], [832, 842, "Soft Skills"], [874, 889, "Soft Skills"]]}
{"id": 50, "text": "Gaurav Soni\nNew Delhi, Delhi - Email me on Indeed: indeed.com/r/Gaurav-Soni/d492b5b1697f7f66\n\nWORK EXPERIENCE\n\nSoftware Developer\n\nION Trading -\n\nJune 2016 to Present\n\nAs part of ION Trading, I have been working as a core product developer on a Repurchase\nAgreements(REPO) Trading Solution, called Anvil 9 Trading Solution. Currently I am working on\nOrder Management System, which is a component/service that enable the traders to capture\norders from emails and chats. I have also worked on Apache CoreNLP to leverage its Named\nEntity Recognition feature to extracting business keywords from a given text.\n\nIntern\n\nMicrosoft -  Hyderabad, Telangana -\n\nJune 2015 to August 2015\n\nI was part of the Microsoft Service Global Delivery Internship for two months in Hyderabad. On\nmy internship, I got an opportunity to develop a Windows 10 application.\n\nEDUCATION\n\nB.Tech(Computer Science) in CSE\n\nGGSIPU\n\n2012 to 2016\n\nSKILLS\n\nC# (Less than 1 year), Java, OOPs, Guice, Microservice architecture, Software Development,\nXAML, ATDD, SOLID, SOA, Scrum, Robot Framework, TDD\n\nADDITIONAL INFORMATION\n\nSkills: JAVA, C#, WPF, XAML, Robot Framework, Scrum, Guice, ATDD, SOA, Fixed Income, CoreNLP,\nSOLID\nprogramming, OOPs, NLP, Microservice architecture.\n\nhttps://www.indeed.com/r/Gaurav-Soni/d492b5b1697f7f66?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 11, "Name"], [12, 28, "Location"], [51, 92, "Email Address"], [111, 129, "Designation"], [131, 142, "Companies worked at"], [146, 166, "Years of Experience"], [628, 648, "Location"], [858, 882, "Degree"], [886, 897, "College Name"], [899, 911, "Graduation Year"], [217, 239, "Designation"], [350, 373, "Job Specific Skills"], [491, 505, "Tech Tools"], [522, 546, "Job Specific Skills"], [607, 613, "Designation"], [615, 624, "Companies worked at"], [652, 677, "Years of Experience"], [759, 768, "Location"], [921, 923, "Tech Tools"], [944, 948, "Tech Tools"], [950, 954, "Tech Tools"], [956, 961, "Tech Tools"], [963, 988, "Job Specific Skills"], [990, 1010, "Job Specific Skills"], [1012, 1016, "Tech Tools"], [1018, 1022, "Tech Tools"], [1024, 1029, "Tech Tools"], [1031, 1034, "Tech Tools"], [1036, 1041, "Job Specific Skills"], [1043, 1058, "Tech Tools"], [1097, 1101, "Tech Tools"], [1103, 1105, "Tech Tools"], [1107, 1110, "Tech Tools"], [1112, 1116, "Tech Tools"], [1135, 1140, "Job Specific Skills"], [1174, 1181, "Tech Tools"], [1183, 1188, "Tech Tools"], [1202, 1206, "Job Specific Skills"], [1208, 1211, "Job Specific Skills"], [1213, 1238, "Job Specific Skills"]]}
{"id": 51, "text": "Viny Khandelwal\nSelf-employed in Family Business - SELF EMPLOYED\n\nKullu, Himachal Pradesh - Email me on Indeed: indeed.com/r/Viny-\nKhandelwal/02e488f477e2f5bc\n\n• Currently pursuing my certifications in Digital Marketing.\n• 2 year's experience in managing, maintaining and assisting to run a camping site.\n• Additional experience in event managing and emcee in Army Wife Welfare Association (NGO)\n• 1-year self-employed in Kudi Firang.\n• 2 years of work experience in Infrastructure Management and Support in TCS.\n• Worked as Soft skill trainer in TCS.\n• Worked as Operation Analyst for Microsoft Server management team.\n• Worked as Application Support engineer for PricewaterhouseCoopers, USA.\n• Worked as Technical Support for Microsoft Office, Outlook and PowerPoint.\n\nWORK EXPERIENCE\n\nSelf-employed in Family Business\n\nSELF EMPLOYED -\n\nFebruary 2015 to Present\n\nof tourist camping site. Managing, maintaining and assisting to run a camping site.\n• Self-employed in Kudi Firang under self-proprietary. Designing Digital print art and getting it\nprinted for Handbags and Home Décor. Actively participated in exhibitions and Trade shows.\n\nSystems Engineer\n\nMicrosoft Corporation -  Hyderabad, Telangana -\n\nOctober 2012 to January 2015\n\nPROJECTS:\n1. Microsoft Server Management\n\n➢ Client: Microsoft Corporation.\n• Role: Microsoft Server infrastructure remote management - Remotely logon to and manage the\nMicrosoft Corporation datacenter servers all over the world and configure/ deploy according to\nthe incident/change management request/ticket.\n• Responsibilities: Incident management and change management, performing simple changes\nand re-assigning incident tickets to on-site work, server rename, rebuild, configuration tasks,\ndatacenter server deployments and hardware procurement/replacement follow-ups.\n\n2. PwC Application Support\n\n➢ Client: PricewaterhouseCoopers.\n• Role: Incident management and other applications for corporates and firms in USA.\n\nhttps://www.indeed.com/r/Viny-Khandelwal/02e488f477e2f5bc?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Viny-Khandelwal/02e488f477e2f5bc?isid=rex-download&ikw=download-top&co=IN\n\n\n• Responsibilities: Handling deployment of firm application upgrades and reporting of bugs and\nerrors to development team, SLA and quality report submission and training agents according\nto new updates and best practices shared, attending team meetings for process development\nand improvement, maintain existing and generate new operational support documentation,\ncommunicating with 3rd party service providers for account activations, upgrades, password\nresets and membership renewals, troubleshooting system software and hardware configuration\nissues, log issue and user contact into customer database, translate end-user business needs\ninto working solutions.\n\nEDUCATION\n\nBachelor of Engineering in Digital Marketing\n\nNitte Meenakshi Institute of Technology -  Bengaluru, Karnataka\n\nSKILLS\n\nEXCHANGE (Less than 1 year), HTML (Less than 1 year), KITCHEN (Less than 1 year), LINUX\n(Less than 1 year), VMWARE (Less than 1 year)\n\nGROUPS\n\nAWWA\n\nNovember 2015 to Present\n\nADDITIONAL INFORMATION\n\n• Photography and video editing for Viny's Kitchen (VLOG on YouTube)\n• Good communication skill and Public Speaking as Orator.\n• Knowledge on Windows Server […] Linux, Exchange server, VMware, HTML.\n\nPERSONAL DOSSIER\n\nName: Viny Khandelwal\nPhone: […]\nEmail: <EMAIL>\nHusband's Name: Capt. Anuj Mahant\n\nPermanent Address: HNO - 240, Gandhinagar,\nDhalpur, […]", "meta": {}, "annotation_approver": null, "labels": [[0, 15, "Name"], [66, 89, "Location"], [112, 158, "Email Address"], [467, 504, "Designation"], [508, 511, "Companies worked at"], [437, 444, "Years of Experience"], [422, 433, "Companies worked at"], [398, 404, "Years of Experience"], [525, 543, "Designation"], [547, 550, "Companies worked at"], [564, 581, "Designation"], [632, 660, "Designation"], [665, 687, "Companies worked at"], [689, 692, "Location"], [706, 723, "Designation"], [839, 863, "Years of Experience"], [968, 979, "Companies worked at"], [1139, 1155, "Designation"], [1157, 1178, "Companies worked at"], [1182, 1202, "Location"], [1206, 1234, "Years of Experience"], [2833, 2856, "Degree"], [2860, 2918, "College Name"], [2922, 2942, "Location"], [3375, 3391, "Name"], [3409, 3434, "Email Address"], [202, 219, "Degree"], [586, 618, "Companies worked at"], [728, 744, "Tech Tools"], [746, 753, "Tech Tools"], [758, 768, "Tech Tools"], [1566, 1585, "Job Specific Skills"], [1590, 1607, "Job Specific Skills"], [1881, 1900, "Job Specific Skills"], [2952, 2960, "Tech Tools"], [2981, 2985, "Tech Tools"], [3034, 3039, "Tech Tools"], [3060, 3066, "Tech Tools"], [3153, 3182, "Job Specific Skills"], [3227, 3240, "Soft Skills"], [3251, 3266, "Soft Skills"], [3293, 3307, "Tech Tools"], [3312, 3317, "Tech Tools"], [3319, 3334, "Tech Tools"], [3336, 3342, "Tech Tools"], [3344, 3348, "Tech Tools"]]}
{"id": 52, "text": "amarjyot sodhi\nVoice and Accent Trainer :Masters in journalism and communication with\n3 years experience\n\nFaridabad, Haryana - Email me on Indeed: indeed.com/r/amarjyot-sodhi/ba2e5a3cbaeccdac\n\nTo constantly learn, enhance my skills and capabilities to reach higher level of competence\nand apply my knowledge and skill to the best of my ability in the interest of the organization.\nAccomplishment counts, not the non-executed ideas. So I just constantly execute the ideas in\nquest for the accomplishment.\n\nWORK EXPERIENCE\n\nSales Associate\n\nShuttl -  Faridabad, Haryana -\n\nSeptember 2017 to May 2018\n\nQuery management and outbound sales\n\nAssociate\n\nSutherland -  Chennai, Tamil Nadu -\n\nOctober 2016 to June 2017\n\n● Query Management\n● Outbound/Inbound Calling\n● Collections\n● Customs Documentation work\n\nvoice and accent trainer\n\nteleperformance microsoft -  Jaipur, Rajasthan -\n\nOctober 2014 to February 2015\n\nResponsibilities\nTo train the students on there comm skills and cx handling skill\n\nEDUCATION\n\nmasters in journalism and communication in mass comm client servicing\n\namity -  Noida, Uttar Pradesh\n\n2011 to 2013\n\nhttps://www.indeed.com/r/amarjyot-sodhi/ba2e5a3cbaeccdac?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [15, 39, "Designation"], [106, 124, "Location"], [147, 191, "Email Address"], [522, 545, "Companies worked at"], [549, 567, "Location"], [571, 597, "Years of Experience"], [599, 634, "Designation"], [636, 657, "Companies worked at"], [661, 680, "Location"], [684, 709, "Years of Experience"], [713, 729, "Designation"], [856, 873, "Location"], [877, 906, "Years of Experience"], [1082, 1102, "Location"], [86, 104, "Years of Experience"], [928, 933, "Soft Skills"], [1002, 1054, "Degree"], [1104, 1116, "Graduation Year"]]}
{"id": 53, "text": "Sameer Kujur\nOrrisha - Email me on Indeed: indeed.com/r/Sameer-Kujur/0771f65bfa7aff96\n\nWORK EXPERIENCE\n\nApp develop\n\nMicrosoft -\n\nAugust 2016 to Present\n\nEDUCATION\n\nElectrical engineering\n\nVSSUT,burla\n\nSKILLS\n\nApplication Development, Software Testing\n\nhttps://www.indeed.com/r/Sameer-Kujur/0771f65bfa7aff96?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 20, "Name"], [43, 85, "Email Address"], [104, 115, "Designation"], [117, 127, "Companies worked at"], [130, 152, "Years of Experience"], [165, 187, "Degree"], [189, 194, "College Name"], [210, 233, "Job Specific Skills"], [235, 251, "Job Specific Skills"]]}
{"id": 54, "text": "Zaheer Uddin\nTechnical Project Manager\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Zaheer-Uddin/fd9892e91ac9a58f\n\nTo be associated with a dynamic team in a progressive organization that gives me the scope to\napply and enrich my knowledge and skills with continual learning and professional growth.\n\nWORK EXPERIENCE\n\nTechnical Project Manager\n\nDELL EMC -\n\nAugust 2015 to May 2018\n\nProvide expertise to support the project team in the following areas:\n* Activity and resource planning.\n* Analyzing and managing project risk.\n* Planning, organizing, and Leading the Project teams from the entire metrics standpoint.\n* Scope &amp; Charter Management.\n* Handled the Account Level Audit (ERM, ITIL)\n* Estimations and cost management.\n* Project Timeline planning and management\n* Procurement, Software Asset management, Change management and Configuration\nmanagement.\n* Issue resolution and escalation\n* Communications &amp; status reporting\n* Scheduling, attending and running all project meetings.\n* Identify, track, and resolve/escalate project impediments, risk, issues, actions.\n* Implementing Power BI Business Analytics tool to analyze data and share insights.\n* Implemented Six Sigma project (DMAIC) for improving the CSAT Score overall.\n* Implemented LEAN project for optimizing the resource management and the Average handling\ntime on the tickets/resolutions.\n\nTechnical Lead/Operations Manager\n\nMicrosoft -\n\nJuly 2013 to July 2015\n\n* Managing 24x7 IT infrastructure services with the scope of Incident management, Security\npatching and software deployment management services for Microsoft Outlook.com email\nservice and O365 cloud services with team size of 68 people.\n* Transitioned and consolidated the Incident management, security patching and deployment\nmanagement work for optimizing the team by improving the productivity and reducing the cycle\ntime.\n* Prepared technical architecture documents integrating all components of the project for better\ntroubleshooting.\n\nhttps://www.indeed.com/r/Zaheer-Uddin/fd9892e91ac9a58f?isid=rex-download&ikw=download-top&co=IN\n\n\n* Deploying builds &amp; security patches as per Build calendar schedule and co-coordinating\nthe issues that arise during the build.\n* Monitoring, Remote administration, maintenance of a Datacenter Servers comprising of\n10,000+ Servers.\n* Trouble shooting on HP storage Architecture in different SKU's with RAID concepts.\n* Implementing disaster recovery procedures to ensure minimal downtime and data loss.\n* Re-building Operating Systems on servers (2003, 2008, 2008 R2) that are in the Hotmail\nEnvironment.\n* Transitioned windows infrastructure support services involving the frontend, inbound, outbound\nand storage infrastructure.\n* Handled security patching, application deployment and incident management for Windows,\nSQL and HP storage servers.\n* Participate in Feature Specification and Release Reviews to ensure complete understanding of\nthe features being deployed.\n* Created the knowledge base articles for the debugging of the software application for the benefit\nof the Customer, Partner and the engineering team.\n\nSenior Service Engineer\n\nEMC -\n\nJanuary 2011 to June 2013\n\n* Experience in installing and maintaining Windows Server 2000/2003, 2008.\n* Alert and resolve any performance issues and notify end-users and resolve any storage\nshortages issues.\n* Monitor and resolve any issues related to Usage, Performance, and availability on storage.\n* Install and configure the EMC Disk tender (archiving) application as per the customer\nrequirements.\n* Troubleshooting NAS, CAS and DAS Storage issues on the servers.\n* Replicate the break-fix implementation in the test machines to test the functionality of the\nbreak-fix and reproduce the same on the production server.\n* Perform testing of the registry exports made from the Production environment and try and test\nit on the Test environment to resolve issues.\n\nSystems Engineer\n\n-\n\nDecember 2007 to December 2010\n\n* Designing and developing computer hardware and support peripherals, including central\nprocessing units (CPUs), support logic, microprocessors, custom integrated circuits, printers, and\ndisk drives.\n* Managing, monitoring and troubleshooting all installed systems and infrastructure.\n* Installing, configuring, testing and maintaining operating systems application software and\nsystem management tools.\n* Ensuring the highest levels of systems and infrastructure availability.\n* Handling Level-2 technical escalations Tickets/Calls.\n* Performing Upgrades, Installation of Software and Drivers and essential software\ntroubleshooting.\n\n\n\n* Assisting end users in deploying Updates, services packs and hot fixes using Windows Update,\nAutomatic Updates.\n* Setting up Active Directory, creating user accounts and providing permissions as requested by\nthe network admin team.\n\nEDUCATION\n\nBSc\n\nOsmania University\n\n2007\n\nBoard of Secondary Education -  Hyderabad, Telangana\n\n2004\n\nDiploma in Computer Application\n\nMac infotech Center", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [13, 38, "Designation"], [40, 60, "Location"], [83, 125, "Email Address"], [329, 354, "Designation"], [356, 364, "Companies worked at"], [368, 391, "Years of Experience"], [1377, 1410, "Designation"], [1412, 1422, "Companies worked at"], [1425, 1447, "Years of Experience"], [3116, 3139, "Designation"], [3141, 3145, "Companies worked at"], [3148, 3173, "Years of Experience"], [3914, 3930, "Designation"], [3935, 3965, "Years of Experience"], [4850, 4853, "Degree"], [4855, 4873, "College Name"], [4875, 4880, "Graduation Year"], [465, 495, "Job Specific Skills"], [499, 534, "Job Specific Skills"], [708, 739, "Job Specific Skills"], [743, 783, "Job Specific Skills"], [799, 824, "Job Specific Skills"], [826, 843, "Job Specific Skills"], [848, 872, "Job Specific Skills"], [1111, 1132, "Job Specific Skills"], [1188, 1197, "Job Specific Skills"], [4913, 4933, "Location"], [4935, 4939, "Graduation Year"], [4941, 4972, "Degree"]]}
{"id": 55, "text": "Abdul B\nArabic Language supporter (Content Analyst)\n\nKarnataka, Karnataka - Email me on Indeed: indeed.com/r/Abdul-B/eb2d7e0d29fe31b6\n\n• 7+ years of experience in supporting ,specially Arabic Language content Arabization.\nGood expertise in working at MNC. As Thomson Reuters for EMEA Project as giving\nlanguage support as a content analyst for financial Data.\n• Language testing in LG soft India as a language tester engineer.\n• The Arabic project of CRM for Microsoft project with language testing experience in\nWipro technologies.\n• Arabic, Urdu, English and Mobile features testing in L G Soft India Bangalore.\n• Present working at Al-wadi Int School as an Arabic/Islamic teacher.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nArabic Language Trainer\n\nAssociated -  Bengaluru, Karnataka -\n\n2010 to 2012\n\nand Arabization Jobs and\nworked as in interpreter for several clients. Language CA for (Arabic) CRM project in Wipro\nTechnologies.\n\nArabic Linguist\n\nHyderabad, Telangana -\n\nJune 2010 to September 2010\n\nTeam Size 30\nWindows Server 2008 R2, MS Office 2010, Windows Vista,\nVisual Studio 2008 SP1, Microsoft Exchange Server 2007,\nEnvironment Software Windows Server 2008 R2 Active Directory Server, Windows\nServer 2008 R2 Hyper-V, Product Studio, UI And\nfunctional automation testing frameworks.\n\nDescription:\nMicrosoft Dynamics CRM is CRM Software application that the businesses use to track and\nmanage the interactions with their customers - such as phone calls and emails. This project\ninvolves\ntesting the Microsoft Dynamics CRM Version5 for around 16 languages for localization and\nfunctional issues.\n\nRoles and Responsibilities:\n• Created the test cases for Microsoft Dynamics CRM modules\n• Execution of the test cases and updating the results in the bug tracking and logging internal\n\nhttps://www.indeed.com/r/Abdul-B/eb2d7e0d29fe31b6?isid=rex-download&ikw=download-top&co=IN\n\n\ntool \"Product Studio\"\n• Creating automation test cases\n\nMiddle East language tester engineer\n\n-\n\n2006 to 2010\n\nTest Engineer (Arabic language)\n\nMICROSOFT INC -\n\nJune 2007 to October 2007\n\nDuration: 2007 June -Oct 2008\nResume\nWork Experience (Wipro Technologies) CRM project\nProject Details\nPROJECT#5 Microsoft Dynamics CRM\nClient MICROSOFT INC.\nRole Test Engineer (Arabic language)\nTesting Automated (UI &amp; Functional) &amp; Manual\n\nEDUCATION\n\nMaster's\n\nSKILLS\n\nENGINEER (4 years), TESTING (Less than 1 year), UI (3 years), USER INTERFACE (Less than 1\nyear), ANALOG (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nTechnical Skills: LG soft India (Bangalore)\n\nApart from teaching and training, I posses a good know how and have experience of working on\nmobilization in the field of Telecommunication while I was working as a UI tester (Software Tester\nEngineer Using with TD ) for Middle east Languages at LG Soft India.\n\nPROJECT DETAILS\n\nPlatform: Analog Devices\nDomain: GSM and GPRS\nDefect Tracking Tool: Test Director (Mercury Quality Centre)\nTest Tool: Genie (Target Testing &amp; Signal Logs)\n\nDescription: The ADI model of LG Electronics is a color display model, Contains the features of\nTelephony services, SMS, MMS, WAP, Java Application, Bluetooth and advanced multimedia\nfeatures like MP3, Camera, FM, Video Recording. This model supports PC Sync tool that is used to\n\n\n\nconnect Mobile Station with PC.\n\nResume\nResponsibilities:\n\n• Teaching students the language skills and interaction in the language of Arabic. Also\nteaching and interacting with Arabs as a language translator.\n• Interaction with Arab professionals during the tenure as a software tester in LG soft.\n• Writing test cases for new features, and Updating of check lists.\n• Involved in feature testing modules like Messages, Settings, and Browser.\n• Reported bugs and execution in Test Director.\n• Conduct Target Testing on Mobile Station (Call Flow, Roaming &amp; Mobility Management,\nRadio Resource stability and GPRS connection) using test Tool.\n• Conduct IOT in different locations of India.\n• Good knowledge command in the Middle East languages Arabic, Urdu &amp; Farsi", "meta": {}, "annotation_approver": null, "labels": [[0, 7, "Name"], [8, 51, "Designation"], [53, 73, "Location"], [96, 133, "Email Address"], [588, 596, "Companies worked at"], [597, 613, "Location"], [660, 682, "Designation"], [733, 768, "Designation"], [772, 792, "Location"], [796, 808, "Years of Experience"], [942, 957, "Designation"], [959, 979, "Location"], [983, 1010, "Years of Experience"], [1948, 1984, "Designation"], [1989, 2002, "Years of Experience"], [2003, 2034, "Designation"], [2036, 2049, "Companies worked at"], [2053, 2078, "Years of Experience"], [2090, 2109, "Years of Experience"], [2339, 2347, "Degree"], [137, 159, "Years of Experience"], [185, 200, "Soft Skills"], [324, 339, "Designation"], [344, 358, "Job Specific Skills"], [362, 378, "Job Specific Skills"], [401, 425, "Designation"], [1025, 1044, "Tech Tools"], [1049, 1063, "Tech Tools"], [1065, 1078, "Tech Tools"], [1080, 1102, "Tech Tools"], [1104, 1134, "Tech Tools"], [1517, 1539, "Tech Tools"], [2242, 2273, "Designation"], [2222, 2235, "Companies worked at"], [2377, 2384, "Job Specific Skills"], [2405, 2407, "Job Specific Skills"], [2419, 2433, "Job Specific Skills"]]}
{"id": 56, "text": "Bike Rally\nChief Coordinator of LEAR\n\nPalghat, Kerala - Email me on Indeed: indeed.com/r/Bike-Rally/e00d408e91e83868\n\nEducation\n\nWORK EXPERIENCE\n\nChief Coordinator of LEAR\n\nMicrosoft -\n\nJune 2011 to July 2013\n\nunder Office Kerala State Board\n\nEdius\ntally\n\nProshow gold Key roles held\nPhotoshop\n\nChief Coordinator of LEAR\nIt is a well equipped training team under the mentorship of JCI\nInternational member and HR international Trainer Dr. Thomas K George,\nChairman of LEAD College of Management, Dhoni.\n\nGet in Touch Key Organizer of Le Adventure, I LEAD and LEADing bands\nThese programs were conducted by LEAD College of Management.\n\nSpecial Police Officer during Panchayat elections\n\nMicrosoft -\n\nJune 2011 to July 2013\n\<EMAIL>\n\nEDUCATION\n\nMBA in marketing-tourism\n\nCalicut University -  Calicut, Kerala\n\n2016 to 2018\n\nhttps://www.indeed.com/r/Bike-Rally/e00d408e91e83868?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 10, "Name"], [11, 28, "Designation"], [32, 36, "Companies worked at"], [38, 53, "Location"], [76, 116, "Email Address"], [146, 163, "Designation"], [167, 182, "Companies worked at"], [186, 208, "Years of Experience"], [223, 235, "Location"], [295, 312, "Designation"], [316, 320, "Companies worked at"], [635, 657, "Designation"], [753, 777, "Degree"], [779, 797, "College Name"], [801, 816, "Location"], [826, 830, "Graduation Year"], [723, 740, "Email Address"]]}
{"id": 57, "text": "Girish Acharya\nTechnical Architect & Sr. Software Engineer\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Girish-Acharya/6757f94ee9f4ec23\n\nI would like to describe myself as a hard-core software engineer and technical architect\npassionate for technology and someone who loves to take challenges head-on. I have been using\ncombined engineering principles wherein I was responsible for designing, developing, testing,\ndeploying and monitoring highly scalable distributed applications and services. \n\nWhile I have always been working using Microsoft technologies, I got chance to work for Microsoft\nin as a full-time employee last year. My focus as part of this role was as a Dev Ops/Site Reliability\nEngineer. The main requirement of this role was to keep-lights-on in all data centers for Windows\nDeveloper Center. While Service Engineering aspect was the main requirement, I used my\ntechnical architect and software tools for inside-out and outside-in monitoring and telemetry. \n\nAs a technical architect, I have worked in Accenture for about 7+ years. My role @ Microsoft\naccount was to demonstrate technical leadership, design technical architecture, propose\nand introduce standard design and architectural patterns and practices, incorporate new\nrequirements and feedbacks from client and execute multiple quick prototypes. As part of this\nrole – I architected Microsoft IT’s (MSIT) biggest Platform As A Service (PaaS) solution back in\nthe days.\n\nI have executed multiple client facing projects wherein I was responsible for designing,\narchitecting the compute, storage, application and deployment architecture. I have experience\nin designing and architecting systems for Business Continuity and Disaster Recovery (BCP/\nDR strategies). I have designed and architected solutions for “Testing in production”. I have\nexperience in designing and architecting for extremely high scale (~ 3 billion requests/day). I\nhave designed and implemented auto healing, smooth traffic routing and eventual consistency\nin multiple client facing projects. \n\nI am in the Information Technology industry since 2001 working for various software organizations\nin India for 4 years till I joined Accenture in 2006. I came to USA in 2010 and was working as\nconsultant for Microsoft. I joined Microsoft in May 2015 as a full-time employee. I earned my\nElectronics Engineering degree in year 2000, Diploma in Advance Computing degree in year 2001\nand completed Microsoft Certified Professional Developer and Enterprise Architect certification\n(MCPDEA) in year 2007. I am also Microsoft Azure Certified Developer and Architect. \n\nI am frequent community contributor. I blog at http://girishthegeek.wordpress.com/ \n\nI have worked and published an asset called Framework for Ops Team considering Windows Azure\nand it was very well received by the clients and appreciated by the managers.\n\nI wrote a white paper related to AppFabric Service Bus which got published in MSDN.\n\nWilling to relocate: Anywhere\n\nhttps://www.indeed.com/r/Girish-Acharya/6757f94ee9f4ec23?isid=rex-download&ikw=download-top&co=IN\n\n\nWORK EXPERIENCE\n\nTechnical Architect\n\nAccenture Services -  Pune, Maharashtra\n\nSoftware Engineer 2\n\nMicrosoft -  Redmond -\n\nJune 2016 to February 2017\n\nEngagement and monetization (Microsoft – Universal Store/Windows Developer Center)\nJune 2016– Recently \n\n〓 Technologies used:\nMicrosoft Azure, C#, SQL, PowerShell, App Insights, Azure Data Factory, Redis Cache, API\nManagement Gateway, Azure App Services, Akamai, Azure CDNs, Azure Traffic Manager,\nAutomated Unit Testing, Web Testing, Coded Unit Tests, Performance Testing etc. Tools – SpecFlow,\nJMeter, Junit, NUNit, MoQ, Fluent Assertions, VSTS, GIT, Microsoft Test Manager, Continuous\nIntegration and deployment, maintaining build definitions \n\n〓 Roles Played:\n\no Technical architect \no Designing and architecting for compute, storage, application, security and deployment\narchitecture \no Designing and architecting for Business Continuity and Disaster Recovery (BCP DR)\no Designing and architecting for the Testing In Production (TIP)\no Designing and architecting for auto healing and auto scaling pattern \no Designing and developing for monitoring \n〓 Design and develop inside out and outside in monitoring and logging \n〓 Design and develop synthetic web tests\n〓 Design and develop watchdogs \no Design and develop the global traffic management using Akamai \no Keeping lights on for the services in all data centers \no Firefighting drills and actual firefighting \no Live site issue tracking, management, coordination and resolving \no Capacity monitoring and management \no Code reviews \no Implemented the security architecture\n\nEDUCATION\n\nBachelor of Engineering in Microsoft\n\nC-DAC\n\n\n\nSKILLS\n\nAzure (8 years), Asp.Net (8 years), C# (9 years), Web API (6 years), Sql (6 years), Technical\nArchitect (5 years), Automation Testing (5 years)\n\nLINKS\n\nhttp://girishthegeek.wordpress.com/\n\nhttps://www.linkedin.com/in/girishazure\n\nCERTIFICATIONS/LICENSES\n\nMicrosoft Certified Professional Developer\n\nMicrosoft Certified Professional Developer\n\nMicrosoft Certified Enterprise Architect\n\nADDITIONAL INFORMATION\n\nTechnical Expertise\n\n*Technical Architecture, Microsoft Azure Platform, Microsoft .NET Platform, C#, ASP.NET*\n\n* Designing/Architecting/Building/Testing/Deploying apps/APIs using Windows Azure, SQL Azure,\nAzure Storage, REST/WCF, AppFabric Cache, Service Bus, Topic and Subscription, Access Control\nServices from last 7 years.\n* Azure Application Architecture: Web Role, Worker Role, VM Role, admin mode, start-up tasks\nintegrating with on premise services using App Fabric Service Bus\n* Azure Security Architecture: Web Role security using SSL, WCF Security using certificates,\nData Encryption using certificates, Authentication using ADFS/STS, Single sign on using ACS.\nParticipated in ACE reviews, and provisioned related documentation/code\n* Azure Operations Architecture: Logging and monitoring using Windows Azure Diagnostics,\nintegration with AVICode, SCOM, disaster recovering and business continuity planning and\nimplementation, automated builds and deployments.\n* Azure Data Architecture: Understands SQL Azure, Windows Azure Storage (Tables, Blobs,\nQueues), Document DB, SQL Azure Data Sync, SQL Azure Federation etc.\n* Deployment Architecture: Continuous integration and deployment. Auto Build, Deployment\nusing Service Management APIs and PowerShell cmdlets\n* Akamai - design and implement traffic management, caching using Akamai\n* Azure traffic manager, CDNs, Azure Data Factory, Auto Scaling\n* Deployment automation and synthetic monitoring using application insights\n* Developing end to end APIs and applications using ASP.NET Web Forms, ASP.NET MVC, Web API\n* Performance Testing: Performance, scalability testing using Visual Studio\n* Automated Testing: Automated Unit Testing, Web Testing, Coded Unit Tests, Performance Testing\netc. Tools - SpecFlow, Jmeter, Junit, NUNit, MoQ, Fluent Assertions, VSTS, GIT\n* Big Data: Architected and developed a big solution using Windows Azure HD Insight, Hive\nqueries\n\nhttp://girishthegeek.wordpress.com/\nhttps://www.linkedin.com/in/girishazure\n\n\nProjects Undertaken\nEngagement and monetization (Microsoft - Universal Store/Windows Developer Center)\nJune 2016- Recently\n\n* Technologies used:\nMicrosoft Azure, C#, SQL, PowerShell, App Insights, Azure Data Factory, Redis Cache, API\nManagement Gateway, Azure App Services, Akamai, Azure CDNs, Azure Traffic Manager,\nAutomated Unit Testing, Web Testing, Coded Unit Tests, Performance Testing etc. Tools - SpecFlow,\nJMeter, Junit, NUNit, MoQ, Fluent Assertions, VSTS, GIT, Microsoft Test Manager, Continuous\nIntegration and deployment, maintaining build definitions\n\n* Roles Played:\n\n◦ Technical architect\n◦ Designing and architecting for compute, storage, application, security and deployment\narchitecture\n◦ Designing and architecting for Business Continuity and Disaster Recovery (BCP DR)\n◦ Designing and architecting for the Testing In Production (TIP)\n◦ Designing and architecting for auto healing and auto scaling pattern\n◦ Designing and developing for monitoring\n* Design and develop inside out and outside in monitoring and logging\n* Design and develop synthetic web tests\n* Design and develop watchdogs\n◦ Design and develop the global traffic management using Akamai\n◦ Keeping lights on for the services in all data centers\n◦ Firefighting drills and actual firefighting\n◦ Live site issue tracking, management, coordination and resolving\n◦ Capacity monitoring and management\n◦ Code reviews\n◦ Implemented the security architecture\n\nChannel inclusion Services - (CIS) Client: Microsoft Corporation / Group: Devices and Studios\n(formerly known as IEB)\nSep 2012- June 2015\n\nSynopsis:\n\nCIS is bunch of REST, SOAP services exposed to around 250+ partners dealing with end-to-end\nretail workflow for around 2 million transactions/day. Microsoft uses these services to distribute\nthe digital goods (Windows, Office and so on.) . It also takes care of the brick and mortar scenario\nwhere in customers walk into the shop to buy Microsoft products\n&lt;&gt;\n* Technologies used:\n◦ Windows Azure - Web Roles, Worker Roles, ASP.NET Web API, ASP.NET MVC and Web Forms,\nWCF, Service Bus, Azure Storage\n◦ SQL Azure, HD Insight, Azure Scheduler\n◦ Entity Framework\n◦ MOQ, SpecFlow, NUNit, JMeter, RhinoMock\n* Roles Played:\n\n\n\n◦ Automated unit, performance, integration testing, synthetic testing, data testing, monitoring\nand improving code coverage, building test case and bugs /defects reports\n◦ Continuous Integration and deployment, maintaining build definitions\n◦ Designing and architecting service/api specifications and contracts\n◦ Designing and architecting for high scale\n◦ Designing and architecting for compute, storage, application, security and deployment\narchitecture\n◦ Designing and architecting for Business Continuity and Disaster Recovery (BCP DR)\n◦ Implementing (developing, testing and monitoring) highly scalable, distributed services\n◦ Senior developer consultant responsible to work independently with the clients in designing\ncontracts, defining user stories, implementing them\n◦ Write, execute the BVTs, Unit tests and functional tests, monitoring and improving code\ncoverage\n◦ Building test plans, test strategy\n◦ Code reviews\nInvolved in Application, Security, Operations and Data architecture\n\nMicrosoft Store - Commerce Broker Services (CBS) Client: Microsoft Corporation / Group: Devices\nand Studios (formerly known as IEB)\nNov 2011 - Till Sep 2012\n\nSynopsis:\n\nCBS is Service Oriented middle tier component which is responsible for communicating with\nMicrosoft Store front end web site and down-stream systems (SAP, Fulfillment, Payment Gateway\netc.)\n&lt;&gt;\n* Technologies used:\n◦ Windows Azure Queues, Service Bus Topics and subscriptions\n◦ WCF\n◦ WCF Workflow Services\n◦ Entity Framework\n◦ MOQ, SpecFlow, NUNit, JMeter\n* Roles Played:\n\n◦ Building test plans, test cases, bugs reports, test strategy\n◦ Continuous Integration and deployment, maintaining build definitions\n◦ Automated unit, performance, integration testing, synthetic testing, data testing\n◦ Senior consultant responsible to work independently with the clients in designing contracts,\ndefining user stories, implementing them\n◦ Write, execute the BVTs, Unit tests and functional tests\n◦ Code reviews\n◦ Involved in Application, Security, Operations and Data architecture\n\nBCWeb Wrap Migration to Windows Azure Client: Microsoft Corporation / Group: VLIT\nJan 2011 - Oct 2011\n\nCase Study Featured on TechNet\n\n\n\nSynopsis:\n\nBCWeb is a Windows Azure ASP.NET Web Role application; authenticated against STS using\nFederation services that provide a set of Web forms that help capture price exception details,\ninformation on business case for doing discounts on price lists, and helps calculate percentage\nof discount. It uses the ASP.NET connector and custom Web services to pull real-time price\ninformation from SAP, and provides sales executives with a light front-end to SAP. BCWeb helps\nsimulate pricing discounts, manage business rules, customize routing and approval workflows,\nand update the LOB data stored in SAP. One of the major goals of this system is to improve\nfield productivity by simplifying the process and removing training requirements for complex\nback-end systems, in this case SAP. BCWeb integrates different promotion types into a simplified\nWeb-application. It also allows users to specify business rules using Excel 2007 files and to get\nnotifications in Outlook 2007. Additional goals of this system include:\n• Reduce revenue leakage by having a business case documented for each promotion field.\n• Obtain visibility decisions and understand who required a discount and why.\n• Understand promotion effectiveness and ensure the field is making informed decisions.\n• Provide a configurable set of files to easily change empowerment guidelines by having a single\nmaster data of workflow routing and approvals.\n• Specific business process in price-execution domain, however, pattern addresses larger\nsolutions space.\n* Roles Played:\n\n◦ Responsible for technical architecture\n◦ Involved in Application, Security, Operations and Data architecture\n◦ Work Estimation and migration assessment\n◦ Key Contributor as Windows Azure Developer, Analyst\n\nOrigin Digital Cloud Coder Client: Origin Digital\nJan 2010 - May 2010\n\nFeatured on MSDN\n\nSynopsis:\n\nOrigin Digital, a video application service provider that aggregates, transcodes, manages and\ndistributes the digital media files. As part of the business need, they wanted to increase\ntranscoding throughput without increasing capital expense.\n\nAs part of the solutioning, the onshore technical architect along with IDC development team put\ntogether an approach that allowed\n- Process intensive digital video transcoding service to-run-on Azure Platform leveraging cloud's\nreal-time scalability and compute capacity. Infrastructure as a Service (IaaS) offering.\n- Secure all business sensitive information by using On-premise data storage through Azure\n- An administration module to support Platform as a Service (PaaS) was developed.\n\nThe team completed the whole migration activity in 6 weeks using Agile methodology and\nmanaged to showcase it successfully in Microsoft PDC 2009 at Los Angeles. The solution resulted\nin reducing the compute costs by half.\n\n\n\n* Technologies used: ASP.Net 3.5, SQL Azure, Windows Azure, Azure Blob/Table Storage, WCF etc.\n\n* Roles Played:\n\n◦ Over all Application and Data architecture\n◦ Designing Auto Scaling Engine (Rules Based)\n◦ Leading a team to do daily integration test on cloud\n◦ Designing classes/database/user interface.\n◦ Coordinating deployment procedure on cloud.\n\nClaims FNOL Client: StateFarm\n\nMay 2009 - Aug 2009\n\nSynopsis:\n\nFNOL (First Notice of Loss) is the front end for Insurance Company employees to log the insurance\nclaims. It implements AICS framework which is exposed for other companies to use on Azure as\nSaaS/multi-tenant (Software-as-service) service.\n\n* Technologies used: ASP.Net 3.5, SQL Azure, Windows Azure, Azure Blob/Table Storage, WCF etc.\n\n* Roles Played:\n\n◦ Overall Application Architect\n◦ Security and database architect\n\nPostal Return Solutions Client: USPS\nOct 2009 - Nov 2009\n\nVision\n- Electronic Recycling Return Solution to companies which would like to offer a recycling capability\non their web sites (like- HP, Dell etc.)\n- Merchandise Return Solutions for large e-Commerce customers (like- Landsend, Amazon etc)\n- Parcel Return Solutions to its business partners (like-UPS, FedEx etc.), who would like to leverage\nUSPS provided return solutions.\n\nApproach\n- Using Silverlight, create rich web-based user interface for calling Electronic Merchandise Return\nService Web Tools.\n- UI should facilitate printing and emailing EMR Labels.\n\nScope\n- Create shipping return labels using USPS's 'Electronic Merchandise Return Solution' for key\npartners (like Dell, WM) that participate in product recycling programs. It involves utilizing USPS\npublic APIs; expand functionality of existing application to create this shipping capability on\nWindows Azure Platform.\n\n\n\n* Technologies used: Silverlight, SQL Azure, Windows Azure, Azure Blob/Table Storage, WCF etc.\n\n* Roles Played:\n\n◦ Delivery Lead\n◦ Overall Application Architect\n\nWhat's new @ (SaaS, Multi-Tenant mobile application) Client: Starbucks, Coca Cola\n\nVision\n- Content Admin (Publisher) prepares and distributes the content to the employees of the\norganization.\n- Employees by using the mobile based application forward the content on social networking sites\nlike Twitter, Facebook etc.\n- Once the users within the network of the employees view/share the content forward, using\nanalytics capture the view/share count.\n- Branding done using dynamically loading CSSs.\n\nApproach\n- Using ASP.Net 3.5, create rich web-based user interface for mobile application.\n- Using ASP.Net 3.5, create rich web-based user interface for publishers.\n- Use Google, Facebook, BitLy and Twitter analytics to capture the view/share counts.\n\n* Technologies used: ASP.Net 3.5, SQL Azure, Windows Azure, Azure Blob/Table Storage, WCF,\nGoogle Analytics, Facebook APIs, Twitter APIS etc.\n\n* Roles Played:\n\n◦ Delivery Lead\n◦ Offshore Technical Architect\n\nGCP Tracker Client: Accenture Internal\nJune2008-Dec 2008\n\nSynopsis:\n\nGCP (Global career program) is a workflow based tool intended to track employees going on\nonsite assignment for more than 6 months. The GCP process starts when DU Lead nominates an\nemployee for onsite assignment and ends when employee comes back to home location. It caters\nto all GCP processes involved at home country as well as processes involved at Host country.\n\n* Technologies used: ASP.Net 2.0, SQL 2005, Windows Workflow Foundation\n* Roles Played:\n◦ Offshore Technical Architect, Dev Lead\n◦ Designing solution with the help of Senior Architects.\n◦ Designing integration interface\n◦ Was responsible for drawing/ implementing complex workflow using WF.\n◦ Leading a team responsible for implementing Change Requests.\n◦ Coordinating deployment procedures.\n\n\n\nAbacus DSM Client: Accenture Internal\nJune2006-June 2008\n\nSynopsis:\nAbacus Demand Supply Management was developed for Accenture HR/Scheduling/Sales teams\nby CIO (Chief Information Office)\n\n* Technologies used: ASP.Net, SQL 2005\n\n* Roles Played:\n◦ Technical Architect, Dev Lead\n◦ Leading a team responsible for implementing Change Requests.\n◦ Talking/calling/interviewing clients to gather requirements. Documenting it. Getting sign-off on\nit.\n◦ Coordinating Internal, User Acceptance, Integration and Performance Testing.\n◦ Leading Production Support team.\n◦ Build Manager.\n\nCFE Client: Bombay Stock Exchange\nOctober 2005 - June 2006\n\nSynopsis:\n\nCFE system (Common Front End System) is a trading platform for member (and traders of\nmembers) of BSE to trade in Multi exchange, Multi segments for trader authentication developed\nby CMC Ltd. It is designed to facilitate Members with centralized access rights management\n(has inbuilt Risk Management Server) and enable member to have a centralized Client-level Risk\nManagement at the Broker Office, wherein the risk for a client will be checked across all traders\nassociated with the broker for trading carried out in all segments and on any of the exchanges,\ncentrally at one location.\n\n* Technologies used: Windows Forms (using C#), MS SQL\n* Roles Played: Dev Lead, Technical Architect, Requirements gathering and analysis, Design,\nCMM-i documentation, writing BRS, FSD, and Program Specs etc.\n\nBond Clinician Client: Bond Technologies\nNovember 2004 - October 2005\n\nSynopsis:\n\nClinician is a complete healthcare solution for any clinic and hospital. It has got interface both\nfor Patients and doctors/physician. It has the capability of generating all sorts of lab reports etc\nand has a full-fledged Document Management System wherein clinics can maintain and scan all\ndocuments and reports in the scanned format. It also uses the third-party interface for Health\nInsurance.\n\n* Technologies used: ASP.Net web forms (using VB.Net), MS SQL, MSMQ\n* Roles Played: Developer, Database Design, Implementation and Testing", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [16, 58, "Designation"], [60, 77, "Location"], [100, 144, "Email Address"], [183, 210, "Designation"], [216, 234, "Designation"], [680, 713, "Designation"], [1050, 1058, "Years of Experience"], [992, 1011, "Designation"], [2213, 2217, "Location"], [2152, 2158, "Location"], [2259, 2268, "Companies worked at"], [2279, 2289, "Companies worked at"], [2338, 2368, "Degree"], [2377, 2381, "Graduation Year"], [2394, 2418, "Degree"], [2427, 2432, "Graduation Year"], [2446, 2536, "Degree"], [2545, 2549, "Graduation Year"], [2561, 2610, "Degree"], [3104, 3123, "Designation"], [3125, 3143, "Companies worked at"], [3147, 3164, "Location"], [3166, 3183, "Designation"], [3187, 3196, "Companies worked at"], [3200, 3208, "Location"], [3211, 3237, "Years of Experience"], [3806, 3825, "Designation"], [4681, 4704, "Degree"], [4708, 4724, "College Name"], [4991, 5033, "Degree"], [5035, 5077, "Degree"], [5079, 5119, "Degree"], [7252, 7271, "Years of Experience"], [7734, 7753, "Designation"]]}
{"id": 58, "text": "Asha Subbaiah\n(Microsoft Partner Readiness Operations Project Manager (APAC) -\nMicrosoft GPS\n\nBangalore, Karnataka - Email me on Indeed: indeed.com/r/Asha-Subbaiah/f7489ca1bec4570b\n\nWORK EXPERIENCE\n\n(Microsoft Partner Readiness Operations Project Manager (APAC)\n\nMicrosoft GPS -\n\nAugust 2014 to Present\n\nWorked closely with the APAC PB&amp;D teams / responsible for coordinating and tracking\nimpact, effectiveness and progress of our readiness activities with focus on driving efficiencies in\nvarious programs in Channel Readiness and Sure step and partner closely with Channel Readiness\nManager and (PCMM - SMB) from APAC SMS&amp;P.\n\nResponsibilities\n1. Tracking progress of Readiness Programs and clearly communicate the impact.\n2. Consolidate Outcomes and articulate the Partner attendance and Feedback.\n3. Track Partner Recruitment through the Sure step program; and work closely with the Tele-\nengine\n4. to ensure its alignment to Sure Step framework\n5. Interface between GPS (India, China, Korea) and APAC PB&amp;D teams for events delivered\nby GPS\n\nMicrosoft - India\nCompete Recruiter in the recently concluded pilot to recruit Google Partners across APAC.\n\nSenior operations coordinator\n\nIBM RESEARCH -  Bangalore, Karnataka -\n\nJune 2008 to 2013\n\nResponsibilities: -\n• Support for the India Technical Leaders Group (ITLG), the highest technical group in IBM India,\nincluding preparing agenda, collecting presentation materials and maintaining ITLG team room\netc.\n• Event management. I have supported the organization of International Conferences and\nWorkshops, including logistics, registration, awards, and general coordination.\n• Travel managements. The director travels on a monthly basis to all parts of world, in all six\ncontinents. I'm intimately familiar with managing complex travel itineraries to any part of the\nworld.\n\n• Setting up high-level conferences, workshops and management meetings, and special events.\nHandling visitors from around the world, and other high-level meetings.\n\nhttps://www.indeed.com/r/Asha-Subbaiah/f7489ca1bec4570b?isid=rex-download&ikw=download-top&co=IN\n\n\n• Calendar management. The director's schedule includes constant and daily interactions with\nthe top-most leaders of IBM, IBM's customers, Industry organizations, Academia, Government,\nand Media from around the world.\n\n• Material preparation, distribution, and archiving. I collect reading materials that are essential for\nany meeting with external or IBM internal meetings. I also maintain many presentation materials,\nas well as archive and distribute documents.\n\nAchievements\n• Joined the IRL-Bangalore team, and I was responsible for the overall operations support team\nat IRL-Bangalore.\n• Supervised the other support team members.\n• Responsible for coordinating the creation of the new premises for the IRL-Bangalore team. The\nnew premises in EGL is a 5000-square foot facility housing the research team, with conference\nrooms and a library / lab.\n• Responsible for space management including seat allocation and room allocation.\n• Asset management\n• Event Management including IRL club and visitors.\n• Responsible focal point for all interactions on location with GVI, AV, WBS, RESO, Procurement,\nSecurity, IS, ITS, STP/Customs/Logistics, BCP team, line management Business controls/ERO/BCP\nsupport at Bangalore\n\nEDUCATION\n\nBachelor's in Commerce\n\nNMKRV College, Bangalore University -  Bangalore, Karnataka", "meta": {}, "annotation_approver": null, "labels": [[0, 13, "Name"], [15, 76, "Designation"], [79, 92, "Companies worked at"], [94, 114, "Location"], [137, 180, "Email Address"], [200, 262, "Designation"], [263, 276, "Companies worked at"], [280, 302, "Years of Experience"], [1212, 1232, "Location"], [1165, 1194, "Designation"], [1196, 1208, "Companies worked at"], [1236, 1253, "Years of Experience"], [3333, 3355, "Degree"], [3357, 3370, "College Name"], [3372, 3392, "College Name"], [3396, 3416, "Location"], [1640, 1658, "Job Specific Skills"], [1579, 1588, "Job Specific Skills"]]}
{"id": 59, "text": "Divesh Singh\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Divesh-Singh/a76ddf6e110a74b8\n\nSeeking new challenges, looking to join a progressive organization that has need and offer\nopportunities for advancement. Seeking a position where I can serve with my utmost capabilities\nand where I can give full play to my creativity to excel within and to grow with institute.\n\nWORK EXPERIENCE\n\nFront Office Executive\n\nMicrosoft iGTSC -  Bangalore Urban, Karnataka -\n\nJune 2016 to July 2017\n\nHaving 11 months experience with Microsoft as a FOE. I have worked for Microsoft as a FOE for\n11 months from May 2016 to July 2017.\n\nAchievements:\n\n❖ Winner of inter school handwriting competition.\n❖ Runner up in inter college solo singing Competition.\n❖ Won Cricketer Of the year 2011\n❖ Selected for Voice Of Bangalore\n\nEDUCATION\n\nB.COM\n\nJAIN COLLEGE\n\n12TH\n\nINDIAN ACADEMY PU COLLEGE\n\nARMY PUBLIC SCHOOL\n\nADDITIONAL INFORMATION\n\nProfessional Skills:\n\n❖ Ability to work with team.\n❖ Good communication skills.\n❖ Good inter-personal skills.\n❖ Positive Attitude.\n❖ Hard &amp; Smart Working.\n❖ Building good relationship with people.\n❖ Ability to work under tough situation.\n❖ Self-Motivator.\n\nhttps://www.indeed.com/r/Divesh-Singh/a76ddf6e110a74b8?isid=rex-download&ikw=download-top&co=IN\n\n\n❖ Quick learner", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [13, 33, "Location"], [56, 98, "Email Address"], [397, 419, "Designation"], [421, 436, "Companies worked at"], [440, 466, "Location"], [470, 492, "Years of Experience"], [598, 624, "Years of Experience"], [826, 831, "Degree"], [833, 898, "College Name"], [501, 521, "Years of Experience"], [969, 973, "Soft Skills"], [977, 995, "Soft Skills"], [1011, 1032, "Soft Skills"], [1036, 1053, "Soft Skills"], [1094, 1111, "Soft Skills"], [1168, 1182, "Soft Skills"], [1285, 1298, "Soft Skills"]]}
{"id": 60, "text": "Ramesh chokkala\nTelangana - Email me on Indeed: indeed.com/r/Ramesh-chokkala/16d5fa56f8c19eb6\n\nWORK EXPERIENCE\n\nsoftware\n\nMicrosoft,Infosis, Google -\n\nMay 2018 to Present\n\nsoftware\n\nMicrosoft,Infosis, Google -\n\nMay 2018 to Present\n\nEDUCATION\n\nbtech\n\nTrinity engineering college\n\nhttps://www.indeed.com/r/Ramesh-chokkala/16d5fa56f8c19eb6?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 15, "Name"], [16, 26, "Location"], [48, 93, "Email Address"], [112, 147, "Companies worked at"], [151, 170, "Years of Experience"], [172, 207, "Companies worked at"], [211, 230, "Years of Experience"], [243, 248, "Degree"], [250, 277, "College Name"]]}
{"id": 61, "text": "Ganesh AlalaSundaram\nA Dev-Test Professional with 8+ Yrs of exp looking for SDET Lead/SDET/\nScrum Master/Program Manager roles.\n\nChennai, Tamil Nadu, Tamil Nadu - Email me on Indeed: indeed.com/r/Ganesh-AlalaSundaram/\ndd5b500021e61f65\n\nMy long-term career objective is to lead programs that solves complex problems, responsible\nfor product delivery and building products that positively impacts millions of consumers and\nenterprise users.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nConsultant SDET\n\nMicrosoft -\n\nJune 2013 to Present\n\nJune 2013 - Present)\nProduct Technologies: Cloud &amp; Mobile Migrations, DevOps for Test Strategies, Visual Studio,\nAzure.\n• Orchestrated projects as an Individual Contributor and Led teams across global engagements.\n• Contributed to 60% of the software component automation in the projects. Streamlined process\nand tools for Dev-Ops implementation that included training, metrics and reporting.\n• Led Manual and Automated Test Management to elevate the quality of Mobile (x Platform),\nCloud and IoT Applications. Designed, Developed and Maintained automation coverage for UI\nand API layer by handshaking Visual Studio with Open Source frameworks.\n• Migrated millions of customers to Azure Cloud through FastTrack program. Developed Tools that\nreduced manual efforts worth 40 hours for each process.\n\nProgrammer Analyst\n\nCognizant -\n\nMarch 2010 to May 2013\n\nBuilt WCF services for iOS and Android Applications for a PoC which helped business to grab more\nmobile projects.\n• Delivered quality of products to customers with the stipulated time. Stand-ups, defect triage,\nbrown bag sessions, bug-bash and retrospectives. Owned and managed weekly quality report.\n• Initiated transformation of web to mobile apps within project and assisted senior stakeholders\nof the company in analyzing the opportunity.\n\nProjects\nIoT and Smart City Projects\n• Formulated the use of VSO Dashboards for ease of status reporting which reduced manual\neffort of an hour per day.\n\nhttps://www.indeed.com/r/Ganesh-AlalaSundaram/dd5b500021e61f65?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Ganesh-AlalaSundaram/dd5b500021e61f65?isid=rex-download&ikw=download-top&co=IN\n\n\n• Leveraged developer background to automate UI and API components through C#, Xamarin\nUI in iOS and Android apps.\n• Led process improvements that standardized operations that included on-boarding/reporting\nand customer acceptance.\n\nPubSec Projects - Dubai/Columbia/New York\n• Coordinated between in-house and client teams and kept stakeholders informed of progress\nand milestones.\n• Supervised an avg. of 10-member software QA team in developing and implementing quality-\nassurance and quality-control methodologies to ensure compliance with QA standards.\n• Created tools that helped the development ecosystem by automating the long running manual\nprocess.\n• Brought a strong focus on \"voice of the customer\" into the software development function to\nensure product and customer success\nPortfolio\nMSDN Blog - https://blogs.msdn.microsoft.com/ganesh/\nGitHub Repo- https://github.com/ganesh-alalasundaram/\nPersonal Website: http://www.ganeshalalasundaram.com\n\nEDUCATION\n\nSoftware Product Management\n\nProduct School -  New York, NY\n\nBachelor of Computer Science in Computer Science Engineering\n\nAnna University\n\nSKILLS\n\nAutomation, Testing, Mobile Testing, SDET, Scrum Master\n\nCERTIFICATIONS/LICENSES\n\nCertified ScrumMaster (CSM)\n\nPUBLICATIONS\n\nMSDN\n\nhttps://blogs.msdn.microsoft.com/ganesh\n\nGIT\n\nhttps://github.com/ganesh-alalasundaram\n\nhttps://blogs.msdn.microsoft.com/ganesh\nhttps://github.com/ganesh-alalasundaram", "meta": {}, "annotation_approver": null, "labels": [[0, 20, "Name"], [21, 44, "Designation"], [76, 90, "Designation"], [92, 104, "Designation"], [105, 120, "Designation"], [129, 136, "Location"], [138, 148, "Location"], [150, 160, "Location"], [183, 234, "Email Address"], [488, 503, "Designation"], [505, 515, "Companies worked at"], [518, 538, "Years of Experience"], [540, 559, "Years of Experience"], [1342, 1360, "Designation"], [1362, 1371, "Companies worked at"], [1375, 1397, "Years of Experience"], [2458, 2463, "Location"], [2464, 2472, "Location"], [2473, 2481, "Location"], [3177, 3220, "College Name"], [3224, 3236, "Location"], [3238, 3266, "Degree"], [3270, 3315, "College Name"], [3407, 3428, "Degree"], [3430, 3433, "Degree"], [50, 63, "Years of Experience"], [595, 612, "Job Specific Skills"], [614, 620, "Job Specific Skills"], [625, 640, "Job Specific Skills"], [641, 655, "Tech Tools"], [657, 662, "Tech Tools"], [1027, 1053, "Job Specific Skills"], [1114, 1116, "Job Specific Skills"], [1146, 1159, "Tech Tools"], [1165, 1187, "Job Specific Skills"], [1225, 1236, "Tech Tools"], [2243, 2262, "Job Specific Skills"], [2282, 2284, "Tech Tools"], [3325, 3335, "Job Specific Skills"], [3337, 3344, "Job Specific Skills"], [3346, 3360, "Job Specific Skills"], [3362, 3366, "Job Specific Skills"], [3368, 3380, "Degree"]]}
{"id": 62, "text": "Srinu Naik Ramavath\nanymore job\n\nSerilingampalle, Andhra Pradesh - Email me on Indeed: indeed.com/r/Srinu-Naik-\nRamavath/2d9f28ccfa115f79\n\nWilling to relocate to: Gachibowli, Telangana - hyderbad, Telangana\n\nWORK EXPERIENCE\n\nsecurity officer\n\nMicrosoft in G4S\n\nCommunication and secure to the employees and no any other person's not allowed to the\ncompany\n\nSecurity Officer\n\nMicrosoft of G4S -  Hyderabad, Telangana -\n\nApril 2010 to May 2018\n\nsalaries is limited\n\nEDUCATION\n\nBsc(mpc) in Bachelor of science\n\nAcharya Nagarjuna University -  Hyderabad, Telangana\n\nMarch 2006 to March 2009\n\nSKILLS\n\nTelugu Hindi movie and English, cricket valiball\n\nADDITIONAL INFORMATION\n\ncomputer and firesafety\n\nhttps://www.indeed.com/r/Srinu-Naik-Ramavath/2d9f28ccfa115f79?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Srinu-Naik-Ramavath/2d9f28ccfa115f79?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 19, "Name"], [33, 64, "Location"], [87, 137, "Email Address"], [163, 184, "Location"], [187, 206, "Location"], [225, 241, "Designation"], [357, 373, "Designation"], [243, 259, "Companies worked at"], [375, 391, "Companies worked at"], [395, 415, "Location"], [419, 441, "Years of Experience"], [475, 506, "Degree"], [508, 536, "College Name"], [540, 560, "Location"], [562, 586, "Years of Experience"], [596, 602, "Soft Skills"], [603, 608, "Soft Skills"], [619, 626, "Soft Skills"], [683, 693, "Soft Skills"]]}
{"id": 63, "text": "Puneet Bhandari\nSAP SD lead - Microsoft IT\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Puneet-Bhandari/c9002fa44d6760bd\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSAP SD lead\n\nMicrosoft IT -\n\nAugust 2010 to Present\n\nTeam Size: 8 Duration: Seven months\n\nScope:\n* Enhancement of Mexico invoicing process as per the current regulations\n* Requirement gathering from third party and client on new process\n* Responsible for implementing the changes in system\n\nArea of Exposure:\n* Understand the AS-IS process and develop to- Be design document to meet the business and\nGovernment requirement\n* Requirement gathering for all SD process for client\n* Developed solution blueprint and Process Design Documents for OTC 3-way and 1-way invoice\nprocesses\n* Interacting with third party to gather requirements from their end\n* Creating functional specification and Gap analysis document for different country\nimplementation with client\n* Design test scripts for functional unit testing (FUT), Integration system testing (IST) and User\nAcceptance Test (UAT)\n\nPhase: Support Phase: Implementation \\ Enhancement\nProject: JCI\nRole: SAP SD lead\nTeam Size: 15 Duration: Twelve months\n\nScope:\n* Communication with client leadership on various issues and efficiency improvement\n* Ticket handling for OTC track as L2 support lead\n* Responsible for change request across OTC and all business areas\n\nArea of Exposure:\n* End to end order to cash cycles issues\n* Interface related issues with exposure to IDOCs\n* Change request handling and process improvement\n\nhttps://www.indeed.com/r/Puneet-Bhandari/c9002fa44d6760bd?isid=rex-download&ikw=download-top&co=IN\n\n\n* SPOC for client from offshore for communication, reporting and continuous improvement\nactivities\n* Design test scripts for functional unit testing (FUT), Integration system testing (IST) and User\nAcceptance Test (UAT) for all changes for process and system improvements\nAchievements:\n* Spot Awards for exceptional contribution to project work\n* Award and appreciation from client at Global level for successful transition of new geographies\nto support global template\n\nPhase: Implementation\nProject: Adient\nRole: SAP SD lead\nTeam Size: 5 Duration: Five months\n\nScope:\n* Lead Optical archiving of all future, live and archived invoices for audit purpose\n* Harmonize invoice archiving process across all plants and SAP instances\n* Requirement and data gathering from all plants on legal aspects of invoice form structures and\ndata of last 10 years\n\nArea of Exposure:\n* Developed solution blueprint and Process Design Documents for OTC 3-way and 1-way invoice\nprocesses\n* Requirement gathering from all the plants legal department on the aspect of form structure\n* Data gathering of all changes across globe for data (customer, vendor, organizational) relative\nto the billing process in system\n* Creating functional specification and Gap analysis document for different country\nimplementation with client\n* Design test scripts for unit testing (UT), Integration system testing (IST) and User Acceptance\nTest (UAT)\n\nPhase: Implementation\nProject: JCI\nRole: Cutover Manager\nTeam Size: 15 Duration: Nine months\n\nScope:\n* Legal entity Separation of asset share and shared sale plants from the core JCI group\n* Co-coordinating with multiple vendors on behalf of client for master data migration and IT\nactivities\nArea of Exposure:\n* Defining scope of activities for the shared sale plants migration to new legal entity\n* Co-ordination with OTC, PTP, PTD and RTR streams for successful implementation of the\nobjectives\n* Analyzing issues related to intercompany transactions occurred for asset share plants\n* Gathering client requirement on basis of legacy system and current need\n* Creating functional specification and Gap analysis documents\n* Reporting to IT head of the organization on the progress of the planned activities\n* Report analysis and finding functional solutions for the issues\n\n\n\n* Hyper care support for multi SAP instance layout\n\nAchievements:\n* Appreciations from the client on smooth and successful execution of the cutover involving\nmultiple stakeholders\n\nPhase: Implementation and Roll-out\nProject: Atlas CopCo\nRole: Master Data Lead and SD team member\nTeam Size: 11 Duration: Thirty-three months\n\nScope:\n* Sales order management\n* Equipment creation in Order to cash cycle\n* Lead for master data migration\n* Conducting workshops along with client IT team for business user\n\nArea of Exposure:\n* Conducting blue print workshops with client for requirement gathering in 6 countries\n* Developed solution blueprint and Process Design Documents for OTC\n* Roll out of the template solution to UK-NORDICS\n* Defining data flow for the sales order-billing document in system\n* Order to cash cycle activity management for data creation in ECC\n* Creating functional specification and Gap analysis document for different country\nimplementation with client\n* Implementation and Configuration of different processes as per the client requirement in the\narea of SAP SD and CRM sales-ECC integration\n* Reporting - Reconciliation, pre validation, post validation\n* Involved as lead in cutover, go-live, hyper care phases of project for five countries in Europe\n\nAchievements:\n* Awarded for Creating landscape for support phase along with top management team of the\nproject\n* At onsite as equipment lead and awarded as valuable member by the client\n\nPhase: Implementation\nProject: Agri Business Client\nRole: SD Team member\nTeam Size: 25 Duration: Ten months\n\nScope:\n* Implementing end to end SAP SD scenario for MNC client\n* Creation of functional specification documents for various processes\nArea of Exposure:\n* Defining enterprise structure, shipping conditions, pricing procedure, etc.\n* Assignment of structure as per business need\n* Creation of BPP and functional specification documents\n* Master data Creation\n* Worked on DUET (SharePoint and SAP initiative)\n\n\n\n* Handling team and driving it to achieve deliverables\n\nAchievements:\n* Published various BOK (reusable artifacts) in Infosys Repository\n* Successfully completed on job internship on Sustainability study of Infosys Clients - Telstra,\nCummins and Vodafone.\n\nPhase: Implementation\nProject: COE\nRole: SD Team member\nTeam Size: 6 Duration: Three months\n\nScope:\n* Configuring sales order management for SAP SD module for client\n* Testing preconfigured solution for internal team\n\nArea of Exposure:\n* Implementation of sales order process with respect to specific SD (sales and Distribution)\nprocesses\n* Testing for SD module processes involving various pricing methodologies\n* Handling team and driving it to achieve deliverables\nAchievements:\n* Cleared domain certification in Retail category\n* Successfully completed Harvard Manage Mentor Certification in Customer Focus and Innovation\nImplementation by Harvard Business Publishing\n\nPhase: Support Client: Microsoft IT\nTeam Size: 15 Duration: Sixteen months\n\nScope:\n* Supporting complete Order to cash cycle for Microsoft Business\n* Analyzing, resolving and implementing issues or tickets\n\nArea of Exposure:\n* Pricing unification management and analysis (stand-alone system for pricing)\n* Handling of Master Data (Customer data, customer information data, condition records, partner\nprofile maintenance)\n* Resolving errors related to configuration issues, extension of sales organization and material,\norder, delivery and invoice errors.\n* Working on LSMW for mass data update in the system\n* Working on monthly rotational activities like IDOCs failure, EDI error notification, short dumps.\n\nAchievements:\n* Resolved maximum numbers of issues among the peer group members\n* Created maximum number of knowledge base articles for the project\n\nTitle: Training Duration: Two months\nScope: Understanding of ES Methodologies, Project management and SAP SD\n\n\n\nArea of Exposure:\n* Understanding fundamentals of Management with respect to IT sector\n* Learning SAP SD (Sales and distribution domain)\n* RFP creation\n\nAchievements:\n* Successfully cleared P100 and P200 certifications\n* Successfully completed RFP and POST\n* Successfully cleared all exams with score more than 4 out of 5\n\nEDUCATION\n\nMBA in Marketing\n\nIIT Roorkee -  Roorkee, Uttarakhand\n\n2008 to 2010\n\nBachelor of Engineering in Electronics and Communication\n\nShri Vaishnav Institute of Technology and Science, RGPV University -  Indore, Madhya Pradesh\n\n2004 to 2008\n\nCBSE\n\nSt. Paul H.S. School -  Indore, Madhya Pradesh\n\n2002 to 2003\n\nHigher Secondary Certificate\n\nShanti Nagar High School, Maharashtra board -  Mumbai, Maharashtra\n\n1999 to 2000\n\nSKILLS\n\nSap Sd (7 years)", "meta": {}, "annotation_approver": null, "labels": [[0, 15, "Name"], [16, 27, "Designation"], [30, 42, "Companies worked at"], [44, 61, "Location"], [84, 129, "Email Address"], [179, 190, "Designation"], [192, 204, "Companies worked at"], [208, 230, "Years of Experience"], [1130, 1141, "Designation"], [2167, 2178, "Designation"], [3107, 3122, "Designation"], [4186, 4209, "Designation"], [5459, 5473, "Designation"], [8154, 8170, "Degree"], [8172, 8183, "College Name"], [8187, 8207, "Location"], [8217, 8222, "Graduation Year"], [8223, 8246, "Degree"], [8250, 8330, "College Name"], [8332, 8347, "College Name"], [8359, 8373, "Location"], [8383, 8388, "Graduation Year"], [351, 372, "Job Specific Skills"], [1190, 1203, "Soft Skills"], [1249, 1271, "Job Specific Skills"], [1753, 1772, "Job Specific Skills"], [1778, 1806, "Job Specific Skills"], [1807, 1840, "Job Specific Skills"], [1845, 1871, "Job Specific Skills"], [2623, 2644, "Job Specific Skills"], [2982, 2999, "Job Specific Skills"], [3001, 3034, "Job Specific Skills"], [3038, 3064, "Job Specific Skills"], [4584, 4608, "Job Specific Skills"], [5055, 5064, "Job Specific Skills"], [5847, 5867, "Job Specific Skills"], [6734, 6817, "Degree"], [7787, 7805, "Job Specific Skills"], [8577, 8580, "Tech Tools"]]}
{"id": 64, "text": "Aarti Pimplay\nOperations Center Shift Manager (OCSM)\n\n- Email me on Indeed: indeed.com/r/Aarti-Pimplay/778c7a91033a71ca\n\nTo work with an organization where I can contribute to the growth of the\norganization through my skill &amp; knowledge for mutual benefit and to\nlearn and excel in highly competitive environment\n\nWORK EXPERIENCE\n\nOperations Center Shift Manager (OCSM)\n\nMicrosoft India -\n\nAugust 2012 to January 2016\n\n• Handling escalations, notifications, task organization, distribution of work, site status enquiries\n• Monitoring the Incidents handled by the team in real time\n• Supervising the reporting of Incidents to respective stake holders\n• Ensuring proper workflow of Incident and major incident processes\nare followed\n• Escalate events that have a potential MS impacts to Security Analyst or as directed by the\nEscalation Matrix\n• Initiate problem tickets based on the recurring incidents identified\n• Reviewing the problem records to ensure timely closure of issues\n• Responsible for publishing monthly SLA reports\n• Providing OJT, concurrent training\n• Global news monitoring (Monitor Global activities on a continual\nbasis)\n• Responsible for administrative duties like reviewing performance\nMetrics, managing breaks/lunch (All stations), Shift Changeover\nProcess and adherence, Policy Reviews and Updates, Supply and equipment requests, OCSM\nPass-down Log, Inventory Control,\nEmployee Recognition Requests, Disciplinary Actions, Annual\nEvaluations, Mentoring and Counselling\n• Maintain and share updates on emergency procedures\n• Develop and/or update all policies and procedures\n\nCommunication Supervisor\n\nMicrosoft India -\n\nFebruary 2011 to July 2012\n\n• Managing all incidents based on the priorities\n• Publishing executive business notifications during outages\n• Responsible for all email communications in GSOC Asia\n• Global news monitoring\n• Handling and initiating Major Incident conference calls and assisting the respective teams\n\nhttps://www.indeed.com/r/Aarti-Pimplay/778c7a91033a71ca?isid=rex-download&ikw=download-top&co=IN\n\n\n• Initiating bridge calls for P1 &amp; P2 Issues\n• Providing overall analysis of incidents by performing root cause\nanalysis and quality checks\n• Provide supervision to assigned staff\n• Maintain an in-depth knowledge of emergency procedures, and adhere to same\n\nService Desk Analyst\n\nSITEL -\n\nSeptember 2009 to January 2011\n\n• Provided technical support to end users\n• Worked as part of escalation team to identify resolution and provide\ninputs to improve/create KB articles\n• Responsible for providing First Call Resolution\n• Providing Technical assistance to customers based on the priorities\n• Resolving Issues related to networking\n• Assist in configuring LAN, Modular Routers and TCP/IP\n• Troubleshooting Hardware and System performance issues\n• Working with Users to identify and rectify the issues pertaining to\nInternet and related services\n• Worked with different Antivirus Softwares - Installation and troubleshooting\n• Team SPOC for Quality and Compliance improvements\n\nADDITIONAL INFORMATION\n\nSKILLS • Ability to build teams and motivate them towards team goals\n• Effective Communication skills\n• Able to handle and overcome objections\n• Ability to work effectively in a team environment\n• Ability to adapt to the changes in organization along with successful\nimplementation of the change in the system", "meta": {}, "annotation_approver": null, "labels": [[0, 13, "Name"], [14, 52, "Designation"], [76, 119, "Email Address"], [334, 372, "Designation"], [374, 389, "Companies worked at"], [393, 420, "Years of Experience"], [1600, 1624, "Designation"], [1626, 1641, "Companies worked at"], [1645, 1671, "Years of Experience"], [1198, 1217, "Job Specific Skills"], [1257, 1273, "Job Specific Skills"], [1274, 1295, "Job Specific Skills"], [1297, 1323, "Job Specific Skills"], [1376, 1393, "Job Specific Skills"], [1325, 1354, "Job Specific Skills"], [1426, 1446, "Job Specific Skills"], [1448, 1466, "Job Specific Skills"], [2319, 2339, "Designation"], [2341, 2346, "Companies worked at"], [2350, 2380, "Years of Experience"], [3082, 3093, "Soft Skills"], [3098, 3106, "Soft Skills"], [3133, 3156, "Soft Skills"]]}
{"id": 65, "text": "Bangalore Tavarekere\nVolunteer Contestant, Yappon\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Bangalore-\nTavarekere/8fc92a48cbe9a47c\n\nWORK EXPERIENCE\n\nVolunteer Contestant, Yappon\n\nMicrosoft -\n\n2017 to 2017\n\nAnalyst Programmer\n\nTavarekere -  Bengaluru, Karnataka -\n\n2015 to 2015\n\n560029\n\nEDUCATION\n\nB.Tech\n\nChrist University Faculty of Engineering -  Bengaluru, Karnataka\n\n2018\n\nClass XII\n\nDelhi Public School -  Kanpur, Uttar Pradesh\n\n2013\n\nCBSE\n\nDelhi Public School -  Kanpur, Uttar Pradesh\n\n2011\n\nhttps://www.indeed.com/r/Bangalore-Tavarekere/8fc92a48cbe9a47c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Bangalore-Tavarekere/8fc92a48cbe9a47c?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 20, "Location"], [94, 145, "Email Address"], [21, 41, "Designation"], [164, 184, "Designation"], [186, 203, "Companies worked at"], [51, 71, "Location"], [43, 49, "Companies worked at"], [207, 219, "Years of Experience"], [221, 239, "Designation"], [241, 275, "Location"], [279, 291, "Years of Experience"], [312, 318, "Degree"], [320, 360, "College Name"], [364, 384, "Location"], [386, 391, "Graduation Year"], [484, 505, "Location"], [426, 447, "Location"]]}
{"id": 66, "text": "Avani Priya\n- Email me on Indeed: indeed.com/r/Avani-Priya/fe6b4c5516207abe\n\nWORK EXPERIENCE\n\nJavaTally Microsoft -\n\n2013 to 2015\n\noffice\n\nADDITIONAL INFORMATION\nI am a girl with simple living and high thinking .I need to work to prove myself. I am to be\nindependent I don't want to depend on someone.\n\nEDUCATION\n\nbr day public school -  Begusarai, Bihar\n\nhttps://www.indeed.com/r/Avani-Priya/fe6b4c5516207abe?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 11, "Name"], [34, 75, "Email Address"], [94, 103, "Designation"], [104, 114, "Companies worked at"], [117, 129, "Years of Experience"], [338, 354, "Location"]]}
{"id": 67, "text": "Sanand Pal\nSQL and MSBI Developer with experience in Azure SQL and Data Lake\nstore.\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Sanand-Pal/5c99c42c3400737c\n\nI intend to establish myself as Software Engineer / architect with an integrated business solution\nprovider through a long time commitment, contributing to the company's growth and in turn\nensuring personal growth within the organization. I believe that my technical, functional and\ncommunication skills will enable me in facing the challenging career ahead.\n\nWilling to relocate to: Kolkata, West Bengal - hyderbad, Telangana\n\nWORK EXPERIENCE\n\nAssistant Consultant\n\nTCS\n\n• Expertise in SQL Server(2008 R2, 2012, 2014) development, Microsoft BI (SSIS)\n• Experience with Microsoft BI (SSAS, SSRS), ASP.NET, VSTO, C#.\n• Experience in all the phases of Software Development Life Cycle (SDLC).\n• Experience in Business Requirements Analysis, meeting customer expectations.\n• Have had the opportunity to handle and work in multiple projects at a time.\n• Experience in working both independently and in a team-oriented, collaborative environment.\n• Excellent written and verbal communication skills, good analytical and problem solving skills.\n\nSQL and SSIS developer/Sustain resource\n\nMICROSOFT -  Hyderabad, Telangana -\n\nAugust 2011 to June 2016\n\nProject\nUS EPG Forecast Workbook (FWB) application is designed to support the US Enterprise Partner\nGroup (US EPG) in producing and maintaining the monthly US sales forecast. It is a transactional\ndatabase combined with Microsoft Office Excel functionality that enables end users to interact\nwith the USNAForecast database. Typically, an ATU manager will connect to the corporate network\nand download data from the forecast database, which creates an offline forecast workbook. This\ndata can then be accessed offline, modelled, and changes to certain data fields are subsequently\nuploaded back to the forecast database through a CorpNet connection. These changes are then\nstored in the online database and subsequently loaded back into upstream systems.\n\nResponsibilities\n• Involved in the Technical discussions/Sessions and efforts estimations and reviews.\n• Involved in analysis of the Bugs/ Issues/defects/CRs.\n• Involved in change requests on VSTO Excel application.\n• Involved in design of database, tables and stored procedures.\n• Developed SQL Server Integration Services packages for ETL process.\n• Unit testing and bug fixing of the code.\n\nhttps://www.indeed.com/r/Sanand-Pal/5c99c42c3400737c?isid=rex-download&ikw=download-top&co=IN\n\n\n• Actively solved the issues that raised during the integration cycle\n• Performed build verification and Smoke tested all the defects raised before giving a delivery.\n• Prepared and updated FS, TS and deployment guides.\n• Provided knowledge sharing to Users\n• Provided post implementation support\n• Promptly check in the final Code in VSTF.\n\nHyderabad, India\n\nEDUCATION\n\nBachelor of Technology in Branch\n\nEast Point College of Engg. & Tech. -  Bengaluru, Karnataka\n\nJune 2006 to July 2010\n\nSKILLS\n\nSql Server, Ssis, T-SQL, ETL, SSRS", "meta": {}, "annotation_approver": null, "labels": [[0, 10, "Name"], [11, 82, "Designation"], [85, 105, "Location"], [128, 168, "Email Address"], [202, 231, "Designation"], [554, 574, "Location"], [577, 596, "Location"], [615, 640, "Designation"], [1209, 1248, "Designation"], [1250, 1260, "Companies worked at"], [1263, 1283, "Location"], [1287, 1311, "Years of Experience"], [2900, 2916, "Location"], [2929, 2951, "Degree"], [2955, 2998, "College Name"], [3002, 3022, "Location"], [3042, 3047, "Graduation Year"], [453, 466, "Soft Skills"], [427, 436, "Soft Skills"], [438, 448, "Soft Skills"], [657, 688, "Tech Tools"], [689, 700, "Job Specific Skills"], [702, 721, "Tech Tools"], [740, 765, "Tech Tools"], [767, 774, "Tech Tools"], [776, 780, "Tech Tools"], [782, 784, "Tech Tools"], [820, 858, "Job Specific Skills"], [876, 906, "Job Specific Skills"], [1123, 1130, "Soft Skills"], [1135, 1141, "Soft Skills"], [1142, 1155, "Soft Skills"], [1164, 1179, "Soft Skills"], [1184, 1199, "Soft Skills"], [1533, 1555, "Tech Tools"], [2185, 2206, "Job Specific Skills"], [2359, 2370, "Tech Tools"], [2420, 2432, "Job Specific Skills"], [2437, 2447, "Job Specific Skills"], [2663, 2675, "Job Specific Skills"], [2827, 2854, "Job Specific Skills"], [3056, 3066, "Tech Tools"], [3068, 3072, "Tech Tools"], [3074, 3079, "Tech Tools"], [3081, 3084, "Tech Tools"], [3086, 3090, "Tech Tools"]]}
{"id": 68, "text": "Partho Sarathi Mitra\nKolkata, West Bengal - Email me on Indeed: indeed.com/r/Partho-Sarathi-\nMitra/683dfd08d0246836\n\nWORK EXPERIENCE\n\nSenior Sales Executive\n\nNokia & Microsoft mobile -\n\nMarch 2005 to April 2016\n\nEDUCATION\n\nB.com\n\nSurendranath college Barrackpore\n\nAWARDS\n\nBest Promoter\n\nhttps://www.indeed.com/r/Partho-Sarathi-Mitra/683dfd08d0246836?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Partho-Sarathi-Mitra/683dfd08d0246836?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 20, "Name"], [21, 41, "Location"], [64, 115, "Email Address"], [134, 182, "Designation"], [186, 210, "Years of Experience"], [223, 228, "Degree"], [230, 262, "College Name"]]}
{"id": 69, "text": "Pranay Sathu\nSoftware Test Automation Engineer\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Pranay-Sathu/ef2fc90d9ec5dde7\n\n• Over 3.6 years of experience in IT industry developing automation code for various clients (Web\nand Mobile applications)\n• Extensive Work Experience in test automation using tools Selenium Webdriver, Xamarin.UITest\nand exposure to all phases of SDLC.\n• Involved in development of automation framework using C#.\n• Experience in Test management tools like Quality center, JIRA, VSTS.\n• Proficiency in C# programming with development of Automation scripts.\n• Strong Debugging skills using Visual Studio.\n• Involved in story grooming sessions, backlog prioritization and product road map discussions\n• Experience in working closely with the product management team (Product owner, BA and UX)\nfor requirement discussion.\n• Good experience in reviewing requirements and identify ambiguity in requirements.\n• Preparing test cases for the system covering User stories, reviewing them with the developers\nand finalizing the test cases.\n• Strong experience in facilitating UAT scenarios/sessions. Provide sprint end product demo to\ncross functional teams\n• Support Project manager in generating test coverage reports, defect metrics.\n• Good Work experience in performance testing using HP load runner (True client protocol)\n• Basic knowledge in authoring user stories in BDD format.\n• Basic knowledge in automating the applications using tool Protractor\n• Quick learner, Self-motivated and problem solving skills.\n\nWilling to relocate to: Pune, Maharashtra - hyderbad, Telangana - Bangalore Urban, Karnataka\n\nWORK EXPERIENCE\n\nConsultant\n\nNeudesic (Vendor @ Microsoft)\n\nWorking as Test Automation Engineer.\n\nSoftware Test Automation Engineer\n\nEPAM -\n\nDecember 2014 to October 2017\n\nWorked as Test Automation Engineer.\n\nEDUCATION\n\nBachelor (B Tech) in Name of the Education Establishment\n\nSR ENGINEERING COLLEGE (SREC)\n\nhttps://www.indeed.com/r/Pranay-Sathu/ef2fc90d9ec5dde7?isid=rex-download&ikw=download-top&co=IN\n\n\nSKILLS\n\nC# (Less than 1 year), Database (Less than 1 year), Java (Less than 1 year), JIRA (Less than 1\nyear), load runner (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nKey Skills:\n\n• Programming Languages: C#, Java\n• Automation Testing: Selenium Webdriver, Xamarin.UITest, Protractor, Speflow\n• Performance Testing: HP Load Runner (True Client protocol)\n• Software Testing: Test Scripts, Test Case Design, Test Summary\n• Platform: Windows.\n• Database: SQL Server 2012, Oracle\n• Management tools: JIRA, HP Quality center; vsts.", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [13, 46, "Designation"], [48, 68, "Location"], [91, 133, "Email Address"], [1566, 1583, "Location"], [1586, 1605, "Location"], [1608, 1634, "Location"], [1653, 1673, "Designation"], [1734, 1767, "Designation"], [1707, 1731, "Designation"], [1769, 1774, "Companies worked at"], [1777, 1806, "Years of Experience"], [1818, 1842, "Designation"], [1856, 1873, "Degree"], [1877, 1943, "College Name"], [142, 165, "Years of Experience"], [181, 207, "Job Specific Skills"], [229, 256, "Job Specific Skills"], [289, 304, "Job Specific Skills"], [317, 335, "Tech Tools"], [337, 351, "Tech Tools"], [356, 364, "Tech Tools"], [382, 386, "Job Specific Skills"], [402, 437, "Job Specific Skills"], [444, 446, "Tech Tools"], [464, 479, "Job Specific Skills"], [491, 505, "Tech Tools"], [507, 511, "Tech Tools"], [513, 517, "Tech Tools"], [536, 538, "Tech Tools"], [571, 589, "Job Specific Skills"], [600, 616, "Job Specific Skills"], [623, 636, "Tech Tools"], [949, 959, "Job Specific Skills"], [984, 996, "Job Specific Skills"], [1100, 1122, "Job Specific Skills"], [1287, 1306, "Job Specific Skills"], [1483, 1496, "Soft Skills"], [1498, 1512, "Soft Skills"], [1517, 1532, "Soft Skills"], [2051, 2053, "Tech Tools"], [2074, 2082, "Job Specific Skills"], [2103, 2107, "Tech Tools"], [2128, 2132, "Tech Tools"], [2154, 2164, "Tech Tools"], [2247, 2249, "Tech Tools"], [2251, 2255, "Tech Tools"], [2258, 2276, "Job Specific Skills"], [2278, 2296, "Tech Tools"], [2298, 2312, "Tech Tools"], [2314, 2324, "Tech Tools"], [2326, 2333, "Tech Tools"], [2336, 2355, "Job Specific Skills"], [2357, 2371, "Tech Tools"], [2397, 2413, "Job Specific Skills"], [2415, 2427, "Job Specific Skills"], [2429, 2445, "Job Specific Skills"], [2447, 2459, "Job Specific Skills"], [2472, 2479, "Tech Tools"], [2483, 2491, "Job Specific Skills"], [2493, 2508, "Tech Tools"], [2510, 2516, "Tech Tools"], [2537, 2541, "Tech Tools"], [2543, 2560, "Tech Tools"], [2562, 2566, "Tech Tools"]]}
{"id": 70, "text": "Tanmoy Maity\nKolkata, West Bengal - Email me on Indeed: indeed.com/r/Tanmoy-Maity/145eb1ed39df317c\n\nWORK EXPERIENCE\n\nHVAC Technician\n\nInfosys and microsoft -\n\nNovember 2015 to Present\n\nHVAC Technician\n\nInfosys and microsoft -\n\nNovember 2015 to Present\n\nEDUCATION\n\nDiploma in Mrac\n\nGtti -  Kolkata, West Bengal\n\nSKILLS\n\nHvac tech (3 years)\n\nAWARDS\n\nbest team leader of the year\n\nFebruary 2017\n\nhttps://www.indeed.com/r/Tanmoy-Maity/145eb1ed39df317c?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [13, 33, "Location"], [56, 98, "Email Address"], [122, 132, "Designation"], [134, 155, "Companies worked at"], [159, 183, "Years of Experience"], [185, 200, "Designation"], [202, 223, "Companies worked at"], [227, 251, "Years of Experience"], [264, 271, "Degree"], [275, 285, "College Name"], [289, 309, "Location"], [319, 328, "Tech Tools"]]}
{"id": 71, "text": "Aanirudh Razdan\nTechnical Support Executive - Teleperformance\n\nJaipur, Rajasthan - Email me on Indeed: indeed.com/r/Aanirudh-Razdan/efbf36cc74cec0e5\n\nTo seek an organisation where my skills find ample opportunities for up-gradation of my\nknowledge and growth of my career and where I can prove myself.\n\nWORK EXPERIENCE\n\nTechnical Support Executive\n\nTeleperformance -  Jaipur, Rajasthan -\n\nJune 2017 to Present\n\n* Handling query related to Norton products\n* Resolving query related to the Operating system (Windwos XP/Vista/7/8/8.1/10)\n\nB2X Process\n\nNokia and Microsoft -\n\nSeptember 2016 to May 2017\n\nhandsets\n* Resolving problems related to Microsoft account\n* Awarded as best Nesting Executive of the month\n\nEDUCATION\n\nB. Tech\n\nJaipur Engineering College and Research Center -  Jaipur, Rajasthan\n\n2015\n\ncommunication and presentation skills\n\n(WAC) in inter Air Force School football championship\n\n2012\n\nAir Force School Jammu -  Jammu, Jammu and Kashmir\n\nAir Foce School Jammu -  Jammu, Jammu and Kashmir\n\nhttps://www.indeed.com/r/Aanirudh-Razdan/efbf36cc74cec0e5?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 15, "Name"], [16, 43, "Designation"], [46, 61, "Companies worked at"], [63, 80, "Location"], [103, 148, "Email Address"], [320, 347, "Designation"], [349, 364, "Companies worked at"], [368, 385, "Location"], [389, 409, "Years of Experience"], [720, 727, "Degree"], [729, 775, "College Name"], [779, 796, "Location"], [798, 802, "Graduation Year"], [488, 504, "Job Specific Skills"], [506, 533, "Tech Tools"], [804, 817, "Soft Skills"], [822, 834, "Soft Skills"]]}
{"id": 72, "text": "Shiksha Bhatnagar\nchnadigarh - Email me on Indeed: indeed.com/r/Shiksha-Bhatnagar/70e68b28225ca499\n\nWORK EXPERIENCE\n\nonline job in home\n\nMicrosoft and copy past -  Chandigarh, Chandigarh -\n\nAugust 2016 to July 2017\n\ni need a online job so that i can attend \nmy regular college and i want to earn money that's it a part time online job so that i can do it\non my phone or laptop\n\nEDUCATION\n\npass 12 in medical\n\nchandigarh university -  Chandigarh, Chandigarh\n\nSeptember 2016 to August 2019\n\nSKILLS\n\nMicrosoft office and java (Less than 1 year)\n\nADDITIONAL INFORMATION\n\ni want to earn money by my hard work or smart work p\n\nhttps://www.indeed.com/r/Shiksha-Bhatnagar/70e68b28225ca499?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[18, 28, "Location"], [0, 17, "Name"], [51, 98, "Email Address"], [164, 186, "Location"], [190, 214, "Years of Experience"], [400, 430, "College Name"], [434, 456, "Location"], [483, 488, "Graduation Year"], [497, 513, "Tech Tools"], [518, 522, "Tech Tools"]]}
{"id": 73, "text": "Chhaya Prabhale\nKharadi, Pune, 411014, IN - Email me on Indeed: indeed.com/r/Chhaya-\nPrabhale/99700a3a95e3ccd7\n\nThis application is Used to information of customer and agency and to\nCheck the reorder level of cylinder.\nTitle: \"Gas Agency System\"\nPlatform: MS Access\n\nThis application is used to store daily information of Cylinder,\nCustomer. Printing Customer information, Employee information,\nSelling information and finding particular customer records etc.\n\nWORK EXPERIENCE\n\nDBA Client\n\nPune, Maharashtra -\n\nJune 2006 to Present\n\nExperiance Summary:\n\n➢ Creation of tools (using Perl), shell scripting and PL-SQL for testing the quality of Data as per\nrequirements. Creating reports / feedbacks.\n➢ Able to create scripts for automation.\n➢ Checking data fields contents by running shell scripts &amp; sql queries\n➢ Database management like, conversion and manipulation of AND's Global Road Data using\nSQL and compiling them to AND's navigational road data.\n➢ Developing PL-SQL models to fetch required data.\n➢ Writing shell script for automation for support and system admin.\n\nProject Name: Product Releasing Data 2018H2\n\nMicrosoft -\n\n2007 to 2007\n\nTesting of data is done by manually and customizing tools developed in Shell/Perl scripting.\nWe modify the Shell scripts as per requirements &amp; to test the Drawing &amp; Image with\nmodified scripts. The product is in Geographic data files (GDF) format this guides to the customer\nwhen the customer needs data up to a certain level.\nMy Role:\n• Performed Black Box, Functional, Integration and Regression Testing of application.\n• Involved in creation and execution of test cases.\n• Reporting of bugs.\nTools:\nPerl Scripts, Shell Scripts, Sql query, PL-SQL and in-house tools on Unix Sun Solaris\n\nhttps://www.indeed.com/r/Chhaya-Prabhale/99700a3a95e3ccd7?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Chhaya-Prabhale/99700a3a95e3ccd7?isid=rex-download&ikw=download-top&co=IN\n\n\nSKILLS\n\nPERL (10+ years), SCRIPTING (10+ years), SQL (10+ years), DATABASE (10+ years), SHELL\nSCRIPTING (10+ years)\n\nADDITIONAL INFORMATION\n\nKey Skills:\n\n➢ Demonstrating responsibility and initiative with an emphasis on Teamwork &amp; Co-\noperation.\n➢ Ability to work under high pressure while maintaining high standards of work.\n➢ A highly consistent and pragmatic approach to problem solving.\n\nOperating Systems: Windows (XP/2000), UNIX Sun Solaris 9. Linux (Red-hat-6)\nScripts: Shell, Basic Perl (UNIX, Linux)\nLanguages: C, C++, VB 6.0\nDatabase: Oracle, MySQL, Access, DB2\nPrimary Skill: Unix shell scripting, Sql, perl (Intermediate Level), Pl-sql\nTest tools: Basic Knowledge of QTP, WIN RUNNER, Test Director\n\nStrengths:\n\n• Professional Programming Skill and experience.\n• Expert in working with various operating systems.\n• Exceptional Technical Skill.\n• Highly skilled in customer service.\n• Good deliver output in less time without losing efficiency.\n• Excellent Communication Skill.\n• Proven strength in problem solving, coordination and analysis.", "meta": {}, "annotation_approver": null, "labels": [[0, 15, "Name"], [16, 37, "Location"], [64, 110, "Email Address"], [490, 507, "Location"], [511, 531, "Years of Experience"], [256, 265, "Tech Tools"], [556, 573, "Job Specific Skills"], [581, 585, "Tech Tools"], [608, 614, "Tech Tools"], [802, 813, "Tech Tools"], [727, 737, "Job Specific Skills"], [971, 978, "Tech Tools"], [1019, 1031, "Job Specific Skills"], [1506, 1563, "Job Specific Skills"], [1594, 1630, "Job Specific Skills"], [1634, 1651, "Job Specific Skills"], [1660, 1672, "Tech Tools"], [1674, 1679, "Tech Tools"], [1689, 1692, "Tech Tools"], [1700, 1706, "Tech Tools"], [1729, 1733, "Tech Tools"], [1734, 1737, "Tech Tools"], [1738, 1745, "Tech Tools"], [1955, 1959, "Tech Tools"], [1973, 1982, "Job Specific Skills"], [1996, 1999, "Tech Tools"], [2013, 2021, "Job Specific Skills"], [2035, 2050, "Tech Tools"], [588, 603, "Tech Tools"], [2167, 2175, "Soft Skills"], [2362, 2369, "Tech Tools"], [2381, 2385, "Tech Tools"], [2386, 2399, "Tech Tools"], [2401, 2406, "Tech Tools"], [2428, 2433, "Tech Tools"], [2435, 2446, "Tech Tools"], [2447, 2451, "Tech Tools"], [2453, 2458, "Tech Tools"], [2471, 2472, "Tech Tools"], [2474, 2477, "Tech Tools"], [2479, 2485, "Tech Tools"], [2496, 2502, "Tech Tools"], [2504, 2509, "Tech Tools"], [2511, 2517, "Tech Tools"], [2519, 2522, "Tech Tools"], [2538, 2558, "Tech Tools"], [2560, 2563, "Tech Tools"], [2565, 2569, "Tech Tools"], [2592, 2599, "Tech Tools"], [2630, 2633, "Tech Tools"], [2635, 2645, "Tech Tools"], [2647, 2660, "Tech Tools"], [2689, 2700, "Job Specific Skills"], [2826, 2842, "Soft Skills"], [2918, 2931, "Soft Skills"], [2960, 2975, "Soft Skills"], [2977, 2989, "Soft Skills"], [2994, 3002, "Soft Skills"]]}
{"id": 74, "text": "Karthik G V\nProgram Manager, Product Manager, Product Owner, BI /\nDatawarehouse, Big Data, Azure, Agile methodologies, Product Backlog\n\nSecunderabad, Andhra Pradesh - Email me on Indeed: indeed.com/r/Karthik-G-\nV/283106d88eb4649c\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSr. Program Manager / Product Owner, Architect, Consultant\n\nMicrosoft India -  Hyderabad, Telangana -\n\nFebruary 2005 to Present\n\n• 15+ years of experience in the IT &amp; Services Industry in multiple roles as DevOps Architect,\nProgram Manager / Product Owner, Architect, Consultant.\n• Successfully delivered engagements in the US, UK, LATAM, Asia Pacific and Middle East Regions.\n\n• Key specialization in building analytical platform, Datawarehouse and Business Intelligence\nsolutions. Have delivered .Net, MVC, API solutions to different customers.\n• Experience on Program Management, Product Management, DevOps practices, Quality\nAssurance, Continuous Quality, Process setup &amp; implementation, Presales.\n• Customer solutions delivered using Agile methodology, Iterative development, Test Driven\nDevelopment (TDD).\n\nEDUCATION\n\nPGDBM in Business Management\n\nNarsee Monji -  Hyderabad, Telangana\n\nSKILLS\n\nDatawarehouse / Business Intelligence (10+ years), Agile Methodologies (6 years), Program\nManagement (2 years), Agile (10+ years), Product Roadmap (2 years), Stakeholder\nManagement (10+ years), Risk Management (10+ years)\n\nLINKS\n\nhttps://www.linkedin.com/in/karthik-g-v-7a25462/\n\nAWARDS\n\nMicrosoft Technology Guru\n\nFebruary 2012\n\nhttps://www.indeed.com/r/Karthik-G-V/283106d88eb4649c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Karthik-G-V/283106d88eb4649c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.linkedin.com/in/karthik-g-v-7a25462/\n\n\nMicrosoft Role Model\n\nFebruary 2007\n\nCERTIFICATIONS/LICENSES\n\nISTQB\n\nITIL\n\nCITA\n\nJanuary 2016 to Present\n\nProduct Owner\n\nCSM", "meta": {}, "annotation_approver": null, "labels": [[0, 11, "Name"], [12, 27, "Designation"], [29, 44, "Designation"], [46, 59, "Designation"], [150, 164, "Location"], [187, 229, "Email Address"], [279, 298, "Designation"], [301, 314, "Designation"], [316, 325, "Designation"], [327, 337, "Designation"], [339, 348, "Companies worked at"], [349, 378, "Location"], [382, 406, "Years of Experience"], [489, 505, "Designation"], [507, 522, "Designation"], [525, 538, "Designation"], [540, 549, "Designation"], [551, 561, "Designation"], [1111, 1116, "Degree"], [1120, 1153, "College Name"], [1157, 1177, "Location"], [61, 79, "Job Specific Skills"], [81, 89, "Job Specific Skills"], [91, 96, "Tech Tools"], [98, 117, "Job Specific Skills"], [119, 134, "Job Specific Skills"], [410, 433, "Years of Experience"], [715, 728, "Job Specific Skills"], [733, 764, "Job Specific Skills"], [781, 785, "Tech Tools"], [787, 790, "Tech Tools"], [792, 795, "Tech Tools"], [846, 864, "Job Specific Skills"], [866, 884, "Job Specific Skills"], [886, 892, "Job Specific Skills"], [904, 921, "Job Specific Skills"], [923, 941, "Job Specific Skills"], [943, 977, "Job Specific Skills"], [979, 987, "Job Specific Skills"], [1026, 1043, "Job Specific Skills"], [1045, 1066, "Job Specific Skills"], [1068, 1097, "Job Specific Skills"], [1187, 1200, "Job Specific Skills"], [1203, 1224, "Job Specific Skills"], [1238, 1257, "Job Specific Skills"], [1269, 1287, "Job Specific Skills"], [1299, 1304, "Job Specific Skills"], [1318, 1333, "Job Specific Skills"], [1345, 1367, "Job Specific Skills"], [1381, 1396, "Job Specific Skills"]]}
{"id": 75, "text": "Mohammed Murtuza\nMajor Incident Manager / Escalation Manager - Microsoft India\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Mohammed-\nMurtuza/0cdc3284bf1bbeab\n\nWORK EXPERIENCE\n\nMajor Incident Manager / Escalation Manager\n\nMicrosoft India -\n\nJune 2016 to Present\n\nJune 2016 - Till date\n\nRoles and Responsibilities:\n• Working as a Major Incident Manager / Escalation Manager for MSIT in Microsoft. Primary\nduties include leading team operations, managing team resources, leading &amp; driving high\nimpact technical incidents to resolution. Providing executive updates throughout the incident\ntill resolution, communicate to global customers, senior executives/GMs on ongoing incidents,\nfacilitating positive and timely outcomes by evaluating and escalating incidents to appropriate\nresources when needed. Preparing post major incident reports and KPI reports of Escalation\nManagement for monthly reviews.\n• Plan and provide elevated level IT support for scheduled planned change/premium events\nacross organization.\n• Leading team of 20 L1's and managing daily operations.\n• Conduct Huddle meetings for team on daily performance targets and manage day to day shift\nactivities.\n• Validating the received escalation impact and pushing the tickets to the right severity with\nproper engagements to mitigate the issue at the earliest and understanding the Microsoft internal\nvarious service lines which is being affected in order to engage the appropriate resources to help\ndrive resolution.\n• Act as global escalation support to coordinate with various global teams within Microsoft IT and\ndrive Bridge calls on high priority incidents such as Managed P1 &amp; P2 incidents to restore the\nservices as soon as possible and send standard communications to global customers on status\nof the incident, ETA, and current restoration plan.\n• Managing the lifecycle of all incidents to restore normal service operation as quickly as possible\nand minimize the adverse impact on business operations across globe, thus insuring that the\nbest possible levels of service quality and availability are maintained.\n• Monitoring the ticketing system and reviewing the communications which will be sent to higher\nmanagement for all major incidents/outages.\n• Follow the Escalation process and keep up with the SLA's on all the new Escalation tickets and\ndocument incident chronology and timelines and support groups for Major incident resolution.\n• Complete assigned On-The-Job (OJT) training for the newly hired techs.\n• Preparing weekly meeting agenda which allows to improve coordination across teams and to\ndiscuss the overall incidents occurred in the past week.\n• Tracking the model of escalations and calculating the SLA's as a part of Quality check and\ncirculating internal group.\n\nhttps://www.indeed.com/r/Mohammed-Murtuza/0cdc3284bf1bbeab?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Mohammed-Murtuza/0cdc3284bf1bbeab?isid=rex-download&ikw=download-top&co=IN\n\n\n• Join Daily Cross-Geo shift hand over calls to review all the ongoing high priority incidents/\nescalations from Redmond, USA team to India &amp; Vice versa.\n• Create and publish post incident reports with high level incident summary and chronology\nto leadership team and thus suggest challenges and improvements to prevent reoccurrence of\nincident.\n• Identify and analyze problem requests and drive to circulate the RCA of the problem.\n• Pre Communication preparations for the various Planned Event Support which includes\nreserving the groups which allows to complete the activity as scheduled without any downtime.\n\nCompany: Genpact India\nClient: Schneider Electric\n\nMajor incident Management Team - Incident Coordinator\n\nRoles and Responsibilities:\n• Provided ultimate ownership and responsibility for end to end Management activities for all\nSeverity 1 &amp; 2 incidents.\n• Collaborate with internal and partner repair organizations, from engineers to executive and\nEnsures that the correct Technical teams are engaged and proper focus is paid to outages and\nrecovery.\n• Documented and tracked the timeline of events that occurred in the process to resolution for\neach of the incidents managed in support of post mortem/root cause analysis.\n• Performed notifications and status of all incidents to high level internal leadership and client\nwhile managing SLA's.\n• Worked directly with Incident Lifecycle Coordinators to provide initial incident response.\n• Manage, escalate, status, and assist, coordinating repair efforts on Service Assurance issues\n• Provide updates to the Management of daily outages. Updating Outage forums.\nCompany: Genpact India\nClient: Invensys\n\nIT consultant\nLevel-2 IT Consultant for Active Directory 2008, Exchange server 2007, VMware ESX 4 .1, Lync\n2010 server.\nRoles and Responsibilities:\n• Managed User Accounts on Windows NT and UNIX Platform (Creation, Deletion, Permissions,\nand VPN Access for company user's and contractors/vendors)\n• Configure users, groups, group policy objects, service accounts.\n• Developed organizational units in Active Directory (AD) and managed user security with group\npolicies.\n• Created and maintained email addresses and distribution lists in MS Exchange.\n• Compiled data to assist technical writers with IT new hire manuals and prepared data to report\nto testers for system enhancements.\n• Handled user account transfers from one field site to another moving client data to different\nservers, to ensure user accessibility.\n• Maintained Microsoft Exchange e-mail accounts and public folder access through Microsoft\nExchange System Manager.\n• Served as lead contact for Desk Side Support Technicians, to provide assistance when trouble-\nshooting desk side issues.\n\n\n\n• Setup queue's for networked printers and added clients to Blackberry Server, which enabled\nemployees to efficiently conduct business while away from the office\nCompany: Genpact India\nClient: Siemens\n\nSAP SRM consultant\n\nKey Responsibilities:\n• Functional support tickets handled in SAP SRM on the following areas of SAP-SRM.\n• Maintenance &amp; Creation of shopping carts\n• Workflow approvals\n• Creation of automatic account determination for consumables.\n• Maintenance on Purchase Orders\n\nCompany: Genpact India\nClient: Armstrong\n\nService desk representative.\n\nRoles and Responsibilities:\n• Effectively answering inbound telephone calls from clients and providing client support through\nthe use of an online knowledge base.\n• Partnered with Tier II and Tier III help desk peers based in the across the globe to resolve\ncomplex problems that required escalation. Provided detailed descriptions of issues in trouble\nticket system and followed up diligently to ensure swift resolutions\n• Configuring &amp; troubleshooting Auto Discover, Offline Address Book, Out of Office,\nScheduling &amp; free/busy, Exchange ActiveSync, Outlook Web Access, Outlook Connectivity,\nRPC over HTTP (Outlook Anywhere)\n• Troubleshooting for login issue, Microsoft Word, Excel, Access, Power Point, Front Page, Visio,\nInternet Explorer, Mozilla Firefox, Scanners, Desktop and Networked Printers.\n• Unlocking and resetting user's passwords for Active Directory, SAP application\nCompany: Wipro\nClient: HP\nWorked as Technical support representative for HP laptop support.\nRoles and Responsibilities:\n• Diagnose, troubleshoot and resolve a range of software, hardware and connectivity issues.\nExcel in asking probing questions and researching, analyzing and rectifying problems in Windows\nXP/ Vista/7, MS Office, and LAN/WAN connectivity issues.\n• Installed software, configured and tested customer PC's, analyzed functionality of peripheral\nappendages.\n• Instructed and trained end-users regarding computer literacy\n• Trained in sales and guided customer's in helping to select the right product\n\nEDUCATION\n\nMBA in Marketing and Human Resources\n\nOsmania University -  Hyderabad, Telangana\n\nB.Sc in Electronics\n\n\n\nOsmania University -  Hyderabad, Telangana\n\nSKILLS\n\nACTIVE DIRECTORY (2 years), EXCEL (2 years), EXCHANGE (2 years), INCIDENT MANAGEMENT\n(2 years), OPERATIONS (2 years)\n\nADDITIONAL INFORMATION\n\nProfessional Skills:\n\n• ITIL- Incident Management, Problem Management, Event Management, Change Management,\nand Configuration Management System.\n• People Management, Shift Rostering, IT Operations management, Resource management\n• Virtualization platforms- VMWare, Microsoft Hyper-V\n• Active Directory 2008, Exchange server 2007, VMware ESX 4 .1, Lync 2010 server, SAP SRM\n\nTools:\nServiceNow, Microsoft ICM, Send Word Now, SCOM, SolarWinds, , MS Clarity Connect, MS\nCentergy, BMC Remedy ticketing tool, Bomgar ticketing tool, Skype For Business, Microsoft\nOneNote, Microsoft StaffHub, MS Excel, PowerPoint tools.", "meta": {}, "annotation_approver": null, "labels": [[0, 16, "Name"], [17, 60, "Designation"], [63, 78, "Companies worked at"], [80, 100, "Location"], [123, 170, "Email Address"], [189, 232, "Designation"], [234, 249, "Companies worked at"], [253, 284, "Years of Experience"], [341, 384, "Designation"], [397, 406, "Companies worked at"], [4621, 4634, "Companies worked at"], [7795, 7831, "Degree"], [7833, 7851, "College Name"], [7855, 7875, "Location"], [7877, 7896, "Degree"], [7900, 7918, "College Name"], [7922, 7942, "Location"], [5883, 5896, "Companies worked at"], [431, 443, "Soft Skills"], [1853, 1879, "Job Specific Skills"], [2121, 2137, "Job Specific Skills"], [4828, 4838, "Tech Tools"], [4843, 4847, "Tech Tools"], [5189, 5200, "Tech Tools"], [6912, 6927, "Job Specific Skills"], [6945, 6959, "Tech Tools"], [6961, 6966, "Tech Tools"], [6968, 6974, "Tech Tools"], [6976, 6987, "Tech Tools"], [6989, 6999, "Tech Tools"], [7001, 7006, "Tech Tools"], [7008, 7025, "Tech Tools"], [7027, 7042, "Tech Tools"], [7952, 7968, "Tech Tools"], [7997, 8005, "Tech Tools"], [7980, 7985, "Tech Tools"], [8017, 8036, "Tech Tools"], [8048, 8058, "Tech Tools"], [8124, 8143, "Job Specific Skills"], [8145, 8163, "Job Specific Skills"], [8165, 8181, "Job Specific Skills"], [8183, 8200, "Job Specific Skills"], [8206, 8237, "Job Specific Skills"], [8241, 8258, "Job Specific Skills"], [8260, 8275, "Job Specific Skills"], [8276, 8301, "Job Specific Skills"], [8303, 8322, "Job Specific Skills"], [8325, 8339, "Job Specific Skills"], [8351, 8357, "Tech Tools"], [8359, 8376, "Tech Tools"], [8379, 8400, "Tech Tools"], [8402, 8422, "Tech Tools"], [8424, 8439, "Tech Tools"], [8441, 8457, "Tech Tools"], [8476, 8485, "Tech Tools"], [8487, 8500, "Tech Tools"], [8502, 8515, "Tech Tools"], [8517, 8521, "Tech Tools"], [8523, 8533, "Tech Tools"], [8537, 8555, "Tech Tools"], [8557, 8568, "Tech Tools"], [8574, 8595, "Tech Tools"], [8597, 8618, "Tech Tools"], [8620, 8625, "Tech Tools"], [8640, 8657, "Tech Tools"], [8659, 8677, "Tech Tools"], [8679, 8687, "Tech Tools"], [8689, 8699, "Tech Tools"]]}
{"id": 76, "text": "Saurabh Saurabh\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Saurabh-\nSaurabh/87e6b26903460061\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nDeveloper Support Engineer\n\nMicrosoft iGTSC -  Bangalore Urban, Karnataka -\n\nAugust 2007 to Present\n\nEDUCATION\n\nCertificate of Achievement\n\nMicrosoft Virtual Academy\n\nDecember 2016\n\nSKILLS\n\nC, C++, Data Structure, Java (1 year)\n\nADDITIONAL INFORMATION\n\n➢ Managerial Skills: Organizer and volunteer at many inter and intra college events, symposia\netc. in\ncollege.\n➢ Co-curricular: -\n1. Selected by College (TISL) to train the first-year engineering students under the Finishing\nSchool\nProgram (January 2017)\n2. District level soccer and cricket player.\n3. Honored at State Level Singing competition; Performed in college.\n\n➢ Leadership Skills: Effective leadership; associated with many student led organizations;\nStudent leader\nfor Oxygen, A movement for and by Students (2007 &amp; 2009)\n\nDECLARATION\n\nThe abovementioned is true to the best of my knowledge, information and personal belief.\nPlace: Bangalore.\n\nhttps://www.indeed.com/r/Saurabh-Saurabh/87e6b26903460061?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Saurabh-Saurabh/87e6b26903460061?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 15, "Name"], [16, 36, "Location"], [59, 105, "Email Address"], [155, 181, "Designation"], [183, 198, "Companies worked at"], [202, 228, "Location"], [232, 254, "Years of Experience"], [267, 293, "Degree"], [295, 320, "College Name"], [322, 335, "Graduation Year"], [345, 346, "Tech Tools"], [348, 351, "Tech Tools"], [353, 367, "Tech Tools"], [369, 373, "Tech Tools"], [410, 427, "Soft Skills"], [780, 790, "Soft Skills"], [799, 819, "Soft Skills"]]}
{"id": 77, "text": "Prabhu Prasad Mohapatra\nNeed job urgently\n\nBhubaneswar, Orissa - Email me on Indeed: indeed.com/r/Prabhu-Prasad-\nMohapatra/1e4b62ea17458993\n\nWORK EXPERIENCE\n\nBeta Tester\n\nMicrosoft & Xiaomi -\n\nJanuary 2018 to Present\n\nI am a beta tester\n\nEDUCATION\n\nStill Studying\n\nBhagabati Nodal High School,Sarakana\n\nSKILLS\n\nTypewriting, Editing\n\nLINKS\n\nhttps://plus.google.com/u/0/108623501355423636575\n\nhttps://www.indeed.com/r/Prabhu-Prasad-Mohapatra/1e4b62ea17458993?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Prabhu-Prasad-Mohapatra/1e4b62ea17458993?isid=rex-download&ikw=download-top&co=IN\nhttps://plus.google.com/u/0/108623501355423636575", "meta": {}, "annotation_approver": null, "labels": [[0, 13, "Name"], [14, 23, "Location"], [43, 62, "Location"], [85, 139, "Email Address"], [158, 169, "Designation"], [171, 189, "Companies worked at"], [193, 216, "Years of Experience"], [225, 236, "Designation"], [311, 322, "Soft Skills"]]}
{"id": 78, "text": "Raja Chandra Mouli\nCuddapah, Andhra Pradesh - Email me on Indeed: indeed.com/r/Raja-Chandra-\nMouli/445cbf3eb0a361cd\n\nWilling to relocate to: Cuddapah, Andhra Pradesh - Vijayawada, Andhra Pradesh - Visakhapatnam,\nAndhra Pradesh\n\nWORK EXPERIENCE\n\nms office\n\nMicrosoft -  Cuddapah, Andhra Pradesh -\n\nMay 2018 to Present\n\nEDUCATION\n\nBSc,Mecs,2nd year completed in Computer science\n\nards collage kadapa -  Cuddapah, Andhra Pradesh\n\nMay 2018 to June 2019\n\nSKILLS\n\nms office, internet,java (Less than 1 year)\n\nCERTIFICATIONS/LICENSES\n\nDegree,BSc(MECs) 2nd year\n\nMay 2018 to Present\n\nADDITIONAL INFORMATION\n\n3-101 KC Narayana Street,new madavaram(v),Vontimitta(M),Kadapa(D)\n\nhttps://www.indeed.com/r/Raja-Chandra-Mouli/445cbf3eb0a361cd?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Raja-Chandra-Mouli/445cbf3eb0a361cd?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 18, "Name"], [19, 43, "Location"], [66, 115, "Email Address"], [141, 165, "Location"], [168, 194, "Location"], [197, 226, "Location"], [269, 293, "Location"], [297, 316, "Years of Experience"], [329, 332, "Degree"], [333, 337, "Degree"], [360, 390, "College Name"], [391, 425, "Location"], [528, 544, "Degree"], [245, 264, "Tech Tools"], [458, 467, "Tech Tools"], [478, 482, "Tech Tools"], [427, 448, "Graduation Year"], [555, 574, "Graduation Year"]]}
{"id": 79, "text": "Krishna Prasad\nPatna City, Bihar - Email me on Indeed: indeed.com/r/Krishna-Prasad/56249a1d0efd3fca\n\nWORK EXPERIENCE\n\nData Entry Operator\n\nMicrosoft -  Patna, Bihar\n\nEDUCATION\n\nBSc in Math\n\nMagadh univercity -  Patna, Bihar\n\nJuly 1999 to April 2001\n\nBSc in Computer\n\nMagadh univercity -  Patna City, Bihar\n\nhttps://www.indeed.com/r/Krishna-Prasad/56249a1d0efd3fca?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [15, 32, "Location"], [55, 99, "Email Address"], [118, 137, "Designation"], [139, 148, "Companies worked at"], [152, 164, "Location"], [177, 188, "Degree"], [190, 207, "College Name"], [211, 223, "Location"], [250, 265, "Degree"], [267, 284, "College Name"], [288, 305, "Location"], [225, 248, "Graduation Year"]]}
{"id": 80, "text": "Soumya Balan\nIT SUPPORT\n\nSulthan Bathery, Kerala, Kerala - Email me on Indeed: indeed.com/r/Soumya-\nBalan/97ead9542c575355\n\n➢ To work in a progressive organization where I can enhance my skills and learning to contribute\nto the success of the organization.\n\nWORK EXPERIENCE\n\nTechnical support engineer\n\nMicrosoft\n\nPosition: TECHNICAL SUPPORT ENGINEER\n\nCompany: Microsoft Corporation - Microsoft India Global Technical Support Center (Microsoft\nIGTSC), Bangalore\n\nYears of Experience: 2 Years and 4 Months\n\nResponsibilities\n\n➢ Represent Microsoft and communicate with corporate customers via telephone, written\ncorrespondence, or electronic service regarding technically complex escalated problems\nidentified in Microsoft software products, and manage relationships with those customers.\n\n➢ Manage not only the technically complex problems, but also politically charged situations\nrequiring the highest level of customer skill.\n\n➢ Receive technically complex, critical or politically hot customer issues, and maintain ownership\nof issue until resolved completely.\n\n➢ Solve highly complex problems, involving broad, in-depth product knowledge or in-depth\nproduct specialty.\n\n➢ Use trace analysis, and other sophisticated tools to analyze problems and develop solutions\nto meet customer needs.\n\n➢ Lead triage meetings to share knowledge with other engineers and develop customer solutions\nefficiently.\n\n➢ Act as technical lead, mentor, and model for a team of engineers; provide direction to others,\nreview solutions and articles, mentoring existing &amp; aspiring Engineers.\n\n➢ Write technical articles for knowledge base.\n\n➢ Consult, collaborate and take escalations when necessary.\n\nhttps://www.indeed.com/r/Soumya-Balan/97ead9542c575355?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Soumya-Balan/97ead9542c575355?isid=rex-download&ikw=download-top&co=IN\n\n\n➢ Maintain working knowledge of pre-release products and take ownership for improvement in\nkey technical areas.\n\n➢ Manage customer escalations and recognize when to solicit additional help.\nParticipate in technical discussions and engage with product team if required to resolve issues\nand represent customer segments.\n\nExchange Server Knowledge\n\n➢ Exchange Server 2007\n➢ Exchange Server 2010\n➢ Exchange Server 2013\n➢ O365\n\nUG PROJECT TITLE: Memory Bounded Anytime Heuristic Search A* Algorithm\n\n➢ This Project presents a heuristic-search algorithm called Memory-bounded Anytime Window\nA*(MAWA*), which is complete, anytime, and memory bounded. MAWA* uses the window-\nbounded anytime-search methodology of AWA* as the basic framework and combines it with the\nmemory-bounded A* -like approach to handle restricted memory situations.\nSimple and efficient versions of MAWA* targeted for tree search have also been presented.\nExperimental results of the sliding-tile puzzle problem and the traveling-salesman problem show\nthe significant advantages of the proposed algorithm over existing methods.\n\nTechnical and Co-Curricular activities\n\n➢ Star Performer in Microsoft IGTSC in 2014.\n➢ Paper Presentations on Applications of Robotics in INOX 2K12.\n➢ Attended a Three-Day workshop on C and C++ Programming and Aliasing.\n➢ Attended a One-Day workshop on Java and Hardware Workshop at VECW\n➢ Paper presentation 4G Technologies, Cloud Computing, Heuristic Algorithms and Applications,\nOpen Source Software.\n➢ Multimedia presentations on Artificial Intellegence, 6th Sense, and Robotics.\n➢ Completed training of OCA (9i, 10g) from Oracle University.\n➢ Attended SPARK training program in Infosys Mysore.\n➢ Attended System Hardware Training program at HCL, Pondicherry.\n\nEDUCATION\n\nBE in Computer Science and Engineering\n\nVivekananda Engineering College for Women -  Chennai, Tamil Nadu\n\n2013\n\nBTEC HNC in Aviation in Hospitality and Travel Management\n\nFrankfinn Institute of Airhostess Training -  Calicut, Kerala\n\n\n\n2008\n\nState Board\n\n2007\n\nSKILLS\n\nLinux (Less than 1 year), Microsoft Office (Less than 1 year), MS OFFICE (Less than 1 year),\nproblem solving (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nSkill Set\n➢ Excellent communication and interpersonal skills.\n➢ Proficient in Computer Applications -Microsoft Office Windows (Windows 2007, XP, 8, 8.1 and\nWindows 10), Linux, Fedora.\n➢ Strong analytical and problem solving skills.\n➢ Ability in managing a team of professionals and enjoy being in a team.", "meta": {}, "annotation_approver": null, "labels": [[0, 12, "Name"], [13, 23, "Designation"], [25, 56, "Location"], [79, 122, "Email Address"], [275, 301, "Designation"], [303, 313, "Companies worked at"], [324, 350, "Companies worked at"], [361, 382, "Companies worked at"], [385, 450, "Companies worked at"], [452, 461, "Location"], [484, 504, "Years of Experience"], [3648, 3686, "Degree"], [3688, 3729, "College Name"], [3733, 3752, "Location"], [3754, 3759, "Graduation Year"], [3760, 3768, "Degree"], [3772, 3861, "College Name"], [3865, 3880, "Location"], [3884, 3889, "Graduation Year"], [2197, 2212, "Tech Tools"], [2226, 2246, "Tech Tools"], [2249, 2269, "Tech Tools"], [2272, 2292, "Tech Tools"], [3156, 3157, "Tech Tools"], [3162, 3165, "Tech Tools"], [3917, 3922, "Tech Tools"], [3943, 3959, "Tech Tools"], [3980, 3989, "Tech Tools"], [4010, 4025, "Soft Skills"], [4082, 4105, "Soft Skills"], [4110, 4123, "Soft Skills"], [4148, 4169, "Job Specific Skills"], [4171, 4187, "Tech Tools"], [4188, 4195, "Tech Tools"], [4197, 4236, "Tech Tools"], [4239, 4244, "Tech Tools"], [4246, 4252, "Tech Tools"], [4263, 4273, "Soft Skills"], [4278, 4293, "Soft Skills"]]}
{"id": 81, "text": "pradeep chauhan\npradeep chauhan\n\nNoida, Uttar Pradesh - Email me on Indeed: indeed.com/r/pradeep-\nchauhan/7fd59212dcc556bd\n\nWilling to relocate to: Noida, Uttar Pradesh\n\nWORK EXPERIENCE\n\nteachers\n\nmicrosoft -  Noida, Uttar Pradesh -\n\nApril 2017 to Present\n\nEDUCATION\n\nb.tech in computer science\n\nsunder deep group of institution -  Noida, Uttar Pradesh\n\nCERTIFICATIONS/LICENSES\n\nAndroid Application Development\n\nhttps://www.indeed.com/r/pradeep-chauhan/7fd59212dcc556bd?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/pradeep-chauhan/7fd59212dcc556bd?isid=rex-download&ikw=download-top&co=IN", "meta": {}, "annotation_approver": null, "labels": [[0, 15, "Name"], [16, 31, "Name"], [33, 53, "Location"], [76, 122, "Email Address"], [148, 168, "Location"], [187, 206, "Designation"], [210, 230, "Location"], [234, 255, "Years of Experience"], [268, 274, "Degree"], [278, 328, "College Name"], [332, 352, "Location"], [379, 410, "Degree"]]}
