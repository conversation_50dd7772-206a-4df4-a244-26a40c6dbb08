{"content": "<PERSON><PERSON><PERSON> K\nSenior Software Engineer\n\nBengaluru, Karnataka, Karnataka - Email me on Indeed: indeed.com/r/Govardhana-K/\nb2de315d95905b68\n\nTotal IT experience 5 Years 6 Months\nCloud Lending Solutions INC 4 Month • Salesforce Developer\nOracle 5 Years 2 Month • Core Java Developer\nLanguages Core Java, Go Lang\nOracle PL-SQL programming,\nSales Force Developer with APEX.\n\nDesignations & Promotions\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSenior Software Engineer\n\nCloud Lending Solutions -  Bangalore, Karnataka -\n\nJanuary 2018 to Present\n\nPresent\n\nSenior Consultant\n\nOracle -  Bangalore, Karnataka -\n\nNovember 2016 to December 2017\n\nStaff Consultant\n\nOracle -  Bangalore, Karnataka -\n\nJanuary 2014 to October 2016\n\nAssociate Consultant\n\nOracle -  Bangalore, Karnataka -\n\nNovember 2012 to December 2013\n\nEDUCATION\n\nB.E in Computer Science Engineering\n\nAdithya Institute of Technology -  Tamil Nadu\n\nSeptember 2008 to June 2012\n\nhttps://www.indeed.com/r/Govardhana-K/b2de315d95905b68?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Govardhana-K/b2de315d95905b68?isid=rex-download&ikw=download-top&co=IN\n\n\nSKILLS\n\nAPEX. (Less than 1 year), Data Structures (3 years), FLEXCUBE (5 years), Oracle (5 years),\nAlgorithms (3 years)\n\nLINKS\n\nhttps://www.linkedin.com/in/govardhana-k-61024944/\n\nADDITIONAL INFORMATION\n\nTechnical Proficiency:\n\nLanguages: Core Java, Go Lang, Data Structures & Algorithms, Oracle\nPL-SQL programming, Sales Force with APEX.\nTools: RADTool, Jdeveloper, NetBeans, Eclipse, SQL developer,\nPL/SQL Developer, WinSCP, Putty\nWeb Technologies: JavaScript, XML, HTML, Webservice\n\nOperating Systems: Linux, Windows\nVersion control system SVN & Git-Hub\nDatabases: Oracle\nMiddleware: Web logic, OC4J\nProduct FLEXCUBE: Oracle FLEXCUBE Versions 10.x, 11.x and 12.x\n\nhttps://www.linkedin.com/in/govardhana-k-61024944/","annotation":[{"label":["Companies worked at"],"points":[{"start":1749,"end":1754,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1696,"end":1701,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1417,"end":1422,"text":"Oracle"}]},{"label":["Skills"],"points":[{"start":1356,"end":1792,"text":"Languages: Core Java, Go Lang, Data Structures & Algorithms, Oracle\nPL-SQL programming, Sales Force with APEX.\nTools: RADTool, Jdeveloper, NetBeans, Eclipse, SQL developer,\nPL/SQL Developer, WinSCP, Putty\nWeb Technologies: JavaScript, XML, HTML, Webservice\n\nOperating Systems: Linux, Windows\nVersion control system SVN & Git-Hub\nDatabases: Oracle\nMiddleware: Web logic, OC4J\nProduct FLEXCUBE: Oracle FLEXCUBE Versions 10.x, 11.x and 12.x"}]},{"label":["Companies worked at"],"points":[{"start":1209,"end":1214,"text":"Oracle"}]},{"label":["Skills"],"points":[{"start":1136,"end":1247,"text":"APEX. (Less than 1 year), Data Structures (3 years), FLEXCUBE (5 years), Oracle (5 years),\nAlgorithms (3 years)\n"}]},{"label":["Graduation Year"],"points":[{"start":928,"end":931,"text":"2012"}]},{"label":["College Name"],"points":[{"start":858,"end":888,"text":"Adithya Institute of Technology"}]},{"label":["Degree"],"points":[{"start":821,"end":855,"text":"B.E in Computer Science Engineering"}]},{"label":["Graduation Year"],"points":[{"start":787,"end":790,"text":"2012"}]},{"label":["Companies worked at"],"points":[{"start":744,"end":749,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":722,"end":741,"text":"Associate Consultant"}]},{"label":["Companies worked at"],"points":[{"start":658,"end":663,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":640,"end":655,"text":"Staff Consultant"}]},{"label":["Companies worked at"],"points":[{"start":574,"end":579,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":555,"end":572,"text":"Senior Consultant\n"}]},{"label":["Companies worked at"],"points":[{"start":470,"end":492,"text":"Cloud Lending Solutions"}]},{"label":["Designation"],"points":[{"start":444,"end":468,"text":"Senior Software Engineer\n"}]},{"label":["Companies worked at"],"points":[{"start":308,"end":313,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":234,"end":239,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":175,"end":197,"text":"Cloud Lending Solutions"}]},{"label":["Email Address"],"points":[{"start":93,"end":136,"text":"indeed.com/r/Govardhana-K/\nb2de315d95905b68\n"}]},{"label":["Location"],"points":[{"start":39,"end":47,"text":"Bengaluru"}]},{"label":["Designation"],"points":[{"start":13,"end":37,"text":"Senior Software Engineer\n"}]},{"label":["Name"],"points":[{"start":0,"end":11,"text":"Govardhana K"}]}]}
{"content": "Harini Komaravelli\nTest Analyst at Oracle, Hyderabad\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Harini-\nKomaravelli/2659eee82e435d1b\n\n➢ 6 Yrs. of IT Experience in Manual and Automation testing.\n\nWORK EXPERIENCE\n\nQA Analyst\n\nOracle\n\nTest Analyst at Oracle, Hyderabad\n\nInfosys Ltd -  Hyderabad, Telangana -\n\nNovember 2011 to February 2016\n\nHyderabad from Nov 2011 to Feb17 2016\n➢ Worked in Tata Consultancy Services, Hyderabad from Feb 24 to Apr 11 2017\n➢ Currently working as a Test Analyst at Oracle, Hyderabad\n\nQA Analyst with 6 years of IT experience\n\nOracle\n\nEDUCATION\n\nMCA\n\nOsmania University\n\nB.Sc. in Computer Science\n\nOsmania University\n\nSKILLS\n\nFunctional Testing, Blue Prism, Qtp\n\nADDITIONAL INFORMATION\n\nArea of Expertise:\n\n➢ Familiar with Agile Methodologies.\n➢ Having knowledge in Energy (Petroleum) & Health Care domains.\n➢ Involved in preparation of Test Scenarios.\n➢ Preparing Test Data for the test cases.\n\nhttps://www.indeed.com/r/Harini-Komaravelli/2659eee82e435d1b?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Harini-Komaravelli/2659eee82e435d1b?isid=rex-download&ikw=download-top&co=IN\n\n\n➢ Experienced in development and execution of Test cases effectively.\n➢ Experienced in Functional testing, GUI testing, Smoke testing, Regression testing and\nIntegration Testing\n➢ Experienced in doing Accessibility testing of an application\n➢ Ability to understand user Requirements, Functional and Design specifications.\n➢ Good knowledge of SDLC and STLC processes.\n➢ Deciding the Severity and Priority of bugs.\n➢ Experience in using Microsoft Test Manager & Oracle Test Manager as Test Management Tools.\n➢ Having good experience in testing windows based & web based applications.\n➢ Involved in Client Interactions for reviews, issues and for any clarifications.\n➢ Web Services Testing\n➢ Writing Test Scripts in QTP, Testcomplete.\n➢ Creating Object Repositories and Function Libraries in QTP.\n➢ Enhanced QTP scripts using VB Script.\n➢ Strong experience in working with Blue Prism tool\n➢ Worked on different Environments like Windows Application & Web Application\n\nTechnical Skills:\n\n❑ Test Automation Tools: Blue Prism, QTP 10.0, Testcomplete\n❑ Test Management Tool: Microsoft Test Manager, Oracle Test Manager & JIRA\n❑ Databases: Oracle 10g, SQL Server.\n\n❑ Operating Systems: Windows 7\n\nProject 1:\nTitle: Cadence\nClient: Baker Hughes\n\nTechnologies: Microsoft Visual Studio and Microsoft Team Foundation Server\n\nClient Background:\nAn oilfield services company delivering focused efforts on shale gas and other oilfield services.\nIt provides services, tools and software for drilling and formation evaluation, well completion,\nproduction management, seismic data collection and interpretation.\n\nProject Description:\nAUT (Application under test) is the next generation revolutionary, robust, easy to use scalable\nwell site data acquisition processing and interpretation system for Client's Drilling Services to\ndeliver services that meets cross divisional business requirements consistently.\n\nProject 2:\n\nDescription:\nParagon supports your entire care team with one tool that your clinicians need to help deliver\nthe best patient care. Designed by physicians, nurses, pharmacists and mid level providers that\nhave a first-hand understanding of clinical workflow needs, Paragon clinical applications allow\nyour caregivers to focus on what matters most; spending time caring for patients. Since Paragon\nis fully-integrated across all applications and built around a single patient database, information\n\n\n\nentered anywhere in the system is immediately available to the entire care team. Immediate\naccess not only helps clinicians make better treatment decisions - it also helps promote patient\nsafety. Paragon offers a broad suite of multidisciplinary clinical software solutions together with\nanytime, anywhere access to the complete patient record.\n\nResponsibilities:\n\n• Performed Smoke testing and Regression testing.\n• Involved in Generating and Executing Test Script using Quick Test Pro & Blue Prism\n• Usability and User Interface Testing.\n• Involved in Defect tracking and reporting the bugs using TFS\n• Participated in frequent walk-through meetings with Internal Quality Assurance groups and with\ndevelopment groups.\n• Participated in client calls and clarifying the doubts by having AT&T sessions\n• Involved in functional, regression and smoke testing to validate the application data changes\ndone in windows application\n• Certifying the build status by running the scripts as part of smoke testing\n\nProject 3:\n\nDescription:\nFood & Beverages R&A: Easily manage business across multiple locations while reducing IT\ncost and complexity. Cloud-based point-of-sale (POS) solutions enable centralized enterprise\nmanagement with lower upfront costs and a smaller footprint.\n\nResponsibilities:\n\n• Performed Functional testing and Regression testing.\n• Involved in Generating and Executing Test Scripts using Blue Prism tool and Open script\n• Involved in preparing bots using Blue Prism tool.\n• Accessibility testing of the web application\n• Involved in Defect tracking and reporting the bugs using JIRA\n• WebServices testing by calling API's to export the data","annotation":[{"label":["Companies worked at"],"points":[{"start":2275,"end":2280,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":2235,"end":2240,"text":"Oracle"}]},{"label":["Companies worked at"],"points":[{"start":1603,"end":1608,"text":"Oracle"}]},{"label":["Skills"],"points":[{"start":667,"end":702,"text":"Functional Testing, Blue Prism, Qtp\n"}]},{"label":["College Name"],"points":[{"start":638,"end":657,"text":"\nOsmania University\n"}]},{"label":["Degree"],"points":[{"start":612,"end":636,"text":"B.Sc. in Computer Science"}]},{"label":["College Name"],"points":[{"start":591,"end":610,"text":"\nOsmania University\n"}]},{"label":["Degree"],"points":[{"start":587,"end":589,"text":"MCA"}]},{"label":["Companies worked at"],"points":[{"start":568,"end":573,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":526,"end":535,"text":"QA Analyst"}]},{"label":["Location"],"points":[{"start":515,"end":523,"text":"Hyderabad"}]},{"label":["Companies worked at"],"points":[{"start":507,"end":512,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":491,"end":502,"text":"Test Analyst"}]},{"label":["Location"],"points":[{"start":429,"end":437,"text":"Hyderabad"}]},{"label":["Location"],"points":[{"start":352,"end":360,"text":"Hyderabad"}]},{"label":["Location"],"points":[{"start":296,"end":304,"text":"Hyderabad"}]},{"label":["Location"],"points":[{"start":270,"end":278,"text":"Hyderabad"}]},{"label":["Companies worked at"],"points":[{"start":262,"end":267,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":246,"end":257,"text":"Test Analyst"}]},{"label":["Companies worked at"],"points":[{"start":238,"end":243,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":226,"end":235,"text":"QA Analyst"}]},{"label":["Designation"],"points":[{"start":177,"end":206,"text":"Manual and Automation testing."}]},{"label":["Years of Experience"],"points":[{"start":150,"end":154,"text":"6 Yrs"}]},{"label":["Location"],"points":[{"start":54,"end":62,"text":"Hyderabad"}]},{"label":["Location"],"points":[{"start":43,"end":51,"text":"Hyderabad"}]},{"label":["Companies worked at"],"points":[{"start":35,"end":40,"text":"Oracle"}]},{"label":["Designation"],"points":[{"start":19,"end":30,"text":"Test Analyst"}]},{"label":["Name"],"points":[{"start":0,"end":17,"text":"Harini Komaravelli"}]}]}
{"content": "Hartej Kathuria\nData Analyst Intern - Oracle Retail\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Hartej-Kathuria/04181c5962a4af19\n\nWilling to relocate to: Delhi - Bangalore, Karnataka - Gurgaon, Haryana\n\nWORK EXPERIENCE\n\nData Analyst Intern\n\nOracle Retail -  Bengaluru, Karnataka -\n\nJune 2017 to Present\n\nJob Responsibilities:\no As an intern part of the Global Retail Insights team at Oracle Retail,\nwork involved creating a data oriented buisness case based using high\nlevel trends for various retailers using Excel and SQL.\no Forecasting Sales with use of various statistical Modelling Methods\nusing SQL and R\no Market Basket Analysis using transactional data of retailers using SQL and R\n\nEDUCATION\n\nStatistics and Probability\n\nManipal University\n\nMay 2018\n\nB. Tech in Electrical and Electronics in Embedded Systems\n\nMIT, Manipal University\n\nMay 2016\n\nSKILLS\n\nPython (2 years), SQL. (1 year), NOSQL (1 year), R (2 years), Machine Learning (2 years)\n\nPUBLICATIONS\n\nPost-operative life expectancy in lung cancer patients\n\nThe objective of the project was to build an efficient predictive model based\non a predefined dataset to predict whether the patient survives or dies within one year of the\noperation. The dataset given has 17 variables: 12 nominal, 2 ordinal and 3 numerical. The target\nvariable has value true if the patient dies within one year of the operation else false if he survives.\nTool used: R\n\nhttps://www.indeed.com/r/Hartej-Kathuria/04181c5962a4af19?isid=rex-download&ikw=download-top&co=IN\n\n\nPredict the Happiness (Sentimental Analysis)\n\nThe objective of this project was to build a binary classifcation model for the data provided by\nTripAdvisor consisiting of a sample of hotel reviews provided by customers.The model built can\nbe used by them to understand the hotels\nlisted by them.Tool Used: R\n\nPredict Network attacks\n\nThe objective of this project was to build a multi-class classification model to predict the type of\nattack for an internet network company in Japan which has\nbeen facing huge losses due to malicious server attacks.The train dataset has\n18 numerical features and 23 categorical features.The target variable has\nthree classes.Tool Used: Python\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLSET\n\n• Languages & Technologies: Python, R, SQL, NoSQL, Predictive Modelling,\nMarket Basket Analysis, Sentimental Analysis, Clustering, Bash\nScripting (Preliminary), Socket Programming, Java (Preliminary)\n\n• Tools: R Studio, Jupyter, GIT, Sublime, MATLAB, Linux, KVM, Virtual Box,\nOpen VZ, Oracle SQL Developer, MySQL, MongoDB, Excel","annotation":[{"label":["Skills"],"points":[{"start":2246,"end":2572,"text":" Languages & Technologies: Python, R, SQL, NoSQL, Predictive Modelling,\nMarket Basket Analysis, Sentimental Analysis, Clustering, Bash\nScripting (Preliminary), Socket Programming, Java (Preliminary)\n\n• Tools: R Studio, Jupyter, GIT, Sublime, MATLAB, Linux, KVM, Virtual Box,\nOpen VZ, Oracle SQL Developer, MySQL, MongoDB, Excel"}]},{"label":["Email Address"],"points":[{"start":1435,"end":1479,"text":"indeed.com/r/Hartej-Kathuria/04181c5962a4af19"}]},{"label":["Skills"],"points":[{"start":875,"end":963,"text":"Python (2 years), SQL. (1 year), NOSQL (1 year), R (2 years), Machine Learning (2 years)\n"}]},{"label":["Graduation Year"],"points":[{"start":861,"end":864,"text":"2016"}]},{"label":["College Name"],"points":[{"start":837,"end":855,"text":"Manipal University\n"}]},{"label":["Degree"],"points":[{"start":773,"end":829,"text":"B. Tech in Electrical and Electronics in Embedded Systems"}]},{"label":["Graduation Year"],"points":[{"start":767,"end":770,"text":"2018"}]},{"label":["College Name"],"points":[{"start":743,"end":761,"text":"Manipal University\n"}]},{"label":["Degree"],"points":[{"start":714,"end":740,"text":"\nStatistics and Probability"}]},{"label":["Location"],"points":[{"start":271,"end":279,"text":"Bengaluru"}]},{"label":["Designation"],"points":[{"start":233,"end":251,"text":"Data Analyst Intern"}]},{"label":["Email Address"],"points":[{"start":96,"end":140,"text":"indeed.com/r/Hartej-Kathuria/04181c5962a4af19"}]},{"label":["Location"],"points":[{"start":53,"end":61,"text":"Bengaluru"}]},{"label":["Companies worked at"],"points":[{"start":38,"end":51,"text":"Oracle Retail\n"}]},{"label":["Designation"],"points":[{"start":16,"end":34,"text":"Data Analyst Intern"}]},{"label":["Name"],"points":[{"start":0,"end":14,"text":"Hartej Kathuria"}]}]}
{"content": "Ijas Nizamuddin\nAssociate Consultant - State Street\n\nIrinchayam B.O, Kerala - Email me on Indeed: indeed.com/r/Ijas-\nNizamuddin/6748d77f76f94eed\n\nWith close to 3 years of experience in IT industry, I have had excellent exposure to design,\ndevelopment and implementation of Client Server Applications in various domains such as\nBanking and Finance concepts. I have been involved in various software Development projects\nin Open System environment.\n\nWORK EXPERIENCE\n\nAssociate Consultant\n\nOracle Corporation -\n\nJune 2011 to Present\n\nState Street Global Advisors (SSgA) is the asset management business of State Street\nCorporation, one of the world's leading providers of financial services to institutional investors1,\nwith a heritage dating back over two centuries. Backed by the strength and stability of the State\nStreet organization, SSgA makes continual investments in asset management and client service\nplatform, resulting in a client-focused, solutions-driven orientation .BrokerViews is the application\nwhich list all the details about the counterparties who invest their securities in State Street.The\ndetails also include ratings given by Bloomberg.\n\nResponsibilities: Development, Testing and support.\nSoftware Used: Java, GWT\n\nAssociate Consultant\n\nOracle Corporation -  Bangalore, Karnataka -\n\nMay 2010 to June 2011\n\nThis project is actually a redesign of an existing client website. The client website was designed\non Java Server Pages (JSP) and our aim was to change it into a more dynamic web page using\nAdobe Flex. At first we changed the home page screen of the client website. After the successful\ncompletion of that we incorporated flex in to the account section also. This data which is obtained\nfrom DataBase is taken by the flex using a remote procedure call and the data is shown to the\nuser. With the use of Advanced Data Grids, Charts(including Bar and Pie Charts) the site increased\nthe readability and understandability of the users who were previously using the pages on java\nserver pages. This site developed by us won the IMC (Interactive Media Council)'s outstanding\nachievement award in Financial information. The judge evaluate website based on 5 criteria:\nDesign, Content, Feature Functionality, Usability and Standard Compliance. Our website scored\n475 out of a maximum of 500 points.\n.\nResponsibilities: Development, Testing and support.\nSoftware Used: Java, Adobe Flex\n\nhttps://www.indeed.com/r/Ijas-Nizamuddin/6748d77f76f94eed?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Ijas-Nizamuddin/6748d77f76f94eed?isid=rex-download&ikw=download-top&co=IN\n\n\nFramework: Springs, MVC\n\nAssociate Consultant\n\nOracle Corporation -  Bangalore, Karnataka -\n\nFebruary 2010 to April 2010\n\nDescription: Development of Basel II Application, Basel II is the second of the Basel Accords,\nwhich are recommendations on banking laws and regulations issued by the Basel Committee on\nBanking Supervision. The purpose of Basel II, which was initially published in June 2004, is to\ncreate an international standard that banking regulators can use when creating regulations about\nhow much capital banks need to put aside to guard against the types of financial and operational\nrisks banks face. In practice, Basel II attempts to accomplish this by setting up rigorous risk\nand capital management requirements designed to ensure that a bank holds capital reserves\nappropriate to the risk the bank exposes itself to through its lending and investment practices.\n\nThe New Accord includes several methodologies for determining a bank's risk-based capital\nrequirements for credit, market and operational risk. For Risk Based Capital (RBC), Credit Usage\n(CU) and Stress Test (ST), the methodologies that will be used for repo style transactions are\nSimple VaR if the collateral is eligible. If the collateral is ineligible then the Wholesale loan\napproach will be utilized or the collateral will be reduced to zero in the Simple VaR.\n.\nRoles and Responsibilities: Development, Testing and support.\nSoftware Used: Oracle 9i\n\nOTHER PROJECTS AND REAL TIME TRAINING:\n\nRTRM(Railway Ticketing System Through Mobile)\nA mobile based real time application with many exciting features like checking pnr status, train\navailability, trains between stations etc. This application was done in j2me and it uses weblogic\nas server and MSSQL as the database.\n\nUndergone a mandatory Training on Finance By Oracle Corporation\n\nEDUCATION\n\nBirla Institute Of Technology -  Pilani, Rajasthan\n\n2011\n\nBachelor of Technology in Computer Science\n\nUniversity College Of Engineering, University Of Kerala\n\n2005 to 2009\n\nADDITIONAL INFORMATION\n\nSKILL SET:\n\n\n\nLanguages: Core Java\nFront end/GUI Tools programming: Adobe Flex, GWT\nDatabase: Oracle 10g\nIDE: Eclipse, FlexBuilder\nFrameWorks: Spring(Basics), MVC frame work\nOperating System: Windows, Linux, Unix","annotation":[{"label":["Skills"],"points":[{"start":4652,"end":4849,"text":"Languages: Core Java\nFront end/GUI Tools programming: Adobe Flex, GWT\nDatabase: Oracle 10g\nIDE: Eclipse, FlexBuilder\nFrameWorks: Spring(Basics), MVC frame work\nOperating System: Windows, Linux, Unix"}]},{"label":["Graduation Year"],"points":[{"start":4607,"end":4611,"text":" 2009"}]},{"label":["College Name"],"points":[{"start":4543,"end":4575,"text":"University College Of Engineering"}]},{"label":["Degree"],"points":[{"start":4499,"end":4540,"text":"Bachelor of Technology in Computer Science"}]},{"label":["Graduation Year"],"points":[{"start":4493,"end":4497,"text":"2011\n"}]},{"label":["College Name"],"points":[{"start":4441,"end":4470,"text":"Birla Institute Of Technology "}]},{"label":["Companies worked at"],"points":[{"start":4410,"end":4427,"text":"Oracle Corporation"}]},{"label":["Companies worked at"],"points":[{"start":2654,"end":2671,"text":"Oracle Corporation"}]},{"label":["Designation"],"points":[{"start":2632,"end":2651,"text":"Associate Consultant"}]},{"label":["Graduation Year"],"points":[{"start":1323,"end":1327,"text":"2011\n"}]},{"label":["Companies worked at"],"points":[{"start":1260,"end":1277,"text":"Oracle Corporation"}]},{"label":["Designation"],"points":[{"start":1238,"end":1257,"text":"Associate Consultant"}]},{"label":["Companies worked at"],"points":[{"start":603,"end":615,"text":"State Street\n"}]},{"label":["Companies worked at"],"points":[{"start":487,"end":504,"text":"Oracle Corporation"}]},{"label":["Designation"],"points":[{"start":465,"end":484,"text":"Associate Consultant"}]},{"label":["Email Address"],"points":[{"start":97,"end":143,"text":" indeed.com/r/Ijas-\nNizamuddin/6748d77f76f94eed"}]},{"label":["Location"],"points":[{"start":53,"end":66,"text":"Irinchayam B.O"}]},{"label":["Companies worked at"],"points":[{"start":39,"end":51,"text":"State Street\n"}]},{"label":["Designation"],"points":[{"start":16,"end":35,"text":"Associate Consultant"}]},{"label":["Name"],"points":[{"start":0,"end":14,"text":"Ijas Nizamuddin"}]}]}
{"content": "Imgeeyaul Ansari\njava developer\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Imgeeyaul-Ansari/a7be1cc43a434ac4\n\nWilling to relocate to: Pune, Maharashtra\n\nWORK EXPERIENCE\n\nApplication Developer\n\nOracle Financial Software Services -  Pune, Maharashtra -\n\nAugust 2016 to Present\n\n• Wrote Services in java using data annotation which in turn were used for creating other files\nusing code generation tool.\n\n• Jar of services with related utility files such as DTOs are deployed on the Host side.\n\n• Host side is hosted on Tomcat Server where proxy files are present.\n\n• Created jsff page for UI, and wrote action, helper, assembler, backing bean for UI side business\nLogic made entries in collectionAppModule, PageDef.\n\n• Used ADF & MVC architecture for building application.\n\nUsed JUnit for Testing services,Algorithms.\nAlso made test Suites For running multiple test case at one go.\n\nUsed Eclipse Debugger for Fixing Service Related Jiras.\nMade Use of Hot Deployment for Fixing Ui related Bugs on UI side which was run on Weblogic\nServer. \nUsed JAWS Reader for solving accessibility Related jiras and IA plugin.\n\nUsed Java to write Batches for fetching of Bulk Data at Regular Interval.\nIt created Thread for Multitasking to reduce the time for processing.\n\nEDUCATION\n\nBachelor of Enginerring in Information Technology\n\nArmy institute of technology -  Pune, Maharashtra\n\n2012 to 2016\n\nCBSE in Physics, Chemistry, Mathematics\n\nRashtriya Military School Bangalore -  Bengaluru, Karnataka\n\nhttps://www.indeed.com/r/Imgeeyaul-Ansari/a7be1cc43a434ac4?isid=rex-download&ikw=download-top&co=IN\n\n\n2010 to 2011\n\nCBSE in Mathematics and English\n\nRashtriya Military School Bangalore -  Bengaluru, Karnataka\n\n2008 to 2009\n\nSKILLS\n\nJAVA (1 year), CSS (1 year), HTML (1 year), MYSQL (1 year), JAVASCRIPT (Less than 1 year),\nAngularjs (1 year), Oracle Pl/Sql\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\nProgramming Languages :C/C++, angular, java, java servlet, HTML, java script, MySQL, css, jsff.\n\nOperating Systems Linux, Windows, Android\n\nORM Eclipse Link, Hibernate\n\nFramework & tools :ADF, Eclipse, Android Studio, Git, Selenium, Code blocks, Net beans, R studio,\nTortoise SVN.","annotation":[{"label":["Skills"],"points":[{"start":1894,"end":2172,"text":"rogramming Languages :C/C++, angular, java, java servlet, HTML, java script, MySQL, css, jsff.\n\nOperating Systems Linux, Windows, Android\n\nORM Eclipse Link, Hibernate\n\nFramework & tools :ADF, Eclipse, Android Studio, Git, Selenium, Code blocks, Net beans, R studio,\nTortoise SVN."}]},{"label":["Skills"],"points":[{"start":1726,"end":1850,"text":"JAVA (1 year), CSS (1 year), HTML (1 year), MYSQL (1 year), JAVASCRIPT (Less than 1 year),\nAngularjs (1 year), Oracle Pl/Sql\n"}]},{"label":["Graduation Year"],"points":[{"start":1711,"end":1715,"text":" 2009"}]},{"label":["College Name"],"points":[{"start":1643,"end":1677,"text":"Rashtriya Military School Bangalore"}]},{"label":["Degree"],"points":[{"start":1610,"end":1641,"text":"CBSE in Mathematics and English\n"}]},{"label":["College Name"],"points":[{"start":1433,"end":1467,"text":"Rashtriya Military School Bangalore"}]},{"label":["Graduation Year"],"points":[{"start":1385,"end":1389,"text":" 2016"}]},{"label":["Location"],"points":[{"start":1359,"end":1362,"text":"Pune"}]},{"label":["College Name"],"points":[{"start":1327,"end":1355,"text":"Army institute of technology "}]},{"label":["Degree"],"points":[{"start":1276,"end":1324,"text":"Bachelor of Enginerring in Information Technology"}]},{"label":["Graduation Year"],"points":[{"start":269,"end":273,"text":" 2016"}]},{"label":["Location"],"points":[{"start":242,"end":245,"text":"Pune"}]},{"label":["Companies worked at"],"points":[{"start":204,"end":237,"text":"Oracle Financial Software Services"}]},{"label":["Designation"],"points":[{"start":181,"end":201,"text":"Application Developer"}]},{"label":["Location"],"points":[{"start":145,"end":148,"text":"Pune"}]},{"label":["Location"],"points":[{"start":33,"end":36,"text":"Pune"}]},{"label":["Designation"],"points":[{"start":17,"end":30,"text":"java developer"}]},{"label":["Name"],"points":[{"start":0,"end":15,"text":"Imgeeyaul Ansari"}]}]}
{"content": "Jay Madhavi\nNavi Mumbai, Maharashtra - Email me on Indeed: indeed.com/r/Jay-\nMadhavi/1e7d0305af766bf6\n\nI look forward to being associated with a growth - oriented, learning firm and\ncontribute my skills for its success. This will allow me to grow both professionally\nas well as an individually.\n\nWORK EXPERIENCE\n\nNIIT -\n\n2016 to 2016\n\nB+ Average\nAdvanced\n\nSQL Oracle -\n\n2016 to 2016\n\nB+ Average\n\nMSCIT -\n\n2011 to 2011\n\nA Completed\nTechnical Institution\n\nProjects undertaken (BE):\n\nS.N. Project Title Name of company/college Nature of the Remarks\nproject\n\n1 Android Based Saraswati College Of Android Completed\nEmployee Tracker Engineering Application\nSystem\n\n2 An innovative Saraswati College Of Compilation Completed\napproach for Engineering\ncode optimization\n\n3 Simple Website Saraswati College Of Website related to Completed\nRelated to Engineering information of\nClassical Italian cars\nCars\n\nAbout Myself:\n• I am Capable and Hardworking, and can adapt to New Surroundings.\n\nhttps://www.indeed.com/r/Jay-Madhavi/1e7d0305af766bf6?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Jay-Madhavi/1e7d0305af766bf6?isid=rex-download&ikw=download-top&co=IN\n\n\n• I Can Face Challenges with confidence and would give my best shot under Stressful situations.\n\n.. 03 : 03:\n\nEDUCATION\n\nBE (Computer Science) in Computer Science\n\nSaraswati College Of Engineering, Kharghar -  Mumbai, Maharashtra\n\n2014 to 2017\n\nHSC in Computer science\n\nAcharya College Chembur -  Mumbai, Maharashtra\n\n2011 to 2013\n\nSSC\n\nState Board\n\n2011\n\nADDITIONAL INFORMATION\n\n• Ability to accept responsibilities and give best performance to complete the given work\nefficiently.\n• To take up challenging jobs & work as a team.\n• To positively accept my Mistake.","annotation":[{"label":["Graduation Year"],"points":[{"start":1520,"end":1523,"text":"2011"}]},{"label":["Degree"],"points":[{"start":1507,"end":1517,"text":"State Board"}]},{"label":["Graduation Year"],"points":[{"start":1496,"end":1499,"text":"2013"}]},{"label":["Graduation Year"],"points":[{"start":1488,"end":1491,"text":"2011"}]},{"label":["College Name"],"points":[{"start":1440,"end":1462,"text":"Acharya College Chembur"}]},{"label":["Degree"],"points":[{"start":1415,"end":1438,"text":"HSC in Computer science\n"}]},{"label":["Graduation Year"],"points":[{"start":1409,"end":1412,"text":"2017"}]},{"label":["College Name"],"points":[{"start":1334,"end":1365,"text":"Saraswati College Of Engineering"}]},{"label":["Degree"],"points":[{"start":1291,"end":1332,"text":"BE (Computer Science) in Computer Science\n"}]},{"label":["Skills"],"points":[{"start":917,"end":939,"text":"Capable and Hardworking"}]},{"label":["Graduation Year"],"points":[{"start":413,"end":416,"text":"2011"}]},{"label":["Graduation Year"],"points":[{"start":405,"end":408,"text":"2011"}]},{"label":["Skills"],"points":[{"start":356,"end":367,"text":"SQL Oracle -"}]},{"label":["Companies worked at"],"points":[{"start":313,"end":317,"text":"NIIT "}]},{"label":["Email Address"],"points":[{"start":51,"end":100,"text":"Indeed: indeed.com/r/Jay-\nMadhavi/1e7d0305af766bf6"}]},{"label":["Location"],"points":[{"start":12,"end":22,"text":"Navi Mumbai"}]},{"label":["Name"],"points":[{"start":0,"end":10,"text":"Jay Madhavi"}]}]}
{"content": "Jitendra Babu\nFI/CO Consultant in Tech Mahindra - SAP FICO\n\nChennai, Tamil Nadu - Email me on Indeed: indeed.com/r/Jitendra-Babu/bc3ea69a183395ed\n\n• Having 3.2-years of SAP experience as sap FICO Consultant\n• Involved in Implementation and support projects\n• Basic knowledge in simple finance.\n• Proficient in SAP's ASAP Methodology and well versed with business process, its mapping &\nconfiguration in SAP\n• Good inter-personal skills, strong analytical ability and problem-solving capabilities\n• Ability to make timely and sound decisions based on logical assumptions, factual information.\n• Ability to work as a team member supporting co-workers and the commitment to the overall\nsuccess of a group.\n• Work effectively with internal customers, co-workers and management.\n• Knowledge on integration of FI with other modules like MM and SD\n• Experience in GL, AP, and AR\n• Good communication skills with an aptitude to interact with the clients for Production support\n• Good Understanding of business process in Industry.\n• Expertise on data uploading tolls-LSMW\n• Good exposure on writing validation and substitution rules for business requirements and\nwriting queries.\n• Interacting with the end user and finalizing the user requirement\n• Coordinating with the other teams for SAP integration aspects\n• Well exposure on designing the organization structure and setting it up in SAP in association\nwith other members from different streams of the implementation team.\n• Detail oriented, quick learner, good listener with strong problem solving skills.\n\nSAP FICO SKILL SET:\n\nFinance\n• Financial Accounting- General Ledger Accounting FI-G/L (New GL),\n• Accounts Payable-FI-A/P,\n• Accounts Receivable FI-A/R\n\nWORK EXPERIENCE\n\nFI/CO Consultant in Tech Mahindra\n\nSAP FICO -\n\n2015 to Present\n\nSAP FICO Consultant\n\nSAP FICO -\n\nApril 2017 to May 2018\n\nProject & Role Description:\n\nhttps://www.indeed.com/r/Jitendra-Babu/bc3ea69a183395ed?isid=rex-download&ikw=download-top&co=IN\n\n\nFossil Group, Inc., together with its subsidiaries, designs, develops, markets, and distributes\nconsumer fashion accessories. The company's principal products include a line of men's and\nwomen's fashion watches and jewelry, handbags, small leather goods, belts, and sunglasses. It\noffers its products under its proprietary brands, such as FOSSIL, MICHELE, MISFIT, RELIC, SKAGEN,\nand ZODIAC, as well as under the licensed brands, including ARMANI EXCHANGE, CHAPS, DIESEL,\nDKNY, EMPORIO ARMANI, KARL LAGERFELD, KATE SPADE NEW YORK, MARC JACOBS, MICHAEL\nKORS, and TORY BURCH. The company sells its products through company-owned retail stores,\ndepartment stores, specialty retail stores, specialty watch.\nRoles and Responsibilities:\n• Resolving Day to Day issues as well as providing Solution for better Business Processes.\n• Adhere to the SLA timelines\n• Coordinating with technical consultants for modifications in outputs and program changes\n• Handling various support issues be it process, configuration or functionality issue.\n• Migrated Transaction and Master Data using migration tool LSMW\n• Effective defect tracking, reporting and documenting the deliverables\n• Handling knowledge transfer sessions to the new comers in the team\n• Participation in regular team members meetings who are part of this support project and SAP\nFICO team in scope.\n• Conducting the Core-Team Training.\n• Configuring new payment terms\n• Defined new payment terms as per the business requirements for Vendors.\n• Actively involved in Table maintenance\n• Preparing the Root cause analysis, Back log report and SLA adherence report inputs to team\nleader from time to time.\n• Supporting the end users while running the Automatic Payment Program\n• Creating new Validations and Substitutions for posting transactions requirements.\n• Working closely with all members of the team to clear the backlog tickets.\n• Good Exposure towards Ticketing tool\n• Resolved Automatic payment program issues / bugs in implementing SAP OSS Notes.\n\nDOMAIN EXPERIENCE:\n• Worked under Auditor for 6 months as a assistant in Tally ERP 9.0 package.\n\nSAP FICO -\n\nMarch 2015 to March 2017\n\nProject & Role Description:\nFord India Private Limited manufactures, distributes, and exports cars, SUVs, sedans, and\nlow displacement engines. It offers total maintenance, extended warranty, scheduled service,\npreferred insurance, and mobile service plans. The company sells its products through\ndealers to individuals, fleet organizations/rental companies, corporate, embassy/consulates and\nprofessionals, and government organizations; and sales and service outlets.\nRoles and Responsibilities:\n• Exposure towards value ASAP methodology\n• Co-ordination with core team and Preparation of Businesses Blue Print for the complete business\nprocess.\n• Documented in AS IS & TO BE document\n• Involved in WRICEF elements\n• Involved in positive, negative & random testing\n\n\n\n• Involved in data upload\n• Involved in SAP customizing, configuring and processing the Business Transactions in Finance.\n• Configured and Customized the G/L account master records, G/L Account groups\n• Define field status variant. Define number range\n• Expertise on data uploading tolls-LSMW\n• Create and Maintain the Master Accounts for GL,\n• Creating vendors and customer master data\n• Configuration of automatic payment program\n• Exposure on writing validation and substitution rules for business requirements\n\nProject:\n\nEDUCATION\n\nB.com\n\nDegree College -  Machilipatnam, Andhra Pradesh\n\n2014\n\nAG&SGS Intermediate College\n\n2011","annotation":[{"label":["Graduation Year"],"points":[{"start":5510,"end":5513,"text":"2011"}]},{"label":["College Name"],"points":[{"start":5481,"end":5507,"text":"AG&SGS Intermediate College"}]},{"label":["Graduation Year"],"points":[{"start":5475,"end":5478,"text":"2014"}]},{"label":["College Name"],"points":[{"start":5426,"end":5439,"text":"Degree College"}]},{"label":["Degree"],"points":[{"start":5419,"end":5424,"text":"B.com\n"}]},{"label":["Companies worked at"],"points":[{"start":4077,"end":4084,"text":"SAP FICO"}]},{"label":["Companies worked at"],"points":[{"start":1810,"end":1817,"text":"SAP FICO"}]},{"label":["Companies worked at"],"points":[{"start":1789,"end":1796,"text":"SAP FICO"}]},{"label":["Companies worked at"],"points":[{"start":1760,"end":1767,"text":"SAP FICO"}]},{"label":["Designation"],"points":[{"start":1725,"end":1757,"text":"FI/CO Consultant in Tech Mahindra"}]},{"label":["Skills"],"points":[{"start":1576,"end":1705,"text":"Finance\n• Financial Accounting- General Ledger Accounting FI-G/L (New GL),\n• Accounts Payable-FI-A/P,\n• Accounts Receivable FI-A/R"}]},{"label":["Companies worked at"],"points":[{"start":1555,"end":1562,"text":"SAP FICO"}]},{"label":["Years of Experience"],"points":[{"start":155,"end":165,"text":" 3.2-years "}]},{"label":["Email Address"],"points":[{"start":101,"end":144,"text":" indeed.com/r/Jitendra-Babu/bc3ea69a183395ed"}]},{"label":["Location"],"points":[{"start":60,"end":66,"text":"Chennai"}]},{"label":["Companies worked at"],"points":[{"start":50,"end":57,"text":"SAP FICO"}]},{"label":["Designation"],"points":[{"start":14,"end":46,"text":"FI/CO Consultant in Tech Mahindra"}]},{"label":["Name"],"points":[{"start":0,"end":12,"text":"Jitendra Babu"}]}]}
{"content": "Jyotirbindu Patnaik\nAssociate consultant@SAP labs , Bangalore Karnataka\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Jyotirbindu-\nPatnaik/77e3ceda47fbb7e4\n\n-\nExperienced incident and change coordinator and strongly skilled and dedicated ITIL Expert with\na superior work ethic and management satisfaction record. Widely and deeply knowledgeable\nin all aspects of ITIL management and coordination. Adept multitasker able to deal a very high\npriority complex situations with accuracy and professionalism.\n\nWilling to relocate to: Bangalore, Karnataka\n\nWORK EXPERIENCE\n\nAssociate consultant\n\nSap labs\n\nIncident and change management coordinator,\ndealing with the escalation process of company products.\nNotifying the customer as well as stake holders regarding the on going issue as well as helping\nproblem management team to provide RCA.\n\nAssociate consultant\n\nSap labs\n\n-\nJoining date from: January 25, 2017\nDesignation: Associate Consultant\nCompany: SAP on the payroll of Bristlecone India LTD.\n\nRoles and responsibilities: -\nIncident Coordinator:\n1. Following the escalation process and handling the high priority incidents by initiating the\ntroubleshooting call and driving the entire call till the issue gets resolve.\n2. Capturing the entire chronological order to provide the RCA for the unplanned downtimes.\n3. As an incident coordinator, I was informing the internal stakeholders regarding the unplanned\ndowntimes/high priority issue by sending the notifications periodically.\n4. Post handling the issue we were updating the MTTR and monthly outage tracker to have a\nclear records of unplanned downtimes.\n5. Monitoring the tools like Catchpoint, Pingdom, CSS for quick find of availability alerts and trying\nto troubleshoot by initial analysis ASAP.\n6. Preparing the documents for all the new process and update it as per its new changes.\n7. Providing the reports (KPI/Availability/IRT-MPT) on weekly and monthly basis to the\nmanagement to minimize the number incidents.\n8. I was analyzing regarding the number of incidents and alerts received, and providing the entire\ncaptured details to management for further process to reduce the incidents and alerts.\n\nhttps://www.indeed.com/r/Jyotirbindu-Patnaik/77e3ceda47fbb7e4?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Jyotirbindu-Patnaik/77e3ceda47fbb7e4?isid=rex-download&ikw=download-top&co=IN\n\n\nChange Coordinator:\n9. As a change, Coordinator was handling the Entire change management process and validating\nthe Change requests to get the CAB approvals.\n10. Providing the KPI report for the change process.\n11. Driving the CAB meeting and KPI meeting.\n\nProjects: -\nI was working for \"Cloud for customer\" and \"Business by design\" project in SAP.\n\nAchievements: -\n1. I have received management appreciation note for handling the change management process\nin a proficient way.\n2. Team Lead appreciated for maintaining the documents and PPT's as updated.\n\nEDUCATION\n\nB. Tech in Electronics and Communication\n\nBiju Patnaik University -  Rayagada, Orissa\n\n2016\n\nSKILLS\n\nITIL foundation","annotation":[{"label":["Skills"],"points":[{"start":3052,"end":3066,"text":"ITIL foundation"}]},{"label":["College Name"],"points":[{"start":2993,"end":3015,"text":"Biju Patnaik University"}]},{"label":["Degree"],"points":[{"start":2951,"end":2991,"text":"B. Tech in Electronics and Communication\n"}]},{"label":["Companies worked at"],"points":[{"start":870,"end":877,"text":"Sap labs"}]},{"label":["Designation"],"points":[{"start":847,"end":867,"text":"\nAssociate consultant"}]},{"label":["Companies worked at"],"points":[{"start":600,"end":607,"text":"Sap labs"}]},{"label":["Designation"],"points":[{"start":577,"end":597,"text":"\nAssociate consultant"}]},{"label":["Email Address"],"points":[{"start":116,"end":166,"text":"indeed.com/r/Jyotirbindu-\nPatnaik/77e3ceda47fbb7e4\n"}]},{"label":["Location"],"points":[{"start":73,"end":81,"text":"Bengaluru"}]},{"label":["Designation"],"points":[{"start":19,"end":39,"text":"\nAssociate consultant"}]},{"label":["Name"],"points":[{"start":0,"end":18,"text":"Jyotirbindu Patnaik"}]}]}
{"content": "Karthihayini C\nSystems Engineer - Infosys Limited\n\nRajapalaiyam, Tamil Nadu - Email me on Indeed: indeed.com/r/Karthihayini-\nC/627254c443836b3c\n\nTo be a part of challenging team, which works for the growth of an organization, explores my\npotential and provides me an opportunity to enhance my knowledge and to be an asset of the\ncompany.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSystems Engineer\n\nInfosys Limited -  Chennai, Tamil Nadu -\n\nDecember 2016 to Present\n\nTrainee\n\nInfosys Limited -  Mysore, Karnataka -\n\nJune 2016 to November 2016\n\nClient: Renault\nProject Details:\n• Trained in .Net technology.\n• Handled VB part of the application which has mainframe as backend and Visual basic as the\nfront end with annuaire service as the medium between mainframe and VB.\n• Took care of site creation in the Share Point technology.\n• Experienced in using SmartSVN, TortoiseSVN, DB Visualiser.\n\nEDUCATION\n\nB E in Production Engineering\n\nVelammal Engineering College -  Chennai, Tamil Nadu\n\n2016\n\nTamil Nadu Board Of Education -  Rajapalaiyam, Tamil Nadu\n\n2012\n\nEducation\n\nHr. Sec. School\n\n2010\n\nhttps://www.indeed.com/r/Karthihayini-C/627254c443836b3c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Karthihayini-C/627254c443836b3c?isid=rex-download&ikw=download-top&co=IN\n\n\nSKILLS\n\nDestructive Testing (Less than 1 year), FORGE (Less than 1 year), Non-Destructive (Less than 1\nyear), Non-Destructive Testing (Less than 1 year), quality control (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nAREAS OF INTEREST\n1. Strength of materials\n2. Engineering statistics and quality control\n\nINDUSTRIAL VISITS\n• BAY FORGE, Madurantakam\n• ASHOK LEYLAN, Ennore\n• CARBORUNDUM, Thiruvottiyur\n• BONFIGLIOLI, Thirumudivakkam\n\nIN-PLANT TRAININGS & WORKSHOPS\n• Attended in-plant training on security division at \"Minda Corporation Limited\", Chennai.\n• Attended in-plant training in \"BHEL\", Ranipet.\n• Attended workshop on \"Aero Modeling\" conducted at Consto UAV Technologies\n• Attended workshop on \"Non-Destructive Testing\" conducted at Velammal engineering College,\nChennai.\n\nPERSONAL QUALIFICATIONS:\n• Ability to quick grasp the concepts\n• Flexible\n• Hard working\n• Consistent","annotation":[{"label":["Skills"],"points":[{"start":2169,"end":2178,"text":"Consistent"}]},{"label":["Skills"],"points":[{"start":2154,"end":2165,"text":"Hard working"}]},{"label":["Skills"],"points":[{"start":2142,"end":2150,"text":" Flexible"}]},{"label":["Skills"],"points":[{"start":2105,"end":2139,"text":"Ability to quick grasp the concepts"}]},{"label":["Skills"],"points":[{"start":1305,"end":1485,"text":"Destructive Testing (Less than 1 year), FORGE (Less than 1 year), Non-Destructive (Less than 1\nyear), Non-Destructive Testing (Less than 1 year), quality control (Less than 1 year)\n"}]},{"label":["College Name"],"points":[{"start":1076,"end":1091,"text":"Hr. Sec. School\n"}]},{"label":["Location"],"points":[{"start":1033,"end":1044,"text":"Rajapalaiyam"}]},{"label":["College Name"],"points":[{"start":1000,"end":1028,"text":"Tamil Nadu Board Of Education"}]},{"label":["College Name"],"points":[{"start":941,"end":968,"text":"Velammal Engineering College"}]},{"label":["Degree"],"points":[{"start":910,"end":938,"text":"B E in Production Engineering"}]},{"label":["Companies worked at"],"points":[{"start":482,"end":496,"text":"Infosys Limited"}]},{"label":["Companies worked at"],"points":[{"start":405,"end":419,"text":"Infosys Limited"}]},{"label":["Designation"],"points":[{"start":387,"end":402,"text":"Systems Engineer"}]},{"label":["Email Address"],"points":[{"start":98,"end":142,"text":"indeed.com/r/Karthihayini-\nC/627254c443836b3c"}]},{"label":["Location"],"points":[{"start":51,"end":62,"text":"Rajapalaiyam"}]},{"label":["Companies worked at"],"points":[{"start":34,"end":49,"text":"Infosys Limited\n"}]},{"label":["Companies worked at"],"points":[{"start":34,"end":48,"text":"Infosys Limited"}]},{"label":["Designation"],"points":[{"start":15,"end":30,"text":"Systems Engineer"}]},{"label":["Name"],"points":[{"start":0,"end":13,"text":"Karthihayini C"}]}]}
{"content": "Karthik GV\nArchitect - Microsoft India\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Karthik-GV/1961c4eff806e6f4\n\nWilling to relocate to: hyderbad, Telangana\n\nWORK EXPERIENCE\n\nArchitect\n\nMicrosoft India -  Hyderabad, Telangana -\n\nFebruary 2005 to Present\n\n• DevOps - One of the key members of the DevOps team in the Enterprise Services Global DevOps\nprogram.\n• Readiness - Provide DevOps readiness to the end customer.\n\nSr. Program Manager\n\nMicrosoft India -\n\nMarch 2016 to January 2018\n\nKey Projects - Aurora BI (SAP), ES Analytics, BI as a Service, Service Center BI, Premier services\n(PSR)\n\nI am accountable for implementing a unified analytical platform for the entire Enterprise Services\nbusiness (~$6 billion) in Microsoft. This allows the services business to strategize and take key\noperational decisions during the monthly and quarterly business reviews with the Leadership\nTeam.\n\nKey Responsibilities\n• Product Owner - Manage & prioritize product backlog. Evangelize product feature with other\nteams and enable more product adoption.\n• Product Roadmap - Prepare roadmap for the product for the upcoming fiscal year.\n• Sprint Planning & monitoring - Scoping, resource levelling / smoothing\n• Stakeholder management - Manage key business stakeholders, get / set expectations and\nmanage communication.\n• Risk management & mitigation\n• Co-ordinate cross functional teams for product releases\n• Product Retirement - Plan and manage existing product retirement, interact, communicate with\nall downstream systems to ensure product retirement and replacement is smooth.\n• Product migration - Retire Informatica and migrate to Azure Data Factory for ETL process.\n• Compliance Management\n◦ Global Data Protection Regulation (GDPR) - Analyze, estimation and planning for the BI group.\n\nKey Skills - Microsoft Azure SQL DW, Azure Data Factory, SQL Server 2016, Power BI dashboard,\nRest API\n\nhttps://www.indeed.com/r/Karthik-GV/1961c4eff806e6f4?isid=rex-download&ikw=download-top&co=IN\n\n\nMethodology - Agile model\n\nArchitect\n\nMicrosoft India -  Hyderabad, Telangana -\n\nSeptember 2013 to March 2016\n\nMcKesson, Singapore Provident Fund, SSE Home Services\n\nKey Responsibilities\n• Delivery Management - Provide technical leadership and strategic direction to the testing\norganization and project delivery team.\n• Presales - Closely interact with customer & pre-sales team, response to RFP, Test estimates and\nprepare Statement of Work. Won multiple test only engagements from CEE, LATAM, India region\n• Test Strategy & Test Plan development & review for test only engagements.\n• Test Consulting Services - Worked with the UK based customer \"SSE Home Services\" to provide\ntest consultancy and technical guidance to the offshore TCS Kolkata team.\n• Test Processes - Defined process steps and quality gates for test only engagements at\norganization level.\n• Quality Analysis - Analysis of trends in testing, identify best practices, improvement areas. I\nhave prepared checklist for presales & delivery team that reduced the effort and cost of review\nand lead to faster turnover to the customer.\n• Project Recovery Team - I am one the key members in the project recovery team. I work closely\nwith the project team and leadership to ensure that the project is recovered successfully. I have\nsuccessfully recovered 3 critical project with high quality. In the current recovery project, I have\nmanaged a team size of 103 team members.\n• Project Delivery review- As a part of Technical Quality Assurance (TQA), I am the quality\ngatekeeper for all dashboard projects (~30 projects) within Microsoft Global Delivery. All test\nartifacts, test approach, test processes are reviewed and signed off during TQA review.\n\nKey Skills\n\nSr consultant\n\nMicrosoft India -  Hyderabad, Telangana -\n\nJune 2008 to March 2016\n\nSr Test Engineer\n\nMicrosoft India -  Hyderabad, Telangana -\n\nFebruary 2005 to June 2008\n\n2013, SQL Server 2014, Test automation using Coded UI, Selenium.\n\nEDUCATION\n\nPGDBM\n\nNarsee Monjee Institute of Management Studies\n\n\n\n2017 to 2018\n\nSKILLS\n\nProgram Management (2 years), Product Management (2 years), Quality Assurance (10+\nyears), Business intelligence, Devops\n\nLINKS\n\nhttps://www.linkedin.com/in/karthik-g-v-7a25462\n\nAWARDS\n\nMicrosoft Technology Guru\n\nFebruary 2012\n\nMicrosoft Role Model\n\nJune 2007\n\nCERTIFICATIONS/LICENSES\n\nScrum Product Owner\n\nScrum Master\n\nCITA\n\nADDITIONAL INFORMATION\n\nMethodologies - Agile model, Iterative Model\nTest Management - Resource forecasting, team sizing and budgeting, release planning, team\nmanagement, technical\nguidance to the team.\n\nRole: Sr. Consultant (June 2008 - Sep 2013)\nExperience on managing test team for Data Warehouse / SQL Server BI, .Net App Development\nProjects, API / Framework, Windows phone apps and Performance Testing engagements. Played\nrole of Test Lead / Manager in all engagements.\n\nKey Projects - NHS UK, Baxter, ATI, Intel Corporation, Merck, EXL, Oman BI, PwC, ANZ Bank, SQL\nServer PDW (Parallel Data-warehousing), Azure Cave tool\n\nKey Responsibilities\n• Test Manager in all engagements.\n• Test Planning & Execution - Define test strategy and test plan. Onboard resources allocate and\ntrack tasks. Identify risks and mitigation plans.\n• Define & implement Test Automation\n• Performance test planning and execution\n\nhttps://www.linkedin.com/in/karthik-g-v-7a25462\n\n\n• Define defect triage process with project & customer.\n• Team Management - Managed maximum team size up to 8 resources.\n\nKey Skills\nTechnologies - Visual Studio 2012, Microsoft Test Manager 2012, Azure, Test Automation using\nCoded UI, Unit Test Framework, Performance testing using Visual Studio 2012, SQL Server 2012.\nMethodologies - Iterative, Agile model, Test Driven Development (TDD)\n\nMicrosoft IT - India, BI CoE (Feb 2005 - June 2008)\nThe Business Intelligence CoE in India manages multiple applications that require Data\nWarehousing, Reporting, Analytical and other BI related Capabilities.\n\nRole: Test Lead\nI was the test lead for multiple Data Warehousing / SQL Server BI projects. My primary role has\nbeen the test lead for all these projects.\n\nKey Projects - MSSales, Rhythm of Business (RoB), Services Information Repository (SIR)\n\nKey Responsibilities\n• Planning and Estimation of quarterly releases\n• Define test strategy and Master Test Plan\n• Review & Inspection of BRD, FSD, TSD, Test Cases & Test Plan\n• Execution of Test Cases and defect logging.\n• Conduct / participate in Defect Review meetings with the developers / client\n• Analysis of Defects - Analysis of the root cause and the Injection phase of each defect.\n• Test automation on MS BI, RoB, PTS Web, SIR & CFR.\n• Team Management (includes FTEs) - managed team size of max 10 resources\n\nKey Skills\nTechnologies -SQL Server 2000 / 2005 / 2008, SSRS, SSAS, SQL Server PI, SharePoint, Azure,\nEnterprise Library\nTools: Visual Studio 2012, QTP 8.2, TFS 2012.\nSDLC & Test Methodologies - Agile methodology, Iterative model.\n\nRoyal Bank of Scotland, New Delhi Aug 2004 to Jan 2005 (6 months)\nMy role as a Quality Engineer involved in\n• Review of functional requirements\n• Design test plan\n• Design & review of test cases\n• Test automation using Winrunner 7.6, QTP.\n• Test case execution and defect reporting.\n• Set up Performance testing environment-using Loadrunner. Completed a POC for performance\ntesting of a D2K application.\n\nKey Skills Acquired\nTechnologies: Oracle, D2K\nTools: Winrunner 7.6, QTP 6.5, Test Director 7.6, Loadrunner 8.0, Clearcase, SQL Navigator\n\nSapient Corporation, New Delhi June 2003 to Aug 2004 (14 months)\n\n\n\nRole of Quality Engineer and primarily involved in\n• Review of functional requirements.\n• Create & execute system & Integration test cases in Test Director\n• Test automation script development & execution using Winrunner 7.6.\n• Maintenance of Test Automation environment.\n• Production deployment support - Testing in Production.\n\nKey Skills Acquired\nTechnologies: Java, JSP, Ariba 1.5\nTools: Test Director 7.6, Toad, Winrunner 7.5, Trackgear 3.5, PVCS Tracker, WinCVS, Togetherj,\nVisio, Winmerge","annotation":[{"label":["Skills"],"points":[{"start":4048,"end":4168,"text":"Program Management (2 years), Product Management (2 years), Quality Assurance (10+\nyears), Business intelligence, Devops\n"}]},{"label":["Graduation Year"],"points":[{"start":4034,"end":4037,"text":"2018"}]},{"label":["College Name"],"points":[{"start":3977,"end":4021,"text":"Narsee Monjee Institute of Management Studies"}]},{"label":["Degree"],"points":[{"start":3970,"end":3974,"text":"PGDBM"}]},{"label":["Location"],"points":[{"start":3841,"end":3849,"text":"Hyderabad"}]},{"label":["Companies worked at"],"points":[{"start":3822,"end":3836,"text":"Microsoft India"}]},{"label":["Designation"],"points":[{"start":3804,"end":3819,"text":"Sr Test Engineer"}]},{"label":["Location"],"points":[{"start":3755,"end":3763,"text":"Hyderabad"}]},{"label":["Companies worked at"],"points":[{"start":3736,"end":3750,"text":"Microsoft India"}]},{"label":["Designation"],"points":[{"start":3721,"end":3733,"text":"Sr consultant"}]},{"label":["Location"],"points":[{"start":2053,"end":2061,"text":"Hyderabad"}]},{"label":["Companies worked at"],"points":[{"start":2034,"end":2048,"text":"Microsoft India"}]},{"label":["Designation"],"points":[{"start":2023,"end":2031,"text":"Architect"}]},{"label":["Graduation Year"],"points":[{"start":493,"end":496,"text":"2018"}]},{"label":["Companies worked at"],"points":[{"start":452,"end":466,"text":"Microsoft India"}]},{"label":["Location"],"points":[{"start":217,"end":225,"text":"Hyderabad"}]},{"label":["Companies worked at"],"points":[{"start":198,"end":212,"text":"Microsoft India"}]},{"label":["Designation"],"points":[{"start":187,"end":195,"text":"Architect"}]},{"label":["Location"],"points":[{"start":40,"end":48,"text":"Hyderabad"}]},{"label":["Companies worked at"],"points":[{"start":23,"end":37,"text":"Microsoft India"}]},{"label":["Designation"],"points":[{"start":11,"end":19,"text":"Architect"}]},{"label":["Name"],"points":[{"start":0,"end":9,"text":"Karthik GV"}]}]}
{"content": "Kartik Sharma\nSystems Engineer - Infosys Ltd\n\nDelhi, Delhi - Email me on Indeed: indeed.com/r/Kartik-Sharma/cc7951fd7809f35e\n\n● Qualified B.Tech in Information Technology with 2.5 years overall and 2 years' experience in\nSAP Security, Project Management and Software Support.\n● Currently spearheading as Senior Systems Engineer with Infosys Ltd Pune, well versed in\nAnalysis, Test and Support activities.\n● Proficient in handling various projects and managing project risks. Possess up to date\nknowledge of latest technological advancements, regulations and statutory compliances in the\nindustry.\n● Instrumental in building relations with upper level decision makers, seizing control of critical\nproblem areas and delivering on client commitments.\n\nPROJECT ANNEXURE:\n\nProject Name: RB (Reckitt Benckiser)\nDuration: Since April '16\nRole: SAP Security Consultant\nResponsibilities:\n● Technical analyst for sap security in production and non-production environments.\n● Worked with Security related tables such as AGR*, USR* etc.\n● Performed User comparison using PFCG.\n● Analysing user access issues using SU53 and system trace (ST01)\n● Role changes done using PFCG as per the change request received.\n● Mass user changes using SHDB, LSMW, SU10.\n● Control Firefighter access in GRC10.1.\n● Handling/Creating Solman CR as per Business requirement.\n● Working on tool Service-Now for User/Business/Technical support.\n\nPROJECT KEY RESULT AREAS:\n\n● Extensive working knowledge in SAP ECC 6.0, SAP R/3 Enterprise GRC 10.1.\n● Expertise in Role Administration, PFCG, User reports, Authorization objects.\n● Expertise in Risk Analysis, Mitigation and Remediation.\n● Utilize SU24 to enable/disable security checks\n● Granting privileged and compensatory controls, providing access in controlled environment\nusing Fire-fighter id.\n● Troubleshoot security/authorization using SU53, ST01 and SUIM.\n● Restrict table access through authorization groups.\n● Ticket handling-related to various issues ranging from user expiration to missing\nauthorizations.\n● Addition, Removal of transaction codes, authorizations, authorization objects by modifying\nexisting roles based upon change request.\n● Supporting Site Go-Lives.\n\nWilling to relocate to: Delhi - Noida, Uttar Pradesh - Gurgaon, Haryana\n\nhttps://www.indeed.com/r/Kartik-Sharma/cc7951fd7809f35e?isid=rex-download&ikw=download-top&co=IN\n\n\nWORK EXPERIENCE\n\nSystems Engineer\n\nInfosys Ltd -  Delhi, Delhi -\n\nMarch 2016 to Present\n\nas a management trainee.\n• Worked with 'AVS InfoTech' as a part-time employee.\n• Guided students in Science, Math and C++ for 4 years.\n\nTECHNICAL SKILL SET\n\nSAP Systems: SAP ECC 6.0.\nProduct Tools: GRC 10.1\nDatabase: Oracle, SQL, RDBMS.\n\nSenior Systems engineer\n\nInfosys Limited -\n\nSeptember 2015 to Present\n\nWorking as SAP SECURITY consultant in a client project with Infosys\n\nEDUCATION\n\nB.Tech in Engineering\n\nNorthern India engineering college, IP UNIVERSITY DELHI\n\n2015\n\nCBSE\n\nLovely Public Sr. Sec. School\n\n2011\n\nCBSE\n\nLovely Public Sr. Sec. School\n\n2009\n\nUniversity / Board\n\nSKILLS\n\nSAP Security\n\n\n\nADDITIONAL INFORMATION\n\nOperating systems: Windows […] 8, 10.\nLanguages: C, C++, C#\n\nPROFESSIONAL SKILL SET:\n\n• Good Communication Skills in English and Hindi.\n• Ability to work under pressure.","annotation":[{"label":["Skills"],"points":[{"start":3086,"end":3254,"text":"Operating systems: Windows […] 8, 10.\nLanguages: C, C++, C#\n\nPROFESSIONAL SKILL SET:\n\n• Good Communication Skills in English and Hindi.\n• Ability to work under pressure."}]},{"label":["Skills"],"points":[{"start":3046,"end":3057,"text":"SAP Security"}]},{"label":["College Name"],"points":[{"start":2981,"end":3009,"text":"Lovely Public Sr. Sec. School"}]},{"label":["Degree"],"points":[{"start":2975,"end":2978,"text":"CBSE"}]},{"label":["Graduation Year"],"points":[{"start":2969,"end":2972,"text":"2011"}]},{"label":["College Name"],"points":[{"start":2938,"end":2966,"text":"Lovely Public Sr. Sec. School"}]},{"label":["Degree"],"points":[{"start":2932,"end":2935,"text":"CBSE"}]},{"label":["Graduation Year"],"points":[{"start":2926,"end":2929,"text":"2015"}]},{"label":["College Name"],"points":[{"start":2869,"end":2923,"text":"Northern India engineering college, IP UNIVERSITY DELHI"}]},{"label":["Degree"],"points":[{"start":2846,"end":2867,"text":"B.Tech in Engineering\n"}]},{"label":["Graduation Year"],"points":[{"start":2749,"end":2752,"text":"2015"}]},{"label":["Skills"],"points":[{"start":2614,"end":2692,"text":"SAP Systems: SAP ECC 6.0.\nProduct Tools: GRC 10.1\nDatabase: Oracle, SQL, RDBMS."}]},{"label":["Location"],"points":[{"start":2425,"end":2429,"text":"Delhi"}]},{"label":["Location"],"points":[{"start":2418,"end":2422,"text":"Delhi"}]},{"label":["Designation"],"points":[{"start":2385,"end":2400,"text":"Systems Engineer"}]},{"label":["Location"],"points":[{"start":2220,"end":2224,"text":"Delhi"}]},{"label":["Skills"],"points":[{"start":837,"end":848,"text":"SAP Security"}]},{"label":["Companies worked at"],"points":[{"start":332,"end":343,"text":" Infosys Ltd"}]},{"label":["Designation"],"points":[{"start":311,"end":326,"text":"Systems Engineer"}]},{"label":["Skills"],"points":[{"start":221,"end":232,"text":"SAP Security"}]},{"label":["Degree"],"points":[{"start":138,"end":169,"text":"B.Tech in Information Technology"}]},{"label":["Email Address"],"points":[{"start":81,"end":124,"text":"indeed.com/r/Kartik-Sharma/cc7951fd7809f35e\n"}]},{"label":["Location"],"points":[{"start":53,"end":57,"text":"Delhi"}]},{"label":["Location"],"points":[{"start":46,"end":50,"text":"Delhi"}]},{"label":["Companies worked at"],"points":[{"start":32,"end":43,"text":" Infosys Ltd"}]},{"label":["Designation"],"points":[{"start":14,"end":29,"text":"Systems Engineer"}]},{"label":["Name"],"points":[{"start":0,"end":12,"text":"Kartik Sharma"}]}]}
{"content": "Kasturika Borah\nTeam Member - Cisco\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Kasturika-\nBorah/9e71468914b38ee8\n\n• Software Engineer with overall 3+ years of experience in Network Monitoring system tool (EM7,\nQuicksilver) Database tool (SQL, Maria DB) and reporting tool (Splunk) in all the releases.\n• Relevant experience as a Test engineer for the releases includes Functional testing as well as\nregression testing. Testing includes writing test cases, execute them and raise bugs.\n• Relevant 1+ years of experience in handling releases for EM7 with proper documentation, Power\npack creation and Tar creation for Sprint releases.\n• Creating Splunk reports from last 6 months.\n• Competent technical person involved in requirement gathering, analysis, design and coding.\n• Experience in coding Python, SQL, and XML as per the requirement.\n• Have knowledge in Event generating using traps and Syslog's generator.\n• Exposure to Agile methodologies using Scrum Works framework, even handled scrum in the\nteam\n• Strong problem-solver who can design solutions and assist developers with issues.\n• Excellent debugging and resolution skills.\n• Good communication and interpersonal skills.\n\n• Working as Software Engineer for Cisco System India Private Ltd under Capgemini India Pvt.\nLtd.. From May 25th 2017 till nowl\n• Working as Software Engineer for Cisco System India Private Ltd under Randstad India Ltd.\nFrom Dec 15 2014 till 30th April.\n• Worked as Data Analyst for Fidelity India Financial Inc. from June 2013 till Oct 2014.\n• Worked as Billing Analyst for IBM Daksh from March 2013 to June 2013.\n\nWilling to relocate to: Bengaluru, Karnataka\n\nWORK EXPERIENCE\n\nTeam Member\n\nCisco -\n\nOctober 2017 to Present\n\nEnvironment: Splunk\nTechnologies: SPL command\n\nResponsibilities\n• Involvement writing Splunk programming language and designing the report dashboard\n• Following Agile methodology\n• Develop the code on the design in splunk.\n• Unit Testing and code review\n\nSenior developer and tester\n\nhttps://www.indeed.com/r/Kasturika-Borah/9e71468914b38ee8?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Kasturika-Borah/9e71468914b38ee8?isid=rex-download&ikw=download-top&co=IN\n\n\nCisco -\n\nDecember 2014 to Present\n\nEnvironment: EM7 platform, Quicksilver, SQL, oracle Toad\nTechnologies: Python coding, xml coding, SQL query writing\n\nDescription\nCisco Systems, Inc. (known as Cisco) is an American multinational technology conglomerate\nheadquartered in San José, California, that develops, manufactures, and sells networking\nhardware, telecommunications equipment, and other high-technology services and products\n(www.cisco.com)\n\nResponsibilities\n1. Developer of individual task on each release by weekly\n• Need to do coding for new requirement.\n• Also need to do end to end testing of all the events including Traps and Syslogs.\n2. Database and Infrastructure Monitoring and Alerting related to device.\n3. Involvement in documentation of release notes and preparation of a Regression testing at the\nend of each release.\n\nTeam Member\n\nCisco -\n\nDecember 2014 to December 2017\n\nEnvironment: INFOVISTA (Vportal)\nTechnologies: MS-Excel (sort, VLOOKUP), PPT\n\nResponsibilities\n• Involvement in generating performance reports for certain customers at the starting of every\nmonth\n• Gathering the data from the INFOVISTA portal and sort it out as per month in the excel and\ndesign the graphs for last consecutive\n• Responsible for each data uploaded to the excel sheet and reviewing it before delivering\n\nFidelity national financial -\n\nJune 2013 to October 2014\n\nRole: QA and Report handling for the team\nTechnologies: MS-Excel (sort, VLOOKUP), PPT, MS-Outlook\n\nResponsibilities\n• Involvement in generating performance reports for the team at the end of each day and monthly\nbased\n• Responsible for each data uploaded to the excel sheet and sending it to the team manager\n\nEDUCATION\n\nCompucom Insitute of Information Technology\n\n\n\nrajasthan University\n\n2012\n\nSKILLS\n\nDatabase (3 years), Python (3 years), Splunk (Less than 1 year), SQL (3 years), xml (3 years)\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\n\n• Programming Languages: Python, XML\n• Database: Maria-DB, sql\n• Cisco Monitoring Tools: EM7\n• Operating Systems: Windows/XP\n• Reporting Tools: Vportal, Splunk\n• Application & Web Servers: Sciencelogic (EM7), Syslog sender, Relay server.\n• Data Structure Knowledge: Intermediate","annotation":[{"label":["Companies worked at"],"points":[{"start":4186,"end":4190,"text":"Cisco"}]},{"label":["Skills"],"points":[{"start":4121,"end":4398,"text":"• Programming Languages: Python, XML\n• Database: Maria-DB, sql\n• Cisco Monitoring Tools: EM7\n• Operating Systems: Windows/XP\n• Reporting Tools: Vportal, Splunk\n• Application & Web Servers: Sciencelogic (EM7), Syslog sender, Relay server.\n• Data Structure Knowledge: Intermediate"}]},{"label":["Skills"],"points":[{"start":3984,"end":4077,"text":"Database (3 years), Python (3 years), Splunk (Less than 1 year), SQL (3 years), xml (3 years)\n"}]},{"label":["Graduation Year"],"points":[{"start":3970,"end":3973,"text":"2012"}]},{"label":["College Name"],"points":[{"start":3948,"end":3968,"text":"rajasthan University\n"}]},{"label":["College Name"],"points":[{"start":3901,"end":3944,"text":"Compucom Insitute of Information Technology\n"}]},{"label":["Companies worked at"],"points":[{"start":3061,"end":3065,"text":"Cisco"}]},{"label":["Designation"],"points":[{"start":3048,"end":3058,"text":"Team Member"}]},{"label":["Companies worked at"],"points":[{"start":2402,"end":2406,"text":"Cisco"}]},{"label":["Companies worked at"],"points":[{"start":2372,"end":2376,"text":"Cisco"}]},{"label":["Companies worked at"],"points":[{"start":2208,"end":2212,"text":"Cisco"}]},{"label":["Companies worked at"],"points":[{"start":1690,"end":1694,"text":"Cisco"}]},{"label":["Designation"],"points":[{"start":1677,"end":1687,"text":"Team Member"}]},{"label":["Location"],"points":[{"start":1638,"end":1646,"text":"Bengaluru"}]},{"label":["Companies worked at"],"points":[{"start":1361,"end":1365,"text":"Cisco"}]},{"label":["Companies worked at"],"points":[{"start":1233,"end":1237,"text":"Cisco"}]},{"label":["Email Address"],"points":[{"start":72,"end":125,"text":"Indeed: indeed.com/r/Kasturika-\nBorah/9e71468914b38ee8"}]},{"label":["Location"],"points":[{"start":37,"end":45,"text":"Bengaluru"}]},{"label":["Companies worked at"],"points":[{"start":30,"end":34,"text":"Cisco"}]},{"label":["Designation"],"points":[{"start":16,"end":26,"text":"Team Member"}]},{"label":["Name"],"points":[{"start":0,"end":14,"text":"Kasturika Borah"}]}]}
