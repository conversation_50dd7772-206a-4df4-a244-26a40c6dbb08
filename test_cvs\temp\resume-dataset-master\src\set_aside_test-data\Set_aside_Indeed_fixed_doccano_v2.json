{"id": 1, "text": "<PERSON>vard<PERSON> K\nSenior Software Engineer\n\nBengaluru, Karnataka, Karnataka - Email me on Indeed: indeed.com/r/Govardhana-K/\nb2de315d95905b68\n\nTotal IT experience 5 Years 6 Months\nCloud Lending Solutions INC 4 Month • Salesforce Developer\nOracle 5 Years 2 Month • Core Java Developer\nLanguages Core Java, Go Lang\nOracle PL-SQL programming,\nSales Force Developer with APEX.\n\nDesignations & Promotions\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSenior Software Engineer\n\nCloud Lending Solutions -  Bangalore, Karnataka -\n\nJanuary 2018 to Present\n\nPresent\n\nSenior Consultant\n\nOracle -  Bangalore, Karnataka -\n\nNovember 2016 to December 2017\n\nStaff Consultant\n\nOracle -  Bangalore, Karnataka -\n\nJanuary 2014 to October 2016\n\nAssociate Consultant\n\nOracle -  Bangalore, Karnataka -\n\nNovember 2012 to December 2013\n\nEDUCATION\n\nB.E in Computer Science Engineering\n\nAdithya Institute of Technology -  Tamil Nadu\n\nSeptember 2008 to June 2012\n\nhttps://www.indeed.com/r/Govardhana-K/b2de315d95905b68?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Govardhana-K/b2de315d95905b68?isid=rex-download&ikw=download-top&co=IN\n\n\nSKILLS\n\nAPEX. (Less than 1 year), Data Structures (3 years), FLEXCUBE (5 years), Oracle (5 years),\nAlgorithms (3 years)\n\nLINKS\n\nhttps://www.linkedin.com/in/govardhana-k-61024944/\n\nADDITIONAL INFORMATION\n\nTechnical Proficiency:\n\nLanguages: Core Java, Go Lang, Data Structures & Algorithms, Oracle\nPL-SQL programming, Sales Force with APEX.\nTools: RADTool, Jdeveloper, NetBeans, Eclipse, SQL developer,\nPL/SQL Developer, WinSCP, Putty\nWeb Technologies: JavaScript, XML, HTML, Webservice\n\nOperating Systems: Linux, Windows\nVersion control system SVN & Git-Hub\nDatabases: Oracle\nMiddleware: Web logic, OC4J\nProduct FLEXCUBE: Oracle FLEXCUBE Versions 10.x, 11.x and 12.x\n\nhttps://www.linkedin.com/in/govardhana-k-61024944/", "meta": {}, "annotation_approver": null, "labels": [[858, 889, "College Name"], [821, 856, "Degree"], [744, 750, "Companies worked at"], [722, 742, "Designation"], [658, 664, "Companies worked at"], [640, 656, "Designation"], [574, 580, "Companies worked at"], [555, 573, "Designation"], [470, 493, "Companies worked at"], [444, 469, "Designation"], [93, 137, "Email Address"], [13, 38, "Designation"], [0, 12, "Name"], [39, 70, "Location"], [158, 174, "Years of Experience"], [175, 202, "Companies worked at"], [203, 210, "Years of Experience"], [213, 233, "Designation"], [234, 240, "Companies worked at"], [241, 256, "Years of Experience"], [259, 278, "Designation"], [497, 517, "Location"], [521, 553, "Years of Experience"], [584, 604, "Location"], [608, 638, "Years of Experience"], [668, 688, "Location"], [692, 720, "Years of Experience"], [754, 774, "Location"], [778, 808, "Years of Experience"], [893, 903, "Location"], [905, 932, "Graduation Year"], [289, 298, "Tech Tools"], [300, 307, "Tech Tools"], [362, 366, "Tech Tools"], [335, 356, "Job Specific Skills"], [308, 321, "Tech Tools"], [322, 333, "Job Specific Skills"], [1162, 1177, "Job Specific Skills"], [1189, 1197, "Tech Tools"], [1209, 1215, "Tech Tools"], [1227, 1237, "Job Specific Skills"], [1367, 1376, "Tech Tools"], [1378, 1385, "Tech Tools"], [1387, 1415, "Job Specific Skills"], [1417, 1430, "Tech Tools"], [1431, 1442, "Job Specific Skills"], [1461, 1465, "Tech Tools"], [1474, 1481, "Tech Tools"], [1483, 1493, "Tech Tools"], [1495, 1503, "Tech Tools"], [1505, 1512, "Tech Tools"], [1514, 1517, "Tech Tools"], [1529, 1535, "Tech Tools"], [1547, 1553, "Tech Tools"], [1555, 1560, "Tech Tools"], [1579, 1589, "Tech Tools"], [1591, 1594, "Tech Tools"], [1596, 1600, "Tech Tools"], [1633, 1638, "Tech Tools"], [1640, 1647, "Tech Tools"], [1671, 1674, "Tech Tools"], [1677, 1684, "Tech Tools"], [1696, 1702, "Tech Tools"], [1715, 1725, "Tech Tools"], [1726, 1730, "Tech Tools"], [1749, 1764, "Tech Tools"], [1602, 1612, "Tech Tools"], [1685, 1694, "Job Specific Skills"], [1444, 1455, "Tech Tools"]]}
{"id": 2, "text": "Harini Komaravelli\nTest Analyst at Oracle, Hyderabad\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Harini-\nKomaravelli/2659eee82e435d1b\n\n➢ 6 Yrs. of IT Experience in Manual and Automation testing.\n\nWORK EXPERIENCE\n\nQA Analyst\n\nOracle\n\nTest Analyst at Oracle, Hyderabad\n\nInfosys Ltd -  Hyderabad, Telangana -\n\nNovember 2011 to February 2016\n\nHyderabad from Nov 2011 to Feb17 2016\n➢ Worked in Tata Consultancy Services, Hyderabad from Feb 24 to Apr 11 2017\n➢ Currently working as a Test Analyst at Oracle, Hyderabad\n\nQA Analyst with 6 years of IT experience\n\nOracle\n\nEDUCATION\n\nMCA\n\nOsmania University\n\nB.Sc. in Computer Science\n\nOsmania University\n\nSKILLS\n\nFunctional Testing, Blue Prism, Qtp\n\nADDITIONAL INFORMATION\n\nArea of Expertise:\n\n➢ Familiar with Agile Methodologies.\n➢ Having knowledge in Energy (Petroleum) & Health Care domains.\n➢ Involved in preparation of Test Scenarios.\n➢ Preparing Test Data for the test cases.\n\nhttps://www.indeed.com/r/Harini-Komaravelli/2659eee82e435d1b?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Harini-Komaravelli/2659eee82e435d1b?isid=rex-download&ikw=download-top&co=IN\n\n\n➢ Experienced in development and execution of Test cases effectively.\n➢ Experienced in Functional testing, GUI testing, Smoke testing, Regression testing and\nIntegration Testing\n➢ Experienced in doing Accessibility testing of an application\n➢ Ability to understand user Requirements, Functional and Design specifications.\n➢ Good knowledge of SDLC and STLC processes.\n➢ Deciding the Severity and Priority of bugs.\n➢ Experience in using Microsoft Test Manager & Oracle Test Manager as Test Management Tools.\n➢ Having good experience in testing windows based & web based applications.\n➢ Involved in Client Interactions for reviews, issues and for any clarifications.\n➢ Web Services Testing\n➢ Writing Test Scripts in QTP, Testcomplete.\n➢ Creating Object Repositories and Function Libraries in QTP.\n➢ Enhanced QTP scripts using VB Script.\n➢ Strong experience in working with Blue Prism tool\n➢ Worked on different Environments like Windows Application & Web Application\n\nTechnical Skills:\n\n❑ Test Automation Tools: Blue Prism, QTP 10.0, Testcomplete\n❑ Test Management Tool: Microsoft Test Manager, Oracle Test Manager & JIRA\n❑ Databases: Oracle 10g, SQL Server.\n\n❑ Operating Systems: Windows 7\n\nProject 1:\nTitle: Cadence\nClient: Baker Hughes\n\nTechnologies: Microsoft Visual Studio and Microsoft Team Foundation Server\n\nClient Background:\nAn oilfield services company delivering focused efforts on shale gas and other oilfield services.\nIt provides services, tools and software for drilling and formation evaluation, well completion,\nproduction management, seismic data collection and interpretation.\n\nProject Description:\nAUT (Application under test) is the next generation revolutionary, robust, easy to use scalable\nwell site data acquisition processing and interpretation system for Client's Drilling Services to\ndeliver services that meets cross divisional business requirements consistently.\n\nProject 2:\n\nDescription:\nParagon supports your entire care team with one tool that your clinicians need to help deliver\nthe best patient care. Designed by physicians, nurses, pharmacists and mid level providers that\nhave a first-hand understanding of clinical workflow needs, Paragon clinical applications allow\nyour caregivers to focus on what matters most; spending time caring for patients. Since Paragon\nis fully-integrated across all applications and built around a single patient database, information\n\n\n\nentered anywhere in the system is immediately available to the entire care team. Immediate\naccess not only helps clinicians make better treatment decisions - it also helps promote patient\nsafety. Paragon offers a broad suite of multidisciplinary clinical software solutions together with\nanytime, anywhere access to the complete patient record.\n\nResponsibilities:\n\n• Performed Smoke testing and Regression testing.\n• Involved in Generating and Executing Test Script using Quick Test Pro & Blue Prism\n• Usability and User Interface Testing.\n• Involved in Defect tracking and reporting the bugs using TFS\n• Participated in frequent walk-through meetings with Internal Quality Assurance groups and with\ndevelopment groups.\n• Participated in client calls and clarifying the doubts by having AT&T sessions\n• Involved in functional, regression and smoke testing to validate the application data changes\ndone in windows application\n• Certifying the build status by running the scripts as part of smoke testing\n\nProject 3:\n\nDescription:\nFood & Beverages R&A: Easily manage business across multiple locations while reducing IT\ncost and complexity. Cloud-based point-of-sale (POS) solutions enable centralized enterprise\nmanagement with lower upfront costs and a smaller footprint.\n\nResponsibilities:\n\n• Performed Functional testing and Regression testing.\n• Involved in Generating and Executing Test Scripts using Blue Prism tool and Open script\n• Involved in preparing bots using Blue Prism tool.\n• Accessibility testing of the web application\n• Involved in Defect tracking and reporting the bugs using JIRA\n• WebServices testing by calling API's to export the data", "meta": {}, "annotation_approver": null, "labels": [[638, 658, "College Name"], [612, 637, "Degree"], [591, 611, "College Name"], [587, 590, "Degree"], [526, 536, "Designation"], [515, 524, "Location"], [507, 513, "Companies worked at"], [491, 503, "Designation"], [429, 438, "Location"], [352, 361, "Location"], [246, 258, "Designation"], [226, 236, "Designation"], [177, 207, "Designation"], [150, 155, "Years of Experience"], [35, 41, "Companies worked at"], [19, 31, "Designation"], [0, 18, "Name"], [43, 74, "Location"], [97, 146, "Email Address"], [262, 292, "Companies worked at"], [296, 316, "Location"], [320, 350, "Years of Experience"], [367, 389, "Years of Experience"], [402, 427, "Companies worked at"], [444, 465, "Years of Experience"], [542, 549, "Years of Experience"], [238, 244, "Tech Tools"], [667, 685, "Job Specific Skills"], [687, 697, "Tech Tools"], [699, 702, "Tech Tools"], [764, 769, "Job Specific Skills"], [807, 825, "Job Specific Skills"], [828, 839, "Job Specific Skills"], [878, 892, "Job Specific Skills"], [1189, 1199, "Job Specific Skills"], [1230, 1248, "Job Specific Skills"], [1250, 1261, "Job Specific Skills"], [1263, 1276, "Job Specific Skills"], [1278, 1296, "Job Specific Skills"], [1301, 1320, "Job Specific Skills"], [1413, 1425, "Job Specific Skills"], [1427, 1463, "Job Specific Skills"], [1578, 1600, "Tech Tools"], [1603, 1622, "Tech Tools"], [1809, 1829, "Job Specific Skills"], [1856, 1859, "Tech Tools"], [1861, 1873, "Tech Tools"], [1932, 1935, "Tech Tools"], [1948, 1951, "Tech Tools"], [1966, 1968, "Tech Tools"], [2013, 2023, "Tech Tools"], [2069, 2076, "Tech Tools"], [2152, 2162, "Tech Tools"], [2164, 2172, "Tech Tools"], [2174, 2186, "Tech Tools"], [2189, 2204, "Job Specific Skills"], [2211, 2233, "Tech Tools"], [2235, 2241, "Tech Tools"], [2242, 2254, "Job Specific Skills"], [2257, 2261, "Tech Tools"], [2274, 2285, "Tech Tools"], [2287, 2297, "Tech Tools"], [2321, 2330, "Tech Tools"], [2366, 2392, "Companies worked at"], [2394, 2417, "Tech Tools"], [3923, 3936, "Job Specific Skills"], [3941, 3959, "Job Specific Skills"], [4685, 4716, "Tech Tools"], [4850, 4868, "Job Specific Skills"], [4873, 4891, "Job Specific Skills"], [5141, 5145, "Tech Tools"], [924, 934, "Job Specific Skills"], [1485, 1489, "Job Specific Skills"], [1494, 1498, "Job Specific Skills"], [1626, 1641, "Job Specific Skills"], [1677, 1723, "Job Specific Skills"], [1840, 1852, "Job Specific Skills"], [2264, 2273, "Job Specific Skills"], [4000, 4011, "Job Specific Skills"], [4018, 4032, "Tech Tools"], [4035, 4045, "Tech Tools"], [4048, 4084, "Job Specific Skills"], [4932, 4944, "Job Specific Skills"], [4951, 4961, "Tech Tools"], [4971, 4982, "Tech Tools"], [5018, 5028, "Tech Tools"], [5037, 5058, "Job Specific Skills"], [5096, 5111, "Job Specific Skills"], [5148, 5167, "Job Specific Skills"]]}
{"id": 3, "text": "Hartej Kathuria\nData Analyst Intern - Oracle Retail\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Hartej-Kathuria/04181c5962a4af19\n\nWilling to relocate to: Delhi - Bangalore, Karnataka - Gurgaon, Haryana\n\nWORK EXPERIENCE\n\nData Analyst Intern\n\nOracle Retail -  Bengaluru, Karnataka -\n\nJune 2017 to Present\n\nJob Responsibilities:\no As an intern part of the Global Retail Insights team at Oracle Retail,\nwork involved creating a data oriented buisness case based using high\nlevel trends for various retailers using Excel and SQL.\no Forecasting Sales with use of various statistical Modelling Methods\nusing SQL and R\no Market Basket Analysis using transactional data of retailers using SQL and R\n\nEDUCATION\n\nStatistics and Probability\n\nManipal University\n\nMay 2018\n\nB. Tech in Electrical and Electronics in Embedded Systems\n\nMIT, Manipal University\n\nMay 2016\n\nSKILLS\n\nPython (2 years), SQL. (1 year), NOSQL (1 year), R (2 years), Machine Learning (2 years)\n\nPUBLICATIONS\n\nPost-operative life expectancy in lung cancer patients\n\nThe objective of the project was to build an efficient predictive model based\non a predefined dataset to predict whether the patient survives or dies within one year of the\noperation. The dataset given has 17 variables: 12 nominal, 2 ordinal and 3 numerical. The target\nvariable has value true if the patient dies within one year of the operation else false if he survives.\nTool used: R\n\nhttps://www.indeed.com/r/Hartej-Kathuria/04181c5962a4af19?isid=rex-download&ikw=download-top&co=IN\n\n\nPredict the Happiness (Sentimental Analysis)\n\nThe objective of this project was to build a binary classifcation model for the data provided by\nTripAdvisor consisiting of a sample of hotel reviews provided by customers.The model built can\nbe used by them to understand the hotels\nlisted by them.Tool Used: R\n\nPredict Network attacks\n\nThe objective of this project was to build a multi-class classification model to predict the type of\nattack for an internet network company in Japan which has\nbeen facing huge losses due to malicious server attacks.The train dataset has\n18 numerical features and 23 categorical features.The target variable has\nthree classes.Tool Used: Python\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLSET\n\n• Languages & Technologies: Python, R, SQL, NoSQL, Predictive Modelling,\nMarket Basket Analysis, Sentimental Analysis, Clustering, Bash\nScripting (Preliminary), Socket Programming, Java (Preliminary)\n\n• Tools: R Studio, Jupyter, GIT, Sublime, MATLAB, Linux, KVM, Virtual Box,\nOpen VZ, Oracle SQL Developer, MySQL, MongoDB, Excel", "meta": {}, "annotation_approver": null, "labels": [[837, 856, "College Name"], [773, 830, "Degree"], [743, 762, "College Name"], [714, 741, "Degree"], [233, 252, "Designation"], [96, 141, "Email Address"], [38, 52, "Companies worked at"], [16, 35, "Designation"], [0, 15, "Name"], [53, 73, "Location"], [167, 195, "Location"], [198, 214, "Location"], [254, 267, "Companies worked at"], [271, 291, "Location"], [295, 315, "Years of Experience"], [397, 410, "Companies worked at"], [763, 771, "Graduation Year"], [857, 865, "Graduation Year"], [523, 528, "Tech Tools"], [533, 536, "Tech Tools"], [614, 617, "Tech Tools"], [622, 623, "Tech Tools"], [875, 881, "Tech Tools"], [893, 896, "Tech Tools"], [908, 913, "Tech Tools"], [924, 925, "Tech Tools"], [937, 953, "Job Specific Skills"], [1420, 1421, "Tech Tools"], [1547, 1567, "Job Specific Skills"], [1829, 1830, "Tech Tools"], [2000, 2005, "Location"], [2193, 2199, "Tech Tools"], [2273, 2279, "Tech Tools"], [2281, 2282, "Tech Tools"], [2284, 2287, "Tech Tools"], [2289, 2294, "Tech Tools"], [2296, 2316, "Job Specific Skills"], [2318, 2340, "Job Specific Skills"], [2342, 2362, "Job Specific Skills"], [2364, 2374, "Job Specific Skills"], [2376, 2390, "Tech Tools"], [2406, 2424, "Job Specific Skills"], [2426, 2430, "Tech Tools"], [2455, 2463, "Tech Tools"], [2465, 2472, "Tech Tools"], [2474, 2477, "Tech Tools"], [2479, 2486, "Tech Tools"], [2488, 2494, "Tech Tools"], [2496, 2501, "Tech Tools"], [2503, 2506, "Tech Tools"], [2508, 2519, "Tech Tools"], [2521, 2528, "Tech Tools"], [2530, 2540, "Tech Tools"], [2552, 2557, "Tech Tools"], [2559, 2566, "Tech Tools"], [2568, 2573, "Tech Tools"], [347, 353, "Designation"], [540, 557, "Job Specific Skills"], [578, 599, "Job Specific Skills"], [626, 648, "Job Specific Skills"], [693, 696, "Tech Tools"], [701, 702, "Tech Tools"]]}
{"id": 4, "text": "Ijas Nizamuddin\nAssociate Consultant - State Street\n\nIrinchayam B.O, Kerala - Email me on Indeed: indeed.com/r/Ijas-\nNizamuddin/6748d77f76f94eed\n\nWith close to 3 years of experience in IT industry, I have had excellent exposure to design,\ndevelopment and implementation of Client Server Applications in various domains such as\nBanking and Finance concepts. I have been involved in various software Development projects\nin Open System environment.\n\nWORK EXPERIENCE\n\nAssociate Consultant\n\nOracle Corporation -\n\nJune 2011 to Present\n\nState Street Global Advisors (SSgA) is the asset management business of State Street\nCorporation, one of the world's leading providers of financial services to institutional investors1,\nwith a heritage dating back over two centuries. Backed by the strength and stability of the State\nStreet organization, SSgA makes continual investments in asset management and client service\nplatform, resulting in a client-focused, solutions-driven orientation .BrokerViews is the application\nwhich list all the details about the counterparties who invest their securities in State Street.The\ndetails also include ratings given by Bloomberg.\n\nResponsibilities: Development, Testing and support.\nSoftware Used: Java, GWT\n\nAssociate Consultant\n\nOracle Corporation -  Bangalore, Karnataka -\n\nMay 2010 to June 2011\n\nThis project is actually a redesign of an existing client website. The client website was designed\non Java Server Pages (JSP) and our aim was to change it into a more dynamic web page using\nAdobe Flex. At first we changed the home page screen of the client website. After the successful\ncompletion of that we incorporated flex in to the account section also. This data which is obtained\nfrom DataBase is taken by the flex using a remote procedure call and the data is shown to the\nuser. With the use of Advanced Data Grids, Charts(including Bar and Pie Charts) the site increased\nthe readability and understandability of the users who were previously using the pages on java\nserver pages. This site developed by us won the IMC (Interactive Media Council)'s outstanding\nachievement award in Financial information. The judge evaluate website based on 5 criteria:\nDesign, Content, Feature Functionality, Usability and Standard Compliance. Our website scored\n475 out of a maximum of 500 points.\n.\nResponsibilities: Development, Testing and support.\nSoftware Used: Java, Adobe Flex\n\nhttps://www.indeed.com/r/Ijas-Nizamuddin/6748d77f76f94eed?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Ijas-Nizamuddin/6748d77f76f94eed?isid=rex-download&ikw=download-top&co=IN\n\n\nFramework: Springs, MVC\n\nAssociate Consultant\n\nOracle Corporation -  Bangalore, Karnataka -\n\nFebruary 2010 to April 2010\n\nDescription: Development of Basel II Application, Basel II is the second of the Basel Accords,\nwhich are recommendations on banking laws and regulations issued by the Basel Committee on\nBanking Supervision. The purpose of Basel II, which was initially published in June 2004, is to\ncreate an international standard that banking regulators can use when creating regulations about\nhow much capital banks need to put aside to guard against the types of financial and operational\nrisks banks face. In practice, Basel II attempts to accomplish this by setting up rigorous risk\nand capital management requirements designed to ensure that a bank holds capital reserves\nappropriate to the risk the bank exposes itself to through its lending and investment practices.\n\nThe New Accord includes several methodologies for determining a bank's risk-based capital\nrequirements for credit, market and operational risk. For Risk Based Capital (RBC), Credit Usage\n(CU) and Stress Test (ST), the methodologies that will be used for repo style transactions are\nSimple VaR if the collateral is eligible. If the collateral is ineligible then the Wholesale loan\napproach will be utilized or the collateral will be reduced to zero in the Simple VaR.\n.\nRoles and Responsibilities: Development, Testing and support.\nSoftware Used: Oracle 9i\n\nOTHER PROJECTS AND REAL TIME TRAINING:\n\nRTRM(Railway Ticketing System Through Mobile)\nA mobile based real time application with many exciting features like checking pnr status, train\navailability, trains between stations etc. This application was done in j2me and it uses weblogic\nas server and MSSQL as the database.\n\nUndergone a mandatory Training on Finance By Oracle Corporation\n\nEDUCATION\n\nBirla Institute Of Technology -  Pilani, Rajasthan\n\n2011\n\nBachelor of Technology in Computer Science\n\nUniversity College Of Engineering, University Of Kerala\n\n2005 to 2009\n\nADDITIONAL INFORMATION\n\nSKILL SET:\n\n\n\nLanguages: Core Java\nFront end/GUI Tools programming: Adobe Flex, GWT\nDatabase: Oracle 10g\nIDE: Eclipse, FlexBuilder\nFrameWorks: Spring(Basics), MVC frame work\nOperating System: Windows, Linux, Unix", "meta": {}, "annotation_approver": null, "labels": [[4543, 4576, "College Name"], [4499, 4541, "Degree"], [4493, 4498, "Graduation Year"], [4441, 4471, "College Name"], [4410, 4428, "Companies worked at"], [2654, 2672, "Companies worked at"], [2632, 2652, "Designation"], [1260, 1278, "Companies worked at"], [1238, 1258, "Designation"], [487, 505, "Companies worked at"], [465, 485, "Designation"], [97, 144, "Email Address"], [16, 36, "Designation"], [0, 15, "Name"], [39, 51, "Companies worked at"], [53, 75, "Location"], [160, 167, "Years of Experience"], [509, 529, "Years of Experience"], [603, 627, "Companies worked at"], [1282, 1302, "Location"], [1306, 1327, "Years of Experience"], [2676, 2696, "Location"], [2700, 2727, "Years of Experience"], [4474, 4491, "Location"], [4578, 4598, "College Name"], [4600, 4612, "Graduation Year"], [273, 299, "Job Specific Skills"], [531, 566, "Companies worked at"], [809, 840, "Companies worked at"], [949, 965, "Soft Skills"], [933, 947, "Soft Skills"], [1148, 1157, "Companies worked at"], [1178, 1189, "Job Specific Skills"], [1191, 1198, "Job Specific Skills"], [1203, 1210, "Job Specific Skills"], [1227, 1231, "Tech Tools"], [1233, 1236, "Tech Tools"], [1431, 1454, "Tech Tools"], [1519, 1529, "Tech Tools"], [2340, 2351, "Job Specific Skills"], [2353, 2360, "Job Specific Skills"], [2365, 2372, "Job Specific Skills"], [2389, 2393, "Tech Tools"], [2395, 2405, "Tech Tools"], [2618, 2625, "Tech Tools"], [2627, 2630, "Tech Tools"], [3986, 3997, "Job Specific Skills"], [3999, 4006, "Job Specific Skills"], [4011, 4018, "Job Specific Skills"], [4035, 4044, "Tech Tools"], [4301, 4305, "Tech Tools"], [4341, 4346, "Tech Tools"], [4663, 4672, "Tech Tools"], [4706, 4716, "Tech Tools"], [4718, 4721, "Tech Tools"], [4732, 4742, "Tech Tools"], [4748, 4755, "Tech Tools"], [4757, 4768, "Tech Tools"], [4781, 4787, "Tech Tools"], [4797, 4800, "Tech Tools"], [4830, 4837, "Tech Tools"], [4839, 4844, "Tech Tools"], [4846, 4850, "Tech Tools"], [389, 409, "Job Specific Skills"], [422, 445, "Job Specific Skills"], [1832, 1851, "Job Specific Skills"], [4673, 4686, "Job Specific Skills"], [4693, 4704, "Job Specific Skills"], [4722, 4730, "Job Specific Skills"]]}
{"id": 5, "text": "Imgeeyaul Ansari\njava developer\n\nPune, Maharashtra - Email me on Indeed: indeed.com/r/Imgeeyaul-Ansari/a7be1cc43a434ac4\n\nWilling to relocate to: Pune, Maharashtra\n\nWORK EXPERIENCE\n\nApplication Developer\n\nOracle Financial Software Services -  Pune, Maharashtra -\n\nAugust 2016 to Present\n\n• Wrote Services in java using data annotation which in turn were used for creating other files\nusing code generation tool.\n\n• Jar of services with related utility files such as DTOs are deployed on the Host side.\n\n• Host side is hosted on Tomcat Server where proxy files are present.\n\n• Created jsff page for UI, and wrote action, helper, assembler, backing bean for UI side business\nLogic made entries in collectionAppModule, PageDef.\n\n• Used ADF & MVC architecture for building application.\n\nUsed JUnit for Testing services,Algorithms.\nAlso made test Suites For running multiple test case at one go.\n\nUsed Eclipse Debugger for Fixing Service Related Jiras.\nMade Use of Hot Deployment for Fixing Ui related Bugs on UI side which was run on Weblogic\nServer. \nUsed JAWS Reader for solving accessibility Related jiras and IA plugin.\n\nUsed Java to write Batches for fetching of Bulk Data at Regular Interval.\nIt created Thread for Multitasking to reduce the time for processing.\n\nEDUCATION\n\nBachelor of Enginerring in Information Technology\n\nArmy institute of technology -  Pune, Maharashtra\n\n2012 to 2016\n\nCBSE in Physics, Chemistry, Mathematics\n\nRashtriya Military School Bangalore -  Bengaluru, Karnataka\n\nhttps://www.indeed.com/r/Imgeeyaul-Ansari/a7be1cc43a434ac4?isid=rex-download&ikw=download-top&co=IN\n\n\n2010 to 2011\n\nCBSE in Mathematics and English\n\nRashtriya Military School Bangalore -  Bengaluru, Karnataka\n\n2008 to 2009\n\nSKILLS\n\nJAVA (1 year), CSS (1 year), HTML (1 year), MYSQL (1 year), JAVASCRIPT (Less than 1 year),\nAngularjs (1 year), Oracle Pl/Sql\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\nProgramming Languages :C/C++, angular, java, java servlet, HTML, java script, MySQL, css, jsff.\n\nOperating Systems Linux, Windows, Android\n\nORM Eclipse Link, Hibernate\n\nFramework & tools :ADF, Eclipse, Android Studio, Git, Selenium, Code blocks, Net beans, R studio,\nTortoise SVN.", "meta": {}, "annotation_approver": null, "labels": [[1643, 1678, "College Name"], [1610, 1642, "Degree"], [1433, 1468, "College Name"], [1327, 1356, "College Name"], [1276, 1325, "Degree"], [204, 238, "Companies worked at"], [181, 202, "Designation"], [17, 31, "Designation"], [0, 16, "Name"], [33, 50, "Location"], [73, 119, "Email Address"], [145, 162, "Location"], [242, 259, "Location"], [263, 285, "Years of Experience"], [1359, 1376, "Location"], [1378, 1390, "Graduation Year"], [1472, 1492, "Location"], [1392, 1431, "Degree"], [1596, 1608, "Graduation Year"], [1682, 1702, "Location"], [1704, 1716, "Graduation Year"], [307, 311, "Tech Tools"], [694, 713, "Tech Tools"], [715, 722, "Tech Tools"], [732, 735, "Tech Tools"], [738, 741, "Tech Tools"], [787, 792, "Tech Tools"], [836, 847, "Job Specific Skills"], [896, 903, "Tech Tools"], [940, 945, "Tech Tools"], [1052, 1056, "Tech Tools"], [1125, 1129, "Tech Tools"], [1726, 1730, "Tech Tools"], [1741, 1744, "Tech Tools"], [1755, 1759, "Tech Tools"], [1770, 1775, "Tech Tools"], [1786, 1796, "Tech Tools"], [1817, 1826, "Tech Tools"], [1837, 1846, "Tech Tools"], [1847, 1850, "Tech Tools"], [1916, 1921, "Tech Tools"], [1923, 1930, "Tech Tools"], [1932, 1936, "Tech Tools"], [1938, 1950, "Tech Tools"], [1952, 1956, "Tech Tools"], [1958, 1969, "Tech Tools"], [1971, 1976, "Tech Tools"], [1978, 1981, "Tech Tools"], [1983, 1987, "Tech Tools"], [2008, 2013, "Tech Tools"], [2015, 2022, "Tech Tools"], [2023, 2049, "Tech Tools"], [2051, 2060, "Tech Tools"], [2081, 2084, "Tech Tools"], [2086, 2093, "Tech Tools"], [2095, 2109, "Tech Tools"], [2111, 2114, "Tech Tools"], [2116, 2124, "Tech Tools"], [2126, 2137, "Tech Tools"], [2139, 2148, "Tech Tools"], [2150, 2158, "Tech Tools"], [2160, 2172, "Tech Tools"], [527, 540, "Tech Tools"], [583, 587, "Tech Tools"], [597, 599, "Job Specific Skills"], [655, 657, "Job Specific Skills"], [797, 813, "Job Specific Skills"], [1029, 1044, "Tech Tools"]]}
{"id": 6, "text": "Jay Madhavi\nNavi Mumbai, Maharashtra - Email me on Indeed: indeed.com/r/Jay-\nMadhavi/1e7d0305af766bf6\n\nI look forward to being associated with a growth - oriented, learning firm and\ncontribute my skills for its success. This will allow me to grow both professionally\nas well as an individually.\n\nWORK EXPERIENCE\n\nNIIT -\n\n2016 to 2016\n\nB+ Average\nAdvanced\n\nSQL Oracle -\n\n2016 to 2016\n\nB+ Average\n\nMSCIT -\n\n2011 to 2011\n\nA Completed\nTechnical Institution\n\nProjects undertaken (BE):\n\nS.N. Project Title Name of company/college Nature of the Remarks\nproject\n\n1 Android Based Saraswati College Of Android Completed\nEmployee Tracker Engineering Application\nSystem\n\n2 An innovative Saraswati College Of Compilation Completed\napproach for Engineering\ncode optimization\n\n3 Simple Website Saraswati College Of Website related to Completed\nRelated to Engineering information of\nClassical Italian cars\nCars\n\nAbout Myself:\n• I am Capable and Hardworking, and can adapt to New Surroundings.\n\nhttps://www.indeed.com/r/Jay-Madhavi/1e7d0305af766bf6?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Jay-Madhavi/1e7d0305af766bf6?isid=rex-download&ikw=download-top&co=IN\n\n\n• I Can Face Challenges with confidence and would give my best shot under Stressful situations.\n\n.. 03 : 03:\n\nEDUCATION\n\nBE (Computer Science) in Computer Science\n\nSaraswati College Of Engineering, Kharghar -  Mumbai, Maharashtra\n\n2014 to 2017\n\nHSC in Computer science\n\nAcharya College Chembur -  Mumbai, Maharashtra\n\n2011 to 2013\n\nSSC\n\nState Board\n\n2011\n\nADDITIONAL INFORMATION\n\n• Ability to accept responsibilities and give best performance to complete the given work\nefficiently.\n• To take up challenging jobs & work as a team.\n• To positively accept my Mistake.", "meta": {}, "annotation_approver": null, "labels": [[1440, 1463, "College Name"], [1415, 1439, "Degree"], [1334, 1366, "College Name"], [1291, 1333, "Degree"], [51, 101, "Email Address"], [0, 11, "Name"], [12, 36, "Location"], [1368, 1399, "Location"], [1401, 1413, "Graduation Year"], [1467, 1486, "Location"], [1488, 1500, "Graduation Year"], [313, 317, "Companies worked at"], [321, 333, "Graduation Year"], [356, 359, "Tech Tools"], [360, 366, "Tech Tools"], [917, 924, "Soft Skills"], [929, 940, "Soft Skills"], [1199, 1209, "Soft Skills"], [557, 564, "Job Specific Skills"], [1685, 1699, "Soft Skills"]]}
{"id": 7, "text": "Jitendra Babu\nFI/CO Consultant in Tech Mahindra - SAP FICO\n\nChennai, Tamil Nadu - Email me on Indeed: indeed.com/r/Jitendra-Babu/bc3ea69a183395ed\n\n• Having 3.2-years of SAP experience as sap FICO Consultant\n• Involved in Implementation and support projects\n• Basic knowledge in simple finance.\n• Proficient in SAP's ASAP Methodology and well versed with business process, its mapping &\nconfiguration in SAP\n• Good inter-personal skills, strong analytical ability and problem-solving capabilities\n• Ability to make timely and sound decisions based on logical assumptions, factual information.\n• Ability to work as a team member supporting co-workers and the commitment to the overall\nsuccess of a group.\n• Work effectively with internal customers, co-workers and management.\n• Knowledge on integration of FI with other modules like MM and SD\n• Experience in GL, AP, and AR\n• Good communication skills with an aptitude to interact with the clients for Production support\n• Good Understanding of business process in Industry.\n• Expertise on data uploading tolls-LSMW\n• Good exposure on writing validation and substitution rules for business requirements and\nwriting queries.\n• Interacting with the end user and finalizing the user requirement\n• Coordinating with the other teams for SAP integration aspects\n• Well exposure on designing the organization structure and setting it up in SAP in association\nwith other members from different streams of the implementation team.\n• Detail oriented, quick learner, good listener with strong problem solving skills.\n\nSAP FICO SKILL SET:\n\nFinance\n• Financial Accounting- General Ledger Accounting FI-G/L (New GL),\n• Accounts Payable-FI-A/P,\n• Accounts Receivable FI-A/R\n\nWORK EXPERIENCE\n\nFI/CO Consultant in Tech Mahindra\n\nSAP FICO -\n\n2015 to Present\n\nSAP FICO Consultant\n\nSAP FICO -\n\nApril 2017 to May 2018\n\nProject & Role Description:\n\nhttps://www.indeed.com/r/Jitendra-Babu/bc3ea69a183395ed?isid=rex-download&ikw=download-top&co=IN\n\n\nFossil Group, Inc., together with its subsidiaries, designs, develops, markets, and distributes\nconsumer fashion accessories. The company's principal products include a line of men's and\nwomen's fashion watches and jewelry, handbags, small leather goods, belts, and sunglasses. It\noffers its products under its proprietary brands, such as FOSSIL, MICHELE, MISFIT, RELIC, SKAGEN,\nand ZODIAC, as well as under the licensed brands, including ARMANI EXCHANGE, CHAPS, DIESEL,\nDKNY, EMPORIO ARMANI, KARL LAGERFELD, KATE SPADE NEW YORK, MARC JACOBS, MICHAEL\nKORS, and TORY BURCH. The company sells its products through company-owned retail stores,\ndepartment stores, specialty retail stores, specialty watch.\nRoles and Responsibilities:\n• Resolving Day to Day issues as well as providing Solution for better Business Processes.\n• Adhere to the SLA timelines\n• Coordinating with technical consultants for modifications in outputs and program changes\n• Handling various support issues be it process, configuration or functionality issue.\n• Migrated Transaction and Master Data using migration tool LSMW\n• Effective defect tracking, reporting and documenting the deliverables\n• Handling knowledge transfer sessions to the new comers in the team\n• Participation in regular team members meetings who are part of this support project and SAP\nFICO team in scope.\n• Conducting the Core-Team Training.\n• Configuring new payment terms\n• Defined new payment terms as per the business requirements for Vendors.\n• Actively involved in Table maintenance\n• Preparing the Root cause analysis, Back log report and SLA adherence report inputs to team\nleader from time to time.\n• Supporting the end users while running the Automatic Payment Program\n• Creating new Validations and Substitutions for posting transactions requirements.\n• Working closely with all members of the team to clear the backlog tickets.\n• Good Exposure towards Ticketing tool\n• Resolved Automatic payment program issues / bugs in implementing SAP OSS Notes.\n\nDOMAIN EXPERIENCE:\n• Worked under Auditor for 6 months as a assistant in Tally ERP 9.0 package.\n\nSAP FICO -\n\nMarch 2015 to March 2017\n\nProject & Role Description:\nFord India Private Limited manufactures, distributes, and exports cars, SUVs, sedans, and\nlow displacement engines. It offers total maintenance, extended warranty, scheduled service,\npreferred insurance, and mobile service plans. The company sells its products through\ndealers to individuals, fleet organizations/rental companies, corporate, embassy/consulates and\nprofessionals, and government organizations; and sales and service outlets.\nRoles and Responsibilities:\n• Exposure towards value ASAP methodology\n• Co-ordination with core team and Preparation of Businesses Blue Print for the complete business\nprocess.\n• Documented in AS IS & TO BE document\n• Involved in WRICEF elements\n• Involved in positive, negative & random testing\n\n\n\n• Involved in data upload\n• Involved in SAP customizing, configuring and processing the Business Transactions in Finance.\n• Configured and Customized the G/L account master records, G/L Account groups\n• Define field status variant. Define number range\n• Expertise on data uploading tolls-LSMW\n• Create and Maintain the Master Accounts for GL,\n• Creating vendors and customer master data\n• Configuration of automatic payment program\n• Exposure on writing validation and substitution rules for business requirements\n\nProject:\n\nEDUCATION\n\nB.com\n\nDegree College -  Machilipatnam, Andhra Pradesh\n\n2014\n\nAG&SGS Intermediate College\n\n2011", "meta": {}, "annotation_approver": null, "labels": [[5510, 5514, "Graduation Year"], [5481, 5508, "College Name"], [5475, 5479, "Graduation Year"], [5426, 5440, "College Name"], [5419, 5425, "Degree"], [4077, 4085, "Companies worked at"], [1810, 1818, "Companies worked at"], [1789, 1797, "Companies worked at"], [1555, 1563, "Companies worked at"], [155, 166, "Years of Experience"], [101, 145, "Email Address"], [0, 13, "Name"], [14, 30, "Designation"], [34, 58, "Companies worked at"], [60, 79, "Location"], [187, 206, "Degree"], [1772, 1787, "Years of Experience"], [1725, 1741, "Designation"], [1745, 1768, "Companies worked at"], [1798, 1808, "Designation"], [1822, 1844, "Years of Experience"], [4087, 4113, "Years of Experience"], [5444, 5473, "Location"], [169, 172, "Tech Tools"], [310, 320, "Job Specific Skills"], [285, 292, "Job Specific Skills"], [354, 370, "Job Specific Skills"], [403, 406, "Tech Tools"], [414, 428, "Soft Skills"], [437, 454, "Soft Skills"], [467, 482, "Soft Skills"], [950, 968, "Job Specific Skills"], [1280, 1283, "Tech Tools"], [1472, 1487, "Soft Skills"], [1489, 1502, "Soft Skills"], [1530, 1545, "Soft Skills"], [1504, 1517, "Soft Skills"], [1586, 1606, "Job Specific Skills"], [1576, 1583, "Job Specific Skills"], [1608, 1633, "Job Specific Skills"], [1653, 1676, "Job Specific Skills"], [1680, 1699, "Job Specific Skills"], [1974, 1992, "Companies worked at"], [2775, 2793, "Job Specific Skills"], [3063, 3067, "Tech Tools"], [3299, 3302, "Tech Tools"], [3964, 3971, "Tech Tools"], [4040, 4049, "Designation"], [4053, 4066, "Tech Tools"], [4143, 4169, "Companies worked at"], [4637, 4653, "Job Specific Skills"], [4923, 4926, "Tech Tools"], [4872, 4879, "Job Specific Skills"], [5171, 5175, "Tech Tools"], [5202, 5224, "Job Specific Skills"], [550, 569, "Soft Skills"], [879, 899, "Soft Skills"], [993, 1009, "Job Specific Skills"], [1129, 1150, "Job Specific Skills"], [1381, 1384, "Tech Tools"], [5375, 5396, "Job Specific Skills"]]}
{"id": 8, "text": "Jyotirbindu Patnaik\nAssociate consultant@SAP labs , Bangalore Karnataka\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Jyotirbindu-\nPatnaik/77e3ceda47fbb7e4\n\n-\nExperienced incident and change coordinator and strongly skilled and dedicated ITIL Expert with\na superior work ethic and management satisfaction record. Widely and deeply knowledgeable\nin all aspects of ITIL management and coordination. Adept multitasker able to deal a very high\npriority complex situations with accuracy and professionalism.\n\nWilling to relocate to: Bangalore, Karnataka\n\nWORK EXPERIENCE\n\nAssociate consultant\n\nSap labs\n\nIncident and change management coordinator,\ndealing with the escalation process of company products.\nNotifying the customer as well as stake holders regarding the on going issue as well as helping\nproblem management team to provide RCA.\n\nAssociate consultant\n\nSap labs\n\n-\nJoining date from: January 25, 2017\nDesignation: Associate Consultant\nCompany: SAP on the payroll of Bristlecone India LTD.\n\nRoles and responsibilities: -\nIncident Coordinator:\n1. Following the escalation process and handling the high priority incidents by initiating the\ntroubleshooting call and driving the entire call till the issue gets resolve.\n2. Capturing the entire chronological order to provide the RCA for the unplanned downtimes.\n3. As an incident coordinator, I was informing the internal stakeholders regarding the unplanned\ndowntimes/high priority issue by sending the notifications periodically.\n4. Post handling the issue we were updating the MTTR and monthly outage tracker to have a\nclear records of unplanned downtimes.\n5. Monitoring the tools like Catchpoint, Pingdom, CSS for quick find of availability alerts and trying\nto troubleshoot by initial analysis ASAP.\n6. Preparing the documents for all the new process and update it as per its new changes.\n7. Providing the reports (KPI/Availability/IRT-MPT) on weekly and monthly basis to the\nmanagement to minimize the number incidents.\n8. I was analyzing regarding the number of incidents and alerts received, and providing the entire\ncaptured details to management for further process to reduce the incidents and alerts.\n\nhttps://www.indeed.com/r/Jyotirbindu-Patnaik/77e3ceda47fbb7e4?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Jyotirbindu-Patnaik/77e3ceda47fbb7e4?isid=rex-download&ikw=download-top&co=IN\n\n\nChange Coordinator:\n9. As a change, Coordinator was handling the Entire change management process and validating\nthe Change requests to get the CAB approvals.\n10. Providing the KPI report for the change process.\n11. Driving the CAB meeting and KPI meeting.\n\nProjects: -\nI was working for \"Cloud for customer\" and \"Business by design\" project in SAP.\n\nAchievements: -\n1. I have received management appreciation note for handling the change management process\nin a proficient way.\n2. Team Lead appreciated for maintaining the documents and PPT's as updated.\n\nEDUCATION\n\nB. Tech in Electronics and Communication\n\nBiju Patnaik University -  Rayagada, Orissa\n\n2016\n\nSKILLS\n\nITIL foundation", "meta": {}, "annotation_approver": null, "labels": [[2993, 3016, "College Name"], [2951, 2992, "Degree"], [870, 878, "Companies worked at"], [847, 868, "Designation"], [600, 608, "Companies worked at"], [577, 598, "Designation"], [116, 167, "Email Address"], [19, 40, "Designation"], [0, 19, "Name"], [41, 49, "Companies worked at"], [52, 93, "Location"], [539, 559, "Location"], [901, 917, "Years of Experience"], [931, 951, "Designation"], [961, 1004, "Companies worked at"], [3020, 3036, "Location"], [3038, 3042, "Graduation Year"], [374, 389, "Job Specific Skills"], [414, 425, "Soft Skills"], [610, 652, "Designation"], [1037, 1057, "Designation"], [1651, 1661, "Tech Tools"], [1663, 1670, "Tech Tools"], [1672, 1675, "Tech Tools"], [2383, 2401, "Designation"], [2560, 2570, "Job Specific Skills"], [3052, 3056, "Tech Tools"], [249, 253, "Tech Tools"], [277, 287, "Soft Skills"], [1333, 1354, "Designation"], [1728, 1740, "Job Specific Skills"], [2455, 2480, "Job Specific Skills"], [2815, 2840, "Job Specific Skills"], [2865, 2874, "Designation"]]}
{"id": 9, "text": "Karthihayini C\nSystems Engineer - Infosys Limited\n\nRajapalaiyam, Tamil Nadu - Email me on Indeed: indeed.com/r/Karthihayini-\nC/627254c443836b3c\n\nTo be a part of challenging team, which works for the growth of an organization, explores my\npotential and provides me an opportunity to enhance my knowledge and to be an asset of the\ncompany.\n\nWilling to relocate: Anywhere\n\nWORK EXPERIENCE\n\nSystems Engineer\n\nInfosys Limited -  Chennai, Tamil Nadu -\n\nDecember 2016 to Present\n\nTrainee\n\nInfosys Limited -  Mysore, Karnataka -\n\nJune 2016 to November 2016\n\nClient: Renault\nProject Details:\n• Trained in .Net technology.\n• Handled VB part of the application which has mainframe as backend and Visual basic as the\nfront end with annuaire service as the medium between mainframe and VB.\n• Took care of site creation in the Share Point technology.\n• Experienced in using SmartSVN, TortoiseSVN, DB Visualiser.\n\nEDUCATION\n\nB E in Production Engineering\n\nVelammal Engineering College -  Chennai, Tamil Nadu\n\n2016\n\nTamil Nadu Board Of Education -  Rajapalaiyam, Tamil Nadu\n\n2012\n\nEducation\n\nHr. Sec. School\n\n2010\n\nhttps://www.indeed.com/r/Karthihayini-C/627254c443836b3c?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Karthihayini-C/627254c443836b3c?isid=rex-download&ikw=download-top&co=IN\n\n\nSKILLS\n\nDestructive Testing (Less than 1 year), FORGE (Less than 1 year), Non-Destructive (Less than 1\nyear), Non-Destructive Testing (Less than 1 year), quality control (Less than 1 year)\n\nADDITIONAL INFORMATION\n\nAREAS OF INTEREST\n1. Strength of materials\n2. Engineering statistics and quality control\n\nINDUSTRIAL VISITS\n• BAY FORGE, Madurantakam\n• ASHOK LEYLAN, Ennore\n• CARBORUNDUM, Thiruvottiyur\n• BONFIGLIOLI, Thirumudivakkam\n\nIN-PLANT TRAININGS & WORKSHOPS\n• Attended in-plant training on security division at \"Minda Corporation Limited\", Chennai.\n• Attended in-plant training in \"BHEL\", Ranipet.\n• Attended workshop on \"Aero Modeling\" conducted at Consto UAV Technologies\n• Attended workshop on \"Non-Destructive Testing\" conducted at Velammal engineering College,\nChennai.\n\nPERSONAL QUALIFICATIONS:\n• Ability to quick grasp the concepts\n• Flexible\n• Hard working\n• Consistent", "meta": {}, "annotation_approver": null, "labels": [[0, 14, "Name"], [15, 31, "Designation"], [34, 49, "Companies worked at"], [51, 75, "Location"], [98, 143, "Email Address"], [387, 403, "Designation"], [405, 420, "Companies worked at"], [424, 443, "Location"], [447, 471, "Years of Experience"], [473, 480, "Designation"], [482, 497, "Companies worked at"], [499, 518, "Location"], [522, 548, "Years of Experience"], [910, 939, "Degree"], [941, 969, "College Name"], [973, 992, "Location"], [994, 998, "Graduation Year"], [1033, 1057, "Location"], [596, 600, "Tech Tools"], [623, 625, "Tech Tools"], [685, 697, "Tech Tools"], [773, 775, "Tech Tools"], [813, 824, "Tech Tools"], [860, 868, "Tech Tools"], [870, 881, "Tech Tools"], [883, 896, "Tech Tools"], [1305, 1324, "Job Specific Skills"], [1345, 1350, "Tech Tools"], [1407, 1430, "Job Specific Skills"], [1451, 1466, "Job Specific Skills"], [1842, 1849, "Location"], [1792, 1800, "Job Specific Skills"], [1814, 1839, "Companies worked at"], [1924, 1937, "Job Specific Skills"], [2000, 2023, "Job Specific Skills"], [2038, 2066, "College Name"], [2068, 2075, "Location"], [2143, 2151, "Soft Skills"], [2154, 2166, "Soft Skills"], [2169, 2179, "Soft Skills"], [1532, 1553, "Job Specific Skills"], [1557, 1579, "Job Specific Skills"], [1584, 1599, "Job Specific Skills"]]}
{"id": 10, "text": "Karthik GV\nArchitect - Microsoft India\n\nHyderabad, Telangana - Email me on Indeed: indeed.com/r/Karthik-GV/1961c4eff806e6f4\n\nWilling to relocate to: hyderbad, Telangana\n\nWORK EXPERIENCE\n\nArchitect\n\nMicrosoft India -  Hyderabad, Telangana -\n\nFebruary 2005 to Present\n\n• DevOps - One of the key members of the DevOps team in the Enterprise Services Global DevOps\nprogram.\n• Readiness - Provide DevOps readiness to the end customer.\n\nSr. Program Manager\n\nMicrosoft India -\n\nMarch 2016 to January 2018\n\nKey Projects - Aurora BI (SAP), ES Analytics, BI as a Service, Service Center BI, Premier services\n(PSR)\n\nI am accountable for implementing a unified analytical platform for the entire Enterprise Services\nbusiness (~$6 billion) in Microsoft. This allows the services business to strategize and take key\noperational decisions during the monthly and quarterly business reviews with the Leadership\nTeam.\n\nKey Responsibilities\n• Product Owner - Manage & prioritize product backlog. Evangelize product feature with other\nteams and enable more product adoption.\n• Product Roadmap - Prepare roadmap for the product for the upcoming fiscal year.\n• Sprint Planning & monitoring - Scoping, resource levelling / smoothing\n• Stakeholder management - Manage key business stakeholders, get / set expectations and\nmanage communication.\n• Risk management & mitigation\n• Co-ordinate cross functional teams for product releases\n• Product Retirement - Plan and manage existing product retirement, interact, communicate with\nall downstream systems to ensure product retirement and replacement is smooth.\n• Product migration - Retire Informatica and migrate to Azure Data Factory for ETL process.\n• Compliance Management\n◦ Global Data Protection Regulation (GDPR) - Analyze, estimation and planning for the BI group.\n\nKey Skills - Microsoft Azure SQL DW, Azure Data Factory, SQL Server 2016, Power BI dashboard,\nRest API\n\nhttps://www.indeed.com/r/Karthik-GV/1961c4eff806e6f4?isid=rex-download&ikw=download-top&co=IN\n\n\nMethodology - Agile model\n\nArchitect\n\nMicrosoft India -  Hyderabad, Telangana -\n\nSeptember 2013 to March 2016\n\nMcKesson, Singapore Provident Fund, SSE Home Services\n\nKey Responsibilities\n• Delivery Management - Provide technical leadership and strategic direction to the testing\norganization and project delivery team.\n• Presales - Closely interact with customer & pre-sales team, response to RFP, Test estimates and\nprepare Statement of Work. Won multiple test only engagements from CEE, LATAM, India region\n• Test Strategy & Test Plan development & review for test only engagements.\n• Test Consulting Services - Worked with the UK based customer \"SSE Home Services\" to provide\ntest consultancy and technical guidance to the offshore TCS Kolkata team.\n• Test Processes - Defined process steps and quality gates for test only engagements at\norganization level.\n• Quality Analysis - Analysis of trends in testing, identify best practices, improvement areas. I\nhave prepared checklist for presales & delivery team that reduced the effort and cost of review\nand lead to faster turnover to the customer.\n• Project Recovery Team - I am one the key members in the project recovery team. I work closely\nwith the project team and leadership to ensure that the project is recovered successfully. I have\nsuccessfully recovered 3 critical project with high quality. In the current recovery project, I have\nmanaged a team size of 103 team members.\n• Project Delivery review- As a part of Technical Quality Assurance (TQA), I am the quality\ngatekeeper for all dashboard projects (~30 projects) within Microsoft Global Delivery. All test\nartifacts, test approach, test processes are reviewed and signed off during TQA review.\n\nKey Skills\n\nSr consultant\n\nMicrosoft India -  Hyderabad, Telangana -\n\nJune 2008 to March 2016\n\nSr Test Engineer\n\nMicrosoft India -  Hyderabad, Telangana -\n\nFebruary 2005 to June 2008\n\n2013, SQL Server 2014, Test automation using Coded UI, Selenium.\n\nEDUCATION\n\nPGDBM\n\nNarsee Monjee Institute of Management Studies\n\n\n\n2017 to 2018\n\nSKILLS\n\nProgram Management (2 years), Product Management (2 years), Quality Assurance (10+\nyears), Business intelligence, Devops\n\nLINKS\n\nhttps://www.linkedin.com/in/karthik-g-v-7a25462\n\nAWARDS\n\nMicrosoft Technology Guru\n\nFebruary 2012\n\nMicrosoft Role Model\n\nJune 2007\n\nCERTIFICATIONS/LICENSES\n\nScrum Product Owner\n\nScrum Master\n\nCITA\n\nADDITIONAL INFORMATION\n\nMethodologies - Agile model, Iterative Model\nTest Management - Resource forecasting, team sizing and budgeting, release planning, team\nmanagement, technical\nguidance to the team.\n\nRole: Sr. Consultant (June 2008 - Sep 2013)\nExperience on managing test team for Data Warehouse / SQL Server BI, .Net App Development\nProjects, API / Framework, Windows phone apps and Performance Testing engagements. Played\nrole of Test Lead / Manager in all engagements.\n\nKey Projects - NHS UK, Baxter, ATI, Intel Corporation, Merck, EXL, Oman BI, PwC, ANZ Bank, SQL\nServer PDW (Parallel Data-warehousing), Azure Cave tool\n\nKey Responsibilities\n• Test Manager in all engagements.\n• Test Planning & Execution - Define test strategy and test plan. Onboard resources allocate and\ntrack tasks. Identify risks and mitigation plans.\n• Define & implement Test Automation\n• Performance test planning and execution\n\nhttps://www.linkedin.com/in/karthik-g-v-7a25462\n\n\n• Define defect triage process with project & customer.\n• Team Management - Managed maximum team size up to 8 resources.\n\nKey Skills\nTechnologies - Visual Studio 2012, Microsoft Test Manager 2012, Azure, Test Automation using\nCoded UI, Unit Test Framework, Performance testing using Visual Studio 2012, SQL Server 2012.\nMethodologies - Iterative, Agile model, Test Driven Development (TDD)\n\nMicrosoft IT - India, BI CoE (Feb 2005 - June 2008)\nThe Business Intelligence CoE in India manages multiple applications that require Data\nWarehousing, Reporting, Analytical and other BI related Capabilities.\n\nRole: Test Lead\nI was the test lead for multiple Data Warehousing / SQL Server BI projects. My primary role has\nbeen the test lead for all these projects.\n\nKey Projects - MSSales, Rhythm of Business (RoB), Services Information Repository (SIR)\n\nKey Responsibilities\n• Planning and Estimation of quarterly releases\n• Define test strategy and Master Test Plan\n• Review & Inspection of BRD, FSD, TSD, Test Cases & Test Plan\n• Execution of Test Cases and defect logging.\n• Conduct / participate in Defect Review meetings with the developers / client\n• Analysis of Defects - Analysis of the root cause and the Injection phase of each defect.\n• Test automation on MS BI, RoB, PTS Web, SIR & CFR.\n• Team Management (includes FTEs) - managed team size of max 10 resources\n\nKey Skills\nTechnologies -SQL Server 2000 / 2005 / 2008, SSRS, SSAS, SQL Server PI, SharePoint, Azure,\nEnterprise Library\nTools: Visual Studio 2012, QTP 8.2, TFS 2012.\nSDLC & Test Methodologies - Agile methodology, Iterative model.\n\nRoyal Bank of Scotland, New Delhi Aug 2004 to Jan 2005 (6 months)\nMy role as a Quality Engineer involved in\n• Review of functional requirements\n• Design test plan\n• Design & review of test cases\n• Test automation using Winrunner 7.6, QTP.\n• Test case execution and defect reporting.\n• Set up Performance testing environment-using Loadrunner. Completed a POC for performance\ntesting of a D2K application.\n\nKey Skills Acquired\nTechnologies: Oracle, D2K\nTools: Winrunner 7.6, QTP 6.5, Test Director 7.6, Loadrunner 8.0, Clearcase, SQL Navigator\n\nSapient Corporation, New Delhi June 2003 to Aug 2004 (14 months)\n\n\n\nRole of Quality Engineer and primarily involved in\n• Review of functional requirements.\n• Create & execute system & Integration test cases in Test Director\n• Test automation script development & execution using Winrunner 7.6.\n• Maintenance of Test Automation environment.\n• Production deployment support - Testing in Production.\n\nKey Skills Acquired\nTechnologies: Java, JSP, Ariba 1.5\nTools: Test Director 7.6, Toad, Winrunner 7.5, Trackgear 3.5, PVCS Tracker, WinCVS, Togetherj,\nVisio, Winmerge", "meta": {}, "annotation_approver": null, "labels": [[3977, 4022, "College Name"], [3970, 3975, "Degree"], [3822, 3837, "Companies worked at"], [3804, 3820, "Designation"], [3736, 3751, "Companies worked at"], [3721, 3734, "Designation"], [2034, 2049, "Companies worked at"], [2023, 2032, "Designation"], [452, 467, "Companies worked at"], [198, 213, "Companies worked at"], [187, 196, "Designation"], [23, 38, "Companies worked at"], [11, 20, "Designation"], [0, 10, "Name"], [40, 60, "Location"], [83, 123, "Email Address"], [149, 168, "Location"], [217, 237, "Location"], [241, 265, "Years of Experience"], [431, 450, "Designation"], [471, 497, "Years of Experience"], [2053, 2073, "Location"], [2077, 2106, "Years of Experience"], [3755, 3775, "Location"], [3779, 3802, "Years of Experience"], [3841, 3861, "Location"], [3865, 3891, "Years of Experience"], [4026, 4038, "Graduation Year"], [4334, 4353, "Degree"], [4355, 4367, "Degree"], [4585, 4599, "Designation"], [4601, 4621, "Years of Experience"], [269, 275, "Job Specific Skills"], [308, 314, "Job Specific Skills"], [372, 381, "Soft Skills"], [392, 398, "Job Specific Skills"], [730, 739, "Companies worked at"], [1139, 1154, "Job Specific Skills"], [1212, 1234, "Job Specific Skills"], [1322, 1350, "Job Specific Skills"], [1639, 1657, "Tech Tools"], [1744, 1751, "Soft Skills"], [1808, 1824, "Soft Skills"], [1825, 1828, "Soft Skills"], [1829, 1831, "Soft Skills"], [1833, 1851, "Soft Skills"], [1853, 1868, "Soft Skills"], [1870, 1878, "Soft Skills"], [1890, 1898, "Soft Skills"], [2010, 2021, "Job Specific Skills"], [2507, 2520, "Job Specific Skills"], [2523, 2544, "Job Specific Skills"], [2583, 2598, "Job Specific Skills"], [2751, 2765, "Job Specific Skills"], [3472, 3505, "Job Specific Skills"], [3899, 3909, "Tech Tools"], [3916, 3931, "Job Specific Skills"], [3938, 3946, "Tech Tools"], [3948, 3956, "Tech Tools"], [4048, 4066, "Job Specific Skills"], [4078, 4097, "Job Specific Skills"], [4108, 4126, "Job Specific Skills"], [4139, 4160, "Job Specific Skills"], [4162, 4168, "Job Specific Skills"], [4415, 4426, "Job Specific Skills"], [4428, 4443, "Job Specific Skills"], [4444, 4459, "Job Specific Skills"], [4462, 4482, "Job Specific Skills"], [4511, 4527, "Job Specific Skills"], [4529, 4544, "Job Specific Skills"], [4660, 4674, "Job Specific Skills"], [4677, 4690, "Tech Tools"], [4692, 4696, "Tech Tools"], [4811, 4820, "Designation"], [4823, 4830, "Designation"], [5027, 5039, "Job Specific Skills"], [5062, 5087, "Job Specific Skills"], [5395, 5410, "Job Specific Skills"], [5485, 5503, "Tech Tools"], [5505, 5532, "Tech Tools"], [5534, 5539, "Tech Tools"], [5541, 5556, "Job Specific Skills"], [5563, 5571, "Tech Tools"], [5573, 5592, "Tech Tools"], [5594, 5613, "Job Specific Skills"], [5620, 5638, "Tech Tools"], [5640, 5655, "Tech Tools"], [5673, 5682, "Job Specific Skills"], [5684, 5695, "Job Specific Skills"], [5697, 5726, "Job Specific Skills"], [5728, 5740, "Companies worked at"], [5743, 5748, "Location"], [5758, 5778, "Years of Experience"], [5944, 5953, "Designation"], [5987, 6003, "Job Specific Skills"], [6006, 6016, "Tech Tools"], [6336, 6358, "Job Specific Skills"], [6486, 6505, "Job Specific Skills"], [6630, 6646, "Job Specific Skills"], [6728, 6738, "Tech Tools"], [6759, 6763, "Tech Tools"], [6765, 6769, "Tech Tools"], [6771, 6784, "Tech Tools"], [6786, 6796, "Tech Tools"], [6798, 6803, "Tech Tools"], [6831, 6849, "Tech Tools"], [6851, 6858, "Tech Tools"], [6860, 6868, "Tech Tools"], [6898, 6915, "Job Specific Skills"], [6917, 6932, "Job Specific Skills"], [6941, 6957, "Companies worked at"], [6959, 6968, "Location"], [6969, 6989, "Years of Experience"], [6991, 6999, "Years of Experience"], [7014, 7030, "Designation"], [7154, 7167, "Tech Tools"], [7169, 7172, "Tech Tools"], [7265, 7275, "Tech Tools"], [7374, 7380, "Tech Tools"], [7393, 7406, "Tech Tools"], [7408, 7415, "Tech Tools"], [7417, 7434, "Tech Tools"], [7436, 7450, "Tech Tools"], [7452, 7461, "Tech Tools"], [7463, 7476, "Tech Tools"], [7478, 7497, "Companies worked at"], [7499, 7508, "Location"], [7509, 7531, "Years of Experience"], [7532, 7541, "Years of Experience"], [7554, 7570, "Designation"], [7688, 7701, "Tech Tools"], [7704, 7719, "Job Specific Skills"], [7757, 7770, "Tech Tools"], [7789, 7804, "Job Specific Skills"], [7820, 7849, "Job Specific Skills"], [7910, 7914, "Tech Tools"], [7916, 7919, "Tech Tools"], [7852, 7873, "Job Specific Skills"], [7921, 7930, "Tech Tools"], [7938, 7955, "Tech Tools"], [7957, 7961, "Tech Tools"], [7963, 7976, "Tech Tools"], [7978, 7991, "Tech Tools"], [7993, 8005, "Tech Tools"], [8007, 8013, "Tech Tools"], [8015, 8024, "Tech Tools"], [8026, 8031, "Tech Tools"], [8033, 8041, "Tech Tools"], [1057, 1072, "Job Specific Skills"], [1298, 1318, "Soft Skills"], [2185, 2205, "Job Specific Skills"], [5115, 5124, "Job Specific Skills"], [6374, 6384, "Job Specific Skills"], [6577, 6592, "Job Specific Skills"], [7119, 7129, "Job Specific Skills"], [7088, 7097, "Job Specific Skills"]]}
{"id": 11, "text": "Kartik Sharma\nSystems Engineer - Infosys Ltd\n\nDelhi, Delhi - Email me on Indeed: indeed.com/r/Kartik-Sharma/cc7951fd7809f35e\n\n● Qualified B.Tech in Information Technology with 2.5 years overall and 2 years' experience in\nSAP Security, Project Management and Software Support.\n● Currently spearheading as Senior Systems Engineer with Infosys Ltd Pune, well versed in\nAnalysis, Test and Support activities.\n● Proficient in handling various projects and managing project risks. Possess up to date\nknowledge of latest technological advancements, regulations and statutory compliances in the\nindustry.\n● Instrumental in building relations with upper level decision makers, seizing control of critical\nproblem areas and delivering on client commitments.\n\nPROJECT ANNEXURE:\n\nProject Name: RB (Reckitt Benckiser)\nDuration: Since April '16\nRole: SAP Security Consultant\nResponsibilities:\n● Technical analyst for sap security in production and non-production environments.\n● Worked with Security related tables such as AGR*, USR* etc.\n● Performed User comparison using PFCG.\n● Analysing user access issues using SU53 and system trace (ST01)\n● Role changes done using PFCG as per the change request received.\n● Mass user changes using SHDB, LSMW, SU10.\n● Control Firefighter access in GRC10.1.\n● Handling/Creating Solman CR as per Business requirement.\n● Working on tool Service-Now for User/Business/Technical support.\n\nPROJECT KEY RESULT AREAS:\n\n● Extensive working knowledge in SAP ECC 6.0, SAP R/3 Enterprise GRC 10.1.\n● Expertise in Role Administration, PFCG, User reports, Authorization objects.\n● Expertise in Risk Analysis, Mitigation and Remediation.\n● Utilize SU24 to enable/disable security checks\n● Granting privileged and compensatory controls, providing access in controlled environment\nusing Fire-fighter id.\n● Troubleshoot security/authorization using SU53, ST01 and SUIM.\n● Restrict table access through authorization groups.\n● Ticket handling-related to various issues ranging from user expiration to missing\nauthorizations.\n● Addition, Removal of transaction codes, authorizations, authorization objects by modifying\nexisting roles based upon change request.\n● Supporting Site Go-Lives.\n\nWilling to relocate to: Delhi - Noida, Uttar Pradesh - Gurgaon, Haryana\n\nhttps://www.indeed.com/r/Kartik-Sharma/cc7951fd7809f35e?isid=rex-download&ikw=download-top&co=IN\n\n\nWORK EXPERIENCE\n\nSystems Engineer\n\nInfosys Ltd -  Delhi, Delhi -\n\nMarch 2016 to Present\n\nas a management trainee.\n• Worked with 'AVS InfoTech' as a part-time employee.\n• Guided students in Science, Math and C++ for 4 years.\n\nTECHNICAL SKILL SET\n\nSAP Systems: SAP ECC 6.0.\nProduct Tools: GRC 10.1\nDatabase: Oracle, SQL, RDBMS.\n\nSenior Systems engineer\n\nInfosys Limited -\n\nSeptember 2015 to Present\n\nWorking as SAP SECURITY consultant in a client project with Infosys\n\nEDUCATION\n\nB.Tech in Engineering\n\nNorthern India engineering college, IP UNIVERSITY DELHI\n\n2015\n\nCBSE\n\nLovely Public Sr. Sec. School\n\n2011\n\nCBSE\n\nLovely Public Sr. Sec. School\n\n2009\n\nUniversity / Board\n\nSKILLS\n\nSAP Security\n\n\n\nADDITIONAL INFORMATION\n\nOperating systems: Windows […] 8, 10.\nLanguages: C, C++, C#\n\nPROFESSIONAL SKILL SET:\n\n• Good Communication Skills in English and Hindi.\n• Ability to work under pressure.", "meta": {}, "annotation_approver": null, "labels": [[2926, 2930, "Graduation Year"], [2869, 2924, "College Name"], [2846, 2868, "Degree"], [2425, 2430, "Location"], [2418, 2423, "Location"], [2385, 2401, "Designation"], [332, 344, "Companies worked at"], [138, 170, "Degree"], [81, 125, "Email Address"], [53, 58, "Location"], [46, 51, "Location"], [32, 44, "Companies worked at"], [14, 30, "Designation"], [0, 13, "Name"], [176, 184, "Years of Experience"], [198, 205, "Years of Experience"], [304, 327, "Designation"], [345, 349, "Location"], [2219, 2267, "Location"], [2403, 2414, "Companies worked at"], [2434, 2455, "Years of Experience"], [2695, 2718, "Designation"], [2720, 2735, "Companies worked at"], [2739, 2764, "Years of Experience"], [221, 233, "Job Specific Skills"], [235, 253, "Job Specific Skills"], [258, 274, "Job Specific Skills"], [366, 374, "Soft Skills"], [376, 380, "Job Specific Skills"], [385, 392, "Job Specific Skills"], [615, 633, "Soft Skills"], [451, 473, "Job Specific Skills"], [837, 860, "Designation"], [881, 898, "Job Specific Skills"], [903, 906, "Tech Tools"], [1037, 1052, "Job Specific Skills"], [1059, 1063, "Tech Tools"], [1470, 1481, "Tech Tools"], [1483, 1510, "Tech Tools"], [1527, 1546, "Job Specific Skills"], [1606, 1619, "Job Specific Skills"], [2575, 2578, "Tech Tools"], [2627, 2640, "Tech Tools"], [2655, 2663, "Tech Tools"], [2674, 2680, "Tech Tools"], [2682, 2685, "Tech Tools"], [2687, 2692, "Tech Tools"], [2777, 2800, "Designation"], [2826, 2833, "Companies worked at"], [3046, 3049, "Tech Tools"], [3105, 3112, "Tech Tools"], [3135, 3136, "Tech Tools"], [3138, 3141, "Tech Tools"], [3143, 3145, "Tech Tools"], [3179, 3192, "Soft Skills"], [3203, 3210, "Soft Skills"], [3215, 3220, "Soft Skills"], [1224, 1228, "Tech Tools"], [1230, 1234, "Tech Tools"], [1236, 1240, "Tech Tools"]]}
{"id": 12, "text": "Kasturika Borah\nTeam Member - Cisco\n\nBengaluru, Karnataka - Email me on Indeed: indeed.com/r/Kasturika-\nBorah/9e71468914b38ee8\n\n• Software Engineer with overall 3+ years of experience in Network Monitoring system tool (EM7,\nQuicksilver) Database tool (SQL, Maria DB) and reporting tool (Splunk) in all the releases.\n• Relevant experience as a Test engineer for the releases includes Functional testing as well as\nregression testing. Testing includes writing test cases, execute them and raise bugs.\n• Relevant 1+ years of experience in handling releases for EM7 with proper documentation, Power\npack creation and Tar creation for Sprint releases.\n• Creating Splunk reports from last 6 months.\n• Competent technical person involved in requirement gathering, analysis, design and coding.\n• Experience in coding Python, SQL, and XML as per the requirement.\n• Have knowledge in Event generating using traps and Syslog's generator.\n• Exposure to Agile methodologies using Scrum Works framework, even handled scrum in the\nteam\n• Strong problem-solver who can design solutions and assist developers with issues.\n• Excellent debugging and resolution skills.\n• Good communication and interpersonal skills.\n\n• Working as Software Engineer for Cisco System India Private Ltd under Capgemini India Pvt.\nLtd.. From May 25th 2017 till nowl\n• Working as Software Engineer for Cisco System India Private Ltd under Randstad India Ltd.\nFrom Dec 15 2014 till 30th April.\n• Worked as Data Analyst for Fidelity India Financial Inc. from June 2013 till Oct 2014.\n• Worked as Billing Analyst for IBM Daksh from March 2013 to June 2013.\n\nWilling to relocate to: Bengaluru, Karnataka\n\nWORK EXPERIENCE\n\nTeam Member\n\nCisco -\n\nOctober 2017 to Present\n\nEnvironment: Splunk\nTechnologies: SPL command\n\nResponsibilities\n• Involvement writing Splunk programming language and designing the report dashboard\n• Following Agile methodology\n• Develop the code on the design in splunk.\n• Unit Testing and code review\n\nSenior developer and tester\n\nhttps://www.indeed.com/r/Kasturika-Borah/9e71468914b38ee8?isid=rex-download&ikw=download-top&co=IN\nhttps://www.indeed.com/r/Kasturika-Borah/9e71468914b38ee8?isid=rex-download&ikw=download-top&co=IN\n\n\nCisco -\n\nDecember 2014 to Present\n\nEnvironment: EM7 platform, Quicksilver, SQL, oracle Toad\nTechnologies: Python coding, xml coding, SQL query writing\n\nDescription\nCisco Systems, Inc. (known as Cisco) is an American multinational technology conglomerate\nheadquartered in San José, California, that develops, manufactures, and sells networking\nhardware, telecommunications equipment, and other high-technology services and products\n(www.cisco.com)\n\nResponsibilities\n1. Developer of individual task on each release by weekly\n• Need to do coding for new requirement.\n• Also need to do end to end testing of all the events including Traps and Syslogs.\n2. Database and Infrastructure Monitoring and Alerting related to device.\n3. Involvement in documentation of release notes and preparation of a Regression testing at the\nend of each release.\n\nTeam Member\n\nCisco -\n\nDecember 2014 to December 2017\n\nEnvironment: INFOVISTA (Vportal)\nTechnologies: MS-Excel (sort, VLOOKUP), PPT\n\nResponsibilities\n• Involvement in generating performance reports for certain customers at the starting of every\nmonth\n• Gathering the data from the INFOVISTA portal and sort it out as per month in the excel and\ndesign the graphs for last consecutive\n• Responsible for each data uploaded to the excel sheet and reviewing it before delivering\n\nFidelity national financial -\n\nJune 2013 to October 2014\n\nRole: QA and Report handling for the team\nTechnologies: MS-Excel (sort, VLOOKUP), PPT, MS-Outlook\n\nResponsibilities\n• Involvement in generating performance reports for the team at the end of each day and monthly\nbased\n• Responsible for each data uploaded to the excel sheet and sending it to the team manager\n\nEDUCATION\n\nCompucom Insitute of Information Technology\n\n\n\nrajasthan University\n\n2012\n\nSKILLS\n\nDatabase (3 years), Python (3 years), Splunk (Less than 1 year), SQL (3 years), xml (3 years)\n\nADDITIONAL INFORMATION\n\nTECHNICAL SKILLS\n\n• Programming Languages: Python, XML\n• Database: Maria-DB, sql\n• Cisco Monitoring Tools: EM7\n• Operating Systems: Windows/XP\n• Reporting Tools: Vportal, Splunk\n• Application & Web Servers: Sciencelogic (EM7), Syslog sender, Relay server.\n• Data Structure Knowledge: Intermediate", "meta": {}, "annotation_approver": null, "labels": [[3970, 3974, "Graduation Year"], [3948, 3969, "College Name"], [3901, 3945, "College Name"], [3061, 3066, "Companies worked at"], [3048, 3059, "Designation"], [2402, 2407, "Companies worked at"], [2208, 2213, "Companies worked at"], [1690, 1695, "Companies worked at"], [1677, 1688, "Designation"], [72, 126, "Email Address"], [30, 35, "Companies worked at"], [16, 27, "Designation"], [0, 15, "Name"], [37, 57, "Location"], [130, 147, "Designation"], [161, 169, "Years of Experience"], [343, 356, "Designation"], [1211, 1228, "Designation"], [1233, 1263, "Companies worked at"], [1270, 1295, "Companies worked at"], [1302, 1324, "Years of Experience"], [1339, 1356, "Designation"], [1361, 1417, "Companies worked at"], [1418, 1450, "Years of Experience"], [1464, 1476, "Designation"], [1481, 1510, "Companies worked at"], [1511, 1539, "Years of Experience"], [1553, 1568, "Designation"], [1573, 1582, "Companies worked at"], [1588, 1611, "Years of Experience"], [1638, 1658, "Location"], [1697, 1722, "Years of Experience"], [2217, 2241, "Years of Experience"], [2372, 2391, "Companies worked at"], [3070, 3100, "Years of Experience"], [187, 212, "Job Specific Skills"], [219, 222, "Tech Tools"], [224, 235, "Tech Tools"], [252, 255, "Tech Tools"], [257, 265, "Tech Tools"], [287, 293, "Tech Tools"], [383, 401, "Job Specific Skills"], [413, 431, "Job Specific Skills"], [558, 561, "Tech Tools"], [658, 664, "Tech Tools"], [734, 784, "Job Specific Skills"], [809, 815, "Tech Tools"], [817, 820, "Tech Tools"], [826, 829, "Tech Tools"], [941, 960, "Job Specific Skills"], [967, 988, "Tech Tools"], [1003, 1008, "Job Specific Skills"], [1030, 1044, "Soft Skills"], [1117, 1141, "Job Specific Skills"], [1157, 1170, "Soft Skills"], [1175, 1188, "Soft Skills"], [1810, 1816, "Tech Tools"], [1737, 1743, "Tech Tools"], [1758, 1761, "Tech Tools"], [1885, 1902, "Job Specific Skills"], [1939, 1945, "Tech Tools"], [1949, 1977, "Job Specific Skills"], [2256, 2268, "Tech Tools"], [2270, 2281, "Tech Tools"], [2283, 2286, "Tech Tools"], [2288, 2294, "Tech Tools"], [2314, 2320, "Tech Tools"], [2329, 2332, "Tech Tools"], [2341, 2344, "Tech Tools"], [2479, 2499, "Location"], [3000, 3018, "Job Specific Skills"], [3115, 3124, "Tech Tools"], [3149, 3157, "Tech Tools"], [3175, 3178, "Tech Tools"], [3553, 3578, "Years of Experience"], [3586, 3608, "Job Specific Skills"], [3636, 3644, "Tech Tools"], [3662, 3665, "Tech Tools"], [3667, 3677, "Tech Tools"], [4004, 4010, "Tech Tools"], [4022, 4028, "Tech Tools"], [4049, 4052, "Tech Tools"], [4064, 4067, "Tech Tools"], [4146, 4152, "Tech Tools"], [4154, 4157, "Tech Tools"], [4170, 4178, "Tech Tools"], [4180, 4183, "Tech Tools"], [4210, 4213, "Tech Tools"], [4235, 4245, "Tech Tools"], [4265, 4272, "Tech Tools"], [4274, 4280, "Tech Tools"], [4310, 4328, "Tech Tools"], [4330, 4336, "Tech Tools"], [4361, 4375, "Job Specific Skills"], [237, 245, "Job Specific Skills"], [458, 468, "Job Specific Skills"], [2295, 2299, "Tech Tools"], [3984, 3992, "Job Specific Skills"], [4123, 4134, "Job Specific Skills"], [4345, 4357, "Tech Tools"]]}
