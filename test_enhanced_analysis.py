#!/usr/bin/env python3

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cv_analyzer_project.settings')
django.setup()

from cv_analyzer.models import CV, CVAnalysis
from cv_analyzer.views import process_cv_logic

def test_enhanced_analysis():
    print("=== Enhanced CV Analysis System Test ===")
    
    # Check if we have CVs in the database
    cv_count = CV.objects.count()
    print(f"Total CVs in database: {cv_count}")
    
    if cv_count == 0:
        print("No CVs found in database. Please upload some CVs first.")
        return
    
    # Get the first CV
    cv = CV.objects.first()
    print(f"\nTesting with CV ID: {cv.id}")
    print(f"CV file: {cv.file.name}")
    print(f"CV status: {cv.status}")
    
    # Check if it has existing analysis
    try:
        existing_analysis = cv.analysis
        print(f"Has existing analysis: Yes")
        print(f"Current name: {existing_analysis.name}")
        print(f"Current email: {existing_analysis.email}")
        print(f"Current years of experience: {existing_analysis.years_of_experience}")
        print(f"Has experience data: {bool(existing_analysis.experience_data)}")
        print(f"Has education data: {bool(existing_analysis.education_data)}")
        print(f"Has certifications data: {bool(existing_analysis.certifications_data)}")
    except CVAnalysis.DoesNotExist:
        print("Has existing analysis: No")
        existing_analysis = None
    
    # Re-analyze with the enhanced system
    print(f"\n=== Re-analyzing CV with Enhanced System ===")
    try:
        # Delete existing analysis if it exists
        if existing_analysis:
            existing_analysis.delete()
            print("Deleted existing analysis")
        
        # Run the enhanced analysis
        new_analysis = process_cv_logic(cv)
        print(f"✅ Enhanced analysis completed successfully!")
        
        print(f"\n=== Enhanced Analysis Results ===")
        print(f"Name: {new_analysis.name}")
        print(f"Email: {new_analysis.email}")
        print(f"Phone: {new_analysis.phone_number}")
        print(f"Location: {new_analysis.location}")
        print(f"Years of Experience: {new_analysis.years_of_experience}")
        print(f"Education Level: {new_analysis.education_level}")
        print(f"Skills: {new_analysis.skills[:100]}..." if len(new_analysis.skills) > 100 else f"Skills: {new_analysis.skills}")
        
        # Check enhanced structured data
        import json
        if new_analysis.experience_data and new_analysis.experience_data != "{}":
            print(f"\n📋 Work Experience Data:")
            try:
                exp_data = json.loads(new_analysis.experience_data) if isinstance(new_analysis.experience_data, str) else new_analysis.experience_data
                positions = exp_data.get('positions', [])
                print(f"  Found {len(positions)} positions")
                for i, pos in enumerate(positions[:2]):  # Show first 2 positions
                    print(f"  {i+1}. {pos.get('title', 'N/A')} at {pos.get('company', 'N/A')} ({pos.get('duration', 'N/A')})")
            except:
                print("  Could not parse experience data")
        
        if new_analysis.education_data and new_analysis.education_data != "{}":
            print(f"\n🎓 Education Data:")
            try:
                edu_data = json.loads(new_analysis.education_data) if isinstance(new_analysis.education_data, str) else new_analysis.education_data
                degrees = edu_data.get('degrees', [])
                print(f"  Found {len(degrees)} degrees")
                for i, deg in enumerate(degrees):
                    print(f"  {i+1}. {deg.get('degree', 'N/A')} in {deg.get('field', 'N/A')} from {deg.get('institution', 'N/A')} ({deg.get('year', 'N/A')})")
            except:
                print("  Could not parse education data")
        
        if new_analysis.certifications_data and new_analysis.certifications_data != "{}":
            print(f"\n🏆 Certifications Data:")
            try:
                cert_data = json.loads(new_analysis.certifications_data) if isinstance(new_analysis.certifications_data, str) else new_analysis.certifications_data
                certs = cert_data.get('certs', [])
                print(f"  Found {len(certs)} certifications")
                for i, cert in enumerate(certs):
                    print(f"  {i+1}. {cert.get('name', 'N/A')} by {cert.get('issuer', 'N/A')} ({cert.get('year', 'N/A')})")
            except:
                print("  Could not parse certifications data")
        
        print(f"\n=== Analysis Quality Scores ===")
        print(f"Overall Score: {new_analysis.overall_score}/100")
        print(f"Content Score: {new_analysis.content_score}/100")
        print(f"Format Score: {new_analysis.format_score}/100")
        print(f"Skills Score: {new_analysis.skills_score}/100")
        
    except Exception as e:
        print(f"❌ Error during enhanced analysis: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_analysis() 