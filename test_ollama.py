#!/usr/bin/env python3
"""
Test script to check Ollama connectivity and available models.
Run this script to verify your Ollama setup before configuring in Django Admin.
"""

import requests
import json
import sys

def test_ollama_connection(url="http://*************:11434"):
    """Test connection to Ollama server and list available models."""
    
    print("🔍 Testing Ollama Connection...")
    print(f"   Server URL: {url}")
    
    try:
        # Test server health
        print("\n1. Testing server health...")
        response = requests.get(f"{url}/api/tags", timeout=10)
        
        if response.status_code == 200:
            print("   ✅ Ollama server is running!")
            
            # List available models
            data = response.json()
            models = data.get('models', [])
            
            if models:
                print(f"\n2. Found {len(models)} available models:")
                for i, model in enumerate(models, 1):
                    name = model.get('name', 'Unknown')
                    size = model.get('size', 0)
                    size_gb = size / (1024**3) if size else 0
                    modified = model.get('modified_at', 'Unknown')
                    print(f"   {i}. {name} ({size_gb:.1f}GB) - Modified: {modified}")
                
                # Test a simple generation with the first model
                if models:
                    test_model = models[0]['name']
                    print(f"\n3. Testing text generation with '{test_model}'...")
                    
                    test_payload = {
                        "model": test_model,
                        "prompt": "Hello, this is a test. Respond with 'Ollama is working!'",
                        "stream": False,
                        "options": {
                            "num_predict": 50
                        }
                    }
                    
                    gen_response = requests.post(
                        f"{url}/api/generate",
                        json=test_payload,
                        timeout=60
                    )
                    
                    if gen_response.status_code == 200:
                        result = gen_response.json()
                        response_text = result.get('response', '')
                        print(f"   ✅ Generation successful!")
                        print(f"   Response: {response_text}")
                        
                        print(f"\n🎉 Success! Ollama is ready for CV Analyzer!")
                        print(f"\n📋 Configuration for Django Admin:")
                        print(f"   • Provider: ollama")
                        print(f"   • API Key: {url}")
                        print(f"   • Model Name: {test_model}")
                        print(f"   • Recommended models for CV analysis:")
                        for model in models[:3]:  # Show top 3 models
                            print(f"     - {model['name']}")
                        
                        return True
                    else:
                        print(f"   ❌ Generation failed: {gen_response.status_code}")
                        print(f"   Response: {gen_response.text}")
                        return False
            else:
                print("   ⚠️  No models found! You need to pull some models first.")
                print("   Example: ollama pull llama3.2")
                return False
                
        else:
            print(f"   ❌ Server returned status code: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Cannot connect to Ollama server!")
        print("   Make sure Ollama is installed and running:")
        print("   • Install: https://ollama.ai/download")
        print("   • Start: Run 'ollama serve' in terminal")
        print("   • Test: Run 'ollama list' to see models")
        return False
        
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return False

def main():
    """Main function to test Ollama setup."""
    print("🚀 Ollama Connectivity Test for CV Analyzer")
    print("=" * 50)
    
    # Test default localhost
    if test_ollama_connection():
        return 0
    
    # Test with custom URL if provided
    if len(sys.argv) > 1:
        custom_url = sys.argv[1]
        print(f"\n🔄 Trying custom URL: {custom_url}")
        if test_ollama_connection(custom_url):
            return 0
    
    print("\n💡 Troubleshooting Tips:")
    print("1. Install Ollama: https://ollama.ai/download")
    print("2. Start Ollama: ollama serve")
    print("3. Pull a model: ollama pull llama3.2")
    print("4. Check models: ollama list")
    print("5. Test this script: python test_ollama.py")
    print("6. Custom URL: python test_ollama.py http://your-server:11434")
    
    return 1

if __name__ == "__main__":
    sys.exit(main()) 