#!/usr/bin/env python3

import os
import sys
import django

# Setup Django environment
project_path = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_path)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cv_analyzer_project.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth.models import User
from cv_analyzer.views import start_ai_analysis
import json

def test_backend_directly():
    """Test the backend analysis function directly"""
    print("🧪 Testing backend analysis function directly...")
    
    # Create a mock request
    factory = RequestFactory()
    
    # Create test data
    test_data = {
        'cv_ids': [15, 14],
        'vacancy_ids': [9],
        'analysis_type': 'fast'
    }
    
    print(f"📋 Test data: {test_data}")
    
    # Create a POST request
    request = factory.post(
        '/api/start-ai-analysis/',
        data=json.dumps(test_data),
        content_type='application/json'
    )
    
    # Add a user to the request (bypass login requirement for testing)
    try:
        user = User.objects.first()
        if not user:
            print("❌ No users found in database")
            return False
        request.user = user
        print(f"👤 Using user: {user.username}")
    except Exception as e:
        print(f"❌ Error getting user: {e}")
        return False
    
    print("🚀 Calling start_ai_analysis function...")
    print("=" * 50)
    
    try:
        # Call the function directly
        response = start_ai_analysis(request)
        
        print("=" * 50)
        print(f"📥 Response status: {response.status_code}")
        
        if hasattr(response, 'content'):
            content = response.content.decode()
            try:
                response_data = json.loads(content)
                print(f"📊 Response data: {json.dumps(response_data, indent=2)}")
                
                if response_data.get('success'):
                    print("✅ Backend analysis completed successfully!")
                    
                    results = response_data.get('results', [])
                    print(f"📈 Processed {len(results)} combinations")
                    
                    for result in results:
                        cv_id = result.get('cv_id')
                        score = result.get('score')
                        method = result.get('method')
                        status = result.get('status')
                        print(f"  - CV {cv_id}: {score}% ({method}) - {status}")
                    
                    return True
                else:
                    print(f"❌ Backend analysis failed: {response_data.get('message', 'Unknown error')}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"Raw content: {content[:500]}...")
                return False
        else:
            print("❌ No content in response")
            return False
            
    except Exception as e:
        print(f"💥 Error calling start_ai_analysis: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Testing Enhanced Backend Progress Logging")
    print("=" * 60)
    
    success = test_backend_directly()
    
    print("=" * 60)
    if success:
        print("✅ Backend progress logging test completed successfully!")
        print("🎯 You should see detailed console output with emojis showing each CV being processed")
    else:
        print("❌ Backend progress logging test failed!")
    
    print("\n💡 Next steps:")
    print("1. Check Django console logs for detailed progress messages")
    print("2. Look for emoji messages showing each CV analysis")
    print("3. Go to http://127.0.0.1:8000/vacancy/9/candidates/ to test UI") 