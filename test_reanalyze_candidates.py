#!/usr/bin/env python3
"""
Test script for the Re-analyze Candidates functionality.
This script tests the AI-powered re-analysis of candidates for a specific vacancy.
"""

import os
import sys
import django
import json
from datetime import datetime
import requests
import time

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cv_analyzer_project.settings')
django.setup()

from cv_analyzer.models import Vacancy, CV, ComparisonAnalysis, AIAPIConfig
from cv_analyzer.views import calculate_compatibility, calculate_detailed_compatibility


def test_ai_config():
    """Test if AI configuration is properly set up"""
    print("=" * 60)
    print("TESTING AI CONFIGURATION")
    print("=" * 60)
    
    ai_configs = AIAPIConfig.objects.filter(is_active=True)
    print(f"Active AI configurations found: {ai_configs.count()}")
    
    if ai_configs.exists():
        for config in ai_configs:
            print(f"  - Provider: {config.provider}")
            print(f"  - Model: {config.model_name}")
            print(f"  - Priority: {config.priority}")
            print(f"  - API Key: {config.api_key[:10]}..." if config.api_key else "  - API Key: Not set")
        return True
    else:
        print("❌ No active AI configurations found!")
        print("Please configure AI providers in Django Admin > AI API Configurations")
        return False


def test_vacancy_data():
    """Test if vacancy data is available"""
    print("\n" + "=" * 60)
    print("TESTING VACANCY DATA")
    print("=" * 60)
    
    vacancies = Vacancy.objects.all()
    print(f"Total vacancies in database: {vacancies.count()}")
    
    if vacancies.exists():
        test_vacancy = vacancies.first()
        print(f"Test vacancy: {test_vacancy.title} at {test_vacancy.company.name}")
        print(f"Description length: {len(test_vacancy.description)} characters")
        print(f"Requirements length: {len(test_vacancy.requirements)} characters")
        return test_vacancy
    else:
        print("❌ No vacancies found!")
        print("Please create at least one vacancy to test the functionality")
        return None


def test_cv_data():
    """Test if CV data is available"""
    print("\n" + "=" * 60)
    print("TESTING CV DATA")
    print("=" * 60)
    
    cvs = CV.objects.all()
    print(f"Total CVs in database: {cvs.count()}")
    
    if cvs.exists():
        test_cv = cvs.first()
        print(f"Test CV: {test_cv.id}")
        print(f"File: {test_cv.file.name if test_cv.file else 'No file'}")
        print(f"Status: {test_cv.status}")
        print(f"Source: {test_cv.source}")
        return test_cv
    else:
        print("❌ No CVs found!")
        print("Please upload at least one CV to test the functionality")
        return None


def test_compatibility_calculation(cv, vacancy):
    """Test the basic compatibility calculation"""
    print("\n" + "=" * 60)
    print("TESTING COMPATIBILITY CALCULATION")
    print("=" * 60)
    
    try:
        print(f"Testing compatibility between CV {cv.id} and vacancy '{vacancy.title}'")
        
        # Test basic compatibility
        print("\n📊 Testing basic compatibility calculation...")
        score = calculate_compatibility(cv, vacancy)
        print(f"✅ Basic compatibility score: {score}%")
        
        # Test detailed compatibility
        print("\n📊 Testing detailed compatibility calculation...")
        detailed_analysis = calculate_detailed_compatibility(cv, vacancy)
        
        print(f"✅ Detailed analysis completed:")
        print(f"   - Overall score: {detailed_analysis['compatibility_score']}%")
        print(f"   - Skills match: {detailed_analysis['skills_match_percentage']}%")
        print(f"   - Recommendation: {detailed_analysis['recommendation_level']}")
        print(f"   - Summary: {detailed_analysis['analysis_text'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in compatibility calculation: {str(e)}")
        return False


def test_comparison_analysis_creation(cv, vacancy):
    """Test creating/updating comparison analysis"""
    print("\n" + "=" * 60)
    print("TESTING COMPARISON ANALYSIS CREATION")
    print("=" * 60)
    
    try:
        # Check if analysis already exists
        existing = ComparisonAnalysis.objects.filter(cv=cv, vacancy=vacancy).first()
        
        if existing:
            print(f"📋 Existing analysis found (ID: {existing.id})")
            print(f"   - Current score: {existing.compatibility_score}%")
            print(f"   - Created: {existing.created_at}")
            
            # Test update
            print("\n🔄 Testing analysis update...")
            detailed_analysis = calculate_detailed_compatibility(cv, vacancy)
            
            existing.compatibility_score = detailed_analysis['compatibility_score']
            existing.skills_match_percentage = detailed_analysis['skills_match_percentage']
            existing.recommendation_level = detailed_analysis['recommendation_level']
            existing.analysis_text = f"{detailed_analysis['analysis_text']} (Updated: {datetime.now().strftime('%Y-%m-%d %H:%M')})"
            existing.analysis_details = detailed_analysis.get('analysis_details', {})
            existing.save()
            
            print(f"✅ Analysis updated successfully!")
            print(f"   - New score: {existing.compatibility_score}%")
            print(f"   - Skills match: {existing.skills_match_percentage}%")
            print(f"   - Recommendation: {existing.recommendation_level}")
            
        else:
            print("📋 No existing analysis found. Creating new one...")
            
            detailed_analysis = calculate_detailed_compatibility(cv, vacancy)
            
            analysis = ComparisonAnalysis.objects.create(
                cv=cv,
                vacancy=vacancy,
                compatibility_score=detailed_analysis['compatibility_score'],
                skills_match_percentage=detailed_analysis['skills_match_percentage'],
                recommendation_level=detailed_analysis['recommendation_level'],
                analysis_text=detailed_analysis['analysis_text'],
                analysis_details=detailed_analysis.get('analysis_details', {})
            )
            
            print(f"✅ New analysis created successfully!")
            print(f"   - Analysis ID: {analysis.id}")
            print(f"   - Score: {analysis.compatibility_score}%")
            print(f"   - Skills match: {analysis.skills_match_percentage}%")
            print(f"   - Recommendation: {analysis.recommendation_level}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in comparison analysis creation: {str(e)}")
        return False


def test_api_endpoint_simulation():
    """Simulate the API endpoint call that the frontend makes"""
    print("\n" + "=" * 60)
    print("TESTING API ENDPOINT SIMULATION")
    print("=" * 60)
    
    try:
        # Get test data
        vacancy = Vacancy.objects.first()
        cvs = CV.objects.all()[:3]  # Test with first 3 CVs
        
        if not vacancy or not cvs:
            print("❌ Insufficient test data")
            return False
        
        print(f"Simulating re-analysis for vacancy: {vacancy.title}")
        print(f"Testing with {cvs.count()} CVs")
        
        results = []
        
        for cv in cvs:
            try:
                print(f"\n🔄 Processing CV {cv.id}...")
                
                # Get existing analysis
                existing = ComparisonAnalysis.objects.filter(cv=cv, vacancy=vacancy).first()
                
                # Run detailed AI analysis
                detailed_analysis = calculate_detailed_compatibility(cv, vacancy)
                compatibility_score = detailed_analysis['compatibility_score']
                skills_match_percentage = detailed_analysis['skills_match_percentage']
                recommendation_level = detailed_analysis['recommendation_level']
                
                if existing:
                    # Update existing
                    existing.compatibility_score = compatibility_score
                    existing.skills_match_percentage = skills_match_percentage
                    existing.recommendation_level = recommendation_level
                    existing.analysis_text = f"{detailed_analysis['analysis_text']} (Re-analyzed: {datetime.now().strftime('%Y-%m-%d %H:%M')})"
                    existing.analysis_details = detailed_analysis.get('analysis_details', {})
                    existing.save()
                    action = 'updated'
                else:
                    # Create new
                    ComparisonAnalysis.objects.create(
                        cv=cv,
                        vacancy=vacancy,
                        compatibility_score=compatibility_score,
                        skills_match_percentage=skills_match_percentage,
                        recommendation_level=recommendation_level,
                        analysis_text=detailed_analysis['analysis_text'],
                        analysis_details=detailed_analysis.get('analysis_details', {})
                    )
                    action = 'created'
                
                results.append({
                    'cv_id': cv.id,
                    'vacancy_id': vacancy.id,
                    'status': 'success',
                    'score': compatibility_score,
                    'skills_match': skills_match_percentage,
                    'recommendation': recommendation_level,
                    'message': f'Analysis {action} ({compatibility_score}% compatibility, {recommendation_level})'
                })
                
                print(f"   ✅ {action.title()}: {compatibility_score}% ({recommendation_level})")
                
            except Exception as e:
                results.append({
                    'cv_id': cv.id,
                    'vacancy_id': vacancy.id,
                    'status': 'error',
                    'message': str(e)
                })
                print(f"   ❌ Error: {str(e)}")
        
        # Summary
        successful = len([r for r in results if r['status'] == 'success'])
        avg_score = sum([r.get('score', 0) for r in results if r['status'] == 'success']) / max(successful, 1)
        high_scores = len([r for r in results if r['status'] == 'success' and r.get('score', 0) >= 80])
        
        print(f"\n📊 SIMULATION RESULTS:")
        print(f"   - Total processed: {len(results)}")
        print(f"   - Successful: {successful}")
        print(f"   - Average score: {avg_score:.1f}%")
        print(f"   - High matches (80%+): {high_scores}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in API simulation: {str(e)}")
        return False


def test_reanalyze_api_endpoint():
    """Test the exact API endpoint the browser calls"""
    print("=== Testing Re-analyze Candidates API Endpoint ===\n")
    
    # This is the exact URL the browser uses
    url = "http://127.0.0.1:8000/api/start-ai-analysis/"
    
    # Get CSRF token from Django (simulating browser behavior)
    from django.middleware.csrf import get_token
    from django.test import RequestFactory
    from django.contrib.auth.models import User
    
    factory = RequestFactory()
    request = factory.get('/')
    request.user = User.objects.get(username='admin')  # Use existing admin user
    csrf_token = get_token(request)
    
    print(f"🔑 CSRF Token: {csrf_token}")
    
    # Prepare the exact payload the browser sends
    payload = {
        "cv_ids": [15, 14, 13, 12, 11, 10, 9],  # Same CVs from browser console
        "vacancy_ids": [9],  # Vacancy 9
        "analysis_type": "ai"  # AI analysis
    }
    
    print(f"📤 Payload: {payload}")
    print(f"📏 Payload size: {len(json.dumps(payload))} bytes")
    
    # Prepare headers (same as browser)
    headers = {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrf_token,
        'User-Agent': 'Mozilla/5.0 (Test Script)',
        'Accept': 'application/json',
    }
    
    print(f"📋 Headers: {headers}")
    
    # Make the request with 3 minute timeout (longer than browser)
    print(f"\n🚀 Starting API request at {time.strftime('%H:%M:%S')}")
    print(f"⏱️ Using 180-second timeout...")
    
    try:
        start_time = time.time()
        
        response = requests.post(
            url,
            json=payload,
            headers=headers,
            timeout=180  # 3 minutes
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n📡 Request completed in {duration:.1f} seconds")
        print(f"📊 Response status: {response.status_code}")
        print(f"📏 Response length: {len(response.text)} characters")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"\n✅ SUCCESS! Response data:")
                print(f"   - Total processed: {result.get('total_processed', 'N/A')}")
                print(f"   - Successful: {result.get('successful', 'N/A')}")
                print(f"   - Results count: {len(result.get('results', []))}")
                
                # Show first few results
                results = result.get('results', [])
                for i, res in enumerate(results[:3]):
                    cv_id = res.get('cv_id', 'N/A')
                    status = res.get('status', 'N/A')
                    score = res.get('score', 'N/A')
                    method = res.get('method', 'N/A')
                    print(f"   - CV {cv_id}: {status} ({method} - {score}%)")
                
                if len(results) > 3:
                    print(f"   - ... and {len(results) - 3} more results")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON Decode Error: {e}")
                print(f"Raw response: {response.text[:500]}...")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        end_time = time.time()
        duration = end_time - start_time
        print(f"⏰ REQUEST TIMED OUT after {duration:.1f} seconds")
        
    except requests.exceptions.ConnectionError as e:
        print(f"🔌 CONNECTION ERROR: {e}")
        
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")


def main():
    """Run all tests"""
    print("🚀 Starting Re-analyze Candidates Functionality Test")
    print(f"Timestamp: {datetime.now()}")
    
    # Test 1: AI Configuration
    ai_ok = test_ai_config()
    
    # Test 2: Data availability
    vacancy = test_vacancy_data()
    cv = test_cv_data()
    
    if not vacancy or not cv:
        print("\n❌ Cannot proceed with tests due to missing data")
        return
    
    if not ai_ok:
        print("\n⚠️  Proceeding with tests using fallback analysis (AI not configured)")
    
    # Test 3: Compatibility calculation
    comp_ok = test_compatibility_calculation(cv, vacancy)
    
    # Test 4: Comparison analysis creation
    analysis_ok = test_comparison_analysis_creation(cv, vacancy)
    
    # Test 5: API endpoint simulation
    api_ok = test_api_endpoint_simulation()
    
    # Test 6: Re-analyze API endpoint
    reanalyze_api_ok = test_reanalyze_api_endpoint()
    
    # Final summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"AI Configuration: {'✅ Pass' if ai_ok else '⚠️  Warning (using fallback)'}")
    print(f"Data Availability: {'✅ Pass' if vacancy and cv else '❌ Fail'}")
    print(f"Compatibility Calculation: {'✅ Pass' if comp_ok else '❌ Fail'}")
    print(f"Analysis Creation: {'✅ Pass' if analysis_ok else '❌ Fail'}")
    print(f"API Simulation: {'✅ Pass' if api_ok else '❌ Fail'}")
    print(f"Re-analyze API Endpoint: {'✅ Pass' if reanalyze_api_ok else '❌ Fail'}")
    
    all_pass = ai_ok and vacancy and cv and comp_ok and analysis_ok and api_ok and reanalyze_api_ok
    print(f"\nOverall Status: {'✅ ALL TESTS PASSED' if all_pass else '⚠️  SOME TESTS FAILED'}")
    
    if all_pass:
        print("\n🎉 The Re-analyze Candidates functionality is working correctly!")
        print("You can now test it in the web interface at:")
        print("http://127.0.0.1:8000/vacancy/{vacancy_id}/candidates/")
    else:
        print("\n🔧 Please address the failed tests before using the functionality")


if __name__ == "__main__":
    main() 