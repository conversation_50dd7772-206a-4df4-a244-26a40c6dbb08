#!/usr/bin/env python3
"""
Test script for real-time streaming progress functionality
Tests the enhanced UI progress implementation for CV analysis
"""

import requests
import json
import time
import sys

def test_streaming_progress():
    """Test the streaming progress functionality"""
    
    print("🧪 Testing Real-Time Streaming Progress")
    print("=" * 50)
    
    # Test data
    url = "http://127.0.0.1:8000/api/start-ai-analysis/"
    
    # Get CSRF token first
    session = requests.Session()
    csrf_response = session.get("http://127.0.0.1:8000/vacancy/9/candidates/")
    
    if csrf_response.status_code != 200:
        print("❌ Failed to get CSRF token")
        return False
    
    # Extract CSRF token from cookies
    csrf_token = session.cookies.get('csrftoken')
    if not csrf_token:
        print("❌ CSRF token not found in cookies")
        return False
    
    print(f"✅ CSRF token obtained: {csrf_token[:20]}...")
    
    # Test payload - smaller test with just 3 CVs
    test_payload = {
        "cv_ids": [10, 9, 15],  # Just 3 CVs for faster testing
        "vacancy_ids": [9],
        "analysis_type": "fast",  # Use fast analysis for quicker results
        "stream_mode": True
    }
    
    headers = {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrf_token,
        'Accept': 'text/event-stream',
        'Referer': 'http://127.0.0.1:8000/vacancy/9/candidates/'
    }
    
    print(f"📋 Test payload: {test_payload}")
    print("🌐 Making streaming request...")
    
    try:
        # Make streaming request
        response = session.post(url, 
                              json=test_payload, 
                              headers=headers, 
                              stream=True)
        
        print(f"📡 Response status: {response.status_code}")
        print(f"📡 Response headers: {dict(response.headers)}")
        
        if response.status_code != 200:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response text: {response.text}")
            return False
        
        print("✅ Streaming connection established!")
        print("📺 Reading streaming events...\n")
        
        # Process streaming response
        buffer = ""
        event_count = 0
        cv_events = {}
        
        for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
            if chunk:
                buffer += chunk
                
                # Process complete lines
                lines = buffer.split('\n')
                buffer = lines.pop()  # Keep incomplete line
                
                for line in lines:
                    if line.strip() == '':
                        continue
                    
                    if line.startswith('data: '):
                        try:
                            event_data = json.loads(line[6:])
                            event_count += 1
                            
                            print(f"📡 Event #{event_count}: {event_data['type']}")
                            
                            # Track specific events
                            if event_data['type'] == 'start':
                                print(f"   📋 Starting analysis of {len(event_data['cv_ids'])} CVs: {event_data['cv_ids']}")
                                print(f"   🎯 Analysis type: {event_data['analysis_type'].upper()}")
                                
                            elif event_data['type'] == 'cv_start':
                                cv_id = event_data['cv_id']
                                cv_events[cv_id] = cv_events.get(cv_id, [])
                                cv_events[cv_id].append('start')
                                print(f"   🔍 CV {cv_id} analysis started ({event_data['progress']}/{event_data['total']})")
                                
                            elif event_data['type'] == 'cv_analyzing':
                                cv_id = event_data['cv_id']
                                cv_events[cv_id] = cv_events.get(cv_id, [])
                                cv_events[cv_id].append('analyzing')
                                method_emoji = '🤖' if event_data['method'] == 'AI' else '⚡'
                                print(f"   {method_emoji} CV {cv_id} being analyzed using {event_data['method']} method")
                                
                            elif event_data['type'] == 'cv_complete':
                                cv_id = event_data['cv_id']
                                result = event_data['result']
                                cv_events[cv_id] = cv_events.get(cv_id, [])
                                cv_events[cv_id].append('complete')
                                
                                score_emoji = '🎯' if result['score'] >= 85 else '✅' if result['score'] >= 75 else '⚠️' if result['score'] >= 65 else '❌'
                                print(f"   {score_emoji} CV {cv_id} completed: {result['score']}% - {result['recommendation']}")
                                print(f"      Progress: {event_data['progress']}/{event_data['total']}")
                                
                            elif event_data['type'] == 'cv_error':
                                cv_id = event_data['cv_id']
                                cv_events[cv_id] = cv_events.get(cv_id, [])
                                cv_events[cv_id].append('error')
                                print(f"   ❌ CV {cv_id} error: {event_data['error']}")
                                
                            elif event_data['type'] == 'complete':
                                print(f"   🎉 Analysis complete! {event_data['successful']}/{event_data['total_processed']} successful")
                                
                                # Calculate summary
                                if 'results' in event_data:
                                    successful_results = [r for r in event_data['results'] if r['status'] == 'success']
                                    if successful_results:
                                        avg_score = sum(r['score'] for r in successful_results) / len(successful_results)
                                        high_scores = sum(1 for r in successful_results if r['score'] >= 80)
                                        good_scores = sum(1 for r in successful_results if 70 <= r['score'] < 80)
                                        
                                        print(f"   📊 Summary: Avg {avg_score:.1f}%, High: {high_scores}, Good: {good_scores}")
                                
                                print("\n🏁 Stream completed successfully!")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON decode error: {e}")
                            print(f"   Line: {line}")
                        except Exception as e:
                            print(f"❌ Event processing error: {e}")
                            print(f"   Line: {line}")
                            
        # Final summary
        print(f"\n📊 Test Summary:")
        print(f"   Total events: {event_count}")
        print(f"   CVs tracked: {len(cv_events)}")
        
        for cv_id, events in cv_events.items():
            print(f"   CV {cv_id}: {' → '.join(events)}")
        
        # Validate we got complete flow for each CV
        expected_cvs = test_payload['cv_ids']
        success = True
        
        for cv_id in expected_cvs:
            if cv_id not in cv_events:
                print(f"❌ CV {cv_id}: No events received")
                success = False
            elif 'complete' not in cv_events[cv_id] and 'error' not in cv_events[cv_id]:
                print(f"❌ CV {cv_id}: Missing completion event")
                success = False
            else:
                print(f"✅ CV {cv_id}: Complete event flow")
        
        return success
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Real-Time Streaming Progress Test")
    print("=" * 60)
    
    # Wait a moment for server to be ready
    print("⏳ Waiting for server...")
    time.sleep(2)
    
    success = test_streaming_progress()
    
    if success:
        print("\n🎉 ✅ STREAMING PROGRESS TEST PASSED!")
        print("Real-time UI updates should now work correctly.")
        sys.exit(0)
    else:
        print("\n💥 ❌ STREAMING PROGRESS TEST FAILED!")
        print("Check the server logs and implementation.")
        sys.exit(1) 