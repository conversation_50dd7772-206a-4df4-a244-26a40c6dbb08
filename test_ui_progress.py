#!/usr/bin/env python3
"""
Test the new UI progress functionality
"""

import os
import django
import requests
import json
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cv_analyzer_project.settings')
django.setup()

def test_ui_progress():
    """Test the UI progress with real browser-like request"""
    print("=== Testing UI Progress Functionality ===\n")
    
    session = requests.Session()
    
    # Step 1: Get page to establish session
    print("📄 Getting vacancy page...")
    page_response = session.get("http://127.0.0.1:8000/vacancy/9/candidates/")
    
    if page_response.status_code != 200:
        print(f"❌ Failed to get page: {page_response.status_code}")
        return False
    
    print(f"✅ Page loaded successfully ({page_response.status_code})")
    
    # Extract CSRF token from cookies
    csrf_token = session.cookies.get('csrftoken')
    if not csrf_token:
        print("❌ No CSRF token found in cookies")
        return False
    
    print(f"🔐 CSRF token obtained: {csrf_token[:20]}...")
    
    # Step 2: Test with fast analysis (should complete quickly)
    print("\n🚀 Testing Fast Analysis...")
    
    payload = {
        "cv_ids": [15, 14],  # Just 2 CVs for quick test
        "vacancy_ids": [9],
        "analysis_type": "fast"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrf_token,
        'Referer': 'http://127.0.0.1:8000/vacancy/9/candidates/'
    }
    
    print(f"📤 Sending request: {payload}")
    start_time = time.time()
    
    response = session.post(
        "http://127.0.0.1:8000/api/start-ai-analysis/",
        json=payload,
        headers=headers,
        timeout=60
    )
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"📥 Response received in {duration:.1f}s")
    print(f"📊 Status: {response.status_code}")
    
    if response.status_code == 200:
        try:
            result = response.json()
            print(f"✅ Success: {result.get('success', False)}")
            print(f"📈 Results: {result.get('total_processed', 0)} processed, {result.get('successful', 0)} successful")
            
            if result.get('results'):
                for res in result['results']:
                    cv_id = res.get('cv_id')
                    score = res.get('score', 0)
                    method = res.get('method', 'Unknown')
                    print(f"   📋 CV {cv_id}: {score}% ({method})")
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ Failed to parse JSON response: {e}")
            print(f"Raw response: {response.text[:200]}...")
            return False
    else:
        print(f"❌ Request failed: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
        return False

if __name__ == "__main__":
    success = test_ui_progress()
    if success:
        print(f"\n🎉 UI Progress test completed successfully!")
        print(f"💡 The UI should now show real-time progress messages!")
        print(f"🌐 Visit: http://127.0.0.1:8000/vacancy/9/candidates/")
        print(f"🔘 Click 'Re-analyze Candidates' to see the enhanced UI in action!")
    else:
        print(f"\n❌ UI Progress test failed!") 