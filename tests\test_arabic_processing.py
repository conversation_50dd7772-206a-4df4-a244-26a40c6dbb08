"""
Comprehensive tests for Arabic CV processing functionality.
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Import the modules to test
from cv_analyzer.arabic_processing import ArabicTextProcessor
from cv_analyzer.arabic_nlp import ArabicNLPProcessor
from cv_analyzer.arabic_validation import ArabicCVValidator, ValidationLevel
from cv_analyzer.arabic_cv_utils import ArabicCVProcessor
from cv_analyzer.arabic_ocr import ArabicOCRProcessor

class TestArabicTextProcessor:
    """Test Arabic text processing functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.processor = ArabicTextProcessor()
        
        # Sample Arabic text
        self.arabic_text = "مرحبا، اسمي أحمد محمد وأنا مهندس برمجيات"
        self.english_text = "Hello, my name is <PERSON> and I am a software engineer"
        self.mixed_text = "مرحبا Hello اسمي Ahmed محمد <PERSON>"
    
    def test_language_detection_arabic(self):
        """Test Arabic language detection."""
        result = self.processor.detect_language(self.arabic_text)
        
        assert result['is_arabic'] is True
        assert result['language'] == 'ar'
        assert result['arabic_ratio'] > 0.8
        assert result['confidence'] > 0.7
    
    def test_language_detection_english(self):
        """Test English language detection."""
        result = self.processor.detect_language(self.english_text)
        
        assert result['is_arabic'] is False
        assert result['language'] == 'en'
        assert result['arabic_ratio'] < 0.2
    
    def test_language_detection_mixed(self):
        """Test mixed language detection."""
        result = self.processor.detect_language(self.mixed_text)
        
        assert result['is_mixed'] is True
        assert 0.2 < result['arabic_ratio'] < 0.8
    
    def test_normalize_arabic_text(self):
        """Test Arabic text normalization."""
        text_with_diacritics = "مَرْحَبًا بِكُمْ"
        normalized = self.processor.normalize_arabic_text(text_with_diacritics)
        
        assert "َ" not in normalized  # Fatha removed
        assert "ْ" not in normalized  # Sukun removed
        assert "مرحبا" in normalized
    
    def test_reshape_arabic_text(self):
        """Test Arabic text reshaping for RTL display."""
        reshaped = self.processor.reshape_arabic_text(self.arabic_text)
        
        assert reshaped is not None
        assert len(reshaped) > 0
    
    @patch('cv_analyzer.arabic_processing.GoogleTranslator')
    def test_translation_success(self, mock_translator):
        """Test successful translation."""
        mock_instance = Mock()
        mock_instance.translate.return_value = "Hello, my name is Ahmed"
        mock_translator.return_value = mock_instance
        
        result = self.processor.translate_text(self.arabic_text, 'ar', 'en')
        
        assert result['success'] is True
        assert result['translated_text'] == "Hello, my name is Ahmed"
        assert result['source_language'] == 'ar'
        assert result['target_language'] == 'en'
    
    def test_extract_arabic_entities(self):
        """Test Arabic entity extraction."""
        text_with_entities = "اسمي أحمد محمد، إيميلي <EMAIL>، رقم هاتفي 123456789"
        entities = self.processor.extract_arabic_entities(text_with_entities)
        
        assert 'emails' in entities
        assert 'phones' in entities
        assert 'names' in entities
        assert len(entities['emails']) > 0
        assert len(entities['phones']) > 0

class TestArabicNLPProcessor:
    """Test Arabic NLP processing functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.nlp_processor = ArabicNLPProcessor()
        self.sample_cv_text = """
        أحمد محمد علي
        مهندس برمجيات
        خبرة 5 سنوات في البرمجة
        بكالوريوس علوم حاسوب
        مهارات: جافا، بايثون، قواعد البيانات
        إيميل: <EMAIL>
        هاتف: +966501234567
        """
    
    def test_process_arabic_cv_text(self):
        """Test comprehensive Arabic CV text processing."""
        result = self.nlp_processor.process_arabic_cv_text(self.sample_cv_text)
        
        assert result['success'] is True
        assert 'normalized_text' in result
        assert 'tokens' in result
        assert 'entities' in result
        assert 'skills' in result
        assert 'job_titles' in result
        assert 'education' in result
    
    def test_extract_arabic_skills(self):
        """Test Arabic skill extraction."""
        skills_text = "مهارات البرمجة: جافا، بايثون، سي شارب، قواعد البيانات"
        skills = self.nlp_processor._extract_arabic_skills(skills_text)
        
        assert len(skills) > 0
        skill_texts = [skill.skill for skill in skills]
        assert any('جافا' in skill for skill in skill_texts)
    
    def test_extract_arabic_job_titles(self):
        """Test Arabic job title extraction."""
        job_text = "عملت كمطور برمجيات ومهندس شبكات"
        job_titles = self.nlp_processor._extract_arabic_job_titles(job_text)
        
        assert len(job_titles) > 0
        assert any('مطور برمجيات' in title['arabic_title'] for title in job_titles)
    
    def test_extract_arabic_education(self):
        """Test Arabic education extraction."""
        edu_text = "حاصل على بكالوريوس في علوم الحاسوب وماجستير في إدارة الأعمال"
        education = self.nlp_processor._extract_arabic_education(edu_text)
        
        assert len(education) > 0
        assert any('بكالوريوس' in edu['arabic_degree'] for edu in education)

class TestArabicCVValidator:
    """Test Arabic CV validation functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = ArabicCVValidator()
        
        self.good_processed_data = {
            'language_analysis': {
                'is_arabic': True,
                'arabic_ratio': 0.8,
                'confidence': 0.9,
                'language': 'ar'
            },
            'entities': {
                'emails': ['<EMAIL>'],
                'phones': ['123456789'],
                'names': ['أحمد محمد']
            },
            'translation': {
                'success': True,
                'translated_text': 'Ahmed Mohamed Software Engineer'
            }
        }
        
        self.poor_processed_data = {
            'language_analysis': {
                'is_arabic': False,
                'arabic_ratio': 0.1,
                'confidence': 0.3,
                'language': 'unknown'
            },
            'entities': {}
        }
    
    def test_validate_good_processing(self):
        """Test validation of good processing results."""
        original_text = "أحمد محمد مهندس برمجيات <EMAIL> 123456789"
        
        result = self.validator.validate_cv_processing(
            original_text, 
            self.good_processed_data
        )
        
        assert result.is_valid is True
        assert result.quality_score > 0.7
        assert len([issue for issue in result.issues if issue.level == ValidationLevel.CRITICAL]) == 0
    
    def test_validate_poor_processing(self):
        """Test validation of poor processing results."""
        original_text = "أحمد محمد مهندس برمجيات"
        
        result = self.validator.validate_cv_processing(
            original_text, 
            self.poor_processed_data
        )
        
        assert result.quality_score < 0.5
        assert len(result.issues) > 0
        assert len(result.recommendations) > 0
    
    def test_validate_empty_text(self):
        """Test validation with empty text."""
        result = self.validator.validate_cv_processing("", {})
        
        assert result.is_valid is False
        assert result.quality_score == 0.0
        assert any(issue.level == ValidationLevel.CRITICAL for issue in result.issues)
    
    def test_validate_batch_processing(self):
        """Test batch processing validation."""
        batch_results = [
            {
                'success': True,
                'original_text': 'أحمد محمد',
                'processed_data': self.good_processed_data
            },
            {
                'success': False,
                'original_text': '',
                'processed_data': {}
            }
        ]
        
        result = self.validator.validate_batch_processing(batch_results)
        
        assert result['total_cvs'] == 2
        assert result['successful_processing'] == 1
        assert result['failed_processing'] == 1

class TestArabicCVProcessor:
    """Test comprehensive Arabic CV processing."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.processor = ArabicCVProcessor()
    
    @patch('cv_analyzer.arabic_cv_utils.CVTextExtractor')
    def test_process_arabic_cv(self, mock_extractor):
        """Test complete Arabic CV processing pipeline."""
        # Mock text extraction
        mock_instance = Mock()
        mock_instance.extract_from_file.return_value = (
            "أحمد محمد مهندس برمجيات",
            Mock(filename="test.pdf", file_type="pdf")
        )
        mock_extractor.return_value = mock_instance
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        try:
            result = self.processor.process_arabic_cv(tmp_path)
            
            assert 'success' in result
            assert 'original_text' in result
            assert 'language_analysis' in result
            assert 'quality_score' in result
        finally:
            os.unlink(tmp_path)

class TestArabicOCRProcessor:
    """Test Arabic OCR processing functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.ocr_processor = ArabicOCRProcessor()
    
    @patch('cv_analyzer.arabic_ocr.pytesseract')
    @patch('cv_analyzer.arabic_ocr.Image')
    def test_extract_text_from_image(self, mock_image, mock_tesseract):
        """Test OCR text extraction from image."""
        # Mock Tesseract OCR
        mock_tesseract.image_to_string.return_value = "أحمد محمد"
        mock_tesseract.image_to_data.return_value = {
            'conf': ['85', '90', '80']
        }
        
        # Mock PIL Image
        mock_img = Mock()
        mock_img.size = (1000, 1000)
        
        result = self.ocr_processor.extract_text_from_image(mock_img, 'arabic')
        
        assert result['success'] is True
        assert result['text'] == "أحمد محمد"
        assert result['confidence'] > 0
    
    @patch('cv_analyzer.arabic_ocr.fitz')
    def test_extract_from_pdf_with_ocr(self, mock_fitz):
        """Test PDF OCR extraction."""
        # Mock PyMuPDF
        mock_doc = Mock()
        mock_page = Mock()
        mock_page.get_text.return_value = ""  # No text, force OCR
        mock_page.get_pixmap.return_value.tobytes.return_value = b"fake_image_data"
        mock_doc.load_page.return_value = mock_page
        mock_doc.__len__.return_value = 1
        mock_fitz.open.return_value = mock_doc
        
        with patch.object(self.ocr_processor, 'extract_text_from_image') as mock_ocr:
            mock_ocr.return_value = {
                'success': True,
                'text': 'أحمد محمد',
                'confidence': 85,
                'language_info': {'is_arabic': True, 'arabic_ratio': 0.9}
            }
            
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            try:
                result = self.ocr_processor.extract_from_pdf_with_ocr(tmp_path)
                
                assert result['success'] is True
                assert 'أحمد محمد' in result['text']
                assert result['total_pages'] == 1
            finally:
                os.unlink(tmp_path)

# Integration tests
class TestArabicProcessingIntegration:
    """Integration tests for Arabic processing components."""
    
    def test_end_to_end_processing(self):
        """Test end-to-end Arabic CV processing."""
        # This would test the complete pipeline from file input to final analysis
        # In a real scenario, you would use actual Arabic CV files
        
        processor = ArabicTextProcessor()
        nlp_processor = ArabicNLPProcessor()
        validator = ArabicCVValidator()
        
        # Sample Arabic CV text
        arabic_cv_text = """
        أحمد محمد علي
        مهندس برمجيات أول
        خبرة 7 سنوات في تطوير التطبيقات
        بكالوريوس علوم حاسوب - جامعة الملك سعود
        ماجستير إدارة أعمال
        
        المهارات التقنية:
        - جافا، بايثون، سي شارب
        - قواعد البيانات: MySQL، Oracle
        - تطوير الويب: HTML، CSS، JavaScript
        
        الخبرة العملية:
        مطور برمجيات أول - شركة التقنية المتقدمة (2020-2023)
        مطور تطبيقات - شركة الحلول الذكية (2017-2020)
        
        معلومات الاتصال:
        الإيميل: <EMAIL>
        الهاتف: +966501234567
        الموقع: الرياض، المملكة العربية السعودية
        """
        
        # Step 1: Language detection
        lang_result = processor.detect_language(arabic_cv_text)
        assert lang_result['is_arabic'] is True
        
        # Step 2: NLP processing
        nlp_result = nlp_processor.process_arabic_cv_text(arabic_cv_text)
        assert nlp_result['success'] is True
        
        # Step 3: Validation
        processed_data = {
            'language_analysis': lang_result,
            'entities': nlp_result['entities'] if 'entities' in nlp_result else {},
            'skills': nlp_result['skills'] if 'skills' in nlp_result else [],
            'job_titles': nlp_result['job_titles'] if 'job_titles' in nlp_result else []
        }
        
        validation_result = validator.validate_cv_processing(
            arabic_cv_text, 
            processed_data
        )
        
        assert validation_result.is_valid is True
        assert validation_result.quality_score > 0.6

# Performance tests
class TestArabicProcessingPerformance:
    """Performance tests for Arabic processing."""
    
    def test_processing_speed(self):
        """Test processing speed with various text sizes."""
        processor = ArabicTextProcessor()
        
        # Small text
        small_text = "أحمد محمد مهندس"
        start_time = pytest.approx(0, abs=1e-3)
        result = processor.detect_language(small_text)
        # Processing should be fast for small text
        assert result['is_arabic'] is True
        
        # Large text (simulate large CV)
        large_text = "أحمد محمد مهندس برمجيات " * 1000
        result = processor.detect_language(large_text)
        # Should still work with large text
        assert result['is_arabic'] is True

if __name__ == '__main__':
    pytest.main([__file__])
