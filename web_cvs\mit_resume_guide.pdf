<!DOCTYPE html>
<html lang="en-US"  class="home wp-singular page-template-default page page-id-6 wp-theme-uConnect wp-child-theme-uConnect_MIT no-js uconnect page-front-page full-width-hero hero-space-banner has-hero-nav double-column">
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/><script type="text/javascript">(window.NREUM||(NREUM={})).init={privacy:{cookies_enabled:true},ajax:{deny_list:["bam.nr-data.net"]},distributed_tracing:{enabled:true}};(window.NREUM||(NREUM={})).loader_config={agentID:"**********",accountID:"1407917",trustKey:"1407917",xpid:"VQIHVl9SDxABUVlUAAQFVFMF",licenseKey:"NRBR-725fd230f226dcacc90",applicationID:"**********"};;/*! For license information please see nr-loader-spa-1.292.0.min.js.LICENSE.txt */
(()=>{var e,t,r={8122:(e,t,r)=>{"use strict";r.d(t,{a:()=>i});var n=r(944);function i(e,t){try{if(!e||"object"!=typeof e)return(0,n.R)(3);if(!t||"object"!=typeof t)return(0,n.R)(4);const r=Object.create(Object.getPrototypeOf(t),Object.getOwnPropertyDescriptors(t)),o=0===Object.keys(r).length?e:r;for(let a in o)if(void 0!==e[a])try{if(null===e[a]){r[a]=null;continue}Array.isArray(e[a])&&Array.isArray(t[a])?r[a]=Array.from(new Set([...e[a],...t[a]])):"object"==typeof e[a]&&"object"==typeof t[a]?r[a]=i(e[a],t[a]):r[a]=e[a]}catch(e){r[a]||(0,n.R)(1,e)}return r}catch(e){(0,n.R)(2,e)}}},2555:(e,t,r)=>{"use strict";r.d(t,{D:()=>s,f:()=>a});var n=r(384),i=r(8122);const o={beacon:n.NT.beacon,errorBeacon:n.NT.errorBeacon,licenseKey:void 0,applicationID:void 0,sa:void 0,queueTime:void 0,applicationTime:void 0,ttGuid:void 0,user:void 0,account:void 0,product:void 0,extra:void 0,jsAttributes:{},userAttributes:void 0,atts:void 0,transactionName:void 0,tNamePlain:void 0};function a(e){try{return!!e.licenseKey&&!!e.errorBeacon&&!!e.applicationID}catch(e){return!1}}const s=e=>(0,i.a)(e,o)},9324:(e,t,r)=>{"use strict";r.d(t,{F3:()=>i,Xs:()=>o,Yq:()=>a,xv:()=>n});const n="1.292.0",i="PROD",o="CDN",a="^2.0.0-alpha.18"},6154:(e,t,r)=>{"use strict";r.d(t,{A4:()=>s,OF:()=>d,RI:()=>i,WN:()=>h,bv:()=>o,gm:()=>a,lR:()=>f,m:()=>u,mw:()=>c,sb:()=>l});var n=r(1863);const i="undefined"!=typeof window&&!!window.document,o="undefined"!=typeof WorkerGlobalScope&&("undefined"!=typeof self&&self instanceof WorkerGlobalScope&&self.navigator instanceof WorkerNavigator||"undefined"!=typeof globalThis&&globalThis instanceof WorkerGlobalScope&&globalThis.navigator instanceof WorkerNavigator),a=i?window:"undefined"!=typeof WorkerGlobalScope&&("undefined"!=typeof self&&self instanceof WorkerGlobalScope&&self||"undefined"!=typeof globalThis&&globalThis instanceof WorkerGlobalScope&&globalThis),s="complete"===a?.document?.readyState,c=Boolean("hidden"===a?.document?.visibilityState),u=""+a?.location,d=/iPad|iPhone|iPod/.test(a.navigator?.userAgent),l=d&&"undefined"==typeof SharedWorker,f=(()=>{const e=a.navigator?.userAgent?.match(/Firefox[/\s](\d+\.\d+)/);return Array.isArray(e)&&e.length>=2?+e[1]:0})(),h=Date.now()-(0,n.t)()},7295:(e,t,r)=>{"use strict";r.d(t,{Xv:()=>a,gX:()=>i,iW:()=>o});var n=[];function i(e){if(!e||o(e))return!1;if(0===n.length)return!0;for(var t=0;t<n.length;t++){var r=n[t];if("*"===r.hostname)return!1;if(s(r.hostname,e.hostname)&&c(r.pathname,e.pathname))return!1}return!0}function o(e){return void 0===e.hostname}function a(e){if(n=[],e&&e.length)for(var t=0;t<e.length;t++){let r=e[t];if(!r)continue;0===r.indexOf("http://")?r=r.substring(7):0===r.indexOf("https://")&&(r=r.substring(8));const i=r.indexOf("/");let o,a;i>0?(o=r.substring(0,i),a=r.substring(i)):(o=r,a="");let[s]=o.split(":");n.push({hostname:s,pathname:a})}}function s(e,t){return!(e.length>t.length)&&t.indexOf(e)===t.length-e.length}function c(e,t){return 0===e.indexOf("/")&&(e=e.substring(1)),0===t.indexOf("/")&&(t=t.substring(1)),""===e||e===t}},3241:(e,t,r)=>{"use strict";r.d(t,{W:()=>o});var n=r(6154);const i="newrelic";function o(e={}){try{n.gm.dispatchEvent(new CustomEvent(i,{detail:e}))}catch(e){}}},1687:(e,t,r)=>{"use strict";r.d(t,{Ak:()=>u,Ze:()=>f,x3:()=>d});var n=r(3241),i=r(7836),o=r(3606),a=r(860),s=r(2646);const c={};function u(e,t){const r={staged:!1,priority:a.P3[t]||0};l(e),c[e].get(t)||c[e].set(t,r)}function d(e,t){e&&c[e]&&(c[e].get(t)&&c[e].delete(t),p(e,t,!1),c[e].size&&h(e))}function l(e){if(!e)throw new Error("agentIdentifier required");c[e]||(c[e]=new Map)}function f(e="",t="feature",r=!1){if(l(e),!e||!c[e].get(t)||r)return p(e,t);c[e].get(t).staged=!0,h(e)}function h(e){const t=Array.from(c[e]);t.every((([e,t])=>t.staged))&&(t.sort(((e,t)=>e[1].priority-t[1].priority)),t.forEach((([t])=>{c[e].delete(t),p(e,t)})))}function p(e,t,r=!0){const a=e?i.ee.get(e):i.ee,c=o.i.handlers;if(!a.aborted&&a.backlog&&c){if((0,n.W)({agentIdentifier:e,type:"lifecycle",name:"drain",feature:t}),r){const e=a.backlog[t],r=c[t];if(r){for(let t=0;e&&t<e.length;++t)g(e[t],r);Object.entries(r).forEach((([e,t])=>{Object.values(t||{}).forEach((t=>{t[0]?.on&&t[0]?.context()instanceof s.y&&t[0].on(e,t[1])}))}))}}a.isolatedBacklog||delete c[t],a.backlog[t]=null,a.emit("drain-"+t,[])}}function g(e,t){var r=e[1];Object.values(t[r]||{}).forEach((t=>{var r=e[0];if(t[0]===r){var n=t[1],i=e[3],o=e[2];n.apply(i,o)}}))}},7836:(e,t,r)=>{"use strict";r.d(t,{P:()=>s,ee:()=>c});var n=r(384),i=r(8990),o=r(2646),a=r(5607);const s="nr@context:".concat(a.W),c=function e(t,r){var n={},a={},d={},l=!1;try{l=16===r.length&&u.initializedAgents?.[r]?.runtime.isolatedBacklog}catch(e){}var f={on:p,addEventListener:p,removeEventListener:function(e,t){var r=n[e];if(!r)return;for(var i=0;i<r.length;i++)r[i]===t&&r.splice(i,1)},emit:function(e,r,n,i,o){!1!==o&&(o=!0);if(c.aborted&&!i)return;t&&o&&t.emit(e,r,n);for(var s=h(n),u=g(e),d=u.length,l=0;l<d;l++)u[l].apply(s,r);var p=v()[a[e]];p&&p.push([f,e,r,s]);return s},get:m,listeners:g,context:h,buffer:function(e,t){const r=v();if(t=t||"feature",f.aborted)return;Object.entries(e||{}).forEach((([e,n])=>{a[n]=t,t in r||(r[t]=[])}))},abort:function(){f._aborted=!0,Object.keys(f.backlog).forEach((e=>{delete f.backlog[e]}))},isBuffering:function(e){return!!v()[a[e]]},debugId:r,backlog:l?{}:t&&"object"==typeof t.backlog?t.backlog:{},isolatedBacklog:l};return Object.defineProperty(f,"aborted",{get:()=>{let e=f._aborted||!1;return e||(t&&(e=t.aborted),e)}}),f;function h(e){return e&&e instanceof o.y?e:e?(0,i.I)(e,s,(()=>new o.y(s))):new o.y(s)}function p(e,t){n[e]=g(e).concat(t)}function g(e){return n[e]||[]}function m(t){return d[t]=d[t]||e(f,t)}function v(){return f.backlog}}(void 0,"globalEE"),u=(0,n.Zm)();u.ee||(u.ee=c)},2646:(e,t,r)=>{"use strict";r.d(t,{y:()=>n});class n{constructor(e){this.contextId=e}}},9908:(e,t,r)=>{"use strict";r.d(t,{d:()=>n,p:()=>i});var n=r(7836).ee.get("handle");function i(e,t,r,i,o){o?(o.buffer([e],i),o.emit(e,t,r)):(n.buffer([e],i),n.emit(e,t,r))}},3606:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var n=r(9908);o.on=a;var i=o.handlers={};function o(e,t,r,o){a(o||n.d,i,e,t,r)}function a(e,t,r,i,o){o||(o="feature"),e||(e=n.d);var a=t[o]=t[o]||{};(a[r]=a[r]||[]).push([e,i])}},3878:(e,t,r)=>{"use strict";function n(e,t){return{capture:e,passive:!1,signal:t}}function i(e,t,r=!1,i){window.addEventListener(e,t,n(r,i))}function o(e,t,r=!1,i){document.addEventListener(e,t,n(r,i))}r.d(t,{DD:()=>o,jT:()=>n,sp:()=>i})},5607:(e,t,r)=>{"use strict";r.d(t,{W:()=>n});const n=(0,r(9566).bz)()},9566:(e,t,r)=>{"use strict";r.d(t,{LA:()=>s,ZF:()=>c,bz:()=>a,el:()=>u});var n=r(6154);const i="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx";function o(e,t){return e?15&e[t]:16*Math.random()|0}function a(){const e=n.gm?.crypto||n.gm?.msCrypto;let t,r=0;return e&&e.getRandomValues&&(t=e.getRandomValues(new Uint8Array(30))),i.split("").map((e=>"x"===e?o(t,r++).toString(16):"y"===e?(3&o()|8).toString(16):e)).join("")}function s(e){const t=n.gm?.crypto||n.gm?.msCrypto;let r,i=0;t&&t.getRandomValues&&(r=t.getRandomValues(new Uint8Array(e)));const a=[];for(var s=0;s<e;s++)a.push(o(r,i++).toString(16));return a.join("")}function c(){return s(16)}function u(){return s(32)}},2614:(e,t,r)=>{"use strict";r.d(t,{BB:()=>a,H3:()=>n,g:()=>u,iL:()=>c,tS:()=>s,uh:()=>i,wk:()=>o});const n="NRBA",i="SESSION",o=144e5,a=18e5,s={STARTED:"session-started",PAUSE:"session-pause",RESET:"session-reset",RESUME:"session-resume",UPDATE:"session-update"},c={SAME_TAB:"same-tab",CROSS_TAB:"cross-tab"},u={OFF:0,FULL:1,ERROR:2}},1863:(e,t,r)=>{"use strict";function n(){return Math.floor(performance.now())}r.d(t,{t:()=>n})},7485:(e,t,r)=>{"use strict";r.d(t,{D:()=>i});var n=r(6154);function i(e){if(0===(e||"").indexOf("data:"))return{protocol:"data"};try{const t=new URL(e,location.href),r={port:t.port,hostname:t.hostname,pathname:t.pathname,search:t.search,protocol:t.protocol.slice(0,t.protocol.indexOf(":")),sameOrigin:t.protocol===n.gm?.location?.protocol&&t.host===n.gm?.location?.host};return r.port&&""!==r.port||("http:"===t.protocol&&(r.port="80"),"https:"===t.protocol&&(r.port="443")),r.pathname&&""!==r.pathname?r.pathname.startsWith("/")||(r.pathname="/".concat(r.pathname)):r.pathname="/",r}catch(e){return{}}}},944:(e,t,r)=>{"use strict";r.d(t,{R:()=>i});var n=r(3241);function i(e,t){"function"==typeof console.debug&&(console.debug("New Relic Warning: https://github.com/newrelic/newrelic-browser-agent/blob/main/docs/warning-codes.md#".concat(e),t),(0,n.W)({agentIdentifier:null,drained:null,type:"data",name:"warn",feature:"warn",data:{code:e,secondary:t}}))}},5701:(e,t,r)=>{"use strict";r.d(t,{B:()=>o,t:()=>a});var n=r(3241);const i=new Set,o={};function a(e,t){const r=t.agentIdentifier;o[r]??={},e&&"object"==typeof e&&(i.has(r)||(t.ee.emit("rumresp",[e]),o[r]=e,i.add(r),(0,n.W)({agentIdentifier:r,loaded:!0,drained:!0,type:"lifecycle",name:"load",feature:void 0,data:e})))}},8990:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var n=Object.prototype.hasOwnProperty;function i(e,t,r){if(n.call(e,t))return e[t];var i=r();if(Object.defineProperty&&Object.keys)try{return Object.defineProperty(e,t,{value:i,writable:!0,enumerable:!1}),i}catch(e){}return e[t]=i,i}},6389:(e,t,r)=>{"use strict";function n(e,t=500,r={}){const n=r?.leading||!1;let i;return(...r)=>{n&&void 0===i&&(e.apply(this,r),i=setTimeout((()=>{i=clearTimeout(i)}),t)),n||(clearTimeout(i),i=setTimeout((()=>{e.apply(this,r)}),t))}}function i(e){let t=!1;return(...r)=>{t||(t=!0,e.apply(this,r))}}r.d(t,{J:()=>i,s:()=>n})},3304:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(7836);const i=()=>{const e=new WeakSet;return(t,r)=>{if("object"==typeof r&&null!==r){if(e.has(r))return;e.add(r)}return r}};function o(e){try{return JSON.stringify(e,i())??""}catch(e){try{n.ee.emit("internal-error",[e])}catch(e){}return""}}},3496:(e,t,r)=>{"use strict";function n(e){return!e||!(!e.licenseKey||!e.applicationID)}function i(e,t){return!e||e.licenseKey===t.info.licenseKey&&e.applicationID===t.info.applicationID}r.d(t,{A:()=>i,I:()=>n})},5289:(e,t,r)=>{"use strict";r.d(t,{GG:()=>o,Qr:()=>s,sB:()=>a});var n=r(3878);function i(){return"undefined"==typeof document||"complete"===document.readyState}function o(e,t){if(i())return e();(0,n.sp)("load",e,t)}function a(e){if(i())return e();(0,n.DD)("DOMContentLoaded",e)}function s(e){if(i())return e();(0,n.sp)("popstate",e)}},384:(e,t,r)=>{"use strict";r.d(t,{NT:()=>o,US:()=>u,Zm:()=>a,bQ:()=>c,dV:()=>s,pV:()=>d});var n=r(6154),i=r(1863);const o={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net"};function a(){return n.gm.NREUM||(n.gm.NREUM={}),void 0===n.gm.newrelic&&(n.gm.newrelic=n.gm.NREUM),n.gm.NREUM}function s(){let e=a();return e.o||(e.o={ST:n.gm.setTimeout,SI:n.gm.setImmediate,CT:n.gm.clearTimeout,XHR:n.gm.XMLHttpRequest,REQ:n.gm.Request,EV:n.gm.Event,PR:n.gm.Promise,MO:n.gm.MutationObserver,FETCH:n.gm.fetch,WS:n.gm.WebSocket}),e}function c(e,t){let r=a();r.initializedAgents??={},t.initializedAt={ms:(0,i.t)(),date:new Date},r.initializedAgents[e]=t}function u(e,t){a()[e]=t}function d(){return function(){let e=a();const t=e.info||{};e.info={beacon:o.beacon,errorBeacon:o.errorBeacon,...t}}(),function(){let e=a();const t=e.init||{};e.init={...t}}(),s(),function(){let e=a();const t=e.loader_config||{};e.loader_config={...t}}(),a()}},2843:(e,t,r)=>{"use strict";r.d(t,{u:()=>i});var n=r(3878);function i(e,t=!1,r,i){(0,n.DD)("visibilitychange",(function(){if(t)return void("hidden"===document.visibilityState&&e());e(document.visibilityState)}),r,i)}},8139:(e,t,r)=>{"use strict";r.d(t,{u:()=>f});var n=r(7836),i=r(3434),o=r(8990),a=r(6154);const s={},c=a.gm.XMLHttpRequest,u="addEventListener",d="removeEventListener",l="nr@wrapped:".concat(n.P);function f(e){var t=function(e){return(e||n.ee).get("events")}(e);if(s[t.debugId]++)return t;s[t.debugId]=1;var r=(0,i.YM)(t,!0);function f(e){r.inPlace(e,[u,d],"-",p)}function p(e,t){return e[1]}return"getPrototypeOf"in Object&&(a.RI&&h(document,f),c&&h(c.prototype,f),h(a.gm,f)),t.on(u+"-start",(function(e,t){var n=e[1];if(null!==n&&("function"==typeof n||"object"==typeof n)){var i=(0,o.I)(n,l,(function(){var e={object:function(){if("function"!=typeof n.handleEvent)return;return n.handleEvent.apply(n,arguments)},function:n}[typeof n];return e?r(e,"fn-",null,e.name||"anonymous"):n}));this.wrapped=e[1]=i}})),t.on(d+"-start",(function(e){e[1]=this.wrapped||e[1]})),t}function h(e,t,...r){let n=e;for(;"object"==typeof n&&!Object.prototype.hasOwnProperty.call(n,u);)n=Object.getPrototypeOf(n);n&&t(n,...r)}},3434:(e,t,r)=>{"use strict";r.d(t,{Jt:()=>o,YM:()=>c});var n=r(7836),i=r(5607);const o="nr@original:".concat(i.W);var a=Object.prototype.hasOwnProperty,s=!1;function c(e,t){return e||(e=n.ee),r.inPlace=function(e,t,n,i,o){n||(n="");const a="-"===n.charAt(0);for(let s=0;s<t.length;s++){const c=t[s],u=e[c];d(u)||(e[c]=r(u,a?c+n:n,i,c,o))}},r.flag=o,r;function r(t,r,n,s,c){return d(t)?t:(r||(r=""),nrWrapper[o]=t,function(e,t,r){if(Object.defineProperty&&Object.keys)try{return Object.keys(e).forEach((function(r){Object.defineProperty(t,r,{get:function(){return e[r]},set:function(t){return e[r]=t,t}})})),t}catch(e){u([e],r)}for(var n in e)a.call(e,n)&&(t[n]=e[n])}(t,nrWrapper,e),nrWrapper);function nrWrapper(){var o,a,d,l;try{a=this,o=[...arguments],d="function"==typeof n?n(o,a):n||{}}catch(t){u([t,"",[o,a,s],d],e)}i(r+"start",[o,a,s],d,c);try{return l=t.apply(a,o)}catch(e){throw i(r+"err",[o,a,e],d,c),e}finally{i(r+"end",[o,a,l],d,c)}}}function i(r,n,i,o){if(!s||t){var a=s;s=!0;try{e.emit(r,n,i,t,o)}catch(t){u([t,r,n,i],e)}s=a}}}function u(e,t){t||(t=n.ee);try{t.emit("internal-error",e)}catch(e){}}function d(e){return!(e&&"function"==typeof e&&e.apply&&!e[o])}},9300:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.ajax},3333:(e,t,r)=>{"use strict";r.d(t,{$v:()=>u,TZ:()=>n,Zp:()=>i,kd:()=>c,mq:()=>s,nf:()=>a,qN:()=>o});const n=r(860).K7.genericEvents,i=["auxclick","click","copy","keydown","paste","scrollend"],o=["focus","blur"],a=4,s=1e3,c=["PageAction","UserAction","BrowserPerformance"],u={MARKS:"experimental.marks",MEASURES:"experimental.measures",RESOURCES:"experimental.resources"}},6774:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.jserrors},993:(e,t,r)=>{"use strict";r.d(t,{A$:()=>o,ET:()=>a,TZ:()=>s,p_:()=>i});var n=r(860);const i={ERROR:"ERROR",WARN:"WARN",INFO:"INFO",DEBUG:"DEBUG",TRACE:"TRACE"},o={OFF:0,ERROR:1,WARN:2,INFO:3,DEBUG:4,TRACE:5},a="log",s=n.K7.logging},3785:(e,t,r)=>{"use strict";r.d(t,{R:()=>c,b:()=>u});var n=r(9908),i=r(1863),o=r(860),a=r(8154),s=r(993);function c(e,t,r={},c=s.p_.INFO,u,d=(0,i.t)()){(0,n.p)(a.xV,["API/logging/".concat(c.toLowerCase(),"/called")],void 0,o.K7.metrics,e),(0,n.p)(s.ET,[d,t,r,c,u],void 0,o.K7.logging,e)}function u(e){return"string"==typeof e&&Object.values(s.p_).some((t=>t===e.toUpperCase().trim()))}},8154:(e,t,r)=>{"use strict";r.d(t,{z_:()=>o,XG:()=>s,TZ:()=>n,rs:()=>i,xV:()=>a});r(6154),r(9566),r(384);const n=r(860).K7.metrics,i="sm",o="cm",a="storeSupportabilityMetrics",s="storeEventMetrics"},6630:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.pageViewEvent},782:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.pageViewTiming},6344:(e,t,r)=>{"use strict";r.d(t,{BB:()=>d,G4:()=>o,Qb:()=>l,TZ:()=>i,Ug:()=>a,_s:()=>s,bc:()=>u,yP:()=>c});var n=r(2614);const i=r(860).K7.sessionReplay,o={RECORD:"recordReplay",PAUSE:"pauseReplay",ERROR_DURING_REPLAY:"errorDuringReplay"},a=.12,s={DomContentLoaded:0,Load:1,FullSnapshot:2,IncrementalSnapshot:3,Meta:4,Custom:5},c={[n.g.ERROR]:15e3,[n.g.FULL]:3e5,[n.g.OFF]:0},u={RESET:{message:"Session was reset",sm:"Reset"},IMPORT:{message:"Recorder failed to import",sm:"Import"},TOO_MANY:{message:"429: Too Many Requests",sm:"Too-Many"},TOO_BIG:{message:"Payload was too large",sm:"Too-Big"},CROSS_TAB:{message:"Session Entity was set to OFF on another tab",sm:"Cross-Tab"},ENTITLEMENTS:{message:"Session Replay is not allowed and will not be started",sm:"Entitlement"}},d=5e3,l={API:"api"}},5270:(e,t,r)=>{"use strict";r.d(t,{Aw:()=>s,CT:()=>c,SR:()=>a,rF:()=>u});var n=r(384),i=r(7767),o=r(6154);function a(e){return!!(0,n.dV)().o.MO&&(0,i.V)(e)&&!0===e?.session_trace.enabled}function s(e){return!0===e?.session_replay.preload&&a(e)}function c(e,t){const r=t.correctAbsoluteTimestamp(e);return{originalTimestamp:e,correctedTimestamp:r,timestampDiff:e-r,originTime:o.WN,correctedOriginTime:t.correctedOriginTime,originTimeDiff:Math.floor(o.WN-t.correctedOriginTime)}}function u(e,t){try{if("string"==typeof t?.type){if("password"===t.type.toLowerCase())return"*".repeat(e?.length||0);if(void 0!==t?.dataset?.nrUnmask||t?.classList?.contains("nr-unmask"))return e}}catch(e){}return"string"==typeof e?e.replace(/[\S]/g,"*"):"*".repeat(e?.length||0)}},3738:(e,t,r)=>{"use strict";r.d(t,{He:()=>i,Kp:()=>s,Lc:()=>u,Rz:()=>d,TZ:()=>n,bD:()=>o,d3:()=>a,jx:()=>l,uP:()=>c});const n=r(860).K7.sessionTrace,i="bstResource",o="resource",a="-start",s="-end",c="fn"+a,u="fn"+s,d="pushState",l=1e3},3962:(e,t,r)=>{"use strict";r.d(t,{AM:()=>o,O2:()=>c,Qu:()=>u,TZ:()=>s,ih:()=>d,pP:()=>a,tC:()=>i});var n=r(860);const i=["click","keydown","submit","popstate"],o="api",a="initialPageLoad",s=n.K7.softNav,c={INITIAL_PAGE_LOAD:"",ROUTE_CHANGE:1,UNSPECIFIED:2},u={INTERACTION:1,AJAX:2,CUSTOM_END:3,CUSTOM_TRACER:4},d={IP:"in progress",FIN:"finished",CAN:"cancelled"}},7378:(e,t,r)=>{"use strict";r.d(t,{$p:()=>x,BR:()=>b,Kp:()=>w,L3:()=>y,Lc:()=>c,NC:()=>o,SG:()=>d,TZ:()=>i,U6:()=>p,UT:()=>m,d3:()=>R,dT:()=>f,e5:()=>A,gx:()=>v,l9:()=>l,oW:()=>h,op:()=>g,rw:()=>u,tH:()=>E,uP:()=>s,wW:()=>T,xq:()=>a});var n=r(384);const i=r(860).K7.spa,o=["click","submit","keypress","keydown","keyup","change"],a=999,s="fn-start",c="fn-end",u="cb-start",d="api-ixn-",l="remaining",f="interaction",h="spaNode",p="jsonpNode",g="fetch-start",m="fetch-done",v="fetch-body-",b="jsonp-end",y=(0,n.dV)().o.ST,R="-start",w="-end",x="-body",T="cb"+w,A="jsTime",E="fetch"},4234:(e,t,r)=>{"use strict";r.d(t,{W:()=>o});var n=r(7836),i=r(1687);class o{constructor(e,t){this.agentIdentifier=e,this.ee=n.ee.get(e),this.featureName=t,this.blocked=!1}deregisterDrain(){(0,i.x3)(this.agentIdentifier,this.featureName)}}},7767:(e,t,r)=>{"use strict";r.d(t,{V:()=>i});var n=r(6154);const i=e=>n.RI&&!0===e?.privacy.cookies_enabled},1741:(e,t,r)=>{"use strict";r.d(t,{W:()=>o});var n=r(944),i=r(4261);class o{#e(e,...t){if(this[e]!==o.prototype[e])return this[e](...t);(0,n.R)(35,e)}addPageAction(e,t){return this.#e(i.hG,e,t)}register(e){return this.#e(i.eY,e)}recordCustomEvent(e,t){return this.#e(i.fF,e,t)}setPageViewName(e,t){return this.#e(i.Fw,e,t)}setCustomAttribute(e,t,r){return this.#e(i.cD,e,t,r)}noticeError(e,t){return this.#e(i.o5,e,t)}setUserId(e){return this.#e(i.Dl,e)}setApplicationVersion(e){return this.#e(i.nb,e)}setErrorHandler(e){return this.#e(i.bt,e)}addRelease(e,t){return this.#e(i.k6,e,t)}log(e,t){return this.#e(i.$9,e,t)}start(){return this.#e(i.d3)}finished(e){return this.#e(i.BL,e)}recordReplay(){return this.#e(i.CH)}pauseReplay(){return this.#e(i.Tb)}addToTrace(e){return this.#e(i.U2,e)}setCurrentRouteName(e){return this.#e(i.PA,e)}interaction(){return this.#e(i.dT)}wrapLogger(e,t,r){return this.#e(i.Wb,e,t,r)}measure(e,t){return this.#e(i.V1,e,t)}}},4261:(e,t,r)=>{"use strict";r.d(t,{$9:()=>d,BL:()=>c,CH:()=>p,Dl:()=>w,Fw:()=>R,PA:()=>v,Pl:()=>n,Tb:()=>f,U2:()=>a,V1:()=>A,Wb:()=>T,bt:()=>y,cD:()=>b,d3:()=>x,dT:()=>u,eY:()=>g,fF:()=>h,hG:()=>o,hw:()=>i,k6:()=>s,nb:()=>m,o5:()=>l});const n="api-",i=n+"ixn-",o="addPageAction",a="addToTrace",s="addRelease",c="finished",u="interaction",d="log",l="noticeError",f="pauseReplay",h="recordCustomEvent",p="recordReplay",g="register",m="setApplicationVersion",v="setCurrentRouteName",b="setCustomAttribute",y="setErrorHandler",R="setPageViewName",w="setUserId",x="start",T="wrapLogger",A="measure"},5205:(e,t,r)=>{"use strict";r.d(t,{j:()=>S});var n=r(384),i=r(1741);var o=r(2555),a=r(3333);const s=e=>{if(!e||"string"!=typeof e)return!1;try{document.createDocumentFragment().querySelector(e)}catch{return!1}return!0};var c=r(2614),u=r(944),d=r(8122);const l="[data-nr-mask]",f=e=>(0,d.a)(e,(()=>{const e={feature_flags:[],experimental:{marks:!1,measures:!1,resources:!1},mask_selector:"*",block_selector:"[data-nr-block]",mask_input_options:{color:!1,date:!1,"datetime-local":!1,email:!1,month:!1,number:!1,range:!1,search:!1,tel:!1,text:!1,time:!1,url:!1,week:!1,textarea:!1,select:!1,password:!0}};return{ajax:{deny_list:void 0,block_internal:!0,enabled:!0,autoStart:!0},api:{allow_registered_children:!0,duplicate_registered_data:!1},distributed_tracing:{enabled:void 0,exclude_newrelic_header:void 0,cors_use_newrelic_header:void 0,cors_use_tracecontext_headers:void 0,allowed_origins:void 0},get feature_flags(){return e.feature_flags},set feature_flags(t){e.feature_flags=t},generic_events:{enabled:!0,autoStart:!0},harvest:{interval:30},jserrors:{enabled:!0,autoStart:!0},logging:{enabled:!0,autoStart:!0},metrics:{enabled:!0,autoStart:!0},obfuscate:void 0,page_action:{enabled:!0},page_view_event:{enabled:!0,autoStart:!0},page_view_timing:{enabled:!0,autoStart:!0},performance:{get capture_marks(){return e.feature_flags.includes(a.$v.MARKS)||e.experimental.marks},set capture_marks(t){e.experimental.marks=t},get capture_measures(){return e.feature_flags.includes(a.$v.MEASURES)||e.experimental.measures},set capture_measures(t){e.experimental.measures=t},capture_detail:!0,resources:{get enabled(){return e.feature_flags.includes(a.$v.RESOURCES)||e.experimental.resources},set enabled(t){e.experimental.resources=t},asset_types:[],first_party_domains:[],ignore_newrelic:!0}},privacy:{cookies_enabled:!0},proxy:{assets:void 0,beacon:void 0},session:{expiresMs:c.wk,inactiveMs:c.BB},session_replay:{autoStart:!0,enabled:!1,preload:!1,sampling_rate:10,error_sampling_rate:100,collect_fonts:!1,inline_images:!1,fix_stylesheets:!0,mask_all_inputs:!0,get mask_text_selector(){return e.mask_selector},set mask_text_selector(t){s(t)?e.mask_selector="".concat(t,",").concat(l):""===t||null===t?e.mask_selector=l:(0,u.R)(5,t)},get block_class(){return"nr-block"},get ignore_class(){return"nr-ignore"},get mask_text_class(){return"nr-mask"},get block_selector(){return e.block_selector},set block_selector(t){s(t)?e.block_selector+=",".concat(t):""!==t&&(0,u.R)(6,t)},get mask_input_options(){return e.mask_input_options},set mask_input_options(t){t&&"object"==typeof t?e.mask_input_options={...t,password:!0}:(0,u.R)(7,t)}},session_trace:{enabled:!0,autoStart:!0},soft_navigations:{enabled:!0,autoStart:!0},spa:{enabled:!0,autoStart:!0},ssl:void 0,user_actions:{enabled:!0,elementAttributes:["id","className","tagName","type"]}}})());var h=r(6154),p=r(9324);let g=0;const m={buildEnv:p.F3,distMethod:p.Xs,version:p.xv,originTime:h.WN},v={appMetadata:{},customTransaction:void 0,denyList:void 0,disabled:!1,entityManager:void 0,harvester:void 0,isolatedBacklog:!1,isRecording:!1,loaderType:void 0,maxBytes:3e4,obfuscator:void 0,onerror:void 0,ptid:void 0,releaseIds:{},session:void 0,timeKeeper:void 0,get harvestCount(){return++g}},b=e=>{const t=(0,d.a)(e,v),r=Object.keys(m).reduce(((e,t)=>(e[t]={value:m[t],writable:!1,configurable:!0,enumerable:!0},e)),{});return Object.defineProperties(t,r)};var y=r(5701);const R=e=>{const t=e.startsWith("http");e+="/",r.p=t?e:"https://"+e};var w=r(7836),x=r(3241);const T={accountID:void 0,trustKey:void 0,agentID:void 0,licenseKey:void 0,applicationID:void 0,xpid:void 0},A=e=>(0,d.a)(e,T),E=new Set;function S(e,t={},r,a){let{init:s,info:c,loader_config:u,runtime:d={},exposed:l=!0}=t;if(!c){const e=(0,n.pV)();s=e.init,c=e.info,u=e.loader_config}e.init=f(s||{}),e.loader_config=A(u||{}),c.jsAttributes??={},h.bv&&(c.jsAttributes.isWorker=!0),e.info=(0,o.D)(c);const p=e.init,g=[c.beacon,c.errorBeacon];E.has(e.agentIdentifier)||(p.proxy.assets&&(R(p.proxy.assets),g.push(p.proxy.assets)),p.proxy.beacon&&g.push(p.proxy.beacon),function(e){const t=(0,n.pV)();Object.getOwnPropertyNames(i.W.prototype).forEach((r=>{const n=i.W.prototype[r];if("function"!=typeof n||"constructor"===n)return;let o=t[r];e[r]&&!1!==e.exposed&&"micro-agent"!==e.runtime?.loaderType&&(t[r]=(...t)=>{const n=e[r](...t);return o?o(...t):n})}))}(e),(0,n.US)("activatedFeatures",y.B),e.runSoftNavOverSpa&&=!0===p.soft_navigations.enabled&&p.feature_flags.includes("soft_nav")),d.denyList=[...p.ajax.deny_list||[],...p.ajax.block_internal?g:[]],d.ptid=e.agentIdentifier,d.loaderType=r,e.runtime=b(d),E.has(e.agentIdentifier)||(e.ee=w.ee.get(e.agentIdentifier),e.exposed=l,(0,x.W)({agentIdentifier:e.agentIdentifier,drained:!!y.B?.[e.agentIdentifier],type:"lifecycle",name:"initialize",feature:void 0,data:e.config})),E.add(e.agentIdentifier)}},8374:(e,t,r)=>{r.nc=(()=>{try{return document?.currentScript?.nonce}catch(e){}return""})()},860:(e,t,r)=>{"use strict";r.d(t,{$J:()=>d,K7:()=>c,P3:()=>u,XX:()=>i,Yy:()=>s,df:()=>o,qY:()=>n,v4:()=>a});const n="events",i="jserrors",o="browser/blobs",a="rum",s="browser/logs",c={ajax:"ajax",genericEvents:"generic_events",jserrors:i,logging:"logging",metrics:"metrics",pageAction:"page_action",pageViewEvent:"page_view_event",pageViewTiming:"page_view_timing",sessionReplay:"session_replay",sessionTrace:"session_trace",softNav:"soft_navigations",spa:"spa"},u={[c.pageViewEvent]:1,[c.pageViewTiming]:2,[c.metrics]:3,[c.jserrors]:4,[c.spa]:5,[c.ajax]:6,[c.sessionTrace]:7,[c.softNav]:8,[c.sessionReplay]:9,[c.logging]:10,[c.genericEvents]:11},d={[c.pageViewEvent]:a,[c.pageViewTiming]:n,[c.ajax]:n,[c.spa]:n,[c.softNav]:n,[c.metrics]:i,[c.jserrors]:i,[c.sessionTrace]:o,[c.sessionReplay]:o,[c.logging]:s,[c.genericEvents]:"ins"}}},n={};function i(e){var t=n[e];if(void 0!==t)return t.exports;var o=n[e]={exports:{}};return r[e](o,o.exports,i),o.exports}i.m=r,i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.f={},i.e=e=>Promise.all(Object.keys(i.f).reduce(((t,r)=>(i.f[r](e,t),t)),[])),i.u=e=>({212:"nr-spa-compressor",249:"nr-spa-recorder",478:"nr-spa"}[e]+"-1.292.0.min.js"),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="NRBA-1.292.0.PROD:",i.l=(r,n,o,a)=>{if(e[r])e[r].push(n);else{var s,c;if(void 0!==o)for(var u=document.getElementsByTagName("script"),d=0;d<u.length;d++){var l=u[d];if(l.getAttribute("src")==r||l.getAttribute("data-webpack")==t+o){s=l;break}}if(!s){c=!0;var f={478:"sha512-TAxjn+tHs+1lVWiAHWpmDycHw1oJpddeYnw1SW/Yr2DtHEz1bJu1onE5HVtc6qWq9lWDWp7nbKH7nmIM8SdPCA==",249:"sha512-VkdREA+sw8+FglrkR6hpknnljB0rJbH9D/aa75/NmxHiyyao1upUiSurfPjxeAdSzzvM/vhhuwd0c08MsfASXw==",212:"sha512-lEC00aiRYsgeVPwFsj6thWrGLkZ7pGjdXDTQK137nHEYol3SmoR91rScxss5nmfgiY2N6YLNsnV61mkSnTx3aw=="};(s=document.createElement("script")).charset="utf-8",s.timeout=120,i.nc&&s.setAttribute("nonce",i.nc),s.setAttribute("data-webpack",t+o),s.src=r,0!==s.src.indexOf(window.location.origin+"/")&&(s.crossOrigin="anonymous"),f[a]&&(s.integrity=f[a])}e[r]=[n];var h=(t,n)=>{s.onerror=s.onload=null,clearTimeout(p);var i=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),i&&i.forEach((e=>e(n))),t)return t(n)},p=setTimeout(h.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=h.bind(null,s.onerror),s.onload=h.bind(null,s.onload),c&&document.head.appendChild(s)}},i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.p="https://js-agent.newrelic.com/",(()=>{var e={38:0,788:0};i.f.j=(t,r)=>{var n=i.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else{var o=new Promise(((r,i)=>n=e[t]=[r,i]));r.push(n[2]=o);var a=i.p+i.u(t),s=new Error;i.l(a,(r=>{if(i.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var o=r&&("load"===r.type?"missing":r.type),a=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+o+": "+a+")",s.name="ChunkLoadError",s.type=o,s.request=a,n[1](s)}}),"chunk-"+t,t)}};var t=(t,r)=>{var n,o,[a,s,c]=r,u=0;if(a.some((t=>0!==e[t]))){for(n in s)i.o(s,n)&&(i.m[n]=s[n]);if(c)c(i)}for(t&&t(r);u<a.length;u++)o=a[u],i.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self["webpackChunk:NRBA-1.292.0.PROD"]=self["webpackChunk:NRBA-1.292.0.PROD"]||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),(()=>{"use strict";i(8374);var e=i(9566),t=i(1741);class r extends t.W{agentIdentifier=(0,e.LA)(16)}var n=i(860);const o=Object.values(n.K7);var a=i(5205);var s=i(9908),c=i(1863),u=i(4261),d=i(3241),l=i(944),f=i(5701),h=i(8154);function p(e,t,i,o){const a=o||i;!a||a[e]&&a[e]!==r.prototype[e]||(a[e]=function(){(0,s.p)(h.xV,["API/"+e+"/called"],void 0,n.K7.metrics,i.ee),(0,d.W)({agentIdentifier:i.agentIdentifier,drained:!!f.B?.[i.agentIdentifier],type:"data",name:"api",feature:u.Pl+e,data:{}});try{return t.apply(this,arguments)}catch(e){(0,l.R)(23,e)}})}function g(e,t,r,n,i){const o=e.info;null===r?delete o.jsAttributes[t]:o.jsAttributes[t]=r,(i||null===r)&&(0,s.p)(u.Pl+n,[(0,c.t)(),t,r],void 0,"session",e.ee)}var m=i(1687),v=i(4234),b=i(5289),y=i(6154),R=i(5270),w=i(7767),x=i(6389);class T extends v.W{constructor(e,t){super(e.agentIdentifier,t),this.abortHandler=void 0,this.featAggregate=void 0,this.onAggregateImported=void 0,this.deferred=Promise.resolve(),!1===e.init[this.featureName].autoStart?this.deferred=new Promise(((t,r)=>{this.ee.on("manual-start-all",(0,x.J)((()=>{(0,m.Ak)(e.agentIdentifier,this.featureName),t()})))})):(0,m.Ak)(e.agentIdentifier,t)}importAggregator(e,t,r={}){if(this.featAggregate)return;let o;this.onAggregateImported=new Promise((e=>{o=e}));const a=async()=>{let a;await this.deferred;try{if((0,w.V)(e.init)){const{setupAgentSession:t}=await i.e(478).then(i.bind(i,6526));a=t(e)}}catch(e){(0,l.R)(20,e),this.ee.emit("internal-error",[e]),this.featureName===n.K7.sessionReplay&&this.abortHandler?.()}try{if(!this.#t(this.featureName,a,e.init))return(0,m.Ze)(this.agentIdentifier,this.featureName),void o(!1);const{Aggregate:n}=await t();this.featAggregate=new n(e,r),e.runtime.harvester.initializedAggregates.push(this.featAggregate),o(!0)}catch(e){(0,l.R)(34,e),this.abortHandler?.(),(0,m.Ze)(this.agentIdentifier,this.featureName,!0),o(!1),this.ee&&this.ee.abort()}};y.RI?(0,b.GG)((()=>a()),!0):a()}#t(e,t,r){switch(e){case n.K7.sessionReplay:return(0,R.SR)(r)&&!!t;case n.K7.sessionTrace:return!!t;default:return!0}}}var A=i(6630),E=i(2614);class S extends T{static featureName=A.T;constructor(e){var t;super(e,A.T),this.setupInspectionEvents(e.agentIdentifier),t=e,p(u.Fw,(function(e,r){"string"==typeof e&&("/"!==e.charAt(0)&&(e="/"+e),t.runtime.customTransaction=(r||"http://custom.transaction")+e,(0,s.p)(u.Pl+u.Fw,[(0,c.t)()],void 0,void 0,t.ee))}),t),this.ee.on("api-send-rum",((e,t)=>(0,s.p)("send-rum",[e,t],void 0,this.featureName,this.ee))),this.importAggregator(e,(()=>i.e(478).then(i.bind(i,1983))))}setupInspectionEvents(e){const t=(t,r)=>{t&&(0,d.W)({agentIdentifier:e,timeStamp:t.timeStamp,loaded:"complete"===t.target.readyState,type:"window",name:r,data:t.target.location+""})};(0,b.sB)((e=>{t(e,"DOMContentLoaded")})),(0,b.GG)((e=>{t(e,"load")})),(0,b.Qr)((e=>{t(e,"navigate")})),this.ee.on(E.tS.UPDATE,((t,r)=>{(0,d.W)({agentIdentifier:e,type:"lifecycle",name:"session",data:r})}))}}var _=i(384);var N=i(2843),O=i(3878),I=i(782);class P extends T{static featureName=I.T;constructor(e){super(e,I.T),y.RI&&((0,N.u)((()=>(0,s.p)("docHidden",[(0,c.t)()],void 0,I.T,this.ee)),!0),(0,O.sp)("pagehide",(()=>(0,s.p)("winPagehide",[(0,c.t)()],void 0,I.T,this.ee))),this.importAggregator(e,(()=>i.e(478).then(i.bind(i,9917)))))}}class j extends T{static featureName=h.TZ;constructor(e){super(e,h.TZ),y.RI&&document.addEventListener("securitypolicyviolation",(e=>{(0,s.p)(h.xV,["Generic/CSPViolation/Detected"],void 0,this.featureName,this.ee)})),this.importAggregator(e,(()=>i.e(478).then(i.bind(i,8351))))}}var C=i(6774),k=i(3304);class L{constructor(e,t,r,n,i){this.name="UncaughtError",this.message="string"==typeof e?e:(0,k.A)(e),this.sourceURL=t,this.line=r,this.column=n,this.__newrelic=i}}function M(e){return K(e)?e:new L(void 0!==e?.message?e.message:e,e?.filename||e?.sourceURL,e?.lineno||e?.line,e?.colno||e?.col,e?.__newrelic)}function H(e){const t="Unhandled Promise Rejection: ";if(!e?.reason)return;if(K(e.reason)){try{e.reason.message.startsWith(t)||(e.reason.message=t+e.reason.message)}catch(e){}return M(e.reason)}const r=M(e.reason);return(r.message||"").startsWith(t)||(r.message=t+r.message),r}function D(e){if(e.error instanceof SyntaxError&&!/:\d+$/.test(e.error.stack?.trim())){const t=new L(e.message,e.filename,e.lineno,e.colno,e.error.__newrelic);return t.name=SyntaxError.name,t}return K(e.error)?e.error:M(e)}function K(e){return e instanceof Error&&!!e.stack}function U(e,t,r,i,o=(0,c.t)()){"string"==typeof e&&(e=new Error(e)),(0,s.p)("err",[e,o,!1,t,r.runtime.isRecording,void 0,i],void 0,n.K7.jserrors,r.ee)}var F=i(3496),W=i(993),B=i(3785);function G(e,{customAttributes:t={},level:r=W.p_.INFO}={},n,i,o=(0,c.t)()){(0,B.R)(n.ee,e,t,r,i,o)}function V(e,t,r,i,o=(0,c.t)()){(0,s.p)(u.Pl+u.hG,[o,e,t,i],void 0,n.K7.genericEvents,r.ee)}function z(e){p(u.eY,(function(t){return function(e,t){const r={};let i,o;(0,l.R)(54,"newrelic.register"),e.init.api.allow_registered_children||(i=()=>(0,l.R)(55));t&&(0,F.I)(t)||(i=()=>(0,l.R)(48,t));const a={addPageAction:(n,i={})=>{u(V,[n,{...r,...i},e],t)},log:(n,i={})=>{u(G,[n,{...i,customAttributes:{...r,...i.customAttributes||{}}},e],t)},noticeError:(n,i={})=>{u(U,[n,{...r,...i},e],t)},setApplicationVersion:e=>{r["application.version"]=e},setCustomAttribute:(e,t)=>{r[e]=t},setUserId:e=>{r["enduser.id"]=e},metadata:{customAttributes:r,target:t,get connected(){return o||Promise.reject(new Error("Failed to connect"))}}};i?i():o=new Promise(((n,i)=>{try{const o=e.runtime?.entityManager;let s=!!o?.get().entityGuid,c=o?.getEntityGuidFor(t.licenseKey,t.applicationID),u=!!c;if(s&&u)t.entityGuid=c,n(a);else{const d=setTimeout((()=>i(new Error("Failed to connect - Timeout"))),15e3);function l(r){(0,F.A)(r,e)?s||=!0:t.licenseKey===r.licenseKey&&t.applicationID===r.applicationID&&(u=!0,t.entityGuid=r.entityGuid),s&&u&&(clearTimeout(d),e.ee.removeEventListener("entity-added",l),n(a))}e.ee.emit("api-send-rum",[r,t]),e.ee.on("entity-added",l)}}catch(f){i(f)}}));const u=async(t,r,a)=>{if(i)return i();const u=(0,c.t)();(0,s.p)(h.xV,["API/register/".concat(t.name,"/called")],void 0,n.K7.metrics,e.ee);try{await o;const n=e.init.api.duplicate_registered_data;(!0===n||Array.isArray(n)&&n.includes(a.entityGuid))&&t(...r,void 0,u),t(...r,a.entityGuid,u)}catch(e){(0,l.R)(50,e)}};return a}(e,t)}),e)}class Z extends T{static featureName=C.T;constructor(e){var t;super(e,C.T),t=e,p(u.o5,((e,r)=>U(e,r,t)),t),function(e){p(u.bt,(function(t){e.runtime.onerror=t}),e)}(e),function(e){let t=0;p(u.k6,(function(e,r){++t>10||(this.runtime.releaseIds[e.slice(-200)]=(""+r).slice(-200))}),e)}(e),z(e);try{this.removeOnAbort=new AbortController}catch(e){}this.ee.on("internal-error",((t,r)=>{this.abortHandler&&(0,s.p)("ierr",[M(t),(0,c.t)(),!0,{},e.runtime.isRecording,r],void 0,this.featureName,this.ee)})),y.gm.addEventListener("unhandledrejection",(t=>{this.abortHandler&&(0,s.p)("err",[H(t),(0,c.t)(),!1,{unhandledPromiseRejection:1},e.runtime.isRecording],void 0,this.featureName,this.ee)}),(0,O.jT)(!1,this.removeOnAbort?.signal)),y.gm.addEventListener("error",(t=>{this.abortHandler&&(0,s.p)("err",[D(t),(0,c.t)(),!1,{},e.runtime.isRecording],void 0,this.featureName,this.ee)}),(0,O.jT)(!1,this.removeOnAbort?.signal)),this.abortHandler=this.#r,this.importAggregator(e,(()=>i.e(478).then(i.bind(i,5928))))}#r(){this.removeOnAbort?.abort(),this.abortHandler=void 0}}var q=i(8990);let X=1;function Y(e){const t=typeof e;return!e||"object"!==t&&"function"!==t?-1:e===y.gm?0:(0,q.I)(e,"nr@id",(function(){return X++}))}function J(e){if("string"==typeof e&&e.length)return e.length;if("object"==typeof e){if("undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer&&e.byteLength)return e.byteLength;if("undefined"!=typeof Blob&&e instanceof Blob&&e.size)return e.size;if(!("undefined"!=typeof FormData&&e instanceof FormData))try{return(0,k.A)(e).length}catch(e){return}}}var Q=i(8139),ee=i(7836),te=i(3434);const re={},ne=["open","send"];function ie(e){var t=e||ee.ee;const r=function(e){return(e||ee.ee).get("xhr")}(t);if(void 0===y.gm.XMLHttpRequest)return r;if(re[r.debugId]++)return r;re[r.debugId]=1,(0,Q.u)(t);var n=(0,te.YM)(r),i=y.gm.XMLHttpRequest,o=y.gm.MutationObserver,a=y.gm.Promise,s=y.gm.setInterval,c="readystatechange",u=["onload","onerror","onabort","onloadstart","onloadend","onprogress","ontimeout"],d=[],f=y.gm.XMLHttpRequest=function(e){const t=new i(e),o=r.context(t);try{r.emit("new-xhr",[t],o),t.addEventListener(c,(a=o,function(){var e=this;e.readyState>3&&!a.resolved&&(a.resolved=!0,r.emit("xhr-resolved",[],e)),n.inPlace(e,u,"fn-",b)}),(0,O.jT)(!1))}catch(e){(0,l.R)(15,e);try{r.emit("internal-error",[e])}catch(e){}}var a;return t};function h(e,t){n.inPlace(t,["onreadystatechange"],"fn-",b)}if(function(e,t){for(var r in e)t[r]=e[r]}(i,f),f.prototype=i.prototype,n.inPlace(f.prototype,ne,"-xhr-",b),r.on("send-xhr-start",(function(e,t){h(e,t),function(e){d.push(e),o&&(p?p.then(v):s?s(v):(g=-g,m.data=g))}(t)})),r.on("open-xhr-start",h),o){var p=a&&a.resolve();if(!s&&!a){var g=1,m=document.createTextNode(g);new o(v).observe(m,{characterData:!0})}}else t.on("fn-end",(function(e){e[0]&&e[0].type===c||v()}));function v(){for(var e=0;e<d.length;e++)h(0,d[e]);d.length&&(d=[])}function b(e,t){return t}return r}var oe="fetch-",ae=oe+"body-",se=["arrayBuffer","blob","json","text","formData"],ce=y.gm.Request,ue=y.gm.Response,de="prototype";const le={};function fe(e){const t=function(e){return(e||ee.ee).get("fetch")}(e);if(!(ce&&ue&&y.gm.fetch))return t;if(le[t.debugId]++)return t;function r(e,r,n){var i=e[r];"function"==typeof i&&(e[r]=function(){var e,r=[...arguments],o={};t.emit(n+"before-start",[r],o),o[ee.P]&&o[ee.P].dt&&(e=o[ee.P].dt);var a=i.apply(this,r);return t.emit(n+"start",[r,e],a),a.then((function(e){return t.emit(n+"end",[null,e],a),e}),(function(e){throw t.emit(n+"end",[e],a),e}))})}return le[t.debugId]=1,se.forEach((e=>{r(ce[de],e,ae),r(ue[de],e,ae)})),r(y.gm,"fetch",oe),t.on(oe+"end",(function(e,r){var n=this;if(r){var i=r.headers.get("content-length");null!==i&&(n.rxSize=i),t.emit(oe+"done",[null,r],n)}else t.emit(oe+"done",[e],n)})),t}var he=i(7485);class pe{constructor(e){this.agentRef=e}generateTracePayload(t){const r=this.agentRef.loader_config;if(!this.shouldGenerateTrace(t)||!r)return null;var n=(r.accountID||"").toString()||null,i=(r.agentID||"").toString()||null,o=(r.trustKey||"").toString()||null;if(!n||!i)return null;var a=(0,e.ZF)(),s=(0,e.el)(),c=Date.now(),u={spanId:a,traceId:s,timestamp:c};return(t.sameOrigin||this.isAllowedOrigin(t)&&this.useTraceContextHeadersForCors())&&(u.traceContextParentHeader=this.generateTraceContextParentHeader(a,s),u.traceContextStateHeader=this.generateTraceContextStateHeader(a,c,n,i,o)),(t.sameOrigin&&!this.excludeNewrelicHeader()||!t.sameOrigin&&this.isAllowedOrigin(t)&&this.useNewrelicHeaderForCors())&&(u.newrelicHeader=this.generateTraceHeader(a,s,c,n,i,o)),u}generateTraceContextParentHeader(e,t){return"00-"+t+"-"+e+"-01"}generateTraceContextStateHeader(e,t,r,n,i){return i+"@nr=0-1-"+r+"-"+n+"-"+e+"----"+t}generateTraceHeader(e,t,r,n,i,o){if(!("function"==typeof y.gm?.btoa))return null;var a={v:[0,1],d:{ty:"Browser",ac:n,ap:i,id:e,tr:t,ti:r}};return o&&n!==o&&(a.d.tk=o),btoa((0,k.A)(a))}shouldGenerateTrace(e){return this.agentRef.init?.distributed_tracing&&this.isAllowedOrigin(e)}isAllowedOrigin(e){var t=!1;const r=this.agentRef.init?.distributed_tracing;if(e.sameOrigin)t=!0;else if(r?.allowed_origins instanceof Array)for(var n=0;n<r.allowed_origins.length;n++){var i=(0,he.D)(r.allowed_origins[n]);if(e.hostname===i.hostname&&e.protocol===i.protocol&&e.port===i.port){t=!0;break}}return t}excludeNewrelicHeader(){var e=this.agentRef.init?.distributed_tracing;return!!e&&!!e.exclude_newrelic_header}useNewrelicHeaderForCors(){var e=this.agentRef.init?.distributed_tracing;return!!e&&!1!==e.cors_use_newrelic_header}useTraceContextHeadersForCors(){var e=this.agentRef.init?.distributed_tracing;return!!e&&!!e.cors_use_tracecontext_headers}}var ge=i(9300),me=i(7295),ve=["load","error","abort","timeout"],be=ve.length,ye=(0,_.dV)().o.REQ,Re=(0,_.dV)().o.XHR;const we="X-NewRelic-App-Data";class xe extends T{static featureName=ge.T;constructor(e){super(e,ge.T),this.dt=new pe(e),this.handler=(e,t,r,n)=>(0,s.p)(e,t,r,n,this.ee);try{const e={xmlhttprequest:"xhr",fetch:"fetch",beacon:"beacon"};y.gm?.performance?.getEntriesByType("resource").forEach((t=>{if(t.initiatorType in e&&0!==t.responseStatus){const r={status:t.responseStatus},i={rxSize:t.transferSize,duration:Math.floor(t.duration),cbTime:0};Te(r,t.name),this.handler("xhr",[r,i,t.startTime,t.responseEnd,e[t.initiatorType]],void 0,n.K7.ajax)}}))}catch(e){}fe(this.ee),ie(this.ee),function(e,t,r,i){function o(e){var t=this;t.totalCbs=0,t.called=0,t.cbTime=0,t.end=A,t.ended=!1,t.xhrGuids={},t.lastSize=null,t.loadCaptureCalled=!1,t.params=this.params||{},t.metrics=this.metrics||{},e.addEventListener("load",(function(r){E(t,e)}),(0,O.jT)(!1)),y.lR||e.addEventListener("progress",(function(e){t.lastSize=e.loaded}),(0,O.jT)(!1))}function a(e){this.params={method:e[0]},Te(this,e[1]),this.metrics={}}function u(t,r){e.loader_config.xpid&&this.sameOrigin&&r.setRequestHeader("X-NewRelic-ID",e.loader_config.xpid);var n=i.generateTracePayload(this.parsedOrigin);if(n){var o=!1;n.newrelicHeader&&(r.setRequestHeader("newrelic",n.newrelicHeader),o=!0),n.traceContextParentHeader&&(r.setRequestHeader("traceparent",n.traceContextParentHeader),n.traceContextStateHeader&&r.setRequestHeader("tracestate",n.traceContextStateHeader),o=!0),o&&(this.dt=n)}}function d(e,r){var n=this.metrics,i=e[0],o=this;if(n&&i){var a=J(i);a&&(n.txSize=a)}this.startTime=(0,c.t)(),this.body=i,this.listener=function(e){try{"abort"!==e.type||o.loadCaptureCalled||(o.params.aborted=!0),("load"!==e.type||o.called===o.totalCbs&&(o.onloadCalled||"function"!=typeof r.onload)&&"function"==typeof o.end)&&o.end(r)}catch(e){try{t.emit("internal-error",[e])}catch(e){}}};for(var s=0;s<be;s++)r.addEventListener(ve[s],this.listener,(0,O.jT)(!1))}function l(e,t,r){this.cbTime+=e,t?this.onloadCalled=!0:this.called+=1,this.called!==this.totalCbs||!this.onloadCalled&&"function"==typeof r.onload||"function"!=typeof this.end||this.end(r)}function f(e,t){var r=""+Y(e)+!!t;this.xhrGuids&&!this.xhrGuids[r]&&(this.xhrGuids[r]=!0,this.totalCbs+=1)}function p(e,t){var r=""+Y(e)+!!t;this.xhrGuids&&this.xhrGuids[r]&&(delete this.xhrGuids[r],this.totalCbs-=1)}function g(){this.endTime=(0,c.t)()}function m(e,r){r instanceof Re&&"load"===e[0]&&t.emit("xhr-load-added",[e[1],e[2]],r)}function v(e,r){r instanceof Re&&"load"===e[0]&&t.emit("xhr-load-removed",[e[1],e[2]],r)}function b(e,t,r){t instanceof Re&&("onload"===r&&(this.onload=!0),("load"===(e[0]&&e[0].type)||this.onload)&&(this.xhrCbStart=(0,c.t)()))}function R(e,r){this.xhrCbStart&&t.emit("xhr-cb-time",[(0,c.t)()-this.xhrCbStart,this.onload,r],r)}function w(e){var t,r=e[1]||{};if("string"==typeof e[0]?0===(t=e[0]).length&&y.RI&&(t=""+y.gm.location.href):e[0]&&e[0].url?t=e[0].url:y.gm?.URL&&e[0]&&e[0]instanceof URL?t=e[0].href:"function"==typeof e[0].toString&&(t=e[0].toString()),"string"==typeof t&&0!==t.length){t&&(this.parsedOrigin=(0,he.D)(t),this.sameOrigin=this.parsedOrigin.sameOrigin);var n=i.generateTracePayload(this.parsedOrigin);if(n&&(n.newrelicHeader||n.traceContextParentHeader))if(e[0]&&e[0].headers)s(e[0].headers,n)&&(this.dt=n);else{var o={};for(var a in r)o[a]=r[a];o.headers=new Headers(r.headers||{}),s(o.headers,n)&&(this.dt=n),e.length>1?e[1]=o:e.push(o)}}function s(e,t){var r=!1;return t.newrelicHeader&&(e.set("newrelic",t.newrelicHeader),r=!0),t.traceContextParentHeader&&(e.set("traceparent",t.traceContextParentHeader),t.traceContextStateHeader&&e.set("tracestate",t.traceContextStateHeader),r=!0),r}}function x(e,t){this.params={},this.metrics={},this.startTime=(0,c.t)(),this.dt=t,e.length>=1&&(this.target=e[0]),e.length>=2&&(this.opts=e[1]);var r,n=this.opts||{},i=this.target;"string"==typeof i?r=i:"object"==typeof i&&i instanceof ye?r=i.url:y.gm?.URL&&"object"==typeof i&&i instanceof URL&&(r=i.href),Te(this,r);var o=(""+(i&&i instanceof ye&&i.method||n.method||"GET")).toUpperCase();this.params.method=o,this.body=n.body,this.txSize=J(n.body)||0}function T(e,t){if(this.endTime=(0,c.t)(),this.params||(this.params={}),(0,me.iW)(this.params))return;let i;this.params.status=t?t.status:0,"string"==typeof this.rxSize&&this.rxSize.length>0&&(i=+this.rxSize);const o={txSize:this.txSize,rxSize:i,duration:(0,c.t)()-this.startTime};r("xhr",[this.params,o,this.startTime,this.endTime,"fetch"],this,n.K7.ajax)}function A(e){const t=this.params,i=this.metrics;if(!this.ended){this.ended=!0;for(let t=0;t<be;t++)e.removeEventListener(ve[t],this.listener,!1);t.aborted||(0,me.iW)(t)||(i.duration=(0,c.t)()-this.startTime,this.loadCaptureCalled||4!==e.readyState?null==t.status&&(t.status=0):E(this,e),i.cbTime=this.cbTime,r("xhr",[t,i,this.startTime,this.endTime,"xhr"],this,n.K7.ajax))}}function E(e,r){e.params.status=r.status;var i=function(e,t){var r=e.responseType;return"json"===r&&null!==t?t:"arraybuffer"===r||"blob"===r||"json"===r?J(e.response):"text"===r||""===r||void 0===r?J(e.responseText):void 0}(r,e.lastSize);if(i&&(e.metrics.rxSize=i),e.sameOrigin&&r.getAllResponseHeaders().indexOf(we)>=0){var o=r.getResponseHeader(we);o&&((0,s.p)(h.rs,["Ajax/CrossApplicationTracing/Header/Seen"],void 0,n.K7.metrics,t),e.params.cat=o.split(", ").pop())}e.loadCaptureCalled=!0}t.on("new-xhr",o),t.on("open-xhr-start",a),t.on("open-xhr-end",u),t.on("send-xhr-start",d),t.on("xhr-cb-time",l),t.on("xhr-load-added",f),t.on("xhr-load-removed",p),t.on("xhr-resolved",g),t.on("addEventListener-end",m),t.on("removeEventListener-end",v),t.on("fn-end",R),t.on("fetch-before-start",w),t.on("fetch-start",x),t.on("fn-start",b),t.on("fetch-done",T)}(e,this.ee,this.handler,this.dt),this.importAggregator(e,(()=>i.e(478).then(i.bind(i,3845))))}}function Te(e,t){var r=(0,he.D)(t),n=e.params||e;n.hostname=r.hostname,n.port=r.port,n.protocol=r.protocol,n.host=r.hostname+":"+r.port,n.pathname=r.pathname,e.parsedOrigin=r,e.sameOrigin=r.sameOrigin}const Ae={},Ee=["pushState","replaceState"];function Se(e){const t=function(e){return(e||ee.ee).get("history")}(e);return!y.RI||Ae[t.debugId]++||(Ae[t.debugId]=1,(0,te.YM)(t).inPlace(window.history,Ee,"-")),t}var _e=i(3738);function Ne(e){p(u.BL,(function(t=Date.now()){const r=t-y.WN;r<0&&(0,l.R)(62,t),(0,s.p)(h.XG,[u.BL,{time:r}],void 0,n.K7.metrics,e.ee),e.addToTrace({name:u.BL,start:t,origin:"nr"}),(0,s.p)(u.Pl+u.hG,[r,u.BL],void 0,n.K7.genericEvents,e.ee)}),e)}const{He:Oe,bD:Ie,d3:Pe,Kp:je,TZ:Ce,Lc:ke,uP:Le,Rz:Me}=_e;class He extends T{static featureName=Ce;constructor(e){var t;super(e,Ce),t=e,p(u.U2,(function(e){if(!(e&&"object"==typeof e&&e.name&&e.start))return;const r={n:e.name,s:e.start-y.WN,e:(e.end||e.start)-y.WN,o:e.origin||"",t:"api"};r.s<0||r.e<0||r.e<r.s?(0,l.R)(61,{start:r.s,end:r.e}):(0,s.p)("bstApi",[r],void 0,n.K7.sessionTrace,t.ee)}),t),Ne(e);if(!(0,w.V)(e.init))return void this.deregisterDrain();const r=this.ee;let o;Se(r),this.eventsEE=(0,Q.u)(r),this.eventsEE.on(Le,(function(e,t){this.bstStart=(0,c.t)()})),this.eventsEE.on(ke,(function(e,t){(0,s.p)("bst",[e[0],t,this.bstStart,(0,c.t)()],void 0,n.K7.sessionTrace,r)})),r.on(Me+Pe,(function(e){this.time=(0,c.t)(),this.startPath=location.pathname+location.hash})),r.on(Me+je,(function(e){(0,s.p)("bstHist",[location.pathname+location.hash,this.startPath,this.time],void 0,n.K7.sessionTrace,r)}));try{o=new PerformanceObserver((e=>{const t=e.getEntries();(0,s.p)(Oe,[t],void 0,n.K7.sessionTrace,r)})),o.observe({type:Ie,buffered:!0})}catch(e){}this.importAggregator(e,(()=>i.e(478).then(i.bind(i,575))),{resourceObserver:o})}}var De=i(6344);class Ke extends T{static featureName=De.TZ;#n;#i;constructor(e){var t;let r;super(e,De.TZ),t=e,p(u.CH,(function(){(0,s.p)(u.CH,[],void 0,n.K7.sessionReplay,t.ee)}),t),function(e){p(u.Tb,(function(){(0,s.p)(u.Tb,[],void 0,n.K7.sessionReplay,e.ee)}),e)}(e),this.#i=e;try{r=JSON.parse(localStorage.getItem("".concat(E.H3,"_").concat(E.uh)))}catch(e){}(0,R.SR)(e.init)&&this.ee.on(De.G4.RECORD,(()=>this.#o())),this.#a(r)?(this.#n=r?.sessionReplayMode,this.#s()):this.importAggregator(this.#i,(()=>i.e(478).then(i.bind(i,6167)))),this.ee.on("err",(e=>{this.#i.runtime.isRecording&&(this.errorNoticed=!0,(0,s.p)(De.G4.ERROR_DURING_REPLAY,[e],void 0,this.featureName,this.ee))}))}#a(e){return e&&(e.sessionReplayMode===E.g.FULL||e.sessionReplayMode===E.g.ERROR)||(0,R.Aw)(this.#i.init)}#c=!1;async#s(e){if(!this.#c){this.#c=!0;try{const{Recorder:t}=await Promise.all([i.e(478),i.e(249)]).then(i.bind(i,8589));this.recorder??=new t({mode:this.#n,agentIdentifier:this.agentIdentifier,trigger:e,ee:this.ee,agentRef:this.#i}),this.recorder.startRecording(),this.abortHandler=this.recorder.stopRecording}catch(e){this.parent.ee.emit("internal-error",[e])}this.importAggregator(this.#i,(()=>i.e(478).then(i.bind(i,6167))),{recorder:this.recorder,errorNoticed:this.errorNoticed})}}#o(){this.featAggregate?this.featAggregate.mode!==E.g.FULL&&this.featAggregate.initializeRecording(E.g.FULL,!0):(this.#n=E.g.FULL,this.#s(De.Qb.API),this.recorder&&this.recorder.parent.mode!==E.g.FULL&&(this.recorder.parent.mode=E.g.FULL,this.recorder.stopRecording(),this.recorder.startRecording(),this.abortHandler=this.recorder.stopRecording))}}var Ue=i(3962);function Fe(e){const t=e.ee.get("tracer");function r(){}p(u.dT,(function(e){return(new r).get("object"==typeof e?e:{})}),e);const i=r.prototype={createTracer:function(r,i){var o={},a=this,d="function"==typeof i;return(0,s.p)(h.xV,["API/createTracer/called"],void 0,n.K7.metrics,e.ee),e.runSoftNavOverSpa||(0,s.p)(u.hw+"tracer",[(0,c.t)(),r,o],a,n.K7.spa,e.ee),function(){if(t.emit((d?"":"no-")+"fn-start",[(0,c.t)(),a,d],o),d)try{return i.apply(this,arguments)}catch(e){const r="string"==typeof e?new Error(e):e;throw t.emit("fn-err",[arguments,this,r],o),r}finally{t.emit("fn-end",[(0,c.t)()],o)}}}};["actionText","setName","setAttribute","save","ignore","onEnd","getContext","end","get"].forEach((t=>{p.apply(this,[t,function(){return(0,s.p)(u.hw+t,[(0,c.t)(),...arguments],this,e.runSoftNavOverSpa?n.K7.softNav:n.K7.spa,e.ee),this},e,i])})),p(u.PA,(function(){e.runSoftNavOverSpa?(0,s.p)(u.hw+"routeName",[performance.now(),...arguments],void 0,n.K7.softNav,e.ee):(0,s.p)(u.Pl+"routeName",[(0,c.t)(),...arguments],this,n.K7.spa,e.ee)}),e)}class We extends T{static featureName=Ue.TZ;constructor(e){if(super(e,Ue.TZ),Fe(e),!y.RI||!(0,_.dV)().o.MO)return;const t=Se(this.ee);Ue.tC.forEach((e=>{(0,O.sp)(e,(e=>{a(e)}),!0)}));const r=()=>(0,s.p)("newURL",[(0,c.t)(),""+window.location],void 0,this.featureName,this.ee);t.on("pushState-end",r),t.on("replaceState-end",r);try{this.removeOnAbort=new AbortController}catch(e){}(0,O.sp)("popstate",(e=>(0,s.p)("newURL",[e.timeStamp,""+window.location],void 0,this.featureName,this.ee)),!0,this.removeOnAbort?.signal);let n=!1;const o=new((0,_.dV)().o.MO)(((e,t)=>{n||(n=!0,requestAnimationFrame((()=>{(0,s.p)("newDom",[(0,c.t)()],void 0,this.featureName,this.ee),n=!1})))})),a=(0,x.s)((e=>{(0,s.p)("newUIEvent",[e],void 0,this.featureName,this.ee),o.observe(document.body,{attributes:!0,childList:!0,subtree:!0,characterData:!0})}),100,{leading:!0});this.abortHandler=function(){this.removeOnAbort?.abort(),o.disconnect(),this.abortHandler=void 0},this.importAggregator(e,(()=>i.e(478).then(i.bind(i,4393))),{domObserver:o})}}var Be=i(7378);const Ge={},Ve=["appendChild","insertBefore","replaceChild"];function ze(e){const t=function(e){return(e||ee.ee).get("jsonp")}(e);if(!y.RI||Ge[t.debugId])return t;Ge[t.debugId]=!0;var r=(0,te.YM)(t),n=/[?&](?:callback|cb)=([^&#]+)/,i=/(.*)\.([^.]+)/,o=/^(\w+)(\.|$)(.*)$/;function a(e,t){if(!e)return t;const r=e.match(o),n=r[1];return a(r[3],t[n])}return r.inPlace(Node.prototype,Ve,"dom-"),t.on("dom-start",(function(e){!function(e){if(!e||"string"!=typeof e.nodeName||"script"!==e.nodeName.toLowerCase())return;if("function"!=typeof e.addEventListener)return;var o=(s=e.src,c=s.match(n),c?c[1]:null);var s,c;if(!o)return;var u=function(e){var t=e.match(i);if(t&&t.length>=3)return{key:t[2],parent:a(t[1],window)};return{key:e,parent:window}}(o);if("function"!=typeof u.parent[u.key])return;var d={};function l(){t.emit("jsonp-end",[],d),e.removeEventListener("load",l,(0,O.jT)(!1)),e.removeEventListener("error",f,(0,O.jT)(!1))}function f(){t.emit("jsonp-error",[],d),t.emit("jsonp-end",[],d),e.removeEventListener("load",l,(0,O.jT)(!1)),e.removeEventListener("error",f,(0,O.jT)(!1))}r.inPlace(u.parent,[u.key],"cb-",d),e.addEventListener("load",l,(0,O.jT)(!1)),e.addEventListener("error",f,(0,O.jT)(!1)),t.emit("new-jsonp",[e.src],d)}(e[0])})),t}const Ze={};function qe(e){const t=function(e){return(e||ee.ee).get("promise")}(e);if(Ze[t.debugId])return t;Ze[t.debugId]=!0;var r=t.context,n=(0,te.YM)(t),i=y.gm.Promise;return i&&function(){function e(r){var o=t.context(),a=n(r,"executor-",o,null,!1);const s=Reflect.construct(i,[a],e);return t.context(s).getCtx=function(){return o},s}y.gm.Promise=e,Object.defineProperty(e,"name",{value:"Promise"}),e.toString=function(){return i.toString()},Object.setPrototypeOf(e,i),["all","race"].forEach((function(r){const n=i[r];e[r]=function(e){let i=!1;[...e||[]].forEach((e=>{this.resolve(e).then(a("all"===r),a(!1))}));const o=n.apply(this,arguments);return o;function a(e){return function(){t.emit("propagate",[null,!i],o,!1,!1),i=i||!e}}}})),["resolve","reject"].forEach((function(r){const n=i[r];e[r]=function(e){const r=n.apply(this,arguments);return e!==r&&t.emit("propagate",[e,!0],r,!1,!1),r}})),e.prototype=i.prototype;const o=i.prototype.then;i.prototype.then=function(...e){var i=this,a=r(i);a.promise=i,e[0]=n(e[0],"cb-",a,null,!1),e[1]=n(e[1],"cb-",a,null,!1);const s=o.apply(this,e);return a.nextPromise=s,t.emit("propagate",[i,!0],s,!1,!1),s},i.prototype.then[te.Jt]=o,t.on("executor-start",(function(e){e[0]=n(e[0],"resolve-",this,null,!1),e[1]=n(e[1],"resolve-",this,null,!1)})),t.on("executor-err",(function(e,t,r){e[1](r)})),t.on("cb-end",(function(e,r,n){t.emit("propagate",[n,!0],this.nextPromise,!1,!1)})),t.on("propagate",(function(e,r,n){this.getCtx&&!r||(this.getCtx=function(){if(e instanceof Promise)var r=t.context(e);return r&&r.getCtx?r.getCtx():this})}))}(),t}const Xe={},Ye="setTimeout",$e="setInterval",Je="clearTimeout",Qe="-start",et=[Ye,"setImmediate",$e,Je,"clearImmediate"];function tt(e){const t=function(e){return(e||ee.ee).get("timer")}(e);if(Xe[t.debugId]++)return t;Xe[t.debugId]=1;var r=(0,te.YM)(t);return r.inPlace(y.gm,et.slice(0,2),Ye+"-"),r.inPlace(y.gm,et.slice(2,3),$e+"-"),r.inPlace(y.gm,et.slice(3),Je+"-"),t.on($e+Qe,(function(e,t,n){e[0]=r(e[0],"fn-",null,n)})),t.on(Ye+Qe,(function(e,t,n){this.method=n,this.timerDuration=isNaN(e[1])?0:+e[1],e[0]=r(e[0],"fn-",this,n)})),t}const rt={};function nt(e){const t=function(e){return(e||ee.ee).get("mutation")}(e);if(!y.RI||rt[t.debugId])return t;rt[t.debugId]=!0;var r=(0,te.YM)(t),n=y.gm.MutationObserver;return n&&(window.MutationObserver=function(e){return this instanceof n?new n(r(e,"fn-")):n.apply(this,arguments)},MutationObserver.prototype=n.prototype),t}const{TZ:it,d3:ot,Kp:at,$p:st,wW:ct,e5:ut,tH:dt,uP:lt,rw:ft,Lc:ht}=Be;class pt extends T{static featureName=it;constructor(e){if(super(e,it),Fe(e),!y.RI)return;try{this.removeOnAbort=new AbortController}catch(e){}let t,r=0;const n=this.ee.get("tracer"),o=ze(this.ee),a=qe(this.ee),u=tt(this.ee),d=ie(this.ee),l=this.ee.get("events"),f=fe(this.ee),h=Se(this.ee),p=nt(this.ee);function g(e,t){h.emit("newURL",[""+window.location,t])}function m(){r++,t=window.location.hash,this[lt]=(0,c.t)()}function v(){r--,window.location.hash!==t&&g(0,!0);var e=(0,c.t)();this[ut]=~~this[ut]+e-this[lt],this[ht]=e}function b(e,t){e.on(t,(function(){this[t]=(0,c.t)()}))}this.ee.on(lt,m),a.on(ft,m),o.on(ft,m),this.ee.on(ht,v),a.on(ct,v),o.on(ct,v),this.ee.on("fn-err",((...t)=>{t[2]?.__newrelic?.[e.agentIdentifier]||(0,s.p)("function-err",[...t],void 0,this.featureName,this.ee)})),this.ee.buffer([lt,ht,"xhr-resolved"],this.featureName),l.buffer([lt],this.featureName),u.buffer(["setTimeout"+at,"clearTimeout"+ot,lt],this.featureName),d.buffer([lt,"new-xhr","send-xhr"+ot],this.featureName),f.buffer([dt+ot,dt+"-done",dt+st+ot,dt+st+at],this.featureName),h.buffer(["newURL"],this.featureName),p.buffer([lt],this.featureName),a.buffer(["propagate",ft,ct,"executor-err","resolve"+ot],this.featureName),n.buffer([lt,"no-"+lt],this.featureName),o.buffer(["new-jsonp","cb-start","jsonp-error","jsonp-end"],this.featureName),b(f,dt+ot),b(f,dt+"-done"),b(o,"new-jsonp"),b(o,"jsonp-end"),b(o,"cb-start"),h.on("pushState-end",g),h.on("replaceState-end",g),window.addEventListener("hashchange",g,(0,O.jT)(!0,this.removeOnAbort?.signal)),window.addEventListener("load",g,(0,O.jT)(!0,this.removeOnAbort?.signal)),window.addEventListener("popstate",(function(){g(0,r>1)}),(0,O.jT)(!0,this.removeOnAbort?.signal)),this.abortHandler=this.#r,this.importAggregator(e,(()=>i.e(478).then(i.bind(i,5592))))}#r(){this.removeOnAbort?.abort(),this.abortHandler=void 0}}var gt=i(3333);class mt extends T{static featureName=gt.TZ;constructor(e){super(e,gt.TZ);const t=[e.init.page_action.enabled,e.init.performance.capture_marks,e.init.performance.capture_measures,e.init.user_actions.enabled,e.init.performance.resources.enabled];var r;if(r=e,p(u.hG,((e,t)=>V(e,t,r)),r),function(e){p(u.fF,(function(){(0,s.p)(u.Pl+u.fF,[(0,c.t)(),...arguments],void 0,n.K7.genericEvents,e.ee)}),e)}(e),Ne(e),z(e),function(e){p(u.V1,(function(t,r){const i=(0,c.t)(),{start:o,end:a,customAttributes:d}=r||{},f={customAttributes:d||{}};if("object"!=typeof f.customAttributes||"string"!=typeof t||0===t.length)return void(0,l.R)(57);const h=(e,t)=>null==e?t:"number"==typeof e?e:e instanceof PerformanceMark?e.startTime:Number.NaN;if(f.start=h(o,0),f.end=h(a,i),Number.isNaN(f.start)||Number.isNaN(f.end))(0,l.R)(57);else{if(f.duration=f.end-f.start,!(f.duration<0))return(0,s.p)(u.Pl+u.V1,[f,t],void 0,n.K7.genericEvents,e.ee),f;(0,l.R)(58)}}),e)}(e),y.RI&&(e.init.user_actions.enabled&&(gt.Zp.forEach((e=>(0,O.sp)(e,(e=>(0,s.p)("ua",[e],void 0,this.featureName,this.ee)),!0))),gt.qN.forEach((e=>{const t=(0,x.s)((e=>{(0,s.p)("ua",[e],void 0,this.featureName,this.ee)}),500,{leading:!0});(0,O.sp)(e,t)}))),e.init.performance.resources.enabled&&y.gm.PerformanceObserver?.supportedEntryTypes.includes("resource"))){new PerformanceObserver((e=>{e.getEntries().forEach((e=>{(0,s.p)("browserPerformance.resource",[e],void 0,this.featureName,this.ee)}))})).observe({type:"resource",buffered:!0})}t.some((e=>e))?this.importAggregator(e,(()=>i.e(478).then(i.bind(i,8019)))):this.deregisterDrain()}}var vt=i(2646);const bt=new Map;function yt(e,t,r,n){if("object"!=typeof t||!t||"string"!=typeof r||!r||"function"!=typeof t[r])return(0,l.R)(29);const i=function(e){return(e||ee.ee).get("logger")}(e),o=(0,te.YM)(i),a=new vt.y(ee.P);a.level=n.level,a.customAttributes=n.customAttributes;const s=t[r]?.[te.Jt]||t[r];return bt.set(s,a),o.inPlace(t,[r],"wrap-logger-",(()=>bt.get(s))),i}class Rt extends T{static featureName=W.TZ;constructor(e){var t;super(e,W.TZ),t=e,p(u.$9,((e,r)=>G(e,r,t)),t),function(e){p(u.Wb,((t,r,{customAttributes:n={},level:i=W.p_.INFO}={})=>{yt(e.ee,t,r,{customAttributes:n,level:i})}),e)}(e),z(e);const r=this.ee;yt(r,y.gm.console,"log",{level:"info"}),yt(r,y.gm.console,"error",{level:"error"}),yt(r,y.gm.console,"warn",{level:"warn"}),yt(r,y.gm.console,"info",{level:"info"}),yt(r,y.gm.console,"debug",{level:"debug"}),yt(r,y.gm.console,"trace",{level:"trace"}),this.ee.on("wrap-logger-end",(function([e]){const{level:t,customAttributes:n}=this;(0,B.R)(r,e,n,t)})),this.importAggregator(e,(()=>i.e(478).then(i.bind(i,5288))))}}new class extends r{constructor(e){var t;(super(),y.gm)?(this.features={},(0,_.bQ)(this.agentIdentifier,this),this.desiredFeatures=new Set(e.features||[]),this.desiredFeatures.add(S),this.runSoftNavOverSpa=[...this.desiredFeatures].some((e=>e.featureName===n.K7.softNav)),(0,a.j)(this,e,e.loaderType||"agent"),t=this,p(u.cD,(function(e,r,n=!1){if("string"==typeof e){if(["string","number","boolean"].includes(typeof r)||null===r)return g(t,e,r,u.cD,n);(0,l.R)(40,typeof r)}else(0,l.R)(39,typeof e)}),t),function(e){p(u.Dl,(function(t){if("string"==typeof t||null===t)return g(e,"enduser.id",t,u.Dl,!0);(0,l.R)(41,typeof t)}),e)}(this),function(e){p(u.nb,(function(t){if("string"==typeof t||null===t)return g(e,"application.version",t,u.nb,!1);(0,l.R)(42,typeof t)}),e)}(this),function(e){p(u.d3,(function(){e.ee.emit("manual-start-all")}),e)}(this),this.run()):(0,l.R)(21)}get config(){return{info:this.info,init:this.init,loader_config:this.loader_config,runtime:this.runtime}}get api(){return this}run(){try{const e=function(e){const t={};return o.forEach((r=>{t[r]=!!e[r]?.enabled})),t}(this.init),t=[...this.desiredFeatures];t.sort(((e,t)=>n.P3[e.featureName]-n.P3[t.featureName])),t.forEach((t=>{if(!e[t.featureName]&&t.featureName!==n.K7.pageViewEvent)return;if(this.runSoftNavOverSpa&&t.featureName===n.K7.spa)return;if(!this.runSoftNavOverSpa&&t.featureName===n.K7.softNav)return;const r=function(e){switch(e){case n.K7.ajax:return[n.K7.jserrors];case n.K7.sessionTrace:return[n.K7.ajax,n.K7.pageViewEvent];case n.K7.sessionReplay:return[n.K7.sessionTrace];case n.K7.pageViewTiming:return[n.K7.pageViewEvent];default:return[]}}(t.featureName).filter((e=>!(e in this.features)));r.length>0&&(0,l.R)(36,{targetFeature:t.featureName,missingDependencies:r}),this.features[t.featureName]=new t(this)}))}catch(e){(0,l.R)(22,e);for(const e in this.features)this.features[e].abortHandler?.();const t=(0,_.Zm)();delete t.initializedAgents[this.agentIdentifier]?.features,delete this.sharedAggregator;return t.ee.get(this.agentIdentifier).abort(),!1}}}({features:[xe,S,P,He,Ke,j,Z,mt,Rt,We,pt],loaderType:"spa"})})()})();</script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=5.0"/>
    <title>Career Advising &amp; Professional Development | MIT</title>
    <link rel="shortcut icon" href="https://cdn.uconnectlabs.com/wp-content/themes/uConnect_MIT/favicon.ico?v=1695411673" type="image/x-icon" />
    <link rel="profile" href="http://gmpg.org/xfn/11"/>

    <script>
        if (window != window.top) {
            document.getElementsByTagName("html")[0].classList.add("embedded");
        }

            </script>

    <meta name='robots' content='max-image-preview:large' />
	<style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>
	<meta name="google-site-verification" content="2E2k1DqCJaPqfP7U6MvyBknMCgXG6mvnI0HvHinUEd0" />
<link rel='dns-prefetch' href='//fonts.googleapis.com' />
<link rel='dns-prefetch' href='//fast.fonts.net' />
<link rel="alternate" type="application/rss+xml" title="Career Advising &amp; Professional Development | MIT &raquo; Feed" href="https://capd.mit.edu/feed/" />
<script type="text/javascript">
/* <![CDATA[ */
window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/svg\/","svgExt":".svg","source":{"concatemoji":"https:\/\/cdn.uconnectlabs.com\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.11"}};
/*! This file is auto-generated */
!function(i,n){var o,s,e;function c(e){try{var t={supportTests:e,timestamp:(new Date).valueOf()};sessionStorage.setItem(o,JSON.stringify(t))}catch(e){}}function p(e,t,n){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);var t=new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data),r=(e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(n,0,0),new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data));return t.every(function(e,t){return e===r[t]})}function u(e,t,n){switch(t){case"flag":return n(e,"\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f","\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f")?!1:!n(e,"\ud83c\uddfa\ud83c\uddf3","\ud83c\uddfa\u200b\ud83c\uddf3")&&!n(e,"\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f","\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f");case"emoji":return!n(e,"\ud83d\udc26\u200d\ud83d\udd25","\ud83d\udc26\u200b\ud83d\udd25")}return!1}function f(e,t,n){var r="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?new OffscreenCanvas(300,150):i.createElement("canvas"),a=r.getContext("2d",{willReadFrequently:!0}),o=(a.textBaseline="top",a.font="600 32px Arial",{});return e.forEach(function(e){o[e]=t(a,e,n)}),o}function t(e){var t=i.createElement("script");t.src=e,t.defer=!0,i.head.appendChild(t)}"undefined"!=typeof Promise&&(o="wpEmojiSettingsSupports",s=["flag","emoji"],n.supports={everything:!0,everythingExceptFlag:!0},e=new Promise(function(e){i.addEventListener("DOMContentLoaded",e,{once:!0})}),new Promise(function(t){var n=function(){try{var e=JSON.parse(sessionStorage.getItem(o));if("object"==typeof e&&"number"==typeof e.timestamp&&(new Date).valueOf()<e.timestamp+604800&&"object"==typeof e.supportTests)return e.supportTests}catch(e){}return null}();if(!n){if("undefined"!=typeof Worker&&"undefined"!=typeof OffscreenCanvas&&"undefined"!=typeof URL&&URL.createObjectURL&&"undefined"!=typeof Blob)try{var e="postMessage("+f.toString()+"("+[JSON.stringify(s),u.toString(),p.toString()].join(",")+"));",r=new Blob([e],{type:"text/javascript"}),a=new Worker(URL.createObjectURL(r),{name:"wpTestEmojiSupports"});return void(a.onmessage=function(e){c(n=e.data),a.terminate(),t(n)})}catch(e){}c(n=f(s,u,p))}t(n)}).then(function(e){for(var t in e)n.supports[t]=e[t],n.supports.everything=n.supports.everything&&n.supports[t],"flag"!==t&&(n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&n.supports[t]);n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&!n.supports.flag,n.DOMReady=!1,n.readyCallback=function(){n.DOMReady=!0}}).then(function(){return e}).then(function(){var e;n.supports.everything||(n.readyCallback(),(e=n.source||{}).concatemoji?t(e.concatemoji):e.wpemoji&&e.twemoji&&(t(e.twemoji),t(e.wpemoji)))}))}((window,document),window._wpemojiSettings);
/* ]]> */
</script>
<link rel='stylesheet' id='uc_embed_google_docs-css' href='https://cdn.uconnectlabs.com/wp-content/plugins/uconnect-embed-google-docs-viewer/css/uconnect-embed-google-docs-viewer.css?ver=17020728561' type='text/css' media='all' />
<style id='wp-emoji-styles-inline-css' type='text/css'>

	img.wp-smiley, img.emoji {
		display: inline !important;
		border: none !important;
		box-shadow: none !important;
		height: 1em !important;
		width: 1em !important;
		margin: 0 0.07em !important;
		vertical-align: -0.1em !important;
		background: none !important;
		padding: 0 !important;
	}
</style>
<link rel='stylesheet' id='wp-block-library-css' href='https://cdn.uconnectlabs.com/wp-includes/css/dist/block-library/style.min.css?ver=6.8.11' type='text/css' media='all' />
<link rel='stylesheet' id='block_accordion_styles-css' href='https://cdn.uconnectlabs.com/wp-content/themes/uConnect/dist/css/block-accordion.min.css?ver=17156134921' type='text/css' media='all' />
<link rel='stylesheet' id='mediaelement-css' href='https://cdn.uconnectlabs.com/wp-includes/js/mediaelement/mediaelementplayer-legacy.min.css?ver=4.2.171' type='text/css' media='all' />
<link rel='stylesheet' id='wp-mediaelement-css' href='https://cdn.uconnectlabs.com/wp-includes/js/mediaelement/wp-mediaelement.min.css?ver=6.8.11' type='text/css' media='all' />
<style id='jetpack-sharing-buttons-style-inline-css' type='text/css'>
.jetpack-sharing-buttons__services-list{display:flex;flex-direction:row;flex-wrap:wrap;gap:0;list-style-type:none;margin:5px;padding:0}.jetpack-sharing-buttons__services-list.has-small-icon-size{font-size:12px}.jetpack-sharing-buttons__services-list.has-normal-icon-size{font-size:16px}.jetpack-sharing-buttons__services-list.has-large-icon-size{font-size:24px}.jetpack-sharing-buttons__services-list.has-huge-icon-size{font-size:36px}@media print{.jetpack-sharing-buttons__services-list{display:none!important}}.editor-styles-wrapper .wp-block-jetpack-sharing-buttons{gap:0;padding-inline-start:0}ul.jetpack-sharing-buttons__services-list.has-background{padding:1.25em 2.375em}
</style>
<link rel='stylesheet' id='muli_fonts-css' href='https://fonts.googleapis.com/css?family=Muli%3A300%2C400%2C400i%2C500%2C600%2C700&#038;display=swap&#038;subset=latin-ext&#038;ver=1.0' type='text/css' media='all' />
<link rel='stylesheet' id='mit_fonts-css' href='https://fast.fonts.net/cssapi/d1a7a6a7-f8a0-45f8-aea8-21101826bd46.css?ver=1.1' type='text/css' media='all' />
<link rel='stylesheet' id='uc-icons-css' href='https://cdn.uconnectlabs.com/wp-content/lib/icons/styles.css?ver=17158929011' type='text/css' media='all' />
<link rel='stylesheet' id='block-editor-customizable-style-css' href='https://cdn.uconnectlabs.com/wp-content/themes/uConnect/dist/css/block-editor/customizable-style.min.css?ver=17340324511' type='text/css' media='all' />
<style id='global-styles-inline-css' type='text/css'>
:root{--wp--preset--aspect-ratio--square: 1;--wp--preset--aspect-ratio--4-3: 4/3;--wp--preset--aspect-ratio--3-4: 3/4;--wp--preset--aspect-ratio--3-2: 3/2;--wp--preset--aspect-ratio--2-3: 2/3;--wp--preset--aspect-ratio--16-9: 16/9;--wp--preset--aspect-ratio--9-16: 9/16;--wp--preset--color--black: #000000;--wp--preset--color--cyan-bluish-gray: #abb8c3;--wp--preset--color--white: #ffffff;--wp--preset--color--pale-pink: #f78da7;--wp--preset--color--vivid-red: #cf2e2e;--wp--preset--color--luminous-vivid-orange: #ff6900;--wp--preset--color--luminous-vivid-amber: #fcb900;--wp--preset--color--light-green-cyan: #7bdcb5;--wp--preset--color--vivid-green-cyan: #00d084;--wp--preset--color--pale-cyan-blue: #8ed1fc;--wp--preset--color--vivid-cyan-blue: #0693e3;--wp--preset--color--vivid-purple: #9b51e0;--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);--wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);--wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);--wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);--wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);--wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);--wp--preset--font-size--small: 13px;--wp--preset--font-size--medium: 20px;--wp--preset--font-size--large: 36px;--wp--preset--font-size--x-large: 42px;--wp--preset--spacing--20: 0.44rem;--wp--preset--spacing--30: 0.67rem;--wp--preset--spacing--40: 1rem;--wp--preset--spacing--50: 1.5rem;--wp--preset--spacing--60: 2.25rem;--wp--preset--spacing--70: 3.38rem;--wp--preset--spacing--80: 5.06rem;--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);--wp--preset--shadow--basic: 0 1px 4px rgba(0, 0, 0, 0.25);--wp--preset--shadow--soft: 0 1px 2px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.07), 0 4px 8px rgba(0, 0, 0, 0.07), 0 8px 16px rgba(0, 0, 0, 0.07), 0 16px 32px rgba(0, 0, 0, 0.07);}:root { --wp--style--global--content-size: 1220px;--wp--style--global--wide-size: 1220px; }:where(body) { margin: 0; }.wp-site-blocks > .alignleft { float: left; margin-right: 2em; }.wp-site-blocks > .alignright { float: right; margin-left: 2em; }.wp-site-blocks > .aligncenter { justify-content: center; margin-left: auto; margin-right: auto; }:where(.wp-site-blocks) > * { margin-block-start: 24px; margin-block-end: 0; }:where(.wp-site-blocks) > :first-child { margin-block-start: 0; }:where(.wp-site-blocks) > :last-child { margin-block-end: 0; }:root { --wp--style--block-gap: 24px; }:root :where(.is-layout-flow) > :first-child{margin-block-start: 0;}:root :where(.is-layout-flow) > :last-child{margin-block-end: 0;}:root :where(.is-layout-flow) > *{margin-block-start: 24px;margin-block-end: 0;}:root :where(.is-layout-constrained) > :first-child{margin-block-start: 0;}:root :where(.is-layout-constrained) > :last-child{margin-block-end: 0;}:root :where(.is-layout-constrained) > *{margin-block-start: 24px;margin-block-end: 0;}:root :where(.is-layout-flex){gap: 24px;}:root :where(.is-layout-grid){gap: 24px;}.is-layout-flow > .alignleft{float: left;margin-inline-start: 0;margin-inline-end: 2em;}.is-layout-flow > .alignright{float: right;margin-inline-start: 2em;margin-inline-end: 0;}.is-layout-flow > .aligncenter{margin-left: auto !important;margin-right: auto !important;}.is-layout-constrained > .alignleft{float: left;margin-inline-start: 0;margin-inline-end: 2em;}.is-layout-constrained > .alignright{float: right;margin-inline-start: 2em;margin-inline-end: 0;}.is-layout-constrained > .aligncenter{margin-left: auto !important;margin-right: auto !important;}.is-layout-constrained > :where(:not(.alignleft):not(.alignright):not(.alignfull)){max-width: var(--wp--style--global--content-size);margin-left: auto !important;margin-right: auto !important;}.is-layout-constrained > .alignwide{max-width: var(--wp--style--global--wide-size);}body .is-layout-flex{display: flex;}.is-layout-flex{flex-wrap: wrap;align-items: center;}.is-layout-flex > :is(*, div){margin: 0;}body .is-layout-grid{display: grid;}.is-layout-grid > :is(*, div){margin: 0;}body{padding-top: 0px;padding-right: 0px;padding-bottom: 0px;padding-left: 0px;}a:where(:not(.wp-element-button)){text-decoration: underline;}:root :where(.wp-element-button, .wp-block-button__link){background-color: #32373c;border-width: 0;color: #fff;font-family: inherit;font-size: inherit;line-height: inherit;padding: calc(0.667em + 2px) calc(1.333em + 2px);text-decoration: none;}.has-black-color{color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-color{color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-color{color: var(--wp--preset--color--white) !important;}.has-pale-pink-color{color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-color{color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-color{color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-color{color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-color{color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-color{color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-color{color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-color{color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-color{color: var(--wp--preset--color--vivid-purple) !important;}.has-black-background-color{background-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-background-color{background-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-background-color{background-color: var(--wp--preset--color--white) !important;}.has-pale-pink-background-color{background-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-background-color{background-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-background-color{background-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-background-color{background-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-background-color{background-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-background-color{background-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-background-color{background-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-background-color{background-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-background-color{background-color: var(--wp--preset--color--vivid-purple) !important;}.has-black-border-color{border-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-border-color{border-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-border-color{border-color: var(--wp--preset--color--white) !important;}.has-pale-pink-border-color{border-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-border-color{border-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-border-color{border-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-border-color{border-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-border-color{border-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-border-color{border-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-border-color{border-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-border-color{border-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-border-color{border-color: var(--wp--preset--color--vivid-purple) !important;}.has-vivid-cyan-blue-to-vivid-purple-gradient-background{background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;}.has-light-green-cyan-to-vivid-green-cyan-gradient-background{background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;}.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;}.has-luminous-vivid-orange-to-vivid-red-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;}.has-very-light-gray-to-cyan-bluish-gray-gradient-background{background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;}.has-cool-to-warm-spectrum-gradient-background{background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;}.has-blush-light-purple-gradient-background{background: var(--wp--preset--gradient--blush-light-purple) !important;}.has-blush-bordeaux-gradient-background{background: var(--wp--preset--gradient--blush-bordeaux) !important;}.has-luminous-dusk-gradient-background{background: var(--wp--preset--gradient--luminous-dusk) !important;}.has-pale-ocean-gradient-background{background: var(--wp--preset--gradient--pale-ocean) !important;}.has-electric-grass-gradient-background{background: var(--wp--preset--gradient--electric-grass) !important;}.has-midnight-gradient-background{background: var(--wp--preset--gradient--midnight) !important;}.has-small-font-size{font-size: var(--wp--preset--font-size--small) !important;}.has-medium-font-size{font-size: var(--wp--preset--font-size--medium) !important;}.has-large-font-size{font-size: var(--wp--preset--font-size--large) !important;}.has-x-large-font-size{font-size: var(--wp--preset--font-size--x-large) !important;}
:root :where(.wp-block-pullquote){font-size: 1.5em;line-height: 1.6;}
</style>
<link rel='stylesheet' id='jquery-ui-css' href='https://cdn.uconnectlabs.com/wp-content/themes/uConnect/jquery-ui/jquery-ui.min.css?ver=16346624661' type='text/css' media='all' />
<link rel='stylesheet' id='uconnect-events-widget-style-css' href='https://cdn.uconnectlabs.com/wp-content/mu-plugins/uconnect/modules/events/css/uc-events-calendar-widget.css?ver=17340324441' type='text/css' media='all' />
<link rel='stylesheet' id='uc_guest_blog_front-css' href='https://cdn.uconnectlabs.com/wp-content/plugins/uconnect-guest-blog/css/uc_guest_blog_front.css?ver=17340324491' type='text/css' media='all' />
<link rel='stylesheet' id='materialize-css' href='https://cdn.uconnectlabs.com/wp-content/lib/materialize/css/materialize.min.css?ver=16730286311' type='text/css' media='all' />
<link rel='stylesheet' id='resets-css' href='https://cdn.uconnectlabs.com/wp-content/themes/uConnect/dist/css/reset.min.css?ver=17466076101' type='text/css' media='all' />
<link rel='stylesheet' id='owl-carousel-css' href='https://cdn.uconnectlabs.com/wp-content/lib/js/owl-carousel/owl.carousel.css?ver=16346624651' type='text/css' media='all' />
<link rel='stylesheet' id='dashicons-css' href='https://cdn.uconnectlabs.com/wp-includes/css/dashicons.min.css?ver=6.8.11' type='text/css' media='all' />
<link rel='stylesheet' id='jquery-tooltipster-css' href='https://cdn.uconnectlabs.com/wp-content/lib/js/jquery-tooltipster/tooltipster.bundle.min.css?ver=16618255451' type='text/css' media='all' />
<link rel='stylesheet' id='jquery-tooltipster-light-css' href='https://cdn.uconnectlabs.com/wp-content/lib/js/jquery-tooltipster/tooltipster-sideTip-light.min.css?ver=16618255451' type='text/css' media='all' />
<link rel='stylesheet' id='uconnect-css' href='https://cdn.uconnectlabs.com/wp-content/themes/uConnect/dist/css/styles.min.css?ver=17501763681' type='text/css' media='all' />
<style id='uconnect-inline-css' type='text/css'>
:root {--hero_banner_width: 1600;--hero_banner_height: 460;--sidebar_uc_button_font_size: 1.375rem;--sidebar_uc_button_font_weight: 400;}
</style>
<link rel='stylesheet' id='client-theme-styles-css' href='https://cdn.uconnectlabs.com/wp-content/themes/uConnect_MIT/dist/css/styles.min.css?ver=17466993631' type='text/css' media='all' />
<style id='akismet-widget-style-inline-css' type='text/css'>

			.a-stats {
				--akismet-color-mid-green: #357b49;
				--akismet-color-white: #fff;
				--akismet-color-light-grey: #f6f7f7;

				max-width: 350px;
				width: auto;
			}

			.a-stats * {
				all: unset;
				box-sizing: border-box;
			}

			.a-stats strong {
				font-weight: 600;
			}

			.a-stats a.a-stats__link,
			.a-stats a.a-stats__link:visited,
			.a-stats a.a-stats__link:active {
				background: var(--akismet-color-mid-green);
				border: none;
				box-shadow: none;
				border-radius: 8px;
				color: var(--akismet-color-white);
				cursor: pointer;
				display: block;
				font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen-Sans', 'Ubuntu', 'Cantarell', 'Helvetica Neue', sans-serif;
				font-weight: 500;
				padding: 12px;
				text-align: center;
				text-decoration: none;
				transition: all 0.2s ease;
			}

			/* Extra specificity to deal with TwentyTwentyOne focus style */
			.widget .a-stats a.a-stats__link:focus {
				background: var(--akismet-color-mid-green);
				color: var(--akismet-color-white);
				text-decoration: none;
			}

			.a-stats a.a-stats__link:hover {
				filter: brightness(110%);
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06), 0 0 2px rgba(0, 0, 0, 0.16);
			}

			.a-stats .count {
				color: var(--akismet-color-white);
				display: block;
				font-size: 1.5em;
				line-height: 1.4;
				padding: 0 13px;
				white-space: nowrap;
			}
		
</style>
<script type="text/javascript" id="uconnect-analytics-js-before">
/* <![CDATA[ */
window.uconnect = window.uconnect || {}; window.uconnect.analytics = window.uconnect.analytics || {}; uconnect.analytics.settings = {"send_page_view":true,"send_404":false,"trackers":{"client_ga4":{"id":"G-R6KRQHZJBT","version":"GA4","is_external":false,"page_title":false,"dimensions":{"is_user_logged_in":false,"site_id":"1:123"}}},"embedded":false}
/* ]]> */
</script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/mu-plugins/uconnect/modules/analytics/dist/js/analytics-tracking.min.js?ver=17364323261" id="uconnect-analytics-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-includes/js/jquery/jquery.min.js?ver=3.7.11" id="jquery-core-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.11" id="jquery-migrate-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/materialize/js/materialize.min.js?ver=16730286311" id="materialize-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/js/uc-lib-common.js?ver=16498867121" id="uc-lib-common-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/js/uc-lib-frontend.js?ver=16346624651" id="uc-lib-front-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/js/placeholders.jquery.min.js?ver=16346624651" id="placeholder-polyfill-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/js/basic-jquery-slider.js?ver=16934055051" id="basic-jquery-slider-js"></script>
<script type="text/javascript" id="uconnect-theme-head-js-extra">
/* <![CDATA[ */
var uc_jquery_migrate = {"migrateTrace":"","migrateMute":"1"};
/* ]]> */
</script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/themes/uConnect/js/uconnect-head.js?ver=17315764941" id="uconnect-theme-head-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/js/jquery.lettering.min.js?ver=16346624651" id="lettering-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/js/jquery.cookie.js?ver=16346624651" id="jquery-cookie-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/js/jquery.infieldlabel.min.js?ver=16346624651" id="infieldlabel-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/js/modernizr.js?ver=16346624651" id="modernizr-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/js/owl-carousel/owl.carousel.js?ver=16346624651" id="owl-carousel-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/js/textFit.min.js?ver=16346624651" id="textfit-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/js/jquery-tooltipster/tooltipster.bundle.min.js?ver=16618255451" id="jquery-tooltipster-js"></script>
<link rel="https://api.w.org/" href="https://capd.mit.edu/api/" /><link rel="alternate" title="JSON" type="application/json" href="https://capd.mit.edu/api/wp/v2/pages/6" /><link rel="canonical" href="https://capd.mit.edu/" />
<link rel='shortlink' href='https://capd.mit.edu/' />
<link rel="canonical" href="https://capd.mit.edu/" />
<!-- Stream WordPress user activity plugin v4.1.1 -->

<!-- Jetpack Open Graph Tags -->
<meta property="og:type" content="website" />
<meta property="og:title" content="Career Advising &amp; Professional Development | MIT" />
<meta property="og:url" content="https://capd.mit.edu/" />
<meta property="og:site_name" content="Career Advising &amp; Professional Development | MIT" />
<meta property="og:image" content="https://cdn.uconnectlabs.com/wp-content/themes/uConnect_MIT/images/default-og-image.png?v=4.744.1.001" />
<meta property="og:locale" content="en_US" />
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:image" content="https://cdn.uconnectlabs.com/wp-content/themes/uConnect_MIT/images/default-og-image.png?v=4.744.1.001" />

<!-- End Jetpack Open Graph Tags -->
</head>
<body class="home wp-singular page-template-default page page-id-6 wp-theme-uConnect wp-child-theme-uConnect_MIT page-front-page full-width-hero hero-space-banner has-hero-nav double-column">
<div id="wrapper" class="hfeed">
            <section class="skip-link-container" aria-label="Skip links">
            <a class="skip-link screen-reader-text" href="#content"
                    title="Skip to content">Skip to content</a>
            <a class="skip-link screen-reader-text" href="#main-nav-skipper"
                title="Skip to main nav">Skip to main nav</a>
        </section>
    <header id="header" class=" has_nav">
    <div id="masthead">
        <div id="masthead_inner" class="clearfix">
            <div id="top_ribbon">
                <div class="widget widget_uc_search">        <form role="search" method="get" class="searchform " action="https://capd.mit.edu/search/">
            <div>
                <input type="text" value="" name="s" id="s1">
                <label for="s1">                        <span class="screen-reader-text">Search Keywords</span>
                                        <span class="search-icon" role="img" aria-hidden="true"></span>
                    </label>
                                <span class="screen-reader-text">                <input type="submit" value="Submit Search" class="button">
                </span>            </div>
        </form>
        </div>                <div class="top-ribbon-nav"><ul id="menu-top-ribbon-menu" class="menu" aria-label="Top Ribbon menu" role="navigation"><li id="menu-item-630" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-630"><a href="https://capd.mit.edu/contact-us/">Contact Us</a></li>
</ul></div><!-- Served from cache in 0.00011 seconds. --><nav class="top-ribbon-nav" aria-label="User account menu"><ul id="menu-user-account-nav" class="menu"><li id="menu-item-guests" class="menu-item-first menu-item menu-item-type-custom menu-item-object-custom menu-item-guests"><a href="https://capd.mit.edu/account/guests/" title="Log in to the guest contributors portal">Guests</a></li>
<li id="menu-item-log-in" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-log-in"><a href="https://capd.mit.edu/account/login/">Log in</a></li>
<li id="menu-item-sign-up" class="menu-item-last menu-item menu-item-type-custom menu-item-object-custom menu-item-sign-up"><a href="https://capd.mit.edu/account/signup/start/" title="Sign up for an account">Sign up</a></li>
</ul></nav><!-- Served from cache in 0.00029 seconds. -->            </div>
                        <h1 id="site-logo" class="site-logo">
                <a class="school-logo" href="https://mit.edu" target="_blank" aria-label="MIT website">
                    <img src="https://cdn.uconnectlabs.com/wp-content/themes/uConnect_MIT/images/mit-capd-left.svg?v=2" alt="MIT Logo">
                </a>
                <a class="internal-logo" href="https://capd.mit.edu/" title="Career Advising &amp; Professional Development | MIT" rel="home">
                    <img src="https://cdn.uconnectlabs.com/wp-content/themes/uConnect_MIT/images/mit-capd-right.svg?v=2" alt="Career Advising & Professional Development">
                </a>
            </h1>
            <!--div id="site-description"></div-->
            <div id="header-widget-area" role="navigation" aria-label="Header area navigation">
                <div id="uc_button-5" class="widget-wrapper widget_uc_button"><a href="/channels/employer/?utc_src_link=header">For Employers</a></div><div id="uc_button-4" class="widget-wrapper widget_uc_button"><a href="https://mit.joinhandshake.com/login/?utc_src_link=header">Handshake</a></div><div id="uc_button-2" class="widget-wrapper widget_uc_button"><a href="https://app.joinhandshake.com/appointments/new/?utc_src_link=header">Schedule Appointment</a></div>            </div>
            <div id="access" role="navigation" aria-label="Primary Navigation">
                                <div id="main-nav-skipper" class="standard-nav"><ul id="menu-main-nav" class="menu"><li id="menu-item-42" class="arrow wide-submenu sub-panel menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-42"><a  href="#!" aria-expanded="false" aria-haspopup="menu">Who You Are<span class="expand-icon icon icon-fa-angle-down" aria-hidden="true"></span></a>
<div class="sub-menu"><ul class="">
	<li id="menu-item-368" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-has-children menu-item-368"><a href="https://capd.mit.edu/channels/affinity-groups/" aria-expanded="false">Affinity / Identity</a>
	<div class="sub-menu"><ul class="">
		<li id="menu-item-369" class="menu-item-first desktop menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-369"><a href="https://capd.mit.edu/channels/daca-undocumented/">DACA/Undocumented</a></li>
		<li id="menu-item-370" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-370"><a href="https://capd.mit.edu/channels/first-generation-low-income/">First Generation, Low Income</a></li>
		<li id="menu-item-53487" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-53487"><a href="https://capd.mit.edu/channels/international-students/">International Students</a></li>
		<li id="menu-item-372" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-372"><a href="https://capd.mit.edu/channels/lbgtq/">LBGTQ+</a></li>
		<li id="menu-item-373" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-373"><a href="https://capd.mit.edu/channels/students-of-color/">Students of Color</a></li>
		<li id="menu-item-374" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-374"><a href="https://capd.mit.edu/channels/students-with-disabilities/">Students with disabilities</a></li>
		<li id="menu-item-650" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-650"><a href="https://capd.mit.edu/channels/veterans/">Veterans</a></li>
		<li id="menu-item-651" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-651"><a href="https://capd.mit.edu/channels/women/">Womxn</a></li>
	</ul></div>
</li>
	<li id="menu-item-43" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-43"><a href="#!" aria-expanded="false">Audiences</a>
	<div class="sub-menu"><ul class="">
		<li id="menu-item-56" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-56"><a href="https://capd.mit.edu/channels/student/">Undergraduate Students</a></li>
		<li id="menu-item-122" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-122"><a href="https://capd.mit.edu/channels/masters-students/">Master&#8217;s Students</a></li>
		<li id="menu-item-121" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-121"><a href="https://capd.mit.edu/channels/phd-students/">PhD Students</a></li>
		<li id="menu-item-55" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-55"><a href="https://capd.mit.edu/channels/postdocs/">Postdocs</a></li>
		<li id="menu-item-237" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-237"><a href="https://capd.mit.edu/channels/alumni/">Alumni</a></li>
		<li id="menu-item-110312" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-110312"><a href="/channels/employer/?utc_src_link=audiencenav">Employers</a></li>
		<li id="menu-item-52" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-52"><a href="https://capd.mit.edu/channels/staff/">Faculty/Staff</a></li>
		<li id="menu-item-26676" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-26676"><a href="https://capd.mit.edu/channels/family-supporters/">Family/Supporters</a></li>
	</ul></div>
</li>
	<li id="menu-item-110313" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-110313"><a href="/channels/employer/?utc_src_link=foremployersnav" aria-expanded="false">For Employers</a>
	<div class="sub-menu"><ul class="">
		<li id="menu-item-110314" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-110314"><a href="/channels/attend-a-career-fair/?utc_src_link=foremployersnav">Career Fairs</a></li>
		<li id="menu-item-268" class="menu-item menu-item-type-post_type menu-item-object-resource menu-item-268"><a href="https://capd.mit.edu/resources/post-jobs-internships-fellowships/">Post jobs, internships, and fellowships</a></li>
		<li id="menu-item-265" class="menu-item menu-item-type-post_type menu-item-object-resource menu-item-265"><a href="https://capd.mit.edu/resources/build-your-brand-at-mit/">Build your brand at MIT</a></li>
		<li id="menu-item-10362" class="menu-item menu-item-type-post_type menu-item-object-resource menu-item-10362"><a href="https://capd.mit.edu/resources/recruiting-guidelines/">Recruiting Guidelines and Resources</a></li>
		<li id="menu-item-708" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-708"><a href="/channels/employer-relations/?utc_src_link=foremployersnav">Connect with Us</a></li>
	</ul></div>
</li>
</ul></div>
</li>
<li id="menu-item-191" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-191 menu-item-has-expand-button"><a href="https://capd.mit.edu/channels/what-we-offer/">What We Offer</a><button class="button-link standard-nav-item-expand-button" aria-label="What We Offer navigation items" aria-expanded="false" aria-haspopup="menu"><span class="expand-icon icon icon-fa-angle-down" aria-hidden="true"></span></button>
<div class="sub-menu"><ul class="">
	<li id="menu-item-192" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-192"><a href="https://capd.mit.edu/channels/career-advising/">Career Advising</a></li>
	<li id="menu-item-110315" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-110315"><a href="/channels/distinguished-fellowships/?utc_src_link=whatweoffernav">Distinguished Fellowships</a></li>
	<li id="menu-item-110316" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-110316"><a href="/channels/employer-relations/?utc_src_link=whatweoffernav">Employer Relations</a></li>
	<li id="menu-item-5021" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-5021"><a href="/channels/graduate-student-professional-development/?utc_src_link=whatweoffernav">Graduate Student Professional Development</a></li>
	<li id="menu-item-195" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-195"><a href="https://capd.mit.edu/channels/prehealth-advising/">Prehealth Advising</a></li>
</ul></div>
</li>
<li id="menu-item-110317" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-110317 menu-item-has-expand-button"><a href="/channels/career-interests/?utc_src_link=careerinterestsnav">Career Interests</a><button class="button-link standard-nav-item-expand-button" aria-label="Career Interests navigation items" aria-expanded="false" aria-haspopup="menu"><span class="expand-icon icon icon-fa-angle-down" aria-hidden="true"></span></button>
<div class="sub-menu"><ul class="">
	<li id="menu-item-135" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-135"><a href="https://capd.mit.edu/channels/academia-education/">Academia &amp; Education</a></li>
	<li id="menu-item-136" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-136"><a href="https://capd.mit.edu/channels/architecture-planning-design/">Architecture, Planning, &amp; Design</a></li>
	<li id="menu-item-137" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-137"><a href="https://capd.mit.edu/channels/arts-communications-media/">Arts, Communications, &amp; Media</a></li>
	<li id="menu-item-22007" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-22007"><a href="https://capd.mit.edu/channels/business-finance-fintech/">Business, Finance, &amp; Fintech</a></li>
	<li id="menu-item-56597" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-56597"><a href="https://capd.mit.edu/channels/computing-and-computer-technology/">Computing &amp; Computer Technology</a></li>
	<li id="menu-item-140" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-140"><a href="https://capd.mit.edu/channels/consulting/">Consulting</a></li>
	<li id="menu-item-141" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-141"><a href="https://capd.mit.edu/channels/data-science/">Data Science</a></li>
	<li id="menu-item-5531" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-5531"><a href="https://capd.mit.edu/channels/energy-environment-sustainability/">Energy, Environment, &amp; Sustainability</a></li>
	<li id="menu-item-143" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-143"><a href="https://capd.mit.edu/channels/life-sciences-biotech-pharma/">Life Sciences, Biotech, &amp; Pharma</a></li>
	<li id="menu-item-138" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-138"><a href="https://capd.mit.edu/channels/manufacturing-transportation/">Manufacturing &amp; Transportation</a></li>
	<li id="menu-item-144" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-144"><a href="https://capd.mit.edu/channels/health-and-medical-professions/">Health &amp; Medical Professions</a></li>
	<li id="menu-item-145" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-145"><a href="https://capd.mit.edu/channels/social-impact-policy-and-law/">Social Impact, Policy, &amp; Law</a></li>
</ul></div>
</li>
<li id="menu-item-10848" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-10848 menu-item-has-expand-button"><a href="https://capd.mit.edu/channels/learn-about/">Resources &amp; Advice</a><button class="button-link standard-nav-item-expand-button" aria-label="Resources &amp; Advice navigation items" aria-expanded="false" aria-haspopup="menu"><span class="expand-icon icon icon-fa-angle-down" aria-hidden="true"></span></button>
<div class="sub-menu"><ul class="">
	<li id="menu-item-110318" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-110318"><a href="/channels/getting-started/?utc_src_link=learnaboutnav">Getting Started &amp; Handshake 101</a></li>
	<li id="menu-item-10334" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-10334"><a href="https://capd.mit.edu/channels/explore-careers/">Exploring careers</a></li>
	<li id="menu-item-130593" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-130593"><a href="https://capd.mit.edu/channels/network-conduct-informational-interview/">Networking &amp; Informational Interviews</a></li>
	<li id="menu-item-10336" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-10336"><a href="https://capd.mit.edu/channels/connecting-with-employers/">Connecting with employers</a></li>
	<li id="menu-item-10341" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-10341"><a href="https://capd.mit.edu/channels/make-a-resume-cover-letter-cv/">Resumes, cover letters, portfolios, &amp; CVs</a></li>
	<li id="menu-item-110320" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-110320"><a href="/channels/attend-a-career-fair/?utc_src_link=learnaboutnav">Career Fairs</a></li>
	<li id="menu-item-10849" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-10849"><a href="https://capd.mit.edu/channels/find-a-job-or-internship/">Finding a Job or Internship</a></li>
	<li id="menu-item-10830" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10830"><a href="https://capd.mit.edu/post-graduateandsummer/">Post-Graduate and Summer Outcomes</a></li>
	<li id="menu-item-110321" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-110321"><a href="/channels/graduate-student-professional-development/?utc_src_link=learnaboutnav">Professional Development Competencies</a></li>
	<li id="menu-item-110322" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-110322"><a href="/channels/distinguished-fellowships/?utc_src_link=learnaboutnav">Distinguished Fellowships</a></li>
	<li id="menu-item-10836" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-10836"><a href="https://capd.mit.edu/channels/prepare-for-graduate-professional-school/">Preparing for Graduate &amp; Professional Schools</a></li>
	<li id="menu-item-10837" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-10837"><a href="https://capd.mit.edu/channels/prepare-for-medical-health-profession-schools/">Preparing for Medical / Health Profession Schools</a></li>
	<li id="menu-item-10340" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-10340"><a href="https://capd.mit.edu/channels/interview/">Interviewing</a></li>
	<li id="menu-item-10342" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-10342"><a href="https://capd.mit.edu/channels/job-offers/">Job offers</a></li>
	<li id="menu-item-100411" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-100411"><a href="https://capd.mit.edu/channels/new-jobs-and-career-transitions/">New jobs &amp; career transitions</a></li>
</ul></div>
</li>
<li id="menu-item-214" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-214 menu-item-has-expand-button"><a href="/events/?utc_src_link=eventsnav">Events</a><button class="button-link standard-nav-item-expand-button" aria-label="Events navigation items" aria-expanded="false" aria-haspopup="menu"><span class="expand-icon icon icon-fa-angle-down" aria-hidden="true"></span></button>
<div class="sub-menu"><ul class="">
	<li id="menu-item-21340" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-21340"><a href="https://capd.mit.edu/events/career-development-programs/">Career Prep and Development Programs</a></li>
	<li id="menu-item-280133" class="menu-item menu-item-type-taxonomy menu-item-object-shared_category menu-item-280133"><a href="https://capd.mit.edu/channels/fall-career-fair/">Fall Career Fair (FCF)</a></li>
	<li id="menu-item-216" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-216"><a href="/channels/attend-a-career-fair/?utc_src_link=eventsnav">Career Fairs</a></li>
	<li id="menu-item-21341" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-21341"><a href="https://capd.mit.edu/events/employer-events/">Employer Events</a></li>
	<li id="menu-item-132829" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-132829"><a href="https://capd.mit.edu/events/outside-events/">Outside Events for Career and Professional Development</a></li>
	<li id="menu-item-215" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-215"><a href="/events/?utc_src_link=eventscalnav">Events Calendar</a></li>
	<li id="menu-item-5168" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-5168"><a href="https://mit.uconnectlabs.com/career-services-workshop-requests/">Career Services Workshop Requests</a></li>
</ul></div>
</li>
<li id="menu-item-110" class="arrow wide-submenu sub-panel menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-110"><a  href="#!" aria-expanded="false" aria-haspopup="menu">Staff<span class="expand-icon icon icon-fa-angle-down" aria-hidden="true"></span></a>
<div class="sub-menu"><ul class="">
	<li id="menu-item-463" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-463"><a href="https://mit.uconnectlabs.com/staff/" aria-expanded="false">Meet the Teams</a>
	<div class="sub-menu"><ul class="">
		<li id="menu-item-464" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-464"><a href="https://capd.mit.edu/career-advising/">Career Advising</a></li>
		<li id="menu-item-14510" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-14510"><a href="https://capd.mit.edu/pages-distinguished-fellowships/">Distinguished Fellowships</a></li>
		<li id="menu-item-25950" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-25950"><a href="https://capd.mit.edu/early-career-advisory-board-members">Early Career Advisory Board</a></li>
		<li id="menu-item-465" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-465"><a href="https://capd.mit.edu/employer-relations/">Employer Relations</a></li>
		<li id="menu-item-467" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-467"><a href="https://capd.mit.edu/professional-development/">Graduate Student Professional Development</a></li>
		<li id="menu-item-655" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-655"><a href="https://mit.uconnectlabs.com/operations/">Operations</a></li>
		<li id="menu-item-27266" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-27266"><a href="https://capd.mit.edu/peer-career-advisors/">Peer Career Advisors</a></li>
		<li id="menu-item-652" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-652"><a href="https://mit.uconnectlabs.com/prehealth-advising/">Prehealth Advising</a></li>
		<li id="menu-item-469" class="menu-item-last menu-item menu-item-type-post_type menu-item-object-page menu-item-469"><a href="https://capd.mit.edu/student-staff/">Student Staff</a></li>
	</ul></div>
</li>
</ul></div>
</li>
<li class="hamburger-menu-container js-hamburger-menu-container"><button class="hamburger-menu js-hamburger-menu" aria-label="More Primary Navigation items" aria-expanded="false"><span class="icon-menu" aria-hidden="true"></span></button></li></ul></div><!-- Served from cache in 0.00017 seconds. -->            </div><!-- #access -->
        </div><!-- #masthead_inner -->
    </div><!-- #masthead -->
</header>
    
<div
    class="uconnect-hero-space has-carousel"
    id="featured_box_wrapper"
    aria-label="Hero Space"
    role="region"
>
    <div id="featured_box">
        <div id="banners">
                        <ul class="bjqs" aria-label="Hero Banners">
                                    <li class="banner" id="hero-space-banner-1"
                        style="background-image: url('https://cdn.uconnectlabs.com/wp-content/uploads/sites/123/2025/06/062025-BreakResources.png?v=284955');">
                                                <a href="https://capd.mit.edu/blog/2025/05/23/5-tips-to-make-your-break-count/">
                                                    <div class="banner-inner">
                                                                                                    <span class="screen-reader-text">Over an aerial image of MIT's campus and the Charles River facing Boston, the text reads, "Five simple career tips for your summer (whatever it looks like). If you have a few minutes, you can make an impact for your future self!"</span>
                                                            </div>
                                                    </a>
                                                </li>
                                        <li class="banner" id="hero-space-banner-2"
                        style="background-image: url('https://cdn.uconnectlabs.com/wp-content/uploads/sites/123/2025/03/102024-ResourcesAdvice2-1.png?v=265595');">
                                                <a href="https://capd.mit.edu/channels/resources-advice/">
                                                    <div class="banner-inner">
                                                                                                    <span class="screen-reader-text">On top of a close-up of a campus art piece, the text reads, "Looking for resources or advice on career topics? It doesn’t have to be overwhelming! Start with this breakdown of essential topics."</span>
                                                            </div>
                                                    </a>
                                                </li>
                                        <li class="banner" id="hero-space-banner-3"
                        style="background-image: url('https://cdn.uconnectlabs.com/wp-content/uploads/sites/123/2025/06/052025-FCFemployer.png?v=284956');">
                                                <a href="https://capd.mit.edu/channels/fcf-employerinfo/">
                                                    <div class="banner-inner">
                                                                                                    <span class="screen-reader-text">Over a collage of Fall Career Fair photos, the text reads, "Employer registration is now open for the MIT Fall Career Fair, MIT's largest recruiting event!"</span>
                                                            </div>
                                                    </a>
                                                </li>
                                        <li class="banner" id="hero-space-banner-4"
                        style="background-image: url('https://cdn.uconnectlabs.com/wp-content/uploads/sites/123/2024/12/082024-Handshake.png?v=240810');">
                                                <a href="https://capd.mit.edu/resources/handshake-101/">
                                                    <div class="banner-inner">
                                                                                                    <span class="screen-reader-text">Over an image of a laptop sitting on a chair, text reads, "Handshake: Your one-stop site for events, appointments, and jobs/internships. Learn how to make the most of it."</span>
                                                            </div>
                                                    </a>
                                                </li>
                                </ul>
                        </div><!-- #banner -->
    </div><!-- #featured_box -->
    </div><!-- #featured_box_wrapper -->
<ul id="menu-hero-nav" class="hero-nav"><li id="menu-item-207940" class="menu-item-first bg-color-A72036 text-size-16 text-color-ffffff menu-item menu-item-type-post_type menu-item-object-page menu-item-207940" style=" background-color: #A72036; --hero_nav_line_height: 1.25rem; line-height: 1.25rem; font-size: 1rem;"><a href="https://capd.mit.edu/career-support-for-different-groups-on-campus/" style="color: #ffffff !important;"><span class="menu-item-content"><i aria-hidden="true"></i><span class='custom-menu-icon icon-recruit' aria-hidden='true'></span>Who we serve</span></a><style>[id*=progress-design] .menu-item-207940, [id*=progress-design] .menu-item-207940:hover{ background-color: rgb(38, 7, 12) !important; } .menu-item-207940:hover{ background-color: rgb(102, 19, 33) !important; } [id*=progress-design] .menu-item-207940>a:before, [id*=progress-design] .menu-item-207940>a:after{background-color: #A72036;} [id*=progress-design] .menu-item-207940>a:hover:after, [id*=progress-design] .menu-item-207940>a:hover:before{background-color: rgb(102, 19, 33);}</style></li>
<li id="menu-item-167762" class="bg-color-b8233b iconi-documents text-size-16 text-color-ffffff menu-item menu-item-type-custom menu-item-object-custom menu-item-167762" style=" background-color: #b8233b; --hero_nav_line_height: 1.25rem; line-height: 1.25rem; font-size: 1rem;"><a href="https://capd.mit.edu/channels/learn-about/" style="color: #ffffff !important;"><span class="menu-item-content"><i aria-hidden="true"></i>Resources &amp; Advice</span></a><style> .menu-item-167762:hover{ background-color: rgb(119, 22, 38) !important; } [id*=progress-design] .menu-item-167762>a:before, [id*=progress-design] .menu-item-167762>a:after{background-color: #b8233b;} [id*=progress-design] .menu-item-167762>a:hover:after, [id*=progress-design] .menu-item-167762>a:hover:before{background-color: rgb(119, 22, 38);}</style></li>
<li id="menu-item-108" class="bg-color-ce2742 iconi-user-general text-size-16 text-color-ffffff menu-item menu-item-type-custom menu-item-object-custom menu-item-108" style=" background-color: #ce2742; --hero_nav_line_height: 1.25rem; line-height: 1.25rem; font-size: 1rem;"><a href="https://app.joinhandshake.com/appointments/new/?utc_src_link=homehero" style="color: #ffffff !important;"><span class="menu-item-content"><i aria-hidden="true"></i>Appointments</span></a><style> .menu-item-108:hover{ background-color: rgb(141, 26, 45) !important; } [id*=progress-design] .menu-item-108>a:before, [id*=progress-design] .menu-item-108>a:after{background-color: #ce2742;} [id*=progress-design] .menu-item-108>a:hover:after, [id*=progress-design] .menu-item-108>a:hover:before{background-color: rgb(141, 26, 45);}</style></li>
<li id="menu-item-101" class="menu-item-last bg-color-d93650 iconi-jobs text-size-16 text-color-ffffff menu-item menu-item-type-custom menu-item-object-custom menu-item-101" style=" background-color: #d93650; --hero_nav_line_height: 1.25rem; line-height: 1.25rem; font-size: 1rem;"><a href="https://capd.mit.edu/channels/getting-started/?utc_src_link=homehero" style="color: #ffffff !important;"><span class="menu-item-content"><i aria-hidden="true"></i>Getting Started</span></a><style> .menu-item-101:hover{ background-color: rgb(163, 30, 52) !important; } [id*=progress-design] .menu-item-101>a:before, [id*=progress-design] .menu-item-101>a:after{background-color: #d93650;} [id*=progress-design] .menu-item-101>a:hover:after, [id*=progress-design] .menu-item-101>a:hover:before{background-color: rgb(163, 30, 52);}</style></li>
</ul><!-- Served from cache in 0.00012 seconds. -->    <aside id="middle-widget-area" class="widget-area horizontal-widget-area clearfix" aria-label="Middle Widget Area"><div id="custom_html-4" class="widget_text widget-container widget_custom_html"><div class="widget_text widget-wrapper"><div class="textwidget custom-html-widget"><style type="text/css">
	@media screen and (min-width: 600px) {
  .home #middle-widget-area .inline-widget-inner-wrapper {
    padding: 0 !important;
  }
	}
	
	#custom_html-4 {
		padding: 30px 10px 5px;
	}
	
	@media screen and (min-width: 1220px) {
		#custom_html-4 .textwidget {
			max-width: 1200px;
			margin-inline: auto;
		}
	}
	
	#custom_html-4 p {
		margin-bottom: 0;
	}
</style>

<p class="has-medium-font-size">
	MIT Career Advising and Professional Development (CAPD) is MIT’s hub for career advising, distinguished fellowships advising, prehealth advising, and professional development support. We work across the Institute to connect students, postdocs, and alumni with resources and support that empower them to build a rewarding career that makes a difference in the world.
</p></div></div></div></aside><main id="main" class="clearfix">

    <div id="container">
        <div id="content">
            
        <div id="post-6" class="clearfix post-6 page type-page status-publish hentry has_thumbnail">
                                    
            <div class="entry-content">
                
<!--

The front page content is managed through a custom widget so that it can appear before the news feed and sidebar. Contact uConnect support to update the text!

-->
                            </div><!-- .entry-content -->
        </div><!-- #post-## -->

        <div role="region" aria-labelledby="section-title__68546c9fe7213" class="post-feed">        <div class="filter-nav collapsed has_shared_tags post" role="region" aria-label="Filter options" aria-live="polite">
            <a href="#posting-wrapper" class="skip-link screen-reader-text"
               title="Skip filter options to posts">Skip filter options to posts</a>

            <div
                    class="nav clearfix ">
                <div class="actions">
                    <div class="hide-on-small-only">
                                                    <h2 class="button action ">
                                <a href="https://capd.mit.edu/blog/">Latest News</a>
                                                            </h2>
                        
                                                    <span class="refine-filter action" aria-hidden="true" aria-controls="filter-box-1">
                                Filter                            </span>
                        
                        <a href="https://capd.mit.edu/blog/"
                           class="button secondary reset-filters action">Clear</a>
                    </div>
                                            <div class="hide-on-med-and-up">
                            <span class="action">Filter:</span>
                        </div>
                                    </div>
                <fieldset class="selected-tags">
                    <legend class="screen-reader-text selected-tags-legend">Filters</legend>
                    <button class="default tag-label more-tags" aria-controls="filter-box-1" style="display: none;">
                        <span>See More Filters</span>
                    </button>
                </fieldset>
            </div>
            <div
                class="all-tags"
                style="display: none;"
                role="group"
                id="filter-box-1"
                aria-labelledby="group-label-1"
                aria-expanded="false"
            >
                <span id="group-label-1" class="skip-link screen-reader-text">Filter Controls</span>
                <form action="https://capd.mit.edu/blog/" method="GET">
                    <div class="tags-wrapper">
                        <fieldset><input type="checkbox" value="about-capd-campus-blogs" id="tag_about-capd-campus-blogs_id_1" name="ctag[]" class="tag-input"  /><label for="tag_about-capd-campus-blogs_id_1" class="tag-label">About CAPD Campus Blogs</label> <input type="checkbox" value="fcf-sponsor-spotlights" id="tag_fcf-sponsor-spotlights_id_1" name="ctag[]" class="tag-input"  /><label for="tag_fcf-sponsor-spotlights_id_1" class="tag-label">FCF Sponsor Spotlights</label> <input type="checkbox" value="fellowship-recipient-profiles" id="tag_fellowship-recipient-profiles_id_1" name="ctag[]" class="tag-input"  /><label for="tag_fellowship-recipient-profiles_id_1" class="tag-label">Fellowship Recipient Profiles</label> <input type="checkbox" value="guest-blog" id="tag_guest-blog_id_1" name="ctag[]" class="tag-input"  /><label for="tag_guest-blog_id_1" class="tag-label">Guest Blog</label> <input type="checkbox" value="news-advice" id="tag_news-advice_id_1" name="ctag[]" class="tag-input"  /><label for="tag_news-advice_id_1" class="tag-label">News &amp; Advice</label> <input type="checkbox" value="spilling-the-tea" id="tag_spilling-the-tea_id_1" name="ctag[]" class="tag-input"  /><label for="tag_spilling-the-tea_id_1" class="tag-label">Spilling the Tea</label> <input type="checkbox" value="success-stories" id="tag_success-stories_id_1" name="ctag[]" class="tag-input"  /><label for="tag_success-stories_id_1" class="tag-label">Student &amp; Postdoc Success Stories</label> <input type="checkbox" value="student-success-stories" id="tag_student-success-stories_id_1" name="ctag[]" class="tag-input"  /><label for="tag_student-success-stories_id_1" class="tag-label">student-success-stories</label> </fieldset><fieldset><legend class="filter-nav-legend">People:</legend><input type="checkbox" value="alumni" id="stag_alumni_id_1" name="stag[]" class="tag-input"  /><label for="stag_alumni_id_1" class="tag-label">Alumni</label> <input type="checkbox" value="employer" id="stag_employer_id_1" name="stag[]" class="tag-input"  /><label for="stag_employer_id_1" class="tag-label">Employers</label> <input type="checkbox" value="staff" id="stag_staff_id_1" name="stag[]" class="tag-input"  /><label for="stag_staff_id_1" class="tag-label">Faculty/Staff</label> <input type="checkbox" value="family-supporters" id="stag_family-supporters_id_1" name="stag[]" class="tag-input"  /><label for="stag_family-supporters_id_1" class="tag-label">Family/Supporters</label> <input type="checkbox" value="masters-students" id="stag_masters-students_id_1" name="stag[]" class="tag-input"  /><label for="stag_masters-students_id_1" class="tag-label">Master's Students</label> <input type="checkbox" value="phd-students" id="stag_phd-students_id_1" name="stag[]" class="tag-input"  /><label for="stag_phd-students_id_1" class="tag-label">PhD Students</label> <input type="checkbox" value="postdocs" id="stag_postdocs_id_1" name="stag[]" class="tag-input"  /><label for="stag_postdocs_id_1" class="tag-label">Postdocs</label> <input type="checkbox" value="people" id="stag_people_id_1" name="stag[]" class="tag-input"  /><label for="stag_people_id_1" class="tag-label">Pre-Law List Non-Undergrads</label> <input type="checkbox" value="student" id="stag_student_id_1" name="stag[]" class="tag-input"  /><label for="stag_student_id_1" class="tag-label">Undergraduate Students</label> </fieldset><fieldset><legend class="filter-nav-legend">Affinity / Identity:</legend><input type="checkbox" value="daca-undocumented" id="stag_daca-undocumented_id_1" name="stag[]" class="tag-input"  /><label for="stag_daca-undocumented_id_1" class="tag-label">DACA/Undocumented</label> <input type="checkbox" value="first-generation-low-income" id="stag_first-generation-low-income_id_1" name="stag[]" class="tag-input"  /><label for="stag_first-generation-low-income_id_1" class="tag-label">First Generation, Low Income</label> <input type="checkbox" value="international-students" id="stag_international-students_id_1" name="stag[]" class="tag-input"  /><label for="stag_international-students_id_1" class="tag-label">International Students</label> <input type="checkbox" value="lbgtq" id="stag_lbgtq_id_1" name="stag[]" class="tag-input"  /><label for="stag_lbgtq_id_1" class="tag-label">LBGTQ+</label> <input type="checkbox" value="students-of-color" id="stag_students-of-color_id_1" name="stag[]" class="tag-input"  /><label for="stag_students-of-color_id_1" class="tag-label">Students of Color</label> <input type="checkbox" value="students-with-disabilities" id="stag_students-with-disabilities_id_1" name="stag[]" class="tag-input"  /><label for="stag_students-with-disabilities_id_1" class="tag-label">Students with disabilities</label> <input type="checkbox" value="veterans" id="stag_veterans_id_1" name="stag[]" class="tag-input"  /><label for="stag_veterans_id_1" class="tag-label">Veterans</label> <input type="checkbox" value="women" id="stag_women_id_1" name="stag[]" class="tag-input"  /><label for="stag_women_id_1" class="tag-label">Womxn</label> </fieldset>                    </div>
                                        <div class="actions actions-button-bar">
                        <button type="button" class="collapse secondary" aria-controls="filter-box-1">Close</button>
                        <a href="https://capd.mit.edu/blog/"
                            class="button secondary reset-filters action">Clear Filters</a>
                        <input type="submit" value="Apply Filters & Reload" class="button" disabled="disabled">
                    </div>
                                        <span class="screen-reader-text"><input type="submit" value="Submit"></span>
                </form>
            </div>
        </div>
        <script type="text/javascript">setup_filter_nav();</script>
        <span id="filter-skipper-1"></span>
        



    <div class="posting-wrapper" id="posting-wrapper">
        
            <div id="post-286595" class="entry clearfix post post-286595 type-post status-publish hentry has_thumbnail">
                                                    <div class="entry-title-wrapper"><h3 class="entry-title"><a href="https://capd.mit.edu/blog/2025/06/11/johnson-johnsons-access-ability-lime-scholarship/"
                                                                                rel="bookmark">Johnson &amp; Johnson&#8217;s Access-Ability Lime Scholarship</a>
                        </h3><div class="post-menu"><button class="js-post-menu button-link share-option-button" aria-label="Share Options for Johnson &amp; Johnson&#8217;s Access-Ability Lime Scholarship" aria-expanded="false" aria-controls="post_sharing_panel_286595"><span class="icon-chevron-down" aria-hidden="true"></span></button><ul id="post_sharing_panel_286595"><li class="post-menu-sharing-row">Share This: <span class="social-sharing"><a rel="noopener" class="share" href="https://www.facebook.com/sharer/sharer.php?u=https%3A%2F%2Fcapd.mit.edu%2Fblog%2F2025%2F06%2F11%2Fjohnson-johnsons-access-ability-lime-scholarship%2F" title="Click to share on Facebook" target="_blank" rel="noopener"><span class="icon-fa-facebook" aria-hidden="true"></span><span class="screen-reader-text">Share Johnson &amp; Johnson&#8217;s Access-Ability Lime Scholarship on Facebook</span></a><a rel="noopener" class="share" href="http://www.linkedin.com/cws/share?token&isFramed=false&url=https%3A%2F%2Fcapd.mit.edu%2Fblog%2F2025%2F06%2F11%2Fjohnson-johnsons-access-ability-lime-scholarship%2F" title="Click to share on LinkedIn" target="_blank" rel="noopener"><span class="icon-linkedin-u" aria-hidden="true"></span><span class="screen-reader-text">Share Johnson &amp; Johnson&#8217;s Access-Ability Lime Scholarship on LinkedIn</span></a><a rel="noopener" class="share" href="https://twitter.com/intent/tweet?text=Johnson+%26+Johnson%27s+Access-Ability+Lime+Scholarship&url=https%3A%2F%2Fcapd.mit.edu%2F%3Fp%3D286595" title="Click to share on Twitter" target="_blank" rel="noopener"><span class="icon-fa-x-twitter" aria-hidden="true"></span><span class="screen-reader-text">Share Johnson &amp; Johnson&#8217;s Access-Ability Lime Scholarship on X</span></a></span></li><li data-action="clipboard" data-clipboard-text="https://capd.mit.edu/blog/2025/06/11/johnson-johnsons-access-ability-lime-scholarship/" ><button class="button-link" aria-label="Copy link to Johnson &amp; Johnson&#8217;s Access-Ability Lime Scholarship">Copy Link</button></li></ul></div></div>

                    <div class="entry-meta">
                                                <span class="entry-date">Published on June 11, 2025</span>                    </div><!-- .entry-meta -->

                                            <div class="entry-summary entry-content">
                            <a href="https://capd.mit.edu/blog/2025/06/11/johnson-johnsons-access-ability-lime-scholarship/" tabindex="-1" aria-hidden="true" class="post_thumbnail thumb-size-cover"><div class="thumb-inner"><img decoding="async" src="https://cdn.uconnectlabs.com/wp-content/uploads/sites/123/2024/03/LimeConnectLogo-BlogFeaturedImageSize.png?v=190681" alt="Logo: LimeConnect" /></div></a><p>Designed for current college students with disabilities who are pursuing a Health Care or STEM degree at a four-year university or college in the U.S. 2025 Applications will open on August 1st.</p>
<p>This scholarship is delivered in collaboration with our  &hellip;</p>
                        </div><!-- .entry-summary -->
                    
                    <div class="entry-utility">
                        
    <div class="author has-author-card">
            By <span tabindex=0 class="author-name uc_admin_staff">Kendel Jester</span>                        <button class="js-author-card button-link author-info-button" aria-expanded="false"
                    aria-controls="post_author_326228785"><span class="screen-reader-text">Author info</span></button>
            <div class="hovercard" id="post_author_326228785">
                <div class="inner-wrapper clearfix">
                                                        <span class="hovercard-author-name">Kendel Jester</span>
                                                                                                <span class="author-title">Assistant Director, Early Career Engagement</span>                                                                                                        <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1450201/avatar-50x50-center-top.jpg?v=1637173551' class='avatar photo icon-user' height='50' width='50'/>                    <div class="links-wrapper">
                                                        <a href="mailto:kj&#101;s&#116;&#101;&#114;&#64;&#109;&#105;&#116;&#46;&#101;du" aria-label="Email Kendel Jester" class="sendemail" target="_blank" rel="noopener"><span class="icon icon-mail" aria-hidden="true"></span>Send Email</a><br/><div class="social-links"><a rel="nofollow noopener" href="https://www.linkedin.com/in/kendeljester/" aria-label="linkedin profile of Kendel Jester" target="_blank"><span class="icon-linkedin-u" aria-hidden="true"></span><span class="screen-reader-text">linkedin</span></a></div>                    </div>
                </div>
            </div>
                    </div>
         <a href="https://capd.mit.edu/blog/2025/06/11/johnson-johnsons-access-ability-lime-scholarship/" class="read_more button" aria-label="Read more Johnson &amp; Johnson&#8217;s Access-Ability Lime Scholarship">Read more <span class="meta-nav" aria-hidden="true">»</span></a>                    </div><!-- .entry-utility -->
                                                </div><!-- #post-## -->

        
            <div id="post-286301" class="entry clearfix post post-286301 type-post status-publish hentry has_thumbnail">
                                                    <div class="entry-title-wrapper"><h3 class="entry-title"><a href="https://capd.mit.edu/blog/2025/06/09/get-a-foot-in-the-biotech-vc-door-an-opportunity-for-ai-ml-grad-students/"
                                                                                rel="bookmark">Opportunity for AI/ML Grad Students Interested in Biotech VC</a>
                        </h3><div class="post-menu"><button class="js-post-menu button-link share-option-button" aria-label="Share Options for Opportunity for AI/ML Grad Students Interested in Biotech VC" aria-expanded="false" aria-controls="post_sharing_panel_286301"><span class="icon-chevron-down" aria-hidden="true"></span></button><ul id="post_sharing_panel_286301"><li class="post-menu-sharing-row">Share This: <span class="social-sharing"><a rel="noopener" class="share" href="https://www.facebook.com/sharer/sharer.php?u=https%3A%2F%2Fcapd.mit.edu%2Fblog%2F2025%2F06%2F09%2Fget-a-foot-in-the-biotech-vc-door-an-opportunity-for-ai-ml-grad-students%2F" title="Click to share on Facebook" target="_blank" rel="noopener"><span class="icon-fa-facebook" aria-hidden="true"></span><span class="screen-reader-text">Share Opportunity for AI/ML Grad Students Interested in Biotech VC on Facebook</span></a><a rel="noopener" class="share" href="http://www.linkedin.com/cws/share?token&isFramed=false&url=https%3A%2F%2Fcapd.mit.edu%2Fblog%2F2025%2F06%2F09%2Fget-a-foot-in-the-biotech-vc-door-an-opportunity-for-ai-ml-grad-students%2F" title="Click to share on LinkedIn" target="_blank" rel="noopener"><span class="icon-linkedin-u" aria-hidden="true"></span><span class="screen-reader-text">Share Opportunity for AI/ML Grad Students Interested in Biotech VC on LinkedIn</span></a><a rel="noopener" class="share" href="https://twitter.com/intent/tweet?text=Opportunity+for+AI%2FML+Grad+Students+Interested+in+Biotech+VC&url=https%3A%2F%2Fcapd.mit.edu%2F%3Fp%3D286301" title="Click to share on Twitter" target="_blank" rel="noopener"><span class="icon-fa-x-twitter" aria-hidden="true"></span><span class="screen-reader-text">Share Opportunity for AI/ML Grad Students Interested in Biotech VC on X</span></a></span></li><li data-action="clipboard" data-clipboard-text="https://capd.mit.edu/blog/2025/06/09/get-a-foot-in-the-biotech-vc-door-an-opportunity-for-ai-ml-grad-students/" ><button class="button-link" aria-label="Copy link to Opportunity for AI/ML Grad Students Interested in Biotech VC">Copy Link</button></li></ul></div></div>

                    <div class="entry-meta">
                                                <span class="entry-date">Published on June 9, 2025</span>                    </div><!-- .entry-meta -->

                                            <div class="entry-summary entry-content">
                            <a href="https://capd.mit.edu/blog/2025/06/09/get-a-foot-in-the-biotech-vc-door-an-opportunity-for-ai-ml-grad-students/" tabindex="-1" aria-hidden="true" class="post_thumbnail thumb-size-cover"><div class="thumb-inner"><img decoding="async" src="https://cdn.uconnectlabs.com/wp-content/uploads/sites/123/2022/05/Grad-PD-Blog-Image-Opportunity-contest-480x320-center-middle.png?v=69102" alt="The word opportunity and an image of a trophy" /></div></a><p>Are you a graduate student working in the fields of artificial intelligence and/or machine learning? Do you have a background in biology or the life sciences? You might be a fit for the following opportunity! </p>
<p> <a href="https://5amventures.com/">5AM Ventures</a> is an early-stage  &hellip;</p>
                        </div><!-- .entry-summary -->
                    
                    <div class="entry-utility">
                        
    <div class="author has-author-card">
            By <span tabindex=0 class="author-name uc_admin_staff">Tara Thakurta</span>                        <button class="js-author-card button-link author-info-button" aria-expanded="false"
                    aria-controls="post_author_1902280547"><span class="screen-reader-text">Author info</span></button>
            <div class="hovercard" id="post_author_1902280547">
                <div class="inner-wrapper clearfix">
                                                        <span class="hovercard-author-name">Tara Thakurta</span>
                                                                                                <span class="author-title">Graduate Community Fellow</span>                                                                                                        <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/2163807/profile-picture-1695749687-50x50-center-top.jpg?v=1695749687' class='avatar photo icon-user' height='50' width='50'/>                    <div class="links-wrapper">
                                                                            </div>
                </div>
            </div>
                    </div>
         <a href="https://capd.mit.edu/blog/2025/06/09/get-a-foot-in-the-biotech-vc-door-an-opportunity-for-ai-ml-grad-students/" class="read_more button" aria-label="Read more Opportunity for AI/ML Grad Students Interested in Biotech VC">Read more <span class="meta-nav" aria-hidden="true">»</span></a>                    </div><!-- .entry-utility -->
                                                </div><!-- #post-## -->

        
            <div id="post-285414" class="entry clearfix post post-285414 type-post status-publish hentry has_thumbnail">
                                                    <div class="entry-title-wrapper"><h3 class="entry-title"><a href="https://capd.mit.edu/blog/2025/06/04/mit-pre-law-society/"
                                                                                rel="bookmark">MIT Pre-Law Society</a>
                        </h3><div class="post-menu"><button class="js-post-menu button-link share-option-button" aria-label="Share Options for MIT Pre-Law Society" aria-expanded="false" aria-controls="post_sharing_panel_285414"><span class="icon-chevron-down" aria-hidden="true"></span></button><ul id="post_sharing_panel_285414"><li class="post-menu-sharing-row">Share This: <span class="social-sharing"><a rel="noopener" class="share" href="https://www.facebook.com/sharer/sharer.php?u=https%3A%2F%2Fcapd.mit.edu%2Fblog%2F2025%2F06%2F04%2Fmit-pre-law-society%2F" title="Click to share on Facebook" target="_blank" rel="noopener"><span class="icon-fa-facebook" aria-hidden="true"></span><span class="screen-reader-text">Share MIT Pre-Law Society on Facebook</span></a><a rel="noopener" class="share" href="http://www.linkedin.com/cws/share?token&isFramed=false&url=https%3A%2F%2Fcapd.mit.edu%2Fblog%2F2025%2F06%2F04%2Fmit-pre-law-society%2F" title="Click to share on LinkedIn" target="_blank" rel="noopener"><span class="icon-linkedin-u" aria-hidden="true"></span><span class="screen-reader-text">Share MIT Pre-Law Society on LinkedIn</span></a><a rel="noopener" class="share" href="https://twitter.com/intent/tweet?text=MIT+Pre-Law+Society&url=https%3A%2F%2Fcapd.mit.edu%2F%3Fp%3D285414" title="Click to share on Twitter" target="_blank" rel="noopener"><span class="icon-fa-x-twitter" aria-hidden="true"></span><span class="screen-reader-text">Share MIT Pre-Law Society on X</span></a></span></li><li data-action="clipboard" data-clipboard-text="https://capd.mit.edu/blog/2025/06/04/mit-pre-law-society/" ><button class="button-link" aria-label="Copy link to MIT Pre-Law Society">Copy Link</button></li></ul></div></div>

                    <div class="entry-meta">
                                                <span class="entry-date">Published on June 4, 2025</span>                    </div><!-- .entry-meta -->

                                            <div class="entry-summary entry-content">
                            <a href="https://capd.mit.edu/blog/2025/06/04/mit-pre-law-society/" tabindex="-1" aria-hidden="true" class="post_thumbnail thumb-size-contain"><div class="thumb-inner"><img decoding="async" src="https://cdn.uconnectlabs.com/wp-content/uploads/sites/123/2025/06/Official-MIT-Pre-Law-Society-Logo-320x320.jpg?v=285415" alt="MIT Pre-Law Society logo with a picture of the MIT dome" /></div></a><p>Are you interested in connecting with other MIT Pre-Law students? Whether you&#8217;re just curious about law school or committed to applying, <a href="https://docs.google.com/forms/d/e/1FAIpQLSfJRsUudBVh0bID1eVqsV2_toiVjYz3Noo59gWSxHS1r9K76Q/viewform">sign up for the mailing list</a> so you can join this community of MIT students interested in the law.  &hellip;</p>
                        </div><!-- .entry-summary -->
                    
                    <div class="entry-utility">
                        
    <div class="author has-author-card">
            By <span tabindex=0 class="author-name uc_admin_staff">Tianna Ransom</span>                        <button class="js-author-card button-link author-info-button" aria-expanded="false"
                    aria-controls="post_author_327194240"><span class="screen-reader-text">Author info</span></button>
            <div class="hovercard" id="post_author_327194240">
                <div class="inner-wrapper clearfix">
                                                        <span class="hovercard-author-name">Tianna Ransom</span>
                                                                                                <span class="author-title">Assistant Director, Career Exploration</span>                                                                                                        <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1356249/avatar-50x50-center-top.jpg?v=1642195439' class='avatar photo icon-user' height='50' width='50'/>                    <div class="links-wrapper">
                                                        <div class="social-links"><a rel="nofollow noopener" href="https://www.linkedin.com/in/tiannaransom/" aria-label="linkedin profile of Tianna Ransom" target="_blank"><span class="icon-linkedin-u" aria-hidden="true"></span><span class="screen-reader-text">linkedin</span></a></div>                    </div>
                </div>
            </div>
                    </div>
         <a href="https://capd.mit.edu/blog/2025/06/04/mit-pre-law-society/" class="read_more button" aria-label="Read more MIT Pre-Law Society">Read more <span class="meta-nav" aria-hidden="true">»</span></a>                    </div><!-- .entry-utility -->
                                                </div><!-- #post-## -->

        
            <div id="post-284619" class="entry clearfix post post-284619 type-post status-publish hentry has_thumbnail">
                                                    <div class="entry-title-wrapper"><h3 class="entry-title"><a href="https://capd.mit.edu/blog/2025/06/01/keep-your-communication-skills-sharp-this-summer-with-programs-at-the-mit-wcc/"
                                                                                rel="bookmark">Keep your communication skills sharp this summer with programs at the MIT WCC!</a>
                        </h3><div class="post-menu"><button class="js-post-menu button-link share-option-button" aria-label="Share Options for Keep your communication skills sharp this summer with programs at the MIT WCC!" aria-expanded="false" aria-controls="post_sharing_panel_284619"><span class="icon-chevron-down" aria-hidden="true"></span></button><ul id="post_sharing_panel_284619"><li class="post-menu-sharing-row">Share This: <span class="social-sharing"><a rel="noopener" class="share" href="https://www.facebook.com/sharer/sharer.php?u=https%3A%2F%2Fcapd.mit.edu%2Fblog%2F2025%2F06%2F01%2Fkeep-your-communication-skills-sharp-this-summer-with-programs-at-the-mit-wcc%2F" title="Click to share on Facebook" target="_blank" rel="noopener"><span class="icon-fa-facebook" aria-hidden="true"></span><span class="screen-reader-text">Share Keep your communication skills sharp this summer with programs at the MIT WCC! on Facebook</span></a><a rel="noopener" class="share" href="http://www.linkedin.com/cws/share?token&isFramed=false&url=https%3A%2F%2Fcapd.mit.edu%2Fblog%2F2025%2F06%2F01%2Fkeep-your-communication-skills-sharp-this-summer-with-programs-at-the-mit-wcc%2F" title="Click to share on LinkedIn" target="_blank" rel="noopener"><span class="icon-linkedin-u" aria-hidden="true"></span><span class="screen-reader-text">Share Keep your communication skills sharp this summer with programs at the MIT WCC! on LinkedIn</span></a><a rel="noopener" class="share" href="https://twitter.com/intent/tweet?text=Keep+your+communication+skills+sharp+this+summer+with+programs+at+the+MIT+WCC%21&url=https%3A%2F%2Fcapd.mit.edu%2F%3Fp%3D284619" title="Click to share on Twitter" target="_blank" rel="noopener"><span class="icon-fa-x-twitter" aria-hidden="true"></span><span class="screen-reader-text">Share Keep your communication skills sharp this summer with programs at the MIT WCC! on X</span></a></span></li><li data-action="clipboard" data-clipboard-text="https://capd.mit.edu/blog/2025/06/01/keep-your-communication-skills-sharp-this-summer-with-programs-at-the-mit-wcc/" ><button class="button-link" aria-label="Copy link to Keep your communication skills sharp this summer with programs at the MIT WCC!">Copy Link</button></li></ul></div></div>

                    <div class="entry-meta">
                                                <span class="entry-date">Published on June 1, 2025</span>                    </div><!-- .entry-meta -->

                                            <div class="entry-summary entry-content">
                            <a href="https://capd.mit.edu/blog/2025/06/01/keep-your-communication-skills-sharp-this-summer-with-programs-at-the-mit-wcc/" tabindex="-1" aria-hidden="true" class="post_thumbnail thumb-size-cover"><div class="thumb-inner"><img decoding="async" src="https://cdn.uconnectlabs.com/wp-content/uploads/sites/123/2021/10/write_1280p_0-480x320.jpg?v=25931" alt="" /></div></a><p>The Writing and Communications Center (WCC) has several sessions ongoing throughout the summer</p>
<p><strong>Book Your Next WCC Consultation or Coaching Session</strong></p>
<p><strong>When:</strong>&nbsp;June 2nd-July 17th, Monday-Friday, 9-5pm</p>
<p><strong>Where:</strong>&nbsp;by Zoom or&nbsp;E18-233, 50 Ames St.&nbsp;</p>
<p><strong>Registration:</strong>&nbsp;<a href="https://mit.mywconline.com/schedule/calendar?scheduleid=sc6837c6369eda1" target="_blank" rel="noreferrer noopener">https://mit.mywconline.com/</a></p>
<p>Hurry to schedule  &hellip;</p>
                        </div><!-- .entry-summary -->
                    
                    <div class="entry-utility">
                        
    <div class="author has-author-card">
            By <span tabindex=0 class="author-name uc_admin_staff">Tara Thakurta</span>                        <button class="js-author-card button-link author-info-button" aria-expanded="false"
                    aria-controls="post_author_630997545"><span class="screen-reader-text">Author info</span></button>
            <div class="hovercard" id="post_author_630997545">
                <div class="inner-wrapper clearfix">
                                                        <span class="hovercard-author-name">Tara Thakurta</span>
                                                                                                <span class="author-title">Graduate Community Fellow</span>                                                                                                        <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/2163807/profile-picture-1695749687-50x50-center-top.jpg?v=1695749687' class='avatar photo icon-user' height='50' width='50'/>                    <div class="links-wrapper">
                                                                            </div>
                </div>
            </div>
                    </div>
         <a href="https://capd.mit.edu/blog/2025/06/01/keep-your-communication-skills-sharp-this-summer-with-programs-at-the-mit-wcc/" class="read_more button" aria-label="Read more Keep your communication skills sharp this summer with programs at the MIT WCC!">Read more <span class="meta-nav" aria-hidden="true">»</span></a>                    </div><!-- .entry-utility -->
                                                </div><!-- #post-## -->

        
            <div id="post-284617" class="entry clearfix post post-284617 type-post status-publish hentry has_thumbnail">
                                                    <div class="entry-title-wrapper"><h3 class="entry-title"><a href="https://capd.mit.edu/blog/2025/06/01/public-speaking-certificate/"
                                                                                rel="bookmark">Public Speaking Certificate</a>
                        </h3><div class="post-menu"><button class="js-post-menu button-link share-option-button" aria-label="Share Options for Public Speaking Certificate" aria-expanded="false" aria-controls="post_sharing_panel_284617"><span class="icon-chevron-down" aria-hidden="true"></span></button><ul id="post_sharing_panel_284617"><li class="post-menu-sharing-row">Share This: <span class="social-sharing"><a rel="noopener" class="share" href="https://www.facebook.com/sharer/sharer.php?u=https%3A%2F%2Fcapd.mit.edu%2Fblog%2F2025%2F06%2F01%2Fpublic-speaking-certificate%2F" title="Click to share on Facebook" target="_blank" rel="noopener"><span class="icon-fa-facebook" aria-hidden="true"></span><span class="screen-reader-text">Share Public Speaking Certificate on Facebook</span></a><a rel="noopener" class="share" href="http://www.linkedin.com/cws/share?token&isFramed=false&url=https%3A%2F%2Fcapd.mit.edu%2Fblog%2F2025%2F06%2F01%2Fpublic-speaking-certificate%2F" title="Click to share on LinkedIn" target="_blank" rel="noopener"><span class="icon-linkedin-u" aria-hidden="true"></span><span class="screen-reader-text">Share Public Speaking Certificate on LinkedIn</span></a><a rel="noopener" class="share" href="https://twitter.com/intent/tweet?text=Public+Speaking+Certificate&url=https%3A%2F%2Fcapd.mit.edu%2F%3Fp%3D284617" title="Click to share on Twitter" target="_blank" rel="noopener"><span class="icon-fa-x-twitter" aria-hidden="true"></span><span class="screen-reader-text">Share Public Speaking Certificate on X</span></a></span></li><li data-action="clipboard" data-clipboard-text="https://capd.mit.edu/blog/2025/06/01/public-speaking-certificate/" ><button class="button-link" aria-label="Copy link to Public Speaking Certificate">Copy Link</button></li></ul></div></div>

                    <div class="entry-meta">
                                                <span class="entry-date">Published on June 1, 2025</span>                    </div><!-- .entry-meta -->

                                            <div class="entry-summary entry-content">
                            <a href="https://capd.mit.edu/blog/2025/06/01/public-speaking-certificate/" tabindex="-1" aria-hidden="true" class="post_thumbnail thumb-size-cover"><div class="thumb-inner"><img decoding="async" src="https://cdn.uconnectlabs.com/wp-content/uploads/sites/123/2022/05/Grad-PD-Blog-Image-Special-Program-2-1-480x320-center-middle.png?v=69113" alt="Image of a lecture space, a microphone, and the words "special program?"" /></div></a><p>The Writing &amp; Communication center is piloting a brand new Public Speaking Certificate program this June!&nbsp;This free program offers graduate students a structured pathway to becoming effective and versatile speakers across diverse settings.</p>
<p>Through a series of interactive workshops, personalized  &hellip;</p>
                        </div><!-- .entry-summary -->
                    
                    <div class="entry-utility">
                        
    <div class="author has-author-card">
            By <span tabindex=0 class="author-name uc_admin_staff">Tara Thakurta</span>                        <button class="js-author-card button-link author-info-button" aria-expanded="false"
                    aria-controls="post_author_1436109398"><span class="screen-reader-text">Author info</span></button>
            <div class="hovercard" id="post_author_1436109398">
                <div class="inner-wrapper clearfix">
                                                        <span class="hovercard-author-name">Tara Thakurta</span>
                                                                                                <span class="author-title">Graduate Community Fellow</span>                                                                                                        <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/2163807/profile-picture-1695749687-50x50-center-top.jpg?v=1695749687' class='avatar photo icon-user' height='50' width='50'/>                    <div class="links-wrapper">
                                                                            </div>
                </div>
            </div>
                    </div>
         <a href="https://capd.mit.edu/blog/2025/06/01/public-speaking-certificate/" class="read_more button" aria-label="Read more Public Speaking Certificate">Read more <span class="meta-nav" aria-hidden="true">»</span></a>                    </div><!-- .entry-utility -->
                                                </div><!-- #post-## -->

        
            <div id="post-284613" class="entry clearfix post post-284613 type-post status-publish hentry has_thumbnail">
                                                    <div class="entry-title-wrapper"><h3 class="entry-title"><a href="https://capd.mit.edu/blog/2025/06/01/building-a-network-digital-and-in-person-strategies-for-students-recent-graduates-and-postdocs/"
                                                                                rel="bookmark">Building a Network: Digital and In-Person Strategies for Students, Recent Graduates, and Postdocs</a>
                        </h3><div class="post-menu"><button class="js-post-menu button-link share-option-button" aria-label="Share Options for Building a Network: Digital and In-Person Strategies for Students, Recent Graduates, and Postdocs" aria-expanded="false" aria-controls="post_sharing_panel_284613"><span class="icon-chevron-down" aria-hidden="true"></span></button><ul id="post_sharing_panel_284613"><li class="post-menu-sharing-row">Share This: <span class="social-sharing"><a rel="noopener" class="share" href="https://www.facebook.com/sharer/sharer.php?u=https%3A%2F%2Fcapd.mit.edu%2Fblog%2F2025%2F06%2F01%2Fbuilding-a-network-digital-and-in-person-strategies-for-students-recent-graduates-and-postdocs%2F" title="Click to share on Facebook" target="_blank" rel="noopener"><span class="icon-fa-facebook" aria-hidden="true"></span><span class="screen-reader-text">Share Building a Network: Digital and In-Person Strategies for Students, Recent Graduates, and Postdocs on Facebook</span></a><a rel="noopener" class="share" href="http://www.linkedin.com/cws/share?token&isFramed=false&url=https%3A%2F%2Fcapd.mit.edu%2Fblog%2F2025%2F06%2F01%2Fbuilding-a-network-digital-and-in-person-strategies-for-students-recent-graduates-and-postdocs%2F" title="Click to share on LinkedIn" target="_blank" rel="noopener"><span class="icon-linkedin-u" aria-hidden="true"></span><span class="screen-reader-text">Share Building a Network: Digital and In-Person Strategies for Students, Recent Graduates, and Postdocs on LinkedIn</span></a><a rel="noopener" class="share" href="https://twitter.com/intent/tweet?text=Building+a+Network%3A+Digital+and+In-Person+Strategies+for+Students%2C+Recent+Graduates%2C+and+Postdocs&url=https%3A%2F%2Fcapd.mit.edu%2F%3Fp%3D284613" title="Click to share on Twitter" target="_blank" rel="noopener"><span class="icon-fa-x-twitter" aria-hidden="true"></span><span class="screen-reader-text">Share Building a Network: Digital and In-Person Strategies for Students, Recent Graduates, and Postdocs on X</span></a></span></li><li data-action="clipboard" data-clipboard-text="https://capd.mit.edu/blog/2025/06/01/building-a-network-digital-and-in-person-strategies-for-students-recent-graduates-and-postdocs/" ><button class="button-link" aria-label="Copy link to Building a Network: Digital and In-Person Strategies for Students, Recent Graduates, and Postdocs">Copy Link</button></li></ul></div></div>

                    <div class="entry-meta">
                                                <span class="entry-date">Published on June 1, 2025</span>                    </div><!-- .entry-meta -->

                                            <div class="entry-summary entry-content">
                            <a href="https://capd.mit.edu/blog/2025/06/01/building-a-network-digital-and-in-person-strategies-for-students-recent-graduates-and-postdocs/" tabindex="-1" aria-hidden="true" class="post_thumbnail thumb-size-cover"><div class="thumb-inner"><img decoding="async" src="https://cdn.uconnectlabs.com/wp-content/uploads/sites/123/2022/01/FeaturedImage-Resource-MITresource-GradPD-480x320-center-middle.png?v=185647" alt="Under a photo of a building on campus with iconic columns, the text reads, "M.I.T. resource."" /></div></a><p><em>The following article is from Trevor Cambron&#8217;s <a href="https://mitcommlab.mit.edu/cee/commkit/building-a-network/">post</a> for the MIT Communications Lab</em></p>
<p>Networking. For some it is a loaded word that induces a visceral sense of dread, while for others it is a natural part of the workday. In  &hellip;</p>
                        </div><!-- .entry-summary -->
                    
                    <div class="entry-utility">
                        
    <div class="author has-author-card">
            By <span tabindex=0 class="author-name uc_admin_staff">Tara Thakurta</span>                        <button class="js-author-card button-link author-info-button" aria-expanded="false"
                    aria-controls="post_author_1672176302"><span class="screen-reader-text">Author info</span></button>
            <div class="hovercard" id="post_author_1672176302">
                <div class="inner-wrapper clearfix">
                                                        <span class="hovercard-author-name">Tara Thakurta</span>
                                                                                                <span class="author-title">Graduate Community Fellow</span>                                                                                                        <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/2163807/profile-picture-1695749687-50x50-center-top.jpg?v=1695749687' class='avatar photo icon-user' height='50' width='50'/>                    <div class="links-wrapper">
                                                                            </div>
                </div>
            </div>
                    </div>
         <a href="https://capd.mit.edu/blog/2025/06/01/building-a-network-digital-and-in-person-strategies-for-students-recent-graduates-and-postdocs/" class="read_more button" aria-label="Read more Building a Network: Digital and In-Person Strategies for Students, Recent Graduates, and Postdocs">Read more <span class="meta-nav" aria-hidden="true">»</span></a>                    </div><!-- .entry-utility -->
                                                </div><!-- #post-## -->

            </div><!-- .posting-wrapper -->
</div><!-- .post-feed --><div class="right-align"><a id="view-all-blogs" href="https://capd.mit.edu/blog/" class="button">View All Blogs</a></div>        </div><!-- #content -->
    </div><!-- #container -->

    
<section aria-label="Sidebar Widget Area"  id="primary" class="widget-area primary-side-widget-area v2-style">
    <h2 class="screen-reader-text">Sidebar</h2>
    <div class="xoxo">
        <div id="uc_search-2" class="widget-container widget-container-inline sidebar widget_uc_search"><div class="widget-wrapper"><h3 class="widget-title">Search Career Advising & Professional Development</h3>        <form role="search" method="get" class="searchform " action="https://capd.mit.edu/search/">
            <div>
                <input type="text" value="" name="s" id="s2">
                <label for="s2">Search Keywords                        <span class="search-icon" role="img" aria-hidden="true"></span>
                    </label>
                                    <style>
                        .topRibbon .widget_uc_search label[for="s2"]:before, #top_ribbon .widget_uc_search label[for="s2"]:before {
                            content: "Search Keywords" !important;
                        }</style>
                                                <input type="submit" value="Search" class="button">
                            </div>
        </form>
        </div></div><div class="inline-widget-wrapper"><div class="inline-widget-inner-wrapper"><div id="eventscalendarwidget-3" class="widget-container widget-container-inline widget-container widget-container-inline-inline sidebar widget_eventscalendarwidget"><div class="widget-wrapper"><h3 class="widget-title"><i class="icon-calendar-2" aria-hidden="true"></i>Upcoming Events</h3>
        <div class="events-list-wrapper">
            <div class="events-list main">
                        <div class="event_item">
            <div class="month" aria-hidden="true">June</div>            <a href="https://capd.mit.edu/events/2025/06/23/seek-employment-together-set-3/">
                                <div class="info">
                    <div class="day_box" aria-hidden="true">
                        <span class="day_box_month">Jun</span>
                        <span class="day_box_date">23</span>
                    </div>
                    <div class="title">
                        <span class="screen-reader-text">Event: </span>
                        <span class="title-content">Seek Employment Together (SET)</span>
                                            </div>
                    <div class="event_date">
                        <span aria-hidden="true">Mon, Jun 23 from 1:30pm - 2:45pm</span>
                        <span class="screen-reader-text">Monday, June 23rd from 1:30pm - 2:45pm</span>
                    </div>
                </div>
            </a>
        </div>
                <div class="event_item">
                        <a href="https://capd.mit.edu/events/2025/06/26/industry-resume-and-cover-letter-bootcamp/">
                                <div class="info">
                    <div class="day_box" aria-hidden="true">
                        <span class="day_box_month">Jun</span>
                        <span class="day_box_date">26</span>
                    </div>
                    <div class="title">
                        <span class="screen-reader-text">Event: </span>
                        <span class="title-content">Industry Resume and Cover Letter Bootcamp</span>
                                            </div>
                    <div class="event_date">
                        <span aria-hidden="true">Thu, Jun 26 from 1pm - 4pm</span>
                        <span class="screen-reader-text">Thursday, June 26th from 1pm - 4pm</span>
                    </div>
                </div>
            </a>
        </div>
                <div class="event_item">
                        <a href="https://capd.mit.edu/events/2025/06/30/seek-employment-together-set-4/">
                                <div class="info">
                    <div class="day_box" aria-hidden="true">
                        <span class="day_box_month">Jun</span>
                        <span class="day_box_date">30</span>
                    </div>
                    <div class="title">
                        <span class="screen-reader-text">Event: </span>
                        <span class="title-content">Seek Employment Together (SET)</span>
                                            </div>
                    <div class="event_date">
                        <span aria-hidden="true">Mon, Jun 30 from 1:30pm - 2:45pm</span>
                        <span class="screen-reader-text">Monday, June 30th from 1:30pm - 2:45pm</span>
                    </div>
                </div>
            </a>
        </div>
                <div class="event_item">
            <div class="month" aria-hidden="true">July</div>            <a href="https://capd.mit.edu/events/2025/07/21/seek-employment-together-set-5/">
                                <div class="info">
                    <div class="day_box" aria-hidden="true">
                        <span class="day_box_month">Jul</span>
                        <span class="day_box_date">21</span>
                    </div>
                    <div class="title">
                        <span class="screen-reader-text">Event: </span>
                        <span class="title-content">Seek Employment Together (SET)</span>
                                            </div>
                    <div class="event_date">
                        <span aria-hidden="true">Mon, Jul 21 at 1:30pm</span>
                        <span class="screen-reader-text">Monday, July 21st at 1:30pm</span>
                    </div>
                </div>
            </a>
        </div>
                <div class="event_item">
                        <a href="https://capd.mit.edu/events/2025/07/28/seek-employment-together-set-6/">
                                <div class="info">
                    <div class="day_box" aria-hidden="true">
                        <span class="day_box_month">Jul</span>
                        <span class="day_box_date">28</span>
                    </div>
                    <div class="title">
                        <span class="screen-reader-text">Event: </span>
                        <span class="title-content">Seek Employment Together (SET)</span>
                                            </div>
                    <div class="event_date">
                        <span aria-hidden="true">Mon, Jul 28 from 1:30pm - 2:45pm</span>
                        <span class="screen-reader-text">Monday, July 28th from 1:30pm - 2:45pm</span>
                    </div>
                </div>
            </a>
        </div>
                    </div>
        </div>
                            <div class="widget-footer">
                <a href="https://capd.mit.edu/events/?ctag%5B%5D=front-page"
                   class="button button-more">View More</a>
            </div>
            </div></div></div></div><!-- .inline-widget-wrapper -->    </div>
    </section>
<section aria-label="Bottom Widget Area" id="bottom-widget-area" class="widget-area horizontal-widget-area vw-center">
            <div class="inline-widget-wrapper"><div class="inline-widget-inner-wrapper"><div id="uc_featured_users-2" class="widget-container widget-container-inline widget_uc_featured_users"><div class="widget-wrapper"><h2 class="widget-title"><i class="icon-users" aria-hidden="true"></i>CAPD Team</h2>        <div class="list clearfix">
            <div class="users-group">                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/943959/avatar-200x200-center-top.jpg?v=1637095959' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Deborah Liverman                                    </span>
                    <a class="button" href="https://capd.mit.edu/operations/#deborah-liverman">Meet Deborah</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1336772/profile-picture-1748446139-200x200-center-top.jpg?v=1748446139' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Lydia Huth                                    </span>
                    <a class="button" href="https://capd.mit.edu/operations/#lydia-huth">Meet Lydia</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/3848169/profile-picture-1749142506-200x200-center-top.jpg?v=1749142506' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Maritza Monvil                                    </span>
                    <a class="button" href="https://capd.mit.edu/operations/#maritza-monvil">Meet Maritza</a>                </div>
                </div><!-- .users-group --><div class="users-group">                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/3454474/profile-picture-1694097972-200x200-center-top.jpg?v=1694097972' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Anna Deng                                    </span>
                    <a class="button" href="https://capd.mit.edu/operations/#anna-deng">Meet Anna</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1356246/profile-picture-1703286519-200x200-center-top.jpg?v=1703286519' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Meredith Pepin                                    </span>
                    <a class="button" href="https://capd.mit.edu/career-advising/#meredith-pepin">Meet Meredith</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1336773/avatar-200x200-center-top.jpg?v=1637157355' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Erik Pavesic                                    </span>
                    <a class="button" href="https://capd.mit.edu/peer-career-advisors/#erik-pavesic">Meet Erik</a>                </div>
                </div><!-- .users-group --><div class="users-group">                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/758778/profile-picture-1668202416-200x200-center-top.jpg?v=1668202416' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Tavi Sookhoo                                    </span>
                    <a class="button" href="https://capd.mit.edu/career-advising/#tavi-sookhoo">Meet Tavi</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1356249/avatar-200x200-center-top.jpg?v=1642195439' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Tianna Ransom                                    </span>
                    <a class="button" href="https://capd.mit.edu/career-advising/#tianna-ransom">Meet Tianna</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1379571/avatar-200x200-center-top.jpg?v=1637358416' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Alexis Boyer                                    </span>
                    <a class="button" href="https://capd.mit.edu/career-advising/#alexis-boyer">Meet Alexis</a>                </div>
                </div><!-- .users-group --><div class="users-group">                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/4980949/profile-picture-1728503722-200x200-center-top.jpg?v=1728503722' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Kamila Madry                                    </span>
                    <a class="button" href="https://capd.mit.edu/career-advising/#kamila-madry">Meet Kamila</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1450201/avatar-200x200-center-top.jpg?v=1637173551' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Kendel Jester                                    </span>
                    <a class="button" href="https://capd.mit.edu/early-career-advisory-board-members/#kendel-jester">Meet Kendel</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/4882439/profile-picture-1748532320-200x200-center-top.jpg?v=1748532320' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Aidan Wright                                    </span>
                    <a class="button" href="https://capd.mit.edu/career-advising/#aidan-wright">Meet Aidan</a>                </div>
                </div><!-- .users-group --><div class="users-group">                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/4944388/profile-picture-1726510326-200x200-center-top.jpg?v=1726510326' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Alexandria Yen                                    </span>
                    <a class="button" href="https://capd.mit.edu/career-advising/#alexandria-yen">Meet Alexandria</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/676619/profile-picture-1705627074-200x200-center-top.jpg?v=1705627074' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Francis Borrego                                    </span>
                    <a class="button" href="https://capd.mit.edu/employer-relations/#francis-borrego">Meet Francis</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/2163281/profile-picture-1717527148-200x200-center-top.jpg?v=1717527148' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Malgorzata Gassan                                    </span>
                    <a class="button" href="https://capd.mit.edu/employer-relations/#malgorzata-gassan">Meet Malgorzata</a>                </div>
                </div><!-- .users-group --><div class="users-group">                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1864451/profile-picture-1651158963-200x200-center-top.jpg?v=1651158963' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Mitchell Moise                                    </span>
                    <a class="button" href="https://capd.mit.edu/employer-relations/#mitchell-moise">Meet Mitchell</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/5480740/profile-picture-1745852169-200x200-center-top.jpg?v=1745852169' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Yanae Zorrilla                                    </span>
                    <a class="button" href="https://capd.mit.edu/operations/#yanae-zorrilla">Meet Yanae</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/3039762/profile-picture-1686674360-200x200-center-top.jpg?v=1686674360' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Elaine Chen                                    </span>
                    <a class="button" href="https://capd.mit.edu/employer-relations/#elaine-chen">Meet Elaine</a>                </div>
                </div><!-- .users-group --><div class="users-group">                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1356241/avatar-200x200-center-top.jpg?v=1623070532' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Jordan Siegel                                    </span>
                    <a class="button" href="https://capd.mit.edu/operations/#jordan-siegel">Meet Jordan</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1356193/avatar-200x200-center-top.jpg?v=**********' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Akunna Rosser                                    </span>
                    <a class="button" href="https://capd.mit.edu/prehealth-advising/#akunna-rosser">Meet Akunna</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1659416/profile-picture-**********-200x200-center-top.jpg?v=**********' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Abie Noor                                    </span>
                    <a class="button" href="https://capd.mit.edu/prehealth-advising/#abie-noor">Meet Abie</a>                </div>
                </div><!-- .users-group --><div class="users-group">                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/2170307/profile-picture-**********-200x200-center-top.jpg?v=**********' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Erica Long                                    </span>
                    <a class="button" href="https://capd.mit.edu/prehealth-advising/#erica-long">Meet Erica</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1356242/avatar-200x200-center-top.jpg?v=**********' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Julia Mongo                                    </span>
                    <a class="button" href="https://capd.mit.edu/pages-distinguished-fellowships/#julia-mongo">Meet Julia</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1356243/avatar-200x200-center-top.jpg?v=**********' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Kim Benard                                    </span>
                    <a class="button" href="https://capd.mit.edu/pages-distinguished-fellowships/#kim-benard">Meet Kim</a>                </div>
                </div><!-- .users-group --><div class="users-group">                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1356240/avatar-200x200-center-top.jpg?v=1644610803' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Ian Murray                                    </span>
                    <a class="button" href="https://capd.mit.edu/pages-distinguished-fellowships/#ian-murray">Meet Ian</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1356236/avatar-200x200-center-top.jpg?v=1639768541' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Amanda Cornwall                                    </span>
                    <a class="button" href="https://capd.mit.edu/professional-development/#amanda-cornwall">Meet Amanda</a>                </div>
                                <div class="user">
                    <img alt='' src='https://cdn.uconnectlabs.com/wp-content/uploads/users/1495218/avatar-200x200-center-top.jpg?v=1638982420' class='avatar photo icon-user' height='200' width='200'/>                    <span class="display_name">
                    Elsie Otero                                    </span>
                    <a class="button" href="https://capd.mit.edu/professional-development/#elsie-otero">Meet Elsie</a>                </div>
                </div><!-- .users-group -->        </div>
        <script type="text/javascript">
            jQuery(function ($) {
                $list = $('#uc_featured_users-2 .list');
                $list.addClass('owl-carousel');
                $list.owlCarousel({
                    nav: true,
                                            items: 1,
                                        navElement: 'div',
                    navText: ['', ''],
                    navClass: ['icon-angle-left nav-prev', 'icon-angle-right nav-next']
                });

            })

        </script>
        </div></div><div id="uc_service_hours-2" class="widget-container widget-container-inline widget_uc_service_hours"><div class="widget-wrapper"><h2 class="widget-title"><i class="icon-clock" aria-hidden="true"></i>Office Hours</h2>
        <table class="service_table">

                    <tr>
                <th class="service_hdr_col" scope="row">
                    <span aria-hidden="true">
                        M                    </span>
                    <span class="screen-reader-text">
                        Monday                    </span>
                </th>
                <td class="service_data_col">
                    10AM-4PM                </td>

            </tr>
                        <tr>
                <th class="service_hdr_col" scope="row">
                    <span aria-hidden="true">
                        T                    </span>
                    <span class="screen-reader-text">
                        Tuesday                    </span>
                </th>
                <td class="service_data_col">
                    10AM-4PM                </td>

            </tr>
                        <tr>
                <th class="service_hdr_col" scope="row">
                    <span aria-hidden="true">
                        W                    </span>
                    <span class="screen-reader-text">
                        Wednesday                    </span>
                </th>
                <td class="service_data_col">
                    10AM-4PM                </td>

            </tr>
                        <tr>
                <th class="service_hdr_col" scope="row">
                    <span aria-hidden="true">
                        TH                    </span>
                    <span class="screen-reader-text">
                        Thursday                    </span>
                </th>
                <td class="service_data_col">
                    10AM-4PM                </td>

            </tr>
                        <tr>
                <th class="service_hdr_col" scope="row">
                    <span aria-hidden="true">
                        F                    </span>
                    <span class="screen-reader-text">
                        Friday                    </span>
                </th>
                <td class="service_data_col">
                    10AM-4PM                </td>

            </tr>
                    </table>
        <div class="description"><p><a href="https://app.joinhandshake.com/appointments/new">Schedule an in-person or virtual appointment</a> to meet with a CAPD team member for personalized assistance.</p>
</div></div></div><div id="uc_contact-2" class="widget-container widget-container-inline widget_uc_contact"><div class="widget-wrapper"><h2 class="widget-title"><i class="icon-pin-map" aria-hidden="true"></i>Contact & Location</h2>                    <div class="row">
                                <div class="col contact_hdr_col">
                    <span class="screen-reader-text">Phone</span>
                    <i class="icon-phone-bud" aria-hidden="true"></i>
                </div>
                                <div class="col contact_data_col">************</div>
            </div>
                            <div class="row">
                                <div class="col contact_hdr_col">
                    <span class="screen-reader-text">Email</span>
                    <i class="icon-mail-bud" aria-hidden="true"></i>
                </div>
                                <div class="col contact_data_col"><a
                        href="mailto:&#99;ap&#100;&#64;m&#105;t&#46;e&#100;u">c&#97;&#112;d&#64;mit&#46;e&#100;u</a>
                </div>
            </div>
                                <div class="row">
                            <div class="col contact_hdr_col">
                    <span class="screen-reader-text">Address</span>
                    <i class="icon-location" aria-hidden="true"></i>
                </div>
                        <div class="col contact_data_col">
                <p><a href="http://whereis.mit.edu/?go=E17">Building E17-294</a><br />
40 Ames Street<br />
Cambridge, MA 02139</p>
            </div>
        </div>
                            <div class="row ">
                                    <div class="col contact_hdr_col">
                        <span class="screen-reader-text">Social</span>
                        <i class="icon-share-bud" aria-hidden="true"></i>
                    </div>
                                <div class="col contact_data_col social_icons">
                    <div class="joinUsWidget-buttons">
                                                    <a href="https://www.instagram.com/mitcareers/" class="instagram" target="_blank" rel="noopener" title="Instagram">
                                <span class="screen-reader-text">Follow Us on Instagram</span>
                                <span class="icon icon-fa-instagram" aria-hidden="true"></span>
                            </a>
                                                    <a href="https://www.linkedin.com/company/35701025/" class="linkedin" target="_blank" rel="noopener" title="LinkedIn">
                                <span class="screen-reader-text">Follow Us on LinkedIn</span>
                                <span class="icon icon-linkedin-u" aria-hidden="true"></span>
                            </a>
                                                    <a href="https://twitter.com/MITCareers" class="twitter" target="_blank" rel="noopener" title="X (Twitter)">
                                <span class="screen-reader-text">Follow Us on X (formerly Twitter)</span>
                                <span class="icon icon-fa-x-twitter" aria-hidden="true"></span>
                            </a>
                                            </div>
                </div>
            </div>
                </div></div></div></div><!-- .inline-widget-wrapper -->    </section>
</main>

        </div><!-- #wrapper -->
    <div id="footer" role="contentinfo">
        <div id="footer-logo" class="footer-content">
    <span id="bc-logo" class="logo">Massachusetts Institute of Technology</span><br/><span id="site-title">Career Advising &amp; Professional Development</span>
    <div class="widget widget_uc_join_us_widget">        <div class="joinUsWidget-buttons">
                                <a href="https://www.instagram.com/mitcareers/" class="instagram" target="_blank" rel="noopener" title="Instagram">
                        <span class="screen-reader-text">Instagram</span>
                        <span class="icon icon-fa-instagram" aria-hidden="true"></span>
                    </a>
                                <a href="https://www.linkedin.com/company/35701025/" class="linkedin" target="_blank" rel="noopener" title="LinkedIn">
                        <span class="screen-reader-text">LinkedIn</span>
                        <span class="icon icon-linkedin-u" aria-hidden="true"></span>
                    </a>
                                <a href="https://twitter.com/MITCareers" class="twitter" target="_blank" rel="noopener" title="X (formerly Twitter)">
                        <span class="screen-reader-text">X (formerly Twitter)</span>
                        <span class="icon icon-fa-x-twitter" aria-hidden="true"></span>
                    </a>
                        </div>
        </div></div>
<div id="footer-links">
            <div id="footer-links-third-col">
            <a href="http://whereis.mit.edu/?go=E17">Building E17-294</a><br/>40 Ames Street<br/>Cambridge, MA 02139<br/>************<br/><a href="mailto:c&#097;pd&#064;mi&#116;&#046;e&#100;&#117;">&#099;&#097;p&#100;&#064;&#109;i&#116;&#046;edu</a>        </div>
        <div id="custom-footer-links">
        <a href="https://capd.mit.edu/mission-vision-and-diversity-statement/">Our mission, vision, and values</a><br>
        <a href="https://capd.mit.edu/staff-hiring/">Join our team</a><br>
        <a href="https://capd.mit.edu/channels/student-leadership-opportunities/">Student leadership opportunities</a><br>
        <a href="https://capd.mit.edu/support-capd/">Support CAPD</a>
    </div>
</div>
        <div id="colophon">
    <div id="colophon_inner" class="row">
        <div class="col s12 l6"><a class="privacy-policy-link" href="https://capd.mit.edu/privacy-policy/">Privacy Policy</a> | <a class="terms-of-service-link" href="https://capd.mit.edu/terms-of-service/">Terms of Service</a> | <a href="http://accessibility.mit.edu/">Accessibility</a>  </div>
        <div id="site-generator" class="col s12 l6">
            <a href="https://www.gouconnect.com/?utm_source=capd.mit.edu&utm_medium=client-footer" target="_blank" rel="noopener" class="uconnect-badge-link"><img src="https://cdn.uconnectlabs.com/wp-content/themes/uConnect/images/powered-by-uconnect-badge.svg" alt="Powered by uConnect" class="uconnect-badge-image" /></a>        </div><!-- #site-generator -->
    </div>
</div><!-- #colophon -->
    </div><!-- #footer -->

<script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/*"},{"not":{"href_matches":["\/wp-*.php","\/wp-admin\/*","\/wp-content\/uploads\/sites\/123\/*","\/wp-content\/*","\/wp-content\/plugins\/*","\/wp-content\/themes\/uConnect_MIT\/*","\/wp-content\/themes\/uConnect\/*","\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
    <div class="full-screen-popup-overlay" style="display: none;"></div>
    <script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/js/uc-combobo.min.js?ver=***********" id="uc-combobo-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/mu-plugins/uconnect/modules/uconnect-user-account/assets/js/combobo-config.js?ver=***********" id="uc-combobo-config-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-includes/js/jquery/ui/core.min.js?ver=1.13.31" id="jquery-ui-core-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-includes/js/jquery/ui/mouse.min.js?ver=1.13.31" id="jquery-ui-mouse-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-includes/js/jquery/ui/resizable.min.js?ver=1.13.31" id="jquery-ui-resizable-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-includes/js/jquery/ui/draggable.min.js?ver=1.13.31" id="jquery-ui-draggable-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-includes/js/jquery/ui/controlgroup.min.js?ver=1.13.31" id="jquery-ui-controlgroup-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-includes/js/jquery/ui/checkboxradio.min.js?ver=1.13.31" id="jquery-ui-checkboxradio-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-includes/js/jquery/ui/button.min.js?ver=1.13.31" id="jquery-ui-button-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-includes/js/jquery/ui/dialog.min.js?ver=1.13.31" id="jquery-ui-dialog-js"></script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/lib/js/focus-within-pollyfill.js?ver=16346624651" id="focus-within-js"></script>
<script type="text/javascript" id="uconnect-theme-js-extra">
/* <![CDATA[ */
var UC_CONFIG = {"HEADER_IMAGE_WIDTH":"1600","HEADER_IMAGE_HEIGHT":"460","UC_SCHOOL_SITE_NAME_SHORT":"Career Advising & Professional Development","SLIDER_SPEED":"7000"};
/* ]]> */
</script>
<script type="text/javascript" id="uconnect-theme-js-before">
/* <![CDATA[ */
const stickyHeader = [""]; 
/* ]]> */
</script>
<script type="text/javascript" src="https://cdn.uconnectlabs.com/wp-content/themes/uConnect/js/uconnect.js?ver=17468072161" id="uconnect-theme-js"></script>
<script type="text/javascript" id="uconnect-theme-js-after">
/* <![CDATA[ */
const betaFeatures = [ "premium-classes","subscribers-upload" ];
/* ]]> */
</script>
<script type="text/javascript">window.NREUM||(NREUM={});NREUM.info={"beacon":"bam.nr-data.net","licenseKey":"NRBR-725fd230f226dcacc90","applicationID":"**********","transactionName":"ZF1bMURSXxZQBUdfX10XeAZCWl4LHgBBWV5HFUkEUVY=","queueTime":0,"applicationTime":217,"atts":"SBpYRwxITBg=","errorBeacon":"bam.nr-data.net","agent":""}</script></body>
</html>

<!-- plugin=object-cache-pro client=phpredis metric#hits=7279 metric#misses=20 metric#hit-ratio=99.7 metric#bytes=3625123 metric#prefetches=439 metric#store-reads=41 metric#store-writes=4 metric#store-hits=454 metric#store-misses=6 metric#sql-queries=6 metric#ms-total=223.49 metric#ms-cache=7.80 metric#ms-cache-avg=0.1774 metric#ms-cache-ratio=3.5 -->
