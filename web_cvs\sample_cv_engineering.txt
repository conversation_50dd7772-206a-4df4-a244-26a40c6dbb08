﻿MICHAEL CHEN
1234 Engineering Way, San Francisco, CA 94105
Phone: (************* | Email: <EMAIL>
LinkedIn: linkedin.com/in/michaelchen | GitHub: github.com/mchen

OBJECTIVE
Mechanical Engineer seeking to leverage 3+ years of design experience and CAD expertise 
to contribute to innovative product development in the automotive industry.

EDUCATION
Bachelor of Science in Mechanical Engineering
University of California, Davis | Davis, CA
Graduated: June 2021 | GPA: 3.6/4.0

Relevant Coursework: Thermodynamics, Fluid Mechanics, Materials Science, Control Systems

TECHNICAL SKILLS
â€¢ CAD Software: SolidWorks, AutoCAD, CATIA, Fusion 360
â€¢ Programming: MATLAB, Python, C++, LabVIEW
â€¢ Manufacturing: CNC Machining, 3D Printing, Welding, Assembly
â€¢ Analysis Tools: ANSYS, Finite Element Analysis (FEA), CFD
â€¢ Operating Systems: Windows, Linux, macOS

PROFESSIONAL EXPERIENCE
Mechanical Design Engineer | TechFlow Dynamics | July 2021 - Present
â€¢ Designed and developed automotive components using SolidWorks, reducing manufacturing costs by 15%
â€¢ Conducted finite element analysis on structural components, improving safety ratings by 20%
â€¢ Collaborated with cross-functional teams to optimize product designs for manufacturability
â€¢ Created detailed technical drawings and specifications for production teams

Engineering Intern | Green Energy Solutions | June 2020 - August 2020
â€¢ Assisted in the design of renewable energy systems, focusing on wind turbine components
â€¢ Performed computational fluid dynamics analysis to optimize blade designs
â€¢ Prepared technical reports and presentations for senior engineering staff
â€¢ Participated in prototype testing and data collection procedures

PROJECTS
Autonomous Vehicle Steering System (Senior Design Project, 2021)
â€¢ Led team of 5 students in developing electric power steering system for autonomous vehicles
â€¢ Implemented control algorithms using MATLAB/Simulink for real-time vehicle control
â€¢ Presented project to industry panel, receiving recognition for innovation and technical execution

Solar Panel Tracking System (Personal Project, 2020)
â€¢ Designed and built automated solar panel tracking system using Arduino microcontroller
â€¢ Increased energy efficiency by 25% compared to fixed solar panel installations
â€¢ Documented project process and shared open-source code on GitHub

CERTIFICATIONS & TRAINING
â€¢ Certified SolidWorks Professional (CSWP) - 2021
â€¢ Six Sigma Yellow Belt Certification - 2022
â€¢ OSHA 10-Hour Construction Safety Training - 2021

PROFESSIONAL AFFILIATIONS
â€¢ American Society of Mechanical Engineers (ASME) - Student Member
â€¢ Society of Automotive Engineers (SAE) - Member since 2020

AWARDS & RECOGNITION
â€¢ Dean's List: Fall 2019, Spring 2020, Fall 2020
â€¢ Outstanding Senior Design Project Award - UC Davis College of Engineering
â€¢ Engineering Scholarship Recipient - 2019-2021
